#!/usr/bin/env sh
# . "$(dirname -- "$0")/_/husky.sh"

# # 定义要检查的文件夹列表
# folders=("rtc" "audio-mixer")

# echo "\n> Adding demos timestamps: [${folders[@]}]"

# # 遍历文件夹，检查是否有更改
# for folder in "${folders[@]}"
# do
#     if git diff --cached --quiet -- "examples/api-cloud-next/$folder/"
#     then
#         :
#     else
#         # 向 index.html 添加时间戳
#         node ./scripts/demo-time-stamper.js "examples/api-cloud-next/$folder/"
#         # 将更改加入暂存区
#         git add "examples/api-cloud-next/$folder/index.html"
#     fi
# done

# echo "> Done.\n"
