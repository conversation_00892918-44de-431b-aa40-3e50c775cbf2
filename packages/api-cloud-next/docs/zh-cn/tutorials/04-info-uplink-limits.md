**上行用户**是指当前正在发布音视频流的用户，用户通过调用 {@link TRTC#startLocalVideo startLocalVideo()} | {@link TRTC#startLocalAudio startLocalAudio()} | {@link TRTC#startScreenShare startScreenShare()} 成功发布本地流后成为`上行用户`；用户通过调用 {@link TRTC#stopLocalVideo stopLocalVideo()} & {@link TRTC#stopLocalAudio stopLocalAudio()} & {@link TRTC#stopScreenShare stopScreenShare()} 取消发布本地流后，该用户就不再是上行用户。

## 上行用户个数限制

实时音视频后台服务器对单个房间内的上行用户个数限制是 50。如果一个房间内上行用户超过 50 个，即第 51 个用户尝试发布本地音视频流的时候就会出现发布失败问题。此时如果 App 业务层想要让第 51 个用户发布本地流，应先让其他用户通过调用 {@link TRTC#stopLocalVideo stopLocalVideo()} 停止发布本地流，让出`上行用户`名额后再让第 51 个用户发布本地流。
