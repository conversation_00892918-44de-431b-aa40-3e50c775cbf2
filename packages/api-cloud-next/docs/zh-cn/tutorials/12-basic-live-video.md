## 功能说明

本文主要介绍如何实现互通直播场景的音视频通话功能。

## 主播端

主播端实现音视频通话的流程与 {@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC} 场景的实现流程基本一致。参考：[开始集成音视频通话](./tutorial-11-basic-video-call.html)。

主要差异在于进房时，设置的 screne 和 role 参数不同。参考如下示例代码：

```js
await trtc.enterRoom({ sdkAppId, userId, userSig, roomId, scene: TRTC.TYPE.SCENE_LIVE, role: TRTC.TYPE.ROLE_ANCHOR });
```

## 观众端

### 以观众身份进房

设置 role 参数为 TRTC.TYPE.ROLE_AUDIENCE。

```javascript
await trtc.enterRoom({ roomId, sdkAppId, userId, userSig, scene: TRTC.TYPE.SCENE_LIVE, role: TRTC.TYPE.ROLE_AUDIENCE })
```

### 播放远端音频

默认情况下，SDK 会自动播放远端音频，您无需调用 API 来播放远端音频。

- 需要注意的是：如果用户在进房前没有与页面产生过交互，自动播放音频可能会因为【浏览器的自动播放策略限制】而失败，您需参考[自动播放受限处理建议](./tutorial-21-advanced-auto-play-policy.html)进行处理。
- 若您不希望 SDK 自动播放音频，您可以在 {@link TRTC#enterRoom trtc.enterRoom()} 时设置 autoReceiveAudio = false 关闭自动拉流及自动播放音频。
- 监听 {@link module:EVENT.REMOTE_AUDIO_AVAILABLE TRTC.EVENT.REMOTE_AUDIO_AVAILABLE} 事件，记录有远端音频的 userId，在需要播放音频时，调用 {@link TRTC#muteRemoteAudio trtc.muteRemoteAudio(userId, false)} 方法。


### 播放远端视频

在进房前监听 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.EVENT.REMOTE_VIDEO_AVAILABLE} 事件，在收到该事件时，通过 {@link TRTC#startRemoteVideo trtc.startRemoteVideo()} 播放远端视频流。

```javascript
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // 为了播放视频画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 `${userId}_${streamType}`
  const view = `${userId}_${streamType}`;
  trtc.startRemoteVideo({ userId, streamType, view });
});
```


### 上麦与主播连麦互动

观众角色没有发布音视频的权限，因此观众若想与主播连麦互动，需要先使用 {@link TRTC#switchRole switchRole} 切换成主播角色，再发布音视频流。

```javascript
// 切换成主播上麦
await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);

// 上麦后开启麦克风
await trtc.startLocalAudio();
// 上麦后开启摄像头
await trtc.startLocalVideo();

// 连麦结束后，调用 exitRoom 退房结束连麦。
await trtc.exitRoom();
```