本文主要介绍如何在视频通话或互动直播中设置视频属性，开发者可以根据具体业务需求调整视频画面的清晰度和流畅度，获得更好的用户体验。
视频属性包括分辨率、帧率和码率。

## 实现方式

通过 trtc 对象的 {@link TRTC#startLocalVideo startLocalVideo()} 或 {@link TRTC#updateLocalVideo updateLocalVideo()} 方法设置视频属性：

- 指定一个预定义的 Profile，每个 Profile 对应着一套推荐的分辨率、帧率和码率。

```javascript
// 启动时指定视频属性
await trtc.startLocalVideo({
  option: { profile: '480p' }
});
// 通话过程中动态调整视频属性
await trtc.updateLocalVideo({
  option: { profile: '360p' }
});
```

- 指定自定义分辨率、帧率和码率

```javascript
// 启动时指定视频属性
await trtc.startLocalVideo({
  option: { profile: { width: 640, height: 480, frameRate: 15, bitrate: 900 /* kpbs */} }
});
// 通话过程中动态调整视频属性
await trtc.updateLocalVideo({
  option: { profile: { width: 640, height: 360, frameRate: 15, bitrate: 800 /* kpbs */} }
});
```
## 视频属性 Profile 列表

| 视频 Profile | 分辨率（宽 x 高）| 帧率（fps）| 码率（kbps）| 备注 |
| :---       | :---           | :---      | :---      | :--- |
| 120p       | 160 x 120      | 15        | 200        ||
| 120p_2       | 160 x 120      | 15        | 100        | v5.1.1+ 支持 |
| 180p       | 320 x 180      | 15        | 350       ||
| 180p_2       | 320 x 180      | 15        | 150       | v5.1.1+ 支持 |
| 240p       | 320 x 240      | 15        | 400       ||
| 240p_2       | 320 x 240      | 15        | 200       | v5.1.1+ 支持 |
| 360p       | 640 x 360      | 15        | 800       ||
| 360p_2       | 640 x 360      | 15        | 400       | v5.1.1+ 支持 |
| 480p       | 640 x 480      | 15        | 900       ||
| 480p_2       | 640 x 480      | 15        | 500       | 默认值，v5.1.1+ 支持 |
| 720p       | 1280 x 720     | 15        | 1500      ||
| 1080p      | 1920 x 1080    | 15        | 2000      ||
| 1440p      | 2560 x 1440    | 30        | 4860      ||
| 4K         | 3840 x 2160    | 30        | 9000      ||

- 由于设备和浏览器的限制，视频分辨率不一定能够完全匹配，在这种情况下，浏览器会自动调整分辨率使其接近 Profile 对应的分辨率
