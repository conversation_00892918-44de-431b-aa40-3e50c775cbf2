## Chrome

1. （2021-11-23）Chrome 88 开启硬件加速时，使用 HTMLMediaElement.captureStream 推 MP4 文件，远端拉流观看黑屏问题。[Chrome 88  bug](https://bugs.chromium.org/p/chromium/issues/detail?id=1156408)。<br>规避方案：
	- 升级至 Chrome 96+ 版本。
	- 也可通过关闭硬件加速来规避。
2. （2021-2-3）Mac Chrome 88(88.0.4324.96) 关闭硬件加速时，推摄像头采集的视频流，远端拉流观看黑屏问题。 [Chrome 88 bug](https://bugs.chromium.org/p/chromium/issues/detail?id=1168948#c34)<br>规避方案：
	- 升级 Chrome 88.0.4324.146+ 版本
	- 保持开启硬件加速不会出现该问题（Chrome 默认是打开硬件加速的）
3. （2021-2-2）Chrome 使用 deviceId 为 default 或 communications (Windows 设备下会有该 deviceId) 时，若插入新的麦克风，再拔出，可能会导致麦克风采集中断。<br>规避方案：
	- 避免使用 deviceId 为 default 或 communications 的麦克风设备即可。
4. (2023-07-20) Mac 设备中，若安装了 Mersive Solstic 软件，其虚拟摄像头驱动会导致 Chrome 无法获取摄像头列表，导致无法采集摄像头。[Chrome issue](https://bugs.chromium.org/p/chromium/issues/detail?id=1379392) & [Mersive Solstic 5.5.2 known issue](https://documentation.mersive.com/content/pages/release-notes.htm)。
	- 规避方案：引导用户删除该虚拟摄像头驱动，或等待后续 Chrome or Mersive Solstic 规避。

		```js
		sudo rm -rf /Library/CoreMediaIO/Plug-Ins/DAL/RelayCam.plugin
		```
5. (2023-12-11) Android 11 部分设备（已知设备：OPPO Reno4）可能会出现推流超时、推流慢的问题。 [Chrome issue](https://bugs.chromium.org/p/chromium/issues/detail?id=1115498)
    - 规避方案：一般无需处理，SDK 重试能恢复。或者引导用户升级 Android 版本至 Android 12+
6. (2024-12-16) Android Chrome 122 可能无法获取音量值。
		- 规避方案：升级 SDK 最新版本，或升级 Chrome 至 Chrome 123+。
## Firefox

1. （2021-2-2）Firefox 不支持设置采集帧率，只能采集 30fps 的视频。
2. （2022-7-7）首次安装的 Firefox 浏览器会在联网状态下动态安装 H.264 编解码器，在安装完成前，无法正常使用 SDK 推拉流。处理建议：
	- 通过 SDK 的 [TRTC.isSupported](./TRTC.html#.isSupported) 接口，若检测到在 Firefox 下不支持 H264 编解码，则引导用户在 Firefox 打开地址：`about:addons`，到 `插件` 中检查 OpenH264 的安装情况。等待安装完成后再进行通话。<br/>
	  <img src="./assets/firefox-264.png" width="400"/>

## Safari

2. （2021-2-2）iOS Safari 不支持多个 tab getUserMedia，否则前一个 tab 会停止采集，远端流也有可能出现黑屏无声。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=179363) <br>规避方案：
	- iOS Safari 尚无计划支持多 tab getUserMedia 特性，若业务侧需要在 iOS Safari 使用多个 tab getUserMedia，建议在切换新 tab 之前，停止设备采集，在切换回来后，再恢复设备采集。<br>注：多个 tab getUserMedia 的业务场景一般有：在视频通话过程中，切换新 tab 进行人脸识别。

3. （2021-10-28）iOS Safari 和 Mac Big Sur Safari 音视频互通，iOS Safari 观看 Mac Safari 的视频卡顿掉帧。<br>规避方案：
	- 升级 Mac BigSur 最新版本。

4. （2021-2-2）iOS 14.2 部分设备及 Mac Big Sur Safari，音频播放会有杂音。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=218762) 。 <br>规避方案：
	- iOS 设备升级 14.3 及其以上版本、Mac Big Sur 升级最新版本。

5. （2021-9-28）iOS 15 Safari 音视频通话时，扬声器外放声音可能会比 iOS 14 低。 [webkit bug](https://bugs.webkit.org/show_bug.cgi?id=230902) 。 <br>规避方案：
	- 升级 iOS 版本至15.4+。

6. （2021-12-24） iOS 15 Safari 及 WKWebview 音视频通话时，连接蓝牙耳机可能会出现声音播放异常的问题。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=234595) 。<br>规避方案:
	- 升级 iOS 版本至15.4+。

7. （2022-1-20） A 用户外放 B 用户的声音，同时手机也在采集 A 的声音，此时 B 用户听到的声音会伴随有电流声，这是 iOS 14 回声消除功能带来的副作用。<br>规避方案:
	- 使用耳麦进行通话。
	- 升级 iOS 版本至 15.4+。
8. （2022-01-19）iOS 15 以下版本，canvas.captureStream 采集出的视频流，无法使用 video 标签播放。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=181663)。

9. (2022-04-20) iOS 15 在某些机型，当您的页面有播放非 MediaStream 的 Audio 标签时，在页面获取麦克风又关闭后，可能会出现 Audio 标签播放的声音变小的情况。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=236439) 。

10. (2024-04-26) iOS 17 视频渲染可能会闪动。[webkit bug](https://bugs.webkit.org/show_bug.cgi?id=230532)。
	- 规避方案：升级 SDK 最新版本。

11. (2024-12-16) iOS 18 中对 OffscreenCanvas 兼容性不佳，导致使用 SDK 的水印插件或者编码翻转功能时可能会导致内存泄漏。
  - 规避方案：升级 SDK 最新版本。

## Webview

1. （2021-11-23）Android System Webview M79 以下的版本，无法使用 H264 解码。[webview bug](https://bugs.chromium.org/p/chromium/issues/detail?id=801501) 。<br>规避方案：
	- 引导用户升级 Android System Webview 版本至 M79+，需要安装对应版本的 webview apk 安装包。

2. (2024-12-31) Android System Webview 104 版本，使用 SDK 的水印插件或者编码翻转功能时，编码时间长的问题。<br>规避方案：
  - 引导用户升级 Android System Webview 版本至 105 及以上。
## 华为设备

1. （2021-4-29）华为浏览器、华为设备中的 Chrome 浏览器无法推流。由于华为设备的限制，部分版本的华为浏览器及华为 Chrome 浏览器不支持 H264 编码，因此无法推流。规避方案：使用 VP8 编码，需开通白名单，可提交 [TRTC 用户支持申请](https://cloud.tencent.com/apply/p/pnh3a63d95) 后加入TRTC交流群，联系群内技术支持开通。

## 荣耀设备

1. (2024-12-25) 在部分荣耀手机，可能会出现拉流无声问题。
   - 规避方案：升级最新版本 SDK 或者更新最新系统版本（后续荣耀会发布修复）。

## 小米设备

1. （2021-11-23）在部分小米手机的微信中，会出现拉流无声问题。已知的机型有：小米9、小米10s、小米11、K30 5G等。该问题为 MIUI 已知问题，小米工程师正着手修复。微信也找到了规避方案，目前正在灰度中。
2. （2025-03-27）小米 14 中调用后置摄像头，可能会出现摄像头卡顿的问题，原因：系统 Bug。

## 微信

1. 微信 TBS/045811 及其以下版本的内核，在授权窗口弹出后，若超过5s才点击授权按钮，可能会出现[自动播放失败事件](./module-EVENT.html#.AUTOPLAY_FAILED)。TBS 已知问题，后续版本的 TBS 内核会修复该问题。<br>规避方案：
	- 可参考[自动播放受限处理建议](./tutorial-21-advanced-auto-play-policy.html)中的方案二，当出现自动播放错误时，引导客户点击页面后，调用 stream.resume() 接口恢复播放。

## 企业微信

1. iOS 企业微信 WebView 打开，获取媒体设备方法之后可能会出现授权弹框不出现的情况。<br>规避方案：
	- 升级 iOS 企业微信版本 >= 4.0.6

## 屏幕分享

1. （2021-2-2）Windows &amp; Mac Chrome 浏览器屏幕分享某个 app 后，最小化会导致采集停止，fps = 0
2. （2021-9-29）Windows 端使用 Chrome 屏幕分享，选择应用窗口分享【微信】【QQ】【钉钉】【WPS】时，可能会出现采集黑屏；或者拖动应用窗口时出现采集黑屏。<br>规避方案：
	- 暂无法解决，建议引导用户分享整个屏幕的方式规避。
3. （2021-11-16) Mac Firefox 屏幕分享可能会出现视频部分区域错位，[Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1536777)。暂无法规避，建议使用 Chrome or Safari 浏览器进行屏幕分享。
4. （2022-03-18）Mac Chrome 在已授权屏幕录制的情况下屏幕分享失败，出现 "NotAllowedError: Permission denied by system" 或者 "NotReadableError: Could not start video source" 错误信息，[Chrome bug](https://bugs.chromium.org/p/chromium/issues/detail?id=1306876)。解决方案：打开【设置】> 点击【安全性与隐私】> 点击【隐私】> 点击【屏幕录制】> 关闭 Chrome 屏幕录制授权 > 重新打开 Chrome 屏幕录制授权 > 关闭 Chrome 浏览器 > 重新打开 Chrome 浏览器。
5. (2022-03-31) Mac 中使用 Chrome 屏幕分享，窗口采集可能无法采集到 WPS PPT 全屏窗口。<br>规避方案：
	- Web SDK 层面暂无法解决，建议引导用户使用分享整个屏幕的方式规避。

## 云端录制

1. 云端转码录制会出现上行音视频流前几秒没有被录制到的情况。如需解决该问题，可提交 [TRTC 用户支持申请](https://cloud.tencent.com/apply/p/pnh3a63d95) 后加入TRTC交流群，联系群内技术支持按需修改云端录制配置。

## 其他

1. (2021-2-2)网易 WebRTC SDK 会改写 RTCPeerConnection.prototype.getStats 方法，返回的数据格式与标准 WebRTC 协议不一致。若同时引入 TRTC 和网易的 WebRTC SDK，TRTC 会拿不到音视频数据，导致无法向正常向仪表盘上报音视频通话数据。
2. (2025-03-27) 索尼部分手机(XPeria 1 VI)Android 15 版本可能会出现通话无声问题, [索尼系统 bug](https://github.com/google/oboe/issues/2121#issuecomment-2515354168)。
   - 规避方案：升级最新版本系统及升级最新版本 sdk。

## 关于 Vue 3 响应式及 Proxy

（2021-11-5）开发者在使用 Vue 3 时应注意，Vue 3 基于 Proxy 实现响应式。请使用 [Vue markRaw](https://cn.vuejs.org/api/reactivity-advanced.html#markraw) 将 TRTC 实例设置为非响应式属性，若对 SDK 实例加了 Proxy，可能会导致 SDK 异常。
