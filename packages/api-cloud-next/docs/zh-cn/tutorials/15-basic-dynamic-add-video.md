## 功能说明

本文主要介绍如何在通话过程中动态地开关摄像头及麦克风，以及如何获取远端用户麦克风、摄像头的开关状态。

## 动态开关摄像头、麦克风

如下几个方案有各自的优点，您可以根据项目需求自行选择。

> 在下述的三个方案中，如下表现是一致的：
> - 在关闭摄像头后，房间内其他用户会收到 {@link module:EVENT.REMOTE_VIDEO_UNAVAILABLE REMOTE_VIDEO_UNAVAILABLE} 事件。
> - 在开启摄像头后，房间内其他用户会收到 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} 事件。
> - 在关闭麦克风后，房间内其他用户会收到 {@link module:EVENT.REMOTE_AUDIO_UNAVAILABLE REMOTE_AUDIO_UNAVAILABLE} 事件。
> - 在开启麦克风后，房间内其他用户会收到 {@link module:EVENT.REMOTE_AUDIO_AVAILABLE REMOTE_AUDIO_AVAILABLE} 事件。

### 方案一（推荐）：使用 {@link TRTC#updateLocalVideo updateLocalVideo()} {@link TRTC#updateLocalAudio updateLocalAudio()} mute 参数。

该方案关闭麦克风、关闭摄像头，不会停止设备采集，摄像头、麦克风“采集灯”会亮着。该方案关闭设备是“软件层面”的操作，因此其优势是重新打开设备更快速。

```js
// 首次打开摄像头
await trtc.startLocalVideo();
// 关闭摄像头，在关闭摄像头后，摄像头预览画面会变成黑屏，您可以在此时显示业务侧自身的 UI 遮罩。
await trtc.updateLocalVideo({ mute: true });
// 打开摄像头
await trtc.updateLocalVideo({ mute: false });
```

由于麦克风启动需要一些时间，这期间可能会漏掉用户的声音，所以相比于方案二的 stopLocalAudio，我们更推荐您使用该方案来实现静音和解除静音的功能。

此外，在 updateLocalAudio({ mute: true }) 后，会发送码率极低的静音包。

这对于需要云端录制的场景非常适用，因为 MP4 等格式的视频文件，对于音频数据的连续性要求很高，使用 stopLocalAudio 会导致录制出的 MP4 文件不易播放。因此在对录制文件的质量要求较高的场景中，建议选择该方案。

```js
// 首次打开麦克风
await trtc.startLocalAudio();
// 关闭麦克风
await trtc.updateLocalAudio({ mute: true });
// 打开麦克风
await trtc.updateLocalAudio({ mute: false });
```

在用户 mute 麦克风后，检测用户是否在说话。

```js
const trtc = TRTC.create();
await trtc.startLocalAudio();

let isAudioMuted = false;
await trtc.updateLocalAudio({ mute: true });
isAudioMuted = true;

// 额外创建一个 trtc 实例，采集麦克风，检测麦克风音量。
const trtcA = TRTC.create();
trtcA.enableAudioVolumeEvaluation();
trtcA.on(TRTC.EVENT.AUDIO_VOLUME, event => {
  event.result.forEach(item => {
      // 1. userId === '' 代表本地音量
      // 2. 一般认为 volume 大于 10 的时候代表用户在说话，你也可以自定义这个阈值。
      if (item.userId === '' && item.volume > 10 && isAudioMuted) {
        // 用户在关闭麦克风后，还在说话，此时可以提醒用户打开麦克风。
      }
    })
})
// 指定采集同一个麦克风。
await trtcA.startLocalAudio({ option: { microphoneId: '' } });
```

### 方案二：使用 {@link TRTC#stopLocalVideo stopLocalVideo()} {@link TRTC#stopLocalAudio stopLocalAudio()} 方法

该方案关闭麦克风、关闭摄像头后，会停止设备采集，摄像头、麦克风“采集灯”会熄灭。在重新开启摄像头后，会重新采集摄像头。

```javascript
// 关闭摄像头
await trtc.stopLocalVideo();

// 打开摄像头
// 'local-video' 为 DOM 中用于播放本地摄像头的视频容器的 element id。
await trtc.startLocalVideo({ view: 'local-video' });
```

```js
// 关闭麦克风
await trtc.stopLocalAudio();

// 打开麦克风
await trtc.startLocalAudio();
```

### 方案三：使用 {@link TRTC#updateLocalVideo updateLocalVideo()} {@link TRTC#updateLocalAudio updateLocalAudio()} publish 参数。

该方案关闭麦克风、关闭摄像头，不会停止设备采集，摄像头、麦克风“采集灯”会亮着。该方案与方案二的差异点是，在关闭摄像头后，本地依然能预览摄像头画面，房间内其他用户看不到本地摄像头。

```js
// 首次打开摄像头
await trtc.startLocalVideo();
// 关闭摄像头。关闭后，本地任然能预览摄像头，房间内其他用户看不到本地摄像头。
await trtc.updateLocalVideo({ publish: false });
// 打开摄像头
await trtc.updateLocalVideo({ publish: true });
```

```js
// 首次打开麦克风
await trtc.startLocalAudio();
// 关闭麦克风
await trtc.updateLocalAudio({ publish: false });
// 打开麦克风
await trtc.updateLocalAudio({ publish: true });
```

## 在摄像头上设置图片垫片

trtc-sdk-v5@5.4.0 之后支持，startLocalVideo() 和 updateLocalVideo() 可以传入占位图片到摄像头流上，本地和远端将都会显示这张图片，不支持在摄像头关闭时调用。

注意如果设置前已在摄像头已经在 mute 状态，设置垫片后房间内其他用户会收到 REMOTE_AUDIO_AVAILABLE 事件。

屏幕分享暂不支持设置垫片。

```js
await trtc.startLocalVideo({ mute: 'https://...' });
// 或
await trtc.updateLocalVideo({ mute: 'https://...' });
```

## 获取远端用户麦克风、摄像头开启状态

假设有两个用户 A 和 B。

1. A 进房成功后，若房间内有另一位主播用户 B，则 A 会收到 {@link module:EVENT.REMOTE_USER_ENTER REMOTE_USER_ENTER} 事件。
2. 此时，A 可默认认为 B 未开启麦克风、摄像头。
3. 此后，若 A 收到了 B 的 {@link module:EVENT.REMOTE_AUDIO_AVAILABLE REMOTE_AUDIO_AVAILABLE} 或 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} 事件，则代表 B 开启了麦克风、摄像头。
