以下 SDK 版本存在接口变更或 `Breaking Change`，升级时需要注意。

## Version 5.10.0 @2025.04.17

- 插件产物格式由 iife 转变为 umd，支持了更多接入环境。
  - 若您通过 import 方式引入插件，则可忽略。
  - 若您通过 script 标签加载插件，升级时需要注意修改插件文件名。
- npm 包中的 wasm 资源文件统一迁移至 assets 目录，您部署时可以直接部署 assets 目录。

## Version 5.6.0 @2024.5.17

【变更】

在 v5.6.0 之前的版本，SDK 默认开启自动视频拉流（当远端推视频流后，SDK 会自动拉远端视频流），但是有时候您的业务场景是按需拉取的，此时自动拉流会造成额外的带宽消耗。

在 v5.6.0 之后的版本，SDK 关闭默认自动视频拉流（当远端视频推流，SDK 不会自动拉视频流），即：[trtc.enterRoom](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#enterRoom) 的 autoReceiveVideo 参数默认值从 true 变更为 false。由业务侧按需调用 [trtc.startRemoteVideo](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#startRemoteVideo) 来拉流播放视频，减少可能存在的不必要带宽占用。

如下场景建议您开启 autoReceiveVideo：

1. 想要获得更快的“秒开”体验（从进房到看到首帧远端视频画面），您可以打开自动视频拉流。
2. 远端没有推视频流，而是推了一路黑帧视频，用于发送 [SEI 消息](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#sendSEIMessage)。此时对于接收端来说，黑帧视频流是不需要 [trtc.startRemoteVideo](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#startRemoteVideo) 渲染出来的。因此你也可以打开自动视频拉流，以确保正常收到 SEI 消息。

## Version 5.1.0 @2023.08.11

【背景】

1. TRTC 数字类型和字符串类型房间号不互通。
2. 在 v5.1.0 之前的版本，{@link TRTC#enterRoom trtc.enterRoom} 接口的 roomId 参数支持传入 number 和 string 两种类型。很容易出现因房间号类型不同，导致多端无法互通的情况。例如：TRTC Web 端使用了字符串类型 roomId "123"，而 TRTC Android 使用了数字类型的 roomId 123。

【变更】

为防止出现上述情况，自 v5.1.0 版本开始，限制 {@link TRTC#enterRoom trtc.enterRoom} 接口的 roomId 参数为 number 类型，不再支持传入 string 类型。若要使用字符串房间号，请使用 strRoomId 参数。

## 从 4.x 升级到 5.x

TRTC Web SDK 5.0.0 于 2023 年 5 月 26 日正式发布。

TRTC Web SDK 5.x 版本是 Web 端全新升级版，提供扁平化接口、大幅简化 API、降低您的接入成本；在多人音视频场景下，具有更好的性能表现及弱网抗性。

5.x 版本的接口与 4.x 版本的接口不兼容，因此您如果升级到 5.x 版本，调用的接口需要做相应的改动。

此部分主要是为有 TRTC Web SDK 4.x 经验的、希望了解 5.x 的新功能和更改的用户而提供的。

下面是 4.x 与 5.x 功能教程，升级时需要注意各个教程的实现差异：

### 基础教程

| 4.x 实现教程                                                                                                        | 5.x 实现教程                                                         |
| ------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- |
| [实现基础音视频通话](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-11-basic-video-call.html)            | [实现基础音视频通话](./tutorial-11-basic-video-call.html)            |
| [实现互动连麦直播](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-12-basic-live-video.html)              | [实现互动连麦直播](./tutorial-12-basic-live-video.html)              |
| [切换摄像头和麦克风](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-13-basic-switch-camera-mic.html)     | [切换摄像头和麦克风](./tutorial-13-basic-switch-camera-mic.html)     |
| [设置本地视频属性](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-14-basic-set-video-profile.html)       | [设置本地视频属性](./tutorial-14-basic-set-video-profile.html)       |
| [动态开关摄像头、麦克风](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-15-basic-dynamic-add-video.html) | [动态开关摄像头、麦克风](./tutorial-15-basic-dynamic-add-video.html) |
| [实现屏幕分享](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-16-basic-screencast.html)                  | [实现屏幕分享](./tutorial-16-basic-screencast.html)                  |
| [音量大小检测](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-17-basic-detect-volume.html)               | [音量大小检测](./tutorial-17-basic-detect-volume.html)               |

### 进阶教程

| 4.x 实现教程                                                                                                                                                                                                                   | 5.x 实现教程                                                                           |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------------------------------------------------------- |
| [自定义采集与自定义播放渲染](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-20-advanced-customized-capture-rendering.html)                                                                                          | [自定义采集与自定义播放渲染](./tutorial-20-advanced-customized-capture-rendering.html) |
| [背景音乐和音效实现方案](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-22-advanced-audio-mixer.html)                                                                                                               | [背景音乐和音效实现方案](./tutorial-22-advanced-audio-mixer.html)                      |
| [通话前环境与设备检测](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-23-advanced-support-detection.html)                                                                                                           | [通话前环境与设备检测](./tutorial-23-advanced-support-detection.html)                  |
| [检测网络质量](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-24-advanced-network-quality.html)                                                                                                                     | [检测网络质量](./tutorial-24-advanced-network-quality.html)                            |
| [检测设备插拔行为](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-25-advanced-device-change.html)                                                                                                                   | [检测设备插拔行为](./tutorial-25-advanced-device-change.html)                          |
| [开启大小流](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-27-advanced-small-stream.html)                                                                                                                          | [开启大小流](./tutorial-27-advanced-small-stream.html)                                 |
| [开启水印](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-29-advanced-water-mark.html)                                                                                                                               | [开启水印](./tutorial-29-advanced-water-mark.html)                                      |
| [开启 AI 降噪](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-35-advanced-ai-denoiser.html)                                                                                                                         | [开启 AI 降噪](./tutorial-35-advanced-ai-denoiser.html)                                |
| [实现云端混流](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-31-advanced-mix-transcode.html) <br/> [实现推流到 CDN](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-26-advanced-publish-cdn-stream.html) | [云端混流与转推 CDN](./tutorial-26-advanced-publish-cdn-stream.html)                   |
| [实现 3D 空间音频](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-36-advanced-spatial-audio.html)                                                                                                                   | 排期开发中，敬请期待                                                                   |
| [实现变声](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-37-advanced-voice-changer.html)                                                                                                                           | 排期开发中，敬请期待                                                                   |

### 最佳实践

| 4.x 实现教程                                                                                                        | 5.x 实现教程                                                         |
| ------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- |
| [自动播放受限处理建议](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-21-advanced-auto-play-policy.html) | [自动播放受限处理建议](./tutorial-21-advanced-auto-play-policy.html) |
| [应对防火墙受限](https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-34-advanced-proxy.html) | [应对防火墙受限](./tutorial-34-advanced-proxy.html) |
