本文主要介绍在音视频通话过程中如何切换摄像头和麦克风。

## 切换摄像头

```js
// 打开摄像头，默认是摄像头列表中第一个摄像头。
await trtc.startLocalVideo();

const cameraList = await TRTC.getCameraList();
// 切换到第二个摄像头
if (cameraList[1]) {
  await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
}

// 移动端
// 切换前置摄像头
await trtc.updateLocalVideo({ option: { useFrontCamera: true }});
// 切换后置摄像头
await trtc.updateLocalVideo({ option: { useFrontCamera: false }});
```

## 切换麦克风


```js
// 打开麦克风，默认麦克风列表中第一个麦克风。
await trtc.startLocalAudio();

const microphoneList = await TRTC.getMicrophoneList();
// 切换到第二个麦克风
if (microphoneList[1]) {
  await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
}
```
