{"00-info-update-guideline": {"title": "SDK 升级指引"}, "01-info-changelog": {"title": "SDK 版本发布日志"}, "02-info-webrtc-issues": {"title": "WebRTC 已知问题及规避方案"}, "03-info-error-code-tips": {"title": "错误码说明及处理建议"}, "04-info-uplink-limits": {"title": "房间内上行用户个数限制"}, "05-info-browser": {"title": "浏览器与应用环境信息"}, "10-basic-get-started-with-demo": {"title": "快速跑通 Demo"}, "11-basic-video-call": {"title": "开始集成音视频通话"}, "12-basic-live-video": {"title": "实现互动直播连麦"}, "13-basic-switch-camera-mic": {"title": "切换摄像头和麦克风"}, "14-basic-set-video-profile": {"title": "设置本地视频属性"}, "15-basic-dynamic-add-video": {"title": "开关摄像头、麦克风"}, "16-basic-screencast": {"title": "屏幕分享"}, "17-basic-detect-volume": {"title": "音量大小检测"}, "18-basic-debug": {"title": "调试模式"}, "20-advanced-customized-capture-rendering": {"title": "自定义采集与自定义播放渲染"}, "21-advanced-auto-play-policy": {"title": "自动播放受限处理建议"}, "22-advanced-audio-mixer": {"title": "背景音乐实现方案"}, "23-advanced-support-detection": {"title": "通话前环境与设备检测"}, "24-advanced-network-quality": {"title": "检测网络质量"}, "25-advanced-device-change": {"title": "处理设备插拔"}, "26-advanced-publish-cdn-stream": {"title": "云端混流与转推CDN"}, "27-advanced-small-stream": {"title": "多人视频通话"}, "29-advanced-water-mark": {"title": "开启水印"}, "28-advanced-beauty": {"title": "开启美颜"}, "30-advanced-cross-room-link": {"title": "实现跨房连麦"}, "34-advanced-proxy": {"title": "应对防火墙受限"}, "35-advanced-ai-denoiser": {"title": "开启 AI 降噪"}, "36-advanced-virtual-background": {"title": "开启虚拟背景"}, "37-advanced-voice-changer": {"title": "开启美声效果"}, "38-advanced-basic-beauty": {"title": "开启基础美颜"}, "39-advanced-video-decoder": {"title": "开启视频解码插件"}, "40-advanced-video-mixer": {"title": "开启视频合流插件"}, "41-advanced-small-stream-auto-switcher": {"title": "开启大小流自动切换插件"}}