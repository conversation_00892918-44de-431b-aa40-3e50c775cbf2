本文介绍 Web SDK 对浏览器的支持情况，以及访问协议限制和防火墙限制的处理建议。
## 浏览器兼容信息
<table>
<tr>
<th>操作系统</th>
<th>浏览器类型</th>
<th>浏览器最低<br>版本要求</th>
<th>SDK 版本要求</th>
<th>接收（拉流）</th>
<th>发送（推流）</th>
<th>屏幕分享</th>
</tr>
<tr>
<td rowspan="11">Windows</td>
<td>桌面版 Chrome 浏览器</td>
<td>56+</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>支持 Chrome72+ 版本</td>
</tr>
<tr>
<td>桌面版 QQ 浏览器（极速内核）</td>
<td>10.4+</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>桌面版 Firefox 浏览器</td>
<td>56+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持 Firefox66+ 版本</td>
</tr>
<tr>
<td>桌面版 Edge 浏览器</td>
<td>80+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持</td>
</tr>
<tr>
<td>桌面版搜狗浏览器（高速模式）</td>
<td>11+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持</td>
</tr>
<tr>
<td>桌面版搜狗浏览器（兼容模式）</td>
<td>-</td>
<td>-</td>
<td>不支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>桌面版 Opera 浏览器</td>
<td>46+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持 Opera60+ 版本</td>
</tr>
<tr>
<td>桌面版 360 安全浏览器（极速模式）</td>
<td>13+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持</td>
</tr>
<tr>
<td>桌面版 360 安全浏览器（兼容模式）</td>
<td>-</td>
<td>-</td>
<td>不支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>桌面版微信内嵌浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>桌面版企业微信内嵌浏览器</td>
<td>4.0.8+（企业微信版本）</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td rowspan="7">Mac OS</td>
<td>桌面版 Safari 浏览器</td>
<td>11+</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>支持 Safari13+ 版本</td>
</tr>
<tr>
<td>桌面版 Chrome 浏览器</td>
<td>56+</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>支持 Chrome72+ 版本</td>
</tr>
<tr>
<td>桌面版 Firefox 浏览器</td>
<td>56+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持 Firefox66+ 版本<a href="#attention3">（注意[3]）</a></td>
</tr>
<tr>
<td>桌面版 Edge 浏览器</td>
<td>80+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持</td>
</tr>
<tr>
<td>桌面版 Opera 浏览器</td>
<td>46+</td>
<td>v4.7.0+</td>
<td>支持</td>
<td>支持</td>
<td>支持 Opera60+ 版本</td>
</tr>
<tr>
<td>桌面版微信内嵌浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>桌面版企业微信内嵌浏览器</td>
<td>4.0.8+（企业微信版本）</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td rowspan="6">Android</td>
<td>微信内嵌浏览器（TBS 内核）</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>微信内嵌浏览器（XWEB 内核）</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>企业微信内嵌浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>移动版 Chrome 浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>移动版 QQ 浏览器</td>
<td>-</td>
<td>-</td>
<td>不支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>移动版 UC 浏览器</td>
<td>-</td>
<td>-</td>
<td>不支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS 12.1.4+</td>
<td>微信内嵌浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS 14.3+</td>
<td>微信内嵌浏览器</td>
<td>6.5+（微信版本）</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS</td>
<td>企业微信内嵌浏览器</td>
<td>4.0.8+（企业微信版本）</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS 11.0+</td>
<td>移动版 Safari 浏览器</td>
<td>11+</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS 12.1.4+</td>
<td>移动版 Chrome 浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>不支持</td>
<td>不支持</td>
</tr>
<tr>
<td>iOS 14.3+</td>
<td>移动版 Chrome 浏览器</td>
<td>-</td>
<td>-</td>
<td>支持</td>
<td>支持</td>
<td>不支持</td>
</tr>
</table>

> !
> - 其他的浏览器环境可以打开 [TRTC 检测页面](https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html) 查看能力支持情况。
> - Mac Firefox 屏幕分享可能会出现视频部分区域错位，[Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1536777)。暂无法规避，建议使用 Chrome or Safari 浏览器进行屏幕分享。
> - [WebRTC 已知问题及规避方案](./tutorial-02-info-webrtc-issues.html)。
> - 由于 H.264 版权限制，华为 Chrome 88 以下版本，无法使用 H264 编码（即无法推流）。如果您希望在华为设备 Chrome 浏览器中，使用 TRTC Web SDK 推流，请提交 [腾讯云实时音视频 Web SDK 用户能力支持申请](https://cloud.tencent.com/apply/p/4ab2rutgukk) 开通 VP8 编解码能力。
> - 建议您及时将 TRTC Web SDK 更新至最新版本，以便获得更好的产品稳定性及在线支持。版本升级注意事项请参见：[升级指引](./tutorial-00-info-update-guideline.html)。

## 页面访问协议说明

浏览器厂商出于对用户安全、隐私等问题的考虑，限制网页在 https 协议下才能正常使用 TRTC Web SDK（WebRTC）的全部功能。为确保生产环境用户顺畅接入和体验 TRTC Web SDK 的全部功能，请使用 https 协议访问音视频应用页面。

注：本地开发可以通过 http://localhost 或者 file:// 协议进行访问。

URL域名及协议支持情况请参考如下表格：

| 应用场景     | 协议             | 接收（拉流） | 发送（推流） | 屏幕分享 | 备注 |
| ------------ | :--------------- | :----------- | ------------ | -------- | ---- |
| 生产环境     | https协议        | 支持         | 支持         | 支持     | 推荐 |
| 生产环境     | http协议         | 支持         | 不支持       | 不支持   |      |
| 本地开发环境 | http://localhost | 支持         | 支持         | 支持     | 推荐 |
| 本地开发环境 | http://127.0.0.1 | 支持         | 支持         | 支持     |      |
| 本地开发环境 | http://[本机IP]  | 支持         | 不支持       | 不支持   |      |
| 本地开发环境 | file://         | 支持         | 支持         | 支持     |      |

> !
> - 如果您的开发环境无法通过localhost访问，也没有 https 协议，建议您使用反向代理工具，将某个 https 域名的访问请求代理到您的开发环境，例如：whistle，fiddler
## 应对防火墙策略

若用户处于受限网络（例如带有防火墙的企业内网），使用 TRTC Web SDK 可能会受防火墙限制导致无法正常通话。
请参考：[应对防火墙策略限制](./tutorial-34-advanced-proxy.html)。

## 设备授权说明

由于操作系统和浏览器的隐私保护策略规定，在浏览器里进行音视频通话，需要在浏览器里获得摄像头、麦克风对授权。设备授权分为两部分：
### 步骤 1: 操作系统允许浏览器使用摄像头、麦克风设备

Windows 和 Mac 的设置路径如下：
   
**Windows**： Windows设置 -> 隐私 -> 摄像头（或者麦克风）-> 允许桌面应用访问你的相机（或者麦克风）

<image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/699b112ba1c8b0e0d4f6edf379d28633.png"></image>

**Mac**：系统偏好设置 -> 安全性与隐私 -> 隐私 -> 摄像头（或者麦克风、屏幕录制）-> 允许您的浏览器访问您的摄像头（或者麦克风、屏幕录制）

<image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/a027cc6b3a4a3dde6a7c620c4368271e.png"></image>

### 步骤2: 浏览器允许应用页面使用摄像头、麦克风设备

浏览器会根据域名进行授权设备的使用权，当 SDK 获取采集设备时，如果没有授权就会弹出授权对话框，需要点击允许才可以进行音视频通话。

- 不同浏览器的对话框是不一样的，这是浏览器内置对话框，目前不支持定制，已知的部分浏览器对话框如下：

桌面端 chrome：

<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/9fcab2136f3142d8a6b89da5918eefc1.png"></image>

桌面端 Safari：

<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/9d81b71ca885a3928368418614f1215f.png"></image>

Android 微信、Chrome：

<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/58cc01ca2b089c3a5af2fde6470a22bd.jpeg"></image>
<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ddf06b774f2f3b08fb379fa23c529c2e.jpeg"></image>

iOS 微信、Safari：

<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ac0c22ed8165262890ef927b44ecffa2.jpeg"></image>
<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ea2c1087e1ae164e80c4f4ad12119caa.jpeg"></image>

- iOS 的浏览器每次都会询问是否授权，目前不支持设置默认授权。
- 在移动端如果选择了禁止授权，通常情况下刷新页面不会重新弹出授权对话框，需要关闭页面，甚至关闭浏览器重新打开页面才能弹出授权对话框。
- 在桌面端如果选择了禁止授权，则需要进入浏览器的设置窗口进行设置，设置路径如下：
  
**Chrome**：点击 URL 前面的安全锁会看到以下界面，按提示设置即可

<image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/d2275e4d02676b7d86a90f6a72922bc2.png"></image>

**Safari**：偏好设置 -> 网站 -> 找到摄像头、麦克风、屏幕共享 -> 按提示设置即可

<image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/bbe99e403a2c5ad91daebb840758a1c6.png"></image>