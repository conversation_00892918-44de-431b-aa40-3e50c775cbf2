## 功能描述
本文主要介绍如何做音量大小的检测。

音量大小的判定主要应用在：

- 检测本地麦克风的音量
- 检测远端用户的音量

## 实现步骤
监听 {@link module:EVENT.AUDIO_VOLUME TRTC.EVENT.AUDIO_VOLUME} 事件，然后调用 {@link TRTC#enableAudioVolumeEvaluation enableAudioVolumeEvaluation()} 开启音量回调事件。

```javascript
trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
    event.result.forEach(({ userId, volume }) => {
        const isMe = userId === ''; // 当 userId 为空串时，代表本地麦克风音量。
        if (isMe) {
            console.log(`my volume: ${volume}`);
        } else {
            console.log(`user: ${userId} volume: ${volume}`);
        }
    })
});

// 开启音量回调，并设置每 500ms 触发一次事件
trtc.enableAudioVolumeEvaluation(500);
// 出于性能的考虑，当页面切换到后台时，SDK 不会抛出音量回调事件。如需在页面切后台时接收音量回调事件，可设置该参数为 true。
trtc.enableAudioVolumeEvaluation(500, true);

// 如需关闭音量回调，传入 interval 值小于等于0即可
trtc.enableAudioVolumeEvaluation(-1);
```

