## 前提条件

1. [注册腾讯云](https://cloud.tencent.com/document/product/378/17985)账号，[创建实时音视频应用](https://console.cloud.tencent.com/trtc/app)。
2. [获取临时 userSig](https://console.cloud.tencent.com/trtc/usersigtool)  ，或者部署 [userSig 签发服务](https://cloud.tencent.com/document/product/647/17275?from_cn_redirect=1#formal)。
3. 为了体验完整的 TRTC 能力，建议开发时使用 `http://localhost` ，生产环境用 `https://[域名]` 访问页面，参考文档[页面访问协议说明](./tutorial-05-info-browser.html#h2-3)。
4. 为了避免防火墙安全策略限制正常的 TRTC 数据传输，需要参考文档[应对防火墙策略](./tutorial-05-info-browser.html#h2-4)进行设置。
5. 为了保证通话体验，建议在正式开始音视频通话前，进行设备检测和浏览器兼容性检测，可以参考文档[浏览器兼容信息](./tutorial-05-info-browser.html#h2-2)，{@tutorial 23-advanced-support-detection}。

## 集成 SDK

SDK 提供了 UMD、ES Module 类型的模块，以及 TypeScript Type Definition 文件，满足在不同类型项目中集成。
### NPM 集成

1. 您可以在项目中使用 [npm](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm) 安装 [trtc-sdk-v5](https://www.npmjs.com/package/trtc-sdk-v5)。
```
npm install trtc-sdk-v5 --save
```
2. 在项目脚本里导入模块。
```javascript
import TRTC from 'trtc-sdk-v5';
```

### Script 集成

1. 在您的 Web 页面中添加如下代码即可：
```html
<script src="trtc.js"></script>
```

**资源下载**
- [单击下载 SDK 及示例代码](https://web.sdk.qcloud.com/trtc/webrtc/v5/download/webrtc_v5_latest.zip)
- [GitHub 仓库地址](https://github.com/LiteAVSDK/TRTC_Web)

## SDK 使用逻辑概览

**基本概念**

您在使用 TRTC Web SDK 时，会接触到以下概念：

- {@link TRTC TRTC} 类，其实例代表一个本地客户端。TRTC 的对象方法提供了加入通话房间、预览本地摄像头、发布本地摄像头和麦克风、播放远端音视频等功能。

**实现音视频通话基本逻辑**
1. 调用 {@link TRTC.create TRTC.create()} 方法创建 {@link TRTC trtc} 对象。
2. 调用 {@link TRTC#enterRoom trtc.enterRoom()} 进入房间，进入后其他用户会收到 {@link module:EVENT.REMOTE_USER_ENTER TRTC.EVENT.REMOTE_USER_ENTER} 进房事件。
3. 在进入房间后，可以开启摄像头和麦克风并发布到房间。
   - 调用 {@link TRTC#startLocalVideo TRTC.startLocalVideo()} 开启摄像头并发布到房间。
   - 调用 {@link TRTC#startLocalAudio TRTC.startLocalAudio()} 开启麦克风并发布到房间。
4. 当一个远端用户发布了音视频后，SDK 默认情况下会自动播放远端音频。您需要通过如下方式来播放远端视频：
   - 在进房前监听 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.EVENT.REMOTE_VIDEO_AVAILABLE} 事件，就能收到所有远端用户的发布视频事件。
   - 在事件回调函数中，调用 {@link TRTC#startRemoteVideo trtc.startRemoteVideo()} 方法播放远端视频。

下图展示了实现音视频通话全过程的基础 API 调用流程：
![](assets/trtc-sdk-call-sequence-cn.png "右键打开图片查看大图")

## 创建 TRTC 对象

通过 {@link TRTC.create TRTC.create()} 方法创建 {@link TRTC TRTC} 对象<br>

```javascript
const trtc = TRTC.create();
```

## 进入房间

调用 {@link TRTC#enterRoom trtc.enterRoom()} 进入房间。通常在`开始通话`按钮的点击回调里进行调用。
关键参数：

- `scene`: 实时音视频通话模式，设置为 'rtc'。
- `sdkAppId`: 您在腾讯云创建的音视频应用的 sdkAppId。
- `userId`: 用户 ID，由您指定。
- `userSig`: 用户签名，参考[获取临时 userSig](https://console.cloud.tencent.com/trtc/usersigtool)，或者部署 [userSig 签发服务](https://cloud.tencent.com/document/product/647/17275?from_cn_redirect=1#formal)。
- `roomId`：房间 ID，由您指定，通常是生成唯一的房间ID。
更详细的参数说明参考接口文档 {@link TRTC#enterRoom trtc.enterRoom()}。

```javascript
try {
  await trtc.enterRoom({ roomId: 8888, scene:'rtc', sdkAppId, userId, userSig });
  console.log('进房成功');
} catch (error) {
  console.error('进房失败 ' + error);
}
```

## 开启摄像头、麦克风

### 开启摄像头

使用 {@link TRTC#startLocalVideo trtc.startLocalVideo()} 方法开启摄像头，并发布到房间。

```javascript
// 为了预览摄像头画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 local-video。
const view = 'local-video';
await trtc.startLocalVideo({ view });
```

### 开启麦克风

使用 {@link TRTC#startLocalAudio trtc.startLocalAudio()} 方法开启麦克风，并发布到房间。

```javascript
await trtc.startLocalAudio();
```

## 播放远端音视频

### 播放远端音频

默认情况下，SDK 会自动播放远端音频，您无需调用 API 来播放远端音频。

- 需要注意的是：如果用户在进房前没有与页面产生过交互，自动播放音频可能会因为【浏览器的自动播放策略限制】而失败，您需参考[自动播放受限处理建议](./tutorial-21-advanced-auto-play-policy.html)进行处理。
- 若您不希望 SDK 自动播放音频，您可以在 {@link TRTC#enterRoom trtc.enterRoom()} 时设置 autoReceiveAudio = false 关闭自动拉流及自动播放音频。
- 监听 {@link module:EVENT.REMOTE_AUDIO_AVAILABLE TRTC.EVENT.REMOTE_AUDIO_AVAILABLE} 事件，记录有远端音频的 userId，在需要播放音频时，调用 {@link TRTC#muteRemoteAudio trtc.muteRemoteAudio(userId, false)} 方法。

### 播放远端视频

在进房前监听 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.EVENT.REMOTE_VIDEO_AVAILABLE} 事件，在收到该事件时，通过 {@link TRTC#startRemoteVideo trtc.startRemoteVideo()} 播放远端视频流。

```javascript
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // 为了播放视频画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 `${userId}_${streamType}`
  const view = `${userId}_${streamType}`;
  trtc.startRemoteVideo({ userId, streamType, view });
});
```

## 退出房间

调用 {@link TRTC#exitRoom trtc.exitRoom()} 方法退出房间，结束音视频通话。

```javascript
await trtc.exitRoom(); 
// 退房成功后，若后续无需使用 trtc 实例，则可以调用 trtc.destroy 方法销毁实例，及时释放相关资源。销毁后的 trtc 实例无法继续使用，需要重新创建新的实例。
trtc.destroy();
```

**处理被踢**

除了用户主动退出房间之外，用户也有可能因为如下原因被踢出房间，此时 SDK 会抛出 {@link module:EVENT.KICKED_OUT KICKED_OUT} 事件，这时不需要调用 `trtc.exitRoom()` 退房，SDK 自动进入退房状态。

1. `kick`：两个相同 userId 的用户进入相同房间，前一个进房的用户会被踢出。同名用户同时进入同一房间是不允许的行为，可能会导致双方音视频通话异常，应避免出现这种情况。
2. `banned`：通过服务端的 [RemoveUser](https://cloud.tencent.com/document/api/647/40496) | [RemoveUserByStrRoomId](https://cloud.tencent.com/document/api/647/50426) 接口将某个用户踢出某个 TRTC 房间，该用户会收到被踢事件，reason 为 `banned` 。
3. `room_disband`：通过服务端的 [DismissRoom](https://cloud.tencent.com/document/api/647/50089) | [DismissRoomByStrRoomId](https://cloud.tencent.com/document/api/647/37088)接口将某个 TRTC 房间解散，解散房间之后，该房间的所有用户都会收到被踢事件，reason 为 `room_disband`。


```javascript
trtc.on(TRTC.EVENT.KICKED_OUT, error => {
  console.error(`kicked out, reason:${error.reason}, message:${error.message}`);
  // error.reason 有以下几种情况
  // 'kick' 由于相同 userId 进相同房间，导致先进入的用户被踢。
  // 'banned' 被管理员移出房间
  // 'room_disband' 管理员解散了房间
});
```

## 联系我们
如在接入实现过程中遇到问题，欢迎到 [GitHub issue](https://github.com/LiteAVSDK/TRTC_Web/issues) 创建 issue，我们会尽快处理。