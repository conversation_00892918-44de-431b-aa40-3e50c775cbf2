## 功能描述
本文主要介绍如何开启调试模式，实现全量日志的云端上传与本地导出功能

调试模式主要应用在：

- 记录全量日志，更好地帮助调试问题
- 支持无法上报日志的私有化客户导出本地的日志文件
- 支持转储本地和远端用户的音频和视频 (v5.8.4开始支持)

## Breaking Change
> 注意: v5.8.4 开始，Debug 插件已内置在 SDK 中，不再支持外部传入插件

## 前提条件
- TRTC Web SDK 版本 >= 5.8.0

## 开启弹窗

### TRTC Web SDK 版本 >= 5.8.4
-  SDK检测到URL参数包含`trtcDebug`时，自动开启弹窗。 例: `www.example.com/?trtcDebug=1`

### TRTC Web SDK 版本 <= 5.8.3
#### 引入并注册插件

```javascript
import { Debug } from 'trtc-sdk-v5/plugins/debug';

let trtc = TRTC.create({ plugins: [Debug] });
```

> 注意: SDK会自动开启Debug插件，您不需要调用`trtc.startPlugin()`方法

#### 开启弹窗
- 开发环境(`file:`, `localhost`, `127.0.0.1`): 自动打开弹窗

- 部署环境: 检测URL参数包含`trtcDebug`时打开弹窗, 例: `www.example.com/?trtcDebug=1`

#### 关闭调试模式

```javascript
trtc.stopPlugin('Debug');
```

## 插件效果
### 全量日志与网络统计信息

<table>
  <tr>
    <th>日志选项卡</th>
    <th>关键信息高亮</th>
    <th>网络选项卡</th>
  </tr>
  <tr>
    <td><img src="./assets/debug/dm-1.png"></td>
    <td><img src="./assets/debug/dm-2.png"></td>
    <td><img src="./assets/debug/dm-3.png"></td>
  </tr>
</table>

### 上传与导出功能
- 上传: 将日志文件上传至TRTC的服务器
- 导出: 将日志文件导出到本地
<table>
  <tr>
    <th>上传</th>
    <th>导出</th>
  </tr>
  <tr>
    <td><img src="./assets/debug/dm-4.png"></td>
    <td><img src="./assets/debug/dm-5.png"></td>
  </tr>
</table>

### 转储音频与视频
- 转储音频: 将本地上行的音频流或远端下行的音频流转储为`.pcm`格式的音频文件
- 转储视频: 将本地上行的视频流或远端下行的视频流转储为`.h264`格式的视频文件
- 使用方法: （1）在输入框输入您要转储的时长，点击`转储`按钮即可开始转储。倒计时结束后，自动下载视频文件到本地。（2）如果您想转储任意时长，可在输入框中输入`0`，点击`转储`按钮开始转储，当您想要结束转储时，点击`结束`按钮即可将视频文件下载到本地
<table>
  <tr>
    <th>转储</th>
  </tr>
  <tr>
    <td><img src="./assets/debug/dm-6.png"></td>
  </tr>
</table>

### 多TRTC实例
如果您创建了多个TRTC实例，每个实例都会初始化一个弹窗。

## 常见问题

### 导出的日志、转储的音视频文件保存在哪里?

浏览器处理下载内容的方式因浏览器、用户设置和其他因素而异。开始保存文件前可能会提示用户，或者文件可能会自动保存，或者文件可能会在外部应用程序、浏览器本身中自动打开。

通常您可以在浏览器的下载记录里找到该文件。
