<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - 浏览器与应用环境信息 - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: 浏览器与应用环境信息</h1>
      <section>
        <header style="display:none">
          <h2>浏览器与应用环境信息</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <p>本文介绍 Web SDK 对浏览器的支持情况，以及访问协议限制和防火墙限制的处理建议。</p>
          <h2>浏览器兼容信息</h2>
          <table>
            <tr>
              <th>操作系统</th>
              <th>浏览器类型</th>
              <th>浏览器最低<br>版本要求</th>
              <th>SDK 版本要求</th>
              <th>接收（拉流）</th>
              <th>发送（推流）</th>
              <th>屏幕分享</th>
            </tr>
            <tr>
              <td rowspan="11">Windows</td>
              <td>桌面版 Chrome 浏览器</td>
              <td>56+</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Chrome72+ 版本</td>
            </tr>
            <tr>
              <td>桌面版 QQ 浏览器（极速内核）</td>
              <td>10.4+</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>桌面版 Firefox 浏览器</td>
              <td>56+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Firefox66+ 版本</td>
            </tr>
            <tr>
              <td>桌面版 Edge 浏览器</td>
              <td>80+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持</td>
            </tr>
            <tr>
              <td>桌面版搜狗浏览器（高速模式）</td>
              <td>11+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持</td>
            </tr>
            <tr>
              <td>桌面版搜狗浏览器（兼容模式）</td>
              <td>-</td>
              <td>-</td>
              <td>不支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>桌面版 Opera 浏览器</td>
              <td>46+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Opera60+ 版本</td>
            </tr>
            <tr>
              <td>桌面版 360 安全浏览器（极速模式）</td>
              <td>13+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持</td>
            </tr>
            <tr>
              <td>桌面版 360 安全浏览器（兼容模式）</td>
              <td>-</td>
              <td>-</td>
              <td>不支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>桌面版微信内嵌浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>桌面版企业微信内嵌浏览器</td>
              <td>4.0.8+（企业微信版本）</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td rowspan="7">Mac OS</td>
              <td>桌面版 Safari 浏览器</td>
              <td>11+</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Safari13+ 版本</td>
            </tr>
            <tr>
              <td>桌面版 Chrome 浏览器</td>
              <td>56+</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Chrome72+ 版本</td>
            </tr>
            <tr>
              <td>桌面版 Firefox 浏览器</td>
              <td>56+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Firefox66+ 版本<a href="#attention3">（注意[3]）</a></td>
            </tr>
            <tr>
              <td>桌面版 Edge 浏览器</td>
              <td>80+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持</td>
            </tr>
            <tr>
              <td>桌面版 Opera 浏览器</td>
              <td>46+</td>
              <td>v4.7.0+</td>
              <td>支持</td>
              <td>支持</td>
              <td>支持 Opera60+ 版本</td>
            </tr>
            <tr>
              <td>桌面版微信内嵌浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>桌面版企业微信内嵌浏览器</td>
              <td>4.0.8+（企业微信版本）</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td rowspan="6">Android</td>
              <td>微信内嵌浏览器（TBS 内核）</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>微信内嵌浏览器（XWEB 内核）</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>企业微信内嵌浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>移动版 Chrome 浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>移动版 QQ 浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>不支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>移动版 UC 浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>不支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS 12.1.4+</td>
              <td>微信内嵌浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS 14.3+</td>
              <td>微信内嵌浏览器</td>
              <td>6.5+（微信版本）</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS</td>
              <td>企业微信内嵌浏览器</td>
              <td>4.0.8+（企业微信版本）</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS 11.0+</td>
              <td>移动版 Safari 浏览器</td>
              <td>11+</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS 12.1.4+</td>
              <td>移动版 Chrome 浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>不支持</td>
              <td>不支持</td>
            </tr>
            <tr>
              <td>iOS 14.3+</td>
              <td>移动版 Chrome 浏览器</td>
              <td>-</td>
              <td>-</td>
              <td>支持</td>
              <td>支持</td>
              <td>不支持</td>
            </tr>
          </table>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>注意：</p>
            <ul>
              <li>其他的浏览器环境可以打开 <a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/detect/index.html">TRTC 检测页面</a> 查看能力支持情况。</li>
              <li>Mac Firefox 屏幕分享可能会出现视频部分区域错位，<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1536777">Firefox bug</a>。暂无法规避，建议使用 Chrome or Safari 浏览器进行屏幕分享。</li>
              <li><a href="./tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a>。</li>
              <li>由于 H.264 版权限制，华为 Chrome 88 以下版本，无法使用 H264 编码（即无法推流）。如果您希望在华为设备 Chrome 浏览器中，使用 TRTC Web SDK 推流，请提交 <a target="_blank" href="https://cloud.tencent.com/apply/p/4ab2rutgukk">腾讯云实时音视频 Web SDK 用户能力支持申请</a> 开通 VP8 编解码能力。</li>
              <li>建议您及时将 TRTC Web SDK 更新至最新版本，以便获得更好的产品稳定性及在线支持。版本升级注意事项请参见：<a href="./tutorial-00-info-update-guideline.html">升级指引</a>。</li>
            </ul>
          </blockquote>
          <h2>页面访问协议说明</h2>
          <p>浏览器厂商出于对用户安全、隐私等问题的考虑，限制网页在 https 协议下才能正常使用 TRTC Web SDK（WebRTC）的全部功能。为确保生产环境用户顺畅接入和体验 TRTC Web SDK 的全部功能，请使用 https 协议访问音视频应用页面。</p>
          <p>注：本地开发可以通过 http://localhost 或者 file:// 协议进行访问。</p>
          <p>URL域名及协议支持情况请参考如下表格：</p>
          <table>
            <thead>
              <tr>
                <th>应用场景</th>
                <th style="text-align:left">协议</th>
                <th style="text-align:left">接收（拉流）</th>
                <th>发送（推流）</th>
                <th>屏幕分享</th>
                <th>备注</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>生产环境</td>
                <td style="text-align:left">https协议</td>
                <td style="text-align:left">支持</td>
                <td>支持</td>
                <td>支持</td>
                <td>推荐</td>
              </tr>
              <tr>
                <td>生产环境</td>
                <td style="text-align:left">http协议</td>
                <td style="text-align:left">支持</td>
                <td>不支持</td>
                <td>不支持</td>
                <td></td>
              </tr>
              <tr>
                <td>本地开发环境</td>
                <td style="text-align:left">http://localhost</td>
                <td style="text-align:left">支持</td>
                <td>支持</td>
                <td>支持</td>
                <td>推荐</td>
              </tr>
              <tr>
                <td>本地开发环境</td>
                <td style="text-align:left">http://127.0.0.1</td>
                <td style="text-align:left">支持</td>
                <td>支持</td>
                <td>支持</td>
                <td></td>
              </tr>
              <tr>
                <td>本地开发环境</td>
                <td style="text-align:left">http://[本机IP]</td>
                <td style="text-align:left">支持</td>
                <td>不支持</td>
                <td>不支持</td>
                <td></td>
              </tr>
              <tr>
                <td>本地开发环境</td>
                <td style="text-align:left">file://</td>
                <td style="text-align:left">支持</td>
                <td>支持</td>
                <td>支持</td>
                <td></td>
              </tr>
            </tbody>
          </table>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>注意：</p>
            <ul>
              <li>如果您的开发环境无法通过localhost访问，也没有 https 协议，建议您使用反向代理工具，将某个 https 域名的访问请求代理到您的开发环境，例如：whistle，fiddler</li>
            </ul>
          </blockquote>
          <h2>应对防火墙策略</h2>
          <p>若用户处于受限网络（例如带有防火墙的企业内网），使用 TRTC Web SDK 可能会受防火墙限制导致无法正常通话。
            请参考：<a href="./tutorial-34-advanced-proxy.html">应对防火墙策略限制</a>。</p>
          <h2>设备授权说明</h2>
          <p>由于操作系统和浏览器的隐私保护策略规定，在浏览器里进行音视频通话，需要在浏览器里获得摄像头、麦克风对授权。设备授权分为两部分：</p>
          <h3>步骤 1: 操作系统允许浏览器使用摄像头、麦克风设备</h3>
          <p>Windows 和 Mac 的设置路径如下：</p>
          <p><strong>Windows</strong>： Windows设置 -&gt; 隐私 -&gt; 摄像头（或者麦克风）-&gt; 允许桌面应用访问你的相机（或者麦克风）</p>
          <p>
            <image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/699b112ba1c8b0e0d4f6edf379d28633.png"></image>
          </p>
          <p><strong>Mac</strong>：系统偏好设置 -&gt; 安全性与隐私 -&gt; 隐私 -&gt; 摄像头（或者麦克风、屏幕录制）-&gt; 允许您的浏览器访问您的摄像头（或者麦克风、屏幕录制）</p>
          <p>
            <image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/a027cc6b3a4a3dde6a7c620c4368271e.png"></image>
          </p>
          <h3>步骤2: 浏览器允许应用页面使用摄像头、麦克风设备</h3>
          <p>浏览器会根据域名进行授权设备的使用权，当 SDK 获取采集设备时，如果没有授权就会弹出授权对话框，需要点击允许才可以进行音视频通话。</p>
          <ul>
            <li>不同浏览器的对话框是不一样的，这是浏览器内置对话框，目前不支持定制，已知的部分浏览器对话框如下：</li>
          </ul>
          <p>桌面端 chrome：</p>
          <p>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/9fcab2136f3142d8a6b89da5918eefc1.png"></image>
          </p>
          <p>桌面端 Safari：</p>
          <p>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/9d81b71ca885a3928368418614f1215f.png"></image>
          </p>
          <p>Android 微信、Chrome：</p>
          <p>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/58cc01ca2b089c3a5af2fde6470a22bd.jpeg"></image>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ddf06b774f2f3b08fb379fa23c529c2e.jpeg"></image>
          </p>
          <p>iOS 微信、Safari：</p>
          <p>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ac0c22ed8165262890ef927b44ecffa2.jpeg"></image>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/ea2c1087e1ae164e80c4f4ad12119caa.jpeg"></image>
          </p>
          <ul>
            <li>iOS 的浏览器每次都会询问是否授权，目前不支持设置默认授权。</li>
            <li>在移动端如果选择了禁止授权，通常情况下刷新页面不会重新弹出授权对话框，需要关闭页面，甚至关闭浏览器重新打开页面才能弹出授权对话框。</li>
            <li>在桌面端如果选择了禁止授权，则需要进入浏览器的设置窗口进行设置，设置路径如下：</li>
          </ul>
          <p><strong>Chrome</strong>：点击 URL 前面的安全锁会看到以下界面，按提示设置即可</p>
          <p>
            <image width="400" src="https://qcloudimg.tencent-cloud.cn/raw/d2275e4d02676b7d86a90f6a72922bc2.png"></image>
          </p>
          <p><strong>Safari</strong>：偏好设置 -&gt; 网站 -&gt; 找到摄像头、麦克风、屏幕共享 -&gt; 按提示设置即可</p>
          <p>
            <image width="680" src="https://qcloudimg.tencent-cloud.cn/raw/bbe99e403a2c5ad91daebb840758a1c6.png"></image>
          </p>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      