<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - Global - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Global</h1>
      <section>
        <header style="display:none">
          <h2>
          </h2>
        </header>
        <article>
          <div class="container-overview">
            <dl class="details">
            </dl>
          </div>
          <h3 class="subsection-title">Type Definitions</h3>
          <h4 class="name" id="TurnServer">TurnServer</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>url</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>TURN 服务器 url</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>username</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>TURN 服务器验证用户名</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>credential</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>TURN 服务器验证密码</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>credentialType</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                  <code>password</code>
                </td>
                <td class="description last">
                  <p>TURN 服务器验证密码类型</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="ProxyServer">ProxyServer</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>websocketProxy</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>websocket 信令服务代理</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>loggerProxy</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>日志上报服务代理</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>turnServer</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#TurnServer">TurnServer</a>></span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>音视频数据传输代理</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>iceTransportPolicy</code></td>
                <td class="type">
                  <span class="param-type">'all'</span>
                  |
                  <span class="param-type">'relay'</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                  <code>'all'</code>
                </td>
                <td class="description last">
                  <p>'all' 优先直连 TRTC，连不通时尝试走 turn server。<br>
                    'relay' 强制走 turn server。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="VideoProfile">VideoProfile</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>width</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频宽度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>height</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频高度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>frameRate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频帧率</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>bitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频码率</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>本地视频流配置</p>
            <p>视频配置参数,可以用字符串预设值或者自定义分辨率等参数</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">视频 Profile</th>
                  <th style="text-align:left">分辨率（宽 x 高）</th>
                  <th style="text-align:left">帧率（fps）</th>
                  <th style="text-align:left">码率（kbps）</th>
                  <th style="text-align:left">备注</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">120p</td>
                  <td style="text-align:left">160 x 120</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">200</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">120p_2</td>
                  <td style="text-align:left">160 x 120</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">100</td>
                  <td style="text-align:left">v5.1.1+ 支持</td>
                </tr>
                <tr>
                  <td style="text-align:left">180p</td>
                  <td style="text-align:left">320 x 180</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">350</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">180p_2</td>
                  <td style="text-align:left">320 x 180</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">150</td>
                  <td style="text-align:left">v5.1.1+ 支持</td>
                </tr>
                <tr>
                  <td style="text-align:left">240p</td>
                  <td style="text-align:left">320 x 240</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">400</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">240p_2</td>
                  <td style="text-align:left">320 x 240</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">200</td>
                  <td style="text-align:left">v5.1.1+ 支持</td>
                </tr>
                <tr>
                  <td style="text-align:left">360p</td>
                  <td style="text-align:left">640 x 360</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">800</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">360p_2</td>
                  <td style="text-align:left">640 x 360</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">400</td>
                  <td style="text-align:left">v5.1.1+ 支持</td>
                </tr>
                <tr>
                  <td style="text-align:left">480p</td>
                  <td style="text-align:left">640 x 480</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">900</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">480p_2</td>
                  <td style="text-align:left">640 x 480</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">500</td>
                  <td style="text-align:left">v5.1.1+ 支持</td>
                </tr>
                <tr>
                  <td style="text-align:left">720p</td>
                  <td style="text-align:left">1280 x 720</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">1500</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">1080p</td>
                  <td style="text-align:left">1920 x 1080</td>
                  <td style="text-align:left">15</td>
                  <td style="text-align:left">2000</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">1440p</td>
                  <td style="text-align:left">2560 x 1440</td>
                  <td style="text-align:left">30</td>
                  <td style="text-align:left">4860</td>
                  <td style="text-align:left"></td>
                </tr>
                <tr>
                  <td style="text-align:left">4K</td>
                  <td style="text-align:left">3840 x 2160</td>
                  <td style="text-align:left">30</td>
                  <td style="text-align:left">9000</td>
                  <td style="text-align:left"></td>
                </tr>
              </tbody>
            </table>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">object</span>
              |
              <span class="param-type">string</span>
            </li>
          </ul>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>const config = {
 option: {
  profile: '480p_2',
 },
}
await trtc.startLocalVideo(config);</code></pre>
          <pre class="highlight lang-javascript"><code>const config = {
 option: {
   profile: {
     width: 640,
     height: 480,
     frameRate: 15,
     bitrate: 500,
   }
 }
}
await trtc.startLocalVideo(config);</code></pre>
          <h4 class="name" id="ScreenShareProfile">ScreenShareProfile</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>width</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>屏幕分享宽度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>height</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>屏幕分享高度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>frameRate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>屏幕分享帧率</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>bitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>屏幕分享码率</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>屏幕分享分辨率码率帧率配置
              屏幕分享配置参数,可以用字符串预设值或者自定义分辨率等参数</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">屏幕 Profile</th>
                  <th style="text-align:left">分辨率（宽 x 高）</th>
                  <th style="text-align:left">帧率（fps）</th>
                  <th style="text-align:left">码率 (kbps)</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">480p</td>
                  <td style="text-align:left">640 x 480</td>
                  <td style="text-align:left">5</td>
                  <td style="text-align:left">900</td>
                </tr>
                <tr>
                  <td style="text-align:left">480p_2</td>
                  <td style="text-align:left">640 x 480</td>
                  <td style="text-align:left">30</td>
                  <td style="text-align:left">1000</td>
                </tr>
                <tr>
                  <td style="text-align:left">720p</td>
                  <td style="text-align:left">1280 x 720</td>
                  <td style="text-align:left">5</td>
                  <td style="text-align:left">1200</td>
                </tr>
                <tr>
                  <td style="text-align:left">720p_2</td>
                  <td style="text-align:left">1280 x 720</td>
                  <td style="text-align:left">30</td>
                  <td style="text-align:left">3000</td>
                </tr>
                <tr>
                  <td style="text-align:left">1080p</td>
                  <td style="text-align:left">1920 x 1080</td>
                  <td style="text-align:left">5</td>
                  <td style="text-align:left">1600</td>
                </tr>
                <tr>
                  <td style="text-align:left">1080p_2</td>
                  <td style="text-align:left">1920 x 1080</td>
                  <td style="text-align:left">30</td>
                  <td style="text-align:left">4000</td>
                </tr>
              </tbody>
            </table>
            <ul>
              <li>屏幕分享默认使用 <code>1080p</code>。</li>
              <li>若以上 Profile 不能满足您的业务需求，您也可以指定自定义的分辨率、帧率和码率。</li>
            </ul>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">object</span>
              |
              <span class="param-type">string</span>
            </li>
          </ul>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const config = {
 option: {
  profile: '720p',
 },
}
await trtc.startScreenShare(config);</code></pre>
          <h4 class="name" id="TRTCStatistics">TRTCStatistics</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>rtt</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>从 SDK 与 云端的往返延时（一个网络包经历 “SDK -&gt; 云端 -&gt; SDK” 的总耗时），单位 ms。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>upLoss</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>从 SDK 到云端的上行丢包率，单位 (%)</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>downLoss</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>从云端到 SDK 的下行丢包率，单位 (%)</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>bytesSent</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>总发送字节数（包含信令数据和音视频数据），单位：字节数（Bytes）。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>bytesReceived</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>总接收字节数（包含信令数据和音视频数据），单位：字节数（Bytes）。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>localStatistics</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#TRTCLocalStatistics">TRTCLocalStatistics</a></span>
                </td>
                <td class="description last">
                  <p>本地的音视频统计信息</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>remoteStatistics</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#TRTCRemoteStatistics">TRTCRemoteStatistics</a>></span>
                </td>
                <td class="description last">
                  <p>远端的音视频统计信息</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="TRTCLocalStatistics">TRTCLocalStatistics</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>audio</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#TRTCAudioStatistic">TRTCAudioStatistic</a></span>
                </td>
                <td class="description last">
                  <p>本地音频统计信息</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>video</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#TRTCVideoStatistic">TRTCVideoStatistic</a>></span>
                </td>
                <td class="description last">
                  <p>本地视频统计信息</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>本地的音视频统计信息</p>
          </div>
          <h4 class="name" id="TRTCRemoteStatistics">TRTCRemoteStatistics</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>远端用户的 userId</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audio</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#TRTCAudioStatistic">TRTCAudioStatistic</a></span>
                </td>
                <td class="description last">
                  <p>远端音频统计信息</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>video</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#TRTCVideoStatistic">TRTCVideoStatistic</a>></span>
                </td>
                <td class="description last">
                  <p>远端视频统计信息</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>远端的音视频统计信息</p>
          </div>
          <h4 class="name" id="TRTCAudioStatistic">TRTCAudioStatistic</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>bitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>音频码率，单位：Kbps</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audioLevel</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>音量大小，0 ~ 1 的浮点数。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>音频统计信息</p>
          </div>
          <h4 class="name" id="TRTCVideoStatistic">TRTCVideoStatistic</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>bitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频码率，单位：Kbps</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>width</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频宽度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>height</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频高度</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>frameRate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>视频帧率</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoType</code></td>
                <td class="type">
                  <span class="param-type">'big'</span>
                  |
                  <span class="param-type">'small'</span>
                  |
                  <span class="param-type">'sub'</span>
                </td>
                <td class="description last">
                  <p>视频类型，大流、小流、辅流。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>视频统计信息</p>
          </div>
          <h4 class="name" id="PluginName">PluginName</h4>
          <dl class="details">
          </dl>
          <div class="description">
            <p>插件名</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">'AudioMixer'</span>
              |
              <span class="param-type">'AIDenoiser'</span>
              |
              <span class="param-type">'CDNStreaming'</span>
            </li>
          </ul>
          <h4 class="name" id="AudioMixerOptions">AudioMixerOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>id</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>给每个传入的音乐 url 设置一个唯一的 ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>url</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>背景音乐地址的 url，支持的格式为 MP3，AAC（以及浏览器支持的其他音频格式）</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>loop</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>背景音乐是否重复播放</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>volume</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>背景音乐的播放音量 (0-1)</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>新增背景音乐插件参数</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="UpdateAudioMixerOptions">UpdateAudioMixerOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>id</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>给背景音乐设置的唯一 ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>loop</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>背景音乐是否重复播放</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>volume</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>背景音乐的播放音量 (0-1)</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>seekFrom</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>从某一秒开始 seek</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>operation</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>对背景音乐的操作: 'pause' ｜ 'resume' ｜ 'stop'</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>更新背景音乐插件参数</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="StopAudioMixerOptions">StopAudioMixerOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>id</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>给背景音乐设置的唯一 ID</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>停止背景音乐插件参数</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="AIDenoiserOptions">AIDenoiserOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>assetsPath</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>降噪资源路径</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>sdkAppId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <p>应用的 SDKAppId</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>当前进房用户的 userId</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>userSig</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>当前进房用户的 userSig</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>开启降噪参数</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="CDNStreamingOptions">CDNStreamingOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>target</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#Target">Target</a></span>
                </td>
                <td class="description last">
                  <p>目标地址，支持转推/转码到腾讯或者第三方 CDN</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>encoding</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#Encoding">Encoding</a></span>
                </td>
                <td class="description last">
                  <p>编码输出参数，推混流模式时必填，您需要指定您预期的转码输出参数。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>mix</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#Mix">Mix</a></span>
                </td>
                <td class="description last">
                  <p>混流配置参数，您需要指定您预期的混流转码配置参数。转推模式下填写则无效。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>CDNStreaming 插件参数，教程可见 <a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="Target">Target</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>publishMode</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PublishMode">PublishMode</a></span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>选择推流模式，可选值为 PublishMode.PublishMainStreamToCDN、PublishMode.publishSubStreamToCDN、PublishMode.publishMixStreamToCDN</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>streamId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>发布 CDN 后的流 ID，默认值为转推流名为 <code>${sdkappid}_${roomid}_${userid}_main</code>。当 roomId 或者 userId 包含大小写英文字母(a-zA-Z)、数字(0-9)、连字符及下划线之外的特殊字符时，转推流名为 <code>${sdkAppId}_${md5(${roomId}_${userId}_main)}</code></p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>appId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>应用 ID（当转推到第三方 CDN 时必填）</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>bizId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>默认推流域名的前 6 个数字（当转推到第三方 CDN 时必填）</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>url</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>第三方 CDN 的推流地址（当转推到第三方 CDN 时必填）</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="Encoding">Encoding</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>videoWidth</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后视频分辨率的宽度（px），转码后视频的宽度设置必须大于等于 0 且能容纳所有混入视频，默认值为 640</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoHeight</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后视频分辨率的高度（px），转码后视频的高度设置必须大于等于 0 且能容纳所有混入视频，默认值为 480</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoBitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>转码后的视频码率（kbps），如果传入值为 0，码率值将由 videoWidth 和 videoHeight 决定</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoFramerate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后的视频帧率（fps），默认值为 15，取值范围为 (0, 30]</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoGOP</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后的视频关键帧间隔（s），默认值为 2，取值范围为 [1, 8]</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audioSampleRate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后的音频采样率（Hz），默认值为 48000</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audioBitrate</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后的音频码率（kbps），默认值为 64，取值范围为 [32, 192]</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audioChannels</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>转码后的音频声道数，默认值为 1，取值范围为 1 或 2</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>编码配置（推混流时必填）</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="Mix">Mix</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>backgroundColor</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>混合后画面的背景颜色，格式为十六进制数字，默认值为 0x000000（黑色）</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>backgroundImage</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>混合后画面的背景图，默认值为 ''。背景图需要事先在实时音视频控制台的应用管理 &gt; 功能配置 &gt; 素材管理中上传，并获取对应的图片ID，然后将图片ID转换成字符串类型并设置到 backgroundImage 中</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>audioMixUserList</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#AudioMixUser">AudioMixUser</a>></span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>音频混流用户列表</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>videoLayoutList</code></td>
                <td class="type">
                  <span class="param-type">Array.&lt;<a href="global.html#VideoLayout">VideoLayout</a>></span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>混流中的视频布局，必须包含自己</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>混流配置（推混流时必填）</p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="AudioMixUser">AudioMixUser</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>用户ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>roomId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>房间ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>strRoomId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>字符串类型的房间ID</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="VideoLayout">VideoLayout</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>fixedVideoUser</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#FixedVideoUser">FixedVideoUser</a></span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>该布局对应的用户</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>fixedVideoStreamType</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>用于指定混入流类型，TRTC.TYPE.STREAM_TYPE_MAIN 为摄像头流，TRTC.TYPE.STREAM_TYPE_SUB 为屏幕分享流</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>fillMode</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>该用户流在混流中的渲染模式，默认为裁剪模式。0：裁剪模式，1：用户流缩放并显示背景，2：用户流缩放并显示黑底</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>zOrder</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>该用户流在混流中的图层层次，取值范围为 [1, 15]</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>width</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>该用户流在混流中的宽度（px），必须大于等于 0，默认值为 0</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>height</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>该用户流在混流中的高度（px），必须大于等于 0，默认值为 0</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>locationX</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>以混流左上角为起点，该用户流在混流中的 X 坐标（px），必须大于等于 0，默认值为 0</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>locationY</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>以混流左上角为起点，该用户流在混流中的 Y 坐标（px），必须大于等于 0，默认值为 0</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="FixedVideoUser">FixedVideoUser</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>该用户ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>roomId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>该用户所在的房间ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>strRoomId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>字符串类型的房间ID</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="PublishMode">PublishMode</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>PublishMainStreamToCDN</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>推送主画面到 CDN</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>publishSubStreamToCDN</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>推送辅流到 CDN</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>publishMixStreamToCDN</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>推送混流到 CDN</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Enum</span>
            </li>
          </ul>
          <h4 class="name" id="VirtualBackgroundOptions">VirtualBackgroundOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>sdkAppId</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>当前应用 ID，可在 <a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a> 获取</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>用户ID。建议限制长度为32字节，只允许包含大小写英文字母(a-zA-Z)、数字(0-9)及下划线和连词符。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>userSig</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>计算 userSig 的方式请参考 <a target="_blank" href="https://cloud.tencent.com/document/product/647/17275">UserSig 相关</a>。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>type</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>虚拟背景类型。填入 <code>'blur'</code> 代表模糊背景；填入 <code>'image'</code> 代表图片背景，需要传入图片 url。默认为 blur。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>src</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>设置类型为图片背景时，需通过此参数传入图片 url。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>VirtualBackground 插件启动参数，教程可见 <a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
          <h4 class="name" id="UpdateVirtualBackgroundOptions">UpdateVirtualBackgroundOptions</h4>
          <dl class="details">
          </dl>
          <h5 class="subsection-title">Properties:</h5>
          <table class="props">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>type</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>虚拟背景类型。填入 <code>'blur'</code> 代表模糊背景；填入 <code>'image'</code> 代表图片背景，需要传入图片 url。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>src</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>设置类型为图片背景时，需通过此参数传入图片 url。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="description">
            <p>VirtualBackground 插件更新参数，教程可见 <a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></p>
          </div>
          <h5>Type:</h5>
          <ul>
            <li>
              <span class="param-type">Object</span>
            </li>
          </ul>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      