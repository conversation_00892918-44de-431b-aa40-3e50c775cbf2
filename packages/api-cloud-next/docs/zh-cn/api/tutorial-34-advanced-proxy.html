<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - 应对防火墙受限 - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: 应对防火墙受限</h1>
      <section>
        <header style="display:none">
          <h2>应对防火墙受限</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>功能描述</h2>
          <p>本文主要介绍应对防火墙受限的最佳实践。例如在企业内网这类有防火墙的网络环境中，往往由于防火墙的原因，无法正常使用 TRTC Web SDK。这种情况下，有两种解决方案：</p>
          <ul>
            <li>方案一：监听 SDK 错误，引导更换网络 或者 配置防火墙白名单。</li>
            <li>方案二：使用 Nginx + coturn 代理方案。</li>
          </ul>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>TRTC Web SDK 默认使用 UDP 与 TRTC 服务器进行媒体通信，并且有内置的 Turn Server，支持以 UDP or TCP 中转媒体数据。
              正常在公网下，用户无需搭建任何代理，SDK 会按直连、Turn Server UDP、Turn Server TCP 的顺序尝试建立媒体连接。
              若明确知道用户将在内网防火墙内使用，则可能会遇到无法建立媒体连接，此时则需要搭建代理。</p>
          </blockquote>
          <h2>前提条件</h2>
          <ul>
            <li>方案二需要搭建两个服务器，Nginx + Turn Server，您可以联系贵公司的运维同学协助帮忙搭建。Nginx 代理服务器用于代理转发 TRTC Web SDK 的 Websocket 信令数据包；Turn Server 用于转发音视频数据包。</li>
          </ul>
          <h2>实现流程</h2>
          <h3>方案一</h3>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>该方案适用于：您无法确定用户的网络是否会受防火墙限制，此时可以监听 SDK 错误，引导用户更换网络 or 检查防火墙。</p>
          </blockquote>
          <p>当您调用 startLocalVideo, startLocalAudio, startRemoteVideo 等 API 时，SDK 内部会建立媒体连接通道，用于传输音视频数据。当遇到防火墙受限时，SDK 可能无法建立连接，此时 SDK 会抛出防火墙受限的错误，并继续进行重试。</p>
          <p>您可以参照如下代码示例，监听该错误，并引导用户更换网络 or 检查网络防火墙，对 TRTC Web SDK 使用到的域名和端口开白名单。参考：<a target="_blank" href="https://cloud.tencent.com/document/product/647/34399#webrtc-.E9.9C.80.E8.A6.81.E9.85.8D.E7.BD.AE.E5.93.AA.E4.BA.9B.E7.AB.AF.E5.8F.A3.E6.88.96.E5.9F.9F.E5.90.8D.E4.B8.BA.E7.99.BD.E5.90.8D.E5.8D.95.EF.BC.9F">TRTC Web SDK 域名和端口白名单</a>。</p>
          <pre class="highlight lang-javascript source lang-js"><code>trtc.on(TRTC.EVENT.ERROR, error => {
  // 用户网络防火墙受限，会导致无法正常进行音视频通话。
  // 此时引导用户更换网络 or 检查网络防火墙设置。
  if (error.code === TRTC.ERROR_CODE.OPERATION_FAILED && error.extraCode === 5501) {
  }
});
</code></pre>
          <h3>方案二</h3>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>该方案适用于：您确定用户的网络是受防火墙限制的，此时需要搭建代理服务器来解决。</p>
          </blockquote>
          <table>
            <thead>
              <tr>
                <th>方案</th>
                <th>适用场景</th>
                <th>网络要求</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>A</td>
                <td>用户网络可以访问特定的外网代理服务器</td>
                <td>代理服务器搭建在外网，内网防火墙需开通白名单，允许内网用户访问外网的代理服务器。</td>
              </tr>
              <tr>
                <td>B</td>
                <td>用户网络只能访问内网代理服务器</td>
                <td>代理服务器搭建在内网，内网防火墙需开通白名单，允许内网代理服务器访问外网。</td>
              </tr>
            </tbody>
          </table>
          <div style="display: flex;justify-content: center;">
            <img src="./assets/proxy-1.png" />
          </div>
          <div style="display: flex;justify-content: center;">
            A 方案示例图
          </div>
          <div style="display: flex;justify-content: center;">
            <img src="./assets/proxy-2.png" />
          </div>
          <div style="display: flex;justify-content: center;">B 方案示例图</div>
          <h3>设置代理服务器</h3>
          <p>当您搭建好 Nginx 和 Turn server 后，可以参考如下示例，设置代理服务器。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>const trtc = TRTC.create(); 
await trtc.enterRoom({
  ...,
  proxy: {
    // 设置 Websocket 代理，用于中转 SDK 与 TRTC 后台的信令数据包。
    websocketProxy: 'wss://proxy.example.com/ws/',
    // 设置 turn server，用于中转 SDK 与 TRTC 后台的媒体数据包。********:3478 为 turn server 的 ip 及端口，
    turnServer: { url: '********:3478', username: 'turn', credential: 'turn', credentialType: 'password' }
    // SDK 默认会向 yun.tim.qq.com 域名上报日志，但若该域名在您的内网无法访问，您需要给该域名开白名单或者配置以下日志代理。
    // 设置日志上报代理，日志是排查问题的关键数据，请务必设置该代理。
    loggerProxy: 'https://proxy.example.com/logger/',
  }
})
</code></pre>
          <h3>A 方案</h3>
          <h4>搭建 Nginx 服务器</h4>
          <ol>
            <li>
              <p>部署 Nginx 服务器</p>
              <p>参考互联网中搜索到的 Nginx 服务器部署教程进行搭建部署。如果企业已有部署 Nginx 服务，可以不用部署直接进行配置。</p>
            </li>
            <li>
              <p>配置 Nginx 服务器</p>
              <pre class="highlight lang-javascript source lang-sh"><code>vi /etc/nginx/nginx.conf 
</code></pre>
              <pre class="highlight lang-javascript source"><code>http {
  server { 
    # nginx 服务器的访问域名
    server_name proxy.example.com; 
    # nginx 服务器的访问端口
    listen 443; 
    ssl on; 
    location /ws/ { # 对应 setProxyServer 中的 websocketProxy 参数
      proxy_pass https://signaling.rtc.qq.com/; # TRTC 的服务器
      proxy_http_version 1.1; 
      proxy_set_header Upgrade $http_upgrade; 
      proxy_set_header Connection &quot;upgrade&quot;; 
    }
    location /logger/ { # 对应 setProxyServer 中的 loggerProxy 参数
      proxy_pass https://yun.tim.qq.com/;
    }
    #域名对应的 SSL 证书，HTTPS 用，用户自行申请 
    ssl_certificate ./crt/1_proxy.trtcapi.com_bundle.crt; 
    ssl_certificate_key ./crt/2_proxy.trtcapi.com.key; 
  }
}
</code></pre>
            </li>
            <li>
              <p>执行重新加载 Nginx</p>
              <pre class="highlight lang-javascript source lang-sh"><code>sudo nginx -s reload
</code></pre>
            </li>
            <li>
              <p>确认公司防火墙允许访问 Nginx 服务器 IP 及端口</p>
            </li>
          </ol>
          <h4>搭建 Turn 服务器</h4>
          <p>您可以在互联网中搜索 turn server 搭建教程进行搭建；也可以使用如下在 <code>Centos 中搭建 turn server </code>的脚本进行搭建。</p>
          <ol>
            <li>
              <p>在 Linux 服务器中，创建脚本文件 <code>turn.sh</code>，脚本内容如下：</p>
              <pre class="highlight lang-javascript source lang-sh"><code>#!/usr/bin/env bash
# current file name is turn.sh
# ref:
# https://gabrieltanner.org/blog/turn-server    STEP 3 testing turn server
# https://medium.com/av-transcode/what-is-webrtc-and-how-to-setup-stun-turn-server-for-webrtc-communication-63314728b9d0
# as super-user
# usage:  current_program &lt;external-ip>
set -x
set -e
ip a
pwd
whoami
display_usage() {
        echo &quot;This script must be run with super-user privileges.&quot;
        echo -e &quot;\nUsage: $0 &lt;external-ip> \ne.g. $0 *************&quot;
}
# if less than two arguments supplied, display usage
if [ $# -lt 1 ]
then
        display_usage
        exit 1
fi
if [[ $1 =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
  echo &quot;get external ip $1&quot;
else
  echo &quot;wrong external ip $1 , must not have whitespace, tab and other char&quot;
  exit 2
fi
yum install -y coturn
# $1 is &lt;external-ip>
cat &lt;&lt;EOF > /etc/coturn/turnserver.conf
external-ip=$1
listening-port=3478
lt-cred-mech
max-port=65535
min-port=20000
no-dtls
no-tls
realm=tencent
user=turn:turn
verbose
EOF
</code></pre>
            </li>
            <li>
              <p>增加可执行权限</p>
              <pre class="highlight lang-javascript source lang-sh"><code>chmod +x turn.sh
</code></pre>
            </li>
            <li>
              <p>以 root 身份执行脚本，<code>sudo ./turn.sh &lt;服务器外网IP&gt;</code>， 例如：</p>
              <pre class="highlight lang-javascript source lang-sh"><code>sudo ./turn.sh ********
</code></pre>
            </li>
            <li>
              <p>启动 turn server</p>
              <pre class="highlight lang-javascript source lang-sh"><code>systemctl start coturn
# 检测 turn 是否启动成功
ps aux | grep coturn
# 若要重启服务，则执行
service coturn restart 
</code></pre>
            </li>
            <li>
              <p>配置 turn 服务器的防火墙，打开入站端口 3478（TCP &amp; UDP），及上述配置中 min ~ max port 之间的出站端口（UDP）。</p>
            </li>
            <li>
              <p>配置公司内网防火墙，允许访问 turn 服务器的 IP，及打开出站端口 3478（TCP &amp; UDP）。</p>
            </li>
            <li>
              <p>测试 turn server</p>
              <p>使用该<a target="_blank" href="https://webrtc.github.io/samples/src/content/peerconnection/trickle-ice/">测试页面</a>，测试搭建好的 turn server 是否能够正常连通访问。如下截图中 done 说明 turn 能正常访问。</p>
              <img src="./assets/turn-test.png" alt="turn-test" style="zoom: 33%;" />
            </li>
          </ol>
          <h3>B 方案</h3>
          <p>B 方案搭建 Nginx 代理的方式，与 A 方案相同。主要有两处区别：</p>
          <ol>
            <li>搭建 turn server 时，配置文件中的 external-ip 字段需填写服务器在您企业内网的地址。</li>
          </ol>
          <pre class="highlight lang-javascript source lang-sh"><code># A 方案中启动脚本填的是服务器外网地址，例如外网地址为 ********
sudo ./turn.sh ********
# B 方案中启动脚本填的是服务器在企业内网地址，例如内网地址为 ********
sudo ./turn.sh ********
</code></pre>
          <ol start="2">
            <li>防火墙配置：</li>
          </ol>
          <ul>
            <li>对于 Nginx 服务器，需要在公司内网防火墙配置域名白名单，允许 Nginx 服务器访问 TRTC 的相关域名，参考：<a target="_blank" href="https://cloud.tencent.com/document/product/647/34399#webrtc-.E9.9C.80.E8.A6.81.E9.85.8D.E7.BD.AE.E5.93.AA.E4.BA.9B.E7.AB.AF.E5.8F.A3.E6.88.96.E5.9F.9F.E5.90.8D.E4.B8.BA.E7.99.BD.E5.90.8D.E5.8D.95.EF.BC.9F">TRTC Web SDK 域名和端口白名单</a></li>
            <li>对于 Turn Server，需要允许 Turn Server 能够访问外网。</li>
          </ul>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      