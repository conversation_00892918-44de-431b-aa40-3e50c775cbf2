<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - ERROR_CODE - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">ERROR_CODE</h1>
      <section>
        <header style="display:none">
        </header>
        <article>
          <div class="container-overview">
            <div class="description">
              <p>TRTC SDK v5.0 定义了8种错误码类型，通过 RtcError 对象来获取 errorCode 并做相应的处理。</p>
            </div>
            <dl class="details">
              <dt class="tag-see">See:</dt>
              <dd class="tag-see">
                <ul>
                  <li><a href="RtcError.html">RtcError</a></li>
                  <li><a href="module-EVENT.html#.ERROR">TRTC.EVENT.ERROR</a></li>
                </ul>
              </dd>
            </dl>
            <h5>Example</h5>
            <pre class="highlight lang-javascript"><code>// 使用方式：
// 1. API 调用错误
trtc.startLocalVideo().catch(error => {
 if (error.code === TRTC.ERROR_CODE.DEVICE_ERROR) {}
});
// 2. 非 API 调用错误，SDK 内部经过重试后依然无法恢复的错误
trtc.on(TRTC.EVENT.ERROR, (error) => {
   if (error.code === TRTC.ERROR_CODE.OPERATION_FAILED) {}
});</code></pre>
          </div>
          <h3 class="subsection-title">Members</h3>
          <h4 class="name" id=".INVALID_PARAMETER"><span class="type-signature">(static) </span>INVALID_PARAMETER<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5000</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：调用接口时传入了不满足 API 要求的参数 <br>
              处理建议：请检查传入参数是否符合 API 的规范，例如参数类型是否正确。</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5001</td>
                  <td style="text-align:left">说明：未填必填参数</td>
                </tr>
                <tr>
                  <td style="text-align:left">5002</td>
                  <td style="text-align:left">说明：参数类型不对</td>
                </tr>
                <tr>
                  <td style="text-align:left">5003</td>
                  <td style="text-align:left">说明：参数传了空值</td>
                </tr>
                <tr>
                  <td style="text-align:left">5004</td>
                  <td style="text-align:left">说明：参数原型不对</td>
                </tr>
                <tr>
                  <td style="text-align:left">5005</td>
                  <td style="text-align:left">说明：参数不在规定的取值范围</td>
                </tr>
                <tr>
                  <td style="text-align:left">5006</td>
                  <td style="text-align:left">说明：参数要大于等于 0</td>
                </tr>
                <tr>
                  <td style="text-align:left">5007</td>
                  <td style="text-align:left">说明：参数要大于最小值</td>
                </tr>
                <tr>
                  <td style="text-align:left">5008</td>
                  <td style="text-align:left">说明：参数要小于最大值</td>
                </tr>
                <tr>
                  <td style="text-align:left">5009</td>
                  <td style="text-align:left">说明：该 elementId 在页面上找不到，或者查找时不在页面上</td>
                </tr>
                <tr>
                  <td style="text-align:left">5010</td>
                  <td style="text-align:left">说明：传入的参数不是 HTMLElement 类型</td>
                </tr>
                <tr>
                  <td style="text-align:left">5011</td>
                  <td style="text-align:left">说明：streamId 不符合规范</td>
                </tr>
                <tr>
                  <td style="text-align:left">5012</td>
                  <td style="text-align:left">说明：传入 roomId 的范围不符合规范 [1, 4294967294]</td>
                </tr>
                <tr>
                  <td style="text-align:left">5013</td>
                  <td style="text-align:left">说明：传入的 strRoomId 类型不是合法的 string</td>
                </tr>
                <tr>
                  <td style="text-align:left">5014</td>
                  <td style="text-align:left">说明：当 userId 不是 '*'时，需要传入 streamType</td>
                </tr>
                <tr>
                  <td style="text-align:left">5015</td>
                  <td style="text-align:left">说明：未填写 roomId 或 strRoomId, roomId 和 strRoomId 必须填写一个</td>
                </tr>
                <tr>
                  <td style="text-align:left">5016</td>
                  <td style="text-align:left">说明：roomId 必须是数字类型，当前传入了字符串类型，如果需要使用字符串作为房间号，请使用 strRoomId</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".INVALID_OPERATION"><span class="type-signature">(static) </span>INVALID_OPERATION<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5100</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：调用接口时，不满足 API 的前提要求。 <br>
              处理建议：请根据对应 API 文档检查调用逻辑是否符合 API 的前提要求。例如：1.未进房成功就进行切换角色，2.播放的远端用户和流不存在。</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5101</td>
                  <td style="text-align:left">说明：在未进房的情况下调用 API，例如 startRemoteVideo muteRemoteAudio switchRole 等 API 需在进房后调用</td>
                </tr>
                <tr>
                  <td style="text-align:left">5102</td>
                  <td style="text-align:left">说明：远端用户不存在，例如在 startRemoteVideo 时，传入的 userId 对应的远端用户不在房间内。</td>
                </tr>
                <tr>
                  <td style="text-align:left">5103</td>
                  <td style="text-align:left">说明：远端流类型不存在，例如在 startRemoteVideo 时，传入的 streamType = TRTC.TYPE.STREAM_TYPE.SUB，但对应的远端用户并没有推屏幕分享。</td>
                </tr>
                <tr>
                  <td style="text-align:left">5104</td>
                  <td style="text-align:left">说明：重复调用 API，例如在进房成功后，又调用 enterRoom 接口。</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".ENV_NOT_SUPPORTED"><span class="type-signature">(static) </span>ENV_NOT_SUPPORTED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5200</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：当前环境不支持该功能，表明当前浏览器不支持调用对应 API <br>
              处理建议：通常使用 TRTC.isSupported 可感知当前浏览器支持哪些能力。如果浏览器不支持，需要引导用户使用支持该能力的浏览器，参考：<a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-23-advanced-support-detection.html">检测浏览器支持性</a></p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5201</td>
                  <td style="text-align:left">说明：当前页面协议非 HTTPS，不支持音视频采集（推流、连麦）能力</td>
                </tr>
                <tr>
                  <td style="text-align:left">5202</td>
                  <td style="text-align:left">说明：当前浏览器不支持 WebRTC 能力，浏览器版本过低</td>
                </tr>
                <tr>
                  <td style="text-align:left">5203</td>
                  <td style="text-align:left">说明：浏览器不支持 H264 编码能力</td>
                </tr>
                <tr>
                  <td style="text-align:left">5204</td>
                  <td style="text-align:left">说明：浏览器不支持 H264 解码能力</td>
                </tr>
                <tr>
                  <td style="text-align:left">5205</td>
                  <td style="text-align:left">说明：浏览器不支持屏幕分享能力</td>
                </tr>
                <tr>
                  <td style="text-align:left">5206</td>
                  <td style="text-align:left">说明：浏览器不支持小流编码能力</td>
                </tr>
                <tr>
                  <td style="text-align:left">5207</td>
                  <td style="text-align:left">说明：浏览器不支持 SEI 收发能力</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".DEVICE_ERROR"><span class="type-signature">(static) </span>DEVICE_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5300</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：获取设备或者采集音视频出现异常。这些接口出现异常时会抛出该错误码：<a href="TRTC.html#startLocalAudio">trtc.startLocalAudio</a> <a href="TRTC.html#startLocalVideo">trtc.startLocalVideo</a> <a href="TRTC.html#startScreenShare">trtc.startScreenShare</a>。<br><br>
              处理建议：引导用户检查设备是否有摄像头及麦克风、系统是否给浏览器授权以及浏览器是否给页面授权。建议增加进房前的设备检测流程，确认麦克风和摄像头是否存在，并且能正常采集，再进行下一步通话操作。通常经过设备检查后都能避免该异常。实现方式请参考: <a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a>。<br><br>
              如果需要区分更详细的异常类别，可以按照以下 extraCode 进行处理：</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5301</td>
                  <td style="text-align:left">说明：由浏览器抛出的 NotFoundError(DOMException) <br> 原因：找不到满足请求参数的媒体设备类型（包括：音频、视频、屏幕分享）。例如：PC 没有摄像头，但是请求浏览器获取视频流，则会报此错误。 <br> 处理建议：建议在通话开始前引导用户检查通话所需的摄像头或麦克风等外设。</td>
                </tr>
                <tr>
                  <td style="text-align:left">5302</td>
                  <td style="text-align:left">说明：由浏览器抛出的 NotAllowedError(DOMException) <br>原因：<li>用户拒绝了当前的浏览器实例的采集麦克风、摄像头、屏幕分享请求。</li>
                    <li>系统关闭了浏览器的麦克风、摄像头采集权限。</li><br> 处理建议：<li>提示用户授权摄像头/麦克风访问才可以进行音视频通话。</li>
                    <li>若是由于系统关闭了浏览器权限，rtcError 会有 <a href="RtcError.html#.handler">rtcError.handler</a> 方法，调用该方法可以跳转到系统权限设置中，方便用户开启权限。</li>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:left">5303</td>
                  <td style="text-align:left">说明：由浏览器抛出的 NotReadableError(DOMException) <br> 原因：尽管用户已经授权使用相应的设备，但是由于操作系统上某个硬件、浏览器或者网页层面发生的错误或者其他应用占用了设备导致设备无法被浏览器访问。<br> 处理建议：根据浏览器的报错信息处理，并提示用户：“暂时无法访问摄像头/麦克风，请确保当前没有其他应用请求访问摄像头/麦克风，并重试”</td>
                </tr>
                <tr>
                  <td style="text-align:left">5304</td>
                  <td style="text-align:left">说明：由浏览器抛出的 OverconstrainedError(DOMException) <br> 原因：cameraId/microphoneId 参数的值无效 <br> 处理建议：检查 cameraId/microphoneId 是否是通过获取设备信息接口返回的值</td>
                </tr>
                <tr>
                  <td style="text-align:left">5305</td>
                  <td style="text-align:left">说明：由浏览器抛出的 InvalidStateError(DOMException) <br> 原因：当前页面未产生交互，页面不是完全激活的 <br> 处理建议：建议经过用户与页面产生点击交互后，再开启摄像头麦克风</td>
                </tr>
                <tr>
                  <td style="text-align:left">5306</td>
                  <td style="text-align:left">说明：由浏览器抛出的 SecurityError(DOMException) <br> 原因：由于系统安全策略禁止使用设备<br> 处理建议：检查系统是否限制使用设备，并建议经过用户与页面产生点击交互后，再开启摄像头麦克风</td>
                </tr>
                <tr>
                  <td style="text-align:left">5307</td>
                  <td style="text-align:left">说明：由浏览器抛出的 AbortError(DOMException) <br> 原因：由于某些未知原因导致设备无法被使用 <br> 处理建议：建议更换设备或者浏览器，重新检测设备是否正常</td>
                </tr>
                <tr>
                  <td style="text-align:left">5308</td>
                  <td style="text-align:left">说明：摄像头采集异常，经过 SDK 重试任然无法恢复采集。 <br> 原因：摄像头异常或者用户手动关闭了浏览器的采集权限导致 <br> 处理建议：提示用户摄像头采集异常，引导用户检查摄像头是否正常、是否有采集权限</td>
                </tr>
                <tr>
                  <td style="text-align:left">5309</td>
                  <td style="text-align:left">说明：麦克风采集异常，经过 SDK 重试任然无法恢复采集。 <br> 原因：麦克风异常或者用户手动关闭了浏览器的采集权限导致 <br> 处理建议：提示用户麦克风采集异常，引导用户检查麦克风是否正常、是否有采集权限</td>
                </tr>
              </tbody>
            </table>
            <p>参考资料：<a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/API/MediaDevices/getUserMedia#%E5%BC%82%E5%B8%B8">getUserMedia 异常</a> 和 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/API/MediaDevices/getDisplayMedia#%E5%BC%82%E5%B8%B8">getDisplayMedia 异常</a></p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.startLocalVideo(...).catch(function(rtcError) {
 if(rtcError.code == TRTC.ERROR_CODE.DEVICE_ERROR) {
   // 引导用户检查设备
   // 以下为可选代码
   switch(rtcError.extraCode) {
     case 5301:
       // 找不到摄像头或者麦克风，引导用户检查麦克风和摄像头是否正常。
       break;
     case 5302:
       if (error.handler) {
         // 提示用户系统关闭了浏览器的摄像头、麦克风、屏幕分享采集权限，即将跳转至系统权限设置 APP，请打开相关权限后，重启浏览器重试。
       } else {
         // 引导用户允许摄像头、麦克风、屏幕分享采集权限
       }
       break;
     // ...
   }
 }
})</code></pre>
          <h4 class="name" id=".SERVER_ERROR"><span class="type-signature">(static) </span>SERVER_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5400</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：收到服务端返回的异常数据时抛出该错误码<br>
              以下接口出现异常时会抛出该错误码：<code>enterRoom</code>，<code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              处理建议：服务端异常通常在开发阶段处理，常见的异常有：传入的 userSig 过期，腾讯云账号欠费，未开通TRTC服务等，服务端返回异常数据有以下原因。</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">-8</td>
                  <td style="text-align:left">sdkAppId 不正确，请检查 sdkAppId 是否正确填写</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10012</td>
                  <td style="text-align:left">未传入 roomId 或者 roomId 不符合规范, 如需使用 string 类型的 roomId，请在调用 trtc.enterRoom 时使用 strRoomId</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10015</td>
                  <td style="text-align:left">服务端获取服务器节点失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10016</td>
                  <td style="text-align:left">服务端内部通信超时，超时时间 3s</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100006</td>
                  <td style="text-align:left">检查权限失败，启用高级权限控制后，请检查 <a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/Client.html#join">client.join</a> 携带的 privateMapKey 参数是否正确。请查看 <a target="_blank" href="https://cloud.tencent.com/document/product/647/32240">开启高级权限设置 </a></td>
                </tr>
                <tr>
                  <td style="text-align:left">-100013</td>
                  <td style="text-align:left">客户服务欠费, 请登录 <a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a>，单击您创建的应用，单击【帐号信息】，在帐号信息面板即可确认服务状态</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100021</td>
                  <td style="text-align:left">服务端过载，进房失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100022</td>
                  <td style="text-align:left">服务器分配失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100024</td>
                  <td style="text-align:left">未开通 TRTC 服务导致进房失败，请到 $<a target="_blank" href="https://console.cloud.tencent.com/im">IM 控制台</a> 为您的应用开通 TRTC 服务</td>
                </tr>
                <tr>
                  <td style="text-align:left">-102006</td>
                  <td style="text-align:left">流控定义的错误码（add user failed）</td>
                </tr>
                <tr>
                  <td style="text-align:left">-102010</td>
                  <td style="text-align:left">启用高级权限控制后，用户没有创建房间的权限，请查看 <a target="_blank" href="https://cloud.tencent.com/document/product/647/32240">开启高级权限设置 </a></td>
                </tr>
                <tr>
                  <td style="text-align:left">-102023</td>
                  <td style="text-align:left">请求参数错误（后端接口服务产生的请求参数错误）</td>
                </tr>
                <tr>
                  <td style="text-align:left">70001</td>
                  <td style="text-align:left">userSig 过期，请尝试重新生成。如果是刚生成就过期，请检查有效期填写的是否过小或者误填为 0</td>
                </tr>
                <tr>
                  <td style="text-align:left">70002</td>
                  <td style="text-align:left">userSig 长度为 0，请确认签名计算是否正确，访问 sign_src 获取计算签名的傻瓜式源码，核对参数，确保签名计算正确性</td>
                </tr>
                <tr>
                  <td style="text-align:left">70003</td>
                  <td style="text-align:left">userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断</td>
                </tr>
                <tr>
                  <td style="text-align:left">70004</td>
                  <td style="text-align:left">userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断</td>
                </tr>
                <tr>
                  <td style="text-align:left">70005</td>
                  <td style="text-align:left">userSig 校验失败，通过工具来验证生成的 userSig 是否正确</td>
                </tr>
                <tr>
                  <td style="text-align:left">70006</td>
                  <td style="text-align:left">userSig 校验失败，通过工具来验证生成的 userSig 是否正确</td>
                </tr>
                <tr>
                  <td style="text-align:left">70007</td>
                  <td style="text-align:left">userSig 校验失败，通过工具来验证生成的 userSig 是否正确</td>
                </tr>
                <tr>
                  <td style="text-align:left">70008</td>
                  <td style="text-align:left">userSig 校验失败，通过工具来验证生成的 userSig 是否正确</td>
                </tr>
                <tr>
                  <td style="text-align:left">70009</td>
                  <td style="text-align:left">用业务公钥验证 userSig 失败，请确认生成的 userSig 使用的私钥和 sdkAppId 是否对应</td>
                </tr>
                <tr>
                  <td style="text-align:left">70010</td>
                  <td style="text-align:left">userSig 校验失败，通过工具来验证生成的 userSig 是否正确</td>
                </tr>
                <tr>
                  <td style="text-align:left">70013</td>
                  <td style="text-align:left">userSig 中 userId 与请求时的 userId 不匹配，请检查登录时填写的 userId 与 userSig 中的是否一致</td>
                </tr>
                <tr>
                  <td style="text-align:left">70014</td>
                  <td style="text-align:left">userSig 中 sdkAppId 与请求时的 sdkAppId 不匹配，请检查登录时填写的 sdkAppId 与 userSig 中的是否一致</td>
                </tr>
                <tr>
                  <td style="text-align:left">70015</td>
                  <td style="text-align:left">未找到该 sdkAppId 和帐号类型对应的验证方式，请确认是否已进行帐号集成操作</td>
                </tr>
                <tr>
                  <td style="text-align:left">70016</td>
                  <td style="text-align:left">拉取到的公钥长度为 0，请确认是否已上传公钥，如果是重新上传的公钥需要十分钟后再尝试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70017</td>
                  <td style="text-align:left">内部第三方票据验证超时，请重试，如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70018</td>
                  <td style="text-align:left">内部验证第三方票据失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">70019</td>
                  <td style="text-align:left">通过 HTTPS 方式验证的票据字段为空，请正确填写 userSig</td>
                </tr>
                <tr>
                  <td style="text-align:left">70020</td>
                  <td style="text-align:left">sdkAppId 未找到，请确认是否已在腾讯云上配置</td>
                </tr>
                <tr>
                  <td style="text-align:left">70052</td>
                  <td style="text-align:left">userSig 已经失效，请重新生成，再次尝试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70101</td>
                  <td style="text-align:left">请求包信息为空</td>
                </tr>
                <tr>
                  <td style="text-align:left">70102</td>
                  <td style="text-align:left">请求包帐号类型错误</td>
                </tr>
                <tr>
                  <td style="text-align:left">70103</td>
                  <td style="text-align:left">电话号码格式错误</td>
                </tr>
                <tr>
                  <td style="text-align:left">70104</td>
                  <td style="text-align:left">邮箱格式错误</td>
                </tr>
                <tr>
                  <td style="text-align:left">70105</td>
                  <td style="text-align:left">TLS 帐号格式错误</td>
                </tr>
                <tr>
                  <td style="text-align:left">70106</td>
                  <td style="text-align:left">非法帐号格式类型</td>
                </tr>
                <tr>
                  <td style="text-align:left">70107</td>
                  <td style="text-align:left">userId 没有注册</td>
                </tr>
                <tr>
                  <td style="text-align:left">70113</td>
                  <td style="text-align:left">批量数量不合法</td>
                </tr>
                <tr>
                  <td style="text-align:left">70114</td>
                  <td style="text-align:left">安全原因被限制</td>
                </tr>
                <tr>
                  <td style="text-align:left">70115</td>
                  <td style="text-align:left">uin 不是对应 sdkAppId 的开发者 uin</td>
                </tr>
                <tr>
                  <td style="text-align:left">70140</td>
                  <td style="text-align:left">sdkAppId 和 acctype 不匹配</td>
                </tr>
                <tr>
                  <td style="text-align:left">70145</td>
                  <td style="text-align:left">帐号类型错误</td>
                </tr>
                <tr>
                  <td style="text-align:left">70169</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70201</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70202</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70203</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70204</td>
                  <td style="text-align:left">sdkAppId 没有对应的 acctype</td>
                </tr>
                <tr>
                  <td style="text-align:left">70205</td>
                  <td style="text-align:left">查找 acctype 失败，请重试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70206</td>
                  <td style="text-align:left">请求中批量数量不合法</td>
                </tr>
                <tr>
                  <td style="text-align:left">70207</td>
                  <td style="text-align:left">内部错误，请重试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70208</td>
                  <td style="text-align:left">内部错误，请重试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70209</td>
                  <td style="text-align:left">获取开发者 uin 标志失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">70210</td>
                  <td style="text-align:left">请求中 uin 为非开发者 uin</td>
                </tr>
                <tr>
                  <td style="text-align:left">70211</td>
                  <td style="text-align:left">请求中 uin 非法</td>
                </tr>
                <tr>
                  <td style="text-align:left">70212</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70213</td>
                  <td style="text-align:left">访问内部数据失败，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70214</td>
                  <td style="text-align:left">验证内部票据失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">70221</td>
                  <td style="text-align:left">登录状态无效，请使用 UserSig 重新鉴权</td>
                </tr>
                <tr>
                  <td style="text-align:left">70222</td>
                  <td style="text-align:left">内部错误，请重试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70225</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70231</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70236</td>
                  <td style="text-align:left">验证 user signature 失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">70308</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70346</td>
                  <td style="text-align:left">票据校验失败。</td>
                </tr>
                <tr>
                  <td style="text-align:left">70347</td>
                  <td style="text-align:left">票据因过期原因校验失败</td>
                </tr>
                <tr>
                  <td style="text-align:left">70348</td>
                  <td style="text-align:left">内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70362</td>
                  <td style="text-align:left">内部超时，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
                <tr>
                  <td style="text-align:left">70401</td>
                  <td style="text-align:left">内部错误，请重试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70402</td>
                  <td style="text-align:left">参数非法。请检查必填字段是否填充，或者字段的填充是否满足协议要求</td>
                </tr>
                <tr>
                  <td style="text-align:left">70403</td>
                  <td style="text-align:left">发起操作者不是 App 管理员，没有权限操作</td>
                </tr>
                <tr>
                  <td style="text-align:left">70050</td>
                  <td style="text-align:left">因失败且重试次数过多导致被限制，请检查票据是否正确，一分钟之后再试</td>
                </tr>
                <tr>
                  <td style="text-align:left">70051</td>
                  <td style="text-align:left">帐号已被拉入黑名单，请联系 TLS 帐号支持，QQ：3268519604</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".OPERATION_FAILED"><span class="type-signature">(static) </span>OPERATION_FAILED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5500</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：在满足 API 调用要求的情况下，SDK 经过多次重试仍然无法解决的异常，通常是由于浏览器、网络的问题造成。<br>
              以下接口出现异常时会抛出该错误码：<code>enterRoom</code>，<code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              处理建议：</p>
            <ul>
              <li>确认通信必需的域名和端口是否满足您的网络环境要求，参考文档<a target="_blank" href="https://cloud.tencent.com/document/product/647/34399#WebRTC-.E9.9C.80.E8.A6.81.E9.85.8D.E7.BD.AE.E5.93.AA.E4.BA.9B.E7.AB.AF.E5.8F.A3.E6.88.96.E5.9F.9F.E5.90.8D.E4.B8.BA.E7.99.BD.E5.90.8D.E5.8D.95.EF.BC.9F">应对防火墙限制及设置代理</a></li>
              <li>其他问题需要联系工程师处理 <a target="_blank" href="https://cloud.tencent.com/act/event/Online_service?from=doc_647">在线客服</a></li>
            </ul>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5501</td>
                  <td style="text-align:left">防火墙受限：SDK 经过多次重试后，依然无法建立媒体连接，会导致无法推流及无法拉流。<br />处理建议：参考教程 <a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-34-advanced-proxy.html">应对防火墙受限</a></td>
                </tr>
                <tr>
                  <td style="text-align:left">5502</td>
                  <td style="text-align:left">重进房失败：当用户经历超过 30s 的断网后，SDK 会尝试重进房恢复通话，但可能由于 userSig 过期导致重进房失败，此时会抛出该错误。<br />处理建议：遇到此错误时，您可以使用最新的 userSig 重新调用 <a href="TRTC.html#enterRoom">TRTC.enterRoom</a> 进房</td>
                </tr>
                <tr>
                  <td style="text-align:left">5503</td>
                  <td style="text-align:left">重进房失败：当用户经历超过 30s 的断网后，SDK 会尝试重进房恢复通话，但可能由于 userSig 过期导致重进房失败，此时会抛出该错误。<br />处理建议：遇到此错误时，您可以使用最新的 userSig 重新调用 <a href="TRTC.html#enterRoom">TRTC.enterRoom</a> 进房</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".OPERATION_ABORT"><span class="type-signature">(static) </span>OPERATION_ABORT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5998</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：中止 API 执行时抛出该错误码。在不满足 API 生命周期的调用或重复调用时 API 会中止执行，避免无意义的操作。<br>例如：连续调用 enterRoom，startLocalXxx等接口，在没有进房就调用退房。<br>
              以下接口出现异常时会抛出该错误码：<code>enterRoom</code>，<code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              处理建议：捕获并识别该错误码，然后在业务逻辑规避不必要的调用，或者也可以不做任何处理，因为 SDK 做了无副作用处理，您只需在 catch 时识别该错误码并忽略。</p>
          </div>
          <h4 class="name" id=".UNKNOWN_ERROR"><span class="type-signature">(static) </span>UNKNOWN_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5999</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>说明：未知错误或者未被定义的错误<br>
              处理建议：联系工程师处理 <a target="_blank" href="https://cloud.tencent.com/act/event/Online_service?from=doc_647">在线客服</a></p>
          </div>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      