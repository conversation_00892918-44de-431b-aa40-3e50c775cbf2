<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - 通话前环境与设备检测 - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: 通话前环境与设备检测</h1>
      <section>
        <header style="display:none">
          <h2>通话前环境与设备检测</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>浏览器环境检测</h2>
          <p>在开始音视频通话之前，建议您先使用 <a href="TRTC.html#.isSupported">TRTC.isSupported</a> 接口检测 SDK 是否支持当前网页。如果 SDK 不支持当前浏览器，建议用户使用最新版的 Chrome 浏览器、Edge 浏览器、Safari 浏览器、Firefox 浏览器。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>TRTC.isSupported().then(checkResult => {
  // 不支持使用 SDK，引导用户使用最新版的 Chrome 浏览器。
  if (!checkResult.result) {}
  // 不支持发布视频
  if (!checkResult.detail.isH264EncodeSupported) {}
  // 不支持拉视频
  if (!checkResult.detail.isH264DecodeSupported) {}
})
</code></pre>
          <p>⚠️ <code>TRTC.isSupported</code> 返回的检测结果为 false 时，可能是以下原因：</p>
          <ol>
            <li>网页使用了 http 协议。浏览器不允许 http 协议的网站采集摄像头和麦克风，您需要使用 https 协议部署您的网页。</li>
            <li>当前浏览器不支持 WebRTC 相关能力，您需要引导用户使用推荐的浏览器 <a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
            <li>Firefox 浏览器安装完成后需要动态加载 H264 编解码器，因此会出现短暂的检测结果为 false 的情况，请稍等再试或引导使用其他浏览器。</li>
          </ol>
          <h2>音视频设备测试</h2>
          <p>为保证用户在使用 TRTC-SDK 的过程中有更好的用户体验，我们建议您在用户加入 TRTC 房间之前，对用户设备及网络状况进行检测并给出建议和引导。</p>
          <p>为方便您快速集成设备检测及网络检测功能，我们提供一下几种方式供您参考：</p>
          <ul>
            <li><a href="./tutorial-23-advanced-support-detection.html#h2-4">rtc-detect 的 JS 库</a></li>
            <li><a href="./tutorial-23-advanced-support-detection.html#h2-5">设备检测的 React 组件</a></li>
            <li><a href="./tutorial-23-advanced-support-detection.html#h2-6">TRTC 能力检测页面</a></li>
          </ul>
          <h2>rtc-detect 库</h2>
          <p>您可以使用 <a target="_blank" href="https://www.npmjs.com/package/rtc-detect">rtc-detect</a> 用来检测当前环境对 TRTC SDK 的支持度，以及当前环境的详细信息。</p>
          <h3>安装</h3>
          <pre class="highlight lang-javascript source lang-shell"><code>npm install rtc-detect
</code></pre>
          <h3>使用方法</h3>
          <pre class="highlight lang-javascript source lang-javascript"><code>import RTCDetect from 'rtc-detect';
// 初始化监测模块
const detect = new RTCDetect();
// 获得当前环境监测结果
const result = await detect.getReportAsync();
// result 包含了当前环境系统的信息，API 支持度，编解码支持度，设备相关的信息
console.log('result is: ' + result);
</code></pre>
          <h3>API</h3>
          <h4>(async) isTRTCSupported()</h4>
          <p>判断当前环境是否支持 TRTC。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const data = await detect.isTRTCSupported();
if (data.result) {
  console.log('current browser supports TRTC.')
} else {
  console.log(`current browser does not support TRTC, reason: ${data.reason}.`)
}
</code></pre>
          <h4>getSystem()</h4>
          <p>获取当前系统环境参数。</p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>UA</td>
                <td>string</td>
                <td>浏览器的 ua</td>
              </tr>
              <tr>
                <td>OS</td>
                <td>string</td>
                <td>当前设备的系统型号</td>
              </tr>
              <tr>
                <td>browser</td>
                <td>object</td>
                <td>当前浏览器信息{ name, version }</td>
              </tr>
              <tr>
                <td>displayResolution</td>
                <td>object</td>
                <td>当前分辨率 { width, height }</td>
              </tr>
              <tr>
                <td>getHardwareConcurrency</td>
                <td>number</td>
                <td>当前设备 CPU 核心数</td>
              </tr>
            </tbody>
          </table>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const result = detect.getSystem();
</code></pre>
          <h4>getAPISupported()</h4>
          <p>获取当前环境 API 支持度。</p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>isUserMediaSupported</td>
                <td>boolean</td>
                <td>是否支持获取用户媒体数据流</td>
              </tr>
              <tr>
                <td>isWebRTCSupported</td>
                <td>boolean</td>
                <td>是否支持 WebRTC</td>
              </tr>
              <tr>
                <td>isWebSocketSupported</td>
                <td>boolean</td>
                <td>是否支持 WebSocket</td>
              </tr>
              <tr>
                <td>isWebAudioSupported</td>
                <td>boolean</td>
                <td>是否支持 WebAudio</td>
              </tr>
              <tr>
                <td>isScreenCaptureAPISupported</td>
                <td>boolean</td>
                <td>是否支持获取屏幕的流</td>
              </tr>
              <tr>
                <td>isCanvasCapturingSupported</td>
                <td>boolean</td>
                <td>是否支持从 canvas 获取数据流</td>
              </tr>
              <tr>
                <td>isVideoCapturingSupported</td>
                <td>boolean</td>
                <td>是否支持从 video 获取数据流</td>
              </tr>
              <tr>
                <td>isRTPSenderReplaceTracksSupported</td>
                <td>boolean</td>
                <td>是否支持替换 track 时不和 peerConnection 重新协商</td>
              </tr>
              <tr>
                <td>isApplyConstraintsSupported</td>
                <td>boolean</td>
                <td>是否支持变更摄像头的分辨率不通过重新调用 getUserMedia</td>
              </tr>
            </tbody>
          </table>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const result = detect.getAPISupported();
</code></pre>
          <h4>(async) getDevicesAsync()</h4>
          <p>获取当前环境可用的设备。</p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>hasCameraPermission</td>
                <td>boolean</td>
                <td>是否支持获取用户摄像头数据</td>
              </tr>
              <tr>
                <td>hasMicrophonePermission</td>
                <td>boolean</td>
                <td>是否支持获取用户麦克风数据</td>
              </tr>
              <tr>
                <td>cameras</td>
                <td>array<CameraItem>
                </td>
                <td>用户的摄像头设备列表，包含支持视频流的分辨率信息，最大宽高以及最大帧率（最大帧率有部分浏览器不支持）</td>
              </tr>
              <tr>
                <td>microphones</td>
                <td>array<DeviceItem>
                </td>
                <td>用户的麦克风设备列表</td>
              </tr>
              <tr>
                <td>speakers</td>
                <td>array<DeviceItem>
                </td>
                <td>用户的扬声器设备列表</td>
              </tr>
            </tbody>
          </table>
          <p><strong>CameraItem</strong></p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>deviceId</td>
                <td>string</td>
                <td>设备 ID， 通常是唯一的，可以用于采集识别设备</td>
              </tr>
              <tr>
                <td>groupId</td>
                <td>string</td>
                <td>组的标识符，如果两个设备属于同一个物理设备，他们就有相同的标识符</td>
              </tr>
              <tr>
                <td>kind</td>
                <td>string</td>
                <td>摄像头设备类型：'videoinput'</td>
              </tr>
              <tr>
                <td>label</td>
                <td>string</td>
                <td>描述该设备的标签</td>
              </tr>
              <tr>
                <td>resolution</td>
                <td>object</td>
                <td>摄像头支持的最大分辨率的宽高和帧率 {maxWidth: 1280, maxHeight: 720, maxFrameRate: 30}</td>
              </tr>
            </tbody>
          </table>
          <p><strong>DeviceItem</strong></p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>deviceId</td>
                <td>string</td>
                <td>设备 ID， 通常是唯一的，可以用于采集识别设备</td>
              </tr>
              <tr>
                <td>groupId</td>
                <td>string</td>
                <td>组的标识符，如果两个设备属于同一个物理设备，他们就有相同的标识符</td>
              </tr>
              <tr>
                <td>kind</td>
                <td>string</td>
                <td>设备类型，例如: 'audioinput', 'audiooutput'</td>
              </tr>
              <tr>
                <td>label</td>
                <td>string</td>
                <td>描述该设备的标签</td>
              </tr>
            </tbody>
          </table>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const result = await detect.getDevicesAsync();
</code></pre>
          <h4>(async) getCodecAsync()</h4>
          <p>获取当前环境参数对编码的支持度。</p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>isH264EncodeSupported</td>
                <td>boolean</td>
                <td>是否支持 h264 编码</td>
              </tr>
              <tr>
                <td>isH264DecodeSupported</td>
                <td>boolean</td>
                <td>是否支持 h264 解码</td>
              </tr>
              <tr>
                <td>isVp8EncodeSupported</td>
                <td>boolean</td>
                <td>是否支持 vp8 编码</td>
              </tr>
              <tr>
                <td>isVp8DecodeSupported</td>
                <td>boolean</td>
                <td>是否支持 vp8 解码</td>
              </tr>
            </tbody>
          </table>
          <p>支持编码即支持发布音视频，支持解码即支持拉取音视频播放</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const result = await detect.getCodecAsync();
</code></pre>
          <h4>(async) getReportAsync()</h4>
          <p>获取当前环境监测报告。</p>
          <table>
            <thead>
              <tr>
                <th>Item</th>
                <th>Type</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>system</td>
                <td>object</td>
                <td>和 getSystem() 的返回值一致</td>
              </tr>
              <tr>
                <td>APISupported</td>
                <td>object</td>
                <td>和 getAPISupported() 的返回值一致</td>
              </tr>
              <tr>
                <td>codecsSupported</td>
                <td>object</td>
                <td>和 getCodecAsync() 的返回值一致</td>
              </tr>
              <tr>
                <td>devices</td>
                <td>object</td>
                <td>和 getDevicesAsync() 的返回值一致</td>
              </tr>
            </tbody>
          </table>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const result = await detect.getReportAsync();
</code></pre>
          <h4>(async) isHardWareAccelerationEnabled()</h4>
          <p>检测 Chrome 浏览器是否开启硬件加速。</p>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>注意：</p>
            <ul>
              <li>该接口的实现依赖于 WebRTC 原生接口，建议在 isTRTCSupported 检测支持后，再调用该接口进行检测。检测最长耗时 30s。经实测：
                <ol>
                  <li>开启硬件加速的情况下，该接口在 Windows 耗时 2s 左右， Mac 需耗时 10s 左右。</li>
                  <li>关闭硬件加速的情况下，该接口在 Windows 和 Mac 耗时均为 30s。</li>
                </ol>
              </li>
            </ul>
          </blockquote>
          <pre class="highlight lang-javascript source lang-javascript"><code>const detect = new RTCDetect();
const data = await detect.isTRTCSupported();
if (data.result) {
  const result = await detect.isHardWareAccelerationEnabled();
  console.log(`is hardware acceleration enabled: ${result}`);
} else {
  console.log(`current browser does not support TRTC, reason: ${data.reason}.`)
}
</code></pre>
          <h2>设备检测的 React 组件</h2>
          <h3>设备检测 UI 组件特点</h3>
          <ol>
            <li>
              <p>处理了设备连接及设备检测逻辑</p>
            </li>
            <li>
              <p>处理了网络检测的逻辑</p>
            </li>
            <li>
              <p>网络检测 tab 页可选</p>
            </li>
            <li>
              <p>支持中、英文两种语言</p>
            </li>
          </ol>
          <h3>设备检测 UI 组件相关链接</h3>
          <p>组件npm包使用说明请参考：<a target="_blank" href="https://www.npmjs.com/package/rtc-device-detector-react">rtc-device-detector-react</a></p>
          <p>组件源码调试请参考：<a target="_blank" href="https://github.com/FTTC/rtc-device-detector">github/rtc-device-detector</a></p>
          <p>组件引用示例请参考：<a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/api-sample/index.html">WebRTC API Example</a></p>
          <h3>设备检测 UI 组件界面</h3>
          <p><img src="https://web.sdk.qcloud.com/trtc/webrtc/assets/rtc-device-detector.jpeg" alt="img"></p>
          <h3>设备及网络检测逻辑</h3>
          <h4>1）设备连接</h4>
          <p>​ 设备连接的目的是检测用户使用的机器是否有摄像头，麦克风，扬声器设备，是否在联网状态。如果有摄像头，麦克风设备，尝试获取音视频流并引导用户授予摄像头，麦克风的访问权限。</p>
          <ul>
            <li>
              <p>判断设备是否有摄像头，麦克风，扬声器设备</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>import TRTC from 'trtc-sdk-v5';
const cameraList = await TRTC.getCameraList();
const micList = await TRTC.getMicrophoneList();
const speakerList = await TRTC.getSpeakerList();
const hasCameraDevice = cameraList.length > 0;
const hasMicrophoneDevice = micList.length > 0;
const hasSpeakerDevice = speakerList.length > 0;
</code></pre>
            </li>
            <li>
              <p>获取摄像头，麦克风的访问权限</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>await trtc.startLocalVideo({ publish: false });
await trtc.startLocalAudio({ publish: false });
</code></pre>
            </li>
            <li>
              <p>判断设备是否联网</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>export function isOnline() {
  const url = 'https://web.sdk.qcloud.com/trtc/webrtc/assets/trtc-logo.png';
  return new Promise((resolve) => {
    try {
      const xhr = new XMLHttpRequest();
      xhr.onload = function () {
        resolve(true);
      };
      xhr.onerror = function () {
        resolve(false);
      };
      xhr.open('GET', url, true);
      xhr.send();
    } catch (err) {
      // console.log(err);
    }
  });
}
const isOnline = await isOnline();
</code></pre>
            </li>
          </ul>
          <h4>2） 摄像头检测</h4>
          <p>​检测原理：打开摄像头，并在页面中渲染摄像头画面。</p>
          <ul>
            <li>
              <p>打开摄像头</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.startLocalVideo({ view: 'camera-video', publish: false });
</code></pre>
            </li>
            <li>
              <p>切换摄像头</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.updateLocalVideo({
  option: { cameraId } 
});
</code></pre>
            </li>
            <li>
              <p><a href="./tutorial-25-advanced-device-change.html">设备插拔检测</a></p>
            </li>
            <li>
              <p>检测完成后关闭摄像头</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.stopLocalVideo();
</code></pre>
            </li>
          </ul>
          <h4>3）麦克风检测</h4>
          <p>​检测原理：打开麦克风，并获取麦克风音量。</p>
          <ul>
            <li>
              <p>打开麦克风</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.startLocalAudio({ publish: false });
</code></pre>
            </li>
            <li>
              <p>切换麦克风</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.updateLocalAudio({ option: { microphoneId }});
</code></pre>
            </li>
            <li>
              <p><a href="./tutorial-25-advanced-device-change.html">设备插拔检测</a></p>
            </li>
            <li>
              <p>检测完成后释放麦克风占用</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>trtc.stopLocalAudio();
</code></pre>
            </li>
          </ul>
          <h4>4） 扬声器检测</h4>
          <p>​ 检测原理：通过 audio 标签播放一个 mp3 媒体文件</p>
          <ul>
            <li>
              <p>创建 audio 标签，提醒用户调高播放音量，播放 mp3 确认扬声器设备是否正常。</p>
              <pre class="highlight lang-javascript source lang-html"><code>&lt;audio id=&quot;audio-player&quot; src=&quot;xxxxx&quot; controls>&lt;/audio>
</code></pre>
            </li>
            <li>
              <p>检测结束后停止播放</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>const audioPlayer = document.getElementById('audio-player');
if (!audioPlayer.paused) {
    audioPlayer.pause();
}
audioPlayer.currentTime = 0;
</code></pre>
            </li>
          </ul>
          <h4>5） 网络检测</h4>
          <p>参考：<a href="./tutorial-24-advanced-network-quality.html">通话前的网络质量检测</a></p>
          <h2>TRTC 能力检测页面</h2>
          <p>您可以在当前使用 TRTC SDK 的地方，使用 <a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/detect/index.html">TRTC 检测页面</a> ，可用于探测当前环境，还可以点击生成报告按钮，得到当前环境的报告，用于环境检测，或者问题排查。</p>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      