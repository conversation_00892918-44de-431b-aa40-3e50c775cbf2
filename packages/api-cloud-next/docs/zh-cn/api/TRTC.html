<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - TRTC - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">TRTC</h1>
      <section>
        <header style="display:none">
          <h2>
            TRTC
          </h2>
        </header>
        <article>
          <div class="container-overview">
            <h4 class="name" style="display:none" id="TRTC"><span class="type-signature"></span>new TRTC<span class="signature">()</span><span class="type-signature"></span></h4>
            <div class="description">
              <p>TRTC对象 通过 <a href="TRTC.html#.create">TRTC.create()</a> 创建，提供实时音视频的核心能力:<br></p>
              <ul>
                <li>进入一个音视频房间 <a href="TRTC.html#enterRoom">enterRoom()</a></li>
                <li>退出当前音视频房间 <a href="TRTC.html#exitRoom">exitRoom()</a></li>
                <li>预览/发布 本地视频 <a href="TRTC.html#startLocalVideo">startLocalVideo()</a></li>
                <li>采集/发布 本地音频 <a href="TRTC.html#startLocalAudio">startLocalAudio()</a></li>
                <li>取消预览/发布 本地视频 <a href="TRTC.html#stopLocalVideo">stopLocalVideo()</a></li>
                <li>取消采集/发布 本地音频 <a href="TRTC.html#stopLocalAudio">stopLocalAudio()</a></li>
                <li>观看远端视频 <a href="TRTC.html#startRemoteVideo">startRemoteVideo()</a></li>
                <li>取消观看远端视频 <a href="TRTC.html#stopRemoteVideo">stopRemoteVideo()</a></li>
                <li>静默/取消静默 远端音频 <a href="TRTC.html#muteRemoteAudio">muteRemoteAudio()</a></li>
              </ul>
              <p>TRTC 生命周期如图所示：<br /></p>
              <img src="./assets/client-life-cycle.png" width="600" />
            </div>
            <dl class="details">
            </dl>
          </div>
          <h3 class="subsection-title">Methods</h3>
          <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="TRTC.html">TRTC</a>}</span></h4>
          <div class="description">
            <p>创建一个 TRTC 对象，用于实现进房、预览、推流、拉流等功能。<br></p>
            <p><strong>注意：</strong></p>
            <ul>
              <li>您必须先创建 TRTC 对象，通过调用此对象方法和监听此对象事件才能实现业务所需要的各种功能。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 创建trtc对象
const trtc = TRTC.create();</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>trtc对象</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type"><a href="TRTC.html">TRTC</a></span>
            </dd>
          </dl>
          <h4 class="name" id="enterRoom"><span class="type-signature">(async) </span>enterRoom<span class="signature">(options)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>进入一个音视频通话房间（以下简称&quot;进房&quot;）。<br></p>
            <ul>
              <li>进房代表开始一个音视频通话会话，只有进房成功后才能和房间内的其他用户进行音视频通话。</li>
              <li>可以通过 <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> 和 <a href="TRTC.html#startLocalAudio">startLocalAudio()</a>发布本地音视频流，发布成功后，房间内其他用户会收到
                <a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a> 和 <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a> 事件通知。
              </li>
              <li>默认情况下 SDK 会自动播放远端音频，您需要在调用 <a href="TRTC.html#startRemoteVideo">startRemoteVideo()</a> 来播放远端视频画面。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const trtc = TRTC.create();
await trtc.enterRoom({ roomId: 8888, sdkAppId, userId, userSig });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>进房参数</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Default</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>sdkAppId</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>sdkAppId <br>
                            在 <a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a> 单击 <strong>应用管理</strong> &gt; <strong>创建应用</strong> 创建新应用之后，即可在 <strong>应用信息</strong> 中获取 sdkAppId 信息。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>用户ID <br>
                            建议限制长度为32字节，只允许包含大小写英文字母(a-zA-Z)、数字(0-9)及下划线和连词符。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userSig</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>userSig 签名 <br>
                            计算 userSig 的方式请参考 <a target="_blank" href="https://cloud.tencent.com/document/product/647/17275">UserSig 相关</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>roomId</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>数字类型的房间号，取值要求为 [1, 4294967294] 的整数;<br>
                            <font color="red">如果需要使用字符串类型的房间号请使用 strRoomId 参数，roomId 和 strRoomId 必须填一个。若两者都填，则优先选择 roomId。</font>
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>strRoomId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>字符串类型的房间号，限制长度为64字节，且仅支持以下范围的字符集：</p>
                          <ul>
                            <li>大小写英文字母（a-zA-Z）</li>
                            <li>数字（0-9）</li>
                            <li>空格 ! # $ % &amp; ( ) + - : ; &lt; = . &gt; ? @ [ ] ^ _ { } | ~ ,</li>
                          </ul>
                          <p>
                            <font color="red">注意：建议采用数字类型的 roomId，字符串类型的房间号 &quot;123&quot; 与 数字类型的房间号 123 不互通。</font>
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>scene</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>应用场景，目前支持以下两种场景：</p>
                          <ul>
                            <li><a href="module-TYPE.html#.SCENE_RTC">TRTC.TYPE.SCENE_RTC</a>（默认）实时通话场景，该模式适合 1对1 的音视频通话，或者参会人数在 300 人以内的在线会议。<a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-04-info-uplink-limits.html">支持最大50人同时开麦</a>。</li>
                            <li><a href="module-TYPE.html#.SCENE_LIVE">TRTC.TYPE.SCENE_LIVE</a> 互动直播场景，该模式适合十万人以内的在线直播场景，但需要您在接下来介绍的 options 参数中指定 角色(role) 这个字段</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>role</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>用户角色，仅在 <a href="module-TYPE.html#.SCENE_LIVE">TRTC.TYPE.SCENE_LIVE</a> 场景下有意义，<a href="module-TYPE.html#.SCENE_RTC">TRTC.TYPE.SCENE_RTC</a> 场景无需指定 role，目前支持两种角色：</p>
                          <ul>
                            <li><a href="module-TYPE.html#.ROLE_ANCHOR">TRTC.TYPE.ROLE_ANCHOR</a>（默认） 主播</li>
                            <li><a href="module-TYPE.html#.ROLE_AUDIENCE">TRTC.TYPE.ROLE_AUDIENCE</a> 观众
                              <br>
                              注意：观众角色没有发布本地音视频的权限，只有收看远端流的权限。如果观众想要连麦跟主播互动，
                              请先通过 <a href="TRTC.html#switchRole">switchRole()</a> 切换角色到主播后再发布本地音视频。
                            </li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>autoReceiveAudio</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>是否自动接收音频。当远端用户发布音频后，SDK 自动播放远端用户的音频。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>autoReceiveVideo</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>是否自动接收视频。当远端用户发布视频后，SDK 自动拉流并解码远端视频，您需要调用 <a href="TRTC.html#startRemoteVideo">startRemoteVideo</a> 播放远端视频。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>enableAutoPlayDialog</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>是否开启 SDK 自动播放失败弹窗，默认：true。</p>
                          <ul>
                            <li>默认开启，当出现自动播放失败时，SDK 会弹窗引导用户点击页面，来恢复音视频播放。</li>
                            <li>可设置为 false 关闭，建议接入侧参考<a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a>来处理自动播放失败相关问题。</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userDefineRecordId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>用于设置云端录制的 userDefineRecordId(选填）。</p>
                          <ul>
                            <li>【推荐取值】限制长度为64字节，只允许包含大小写英文字母（a-zA-Z）、数字（0-9）及下划线和连词符。</li>
                            <li>【参考文档】<a target="_blank" href="https://cloud.tencent.com/document/product/647/16823">云端录制</a>。</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>proxy</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type"><a href="global.html#ProxyServer">ProxyServer</a></span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>设置代理服务器。参考最佳实践：<a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>privateMapKey</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>进房钥匙，若需要权限控制请携带该参数（传空或传错会导致进房失败）。<br><a target="_blank" href="https://cloud.tencent.com/document/product/647/32240">privateMapKey 权限设置</a></p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="exitRoom"><span class="type-signature">(async) </span>exitRoom<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>退出当前音视频通话房间。</p>
            <ul>
              <li>退房后将会关闭和远端用户的连接，不再接收和播放远端用户音视频，并且停止本地音视频的发布。</li>
              <li>本地摄像头和麦克风的采集和预览不会因此而停止。您可以调用 <a href="TRTC.html#stopLocalVideo">stopLocalVideo()</a> 和 <a href="TRTC.html#stopLocalAudio">stopLocalAudio()</a> 停止本地音视频采集。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.exitRoom();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="switchRole"><span class="type-signature">(async) </span>switchRole<span class="signature">(role)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>切换用户角色，仅在 TRTC.TYPE.SCENE_LIVE 互动直播模式下生效。</p>
            <p>互动直播模式下，一个用户可能需要在“观众”和“主播”之间来回切换。
              您可以通过 <a href="TRTC.html#enterRoom">enterRoom()</a> 中的 role 字段确定角色，也可以通过 switchRole 在进房后切换角色。</p>
            <ul>
              <li>观众切换为主播，调用 trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR) 将用户角色转换为 TRTC.TYPE.ROLE_ANCHOR 主播角色，之后按需调用 <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> 和 <a href="TRTC.html#startLocalAudio">startLocalAudio()</a> 发布本地音视频。</li>
              <li>主播切换为观众，调用 trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE) 将用户角色转换为 TRTC.TYPE.ROLE_AUDIENCE 观众角色，此时如果有已发布的本地音视频，SDK 会取消发布本地音视频。</li>
            </ul>
            <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
              <p>注意：</p>
              <ul>
                <li>该接口需要在进房成功后才可以调用。</li>
                <li>关闭摄像头和麦克风后，建议及时切换成观众角色，避免主播角色占用 50路上行的资源。</li>
              </ul>
            </blockquote>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 进房成功后
// TRTC.TYPE.SCENE_LIVE 互动直播模式下，观众切换为主播
await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);
// 观众角色切换为主播，开始推流
await trtc.startLocalVideo();
// TRTC.TYPE.SCENE_LIVE 互动直播模式下，主播切换为观众
await trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>role</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>用户角色</p>
                  <ul>
                    <li>TRTC.TYPE.ROLE_ANCHOR 主播，可以发布本地音视频，单个房间里最多支持 50 个主播同时发布本地音视频。</li>
                    <li>TRTC.TYPE.ROLE_AUDIENCE 观众，不能发布本地音视频，只能观看远端流，单个房间里的观众人数没有上限。</li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="destroy"><span class="type-signature"></span>destroy<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>销毁 TRTC 实例 <br /></p>
            <p>在退房之后，若业务侧无需再使用 trtc 时，需调用该接口及时销毁 trtc 实例，释放相关资源。</p>
            <p>注意：</p>
            <ul>
              <li>销毁后的 trtc 实例不可再继续使用。</li>
              <li>已进房的情况下，需先调用 <a href="TRTC.html#exitRoom">TRTC.exitRoom</a> 接口退房成功后，才能调用该接口销毁 trtc。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 通话结束时
await trtc.exitRoom();
// 若后续无需再使用该 trtc，则销毁 trtc，并释放引用。
trtc.destroy();
trtc = null;</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></p>
          </div>
          <h4 class="name" id="startLocalAudio"><span class="type-signature">(async) </span>startLocalAudio<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>开启本地麦克风采集，并发布到当前的房间中。</p>
            <ul>
              <li>调用时机：进房前后均可调用，不可重复调用。</li>
              <li>一个 trtc 实例只能开启一路麦克风，若您需要在已经开启一路麦克风的情况下，再开启一路麦克风用于测试，可以创建多个 trtc 实例实现。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>// 采集默认麦克风并发布
await trtc.startLocalAudio();</code></pre>
          <pre class="highlight lang-javascript"><code>// 如下是测试麦克风音量的代码示例，可用于麦克风音量检测。
trtc.enableAudioVolumeEvaluation();
trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => { });
// 测试麦克风无需发布音频
await trtc.startLocalAudio({ publish: false });
// 测试完毕后，关闭麦克风
await trtc.stopLocalAudio();</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <p>配置项</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将本地音频发布到房间中，默认为true。若在进房前调用该接口，并且 publish = true，则在进房后 SDK 会自动发布。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>本地音频选项</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否静音麦克风。参考：<a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.microphoneId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>指定使用哪个麦克风</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.audioTrack</code></td>
                        <td class="type">
                          <span class="param-type">MediaStreamTrack</span>
                        </td>
                        <td class="description last">
                          <p>自定义采集的 audioTrack。<a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.captureVolume</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="description last">
                          <p>设置采集音量，默认值 100，设置小于 100 可以降低采集音量，设置大于 100 可以放大采集音量，注意有爆音风险。自 v5.2.1+ 支持。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.earMonitorVolume</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="description last">
                          <p>设置耳返音量，取值[0, 100]，本地麦克风默认静音播放。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.profile</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>音频编码配置, 默认<a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">TRTC.TYPE.AUDIO_PROFILE_STANDARD</a></p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateLocalAudio"><span class="type-signature">(async) </span>updateLocalAudio<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>更新本地麦克风配置。</p>
            <ul>
              <li>调用时机：该接口需在 <a href="TRTC.html#startLocalAudio">startLocalAudio()</a> 成功后调用，可以多次调用。</li>
              <li>本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 切换麦克风
const microphoneList = await TRTC.getMicrophoneList();
if (microphoneList[1]) {
  await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
}</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将本地音频发布到房间中，默认为 true。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否静音麦克风。参考：<a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>本地音频配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>microphoneId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>指定使用哪个麦克风，用来切换麦克风。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>audioTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>自定义采集的 audioTrack。 <a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>captureVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>设置采集音量，默认值 100，设置小于 100 可以降低采集音量，设置大于 100 可以放大采集音量，注意有爆音风险。自 v5.2.1+ 支持。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>earMonitorVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>设置耳返音量，取值[0, 100]，本地麦克风默认静音播放。</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopLocalAudio"><span class="type-signature">(async) </span>stopLocalAudio<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>停止本地麦克风的采集及发布。</p>
            <ul>
              <li>如果您只是想静音麦克风，请使用 updateLocalAudio({ mute: true })。参考：<a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a>。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopLocalAudio();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startLocalVideo"><span class="type-signature">(async) </span>startLocalVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>开启本地摄像头采集，在您指定的 HTMLElement 标签下播放摄像头画面，并将摄像头画面发布到当前所在房间中。</p>
            <ul>
              <li>调用时机：进房前后均可调用，不可重复调用。</li>
              <li>一个 trtc 实例只能开启一路摄像头。若您需要在已经开启一路摄像头的情况下，再开启一路摄像头用于测试，可以创建多个 trtc 实例实现。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <p class="code-caption">示例 1：预览及发布摄像头</p>
          <pre class="highlight lang-javascript"><code>// 预览及发布摄像头
await trtc.startLocalVideo({
  view: document.getElementById('localVideo'), // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
});</code></pre>
          <p class="code-caption">示例 2：测试摄像头——只预览不发布</p>
          <pre class="highlight lang-javascript"><code>// 只预览摄像头画面、不发布。可用于做摄像头测试。
const config = {
  view: document.getElementById('localVideo'), // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
  publish: false // 不发布摄像头
}
await trtc.startLocalVideo(config);
// 当需要发布视频时调用 updateLocalVideo
await trtc.updateLocalVideo({ publish:true });</code></pre>
          <p class="code-caption">示例 3：预览及发布指定的摄像头</p>
          <pre class="highlight lang-javascript"><code>// 使用指定的摄像头。
// 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
const view = document.getElementById('localVideo');
const cameraList = await TRTC.getCameraList();
if (cameraList[0]) {
  await trtc.startLocalVideo({
    view,
    option: {
      cameraId: cameraList[0].deviceId,
    }
  });
}
// 在移动端指定使用前置摄像头
await trtc.startLocalVideo({ view, option: { useFrontCamera: true }});
// 在移动端指定使用后置摄像头
await trtc.startLocalVideo({ view, option: { useFrontCamera: false }});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>本地视频预览的 HTMLElement 实例或者 Id， 如果不传或传入 null， 则不会播放视频。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将本地视频发布到房间中。默认为 true，若在进房前调用该接口，SDK 会在进房成功后自动发布（若 publish=true）。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>本地视频配置</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否暂停摄像头采集，参考：<a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.cameraId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>指定使用哪个摄像头，用于切换摄像头。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.useFrontCamera</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否使用前置摄像头</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.videoTrack</code></td>
                        <td class="type">
                          <span class="param-type">MediaStreamTrack</span>
                        </td>
                        <td class="description last">
                          <p>自定义采集的 videoTrack。<a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.mirror</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否开启本地预览镜像，默认为 true。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.fillMode</code></td>
                        <td class="type">
                          <span class="param-type">'contain'</span>
                          |
                          <span class="param-type">'cover'</span>
                          |
                          <span class="param-type">'fill'</span>
                        </td>
                        <td class="description last">
                          <p>视频填充模式。默认为 <code>cover</code>。参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit">CSS object-fit</a> 属性。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.profile</code></td>
                        <td class="type">
                          <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                        </td>
                        <td class="description last">
                          <p>视频大流编码参数。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.small</code></td>
                        <td class="type">
                          <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                        </td>
                        <td class="description last">
                          <p>视频小流编码参数。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option.qosPreference</code></td>
                        <td class="type">
                          <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                          |
                          <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                        </td>
                        <td class="description last">
                          <p>设置弱网时，视频编码策略。（默认）流畅度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>）或 清晰度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a>）</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateLocalVideo"><span class="type-signature">(async) </span>updateLocalVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>更新本地摄像头配置。</p>
            <ul>
              <li>该接口需在 <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> 成功后调用。</li>
              <li>该接口可以多次调用。</li>
              <li>本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <p class="code-caption">示例 1：动态切换摄像头</p>
          <pre class="highlight lang-javascript"><code>// 切换摄像头
const cameraList = await TRTC.getCameraList();
if (cameraList[1]) {
  await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
}</code></pre>
          <p class="code-caption">示例 2：停止发布视频，但保持本地预览</p>
          <pre class="highlight lang-javascript"><code>// 停止发布视频，但保持本地预览
await trtc.updateLocalVideo({ publish:false });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>预览摄像头的 HTMLElement 实例或者 Id，如果不传或传入null，则不会渲染视频，但仍然会推流并消耗带宽</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将本地视频发布到房间中。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否暂停摄像头采集，参考：<a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>本地视频配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>cameraId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>指定使用哪个摄像头，</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>useFrontCamera</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否使用前置摄像头</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>videoTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>自定义采集的 videoTrack，<a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否开启镜像</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>视频填充模式。参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit"> CSS object-fit</a> 属性</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>视频大流编码参数</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>视频小流编码参数</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>设置弱网时，视频编码策略。（默认）流畅度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>）或 清晰度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_SMOOTH</a>）</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopLocalVideo"><span class="type-signature">(async) </span>stopLocalVideo<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>停止本地摄像头的采集、预览及发布。</p>
            <ul>
              <li>如果希望仅停止发布视频但保留本地摄像头预览，可以使用<a href="TRTC.html#updateLocalVideo">updateLocalVideo({ publish:false </a>)}方法。<br></li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopLocalVideo();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startScreenShare"><span class="type-signature">(async) </span>startScreenShare<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>开启屏幕分享。</p>
            <ul>
              <li>开启屏幕分享后，房间内其他用户会收到 <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a> 事件，streamType 为 <a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a>，其他用户可以通过 <a href="TRTC.html#startRemoteVideo">startRemoteVideo</a> 播放屏幕分享。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 开始屏幕分享
await trtc.startScreenShare();</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>预览本地屏幕分享的 HTMLElement 实例或 Id， 如果不传或传入 null， 则不会渲染本地屏幕分享。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将屏幕分享发布到房间中。默认为 true，若在进房前调用该接口，SDK 会在进房成功后自动发布。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>屏幕分享配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>systemAudio</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否采集系统声音，默认为 false。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>视频填充模式。默认为 <code>contain</code>，参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit">CSS object-fit</a> 属性。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#ScreenShareProfile">ScreenShareProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>屏幕分享编码配置。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>设置弱网时，视频编码策略。流畅度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>）或 （默认）清晰度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a>）</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>videoTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>自定义采集的 videoTrack。<a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>。</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateScreenShare"><span class="type-signature">(async) </span>updateScreenShare<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>更新屏幕分享配置</p>
            <ul>
              <li>该接口需在 <a href="TRTC.html#startScreenShare">startScreenShare()</a> 成功后调用。</li>
              <li>该接口可以多次调用。</li>
              <li>本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 停止屏幕分享，但保持屏幕分享本地预览
await trtc.updateScreenShare({publish:false});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>屏幕分享预览的 HTMLElement 实例或 Id， 如果不传或传入 null， 则不会渲染屏幕分享。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>是否将屏幕分享发布到房间中。可监听该事件获取推流状态 <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>屏幕分享配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>视频填充模式。默认为 <code>contain</code>，参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit">CSS object-fit</a> 属性。</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>设置弱网时，视频编码策略。流畅度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>）或 （默认）清晰度优先（<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a>）</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopScreenShare"><span class="type-signature">(async) </span>stopScreenShare<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>停止屏幕分享。</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopScreenShare();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startRemoteVideo"><span class="type-signature">(async) </span>startRemoteVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>播放远端视频</p>
            <ul>
              <li>调用时机：在收到 <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">TRTC.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE)</a> 事件后调用。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // 您需在 DOM 中提前放置视频容器，建议以 `${userId}_${streamType}` 作为 element id。
  trtc.startRemoteVideo({ userId, streamType, view: `${userId}_${streamType}` });
})</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>用于播放远端视频的 HTMLElement 实例或者 Id， 如果不传或传入null， 则不会渲染视频， 但会仍然会拉流消耗带宽</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>远端用户Id</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>远端流类型</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: 主流（远端用户的摄像头）（远端用户的摄像头）</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: 辅流（远端用户的屏幕分享）</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>远端视频配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否拉小流</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否开启镜像</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>视频填充模式。参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit">CSS object-fit</a> 属性。</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateRemoteVideo"><span class="type-signature">(async) </span>updateRemoteVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>更新远端视频播放配置<br></p>
            <ul>
              <li>该方法需 <a href="TRTC.html#startRemoteVideo">startRemoteVideo</a> 成功后调用。</li>
              <li>该方法可多次调用。</li>
              <li>该方法采用增量更新的方式，只需要传入需要更新的配置项即可。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const config = {
 view: document.getElementById(userId),
 userId,
}
await trtc.updateRemoteVideo(config);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>用于播放远端视频的 HTMLElement 实例或者 Id， 如果不传或传入null， 则不会渲染视频， 但会仍然会拉流消耗带宽</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>远端用户Id</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>远端流类型：</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: 主流（远端用户的摄像头）</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: 辅流（远端用户的屏幕分享）</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>远端视频配置</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否拉小流，参考：<a href="tutorial-27-advanced-small-stream.html">开启大小流</a></p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>是否开启镜像</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>视频填充模式。参考 <a target="_blank" href="https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit">CSS object-fit</a> 属性。</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopRemoteVideo"><span class="type-signature">(async) </span>stopRemoteVideo<span class="signature">(config)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>用于停止远端视频播放。<br></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 停止播放所有远端用户
await trtc.stopRemoteVideo({ userId: '*' });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>远端视频配置</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>远端用户 userId，'*' 代表所有用户。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <p>远端流类型，当 userId 不为 '*' 时，该字段必填。</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: 主流（远端用户的摄像头）</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: 辅流（远端用户的屏幕分享）</li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="muteRemoteAudio"><span class="type-signature">(async) </span>muteRemoteAudio<span class="signature">(userId, mute)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>静音某个远端用户，并且不再拉取该用户的音频数据。仅对当前用户有效，房间内的其他用户依然可以听到被静音用户的声音。<br></p>
            <p>注意：</p>
            <ul>
              <li>默认情况下，在进房后，SDK 会自动播放远端音频。您可以调用该接口将远端用户静音及取消静音。</li>
              <li>进房时如果传入参数 autoReceiveAudio = false，则不会自动播放远端音频。当需要播放音频时，需要调用该方法（mute 传入 false）播放远端音频。</li>
              <li>在进入房间（enterRoom）之前或之后调用本接口均生效，静音状态在退出房间（exitRoom）之后会被重置为 false。</li>
              <li>如果您希望继续拉取该用户的音频数据，仅仅是不播放，可以调用 setRemoteAudioVolume(userId, 0)</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 静音所有远端用户
await trtc.muteRemoteAudio('*', true);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>远端用户 userId，'*' 代表所有用户。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>mute</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>是否静音</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="setRemoteAudioVolume"><span class="type-signature"></span>setRemoteAudioVolume<span class="signature">(userId, volume)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>用于控制远端音频的播放音量。<br></p>
            <ul>
              <li>不支持 iOS Safari</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.setRemoteAudioVolume('123', 90);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>远端用户 userId</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>volume</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>音量大小，取值范围为0 - 100，默认值为 100。<br>
                    自 <code>v5.1.3+</code> 版本支持设置 volume 大于100。需注意，设置超过 100 可能有爆音风险。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="startPlugin"><span class="type-signature">(async) </span>startPlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>开启插件</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>插件名称</th>
                  <th>参考教程</th>
                  <th>参数类型</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>背景音乐插件</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#AudioMixerOptions">AudioMixerOptions</a></td>
                </tr>
                <tr>
                  <td>'AIDenoiser'</td>
                  <td>降噪插件</td>
                  <td><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#AIDenoiserOptions">AIDenoiserOptions</a></td>
                </tr>
                <tr>
                  <td>'CDNStreaming'</td>
                  <td>CDN混流插件</td>
                  <td><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#CDNStreamingOptions">CDNStreamingOptions</a></td>
                </tr>
                <tr>
                  <td>'VirtualBackground'</td>
                  <td>虚拟背景插件</td>
                  <td><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#VirtualBackgroundOptions">VirtualBackgroundOptions</a></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#AudioMixerOptions">AudioMixerOptions</a></span>
                  |
                  <span class="param-type"><a href="global.html#AIDenoiserOptions">AIDenoiserOptions</a></span>
                  |
                  <span class="param-type"><a href="global.html#CDNStreamingOptions">CDNStreamingOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="updatePlugin"><span class="type-signature">(async) </span>updatePlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>更新插件</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>插件名称</th>
                  <th>参考教程</th>
                  <th>参数类型</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>背景音乐插件</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#UpdateAudioMixerOptions">UpdateAudioMixerOptions</a></td>
                </tr>
                <tr>
                  <td>'CDNStreaming'</td>
                  <td>CDN混流插件</td>
                  <td><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#CDNStreamingOptions">CDNStreamingOptions</a></td>
                </tr>
                <tr>
                  <td>'VirtualBackground'</td>
                  <td>虚拟背景插件</td>
                  <td><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#UpdateVirtualBackgroundOptions">UpdateVirtualBackgroundOptions</a></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#UpdateAudioMixerOptions">UpdateAudioMixerOptions</a></span>
                  |
                  <span class="param-type"><a href="global.html#CDNStreamingOptions">CDNStreamingOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="stopPlugin"><span class="type-signature">(async) </span>stopPlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>停止插件</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>插件名称</th>
                  <th>参考教程</th>
                  <th>参数类型</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>背景音乐插件</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#StopAudioMixerOptions">StopAudioMixerOptions</a></td>
                </tr>
                <tr>
                  <td>'AIDenoiser'</td>
                  <td>降噪插件</td>
                  <td><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></td>
                  <td></td>
                </tr>
                <tr>
                  <td>'CDNStreaming'</td>
                  <td>CDN混流插件</td>
                  <td><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#CDNStreamingOptions">CDNStreamingOptions</a></td>
                </tr>
                <tr>
                  <td>'VirtualBackground'</td>
                  <td>虚拟背景插件</td>
                  <td><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#StopAudioMixerOptions">StopAudioMixerOptions</a></span>
                  |
                  <span class="param-type"><a href="global.html#CDNStreamingOptions">CDNStreamingOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="enableAudioVolumeEvaluation"><span class="type-signature"></span>enableAudioVolumeEvaluation<span class="signature">(interval<span class="signature-attributes">opt</span>, enableInBackground<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>开启或关闭音量大小回调<br></p>
            <ul>
              <li>开启此功能后，无论房间内是否有人说话，SDK 会定时抛出 <a href="module-EVENT.html#.AUDIO_VOLUME">TRTC.on(TRTC.EVENT.AUDIO_VOLUME)</a> 事件，反馈每一个用户的的音量大小评估值。<br></li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   event.result.forEach(({ userId, volume }) => {
       const isMe = userId === ''; // 当 userId 为空串时，代表本地麦克风音量。
       if (isMe) {
           console.log(`my volume: ${volume}`);
       } else {
           console.log(`user: ${userId} volume: ${volume}`);
       }
   })
});
// 开启音量回调，并设置每 1000ms 触发一次事件
trtc.enableAudioVolumeEvaluation(1000);
// 如需关闭音量回调，传入 interval 值小于等于0即可
trtc.enableAudioVolumeEvaluation(-1);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>interval</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="default">
                  <code>2000</code>
                </td>
                <td class="description last">
                  <p>用于设置音量回调事件定时触发的时间间隔。默认为 2000(ms)，最小值为100(ms)。若设置小于等于0时，则关闭音量大小回调。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>enableInBackground</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="default">
                  <code>false</code>
                </td>
                <td class="description last">
                  <p>出于性能的考虑，当页面切换到后台时，SDK 不会抛出音量回调事件。如需在页面切后台时接收音量回调事件，可设置该参数为 true。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="on"><span class="type-signature"></span>on<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>监听 TRTC 事件<br><br>
              详细事件列表请参见：<a href="module-EVENT.html">TRTC.EVENT</a></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
  // REMOTE_VIDEO_AVAILABLE event handler
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>事件名</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>事件回调函数</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>上下文</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="off"><span class="type-signature"></span>off<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>取消事件监听<br></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, function peerJoinHandler(event) {
  // REMOTE_USER_ENTER event handler
  console.log('remote user enter');
  trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, peerJoinHandler);
});
// 解除所有事件绑定
trtc.off('*');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>事件名，传入通配符 '*' 会解除所有事件监听。</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>事件回调函数</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>上下文</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="getVideoTrack"><span class="type-signature"></span>getVideoTrack<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; {MediaStreamTrack|null}</span></h4>
          <div class="description">
            <p>获取视频轨道</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 获取本地摄像头 videoTrack
const videoTrack = trtc.getVideoTrack();
// 获取本地屏幕分享 videoTrack
const screenVideoTrack = trtc.getVideoTrack({ streamType: TRTC.TYPE.STREAM_TYPE_SUB });
// 获取远端用户的主流 videoTrack
const remoteMainVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
// 获取远端用户的辅流 videoTrack
const remoteSubVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_SUB });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>不传则获取本地摄像头 videoTrack</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>不传或传空串，代表获取本地的 videoTrack。传远端用户的 userId，代表获取远端用户的 videoTrack。</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <p>远端流类型：</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: 主流（远端用户的摄像头）（默认值）</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: 辅流（远端用户的屏幕分享）</li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>视频轨道</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
              |
              <span class="param-type">null</span>
            </dd>
          </dl>
          <h4 class="name" id="getAudioTrack"><span class="type-signature"></span>getAudioTrack<span class="signature">(userId<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; (nullable) {MediaStreamTrack}</span></h4>
          <div class="description">
            <p>获取音频轨道</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>不传则获取本地的 audioTrack</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>音频轨道</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
            </dd>
          </dl>
          <h4 class="name" id="sendSEIMessage"><span class="type-signature"></span>sendSEIMessage<span class="signature">(buffer, options<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>发送 SEI Message <br></p>
            <p>视频帧的头部有一个叫做 SEI 的头部数据块，该接口的原理就是利用这个被称为 SEI 的头部数据块，将您要发送的自定义信令嵌入其中，使其同视频帧一并发送出去。
              SEI 消息可以伴随着视频帧一直传输到直播 CDN 上。</p>
            <p>适用场景：视频画面的精准布局、歌词同步、直播答题等。
              调用时机：在 <a href="TRTC.html#startLocalVideo">trtc.startLocalVideo</a> 后调用。</p>
            <p>注意：</p>
            <ol>
              <li>单次最大发送1KB(Byte)，每秒最大调用次数30次，每秒最多发送8KB。</li>
              <li>目前仅支持 Chrome 86+, Edge 86+, Opera 72+ 浏览器。</li>
              <li>由于 SEI 跟随视频帧一起发送，视频帧有丢失的可能，因此 SEI 也可能丢失。可在使用频次限制范围内，增加发送次数，业务侧需要在接收端做消息去重。</li>
              <li>没有推视频流时，无法发送 SEI；没有订阅视频流时，无法接收 SEI。</li>
              <li>仅支持 H264 编码器发送 SEI。</li>
              <li>小流暂不支持 SEI 收发。</li>
            </ol>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>v5.3.0</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li>TRTC.EVENT.SEI_MESSAGE</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 1. 开启 SEI
const trtc = TRTC.create({
   enableSEI: true // 开启 SEI 收发功能
})
// 2. 推流端发送 SEI
try {
 await trtc.enterRoom({
  userId: 'user_1',
  roomId: 12345,
})
 await trtc.startLocalVideo();
 const unit8Array = new Uint8Array([1, 2, 3]);
 trtc.sendSEIMessage(unit8Array.buffer);
 // 消息已通过限制，等待后续视频帧发送。
} catch(error) {
 // 发送失败，可能原因：当前浏览器不支持、参数校验未通过、未推流、没推视频流、调用次数超出限制、使用非 H264 编码器等。
 console.warn(error);
}
// 3. 拉流端接收 SEI
trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
 console.warn(`收到 ${event.userId} 的 sei ${event.data}`);
})</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>buffer</code></td>
                <td class="type">
                  <span class="param-type">ArrayBuffer</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>待发送的 SEI 数据</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">Object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>seiPayloadType</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>设置 SEI payload type。SDK 默认使用自定义 payloadType 243，业务侧可使用该参数将 payloadType 设置为标准的 5。当业务侧使用 5 payloadType 时，需按照规范确保 <code>buffer</code> 的前16字节是业务侧自定义的 uuid。</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id=".setLogLevel"><span class="type-signature">(static) </span>setLogLevel<span class="signature">(level<span class="signature-attributes">opt</span>, enableUploadLog<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>设置日志输出等级
              <br>
              建议在开发测试阶段设置为 DEBUG 等级，该日志等级包含详细的提示信息。
              默认输出 INFO 日志等级，该日志等级包含 SDK 主要功能的日志信息。
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 输出 DEBUG 以上日志等级
TRTC.setLogLevel(1);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>level</code></td>
                <td class="type">
                  <span class="param-type">0-5</span>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>日志输出等级 0: TRACE 1: DEBUG 2: INFO 3: WARN 4: ERROR 5: NONE</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>enableUploadLog</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="default">
                  <code>true</code>
                </td>
                <td class="description last">
                  <p>是否开启日志上传，默认开启。不建议关闭，关闭后将影响问题排障。</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id=".isSupported"><span class="type-signature">(static) </span>isSupported<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;object>}</span></h4>
          <div class="description">
            <p>检测 TRTC Web SDK 是否支持当前浏览器</p>
            <ul>
              <li>参考：<a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a>。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>TRTC.isSupported().then((checkResult) => {
  if(!checkResult.result) {
     console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
     // SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
  }
});</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise 返回检测结果</p>
            <table>
              <thead>
                <tr>
                  <th>Property</th>
                  <th>Type</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>checkResult.result</td>
                  <td>boolean</td>
                  <td>检测结果</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isBrowserSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否是 SDK 支持的浏览器</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isWebRTCSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否支持 WebRTC</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isWebCodecsSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否支持 WebCodecs</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isMediaDevicesSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否支持获取媒体设备及媒体流</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isScreenShareSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否支持屏幕分享</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isSmallStreamSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器是否支持小流</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isH264EncodeSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器上行是否支持 H264 编码</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isH264DecodeSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器下行是否支持 H264 编码</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isVp8EncodeSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器上行是否支持 VP8 编码</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isVp8DecodeSupported</td>
                  <td>boolean</td>
                  <td>当前浏览器下行是否支持 VP8 编码</td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;object></span>
            </dd>
          </dl>
          <h4 class="name" id=".getCameraList"><span class="type-signature">(static) </span>getCameraList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>返回摄像头设备列表
              <br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>该接口不支持在 http 协议下使用，请使用 https 协议部署您的网站。<a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security">Privacy and security</a></li>
              <li>出于安全的考虑，在用户未授权摄像头或麦克风访问权限前，label 及 deviceId 字段可能都是空的。因此建议在用户授权访问后，再调用该接口获取设备详情。</li>
              <li>可以调用浏览器原生接口 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities">getCapabilities</a> 获取摄像头支持的最大分辨率、帧率、移动端设备区分前后置摄像头等。该接口支持 Chrome 67+, Edge 79+, Safari 17+, Opera 54+ 浏览器。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const cameraList = await TRTC.getCameraList();
if (cameraList[0] &amp;&amp; cameraList[0].getCapabilities) {
  const { width, height, frameRate, facingMode } = cameraList[0].getCapabilities();
  // 摄像头支持的最大分辨率、帧率。
  console.log(width.max, height.max, frameRate.max);
  // 在移动端区分该摄像头是前置还是后置。
  if (facingMode) {
    if (facingMode[0] === 'user') {
      // 前置摄像头
    } else if (facingMode[0] === 'environment') {
      // 后置摄像头
    }
  }
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise 返回 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a> 数组</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".getMicrophoneList"><span class="type-signature">(static) </span>getMicrophoneList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>返回麦克风设备列表
              <br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>该接口不支持在 http 协议下使用，请使用 https 协议部署您的网站。<a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security">Privacy and security</a></li>
              <li>出于安全的考虑，在用户未授权摄像头或麦克风访问权限前，label 及 deviceId 字段可能都是空的。因此建议在用户授权访问后，再调用该接口获取设备详情。</li>
              <li>可以调用浏览器原生接口 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities">getCapabilities</a> 获取麦克风能力信息，例如支持的最大声道数等。该接口支持 Chrome 67+, Edge 79+, Safari 17+, Opera 54+ 浏览器。</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const microphoneList = await TRTC.getMicrophoneList();
if (microphoneList[0] &amp;&amp; microphoneList[0].getCapabilities) {
  const { channelCount } = microphoneList[0].getCapabilities();
  console.log(channelCount.max);
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise 返回 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a> 数组</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".getSpeakerList"><span class="type-signature">(static) </span>getSpeakerList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>返回扬声器设备列表
              <br>
              出于安全的考虑，在用户未授权摄像头或麦克风访问权限前，label 及 deviceId 字段可能都是空的。因此建议在用户授权访问后
              再调用该接口获取设备详情。
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise 返回 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a> 数组</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".setCurrentSpeaker"><span class="type-signature">(async, static) </span>setCurrentSpeaker<span class="signature">(speakerId)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>设置当前音频播放的扬声器</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>speakerId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>扬声器 ID</p>
                </td>
              </tr>
            </tbody>
          </table>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      