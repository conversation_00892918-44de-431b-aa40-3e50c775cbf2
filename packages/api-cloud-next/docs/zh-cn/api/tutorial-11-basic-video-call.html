<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - 开始集成音视频通话 - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: 开始集成音视频通话</h1>
      <section>
        <header style="display:none">
          <h2>开始集成音视频通话</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>前提条件</h2>
          <ol>
            <li><a target="_blank" href="https://cloud.tencent.com/document/product/378/17985">注册腾讯云</a>账号，<a target="_blank" href="https://console.cloud.tencent.com/trtc/app">创建实时音视频应用</a>。</li>
            <li><a target="_blank" href="https://console.cloud.tencent.com/trtc/usersigtool">获取临时 userSig</a> ，或者部署 <a target="_blank" href="https://cloud.tencent.com/document/product/647/17275?from_cn_redirect=1#formal">userSig 签发服务</a>。</li>
            <li>为了体验完整的 TRTC 能力，建议开发时使用 <code>http://localhost</code> ，生产环境用 <code>https://[域名]</code> 访问页面，参考文档<a href="./tutorial-05-info-browser.html#h2-3">页面访问协议说明</a>。</li>
            <li>为了避免防火墙安全策略限制正常的 TRTC 数据传输，需要参考文档<a href="./tutorial-05-info-browser.html#h2-4">应对防火墙策略</a>进行设置。</li>
            <li>为了保证通话体验，建议在正式开始音视频通话前，进行设备检测和浏览器兼容性检测，可以参考文档<a href="./tutorial-05-info-browser.html#h2-2">浏览器兼容信息</a>，<a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a>。</li>
          </ol>
          <h2>集成 SDK</h2>
          <p>SDK 提供了 UMD、ES Module 类型的模块，以及 TypeScript Type Definition 文件，满足在不同类型项目中集成。</p>
          <h3>NPM 集成</h3>
          <ol>
            <li>您可以在项目中使用 <a target="_blank" href="https://docs.npmjs.com/downloading-and-installing-node-js-and-npm">npm</a> 安装 <a target="_blank" href="https://www.npmjs.com/package/trtc-sdk-v5">trtc-sdk-v5</a>。</li>
          </ol>
          <pre class="highlight lang-javascript source"><code>npm install trtc-sdk-v5 --save
</code></pre>
          <ol start="2">
            <li>在项目脚本里导入模块。</li>
          </ol>
          <pre class="highlight lang-javascript source lang-javascript"><code>import TRTC from 'trtc-sdk-v5';
</code></pre>
          <h3>Script 集成</h3>
          <ol>
            <li>在您的 Web 页面中添加如下代码即可：</li>
          </ol>
          <pre class="highlight lang-javascript source lang-html"><code>&lt;script src=&quot;trtc.js&quot;>&lt;/script>
</code></pre>
          <p><strong>资源下载</strong></p>
          <ul>
            <li><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/download/webrtc_v5_latest.zip">单击下载 SDK 及示例代码</a></li>
            <li><a target="_blank" href="https://github.com/LiteAVSDK/TRTC_Web">GitHub 仓库地址</a></li>
          </ul>
          <h2>SDK 使用逻辑概览</h2>
          <p><strong>基本概念</strong></p>
          <p>您在使用 TRTC Web SDK 时，会接触到以下概念：</p>
          <ul>
            <li><a href="TRTC.html">TRTC</a> 类，其实例代表一个本地客户端。TRTC 的对象方法提供了加入通话房间、预览本地摄像头、发布本地摄像头和麦克风、播放远端音视频等功能。</li>
          </ul>
          <p><strong>实现音视频通话基本逻辑</strong></p>
          <ol>
            <li>调用 <a href="TRTC.html#.create">TRTC.create()</a> 方法创建 <a href="TRTC.html">trtc</a> 对象。</li>
            <li>调用 <a href="TRTC.html#enterRoom">trtc.enterRoom()</a> 进入房间。</li>
            <li>在进入房间后，可以开启摄像头和麦克风并发布到房间。
              <ul>
                <li>调用 <a href="TRTC.html#startLocalVideo">TRTC.startLocalVideo()</a> 开启摄像头并发布到房间。</li>
                <li>调用 <a href="TRTC.html#startLocalAudio">TRTC.startLocalAudio()</a> 开启麦克风并发布到房间。</li>
              </ul>
            </li>
            <li>当一个远端用户发布了音视频后，SDK 默认情况下会自动播放远端音频。您需要通过如下方式来播放远端视频：
              <ul>
                <li>在进房前监听 <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">TRTC.EVENT.REMOTE_VIDEO_AVAILABLE</a> 事件，就能收到所有远端用户的发布视频事件。</li>
                <li>在事件回调函数中，调用 <a href="TRTC.html#startRemoteVideo">trtc.startRemoteVideo()</a> 方法播放远端视频。</li>
              </ul>
            </li>
          </ol>
          <p>下图展示了实现音视频通话全过程的基础 API 调用流程：
            <img src="assets/trtc-sdk-call-sequence-cn.png" alt="" title="右键打开图片查看大图">
          </p>
          <h2>创建 TRTC 对象</h2>
          <p>通过 <a href="TRTC.html#.create">TRTC.create()</a> 方法创建 <a href="TRTC.html">TRTC</a> 对象<br></p>
          <pre class="highlight lang-javascript source lang-javascript"><code>const trtc = TRTC.create();
</code></pre>
          <h2>进入房间</h2>
          <p>调用 <a href="TRTC.html#enterRoom">trtc.enterRoom()</a> 进入房间。通常在<code>开始通话</code>按钮的点击回调里进行调用。
            关键参数：</p>
          <ul>
            <li><code>scene</code>: 实时音视频通话模式，设置为 'rtc'。</li>
            <li><code>sdkAppId</code>: 您在腾讯云创建的音视频应用的 sdkAppId。</li>
            <li><code>userId</code>: 用户 ID，由您指定。</li>
            <li><code>userSig</code>: 用户签名，参考<a target="_blank" href="https://console.cloud.tencent.com/trtc/usersigtool">获取临时 userSig</a>，或者部署 <a target="_blank" href="https://cloud.tencent.com/document/product/647/17275?from_cn_redirect=1#formal">userSig 签发服务</a>。</li>
            <li><code>roomId</code>：房间 ID，由您指定，通常是生成唯一的房间ID。
              更详细的参数说明参考接口文档 <a href="TRTC.html#enterRoom">trtc.enterRoom()</a>。</li>
          </ul>
          <pre class="highlight lang-javascript source lang-javascript"><code>try {
  await trtc.enterRoom({ roomId: 8888, scene:'rtc', sdkAppId, userId, userSig });
  console.log('进房成功');
} catch (error) {
  console.error('进房失败 ' + error);
}
</code></pre>
          <h2>开启摄像头、麦克风</h2>
          <h3>开启摄像头</h3>
          <p>使用 <a href="TRTC.html#startLocalVideo">trtc.startLocalVideo()</a> 方法开启摄像头，并发布到房间。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 为了预览摄像头画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 local-video。
const view = 'local-video';
await trtc.startLocalVideo({ view });
</code></pre>
          <h3>开启麦克风</h3>
          <p>使用 <a href="TRTC.html#startLocalAudio">trtc.startLocalAudio()</a> 方法开启麦克风，并发布到房间。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>await trtc.startLocalAudio();
</code></pre>
          <h2>播放远端音视频</h2>
          <h3>播放远端音频</h3>
          <p>默认情况下，SDK 会自动播放远端音频，您无需调用 API 来播放远端音频。</p>
          <ul>
            <li>需要注意的是：如果用户在进房前没有与页面产生过交互，自动播放音频可能会因为【浏览器的自动播放策略限制】而失败，您需参考<a href="./tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a>进行处理。</li>
            <li>若您不希望 SDK 自动播放音频，您可以在 <a href="TRTC.html#enterRoom">trtc.enterRoom()</a> 时设置 receiveMode = TRTC.TYPE.RECEIVE_MODE_MANUAL 关闭自动播放音频。</li>
            <li>监听 <a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">TRTC.EVENT.REMOTE_AUDIO_AVAILABLE</a> 事件，记录有远端音频的 userId，在需要播放音频时，调用 <a href="TRTC.html#muteRemoteAudio">trtc.muteRemoteAudio(userId, false)</a> 方法。</li>
          </ul>
          <h3>播放远端视频</h3>
          <p>在进房前监听 <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">TRTC.EVENT.REMOTE_VIDEO_AVAILABLE</a> 事件，在收到该事件时，通过 <a href="TRTC.html#startRemoteVideo">trtc.startRemoteVideo()</a> 播放远端视频流。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // 为了播放视频画面，您需在 DOM 中放置一个 HTMLElement，可以是一个 div 标签，假设其 id 为 `${userId}_${streamType}`
  const view = `${userId}_${streamType}`;
  trtc.startRemoteVideo({ userId, streamType, view });
});
</code></pre>
          <h2>退出房间</h2>
          <p>调用 <a href="TRTC.html#exitRoom">trtc.exitRoom()</a> 方法退出房间，结束音视频通话。</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>await trtc.exitRoom(); 
// 退房成功后，若后续无需使用 trtc 实例，则可以调用 trtc.destroy 方法销毁实例，及时释放相关资源。销毁后的 trtc 实例无法继续使用，需要重新创建新的实例。
trtc.destroy();
</code></pre>
          <p><strong>处理被踢</strong></p>
          <p>除了用户主动退出房间之外，用户也有可能因为如下原因被踢出房间，此时 SDK 会抛出 <a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a> 事件，这时不需要调用 <code>trtc.exitRoom()</code> 退房，SDK 自动进入退房状态。</p>
          <ol>
            <li><code>kick</code>：两个相同 userId 的用户进入相同房间，前一个进房的用户会被踢出。同名用户同时进入同一房间是不允许的行为，可能会导致双方音视频通话异常，应避免出现这种情况。</li>
            <li><code>banned</code>：通过服务端的 <a target="_blank" href="https://cloud.tencent.com/document/api/647/40496">RemoveUser</a> | <a target="_blank" href="https://cloud.tencent.com/document/api/647/50426">RemoveUserByStrRoomId</a> 接口将某个用户踢出某个 TRTC 房间，该用户会收到被踢事件，reason 为 <code>banned</code> 。</li>
            <li><code>room-disband</code>：通过服务端的 <a target="_blank" href="https://cloud.tencent.com/document/api/647/50089">DismissRoom</a> | <a target="_blank" href="https://cloud.tencent.com/document/api/647/37088">DismissRoomByStrRoomId</a>接口将某个 TRTC 房间解散，解散房间之后，该房间的所有用户都会收到被踢事件，reason 为 <code>room-disband</code>。</li>
          </ol>
          <pre class="highlight lang-javascript source lang-javascript"><code>trtc.on(TRTC.EVENT.KICKED_OUT, error => {
  console.error(`kicked out, reason:${error.reason}, message:${error.message}`);
  // error.reason 有以下几种情况
  // 'kick' 由于相同 userId 进相同房间，导致先进入的用户被踢。
  // 'banned' 被管理员移出房间
  // 'room-disband' 管理员解散了房间
});
</code></pre>
          <h2>联系我们</h2>
          <p>如在接入实现过程中遇到问题，欢迎到 <a target="_blank" href="https://github.com/LiteAVSDK/TRTC_Web/issues">GitHub issue</a> 创建 issue，我们会尽快处理。</p>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      