<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - 云端混流与转推CDN - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: 云端混流与转推CDN</h1>
      <section>
        <header style="display:none">
          <h2>云端混流与转推CDN</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>功能描述</h2>
          <p>本文主要描述如何使用 CDNStreaming 插件，实现云端混流及转推 CDN 的功能。</p>
          <h2>前提条件</h2>
          <ol>
            <li>
              <p><a target="_blank" href="https://www.npmjs.com/package/trtc-sdk-v5">TRTC SDK</a> 版本需要 &gt;= 5.1。</p>
            </li>
            <li>
              <p>开通腾讯 <a target="_blank" href="https://console.cloud.tencent.com/live">云直播</a> 服务。应国家相关部门的要求，直播播放必须配置播放域名，具体操作请参考 <a target="_blank" href="https://console.cloud.tencent.com/live/livestat">域名管理</a>。</p>
            </li>
            <li>
              <p>在 <a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a> 左侧导航栏选择【应用管理】，单击目标应用所在行的【配置】，打开【启用旁路推流】的开关。</p>
              <ul>
                <li>“全局自动旁路”模式下，TRTC 所有上行音视频流都会被自动转推到腾讯云 CDN。</li>
                <li>“指定流旁路”模式下，您可以通过该插件或者 <a target="_blank" href="https://cloud.tencent.com/document/product/647/84721">服务端 REST API</a> 将指定音视频流推送到 CDN 且指定播放地址。</li>
              </ul>
              <div align="center">
                <img src="assets/relayed-push.png" />
              </div>
            </li>
          </ol>
          <h2>使用步骤</h2>
          <h3>一、注册插件</h3>
          <p>在使用之前，首先需要注册插件。以下是注册插件的示例代码：</p>
          <pre class="highlight lang-javascript source lang-js"><code>import TRTC from 'trtc-sdk-v5';
import { CDNStreaming, PublishMode } from 'trtc-sdk-v5/plugins/cdn-streaming';
const trtc = TRTC.create({ plugins: [CDNStreaming] });
</code></pre>
          <h3>二、使用插件</h3>
          <p>本插件提供了三种推流转码模式</p>
          <p>模式 A：转推摄像头麦克风流</p>
          <pre class="highlight lang-javascript source lang-js"><code>const options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN,
    // ... 其他参数
  }
}
</code></pre>
          <p>模式 B：转推屏幕分享流</p>
          <pre class="highlight lang-javascript source lang-js"><code>const options = {
  target: {
    publishMode: PublishMode.PublishSubStreamToCDN,
    // ... 其他参数
  }
}
</code></pre>
          <p>模式 C：转推混流</p>
          <pre class="highlight lang-javascript source lang-js"><code>const options = {
  target: {
    publishMode: PublishMode.PublishMixStreamToCDN,
    // ... 其他参数
  },
  encoding: {
    // 云端混流后输出的画面编码参数
  },
  mix: {
    // 云端混流的配置参数
  }
}
</code></pre>
          <p>对于每一种模式，均满足以下生命周期</p>
          <ul>
            <li>调用 1 次<code>startPlugin</code> 方法启动插件 <code>trtc.startPlugin('CDNStreaming', options);</code></li>
            <li>调用 0 或 N 次 <code>updatePlugin</code> 方法更新参数 <code>trtc.updatePlugin('CDNStreaming', options);</code></li>
            <li>调用 1 次 <code>stopPlugin</code> 方法更新参数 <code>trtc.stopPlugin('CDNStreaming', options);</code></li>
          </ul>
          <p>详细的 <code>options</code> 参数可见 <a href="./global.html#CDNStreamingOptions">参数定义</a>。</p>
          <h3>三、使用示例</h3>
          <p>接下来将以三个使用场景为例，介绍插件的使用。</p>
          <ul>
            <li>场景 1：实现将摄像头、屏幕分享推流到腾讯云 CDN</li>
            <li>场景 2：将混流转推到腾讯云 CDN</li>
            <li>场景 3：推流到指定 CDN</li>
          </ul>
          <h4>场景 1：实现将摄像头、屏幕分享推流到腾讯云 CDN</h4>
          <p>步骤(1)：选择【旁路推流方式】，见本文<a href="#h2-3">前提条件</a>第 3 条</p>
          <ul>
            <li>
              <p>若您选择“全局自动旁路”模式</p>
              <p>TRTC 房间里的摄像头流、屏幕分享流将<strong>自动</strong>被推流到腾讯云 CDN，每一路画面都自动配备一路对应的播放地址，该地址的格式如下：</p>
              <p><code>http://[播放域名]/live/[streamId].flv</code>，其中</p>
              <ul>
                <li><code>[播放域名]</code> 为云直播中配置的播放域名，查看已有的域名请参考 <a target="_blank" href="https://console.cloud.tencent.com/live/livestat">域名管理</a>。</li>
                <li>摄像头流的 <code>[streamId]</code> 默认值为 <code>${sdkAppId}_${roomId}_${userId}_main</code>。</li>
                <li>屏幕分享流的 <code>[streamId]</code> 默认值为<code>${sdkAppId}_${roomId}_${userId}_aux</code>。</li>
                <li>当 roomId 或者 userId 包含特殊字符时（除了大小写英文字母(a-zA-Z)、数字(0-9)、连字符及下划线之外的均为特殊字符），默认 streamId 摄像头流为 <code>${sdkAppId}_${md5(${roomId}_${userId}_main)}</code>，屏幕分享流为 <code>${sdkAppId}_${md5(${roomId}_${userId}_aux)}</code></li>
              </ul>
              <p>此时已完成自动推流，通过默认地址播放可以播放。若想修改播放地址，可以继续阅读步骤(2)。</p>
            </li>
            <li>
              <p>若您选择“手动旁路”模式</p>
              <p>TRTC 房间里的流<strong>不会</strong>被自动被推流到腾讯云 CDN，请阅读步骤(2)手动开启推流。</p>
            </li>
          </ul>
          <p>步骤(2)：设定插件参数</p>
          <p>选择想要转推摄像头流还是屏幕分享流</p>
          <ul>
            <li>若转推摄像头流，则参数的 <code>options.target.publishMode</code> 填写 <code>PublishMode.PublishMainStreamToCDN</code></li>
            <li>若转推屏幕分享流，则参数的 <code>options.target.publishMode</code> 填写 <code>PublishMode.PublishSubStreamToCDN</code></li>
          </ul>
          <p>选择是否要自定义播放地址</p>
          <ul>
            <li>若要指定推流后播放地址，则需要设置参数的 <code>options.target.streamId</code></li>
          </ul>
          <p>代码示例如下：</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 开启插件
const options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN
  }
}
try {
  await trtc.startPlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming start failed', error);
}
</code></pre>
          <p>摄像头流开始被转推到 CDN，使用的是播放地址：<code>http://[播放域名]/live/${sdkAppId}_${roomId}_${userId}_main.flv</code></p>
          <p>若要修改播放地址，可以修改 <code>streamId</code> 字段</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 更新转推参数
const options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN,
    streamId: 'stream001'
  }
}
try {
  await trtc.updatePlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming update failed', error);
}
</code></pre>
          <p>播放地址变更为：<code>http://[播放域名]/live/stream001.flv</code></p>
          <p>不需要进行转推时停止插件</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 停止插件，只需要填写 publishMode 字段
const options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN,
  }
}
try {
  await trtc.stopPlugin('CDNStreaming', options);
} catch (error) {
  console.log('CDNStreaming stop failed', error);
}
</code></pre>
          <p>可通过 <a target="_blank" href="https://console.cloud.tencent.com/live/streammanage">流管理界面</a> 查看目前的流状态。</p>
          <h4>场景二：将混流转推到腾讯云 CDN</h4>
          <p>接下里主要介绍 Web 端 SDK 发起云端混流。其他端实现方式可参考 <a target="_blank" href="https://cloud.tencent.com/document/product/647/16827">云端混流转码</a>。</p>
          <p>当发起混流转码时，本插件会向腾讯云的转码服务器发送一条指令，目的是将房间里的多路音视频流混合为一路。开发者可以通过 <code>encoding</code> 和 <code>mix</code> 参数来调整每一路混入流的参数和混合后音视频流的编码值，原理图如下：</p>
          <img src="https://qcloudimg.tencent-cloud.cn/raw/780b843f7f5f4a06dcd9ff0dc13b2621.png" style="width: 1000px">
          <p>您可以在开启插件时对本地用户流和远端用户流进行任意排版，并且在远端流开始推流和停止推流的时候根据需求及时调整混流参数。可实现的其中一种效果如下：</p>
          <ol>
            <li>调用 startPlugin，混流的布局如图 A 所示；</li>
            <li>想要新增屏幕分享时，调用 updatePlugin 更新参数，混流的布局如图 B 所示；</li>
            <li>不需要混流时调用 stopPlugin。</li>
          </ol>
          <img src="https://qcloudimg.tencent-cloud.cn/raw/3069d75fab45d643ec940f8705d65b7b.png" style="width: 600px">
          <p>以实现上面的效果为例，开启的步骤为：</p>
          <ol>
            <li>
              <p>将插件参数 <code>target.publishMode</code> 设置为 <code>PublishMode.PublishMixStreamToCDN</code>，表示目前是对混流进行操作。</p>
            </li>
            <li>
              <p>指定 <code>encoding</code> 参数中的 videoWidth，videoHeight, videoBitrate, videoFramerate, videoGOP, audioSampleRate, audioBitrate, audioChannels 这些关乎混流输出流的音视频质量的参数，同时您也可以通过指定 <code>mix</code> 参数中的 backgroundColor 及 backgroundImage 参数指定混流输出流的背景颜色和背景图片。</p>
              <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
                <p>背景图需要您事先在 “<a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a> &gt; 应用管理 &gt; 功能配置 &gt; 素材管理” 中单击【新增图片】按钮进行上传。 上传成功后可以获得对应的“图片ID”，然后将“图片ID”转换成字符串类型并设置到 backgroundImage 里即可。 例如：假设“图片ID”为 63，则设置 backgroundImage 为 &quot;63&quot;;</p>
              </blockquote>
            </li>
            <li>
              <p>组装 <code>mix.audioMixUserList</code> 参数，将需要用户的音频填入到数组中。</p>
            </li>
            <li>
              <p>组装 <code>mix.videoLayoutList</code> 参数，如果需要视频布局信息，则可以将不同的布局信息填写到这里的数组中。</p>
            </li>
            <li>
              <p>配置好参数后，开启混流，将会自动发布在 CDN 上，默认播放地址为 <code>http://播放域名/live/${sdkAppId}_${roomId}_${userId}_main.flv</code>，若配置了 streamId，则变为 <code>http://播放域名/live/[streamId].flv</code></p>
            </li>
          </ol>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 开启混流
let options = {
  target: {
    publishMode: PublishMode.PublishMixStreamToCDN,
    streamId: 'mix-stream',
  },
  encoding: {
    videoWidth: 1280,
    videoHeight: 720,
    videoBitrate: 1500,
    videoFramerate: 15,
  },
  mix: {
    audioMixUserList: [
      {
        userId: 'current_user_id', // 当前用户 ID
      }
    ],
    videoLayoutList: [
      {
        fixedVideoUser: {
          userId: 'current_user_id', // 当前用户 ID
        },
        width: 640,
        height: 480,
        locationX: 0,
        locationY: 0,
        fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN, // 这个用户的摄像头流还是屏幕分享流
        zOrder: 1
      },
    ]
  }
};
try {
  await trtc.startPlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming start failed', error);
}
</code></pre>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 更新混流参数，此处为添加上当前用户的屏幕分享
let options = {
  target: {
    publishMode: PublishMode.PublishMixStreamToCDN,
    streamId: 'mix-stream',
  },
  encoding: {
    videoWidth: 1280,
    videoHeight: 720,
    videoBitrate: 1500,
    videoFramerate: 15,
  },
  mix: {
    audioMixUserList: [
      {
        userId: 'current_user_id',
      }
    ],
    videoLayoutList: [
      {
        fixedVideoUser: {
          userId: 'current_user_id',
        },
        width: 640,
        height: 480,
        locationX: 0,
        locationY: 0,
        fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
        zOrder: 1
      },
      {
        fixedVideoUser: {
          userId: 'current_user_id',
        },
        width: 640,
        height: 480,
        locationX: 640,
        locationY: 0,
        fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_SUB,
        zOrder: 1
      },
    ]
  }
}
try {
  await trtc.updatePlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming update failed', error);
}
</code></pre>
          <pre class="highlight lang-javascript source lang-javascript"><code>// 停止混流
let options = {
  target: {
    publishMode: PublishMode.PublishMixStreamToCDN,
  }
}
try {
  await trtc.updatePlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming update failed', error);
}
</code></pre>
          <h4>场景三：推流到指定 CDN</h4>
          <ol>
            <li>
              <p>在任意云服务商获取 CDN 推流地址 url。</p>
            </li>
            <li>
              <p>在 <a target="_blank" href="https://console.cloud.tencent.com/trtc">实时音视频控制台</a>【应用管理-配置-旁路转推配置】查看默认推流域名的前6个数字是 bizid，应用的 SDKAppId 是 appid。</p>
            </li>
            <li>
              <p>开始向指定 CDN 地址发布当前用户的音视频流。以发布用户摄像头流到指定 CDN 为例</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>let options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN,
    appId: 0,
    bizId: 0,
    url: ''
  }
};
try {
  await trtc.startPlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming start customCDN failed', error);
}
</code></pre>
              <p>停止发布的代码：</p>
              <pre class="highlight lang-javascript source lang-javascript"><code>let options = {
  target: {
    publishMode: PublishMode.PublishMainStreamToCDN,
  }
}
try {
  await trtc.startPlugin('CDNStreaming', options);
} catch (error) {
  console.error('CDNStreaming stop customCDN failed ', error);
}
</code></pre>
            </li>
          </ol>
          <h2>API 说明</h2>
          <p>对于 <code>startPlugin('CDNStreaming', options)</code> 与 <code>updatePlugin('CDNStreaming', options)</code>，参数类型可见 <a href="./global.html#CDNStreamingOptions">CDNStreamingOptions</a>。</p>
          <p>对于 <code>stopPlugin('CDNStreaming', options)</code>，参数只需要填入 <code>options.target.publishMode</code> 字段即可。</p>
          <h2>注意事项</h2>
          <ol>
            <li>
              <p>录制混合后的音频流，请参考 <a target="_blank" href="https://cloud.tencent.com/document/product/647/50768#3158718a-df0d-4590-b3d4-a4796d4c6561">云端录制</a>。</p>
            </li>
            <li>
              <p>云端混流转码需要对输入 MCU 集群的音视频流进行解码后重新编码输出，将产生额外的服务成本，因此 TRTC 将向使用 MCU 集群进行云端混流转码的用户收取额外的增值费用。云端混流转码费用根据<strong>转码输出的分辨率大小</strong>和<strong>转码时长</strong>进行计费，转码输出的分辨率越高、转码输出的时间越长，费用越高。详情请参见 <a target="_blank" href="https://cloud.tencent.com/document/product/647/49446">云端混流转码计费说明</a>。</p>
            </li>
            <li>
              <p>退出房间后不会继续计费，但在重新进房时会自动继续开启，若想直接终止转推 CDN 和混流，请执行 stopPlugin 对应的 publishMode。</p>
            </li>
          </ol>
          <h2>常见问题</h2>
          <p>Q.无法停止推流？</p>
          <p>请检查是否在控制台开启了“全局自动旁路”，该模式下无法通过接口停止。</p>
          <p>Q.如何查看目前所有的流？</p>
          <p>可通过 <a target="_blank" href="https://console.cloud.tencent.com/live/streammanage">流管理界面</a> 查看目前的流状态。</p>
          <p>Q.屏幕分享模糊？</p>
          <p>SDK 默认使用 <code>1080p</code> 参数配置来采集屏幕分享，具体参考接口：<a href="TRTC.html#startScreenShare">startScreenShare</a></p>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      