<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - EVENT - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">EVENT</h1>
      <section>
        <header style="display:none">
        </header>
        <article>
          <div class="container-overview">
            <div class="description">
              <p><strong>TRTC事件列表</strong><br>
                <br>
                通过 <a href="TRTC.html#on">trtc.on(TRTC.EVENT.XXX)</a> 监听指定的事件。您可以通过这些事件实现管理房间用户列表，以及管理用户的流状态，感知网络状态等功能，下面是事件的详细介绍。
              </p>
              <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
                <p>注意：</p>
                <ul>
                  <li>事件需要在事件触发之前监听，这样才能收到相应的事件通知，因此建议在 trtc 进房前完成事件监听，这样才能确保不会漏掉事件通知。</li>
                </ul>
              </blockquote>
            </div>
            <dl class="details">
            </dl>
            <h5>Example</h5>
            <pre class="highlight lang-javascript"><code>// 使用方式：
trtc.on(TRTC.EVENT.ERROR, () => {});</code></pre>
          </div>
          <h3 class="subsection-title">Members</h3>
          <h4 class="name" id=".ERROR"><span class="type-signature">(static) </span>ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'error'</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li><a href="RtcError.html">RtcError</a></li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>错误事件，非 API 调用错误，SDK 在运行过程中出现了不可恢复的错误时抛出。</p>
            <ul>
              <li>错误码(error.code)为：<a href="module-ERROR_CODE.html#.OPERATION_FAILED">ErrorCode.OPERATION_FAILED</a></li>
              <li>可能的扩展错误码(error.extraCode)：5501, 5502</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.ERROR, error => {
  console.error('trtc error observed: ' + error);
  const errorCode = error.code;
  const extraCode = error.extraCode;
});</code></pre>
          <h4 class="name" id=".AUTOPLAY_FAILED"><span class="type-signature">(static) </span>AUTOPLAY_FAILED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'autoplay-failed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>自动播放失败，参考 <a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-21-advanced-auto-play-policy.html">自动播放处理建议</a></p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUTOPLAY_FAILED, event => {
  // 引导用户点击页面，当用户点击页面时，SDK 会自动恢复播放。
  // 自 v5.1.3+ 新增 userId 参数，表示哪个用户出现自动播放失败。
  console.log(event.userId);
});</code></pre>
          <h4 class="name" id=".KICKED_OUT"><span class="type-signature">(static) </span>KICKED_OUT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'kicked-out'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>被踢出房间，原因如下：<br /></p>
            <ul>
              <li>kick: 相同 userId 的用户同时进入同一个房间（以下简称为同名进房），先进入房间的用户会被后进入的用户踢出房间。<br>
                <ul>
                  <li>同名进房是不允许的行为，可能会导致双方音视频通话异常，业务侧应避免出现这种情况。</li>
                  <li>TRTC 后台不保证观众角色同名进房互踢。即观众角色的用户，使用同 userId 进同一个房间，可能不会收到该事件。</li>
                </ul>
              </li>
              <li>banned: 系统管理员通过<a target="_blank" href="https://cloud.tencent.com/document/product/647/40496">服务端 API</a> 将该用户踢出房间。</li>
              <li>room-disband: 系统管理员通过<a target="_blank" href="https://cloud.tencent.com/document/product/647/40496">服务端 API</a> 解散房间。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.KICKED_OUT, event => {
  console.log(event.reason)
});</code></pre>
          <h4 class="name" id=".REMOTE_USER_ENTER"><span class="type-signature">(static) </span>REMOTE_USER_ENTER<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-user-enter'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端用户进房事件。</p>
            <ul>
              <li><code>live</code> 模式下，只有主播才有进退房通知，观众没有进退房通知，观众可以收到主播的进退房通知。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_USER_EXIT"><span class="type-signature">(static) </span>REMOTE_USER_EXIT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-user-exit'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端用户退房事件。</p>
            <ul>
              <li><code>live</code> 模式下，只有主播才有进退房通知，观众没有进退房通知，观众可以收到主播的进退房通知。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_AUDIO_AVAILABLE"><span class="type-signature">(static) </span>REMOTE_AUDIO_AVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-audio-available'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端用户发布了音频。当远端用户打开麦克风后，您会收到该通知。参考：<a href="./tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></p>
            <ul>
              <li>默认情况下，SDK 会自动播放远端音频，您无需调用 API 来播放远端音频。可以监听该事件及 <a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a> 来更新“远端是否开启麦克风”的 UI icon。</li>
              <li>需要注意的是：如果用户在进房前没有与页面产生过交互，自动播放音频可能会因为【浏览器的自动播放策略限制】而失败，您需参考<a href="./tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a>进行处理。</li>
              <li>若您不希望 SDK 自动播放音频，您可以在 <a href="TRTC.html#enterRoom">trtc.enterRoom()</a> 时设置 receiveMode = TRTC.TYPE.RECEIVE_MODE_MANUAL 关闭自动播放音频。</li>
              <li>监听 <a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">TRTC.EVENT.REMOTE_AUDIO_AVAILABLE</a> 事件，记录有远端音频的 userId，在需要播放音频时，调用 <a href="TRTC.html#muteRemoteAudio">trtc.muteRemoteAudio(userId, false)</a> 方法。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 在进房前监听
trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_AUDIO_UNAVAILABLE"><span class="type-signature">(static) </span>REMOTE_AUDIO_UNAVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-audio-unavailable'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端停止发布了音频。当远端用户关闭麦克风后，您会收到该通知。</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 在进房前监听
trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_VIDEO_AVAILABLE"><span class="type-signature">(static) </span>REMOTE_VIDEO_AVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-video-available'</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
                <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端用户发布了视频，当远端用户开启摄像头后，您会收到该通知。参考：<a href="./tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></p>
            <ul>
              <li>可以监听该事件及 <a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a> 来更新“远端是否开启摄像头”的 UI icon。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 在进房前监听
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
  const userId = event.userId;
  const streamType = event.streamType;
  trtc.startRemoteVideo({userId, streamType, view});
});</code></pre>
          <h4 class="name" id=".REMOTE_VIDEO_UNAVAILABLE"><span class="type-signature">(static) </span>REMOTE_VIDEO_UNAVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-video-unavailable'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>远端用户停止发布视频，当远端用户关闭摄像头后，您会收到该通知。</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 在进房前监听
trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, event => {
  const userId = event.userId;
  const streamType = event.streamType;
  // 此时 SDK 会自动停止播放，无需调用 stopRemoteVideo。
});</code></pre>
          <h4 class="name" id=".AUDIO_VOLUME"><span class="type-signature">(static) </span>AUDIO_VOLUME<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'audio-volume'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>音量大小事件<br>
              调用 <a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a> 接口开启音量大小回调后，SDK 会定时抛出该事件，通知每个 userId 的音量大小。<br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>回调中包含本地麦克风音量及远端用户的音量，无论是否有人说话，都会触发该回调。</li>
              <li>回调 event.result 会根据音量大小，按大到小进行排序。</li>
              <li>当 userId 为空串时，代表本地麦克风音量。</li>
              <li>volume 取值为0-100的正整数</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   event.result.forEach(({ userId, volume }) => {
       const isMe = userId === ''; // 当 userId 为空串时，代表本地麦克风音量。
       if (isMe) {
           console.log(`my volume: ${volume}`);
       } else {
           console.log(`user: ${userId} volume: ${volume}`);
       }
   })
});
// 开启音量回调，并设置每 1000ms 触发一次事件
trtc.enableAudioVolumeEvaluation(1000);</code></pre>
          <h4 class="name" id=".NETWORK_QUALITY"><span class="type-signature">(static) </span>NETWORK_QUALITY<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'network-quality'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>网络质量统计数据事件，进房后开始统计，每两秒触发一次，该数据反映的是您本地的上、下行的网络质量。</p>
            <ul>
              <li>
                <p>上行网络质量（uplinkNetworkQuality）指的是您上传本地流的网络情况（SDK 到腾讯云的上行连接网络质量）</p>
              </li>
              <li>
                <p>下行网络质量（downlinkNetworkQuality）指的是您下载所有流的平均网络情况（腾讯云到 SDK 的所有下行连接的平均网络质量）</p>
                <p>其枚举值及含义如下表所示：</p>
                <table>
                  <thead>
                    <tr>
                      <th style="text-align:left">数值</th>
                      <th style="text-align:left">含义</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td style="text-align:left">0</td>
                      <td style="text-align:left">网络状况未知，表示当前 trtc 实例还没有建立上行/下行连接</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">1</td>
                      <td style="text-align:left">网络状况极佳</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">2</td>
                      <td style="text-align:left">网络状况较好</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">3</td>
                      <td style="text-align:left">网络状况一般</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">4</td>
                      <td style="text-align:left">网络状况差</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">5</td>
                      <td style="text-align:left">网络状况极差</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">6</td>
                      <td style="text-align:left">网络连接已断开<br />注意：若下行网络质量为此值，则表示所有下行连接都断开了</td>
                    </tr>
                  </tbody>
                </table>
              </li>
              <li>
                <p>uplinkRTT，uplinkLoss 为上行 RTT(ms) 及上行丢包率。</p>
              </li>
              <li>
                <p>downlinkRTT，downlinkLoss 为所有下行连接的平均 RTT(ms) 及平均丢包率。</p>
              </li>
            </ul>
            <p><strong>Note</strong></p>
            <ul>
              <li>如果您想知道对方的上下行网络情况，需要把对方的网络质量情况通过 IM 广播出去。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.NETWORK_QUALITY, event => {
   console.log(`network-quality, uplinkNetworkQuality:${event.uplinkNetworkQuality}, downlinkNetworkQuality: ${event.downlinkNetworkQuality}`)
   console.log(`uplink rtt:${event.uplinkRTT} loss:${event.uplinkLoss}`)
   console.log(`downlink rtt:${event.downlinkRTT} loss:${event.downlinkLoss}`)
})</code></pre>
          <h4 class="name" id=".CONNECTION_STATE_CHANGED"><span class="type-signature">(static) </span>CONNECTION_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'connection-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>SDK 和腾讯云的连接状态变更事件，您可以利用该事件从总体上监听 SDK 与腾讯云的连接状态。<br></p>
            <ul>
              <li>'DISCONNECTED'：连接断开</li>
              <li>'CONNECTING'：正在连接中</li>
              <li>'CONNECTED'：已连接</li>
            </ul>
            <p>不同状态变更的含义：</p>
            <ul>
              <li>DISCONNECTED -&gt; CONNECTING: 正在尝试建立连接，调用进房接口或者 SDK 自动重连时触发。</li>
              <li>CONNECTING -&gt; DISCONNECTED: 连接建立失败，当正在连接时调用退房接口中断连接或者经过 SDK 重试后任然连接失败时触发。</li>
              <li>CONNECTING -&gt; CONNECTED: 连接建立成功，连接成功时触发。</li>
              <li>CONNECTED -&gt; DISCONNECTED: 连接中断，调用退房接口或者当网络异常导致连接断开时触发。</li>
            </ul>
            <p>处理建议：可以监听该事件，在不同状态显示不同的 UI，提醒用户当前的连接状态。</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.CONNECTION_STATE_CHANGED, event => {
  const prevState = event.prevState;
  const curState = event.state;
});</code></pre>
          <h4 class="name" id=".AUDIO_PLAY_STATE_CHANGED"><span class="type-signature">(static) </span>AUDIO_PLAY_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'audio-play-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>音频播放状态变更事件</p>
            <p>event.userId 当 userId 为空串时，代表本地用户，非空串代表远端用户。</p>
            <p>event.state 取值如下：</p>
            <ul>
              <li>'PLAYING'：开始播放
                <ul>
                  <li>event.reason 为 'playing' 或者 'unmute'。</li>
                </ul>
              </li>
              <li>'PAUSED'：暂停播放
                <ul>
                  <li>event.reason 为 'pause' 时，由 &lt;audio&gt; element 的 pause 事件触发，如下几种情况会触发：
                    <ul>
                      <li>调用 HTMLMediaElement.pause 接口。</li>
                    </ul>
                  </li>
                  <li>event.reason 为 'mute' 时。详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a>
                    <ul>
                      <li>若 userId 为自己时 触发该事件，表明音频采集暂停，通常是设备异常引起，如设备被其他应用抢占，此时需引导用户重新采集。</li>
                      <li>若 userId 为他人时 触发该事件，表明收到的音频数据不足以播放。通常是网络抖动引起，接入侧无需做任何处理。当收到的数据足以播放时，会自动恢复。</li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li>'STOPPED'：停止播放
                <ul>
                  <li>event.reason 为 'ended'。</li>
                </ul>
              </li>
            </ul>
            <p>event.reason 状态变化的原因，取值如下：</p>
            <ul>
              <li>'playing'：开始播放，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event%20"> HTMLMediaElement.playing_event</a></li>
              <li>'mute'：音频轨道暂时未能提供数据，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a></li>
              <li>'unmute'：音频轨道恢复提供数据，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event%20"> MediaStreamTrack.unmute_event</a></li>
              <li>'ended'：音频轨道已被关闭</li>
              <li>'pause'：播放暂停</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_PLAY_STATE_CHANGED, event => {
  console.log(`${event.userId} player is ${event.state} because of ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".VIDEO_PLAY_STATE_CHANGED"><span class="type-signature">(static) </span>VIDEO_PLAY_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'video-play-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>视频播放状态变更事件</p>
            <p>event.userId 当 userId 为空串时，代表本地用户，非空串代表远端用户。</p>
            <p>event.streamType 流类型，取值：<a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a> <a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a></p>
            <p>event.state 取值如下：</p>
            <ul>
              <li>'PLAYING'：开始播放
                <ul>
                  <li>event.reason 为 'playing' 或者 'unmute'。</li>
                </ul>
              </li>
              <li>'PAUSED'：暂停播放
                <ul>
                  <li>event.reason 为 'pause' 时，由 &lt;video&gt; element 的 pause 事件触发，如下几种情况会触发：
                    <ul>
                      <li>调用 HTMLMediaElement.pause 接口。</li>
                      <li>在播放成功后，从 DOM 中移除了播放视频的 view 容器。</li>
                    </ul>
                  </li>
                  <li>event.reason 为 'mute' 时。详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a>
                    <ul>
                      <li>若 userId 为自己时 触发该事件，表明视频采集暂停，通常是设备异常引起，如设备被其他应用抢占，此时需引导用户重新采集。</li>
                      <li>若 userId 为他人时 触发该事件，表明收到的视频数据不足以播放。通常是网络抖动引起，接入侧无需做任何处理。当收到的数据足以播放时，会自动恢复。</li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li>'STOPPED'：停止播放
                <ul>
                  <li>event.reason 为 'ended'。</li>
                </ul>
              </li>
            </ul>
            <p>event.reason 状态变化的原因，取值如下：</p>
            <ul>
              <li>'playing'：开始播放，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event%20"> HTMLMediaElement.playing_event</a></li>
              <li>'mute'：视频轨道暂时未能提供数据，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a></li>
              <li>'unmute'：视频轨道恢复提供数据，详见事件 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event%20"> MediaStreamTrack.unmute_event</a></li>
              <li>'ended'：视频轨道已被关闭</li>
              <li>'pause'：播放暂停</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.VIDEO_PLAY_STATE_CHANGED, event => {
  console.log(`${event.userId} ${event.streamType} video player is ${event.state} because of ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".SCREEN_SHARE_STOPPED"><span class="type-signature">(static) </span>SCREEN_SHARE_STOPPED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'screen-sharing-stopped'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>本地屏幕分享停止事件通知，仅对本地屏幕分享流有效。</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, () => {
  console.log('screen sharing was stopped');
});</code></pre>
          <h4 class="name" id=".DEVICE_CHANGED"><span class="type-signature">(static) </span>DEVICE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'device-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>摄像头、麦克风等设备变化的通知事件。</p>
            <ul>
              <li>event.device 是一个 <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a> 对象，属性：
                <ul>
                  <li>deviceId：设备 Id</li>
                  <li>label：设备描述信息</li>
                  <li>groupId：设备 groupId</li>
                </ul>
              </li>
              <li>event.type 值：<code>'camera'|'microphone'|'speaker'</code></li>
              <li>event.action 值：
                <ul>
                  <li>'add' 设备已添加。</li>
                  <li>'remove' 设备已被移除。</li>
                  <li>'active' 设备已启动，例如：startLocalVideo 成功后，会触发该事件。</li>
                </ul>
              </li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
  console.log(`${event.type}(${event.device.label}) ${event.action}`);
});</code></pre>
          <h4 class="name" id=".PUBLISH_STATE_CHANGED"><span class="type-signature">(static) </span>PUBLISH_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'publish-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>推流状态变更事件。</p>
            <ul>
              <li>event.mediaType 媒体类型，值：<code>'audio'|'video'|'screen'</code>。</li>
              <li>event.state 当前的推流状态，值：
                <ul>
                  <li><code>'starting'</code> 正在尝试推流</li>
                  <li><code>'started'</code> 推流成功</li>
                  <li><code>'stopped'</code> 推流停止，原因见 event.reason 字段</li>
                </ul>
              </li>
              <li>event.prevState 上一次事件触发时的推流状态，值和 event.state 相同。</li>
              <li>event.reason 推流状态变为 <code>'stopped'</code> 的原因，值：
                <ul>
                  <li><code>'timeout'</code> 推流超时，一般是由于网络抖动、防火墙拦截导致。SDK 会不断进行重试，业务侧可以在此时引导用户检查网络、更换网络。</li>
                  <li><code>'error'</code> 推流出错，此时可从 event.error 中获取到具体错误信息，一般是由于浏览器不支持 H264 编码导致。</li>
                  <li><code>'api-call'</code> 业务侧 api 调用导致推流停止，例如在 startLocalVideo 推流成功前，调用了 stopLocalVideo 停止了推流，属于正常行为，业务侧无需关注。</li>
                </ul>
              </li>
              <li>event.error event.reason 为 <code>'error'</code> 时的错误信息。</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
  console.log(`${event.mediaType} ${event.state} ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".STATISTICS"><span class="type-signature">(static) </span>STATISTICS<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>v5.2.0</li>
              </ul>
            </dd>
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'statistics'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>通话相关数据指标。<br></p>
            <ul>
              <li>监听该事件后，SDK 会以 2s 一次的频率定时抛出该事件。</li>
              <li>您可以从该事件中可以获取通话的网络质量（rtt、丢包率）、上行和下行的音视频统计信息（音量大小、宽高、帧率、码率）等信息。详细参数说明请参考：<a href="global.html#TRTCStatistics">TRTCStatistics</a></li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.STATISTICS, statistics => {
   console.warn(statistics.rtt, statistics.upLoss, statistics.downLoss);
})</code></pre>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      