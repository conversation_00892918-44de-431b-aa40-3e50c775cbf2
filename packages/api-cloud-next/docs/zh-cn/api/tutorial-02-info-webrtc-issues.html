<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <title>腾讯云 RTC SDK - WebRTC 已知问题及规避方案 - Documentation</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK 版本发布日志</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC 已知问题及规避方案</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">错误码说明及处理建议</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">房间内上行用户个数限制</a></li>
        <li><a href="tutorial-05-info-browser.html">浏览器与应用环境信息</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a></li>
        <li><a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">实现互动直播连麦</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">切换摄像头和麦克风</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">设置本地视频属性</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">开关摄像头、麦克风</a></li>
        <li><a href="tutorial-16-basic-screencast.html">屏幕分享</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">音量大小检测</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">背景音乐实现方案</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">通话前环境与设备检测</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">检测网络质量</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">处理设备插拔</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">云端混流与转推CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">开启大小流</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">开启水印</a></li>
        <li><a target="_blank" href="https://cloud.tencent.com/document/product/1093/68499">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>
        
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: WebRTC 已知问题及规避方案</h1>
      <section>
        <header style="display:none">
          <h2>WebRTC 已知问题及规避方案</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>Chrome</h2>
          <ol>
            <li>（2021-11-23）Chrome 88 开启硬件加速时，使用 HTMLMediaElement.captureStream 推 MP4 文件，远端拉流观看黑屏问题。<a target="_blank" href="https://bugs.chromium.org/p/chromium/issues/detail?id=1156408">Chrome 88 bug</a>。<br>规避方案：
              <ul>
                <li>升级至 Chrome 96+ 版本。</li>
                <li>也可通过关闭硬件加速来规避。</li>
              </ul>
            </li>
            <li>（2021-2-3）Mac Chrome 88(88.0.4324.96) 关闭硬件加速时，推摄像头采集的视频流，远端拉流观看黑屏问题。 <a target="_blank" href="https://bugs.chromium.org/p/chromium/issues/detail?id=1168948#c34">Chrome 88 bug</a><br>规避方案：
              <ul>
                <li>升级 Chrome 88.0.4324.146+ 版本</li>
                <li>保持开启硬件加速不会出现该问题（Chrome 默认是打开硬件加速的）</li>
              </ul>
            </li>
            <li>（2021-2-2）Chrome 使用 deviceId 为 default 或 communications (Windows 设备下会有该 deviceId) 时，若插入新的麦克风，再拔出，可能会导致麦克风采集中断。<br>规避方案：
              <ul>
                <li>避免使用 deviceId 为 default 或 communications 的麦克风设备即可。</li>
              </ul>
            </li>
            <li>(2023-07-20) Mac 设备中，若安装了 Mersive Solstic 软件，其虚拟摄像头驱动会导致 Chrome 无法获取摄像头列表，导致无法采集摄像头。<a target="_blank" href="https://bugs.chromium.org/p/chromium/issues/detail?id=1379392">Chrome issue</a> &amp; <a target="_blank" href="https://documentation.mersive.com/content/pages/release-notes.htm">Mersive Solstic 5.5.2 known issue</a>。
              <ul>
                <li>
                  <p>规避方案：引导用户删除该虚拟摄像头驱动，或等待后续 Chrome or Mersive Solstic 规避。</p>
                  <pre class="highlight lang-javascript source lang-js"><code>sudo rm -rf /Library/CoreMediaIO/Plug-Ins/DAL/RelayCam.plugin
</code></pre>
                </li>
              </ul>
            </li>
          </ol>
          <h2>Firefox</h2>
          <ol>
            <li>（2021-2-2）Firefox 不支持设置采集帧率，只能采集 30fps 的视频。</li>
            <li>（2022-7-7）首次安装的 Firefox 浏览器会在联网状态下动态安装 H.264 编解码器，在安装完成前，无法正常使用 SDK 推拉流。处理建议：
              <ul>
                <li>通过 SDK 的 <a href="./TRTC.html#.isSupported">TRTC.isSupported</a> 接口，若检测到在 Firefox 下不支持 H264 编解码，则引导用户在 Firefox 打开地址：<code>about:addons</code>，到 <code>插件</code> 中检查 OpenH264 的安装情况。等待安装完成后再进行通话。<br />
                  <img src="./assets/firefox-264.png" width="400" />
                </li>
              </ul>
            </li>
          </ol>
          <h2>Safari</h2>
          <ol start="2">
            <li>
              <p>（2021-2-2）iOS Safari 不支持多个 tab getUserMedia，否则前一个 tab 会停止采集，远端流也有可能出现黑屏无声。<a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=179363">webkit bug</a> <br>规避方案：</p>
              <ul>
                <li>iOS Safari 尚无计划支持多 tab getUserMedia 特性，若业务侧需要在 iOS Safari 使用多个 tab getUserMedia，建议在切换新 tab 之前，停止设备采集，在切换回来后，再恢复设备采集。<br>注：多个 tab getUserMedia 的业务场景一般有：在视频通话过程中，切换新 tab 进行人脸识别。</li>
              </ul>
            </li>
            <li>
              <p>（2021-10-28）iOS Safari 和 Mac Big Sur Safari 音视频互通，iOS Safari 观看 Mac Safari 的视频卡顿掉帧。<br>规避方案：</p>
              <ul>
                <li>升级 Mac BigSur 最新版本。</li>
              </ul>
            </li>
            <li>
              <p>（2021-2-2）iOS 14.2 部分设备及 Mac Big Sur Safari，音频播放会有杂音。<a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=218762">webkit bug</a> 。 <br>规避方案：</p>
              <ul>
                <li>iOS 设备升级 14.3 及其以上版本、Mac Big Sur 升级最新版本。</li>
              </ul>
            </li>
            <li>
              <p>（2021-9-28）iOS 15 Safari 音视频通话时，扬声器外放声音可能会比 iOS 14 低。 <a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=230902">webkit bug</a> 。 <br>规避方案：</p>
              <ul>
                <li>升级 iOS 版本至15.4+。</li>
              </ul>
            </li>
            <li>
              <p>（2021-12-24） iOS 15 Safari 及 WKWebview 音视频通话时，连接蓝牙耳机可能会出现声音播放异常的问题。<a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=234595">webkit bug</a> 。<br>规避方案:</p>
              <ul>
                <li>升级 iOS 版本至15.4+。</li>
              </ul>
            </li>
            <li>
              <p>（2022-1-20） A 用户外放 B 用户的声音，同时手机也在采集 A 的声音，此时 B 用户听到的声音会伴随有电流声，这是 iOS 14 回声消除功能带来的副作用。<br>规避方案:</p>
              <ul>
                <li>使用耳麦进行通话。</li>
                <li>升级 iOS 版本至 15.4+。</li>
              </ul>
            </li>
            <li>
              <p>（2022-01-19）iOS 15 以下版本，canvas.captureStream 采集出的视频流，无法使用 video 标签播放。<a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=181663">webkit bug</a>。</p>
            </li>
            <li>
              <p>(2022-04-20) iOS 15 在某些机型，当您的页面有播放非 MediaStream 的 Audio 标签时，在页面获取麦克风又关闭后，可能会出现 Audio 标签播放的声音变小的情况。<a target="_blank" href="https://bugs.webkit.org/show_bug.cgi?id=236439">webkit bug</a> 。</p>
            </li>
          </ol>
          <h2>Webview</h2>
          <ol>
            <li>（2021-11-23）Android System Webview M79 以下的版本，无法使用 H264 解码。<a target="_blank" href="https://bugs.chromium.org/p/chromium/issues/detail?id=801501">webview bug</a> 。<br>规避方案：
              <ul>
                <li>引导用户升级 Android System Webview 版本至 M79+，需要安装对应版本的 webview apk 安装包。</li>
              </ul>
            </li>
          </ol>
          <h2>华为设备</h2>
          <ol>
            <li>（2021-4-29）华为浏览器、华为设备中的 Chrome 浏览器无法推流。由于华为设备的限制，部分版本的华为浏览器及华为 Chrome 浏览器不支持 H264 编码，因此无法推流。规避方案：使用 VP8 编码，需开通白名单，可提交 <a target="_blank" href="https://cloud.tencent.com/apply/p/pnh3a63d95">TRTC 用户支持申请</a> 后加入TRTC交流群，联系群内技术支持开通。</li>
          </ol>
          <h2>小米设备</h2>
          <ol>
            <li>（2021-11-23）在部分小米手机的微信中，会出现拉流无声问题。已知的机型有：小米9、小米10s、小米11、K30 5G等。该问题为 MIUI 已知问题，小米工程师正着手修复。微信也找到了规避方案，目前正在灰度中。</li>
          </ol>
          <h2>微信</h2>
          <ol>
            <li>微信 TBS/045811 及其以下版本的内核，在授权窗口弹出后，若超过5s才点击授权按钮，可能会出现<a href="./module-EVENT.html#.AUTOPLAY_FAILED">自动播放失败事件</a>。TBS 已知问题，后续版本的 TBS 内核会修复该问题。<br>规避方案：
              <ul>
                <li>可参考<a href="./tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a>中的方案二，当出现自动播放错误时，引导客户点击页面后，调用 stream.resume() 接口恢复播放。</li>
              </ul>
            </li>
          </ol>
          <h2>企业微信</h2>
          <ol>
            <li>iOS 企业微信 WebView 打开，获取媒体设备方法之后可能会出现授权弹框不出现的情况。<br>规避方案：
              <ul>
                <li>升级 iOS 企业微信版本 &gt;= 4.0.6</li>
              </ul>
            </li>
          </ol>
          <h2>屏幕分享</h2>
          <ol>
            <li>（2021-2-2）Windows &amp; Mac Chrome 浏览器屏幕分享某个 app 后，最小化会导致采集停止，fps = 0</li>
            <li>（2021-9-29）Windows 端使用 Chrome 屏幕分享，选择应用窗口分享【微信】【QQ】【钉钉】【WPS】时，可能会出现采集黑屏；或者拖动应用窗口时出现采集黑屏。<br>规避方案：
              <ul>
                <li>暂无法解决，建议引导用户分享整个屏幕的方式规避。</li>
              </ul>
            </li>
            <li>（2021-11-16) Mac Firefox 屏幕分享可能会出现视频部分区域错位，<a target="_blank" href="https://bugzilla.mozilla.org/show_bug.cgi?id=1536777">Firefox bug</a>。暂无法规避，建议使用 Chrome or Safari 浏览器进行屏幕分享。</li>
            <li>（2022-03-18）Mac Chrome 在已授权屏幕录制的情况下屏幕分享失败，出现 &quot;NotAllowedError: Permission denied by system&quot; 或者 &quot;NotReadableError: Could not start video source&quot; 错误信息，<a target="_blank" href="https://bugs.chromium.org/p/chromium/issues/detail?id=1306876">Chrome bug</a>。解决方案：打开【设置】&gt; 点击【安全性与隐私】&gt; 点击【隐私】&gt; 点击【屏幕录制】&gt; 关闭 Chrome 屏幕录制授权 &gt; 重新打开 Chrome 屏幕录制授权 &gt; 关闭 Chrome 浏览器 &gt; 重新打开 Chrome 浏览器。</li>
            <li>(2022-03-31) Mac 中使用 Chrome 屏幕分享，窗口采集可能无法采集到 WPS PPT 全屏窗口。<br>规避方案：
              <ul>
                <li>Web SDK 层面暂无法解决，建议引导用户使用分享整个屏幕的方式规避。</li>
              </ul>
            </li>
          </ol>
          <h2>云端录制</h2>
          <ol>
            <li>云端转码录制会出现上行音视频流前几秒没有被录制到的情况。如需解决该问题，可提交 <a target="_blank" href="https://cloud.tencent.com/apply/p/pnh3a63d95">TRTC 用户支持申请</a> 后加入TRTC交流群，联系群内技术支持按需修改云端录制配置。</li>
          </ol>
          <h2>其他厂商</h2>
          <ol>
            <li>（2021-2-2）网易 WebRTC SDK 会改写 RTCPeerConnection.prototype.getStats 方法，返回的数据格式与标准 WebRTC 协议不一致。若同时引入 TRTC 和网易的 WebRTC SDK，TRTC 会拿不到音视频数据，导致无法向正常向仪表盘上报音视频通话数据。</li>
          </ol>
          <h2>关于 Vue 3 响应式及 Proxy</h2>
          <p>（2021-11-5）开发者在使用 Vue 3 时应注意，Vue 3 基于 Proxy 实现响应式。请使用 <a target="_blank" href="https://cn.vuejs.org/api/reactivity-advanced.html#markraw">Vue markRaw</a> 将 TRTC 实例设置为非响应式属性，若对 SDK 实例加了 Proxy，可能会导致 SDK 异常。</p>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:47 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      