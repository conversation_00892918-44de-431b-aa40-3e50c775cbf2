/**
   * @typedef TRTCStatistics TRTC 通话统计信息
   * @property {number} rtt 从 SDK 与 云端的往返延时（一个网络包经历 “SDK -> 云端 -> SDK” 的总耗时），单位 ms。
   * @property {number} upLoss 从 SDK 到云端的上行丢包率，单位 (%)
   * @property {number} downLoss 从云端到 SDK 的下行丢包率，单位 (%)
   * @property {number} bytesSent 总发送字节数（包含信令数据和音视频数据），单位：字节数（Bytes）。
   * @property {number} bytesReceived 总接收字节数（包含信令数据和音视频数据），单位：字节数（Bytes）。
   * @property {TRTCLocalStatistics} localStatistics 本地的音视频统计信息
   * @property {TRTCRemoteStatistics[]} remoteStatistics 远端的音视频统计信息
   */
/**
   * 本地的音视频统计信息
   * @typedef TRTCLocalStatistics
   * @property {TRTCAudioStatistic} audio 本地音频统计信息
   * @property {TRTCVideoStatistic[]} video 本地视频统计信息
   */
/**
   * 远端的音视频统计信息
   * @typedef TRTCRemoteStatistics
   * @property {string} userId 远端用户的 userId
   * @property {TRTCAudioStatistic} audio 远端音频统计信息
   * @property {TRTCVideoStatistic[]} video 远端视频统计信息
   */
/**
   * 音频统计信息
   * @typedef TRTCAudioStatistic
   * @property {number} bitrate 音频码率，单位：Kbps
   * @property {number} audioLevel 音量大小，0 ~ 1 的浮点数。
   * @property {number} jitterBufferDelay 播放延迟，单位：ms。Since `v5.11.0`。
   * @property {number} point2pointDelay 端到端延迟，单位：ms。该指标为估算值，会受到网络质量影响产生误差。Since `v5.11.0`
   */
/** 视频统计信息
   * @typedef TRTCVideoStatistic
   * @property {number} bitrate 视频码率，单位：Kbps
   * @property {number} width 视频宽度
   * @property {number} height 视频高度
   * @property {number} frameRate 视频帧率
   * @property {'big'|'small'|'sub'} videoType 视频类型，大流、小流、辅流。
   * @property {number} jitterBufferDelay 播放延迟，单位：ms。Since `v5.11.0`
   * @property {number} point2pointDelay 端到端延迟，单位：ms。该指标为估算值，会受到网络质量影响产生误差。Since `v5.11.0`
   *
   */
/**
 * @typedef EnableAudioFrameEventOptions
 * @property {boolean} enable 是否开启监听
 * @property {string} userId 监听的用户 userId. '' 表示本地麦克风数据, '*' 表示监听所有远端用户的音频数据。
 * @property {number=} [sampleRate=48000] 设置 pcm 数据的采样率，默认为 48000. 支持 8000, 16000, 32000, 44100, 48000
 * @property {number=} [channelCount=1] 设置 pcm 数据的声道数，默认为 1. 支持 1, 2
 * @property {MessagePort=} port 设置 pcm 回调的 MessagePort
 */
/**
 * **TRTC事件列表**<br>
 * <br>
 * 通过 {@link TRTC#on trtc.on(TRTC.EVENT.XXX)} 监听指定的事件。您可以通过这些事件实现管理房间用户列表，以及管理用户的流状态，感知网络状态等功能，下面是事件的详细介绍。
 * > !
 * > - 事件需要在事件触发之前监听，这样才能收到相应的事件通知，因此建议在 trtc 进房前完成事件监听，这样才能确保不会漏掉事件通知。
 * @module EVENT
 * @example
 * // 使用方式：
 * trtc.on(TRTC.EVENT.ERROR, () => {});
 */
export const TRTCEvent = {
  /**
     * 错误事件，非 API 调用错误，SDK 在运行过程中出现了不可恢复的错误时抛出。
     *
     * - 错误码(error.code)为：{@link module:ERROR_CODE.OPERATION_FAILED ErrorCode.OPERATION_FAILED}
     * - 可能的扩展错误码(error.extraCode)：5501, 5502
     * @default 'error'
     * @memberof module:EVENT
     * @see {@link RtcError RtcError}
     * @e
     * @example
     *
     * trtc.on(TRTC.EVENT.ERROR, error => {
     *   console.error('trtc error observed: ' + error);
     *   const errorCode = error.code;
     *   const extraCode = error.extraCode;
     * });
     */
  ERROR: 'error',
  /**
     * @description 自动播放失败，参考 [自动播放处理建议](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-21-advanced-auto-play-policy.html)
     * @default 'autoplay-failed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.AUTOPLAY_FAILED, event => {
     *   // 引导用户点击页面，当用户点击页面时，SDK 会自动恢复播放。
     *   // 自 v5.1.3+ 新增 userId 参数，表示哪个用户出现自动播放失败。
     *   console.log(event.userId);
     *   // 自 v5.11.0+ 新增 mediaType 参数，表示媒体类型，值：'audio'|'video'|'screen'。
     *   console.log(event.mediaType);
     *   // 自 v5.9.0+ 新增 resume 方法，当用户点击页面后，可以主动调用该方法恢复播放 event.userId 对应的流。
     *   event.resume();
     * });
     */
  AUTOPLAY_FAILED: 'autoplay-failed',
  /**
     * 被踢出房间，原因如下：<br/>
     * - kick: 相同 userId 的用户同时进入同一个房间（以下简称为同名进房），先进入房间的用户会被后进入的用户踢出房间。<br>
     *   - 同名进房是不允许的行为，可能会导致双方音视频通话异常，业务侧应避免出现这种情况。
     *   - TRTC 后台不保证观众角色同名进房互踢。即观众角色的用户，使用同 userId 进同一个房间，可能不会收到该事件。
     * - banned: 系统管理员通过{@link https://cloud.tencent.com/document/product/647/40496 服务端 API} 将该用户踢出房间。
     * - room-disband: 系统管理员通过{@link https://cloud.tencent.com/document/product/647/40496 服务端 API} 解散房间。
     * @default 'kicked-out'
     * @memberof module:EVENT
     * @example
     *
     * trtc.on(TRTC.EVENT.KICKED_OUT, event => {
     *   console.log(event.reason)
     * });
     */
  KICKED_OUT: 'kicked-out',
  /**
     * 远端用户进房事件。
     *
     * - `rtc` 模式下，所有用户都会收到进退房通知。
     * - `live` 模式下，只有主播才有进退房通知，观众没有进退房通知，观众可以收到主播的进退房通知。
     * @default 'remote-user-enter'
     * @memberof module:EVENT
     * @example
     *
     * trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, event => {
     *   const userId = event.userId;
     * });
     */
  REMOTE_USER_ENTER: 'remote-user-enter',
  /**
     * 远端用户退房事件。
     *
     * - `rtc` 模式下，所有用户都会收到进退房通知。
     * - `live` 模式下，只有主播才有进退房通知，观众没有进退房通知，观众可以收到主播的进退房通知。
     * @default 'remote-user-exit'
     * @memberof module:EVENT
     * @example
     *
     * trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, event => {
     *   const userId = event.userId;
     * });
     */
  REMOTE_USER_EXIT: 'remote-user-exit',
  /**
     * 远端用户发布了音频。当远端用户打开麦克风后，您会收到该通知。参考：[开关摄像头、麦克风](./tutorial-15-basic-dynamic-add-video.html)
     *
     * - 默认情况下，SDK 会自动播放远端音频，您无需调用 API 来播放远端音频。可以监听该事件及 {@link module:EVENT.REMOTE_AUDIO_UNAVAILABLE REMOTE_AUDIO_UNAVAILABLE} 来更新“远端是否开启麦克风”的 UI icon。
     * - 需要注意的是：如果用户在进房前没有与页面产生过交互，自动播放音频可能会因为【浏览器的自动播放策略限制】而失败，您需参考[自动播放受限处理建议](./tutorial-21-advanced-auto-play-policy.html)进行处理。
     * - 若您不希望 SDK 自动播放音频，您可以在 {@link TRTC#enterRoom trtc.enterRoom()} 时设置 `autoReceiveAudio` 为 `false` 关闭自动播放音频。
     * - 监听 {@link module:EVENT.REMOTE_AUDIO_AVAILABLE TRTC.EVENT.REMOTE_AUDIO_AVAILABLE} 事件，记录有远端音频的 userId，在需要播放音频时，调用 {@link TRTC#muteRemoteAudio trtc.muteRemoteAudio(userId, false)} 方法。
     * @default 'remote-audio-available'
     * @memberof module:EVENT
     * @example
     * // 在进房前监听
     * trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, event => {
     *   const userId = event.userId;
     * });
     */
  REMOTE_AUDIO_AVAILABLE: 'remote-audio-available',
  /**
     * 远端停止发布了音频。当远端用户关闭麦克风后，您会收到该通知。
     *
     * @default 'remote-audio-unavailable'
     * @memberof module:EVENT
     * @example
     * // 在进房前监听
     * trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, event => {
     *   const userId = event.userId;
     *
     * });
     */
  REMOTE_AUDIO_UNAVAILABLE: 'remote-audio-unavailable',
  /**
     * 远端用户发布了视频，当远端用户开启摄像头后，您会收到该通知。参考：[开关摄像头、麦克风](./tutorial-15-basic-dynamic-add-video.html)
     *
     * - 可以监听该事件及 {@link module:EVENT.REMOTE_VIDEO_UNAVAILABLE REMOTE_VIDEO_UNAVAILABLE} 来更新“远端是否开启摄像头”的 UI icon。
     * @see {@link module:TYPE.STREAM_TYPE_MAIN STREAM_TYPE_MAIN}
     * @see {@link module:TYPE.STREAM_TYPE_SUB STREAM_TYPE_SUB}
     * @default 'remote-video-available'
     * @memberof module:EVENT
     * @example
     * // 在进房前监听
     * trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
     *   const userId = event.userId;
     *   const streamType = event.streamType;
     *   trtc.startRemoteVideo({userId, streamType, view});
     * });
     */
  REMOTE_VIDEO_AVAILABLE: 'remote-video-available',
  /**
     * 远端用户停止发布视频，当远端用户关闭摄像头后，您会收到该通知。
     * @default 'remote-video-unavailable'
     * @memberof module:EVENT
     * @example
     * // 在进房前监听
     * trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, event => {
     *   const userId = event.userId;
     *   const streamType = event.streamType;
     *   // 此时 SDK 会自动停止播放，无需调用 stopRemoteVideo。
     * });
     */
  REMOTE_VIDEO_UNAVAILABLE: 'remote-video-unavailable',
  /**
     * @description 音量大小事件<br>
     * 调用 {@link TRTC#enableAudioVolumeEvaluation enableAudioVolumeEvaluation} 接口开启音量大小回调后，SDK 会定时抛出该事件，通知每个 userId 的音量大小。<br>
     * **Note**
     * - 回调中包含本地麦克风音量及远端用户的音量，无论是否有人说话，都会触发该回调。
     * - 回调 event.result 会根据音量大小，按大到小进行排序。
     * - 当 userId 为空串时，代表本地麦克风音量。
     * - volume 取值为 0-100 的正整数
     * - floatVolume 取值为 0-1.0 的浮点数。自 v5.11.1+ 支持。当麦克风采集异常无声时，该 floatVolume 值是 0，若是用户没说话，floatVolume 是非 0 的浮点数。
     * @default 'audio-volume'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
     *    event.result.forEach(({ userId, volume, floatVolume }) => {
     *        const isMe = userId === ''; // 当 userId 为空串时，代表本地麦克风音量。
     *        if (isMe) {
     *            console.log(`my volume: ${volume}`);
     *        } else {
     *            console.log(`user: ${userId} volume: ${volume}`);
     *        }
     *    })
     * });
     *
     * // 开启音量回调，并设置每 1000ms 触发一次事件
     * trtc.enableAudioVolumeEvaluation(1000);
     */
  AUDIO_VOLUME: 'audio-volume',
  /**
   * @since v5.8.3
   * @description 获取麦克风的原始 PCM 数据，可用于语音识别场景。自 v5.11.0+ 支持回调远端用户的原始 PCM 数据<br>
   * **Note**
   * - 默认只回调麦克风的数据，如果需要订阅远端用户的音频，需要调用 trtc.callExperimentalAPI('enableAudioFrameEvent', options) 开启回调，详情请访问 {@link TRTC#callExperimentalAPI }
   * - 在开启麦克风或订阅远端用户的音频后，该事件每 40ms (float32 类型) 触发一次。
   * - PCM 数据为单声道, 48000Hz 采样率. 可通过 {@link TRTC#callExperimentalAPI } 指定其他声道数和采样率.
   * @default 'audio-frame'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.AUDIO_FRAME, event => {
   *   console.log(event.data) // pcm 数据, 如果是单声道，数据类型为 float32Array，如果是立体声，数据类型为 float32Array[].
   *   console.log(event.userId) // 自 v5.11.0, 如果 userId 为空字符串，则表示本地麦克风。其他则表示远端用户。
   *   console.log(event.channelCount) // 自 v5.11.0, 1 表示单声道，2 表示立体声。
   *   console.log(event.sampleRate) // 自 v5.11.0, pcm 数据的采样率。
   * });
   *
   */
  AUDIO_FRAME: 'audio-frame',
  /**
     * @description 网络质量统计数据事件，进房后开始统计，每两秒触发一次，该数据反映的是您本地的上、下行的网络质量。
     * - 上行网络质量（uplinkNetworkQuality）指的是您上传本地流的网络情况（SDK 到腾讯云的上行连接网络质量）
     * - 下行网络质量（downlinkNetworkQuality）指的是您下载所有流的平均网络情况（腾讯云到 SDK 的所有下行连接的平均网络质量）
     *
     *    其枚举值及含义如下表所示：
     *    | 数值 | 含义 |
     *    | :--- | :---- |
     *    | 0 | 网络状况未知，表示当前 trtc 实例还没有建立上行/下行连接 |
     *    | 1 | 网络状况极佳 |
     *    | 2 | 网络状况较好|
     *    | 3 | 网络状况一般 |
     *    | 4 | 网络状况差 |
     *    | 5 | 网络状况极差 |
     *    | 6 | 网络连接已断开<br/>注意：若下行网络质量为此值，则表示所有下行连接都断开了 |
     * - uplinkRTT，uplinkLoss 为上行 RTT(ms) 及上行丢包率。
     * - downlinkRTT，downlinkLoss 为所有下行连接的平均 RTT(ms) 及平均丢包率。
     *
     * **Note**
     * - 如果您想知道对方的上下行网络情况，需要把对方的网络质量情况通过 IM 广播出去。
     *
     * @default 'network-quality'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.NETWORK_QUALITY, event => {
     *    console.log(`network-quality, uplinkNetworkQuality:${event.uplinkNetworkQuality}, downlinkNetworkQuality: ${event.downlinkNetworkQuality}`)
     *    console.log(`uplink rtt:${event.uplinkRTT} loss:${event.uplinkLoss}`)
     *    console.log(`downlink rtt:${event.downlinkRTT} loss:${event.downlinkLoss}`)
     * })
     */
  NETWORK_QUALITY: 'network-quality',
  /**
     * @description SDK 和腾讯云的连接状态变更事件，您可以利用该事件从总体上监听 SDK 与腾讯云的连接状态。<br>
     * - 'DISCONNECTED'：连接断开
     * - 'CONNECTING'：正在连接中
     * - 'CONNECTED'：已连接
     *
     * 不同状态变更的含义：
     *
     * - DISCONNECTED -> CONNECTING: 正在尝试建立连接，调用进房接口或者 SDK 自动重连时触发。
     * - CONNECTING -> DISCONNECTED: 连接建立失败，当正在连接时调用退房接口中断连接或者经过 SDK 重试后任然连接失败时触发。
     * - CONNECTING -> CONNECTED: 连接建立成功，连接成功时触发。
     * - CONNECTED -> DISCONNECTED: 连接中断，调用退房接口或者当网络异常导致连接断开时触发。
     *
     * 处理建议：可以监听该事件，在不同状态显示不同的 UI，提醒用户当前的连接状态。
     *
     * @default 'connection-state-changed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.CONNECTION_STATE_CHANGED, event => {
     *   const prevState = event.prevState;
     *   const curState = event.state;
     * });
     */
  CONNECTION_STATE_CHANGED: 'connection-state-changed',
  /**
     * @description 音频播放状态变更事件
     *
     * event.userId 当 userId 为空串时，代表本地用户，非空串代表远端用户。
     *
     * event.state 取值如下：
     * - 'PLAYING'：开始播放
     *   - event.reason 为 'playing' 或者 'unmute'。
     * - 'PAUSED'：暂停播放
     *   - event.reason 为 'pause' 时，由 \<audio\> element 的 pause 事件触发，如下几种情况会触发：
     *      - 调用 HTMLMediaElement.pause 接口。
     *   - event.reason 为 'mute' 时。详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event | MediaStreamTrack.mute_event}
     *      - 若 userId 为自己时 触发该事件，表明音频采集暂停，通常是设备异常引起，如设备被其他应用抢占，此时需引导用户重新采集。
     *      - 若 userId 为他人时 触发该事件，表明收到的音频数据不足以播放。通常是网络抖动引起，接入侧无需做任何处理。当收到的数据足以播放时，会自动恢复。
     * - 'STOPPED'：停止播放
     *   - event.reason 为 'ended'。
     *
     * event.reason 状态变化的原因，取值如下：
     * - 'playing'：开始播放，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event | HTMLMediaElement.playing_event}
     * - 'mute'：音频轨道暂时未能提供数据，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event | MediaStreamTrack.mute_event}
     * - 'unmute'：音频轨道恢复提供数据，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event | MediaStreamTrack.unmute_event}
     * - 'ended'：音频轨道已被关闭
     * - 'pause'：播放暂停
     * @default 'audio-play-state-changed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.AUDIO_PLAY_STATE_CHANGED, event => {
     *   console.log(`${event.userId} player is ${event.state} because of ${event.reason}`);
     * });
     */
  AUDIO_PLAY_STATE_CHANGED: 'audio-play-state-changed',
  /**
     * @description 视频播放状态变更事件
     *
     * event.userId 当 userId 为空串时，代表本地用户，非空串代表远端用户。
     *
     * event.streamType 流类型，取值：{@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN} {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}
     *
     * event.state 取值如下：
     * - 'PLAYING'：开始播放
     *   - event.reason 为 'playing' 或者 'unmute'。
     * - 'PAUSED'：暂停播放
     *   - event.reason 为 'pause' 时，由 \<video\> element 的 pause 事件触发，如下几种情况会触发：
     *      - 调用 HTMLMediaElement.pause 接口。
     *      - 在播放成功后，从 DOM 中移除了播放视频的 view 容器。
     *   - event.reason 为 'mute' 时。详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event | MediaStreamTrack.mute_event}
     *      - 若 userId 为自己时 触发该事件，表明视频采集暂停，通常是设备异常引起，如设备被其他应用抢占，此时需引导用户重新采集。
     *      - 若 userId 为他人时 触发该事件，表明收到的视频数据不足以播放。通常是网络抖动引起，接入侧无需做任何处理。当收到的数据足以播放时，会自动恢复。
     * - 'STOPPED'：停止播放
     *   - event.reason 为 'ended'。
     *
     * event.reason 状态变化的原因，取值如下：
     * - 'playing'：开始播放，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event | HTMLMediaElement.playing_event}
     * - 'mute'：视频轨道暂时未能提供数据，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event | MediaStreamTrack.mute_event}
     * - 'unmute'：视频轨道恢复提供数据，详见事件 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event | MediaStreamTrack.unmute_event}
     * - 'ended'：视频轨道已被关闭
     * - 'pause'：播放暂停
     * @default 'video-play-state-changed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.VIDEO_PLAY_STATE_CHANGED, event => {
     *   console.log(`${event.userId} ${event.streamType} video player is ${event.state} because of ${event.reason}`);
     * });
     */
  VIDEO_PLAY_STATE_CHANGED: 'video-play-state-changed',
  /**
     * @description 本地屏幕分享停止事件通知，仅对本地屏幕分享流有效。
     * @default 'screen-share-stopped'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, () => {
     *   console.log('screen sharing was stopped');
     * });
     */
  SCREEN_SHARE_STOPPED: 'screen-share-stopped',
  /**
     * @description 摄像头、麦克风等设备变化的通知事件。
     * - event.device 是一个 [MediaDeviceInfo](https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo) 对象，属性：
     *    - deviceId：设备 Id
     *    - label：设备描述信息
     *    - groupId：设备 groupId
     * - event.type 值：`'camera'|'microphone'|'speaker'`
     * - event.action 值：
     *    - 'add' 设备已添加。
     *    - 'remove' 设备已被移除。
     *    - 'active' 设备已启动，例如：startLocalVideo 成功后，会触发该事件。
     * @default 'device-changed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
     *   console.log(`${event.type}(${event.device.label}) ${event.action}`);
     * });
     */
  DEVICE_CHANGED: 'device-changed',
  /**
     * @description 推流状态变更事件。
     * - event.mediaType 媒体类型，值：`'audio'|'video'|'screen'`。
     * - event.state 当前的推流状态，值：
     *    - `'starting'` 正在尝试推流
     *    - `'started'` 推流成功
     *    - `'stopped'` 推流停止，原因见 event.reason 字段
     * - event.prevState 上一次事件触发时的推流状态，值和 event.state 相同。
     * - event.reason 推流状态变为 `'stopped'` 的原因，值：
     *    - `'timeout'` 推流超时，一般是由于网络抖动、防火墙拦截导致。SDK 会不断进行重试，业务侧可以在此时引导用户检查网络、更换网络。
     *    - `'error'` 推流出错，此时可从 event.error 中获取到具体错误信息，一般是由于浏览器不支持 H264 编码导致。
     *    - `'api-call'` 业务侧 api 调用导致推流停止，例如在 startLocalVideo 推流成功前，调用了 stopLocalVideo 停止了推流，属于正常行为，业务侧无需关注。
     * - event.error event.reason 为 `'error'` 时的错误信息。
     * @default 'publish-state-changed'
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
     *   console.log(`${event.mediaType} ${event.state} ${event.reason}`);
     * });
     */
  PUBLISH_STATE_CHANGED: 'publish-state-changed',
  /**
   * @since v5.3.0
   * @description 收到新的 MediaStreamTrack 对象
   * @default 'track'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.TRACK, event => {
   *   const isLocal = event.userId === ''; // userId === '' means event.track is a local track, otherwise it's a remote track
   *   const isSubStream = event.streamType === TRTC.TYPE.STREAM_TYPE_SUB; // Usually the sub stream is a screen-sharing video stream.
   *   const mediaStreamTrack = event.track;
   *   const kind = event.track.kind; // audio or video
   * })
   */
  TRACK: 'track',
  /**
     * @description 通话相关数据指标。<br>
     *
     * - 监听该事件后，SDK 会以 2s 一次的频率定时抛出该事件。
     * - 您可以从该事件中可以获取通话的网络质量（rtt、丢包率）、上行和下行的音视频统计信息（音量大小、宽高、帧率、码率）等信息。详细参数说明请参考：{@link TRTCStatistics}
     * @default 'statistics'
     * @since v5.2.0
     * @memberof module:EVENT
     * @example
     * trtc.on(TRTC.EVENT.STATISTICS, statistics => {
     *    console.warn(statistics.rtt, statistics.upLoss, statistics.downLoss);
     * })
     */
  STATISTICS: 'statistics',
  /**
   * @since v5.3.0
   * @description 收到 SEI message<br>
   * @default 'sei-message'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
   *    console.log(`收到 ${event.userId} 的 sei message: ${event.data}`)
   * })
   */
  SEI_MESSAGE: 'sei-message',
  /**
   * @since v5.6.0
   * @description 收到自定义消息
   * @default 'custom-message'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, event => {
   *    // event.userId: 远端发消息的 userId
   *    // event.cmdId: 您自定义的消息 Id
   *    // event.seq: 消息的序号
   *    // event.data: 消息内容，ArrayBuffer 类型
   * })
   */
  CUSTOM_MESSAGE: 'custom-message',
  /**
   * @private
   * @since v5.9.0
   * @description 视频解码降级事件。
   * @default 'video-decode-downgrade-state-changed'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.VIDEO_DECODE_DOWNGRADE_STATE_CHANGED, event => {
   *    // event.reason: 降级原因
   *    // event.state: STARTING | STARTED | FAILED.
   *    // event.preState: STARTING | STARTED.
   *    // event.streamType: TRTCStreamType.Main | TRTCStreamType.Sub.
   *    // event.userId: 用户 ID
   *    // event.type: 'webCodecs' | 'wasm'.
   *    // event.renderer: 'webgl' | '2d' | 'videoFrame'.
   * })
   */
  VIDEO_DECODE_DOWNGRADE_STATE_CHANGED: 'video-decode-downgrade-state-changed',
  /**
   * @since v5.9.0
   * @description 本地视频或者远端视频首帧渲染事件。
   * @default 'first-video-frame'
   * @memberof module:EVENT
   * @example
   * trtc.on(TRTC.EVENT.FIRST_VIDEO_FRAME, event => {
   *    // event.height: 视频高度
   *    // event.width: 视频宽度
   *    // event.streamType: 视频流类型.
   *    // event.userId: 用户 ID，若是空串，则代表是本地视频。
   * })
   */
  FIRST_VIDEO_FRAME: 'first-video-frame'
};
