import { createDecorator } from 'trtc-js-sdk-core/src/plugin';
import { doValidate } from '../common/validate';
export const validatePlugin = (method) => createDecorator((next, fnName) => async function (name, options) {
    const plugin = this._plugins.get(name);
    if (!plugin) {
        this._log.warn(`plugin ${name} is not found`);
        return;
    }
    doValidate.call(this, plugin.getValidateRule(method), [options], fnName, 'TRTC');
    return next.call(this, plugin, options);
});
