import { createDecorator } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { AbortError, RandContext, RandState } from 'trtc-js-sdk-core/src/utils/decorators/random-call';
import RtcError from '../common/rtc-error';
import { ErrorCode } from '../common/error-code';
const debounceMap = new WeakMap();
export const throwAbort = (fnName, e) => {
    if (e instanceof AbortError) {
        const { stack } = e;
        e = new RtcError({
            code: ErrorCode.OPERATION_ABORT,
            message: `${fnName} abort: ${e.message}`,
            fnName
        });
        if (stack)
            e.stack += stack.substr(stack.indexOf('\n'));
    }
    throw e;
};
export function randStart(group, isSame) {
    return createDecorator((next, fnName) => function (...args) {
        const ctx = RandContext.get(this, typeof group === 'string' ? group : group.call(this, ...args));
        if (isSame)
            ctx.startSame = isSame.bind(this);
        return ctx.action(RandState.Start, next.bind(this), args).catch(throwAbort.bind(null, fnName));
    });
}
export function randUpdate(group, option) {
    const { merge, debounce } = option || {};
    return createDecorator((next, fnName) => function (...args) {
        const ctx = RandContext.get(this, typeof group === 'string' ? group : group.call(this, ...args));
        if (merge)
            ctx.mergeUpdate = merge.bind(this);
        // 防抖
        if (debounce && debounce.isNeedToDebounce.apply(this, args)) {
            const { delay, getKey } = debounce;
            return new Promise((resolve, reject) => {
                const data = debounceMap.get(this)?.get(getKey(...args));
                if (data) {
                    const { timeoutId: prevTimeoutId, resolve: prevResolve } = data;
                    clearTimeout(prevTimeoutId);
                    prevResolve();
                }
                const timeoutId = setTimeout(() => {
                    if (ctx.state === RandState.Stop || ctx.state === RandState.Stopped)
                        return resolve();
                    ctx.action(RandState.Update, next.bind(this), args)
                        .catch(throwAbort.bind(null, fnName))
                        .then(resolve, reject);
                }, delay);
                if (!debounceMap.has(this)) {
                    debounceMap.set(this, new Map([[getKey(...args), { timeoutId, resolve }]]));
                }
                else {
                    debounceMap.get(this)?.set(getKey(...args), { timeoutId, resolve });
                }
            });
        }
        return ctx.action(RandState.Update, next.bind(this), args).catch(throwAbort.bind(null, fnName));
    });
}
export function randStop(group) {
    return createDecorator((next, fnName) => function (...args) {
        const ctx = RandContext.get(this, typeof group === 'string' ? group : group.call(this, ...args));
        return ctx.action(RandState.Stop, next.bind(this), args).catch(throwAbort.bind(null, fnName));
    });
}
// export function randClearStarted(group: string | ((...arg: any) => string)) {
//   return createDecorator((next: ProcessHandler, fnName: string) => function (this: object, ...args: unknown[]) {
//     const ctx = RandContext.get(this, typeof group === 'string' ? group : group(...args));
//     ctx.started = false;
//     return next.call(this, ...args);
//   });
// }
