import { createDecorator } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { getValueType, isPromise, performanceNow } from 'trtc-js-sdk-core/src/utils/utils';
import RtcError from '../common/rtc-error';
import innerEmitter from 'trtc-js-sdk-core/src/inner-emitter/inner-emitter';
import INNER_EVENT from 'trtc-js-sdk-core/src/inner-emitter/inner-event';
/**
 * 给 API 添加调用日志
 * @example
 * class TRTC {
 *  // 异步方法，在 startLocalVideo 开始、成功、失败时间点打印日志
 *  @addAPICallLog
 *  async startLocalVideo() {}
 *  // 同步方法，只在调用时打印日志
 *  @addAPICallLog
 *  stopLocalVideo() {}
 * }
 */
export function addAPICallLog(options = {}) {
    const { namePrefix = () => '', getRemoteId = () => '' } = options;
    return createDecorator((next, originName) => function (...args) {
        function stringifyParam(key, value, keysToHideValue) {
            if (keysToHideValue && keysToHideValue.includes(key))
                return 'hided';
            if (value === args || key in args)
                return value;
            try {
                // 输出参数时，对于 HTMLElement 类型的参数，输出 elementId + element 类型
                if (value instanceof HTMLElement) {
                    return `id: ${value.id} type:${getValueType(value)}`;
                }
                JSON.stringify(value);
                return value;
            }
            catch (error) {
                // 不可序列化参数返回参数类型，避免报错导致接口调用失败。例如循环引用对象无法序列化
                return `type:${getValueType(value)}`;
            }
        }
        // @ts-ignore
        const log = this._log || loggerManager;
        const namePrefixResult = namePrefix.call(this, ...args);
        const isPluginMethod = namePrefixResult !== '';
        const name = isPluginMethod ? `${namePrefixResult}.${originName}` : originName;
        if (args.length > 0) {
            // 打印参数时，隐藏部分敏感、过长的参数，当失败时，再完整打印。减少日志量。
            log.info(`${name}() ${JSON.stringify(args, (key, value) => stringifyParam(key, value, ['userSig', 'privateMapKey']))}`);
        }
        else {
            log.info(`${name}()`);
        }
        try {
            const res = next.apply(this, args);
            const now = performanceNow();
            if (isPromise(res)) {
                return res.then(val => {
                    log.info(`${name}() success ${getRemoteId.call(this, ...args)}`);
                    innerEmitter.emit(INNER_EVENT.API_SUCCESS_RATE, ({ room: this._room, apiName: name, cost: performanceNow() - now }));
                    // eventLog.logSuccessEvent({ userId: this._room.userId, eventType: name });
                    return val;
                }).catch(error => {
                    error = RtcError.convertFrom.call(this, error, name, args.length === 1 ? args[0] : args);
                    log.error(`${name}() failed ${getRemoteId.call(this, ...args)} ${error} params: ${JSON.stringify(args, stringifyParam)}`);
                    innerEmitter.emit(INNER_EVENT.API_SUCCESS_RATE, ({ room: this._room, apiName: name, error }));
                    throw error;
                });
            }
            innerEmitter.emit(INNER_EVENT.API_SUCCESS_RATE, ({ room: this._room, apiName: name, cost: performanceNow() - now }));
            // eventLog.logSuccessEvent({ userId: this._room.userId, eventType: name });
            return res;
        }
        catch (error) {
            error = RtcError.convertFrom.call(this, error, name);
            // 失败时打印完整参数
            log.error(`${name}() failed ${error} params: ${JSON.stringify(args, stringifyParam)}`);
            innerEmitter.emit(INNER_EVENT.API_SUCCESS_RATE, ({ room: this._room, apiName: name, error: error }));
            throw error;
        }
    });
}
