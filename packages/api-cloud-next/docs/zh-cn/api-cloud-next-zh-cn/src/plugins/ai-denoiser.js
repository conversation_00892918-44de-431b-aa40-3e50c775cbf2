import { BASIC_TYPE } from 'trtc-js-sdk-core/src/enums';
import { ErrorCode, RtcError } from 'trtc-js-sdk-core';
let adSeq = 0;
class AIDenoiser {
    core;
    log;
    static startValidateRule = {
        name: 'options',
        required: true,
        type: BASIC_TYPE.Object,
        properties: {
            assetsPath: { type: BASIC_TYPE.String, required: true },
            sdkAppId: { type: BASIC_TYPE.Number, required: true },
            userId: { type: BASIC_TYPE.String, required: true },
            userSig: { type: BASIC_TYPE.String, required: true }
        }
    };
    static updateValidateRule = {
        type: BASIC_TYPE.Object
    };
    static stopValidateRule = {
        type: BASIC_TYPE.Object
    };
    constructor(core) {
        this.core = core;
        adSeq = adSeq + 1;
        this.log = core.log.createChild({ id: `${this.getAlias()}${adSeq}` });
        this.log.info(`[audioDenoiser] created id=${this.getAlias()}${adSeq}`);
    }
    getName() {
        return 'AIDenoiser';
    }
    getAlias() {
        return 'ad';
    }
    // 本地只允许一次
    getGroup(options) {
        const timeStamp = Date.now();
        return `AIDenoiser_${timeStamp}`;
    }
    getValidateRule(method) {
        switch (method) {
            case 'start':
                return AIDenoiser.startValidateRule;
            case 'update':
                return AIDenoiser.updateValidateRule;
            case 'stop':
                return AIDenoiser.stopValidateRule;
        }
    }
    async start(options) {
        const { room } = this.core;
        const { assetsPath, sdkAppId, userId, userSig } = options;
        if (assetsPath && !room.audioManager.isDenoiserInit) {
            try {
                await room.audioManager.initDenoiser({
                    assetsPath,
                    sdkAppId,
                    userId,
                    userSig
                });
            }
            catch (error) {
                if (error.message) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        message: error.message
                    });
                }
            }
        }
        await room.audioManager.enableDenoiser(options);
        room.sendAbilityStatus({ ai_denoise: 1 });
        // if (room.isJoined) {
        //   for (const item of room.localTracks) {
        //     if (item instanceof LocalAudioTrack) {
        //       await room.replaceTrack(item);
        //     }
        //   }
        // }
    }
    async update(options) { }
    async stop(options) {
        const { room } = this.core;
        await room.audioManager.disableDenoiser();
    }
}
export { AIDenoiser };
