import { BASIC_TYPE } from 'trtc-js-sdk-core/src/enums';
import RtcError from '../common/rtc-error';
import { ErrorCode } from '../common/error-code';
let axSeq = 0;
class AudioMixer {
    core;
    log;
    static startValidateRule = {
        name: 'options',
        required: true,
        type: BASIC_TYPE.Object,
        properties: {
            id: { type: BASIC_TYPE.String, required: true },
            url: { type: BASIC_TYPE.String, required: true },
            loop: { type: BASIC_TYPE.Boolean },
            volume: { type: BASIC_TYPE.Number }
        },
        validate(config, key, fnName) {
            if (config.url !== '*') {
                const fileType = ['mp3', 'ogg', 'wav', 'flac'];
                const type = config.url.split('.').pop();
                const isMusic = fileType.indexOf(type) >= 0;
                const isBlob = config.url.startsWith('blob');
                const isData = config.url.startsWith('data');
                if (!(isMusic || isBlob || isData)) {
                    throw new RtcError({
                        code: ErrorCode.INVALID_PARAMETER,
                        message: 'start audioMixer plugin: music url is invalid, please check your file format.',
                        fnName
                    });
                }
            }
        }
    };
    static updateValidateRule = {
        name: 'options',
        required: true,
        type: BASIC_TYPE.Object,
        properties: {
            id: { type: BASIC_TYPE.String, required: true },
            loop: { type: BASIC_TYPE.Boolean },
            volume: { type: BASIC_TYPE.Number },
            seekFrom: { type: BASIC_TYPE.Number },
            operation: { type: BASIC_TYPE.String, values: ['pause', 'resume', 'stop'] }
        }
    };
    static stopValidateRule = {
        name: 'options',
        type: BASIC_TYPE.Object,
        required: true,
        properties: {
            id: { type: BASIC_TYPE.String, required: true }
        }
    };
    constructor(core) {
        this.core = core;
        axSeq = axSeq + 1;
        this.log = core.log.createChild({ id: `${this.getAlias()}${axSeq}` });
        this.log.info(`[audioMixer] created id=${this.getAlias()}${axSeq}`);
        this.core = core;
    }
    getName() {
        return 'AudioMixer';
    }
    getAlias() {
        return 'ax';
    }
    getGroup(options) {
        return options?.id;
    }
    getValidateRule(method) {
        switch (method) {
            case 'start':
                return AudioMixer.startValidateRule;
            case 'update':
                return AudioMixer.updateValidateRule;
            case 'stop':
                return AudioMixer.stopValidateRule;
        }
    }
    async start(options) {
        const { room } = this.core;
        await room.audioManager.addMusicSource(options);
        // 由 setOutputMediaStreamTrack 统一处理
        // if (room.isJoined) {
        //   for (const item of room.localTracks) {
        //     if (item instanceof LocalAudioTrack) {
        //       await room.replaceTrack(item);
        //     }
        //   }
        // }
    }
    async update(options) {
        const { room } = this.core;
        await room.audioManager.updateMusicSource(options);
    }
    async stop(options) {
        const { room } = this.core;
        await room.audioManager.removeMusicSource(options);
    }
}
export { AudioMixer };
