import { TRTCType } from './type';
import { RemoteStreamType } from 'trtc-js-sdk-core';
export function convertStreamType(streamType) {
    if (streamType === "sub" /* TRTCStreamType.Sub */) {
        // @ts-ignore
        return RemoteStreamType.Aux;
    }
    if (streamType === RemoteStreamType.Aux) {
        // @ts-ignore
        return "sub" /* TRTCStreamType.Sub */;
    }
    // @ts-ignore
    return RemoteStreamType.Main;
}
export function getContentHintFromQosPreference(qosPreference) {
    if (qosPreference === TRTCType.QOS_PREFERENCE_CLEAR)
        return 'detail';
    if (qosPreference === TRTCType.QOS_PREFERENCE_SMOOTH)
        return 'motion';
    return '';
}
