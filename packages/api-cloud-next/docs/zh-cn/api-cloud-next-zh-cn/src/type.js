import { Scene, UserRole } from 'trtc-js-sdk-core';
export var TRTCDeviceType;
(function (TRTCDeviceType) {
    TRTCDeviceType["Camera"] = "camera";
    TRTCDeviceType["Microphone"] = "microphone";
    TRTCDeviceType["Speaker"] = "speaker";
})(TRTCDeviceType || (TRTCDeviceType = {}));
export var TRTCDeviceAction;
(function (TRTCDeviceAction) {
    TRTCDeviceAction["Remove"] = "remove";
    TRTCDeviceAction["Add"] = "add";
    TRTCDeviceAction["Active"] = "active";
})(TRTCDeviceAction || (TRTCDeviceAction = {}));
/**
 * **TRTC 常量**<br>
 * @module TYPE
 * @example
 * // 使用方式：
 * TRTC.TYPE.SCENE_LIVE
 */
export const TRTCType = {
    /**
     * 直播场景
     * @default 'live'
     * @memberof module:TYPE
     */
    SCENE_LIVE: Scene.LIVE,
    /**
     * 通话场景
     * @default 'rtc'
     * @memberof module:TYPE
    */
    SCENE_RTC: Scene.RTC,
    /**
     * 主播角色
     * @default 'anchor'
     * @memberof module:TYPE
     */
    ROLE_ANCHOR: UserRole.ANCHOR,
    /**
     * 观众角色
     * @default 'audience'
     * @memberof module:TYPE
     */
    ROLE_AUDIENCE: UserRole.AUDIENCE,
    /**
     * 主流
     *
     * - TRTC 有主路视频流（主流）和辅路视频流（辅流）
     * - 摄像头通过主流发布，屏幕分享通过辅流发布。
     * - 主路视频流包括：高清大画面和低清小画面两种，默认情况下，{@link TRTC#startRemoteVideo TRTC.startRemoteVideo} 播放的是高清大画面，可以通过 small 参数播放低清小画面，参考：[开启大小流功能](./tutorial-27-advanced-small-stream.html)。
     * @default 'main'
     * @memberof module:TYPE
     */
    STREAM_TYPE_MAIN: "main" /* TRTCStreamType.Main */,
    /**
     * 辅流
     * @default 'sub'
     * @memberof module:TYPE
     */
    STREAM_TYPE_SUB: "sub" /* TRTCStreamType.Sub */,
    /**
     * 标准音质
    * | 音频 Profile | 采样率 | 声道 | 码率 (kbps) |
    * | :---        | :---  | :--- | :---       |
    * | TRTC.TYPE.AUDIO_PROFILE_STANDARD    | 48000 | 单声道| 40         |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH        | 48000 | 单声道| 128        |
    * | TRTC.TYPE.AUDIO_PROFILE_STANDARD_STEREO | 48000 | 双声道| 64        |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH_STEREO | 48000 | 双声道| 192        |
    * @default 'standard'
    * @memberof module:TYPE
    */
    AUDIO_PROFILE_STANDARD: 'standard',
    /**
     * 标准音质立体声
   * | 音频 Profile | 采样率 | 声道 | 码率 (kbps) |
    * | :---        | :---  | :--- | :---       |
    * | TRTC.TYPE.AUDIO_PROFILE_STANDARD    | 48000 | 单声道| 40         |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH        | 48000 | 单声道| 128        |
    * | TRTC.TYPE.AUDIO_PROFILE_STANDARD_STEREO | 48000 | 双声道| 64        |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH_STEREO | 48000 | 双声道| 192        |
    * @default 'standard-stereo'
    * @memberof module:TYPE
    */
    AUDIO_PROFILE_STANDARD_STEREO: 'standard-stereo',
    /**
     * 高音质
     * | 音频 Profile | 采样率 | 声道 | 码率 (kbps) |
     * | :---        | :---  | :--- | :---       |
     * | TRTC.TYPE.AUDIO_PROFILE_STANDARD    | 48000 | 单声道| 40         |
     * | TRTC.TYPE.AUDIO_PROFILE_HIGH        | 48000 | 单声道| 128        |
     * | TRTC.TYPE.AUDIO_PROFILE_STANDARD_STEREO | 48000 | 双声道| 64        |
     * | TRTC.TYPE.AUDIO_PROFILE_HIGH_STEREO | 48000 | 双声道| 192        |
    * @default 'high'
    * @memberof module:TYPE
    */
    AUDIO_PROFILE_HIGH: 'high',
    /**
     * 高音质立体声
     * | 音频 Profile | 采样率 | 声道 | 码率 (kbps) |
     * | :---        | :---  | :--- | :---       |
     * | TRTC.TYPE.AUDIO_PROFILE_STANDARD    | 48000 | 单声道| 40         |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH        | 48000 | 单声道| 128        |
    * | TRTC.TYPE.AUDIO_PROFILE_STANDARD_STEREO | 48000 | 双声道| 64        |
    * | TRTC.TYPE.AUDIO_PROFILE_HIGH_STEREO | 48000 | 双声道| 192        |
    * @default 'high-stereo'
    * @memberof module:TYPE
    */
    AUDIO_PROFILE_HIGH_STEREO: 'high-stereo',
    /**
     * 弱网时，视频编码策略以流畅度优先，即优先保帧率。
     * 摄像头默认流畅度优先，屏幕分享默认清晰度优先。
    * @default 'smooth'
    * @memberof module:TYPE
    */
    QOS_PREFERENCE_SMOOTH: 'smooth',
    /**
     * 弱网时，视频编码策略以清晰度优先，即优先保分辨率。
     * 摄像头默认流畅度优先，屏幕分享默认清晰度优先。
    * @default 'clear'
    * @memberof module:TYPE
    */
    QOS_PREFERENCE_CLEAR: 'clear'
};
export var TRTCVideoType;
(function (TRTCVideoType) {
    TRTCVideoType["Big"] = "big";
    TRTCVideoType["Small"] = "small";
    TRTCVideoType["Sub"] = "sub";
})(TRTCVideoType || (TRTCVideoType = {}));
