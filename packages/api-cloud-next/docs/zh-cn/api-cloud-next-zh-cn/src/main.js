import { EventEmitter } from 'eventemitter3';
import { AUDIO_LEVEL_SCALE, ErrorCode as CoreErrorCode, DeviceChangeEvent, LocalAudioTrack, LocalScreenAudioTrack, LocalScreenTrack, LocalTrackEvent, LocalVideoTrack, MediaType, NAME, RoomEvent, SPEAKER_DEFAULT, Scene, TrackEvent, UserRole, UserRoleNumber, audioProfileMap, deviceDetector, getCameras, getMicrophones, getSpeakers, loggerManager, screenProfileMap, setVersion, videoProfileMap } from 'trtc-js-sdk-core';
import { checkSystemRequirementsInternal, isSmallStreamSupported } from 'trtc-js-sdk-core/src/common/rtc-detection';
import * as eventManager from 'trtc-js-sdk-core/src/manager/event-manager';
import { limitCallFrequency } from 'trtc-js-sdk-core/src/utils/decorators/limit-call-frequency';
import { createDecorator } from 'trtc-js-sdk-core/src/utils/decorators/middleware';
import { introInfo } from 'trtc-js-sdk-core/src/utils/intro';
import { deepMerge, getContainerFromElement, isArray, isBoolean, isEmpty, isNumber, isString, isUndefined } from 'trtc-js-sdk-core/src/utils/utils';
import validateConfig from './common/validate-config';
import { randStart, randStop, randUpdate } from './decorator/random-call';
import { TRTCEvent } from './trtc-event';
import { TRTCDeviceAction, TRTCDeviceType, TRTCType, TRTCVideoType } from './type';
import { convertStreamType, getContentHintFromQosPreference } from './utils';
import { createCore } from 'trtc-js-sdk-core/src/plugin';
import { catchEmitterError } from 'trtc-js-sdk-core/src/utils/catch-emitter';
import { debounce } from 'trtc-js-sdk-core/src/utils/decorators/debounce';
import { version } from '../package.json';
import { ErrorCode, ErrorCodeDictionary } from './common/error-code';
import RtcError from './common/rtc-error';
import { validate, validateSync } from './common/validate';
import { addAPICallLog } from './decorator/add-api-call-log';
import { validatePlugin } from './decorator/validate-plugin';
import { AudioMixer } from './plugins/audio-mixer';
import { AIDenoiser } from './plugins/ai-denoiser';
let seq = 0;
const trtcInstanceSet = new Set();
// 当前使用的 speaker
let lastSpeakerDeviceInfo = null;
setVersion(version);
/**
 * TRTC对象 通过 {@link TRTC.create TRTC.create()} 创建，提供实时音视频的核心能力:<br>
 * - 进入一个音视频房间 {@link TRTC#enterRoom enterRoom()}
 * - 退出当前音视频房间 {@link TRTC#exitRoom exitRoom()}
 * - 预览/发布 本地视频 {@link TRTC#startLocalVideo startLocalVideo()}
 * - 采集/发布 本地音频 {@link TRTC#startLocalAudio startLocalAudio()}
 * - 取消预览/发布 本地视频 {@link TRTC#stopLocalVideo stopLocalVideo()}
 * - 取消采集/发布 本地音频 {@link TRTC#stopLocalAudio stopLocalAudio()}
 * - 观看远端视频 {@link TRTC#startRemoteVideo startRemoteVideo()}
 * - 取消观看远端视频 {@link TRTC#stopRemoteVideo stopRemoteVideo()}
 * - 静默/取消静默 远端音频 {@link TRTC#muteRemoteAudio muteRemoteAudio()}
 *
 * TRTC 生命周期如图所示：<br/>
 *
 * <img src="./assets/client-life-cycle.png" width="600"/>
 *
 * @class TRTC
 */
class TRTC extends EventEmitter {
  _room;
  _eventListened = new Set();
  _localVideoTrack = null;
  _localAudioTrack = null;
  _localScreenTrack = null;
  _localScreenAudioTrack = null;
  _localVideoConfig = null;
  _localScreenConfig = null;
  _localAudioConfig = null;
  _remoteVideoConfigMap = new Map();
  _remoteAudioConfigMap = new Map();
  _remoteAudioMuteMap = new Map();
  _log = loggerManager.createLogger({ id: `t${++seq}` });
  static _loggerManager = loggerManager;
  _plugins = new Map();
  _networkQuality = null;
  /**
     * 创建一个 TRTC 对象，用于实现进房、预览、推流、拉流等功能。<br>
     *
     * **注意：**
     * - 您必须先创建 TRTC 对象，通过调用此对象方法和监听此对象事件才能实现业务所需要的各种功能。
     * @param {Array=} options.plugins 注册插件的列表（选填）。
     * @param {boolean=} [options.enableSEI=false] 是否开启 SEI 收发功能（选填）。[参考文档](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#sendSEIMessage)
     * @param {string=} options.assetsPath 插件依赖静态资源文件的地址（选填）。
     * - 将 node_modules/trtc-sdk-v5/assets 目录发布至 CDN 或者静态资源服务器中。
     * - 设置 assetsPath 参数为 CDN 地址，例如： `TRTC.create({ assetsPath: 'https://xxx/assets' })`，SDK 会按需加载相关资源文件。
     * @param {string=} options.userDefineRecordId 用于设置云端录制的 userDefineRecordId(选填）。
     * - 【推荐取值】限制长度为64字节，只允许包含大小写英文字母（a-zA-Z）、数字（0-9）及下划线和连词符。
     * - 【参考文档】[云端录制](https://cloud.tencent.com/document/product/647/16823)。
     * @example
     * // 创建trtc对象
     * const trtc = TRTC.create();
     *
     * @returns {TRTC} trtc对象
     */
  // @ts-ignore
  static create(options) {
  }
    @validate(validateConfig.TRTC.create)
  static _create(RoomClass, options) {
    introInfo();
    const trtc = new TRTC(RoomClass, options || {});
    trtcInstanceSet.add(trtc);
    if (lastSpeakerDeviceInfo) {
      trtc.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: lastSpeakerDeviceInfo });
    } else {
      // 初始化默认扬声器
      getSpeakers().then(speakers => {
        if (speakers[0]) {
          lastSpeakerDeviceInfo = speakers[0];
          trtc.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: speakers[0] });
        }
      });
    }
    return trtc;
  }
    constructor(RoomClass, options) {
      super();
      this._room = new RoomClass({
        logger: this._log,
        frameWorkType: TRTC.frameWorkType,
        ...options
      });
      if (options.plugins) {
        options.plugins.forEach(Plugin => {
          const plugin = new Plugin(createCore(this._room));
          this._plugins.set(plugin.getName(), plugin);
        });
      }
      const audioMixer = new AudioMixer(createCore(this._room));
      this._plugins.set(audioMixer.getName(), audioMixer);
      const denoiser = new AIDenoiser(createCore(this._room));
      this._plugins.set(denoiser.getName(), denoiser);
      this._room.on(RoomEvent.AUDIO_VOLUME, result => {
        if (!result.find(item => item.userId === '') && this._localAudioTrack) {
          result.push({ userId: '', volume: Math.floor(this._localAudioTrack.getAudioLevel() * 100) });
        }
        this.emit(TRTCEvent.AUDIO_VOLUME, { result: result.sort((a, b) => b.volume - a.volume) });
      });
      this._room.videoManager.on('error', ({ reason, error }) => {
        this._log.error(new RtcError({
          code: ErrorCode.OPERATION_FAILED,
          extraCode: ErrorCodeDictionary.VIDEO_CONTEXT_ERROR,
          messageParams: {
            reason,
            error
          }
        }));
      });
      this.on(TRTCEvent.REMOTE_AUDIO_UNAVAILABLE, ({ userId }) => {
        // SDK 自动清理相关状态，让业务侧在后续 available 时可以 startRemoteAudio
        // 无需调用 unsubscribe 接口
        this._stopRemoteAudio({ userId }, false).catch(() => { });
      });
      this.on(TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, ({ userId, streamType }) => {
        // SDK 自动清理相关状态，让业务侧在后续 available 时可以 startRemoteVideo
        // 无需调用 unsubscribe 接口
        this._stopRemoteVideo({ userId, streamType }, false).catch(() => { });
      });
      eventManager.create(deviceDetector, deviceDetector)
        .add(DeviceChangeEvent.AUDIO_INPUT_ADDED, device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Add, device });
        })
        .add(DeviceChangeEvent.AUDIO_INPUT_REMOVED, device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Remove, device });
        })
        .add(DeviceChangeEvent.VIDEO_INPUT_ADDED, device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Add, device });
        })
        .add(DeviceChangeEvent.VIDEO_INPUT_REMOVED, device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Remove, device });
        })
        .add(DeviceChangeEvent.AUDIO_OUTPUT_ADDED, async device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Add, device });
          // 当前使用的是 default 扬声器，插入新的扬声器变成了 default，则抛出 active 事件。
          if (lastSpeakerDeviceInfo && lastSpeakerDeviceInfo.deviceId === SPEAKER_DEFAULT) {
            const defaultSpeaker = (await getSpeakers()).find(speaker => speaker.deviceId === SPEAKER_DEFAULT);
            if (defaultSpeaker && lastSpeakerDeviceInfo.groupId !== defaultSpeaker.groupId) {
              lastSpeakerDeviceInfo = defaultSpeaker;
              this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: defaultSpeaker });
            }
          }
        })
        .add(DeviceChangeEvent.AUDIO_OUTPUT_REMOVED, async device => {
          this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Remove, device });
          // 当前使用的扬声器被移除，默认认为扬声器列表第一个是 active 扬声器
          const firstSpeaker = (await getSpeakers())[0];
          if (firstSpeaker && lastSpeakerDeviceInfo) {
            if (lastSpeakerDeviceInfo.deviceId === device.deviceId ||
                    (lastSpeakerDeviceInfo.deviceId === SPEAKER_DEFAULT && lastSpeakerDeviceInfo.groupId !== firstSpeaker.groupId)) {
              lastSpeakerDeviceInfo = firstSpeaker;
              this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: firstSpeaker });
            }
          }
        });
      // @ts-ignore
      // 捕获业务侧的事件回调代码报错，避免影响 SDK 内部逻辑
      catchEmitterError(this, 'trtc');
    }
    /**
     * @typedef TurnServer
     * @property {string} url TURN 服务器 url
     * @property {string=} username TURN 服务器验证用户名
     * @property {string=} credential TURN 服务器验证密码
     * @property {string=} [credentialType=password] TURN 服务器验证密码类型
     */
    /**
     * @typedef ProxyServer
     * @property {string} [websocketProxy] websocket 信令服务代理
     * @property {string} [loggerProxy] 日志上报服务代理
     * @property {TurnServer[]} [turnServer] 音视频数据传输代理
     * @property {'all'|'relay'} [iceTransportPolicy='all'] 'all' 优先直连 TRTC，连不通时尝试走 turn server。<br>
     * 'relay' 强制走 turn server。
     */
    // 自动播放弹框配置
    // 自动订阅配置
    // 代理配置
    // 更新 this._log 的 userId 和 sdkappid
    /**
     * 进入一个音视频通话房间（以下简称"进房"）。<br>
     * - 进房代表开始一个音视频通话会话，只有进房成功后才能和房间内的其他用户进行音视频通话。
     * - 可以通过 {@link TRTC#startLocalVideo startLocalVideo()} 和  {@link TRTC#startLocalAudio startLocalAudio()}发布本地音视频流，发布成功后，房间内其他用户会收到
     * {@link module:EVENT.REMOTE_AUDIO_AVAILABLE REMOTE_AUDIO_AVAILABLE} 和 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} 事件通知。
     * - 默认情况下 SDK 会自动播放远端音频，您需要在调用 {@link TRTC#startRemoteVideo startRemoteVideo()} 来播放远端视频画面。
     *
     * @param {object} options 进房参数
     * @param {number} options.sdkAppId sdkAppId <br>
     * 在 [实时音视频控制台](https://console.cloud.tencent.com/trtc) 单击 **应用管理** > **创建应用** 创建新应用之后，即可在 **应用信息** 中获取 sdkAppId 信息。
     * @param {string} options.userId 用户ID <br>
     * 建议限制长度为32字节，只允许包含大小写英文字母(a-zA-Z)、数字(0-9)及下划线和连词符。
     * @param {string} options.userSig userSig 签名 <br>
     * 计算 userSig 的方式请参考 [UserSig 相关](https://cloud.tencent.com/document/product/647/17275)。
     * @param {number=} options.roomId
     * 数字类型的房间号，取值要求为 [1, 4294967294] 的整数;<br>
     * <font color="red">如果需要使用字符串类型的房间号请使用 strRoomId 参数，roomId 和 strRoomId 必须填一个。若两者都填，则优先选择 roomId。</font>
    * @param {string=} options.strRoomId
    * 字符串类型的房间号，限制长度为64字节，且仅支持以下范围的字符集：
     * - 大小写英文字母（a-zA-Z）
     * - 数字（0-9）
     * - 空格 ! # $ % & ( ) + - : ; < = . > ? @ [ ] ^ _ { } | ~ ,
      *
      * <font color="red">注意：建议采用数字类型的 roomId，字符串类型的房间号 "123" 与 数字类型的房间号 123 不互通。</font>
    * @param {string} [options.scene] 应用场景，目前支持以下两种场景：
      * - {@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC}（默认）实时通话场景，该模式适合 1对1 的音视频通话，或者参会人数在 300 人以内的在线会议。[支持最大50人同时开麦](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-04-info-uplink-limits.html)。
      * - {@link module:TYPE.SCENE_LIVE TRTC.TYPE.SCENE_LIVE} 互动直播场景，该模式适合十万人以内的在线直播场景，但需要您在接下来介绍的 options 参数中指定 角色(role) 这个字段
     * @param {string=} [options.role] 用户角色，仅在 {@link module:TYPE.SCENE_LIVE TRTC.TYPE.SCENE_LIVE} 场景下有意义，{@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC} 场景无需指定 role，目前支持两种角色：
     * - {@link module:TYPE.ROLE_ANCHOR TRTC.TYPE.ROLE_ANCHOR}（默认） 主播
     * - {@link module:TYPE.ROLE_AUDIENCE TRTC.TYPE.ROLE_AUDIENCE} 观众
     * <br>
     * 注意：观众角色没有发布本地音视频的权限，只有收看远端流的权限。如果观众想要连麦跟主播互动，
     * 请先通过 {@link TRTC#switchRole switchRole()} 切换角色到主播后再发布本地音视频。
     * @param {boolean} [options.autoReceiveAudio=true] 是否自动接收音频。当远端用户发布音频后，SDK 自动播放远端用户的音频。
     * @param {boolean} [options.autoReceiveVideo=false] 是否自动接收视频。当远端用户发布视频后，SDK 自动拉流并解码远端视频，您需要调用 {@link TRTC#startRemoteVideo startRemoteVideo} 播放远端视频。
     * - 自 v5.6.0 版本开始，该参数默认值变更为 `false`，参考：[v5.6.0 的 Breaking Changed](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/tutorial-00-info-update-guideline.html)
     * @param {boolean} [options.enableAutoPlayDialog] 是否开启 SDK 自动播放失败弹窗，默认：true。
     * - 默认开启，当出现自动播放失败时，SDK 会弹窗引导用户点击页面，来恢复音视频播放。
     * - 可设置为 false 关闭，建议接入侧参考{@tutorial 21-advanced-auto-play-policy}来处理自动播放失败相关问题。
     * @param {string|ProxyServer} [options.proxy] 设置代理服务器。参考最佳实践：{@tutorial 34-advanced-proxy}。
     * @param {string} [options.privateMapKey] 进房钥匙，若需要权限控制请携带该参数（传空或传错会导致进房失败）。<br>[privateMapKey 权限设置](https://cloud.tencent.com/document/product/647/32240)
     * @throws
     * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
     * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
     * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
     * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
     * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
     * @example
     * const trtc = TRTC.create();
     * await trtc.enterRoom({ roomId: 8888, sdkAppId, userId, userSig });
     */
    @validate(validateConfig.TRTC.enterRoom)
    @randStart('room', ([arg1], [arg2]) => (arg1.roomId || arg1.strRoomId) === (arg2.roomId || arg2.strRoomId) && arg1.userId === arg2.userId && arg1.sdkAppId === arg2.sdkAppId)
    @createDecorator(next => function (params) {
      this._log.setUserId(params.userId);
      this._log.setSdkAppId(params.sdkAppId);
      return next.call(this, params).catch(e => {
        eventManager.remove(this);
        throw e;
      });
    })
    @addAPICallLog()
    async enterRoom(params) {
      // TODO: dev 弹窗提示
      // if (!this._eventListened.has(TRTCEvent.ERROR)) {
      //   console.warn('You should listen to the error event to handle the error properly.');
      // }
      // if (!this._eventListened.has(TRTCEvent.AUTOPLAY_FAILED)) {
      //   console.warn('You should listen to the autoplay-failed event to recover play.');
      // }
      // if (!this._eventListened.has(TRTCEvent.REMOTE_VIDEO_AVAILABLE)) {
      //   console.warn('You should listen to the remote-video-available event to play remote video.');
      // }
      const { scene = Scene.RTC, enableAutoPlayDialog = true, autoReceiveAudio = true, autoReceiveVideo = true } = params;
      if (params.proxy) {
        this._room.setProxyServer(params.proxy);
        if (!isString(params.proxy) && params.proxy.turnServer) {
          this._room.setTurnServer?.(params.proxy.turnServer, params.proxy.iceTransportPolicy);
        }
      }
      this._room.enableAutoPlayDialog = enableAutoPlayDialog;
      this._room.autoReceiveAudio = autoReceiveAudio;
      this._room.autoReceiveVideo = autoReceiveVideo;
      // 隐藏参数。
      // @ts-ignore
      if (isBoolean(params.enableHWEncoder)) this._room.enableHWEncoder = params.enableHWEncoder;
      const roomParams = {
        sdkAppId: params.sdkAppId,
        userId: params.userId,
        userSig: params.userSig,
        privateMapKey: params.privateMapKey || null,
        role: params.role === UserRole.AUDIENCE ? UserRoleNumber.AUDIENCE : UserRoleNumber.ANCHOR,
        roomId: params.roomId || 0,
        strRoomId: params.strRoomId || '',
        businessInfo: params.businessInfo || null,
        streamId: null,
        userDefineRecordId: params.userDefineRecordId || null,
        // 内部参数不暴露
        // @ts-ignore
        frameWorkType: params.frameWorkType,
        // @ts-ignore
        component: params.component,
        // @ts-ignore
        language: params.language
      };
      if (params.strRoomId && !params.roomId) {
        this._room.useStringRoomId = true;
      }
      // @ts-ignore
      eventManager.create(this, this._room)
        .add(RoomEvent.PEER_JOIN, user => {
          const { userId } = user;
          // 将map中的普通object转换成类对象
          this.emit(TRTCEvent.REMOTE_USER_ENTER, { userId });
        })
        .add(RoomEvent.PEER_LEAVE, userId => {
          this.emit(TRTCEvent.REMOTE_USER_EXIT, { userId });
        })
        .add(RoomEvent.BANNED, event => {
          // 被踢后清理进房相关状态，业务侧可以重新调用 enterRoom 接口进房
          this._exitRoom().then(() => {
            this.emit(TRTCEvent.KICKED_OUT, { reason: event.reason });
          });
        })
        .add(RoomEvent.ERROR, error => {
          // 清理进房相关状态，业务侧可以重新调用 enterRoom 接口进房
          this._exitRoom().then(() => {
            this.emit(TRTCEvent.ERROR, RtcError.convertFrom(error));
          });
        })
        .add(RoomEvent.SIGNAL_CONNECTION_STATE_CHANGED, event => {
          this.emit(TRTCEvent.CONNECTION_STATE_CHANGED, event);
        })
      // .add(RoomEvent.MEDIA_CONNECTION_STATE_CHANGED, (event: { prevState: ConnectionState, state: ConnectionState; userId: string; }) => {
      //   this.emit(TRTCEvent.CONNECTION_STATE_CHANGED, { ...event, type: 'media' });
      // })
        .add(RoomEvent.NETWORK_QUALITY, result => {
          this._networkQuality = result;
          this.emit(TRTCEvent.NETWORK_QUALITY, result);
        })
        .add(RoomEvent.REMOTE_PUBLISHED, remotePublishedUser => {
          const remoteTracks = [remotePublishedUser.remoteAudioTrack, remotePublishedUser.remoteVideoTrack, remotePublishedUser.remoteAuxiliaryTrack];
          remoteTracks.forEach(remoteTrack => {
            eventManager.create(remoteTrack, remoteTrack)
              .add(TrackEvent.PLAYER_STATE_CHANGED, event => {
                const data = { ...event, userId: remotePublishedUser.userId };
                if (remoteTrack.kind === NAME.VIDEO) {
                  data.streamType = convertStreamType(remoteTrack.streamType);
                }
                this.emit(remoteTrack.kind === NAME.AUDIO ? TRTCEvent.AUDIO_PLAY_STATE_CHANGED : TRTCEvent.VIDEO_PLAY_STATE_CHANGED, data);
              })
              .add(TrackEvent.ERROR, error => {
                if (error.getCode() === CoreErrorCode.PLAY_NOT_ALLOWED) {
                  this.emit(TRTCEvent.AUTOPLAY_FAILED, { userId: remoteTrack.userId });
                }
              });
          });
        })
        .add(RoomEvent.REMOTE_UNPUBLISHED, remotePublishedUser => {
          const remoteTracks = [remotePublishedUser.remoteAudioTrack, remotePublishedUser.remoteVideoTrack, remotePublishedUser.remoteAuxiliaryTrack];
          remoteTracks.forEach(remoteTrack => {
            eventManager.remove(remoteTrack);
          });
        })
        .add(RoomEvent.REMOTE_PUBLISH_STATE_CHANGED, ({ prevMuteState, muteState }) => {
          const { userId } = muteState;
          const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
          const prevAudioAvailable = prevMuteState.audioAvailable;
          const prevVideoAvailable = prevMuteState.videoAvailable;
          const { audioAvailable, videoAvailable } = muteState;
          if (!audioAvailable) {
            this._remoteAudioConfigMap.delete(userId);
          }
          if (!videoAvailable) {
            this._remoteVideoConfigMap.delete(`${userId}_${'main' /* TRTCStreamType.Main */}`);
          }
          if (!muteState.hasAuxiliary) {
            this._remoteVideoConfigMap.delete(`${userId}_${'sub' /* TRTCStreamType.Sub */}`);
          }
          if (prevAudioAvailable !== audioAvailable) {
            this.emit(audioAvailable ? TRTCEvent.REMOTE_AUDIO_AVAILABLE : TRTCEvent.REMOTE_AUDIO_UNAVAILABLE, { userId });
          }
          if (prevVideoAvailable !== videoAvailable) {
            this.emit(videoAvailable ? TRTCEvent.REMOTE_VIDEO_AVAILABLE : TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, { userId, streamType: 'main' /* TRTCStreamType.Main */ });
          }
          if (prevMuteState.hasAuxiliary !== muteState.hasAuxiliary) {
            this.emit(muteState.hasAuxiliary ? TRTCEvent.REMOTE_VIDEO_AVAILABLE : TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, { userId, streamType: 'sub' /* TRTCStreamType.Sub */ });
          }
        })
        .add(RoomEvent.FIREWALL_RESTRICTION, () => {
          this.emit(TRTCEvent.ERROR, new RtcError({
            code: ErrorCode.OPERATION_FAILED,
            extraCode: ErrorCodeDictionary.FIREWALL_RESTRICTION
          }));
        })
        .add(RoomEvent.SEI_MESSAGE, event => {
          this.emit(TRTCEvent.SEI_MESSAGE, event);
        })
        .add(RoomEvent.HEARTBEAT_REPORT, report => {
          const videoTypeMap = {
            2: TRTCVideoType.Big,
            3: TRTCVideoType.Small,
            7: TRTCVideoType.Sub
          };
          const stats = {
            rtt: report.msg_up_stream_info.msg_network_status.uint32_rtt || report.msg_down_stream_info[0]?.msg_network_status.uint32_rtt || this._networkQuality?.uplinkRTT || this._networkQuality?.downlinkRTT || 0,
            upLoss: this._networkQuality?.uplinkLoss || 0,
            downLoss: this._networkQuality?.downlinkLoss || 0,
            bytesSent: report.bytes_sent || 0,
            bytesReceived: report.bytes_received || 0,
            localStatistics: {
              audio: { bitrate: (report.msg_up_stream_info.msg_audio_status?.uint32_audio_codec_bitrate || 0) / 1000, audioLevel: (report.msg_up_stream_info.msg_audio_status?.uint32_audio_level || 0) / AUDIO_LEVEL_SCALE },
              video: report.msg_up_stream_info.msg_video_status.filter(item => videoTypeMap[item.uint32_video_stream_type]).map(item => ({
                bitrate: (item.uint32_video_codec_bitrate || 0) / 1000,
                width: item.uint32_video_width,
                height: item.uint32_video_height,
                frameRate: item.uint32_video_enc_fps,
                videoType: videoTypeMap[item.uint32_video_stream_type]
              }))
            },
            remoteStatistics: report.msg_down_stream_info.map(item => ({
              userId: item.msg_user_info.str_identifier,
              audio: { bitrate: (item.msg_audio_status.uint32_audio_codec_bitrate || 0) / 1000, audioLevel: (item.msg_audio_status.uint32_audio_level || 0) / AUDIO_LEVEL_SCALE },
              video: item.msg_video_status.map(videoItem => ({
                bitrate: (videoItem.uint32_video_codec_bitrate || 0) / 1000,
                width: videoItem.uint32_video_width,
                height: videoItem.uint32_video_height,
                frameRate: videoItem.uint32_video_dec_fps,
                videoType: videoTypeMap[videoItem.uint32_video_stream_type]
              }))
            }))
          };
          this.emit(TRTCEvent.STATISTICS, stats);
        });
      this._handleReceiveMode();
      await this._room.join(roomParams, scene, TRTC.frameWorkType);
      this._checkTrackToPublish();
    }
    /**
     * 退出当前音视频通话房间。
     * - 退房后将会关闭和远端用户的连接，不再接收和播放远端用户音视频，并且停止本地音视频的发布。
     * - 本地摄像头和麦克风的采集和预览不会因此而停止。您可以调用 {@link TRTC#stopLocalVideo stopLocalVideo()} 和 {@link TRTC#stopLocalAudio stopLocalAudio()} 停止本地音视频采集。
     * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
     * @memberof TRTC
     * @example
     * await trtc.exitRoom();
     */
    @addAPICallLog()
    async exitRoom() {
      return await this._exitRoom();
    }

    /**
     * 为观众切换直播房间。<br>
     * - 在直播场景中切换观众正在观看的房间。这比退出旧房间再进入新房间更快，可以优化直播等场景的打开时间。
     * - [联系我们](https://cloud.tencent.com/document/product/647/19906) 以启用此 API。
     *
     * @param {object} options 切换房间的参数
     * @param {string} options.userSig UserSig 签名 <br>
     * 计算 userSig 的方式请参考 [UserSig 相关](https://trtc.io/document/35166) <br/>
     * @param {number=} options.roomId
     * 数字类型的房间号，取值要求为 [1, 4294967294] 的整数;<br>
     * <font color="red">注意: <br>1. 要切换的房间和当前房间需为相同房间类型（同为数字房间或字符串房间）<br> 2. roomId 和 strRoomId 必须填一个。若两者都填，则优先选择 roomId。</font>
     * @param {string=} options.strRoomId
     * 字符串类型的房间号，限制长度为64字节，且仅支持以下范围的字符集：
     * - 大小写英文字母（a-zA-Z）
     * - 数字（0-9）
     * - 空格 ! # $ % & ( ) + - : ; < = . > ? @ [ ] ^ _ { } | ~ ,
     * @param {boolean=} [options.privateMapKey] 进房钥匙，若需要权限控制请携带该参数（传空或传错会导致进房失败）。<br>[privateMapKey 权限设置](https://cloud.tencent.com/document/product/647/32240).
     * @throws
     * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
     * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
     * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
     * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
     * @example
     * const trtc = TRTC.create();
     * await trtc.enterRoom({
     *    ...
     *    roomId: 8888,
     *    scene: TRTC.TYPE.SCENE_LIVE,
     *    role: TRTC.TYPE.ROLE_AUDIENCE
     * });
     * await trtc.switchRoom({
     *    userSig,
     *    roomId: 9999
     * });
     */
    switchRoom(options) {}

    /**
   * 切换用户角色，仅在 TRTC.TYPE.SCENE_LIVE 互动直播模式下生效。
   *
   * 互动直播模式下，一个用户可能需要在“观众”和“主播”之间来回切换。
   * 您可以通过 {@link TRTC#enterRoom enterRoom()} 中的 role 字段确定角色，也可以通过 switchRole 在进房后切换角色。
   * - 观众切换为主播，调用 trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR) 将用户角色转换为 TRTC.TYPE.ROLE_ANCHOR 主播角色，之后按需调用 {@link TRTC#startLocalVideo startLocalVideo()} 和 {@link TRTC#startLocalAudio startLocalAudio()} 发布本地音视频。
   * - 主播切换为观众，调用 trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE) 将用户角色转换为 TRTC.TYPE.ROLE_AUDIENCE 观众角色，此时如果有已发布的本地音视频，SDK 会取消发布本地音视频。
   * > !
   * > - 该接口需要在进房成功后才可以调用。
   * > - 关闭摄像头和麦克风后，建议及时切换成观众角色，避免主播角色占用 50路上行的资源。
   * @param {string} role 用户角色
   * - TRTC.TYPE.ROLE_ANCHOR 主播，可以发布本地音视频，单个房间里最多支持 50 个主播同时发布本地音视频。
   * - TRTC.TYPE.ROLE_AUDIENCE 观众，不能发布本地音视频，只能观看远端流，单个房间里的观众人数没有上限。
   * @param {object} [option]
   * @param {string} [option.privateMapKey] `支持 v5.3.0+` <br>
   * privateMapKey 有可能会超时失效，您可以传入该参数更新 privateMapKey。
   * @throws
   * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
   * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
   * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
   * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
   * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
   * @memberof TRTC
   * @example
   * // 进房成功后
   * // TRTC.TYPE.SCENE_LIVE 互动直播模式下，观众切换为主播
   * await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);
   * // 观众角色切换为主播，开始推流
   * await trtc.startLocalVideo();
   *
   * // TRTC.TYPE.SCENE_LIVE 互动直播模式下，主播切换为观众
   * await trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE);
   * @example
   * // Since v5.3.0+
   * await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR, { privateMapKey: 'your new privateMapKey' });
   */
    @validate(validateConfig.TRTC.switchRole)
    @randUpdate('room', { merge: (arg1, arg2) => arg2 })
    @addAPICallLog()
    async switchRole(role) {
      await this._room.switchRole(role);
      if (role === UserRole.ANCHOR) {
        this._checkTrackToPublish();
      }
    }
    /**
     * 销毁 TRTC 实例 <br/>
     *
     * 在退房之后，若业务侧无需再使用 trtc 时，需调用该接口及时销毁 trtc 实例，释放相关资源。
     *
     * 注意：
     *  - 销毁后的 trtc 实例不可再继续使用。
     *  - 已进房的情况下，需先调用 {@link TRTC#exitRoom TRTC.exitRoom} 接口退房成功后，才能调用该接口销毁 trtc。
     *
     * @example
     * // 通话结束时
     * await trtc.exitRoom();
     * // 若后续无需再使用该 trtc，则销毁 trtc，并释放引用。
     * trtc.destroy();
     * trtc = null;
     * @throws {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
     * @memberof TRTC
     */
    @addAPICallLog()
    destroy() {
      // API 层事件销毁
      eventManager.remove(deviceDetector);
      this.removeAllListeners();
      this._room.destroy();
      // 销毁插件
      trtcInstanceSet.delete(this);
      if (this._localAudioTrack) {
        this.stopLocalAudio();
      }
      if (this._localVideoTrack) {
        this.stopLocalVideo();
      }
      if (this._localScreenTrack) {
        this.stopScreenShare();
      }
    }
    /**
    * 开启本地麦克风采集，并发布到当前的房间中。
    * - 调用时机：进房前后均可调用，不可重复调用。
    * - 一个 trtc 实例只能开启一路麦克风，若您需要在已经开启一路麦克风的情况下，再开启一路麦克风用于测试，可以创建多个 trtc 实例实现。
    *
    * @param {object} [config] - 配置项
    * @param {boolean} [config.publish] - 是否将本地音频发布到房间中，默认为true。若在进房前调用该接口，并且 publish = true，则在进房后 SDK 会自动发布。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean} [config.mute] - 是否静音麦克风。参考：{@tutorial 15-basic-dynamic-add-video}。
    * @param {object} [config.option] - 本地音频选项
    * @param {string} [config.option.microphoneId]- 指定使用哪个麦克风
    * @param {MediaStreamTrack} [config.option.audioTrack] - 自定义采集的 audioTrack。{@tutorial 20-advanced-customized-capture-rendering}。
    * @param {number} [config.option.captureVolume] - 设置采集音量，默认值 100，设置小于 100 可以降低采集音量，设置大于 100 可以放大采集音量，注意有爆音风险。自 v5.2.1+ 支持。
    * @param {number} [config.option.earMonitorVolume] - 设置耳返音量，取值[0, 100]，本地麦克风默认静音播放。
    * @param {string} [config.option.profile] - 音频编码配置, 默认{@link module:TYPE.AUDIO_PROFILE_STANDARD TRTC.TYPE.AUDIO_PROFILE_STANDARD}
    * @throws
    * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
    * @example
    * // 采集默认麦克风并发布
    * await trtc.startLocalAudio();
    * @example
    * // 如下是测试麦克风音量的代码示例，可用于麦克风音量检测。
    * trtc.enableAudioVolumeEvaluation();
    * trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => { });
    * // 测试麦克风无需发布音频
    * await trtc.startLocalAudio({ publish: false });
    * // 测试完毕后，关闭麦克风
    * await trtc.stopLocalAudio();
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.startLocalAudio)
    @randStart('audio', ([arg1], [arg2]) => arg1?.option?.microphoneId === arg2?.option?.microphoneId)
    @addAPICallLog()
    async startLocalAudio(config = { publish: true }) {
      if (this._localAudioTrack) {
        this._log.warn('local audio is already started');
        return;
      }
      const { publish = true, option } = config;
      const localAudioTrack = new LocalAudioTrack(this._room.audioManager);
      const captureOption = {};
      const playOption = { muted: true };
      // TODO: 增加 capture 参数
      if (option) {
        if (!isUndefined(option.microphoneId)) {
          captureOption.deviceId = option.microphoneId;
        } else if (!isUndefined(option.audioTrack)) {
          captureOption.customSource = option.audioTrack;
        }
        if (option && isNumber(option.captureVolume)) {
          localAudioTrack.setCaptureVolume(option.captureVolume);
        }
        if (!isUndefined(option.profile)) {
          if (isString(option.profile)) {
            audioProfileMap[option.profile] && localAudioTrack.setProfile(audioProfileMap[option.profile]);
          } else {
            localAudioTrack.setProfile(option.profile);
          }
        }
        if (isNumber(option.earMonitorVolume)) {
          playOption.muted = !(option.earMonitorVolume > 0);
          playOption.volume = option.earMonitorVolume;
        }
        // 3A 配置
        if (!isUndefined(option.echoCancellation)) {
          localAudioTrack.profile.echoCancellation = option.echoCancellation;
        }
        if (!isUndefined(option.noiseSuppression)) {
          localAudioTrack.profile.noiseSuppression = option.noiseSuppression;
        }
        if (!isUndefined(option.autoGainControl)) {
          localAudioTrack.profile.autoGainControl = option.autoGainControl;
        }
      }
      localAudioTrack.on(LocalTrackEvent.DEVICE_RECOVER_FAILED, error => {
        this.emit(TRTCEvent.ERROR, new RtcError({
          code: ErrorCode.DEVICE_ERROR,
          extraCode: ErrorCodeDictionary.MICROPHONE_RECOVER_FAILED,
          messageParams: { error }
        }));
      });
      localAudioTrack.on(LocalTrackEvent.DEVICE_CHANGED, device => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Microphone, action: TRTCDeviceAction.Active, device });
      });
      localAudioTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, event => {
        let error;
        if (event.error) {
          error = RtcError.convertFrom(event.error);
        }
        this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
      });
      await localAudioTrack.capture(captureOption);
      eventManager.create(localAudioTrack, localAudioTrack)
        .add(TrackEvent.PLAYER_STATE_CHANGED, event => {
          this.emit(TRTCEvent.AUDIO_PLAY_STATE_CHANGED, { ...event, userId: '' });
        });
      if (publish && this._room.isJoined) {
        this._room.publish(localAudioTrack).catch(() => { });
      }
      await this._updateAudioPlayOption({ playOption, track: localAudioTrack });
      this._localAudioTrack = localAudioTrack;
      this._localAudioConfig = { ...config, publish };
    }
    /**
    * 更新本地麦克风配置。
    * - 调用时机：该接口需在 {@link TRTC#startLocalAudio startLocalAudio()} 成功后调用，可以多次调用。
    * - 本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。
    * @param {object} [config]
    * @param {boolean} [config.publish] - 是否将本地音频发布到房间中，默认为 true。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean} [config.mute] - 是否静音麦克风。参考：{@tutorial 15-basic-dynamic-add-video}。
    * @param {object} [config.option] - 本地音频配置
    * @param {string} [config.option.microphoneId] - 指定使用哪个麦克风，用来切换麦克风。
    * @param {MediaStreamTrack} [config.option.audioTrack] - 自定义采集的 audioTrack。 {@tutorial 20-advanced-customized-capture-rendering}。
    * @param {number} [config.option.captureVolume] - 设置采集音量，默认值 100，设置小于 100 可以降低采集音量，设置大于 100 可以放大采集音量，注意有爆音风险。自 v5.2.1+ 支持。
    * @param {number} [config.option.earMonitorVolume] - 设置耳返音量，取值[0, 100]，本地麦克风默认静音播放。
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * // 切换麦克风
    * const microphoneList = await TRTC.getMicrophoneList();
    * if (microphoneList[1]) {
    *   await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
    * }
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.updateLocalAudio)
    @randUpdate('audio', { debounce: { delay: 200, getKey: () => `${seq}-localAudio`, isNeedToDebounce: config => !isUndefined(config.option?.captureVolume) } })
    @addAPICallLog()
    async updateLocalAudio(config) {
      // replace track
      // 设置采集音量
      // 改变推流状态
      if (!this._localAudioTrack || !this._localAudioConfig) return;
      const { publish, mute, option } = config;
      const playOption = {};
      if (option) {
        if (option.microphoneId) {
          await this._localAudioTrack.switchDevice(option.microphoneId);
        } else if (!isUndefined(option.audioTrack)) {
          await this._localAudioTrack.setInputMediaStreamTrack(option.audioTrack);
        }
        if (!isUndefined(option.captureVolume)) {
          this._localAudioTrack.setCaptureVolume(option.captureVolume);
        }
        if (!isUndefined(option.earMonitorVolume)) {
          playOption.muted = !(option.earMonitorVolume > 0);
          playOption.volume = option.earMonitorVolume;
        }
        await this._localAudioTrack.update3A(option);
      }
      if (this._room.isJoined && !isUndefined(publish)) {
        if (publish && !this._localAudioConfig.publish) {
          this._room.publish(this._localAudioTrack).catch(() => { });
        }
        if (this._localAudioConfig.publish && !publish) {
          this._room.unpublish(this._localAudioTrack).catch(() => { });
        }
      }
      if (!isUndefined(mute)) {
        this._localAudioTrack.setMute(mute);
      }
      await this._updateAudioPlayOption({ playOption, track: this._localAudioTrack, prevConfig: this._localAudioConfig });
      deepMerge(this._localAudioConfig, config);
    }
    /**
    * 停止本地麦克风的采集及发布。
    * - 如果您只是想静音麦克风，请使用 updateLocalAudio({ mute: true })。参考：{@tutorial 15-basic-dynamic-add-video}。
    * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * await trtc.stopLocalAudio();
    * */
    @randStop('audio')
    @addAPICallLog()
    async stopLocalAudio() {
      if (!this._localAudioTrack) return;
      if (this._room.isJoined) {
        // unpublish 不会失败，catch error
        await this._room.unpublish(this._localAudioTrack).catch(() => { });
      }
      // TODO: 停止插件
      this._localAudioTrack.stop();
      this._localAudioTrack.close();
      eventManager.remove(this._localAudioTrack);
      this._localAudioTrack = null;
      this._localAudioConfig = null;
    }
    /**
     * @typedef {object|string} VideoProfile - 本地视频流配置
     *
     * 视频配置参数,可以用字符串预设值或者自定义分辨率等参数
     * | 视频 Profile | 分辨率（宽 x 高）| 帧率（fps）| 码率（kbps）| 备注 |
     * | :---       | :---           | :---      | :---      | :--- |
     * | 120p       | 160 x 120      | 15        | 200        ||
     * | 120p_2       | 160 x 120      | 15        | 100        | v5.1.1+ 支持 |
     * | 180p       | 320 x 180      | 15        | 350       ||
     * | 180p_2       | 320 x 180      | 15        | 150       | v5.1.1+ 支持 |
     * | 240p       | 320 x 240      | 15        | 400       ||
     * | 240p_2       | 320 x 240      | 15        | 200       | v5.1.1+ 支持 |
     * | 360p       | 640 x 360      | 15        | 800       ||
     * | 360p_2       | 640 x 360      | 15        | 400       | v5.1.1+ 支持 |
     * | 480p       | 640 x 480      | 15        | 900       ||
     * | 480p_2       | 640 x 480      | 15        | 500       | 默认值，v5.1.1+ 支持 |
     * | 720p       | 1280 x 720     | 15        | 1500      ||
     * | 1080p      | 1920 x 1080    | 15        | 2000      ||
     * | 1440p      | 2560 x 1440    | 30        | 4860      ||
     * | 4K         | 3840 x 2160    | 30        | 9000      ||
     * @property {number} width - 视频宽度
     * @property {number} height - 视频高度
     * @property {number} frameRate - 视频帧率
     * @property {number} bitrate - 视频码率
     * @example
     * const config = {
     *  option: {
     *   profile: '480p_2',
     *  },
     * }
     * await trtc.startLocalVideo(config);
     * @example
     * const config = {
     *  option: {
     *    profile: {
     *      width: 640,
     *      height: 480,
     *      frameRate: 15,
     *      bitrate: 500,
     *    }
     *  }
     * }
     * await trtc.startLocalVideo(config);
     */
    /**
    * 开启本地摄像头采集，在您指定的 HTMLElement 标签下播放摄像头画面，并将摄像头画面发布到当前所在房间中。
    * - 调用时机：进房前后均可调用，不可重复调用。
    * - 一个 trtc 实例只能开启一路摄像头。若您需要在已经开启一路摄像头的情况下，再开启一路摄像头用于测试，可以创建多个 trtc 实例实现。
    *
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 本地视频预览的 HTMLElement 实例或者 Id， 如果不传或传入 null， 则不会播放视频。
    * @param {boolean} [config.publish] - 是否将本地视频发布到房间中。默认为 true，若在进房前调用该接口，SDK 会在进房成功后自动发布（若 publish=true）。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean | string} [config.mute] - 是否临时关闭摄像头。支持传入图片 url 字符串，本地与远端将显示这张图片，房间内其他用户会收到 REMOTE_AUDIO_AVAILABLE 事件，不支持在摄像头关闭时调用。详细内容请参考：{@tutorial 15-basic-dynamic-add-video}.
    * @param {boolean} [config.capture] - Since v5.11.0. 是否采集摄像头。在不开启摄像头的前提下合流时会用到，参考：{@tutorial 40-advanced-video-mixer}。
    * @param {object} [config.option] - 本地视频配置
    * @param {string} [config.option.cameraId] - 指定使用哪个摄像头，用于切换摄像头。
    * @param {boolean} [config.option.useFrontCamera] - 是否使用前置摄像头。
    * @param {MediaStreamTrack} [config.option.videoTrack] - 自定义采集的 videoTrack。{@tutorial 20-advanced-customized-capture-rendering}。
    * @param {'view' | 'publish' | 'both' | boolean} [config.option.mirror] - 视频镜像模式，默认为 'view'。可选参数如下：
    * - 'view': 你看自己是镜像，对方看你是非镜像的。
    * - 'publish': 你看自己是非镜像的，对方看你是镜像的。
    * - 'both': 你看自己是镜像的，对方看你是镜像的。
    * - false: 布尔值，代表没有任何镜像。
    *
    * <font color="orange"> 注意: 5.3.2 之前版本仅支持传入 boolean，true 代表本地预览镜像，false 代表没有任何镜像。</font>
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。默认为 `cover`。参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit CSS object-fit} 属性。
    * @param {VideoProfile} [config.option.profile] - 视频大流编码参数。默认值：`480p_2`。
    * @param {boolean | VideoProfile} [config.option.small] - 视频小流编码参数，参考：{@tutorial 27-advanced-small-stream}
    * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - 设置弱网时，视频编码策略。（默认）流畅度优先（{@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}）或 清晰度优先（{@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_PREFERENCE_CLEAR}）
    * @param {boolean} [config.option.avoidCropping] - 在 PC Chrome 和 Firefox 中，采集 360p 180p 这类低分辨率 16/9 的视频时，可能会出现画幅被裁剪的问题。设置该参数为 true 可以避免裁剪。
    * @param {0 | 90 | 180 | 270} [config.option.rotation] - 设置摄像头顺时针旋转的角度。Since `v5.11.0`。
    * @throws
    * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
    * @example <caption>示例 1：预览及发布摄像头</caption>
    * // 预览及发布摄像头
    * await trtc.startLocalVideo({
    *   view: document.getElementById('localVideo'), // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
    * });
    * @example <caption>示例 2：测试摄像头——只预览不发布</caption>
    * // 只预览摄像头画面、不发布。可用于做摄像头测试。
    * const config = {
    *   view: document.getElementById('localVideo'), // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
    *   publish: false // 不发布摄像头
    * }
    * await trtc.startLocalVideo(config);
    * // 当需要发布视频时调用 updateLocalVideo
    * await trtc.updateLocalVideo({ publish:true });
    * @example <caption>示例 3：预览及发布指定的摄像头</caption>
    * // 使用指定的摄像头。
    * // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
    * const view = document.getElementById('localVideo');
    * const cameraList = await TRTC.getCameraList();
    * if (cameraList[0]) {
    *   await trtc.startLocalVideo({
    *     view,
    *     option: {
    *       cameraId: cameraList[0].deviceId,
    *     }
    *   });
    * }
    *
    * // 在移动端指定使用前置摄像头
    * await trtc.startLocalVideo({ view, option: { useFrontCamera: true }});
    * // 在移动端指定使用后置摄像头
    * await trtc.startLocalVideo({ view, option: { useFrontCamera: false }});
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.startLocalVideo)
    @randStart('video', ([arg1], [arg2]) => arg1?.option?.cameraId === arg2?.option?.cameraId)
    @addAPICallLog()
    async startLocalVideo(config = { publish: true, view: null }) {
      if (this._localVideoTrack) {
        this._log.warn('local video is already started');
        return;
      }
      const { view, publish = true, option } = config;
      const localVideoTrack = new LocalVideoTrack(this._room.videoManager);
      const captureOption = {};
      const playOption = {};
      if (option) {
        if (option.cameraId) {
          captureOption.deviceId = option.cameraId;
        } else if (!isUndefined(option.useFrontCamera)) {
          captureOption.facingMode = option.useFrontCamera ? NAME.FACING_MODE_USER : NAME.FACING_MODE_ENVIRONMENT;
        } else if (!isUndefined(option.videoTrack)) {
          captureOption.customSource = option.videoTrack;
        }
        if (!isUndefined(option.profile)) {
          if (isString(option.profile)) {
            videoProfileMap[option.profile] && localVideoTrack.setProfile(videoProfileMap[option.profile]);
          } else {
            localVideoTrack.setProfile(option.profile);
          }
        }
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (!isUndefined(option.mirror)) {
          playOption.mirror = option.mirror;
        }
        if (!isUndefined(option.small)) {
          // 小流开启
          if (isSmallStreamSupported()) {
            if (isString(option.small)) {
              localVideoTrack.small = videoProfileMap[option.small];
            } else {
              localVideoTrack.small = option.small;
            }
          } else {
            this._log.warn('small stream is not supported');
          }
        }
      }
      localVideoTrack.on(LocalTrackEvent.DEVICE_RECOVER_FAILED, error => {
        this.emit(TRTCEvent.ERROR, new RtcError({
          code: ErrorCode.DEVICE_ERROR,
          extraCode: ErrorCodeDictionary.CAMERA_RECOVER_FAILED,
          messageParams: { error }
        }));
      });
      localVideoTrack.on(LocalTrackEvent.DEVICE_CHANGED, device => {
        this.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Camera, action: TRTCDeviceAction.Active, device });
      });
      localVideoTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, event => {
        let error;
        if (event.error) {
          error = RtcError.convertFrom(event.error);
        }
        this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
      });
      // 采集，自定义源
      await localVideoTrack.capture(captureOption);
      if (option?.qosPreference && localVideoTrack.mediaTrack) {
        const contentHint = getContentHintFromQosPreference(option.qosPreference);
        localVideoTrack.mediaTrack.contentHint = contentHint;
      }
      eventManager.create(localVideoTrack, localVideoTrack)
        .add(TrackEvent.PLAYER_STATE_CHANGED, event => {
          this.emit(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, { ...event, userId: '', streamType: 'main' /* TRTCStreamType.Main */ });
        });
      // 是否推流
      if (publish && this._room.isJoined) {
        this._room.publish(localVideoTrack).catch(() => { });
      }
      // 播放
      await this._updateVideoPlayOption({ view, playOption, track: localVideoTrack });
      this._localVideoTrack = localVideoTrack;
      this._localVideoConfig = { ...config, view, publish };
    }
    /**
    * 更新本地摄像头配置。
    * - 该接口需在 {@link TRTC#startLocalVideo startLocalVideo()} 成功后调用。
    * - 该接口可以多次调用。
    * - 本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 预览摄像头的 HTMLElement 实例或者 Id， 如果不传或传入null，则不会渲染视频，但仍然会推流并消耗带宽
    * @param {boolean} [config.publish] - 是否将本地视频发布到房间中。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean | string} [config.mute] - 是否临时关闭摄像头。支持传入图片 url 字符串，本地与远端将显示这张图片，房间内其他用户会收到 REMOTE_AUDIO_AVAILABLE 事件，不支持在摄像头关闭时调用。详细内容请参考：{@tutorial 15-basic-dynamic-add-video}.
    * @param {boolean} [config.capture] - Since v5.11.0. 是否采集摄像头。在不开启摄像头的前提下合流时会用到，参考：{@tutorial 40-advanced-video-mixer}。
    * @param {object} [config.option] - 本地视频配置
    * @param {string} [config.option.cameraId] - 指定使用哪个摄像头，
    * @param {boolean} [config.option.useFrontCamera] - 是否使用前置摄像头
    * @param {MediaStreamTrack} [config.option.videoTrack] - 自定义采集的 videoTrack，{@tutorial 20-advanced-customized-capture-rendering}。
    * @param {'view' | 'publish' | 'both' | boolean} [config.option.mirror] - 视频镜像模式，默认为 'view'。可选参数如下：
    * - 'view': 你看自己是镜像，对方看你是非镜像的。
    * - 'publish': 你看自己是非镜像的，对方看你是镜像的。
    * - 'both': 你看自己是镜像的，对方看你是镜像的。
    * - false: 布尔值，代表没有任何镜像。
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit| CSS object-fit} 属性
    * @param {string | VideoProfile} [config.option.profile] - 视频大流编码参数
    * @param {string | boolean | VideoProfile} [config.option.small] - 视频小流编码参数，参考：{@tutorial 27-advanced-small-stream}
    * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - 设置弱网时，视频编码策略。（默认）流畅度优先（{@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}）或 清晰度优先（{@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_PREFERENCE_SMOOTH}
    * @param {0 | 90 | 180 | 270} [config.option.rotation] - 设置推流时摄像头顺时针旋转的角度。Since `v5.11.0`。
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example <caption>示例 1：动态切换摄像头</caption>
    * // 切换摄像头
    * const cameraList = await TRTC.getCameraList();
    * if (cameraList[1]) {
    *   await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
    * }
    * @example <caption>示例 2：停止发布视频，但保持本地预览</caption>
    * // 停止发布视频，但保持本地预览
    * await trtc.updateLocalVideo({ publish:false });
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.updateLocalVideo)
    @randUpdate('video')
    @addAPICallLog()
    async updateLocalVideo(config) {
      if (!this._localVideoTrack || !this._localVideoConfig) return;
      const { view, publish, mute, option } = config;
      const playOption = {};
      if (option) {
        if (!isUndefined(option.profile)) {
          if (isString(option.profile)) {
            videoProfileMap[option.profile] && this._localVideoTrack.setProfile(videoProfileMap[option.profile]);
          } else {
            this._localVideoTrack.setProfile(option.profile);
          }
          if ((!option.cameraId || !this._localVideoTrack.isNeedToSwitchDevice(option.cameraId)) && isUndefined(option.useFrontCamera)) {
            // 不会重新进行 capture，需要应用新属性
            this._localVideoTrack.applyProfile();
          }
        }
        if (option.cameraId) {
          await this._localVideoTrack.switchDevice(option.cameraId);
        } else if (!isUndefined(option.useFrontCamera)) {
          await this._localVideoTrack.switchDevice(option.useFrontCamera ? NAME.FACING_MODE_USER : NAME.FACING_MODE_ENVIRONMENT);
        } else if (!isUndefined(option.videoTrack)) {
          await this._localVideoTrack.setInputMediaStreamTrack(option.videoTrack);
        }
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (!isUndefined(option.mirror)) {
          playOption.mirror = option.mirror;
        }
        if (option.qosPreference && this._localVideoTrack.mediaTrack) {
          const contentHint = getContentHintFromQosPreference(option.qosPreference);
          this._localVideoTrack.mediaTrack.contentHint = contentHint;
        }
        if (option.small) {
          const noSmall = !this._localVideoTrack.small;
          if (isSmallStreamSupported()) {
            if (option.small === true) {
              this._localVideoTrack.small = videoProfileMap['120p'];
            } else if (isString(option.small)) {
              this._localVideoTrack.small = videoProfileMap[option.small];
            } else {
              this._localVideoTrack.small = option.small;
            }
            this._room.videoManager.update();
            if (noSmall) this._room.enableSmall(true);
          } else {
            this._log.warn('small stream is not supported');
          }
        } else if (option.small === false && this._localVideoTrack.small) {
          delete this._localVideoTrack.small;
          this._room.videoManager.update();
          this._room.enableSmall(false);
        }
      }
      if (this._room.isJoined && !isUndefined(publish)) {
        if (publish && !this._localVideoConfig.publish) {
          this._room.publish(this._localVideoTrack).catch(() => { });
        }
        if (this._localVideoConfig.publish && !publish) {
          this._room.unpublish(this._localVideoTrack).catch(() => { });
        }
      }
      if (!isUndefined(mute)) {
        this._localVideoTrack.setMute(mute);
      }
      await this._updateVideoPlayOption({ view, playOption, track: this._localVideoTrack, prevConfig: this._localVideoConfig });
      deepMerge(this._localVideoConfig, config);
    }
    /**
    * 停止本地摄像头的采集、预览及发布。
    * - 如果希望仅停止发布视频但保留本地摄像头预览，可以使用{@link TRTC#updateLocalVideo updateLocalVideo({ publish:false })}方法。<br>
    * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * await trtc.stopLocalVideo();
    * */
    @randStop('video')
    @addAPICallLog()
    async stopLocalVideo() {
      if (!this._localVideoTrack) return;
      if (this._room.isJoined) {
        // unpublish 不会失败，catch error
        await this._room.unpublish(this._localVideoTrack).catch(() => { });
      }
      this._localVideoTrack.stop();
      this._localVideoTrack.close();
      eventManager.remove(this._localVideoTrack);
      this._localVideoTrack = null;
      this._localVideoConfig = null;
    }
    /**
     * @typedef {object|string} ScreenShareProfile - 屏幕分享分辨率码率帧率配置
   * 屏幕分享配置参数,可以用字符串预设值或者自定义分辨率等参数
   * | 屏幕 Profile | 分辨率（宽 x 高）| 帧率（fps）| 码率 (kbps) |
    * | :---       | :---           | :---      | :---        |
    * | 480p       | 640 x 480      | 5         | 900         |
    * | 480p_2     | 640 x 480      | 30        | 1000        |
    * | 720p       | 1280 x 720     | 5         | 1200        |
    * | 720p_2     | 1280 x 720     | 30        | 3000        |
    * | 1080p      | 1920 x 1080    | 5         | 1600        |
    * | 1080p_2    | 1920 x 1080    | 30        | 4000        |
    * - 屏幕分享默认使用 `1080p`。
    * - 若以上 Profile 不能满足您的业务需求，您也可以指定自定义的分辨率、帧率和码率。
    *
     * @property {number} width - 屏幕分享宽度
     * @property {number} height - 屏幕分享高度
     * @property {number} frameRate - 屏幕分享帧率
     * @property {number} bitrate - 屏幕分享码率
    * @example
    * const config = {
    *  option: {
    *   profile: '720p',
    *  },
    * }
    * await trtc.startScreenShare(config);
   */
    /**
    * 开启屏幕分享。
    *
    * - 开启屏幕分享后，房间内其他用户会收到 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE REMOTE_VIDEO_AVAILABLE} 事件，streamType 为 {@link module:TYPE.STREAM_TYPE_SUB STREAM_TYPE_SUB}，其他用户可以通过 {@link TRTC#startRemoteVideo startRemoteVideo} 播放屏幕分享。
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 预览本地屏幕分享的 HTMLElement 实例或 Id， 如果不传或传入 null， 则不会渲染本地屏幕分享。
    * @param {boolean} [config.publish] - 是否将屏幕分享发布到房间中。默认为 true，若在进房前调用该接口，SDK 会在进房成功后自动发布。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean} [config.muteSystemAudio=false] - 是否 mute 系统音频，自 v5.12.0+ 支持。
    * @param {object} [config.option] - 屏幕分享配置
    * @param {boolean} [config.option.systemAudio] - 是否采集系统声音，默认为 false。
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。默认为 `contain`，参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit CSS object-fit} 属性。
    * @param {ScreenShareProfile} [config.option.profile] - 屏幕分享编码配置。 默认值：`1080p`。
    * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - 设置弱网时，视频编码策略。流畅度优先（{@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}）或 （默认）清晰度优先（{@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_PREFERENCE_CLEAR}）
    * @param {MediaStreamTrack} [config.option.videoTrack] - 自定义采集的 videoTrack。{@tutorial 20-advanced-customized-capture-rendering}。
    * @param {HTMLElement} [config.option.captureElement] - 采集当前页面中的某个 HTMLElement，支持 Chrome 104+。
    * @param {'current-tab' | 'tab' | 'window' | 'monitor'} [config.option.preferDisplaySurface='monitor'] - 控制屏幕分享预选框中的不同类型采集的显示优先级，默认是 monitor，即：在屏幕分享采集预选框中，优先展示屏幕采集。若填 'current-tab'，预选框只会展示当前页面。支持 Chrome 94+。
    * @throws
    * - {@link module:ERROR_CODE.ENV_NOT_SUPPORTED ENV_NOT_SUPPORTED}
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
    * @example
    * // 开始屏幕分享
    * await trtc.startScreenShare();
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.startScreenShare)
    @randStart('screen', () => true)
    @addAPICallLog()
    async startScreenShare(config = { publish: true, view: null }) {
      if (this._localScreenTrack) {
        this._log.warn('screen share is already started');
        return;
      }
      const { view = null, publish = true, option } = config;
      const localScreenTrack = new LocalScreenTrack(this._room.videoManager);
      localScreenTrack.on(LocalTrackEvent.PUBLISH_STATE_CHANGED, event => {
        let error;
        if (event.error) {
          error = RtcError.convertFrom(event.error);
        }
        this.emit(TRTCEvent.PUBLISH_STATE_CHANGED, { ...event, error });
      });
      let localScreenAudioTrack = null;
      // 屏幕分享以辅流形式推流
      localScreenTrack.setMediaType(MediaType.AUX_VIDEO);
      const captureOption = {};
      const playOption = {};
      if (option) {
        if (!isUndefined(option.profile)) {
          if (isString(option.profile)) {
            screenProfileMap[option.profile] && localScreenTrack.setProfile(screenProfileMap[option.profile]);
          } else {
            localScreenTrack.setProfile(option.profile);
          }
        }
        if (option.systemAudio) {
          captureOption.systemAudio = true;
          captureOption.echoCancellation = option.echoCancellation;
          captureOption.noiseSuppression = option.noiseSuppression;
          captureOption.autoGainControl = option.autoGainControl;
        }
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (option.videoTrack) {
          captureOption.videoTrack = option.videoTrack;
        }
        if (option.audioTrack) {
          captureOption.audioTrack = option.audioTrack;
        }
      }
      const mediaStream = await localScreenTrack.capture(captureOption);
      if (option?.qosPreference) {
        const contentHint = getContentHintFromQosPreference(option.qosPreference);
        localScreenTrack.mediaTrack.contentHint = contentHint;
      }
      localScreenTrack.mediaTrack.addEventListener(NAME.ENDED, () => {
        // 用户点击浏览器提供的关闭屏幕分享按钮，SDK 自动取消推流关闭屏幕分享。
        this._stopScreenShare();
        this.emit(TRTCEvent.SCREEN_SHARE_STOPPED);
      });
      if (mediaStream.getAudioTracks()[0]) {
        localScreenAudioTrack = new LocalScreenAudioTrack(this._room.audioManager);
        localScreenAudioTrack.setInputMediaStreamTrack(mediaStream.getAudioTracks()[0]);
      }
      eventManager.create(localScreenTrack, localScreenTrack)
        .add(TrackEvent.PLAYER_STATE_CHANGED, event => {
          this.emit(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, { ...event, userId: '', streamType: 'sub' /* TRTCStreamType.Sub */ });
        });
      if (publish && this._room.isJoined) {
        const trackListToPublish = [localScreenTrack];
        if (localScreenAudioTrack) {
          trackListToPublish.push(localScreenAudioTrack);
        }
        this._room.publish(...trackListToPublish).catch(() => { });
      }
      await this._updateVideoPlayOption({ view, playOption, track: localScreenTrack });
      this._localScreenTrack = localScreenTrack;
      this._localScreenAudioTrack = localScreenAudioTrack;
      this._localScreenConfig = { ...config, view, publish };
    }
    /**
    * 更新屏幕分享配置
    * - 该接口需在 {@link TRTC#startScreenShare startScreenShare()} 成功后调用。
    * - 该接口可以多次调用。
    * - 本方法采用增量更新方式：只更新传入的参数，不传入的参数保持不变。
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 屏幕分享预览的 HTMLElement 实例或 Id， 如果不传或传入 null， 则不会渲染屏幕分享。
    * @param {boolean} [config.publish] - 是否将屏幕分享发布到房间中。可监听该事件获取推流状态 {@link module:EVENT.PUBLISH_STATE_CHANGED PUBLISH_STATE_CHANGED}。
    * @param {boolean} [config.muteSystemAudio=false] - 是否 mute 系统音频，自 v5.12.0+ 支持。
    * @param {object} [config.option] - 屏幕分享配置
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。默认为 `contain`，参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit CSS object-fit} 属性。
    * @param {QOS_PREFERENCE_SMOOTH|QOS_PREFERENCE_CLEAR} [config.option.qosPreference] - 设置弱网时，视频编码策略。流畅度优先（{@link module:TYPE.QOS_PREFERENCE_SMOOTH QOS_PREFERENCE_SMOOTH}）或 （默认）清晰度优先（{@link module:TYPE.QOS_PREFERENCE_CLEAR QOS_PREFERENCE_CLEAR}）
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.DEVICE_ERROR DEVICE_ERROR}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
    * @example
    * // 停止屏幕分享，但保持屏幕分享本地预览
    * await trtc.updateScreenShare({publish:false});
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.updateScreenShare)
    @randUpdate('screen')
    @addAPICallLog()
    async updateScreenShare(config) {
      if (!this._localScreenTrack || !this._localScreenConfig) return;
      const { view, publish, option } = config;
      const playOption = {};
      if (option) {
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (option.qosPreference) {
          const contentHint = getContentHintFromQosPreference(option.qosPreference);
          this._localScreenTrack.mediaTrack.contentHint = contentHint;
        }
      }
      if (this._room.isJoined && !isUndefined(publish)) {
        if (publish && !this._localScreenConfig.publish) {
          this._room.publish(this._localScreenTrack).catch(() => { });
        }
        if (this._localScreenConfig.publish && !publish) {
          this._room.unpublish(this._localScreenTrack).catch(() => { });
        }
      }
      await this._updateVideoPlayOption({ view, playOption, track: this._localScreenTrack, prevConfig: this._localScreenConfig });
      deepMerge(this._localScreenConfig, config);
    }
    /**
    * 停止屏幕分享。
    * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * await trtc.stopScreenShare();
    * */
    @addAPICallLog()
    async stopScreenShare() {
      return await this._stopScreenShare();
    }
    /**
    * 播放远端视频
    *
    * - 调用时机：在收到 {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE)} 事件后调用。
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 用于播放远端视频的 HTMLElement 实例或者 Id， 如果不传或传入null， 则不会渲染视频， 但会仍然会拉流消耗带宽
    * @param {string} config.userId - 远端用户Id
    * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType - 远端流类型
    * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: 主流（远端用户的摄像头）（远端用户的摄像头）
    * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: 辅流（远端用户的屏幕分享）
    * @param {object} [config.option] - 远端视频配置
    * @param {boolean} [config.option.small] - 是否拉小流
    * @param {boolean} [config.option.mirror] - 是否开启镜像
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit CSS object-fit} 属性。
    * @param {boolean} [config.option.receiveWhenViewVisible] - 支持 v5.4.0+ <br>只在 view 可视时，才拉取视频流，不在可视区域内的流，SDK 会自动取消拉流，以达到节省流量的目的。参考：{@tutorial 27-advanced-small-stream}。
    * @param {HTMLElement} [config.option.viewRoot=document.body] - 支持 v5.4.0+ <br>view 的父级元素，用于计算 view 相对于 viewRoot 是否处于可视区域。建议传入 view 列表第一级父级元素，默认值是 document.body。参考：{@tutorial 27-advanced-small-stream}.
    * @param {string} [config.option.poster] - Since v5.10.0 <br> 视频的默认封面图。一般无需关心，少数浏览器会显示默认的自带图片，此时可以通过该参数自定义。参考：{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLVideoElement/poster HTMLVideoElement: poster property}
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * - {@link module:ERROR_CODE.SERVER_ERROR SERVER_ERROR}
    * @example
    * trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
    *   // 您需在 DOM 中提前放置视频容器，建议以 `${userId}_${streamType}` 作为 element id。
    *   trtc.startRemoteVideo({ userId, streamType, view: `${userId}_${streamType}` });
    * })
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.startRemoteVideo)
    @randStart(config => `v${config.userId}${config.streamType}`, () => true)
    @addAPICallLog({ getRemoteId: config => `${config.userId}_${config.streamType}` })
    async startRemoteVideo(config) {
      const { view, userId, streamType, option } = config;
      const key = `${userId}_${streamType}`;
      if (this._remoteVideoConfigMap.has(key)) {
        this._log.warn(`remote video has already started. userId:${userId}, streamType:${streamType}`);
        return;
      }
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (!remotePublishedUser) return;
      const playOption = {};
      const remoteTrack = streamType === 'main' /* TRTCStreamType.Main */ ? remotePublishedUser.remoteVideoTrack : remotePublishedUser.remoteAuxiliaryTrack;
      if (option) {
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (!isUndefined(option.mirror)) {
          playOption.mirror = option.mirror;
        }
        if (streamType === 'main' /* TRTCStreamType.Main */ && !isUndefined(option.small)) {
          this._room.changeType(option.small, remoteTrack.user.userId);
        }
      }
      await this._room.subscribe(remoteTrack);
      await this._updateVideoPlayOption({ view, playOption, track: remoteTrack });
      this._remoteVideoConfigMap.set(key, config);
    }
    /**
    * 更新远端视频播放配置<br>
    * - 该方法需 {@link TRTC#startRemoteVideo startRemoteVideo} 成功后调用。
    * - 该方法可多次调用。
    * - 该方法采用增量更新的方式，只需要传入需要更新的配置项即可。
    * @param {object} [config]
    * @param {string | HTMLElement | HTMLElement[] | null} [config.view] - 用于播放远端视频的 HTMLElement 实例或者 Id， 如果不传或传入null， 则不会渲染视频， 但会仍然会拉流消耗带宽
    * @param {string} config.userId - 远端用户Id
    * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType - 远端流类型：
    * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: 主流（远端用户的摄像头）
    * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: 辅流（远端用户的屏幕分享）
    * @param {object} [config.option] - 远端视频配置
    * @param {boolean} [config.option.small] - 是否拉小流，参考：{@tutorial 27-advanced-small-stream}
    * @param {boolean} [config.option.mirror] - 是否开启镜像
    * @param {'contain' | 'cover' | 'fill'} [config.option.fillMode] - 视频填充模式。参考 {@link https://developer.mozilla.org/zh-CN/docs/Web/CSS/object-fit CSS object-fit} 属性。
    * @param {boolean} [config.option.receiveWhenViewVisible] - 支持 v5.4.0+ <br> 只在 view 可视时，才拉取视频流，不在可视区域内的流，SDK 会自动取消拉流，以达到节省流量的目的。参考：{@tutorial 27-advanced-small-stream}。
    * @param {HTMLElement} [config.option.viewRoot=document.body] - 支持 v5.4.0+ <br>view 的父级元素，用于计算 view 相对于 viewRoot 是否处于可视区域。建议传入 view 列表第一级父级元素，默认值是 document.body。参考：{@tutorial 27-advanced-small-stream}.
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * const config = {
    *  view: document.getElementById(userId), // 你可以传入新的 view 来更新 video 播放位置。
    *  userId,
    *  streamType: TRTC.TYPE.STREAM_TYPE_MAIN
    * }
    * await trtc.updateRemoteVideo(config);
    * @memberof TRTC
    */
    @validate(validateConfig.TRTC.updateRemoteVideo)
    @randUpdate(config => `v${config.userId}${config.streamType}`)
    @addAPICallLog({ getRemoteId: config => `${config.userId}_${config.streamType}` })
    async updateRemoteVideo(config) {
      const { view, userId, streamType, option } = config;
      const key = `${userId}_${streamType}`;
      if (!this._remoteVideoConfigMap.has(key) || !this._room.remotePublishedUserMap.has(userId)) return;
      const playOption = {};
      if (option) {
        if (!isUndefined(option.fillMode)) {
          playOption.objectFit = option.fillMode;
        }
        if (!isUndefined(option.mirror)) {
          playOption.mirror = option.mirror;
        }
      }
      let remoteTrack = null;
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (streamType === 'main' /* TRTCStreamType.Main */ && remotePublishedUser?.muteState.hasVideo) {
        remoteTrack = remotePublishedUser.remoteVideoTrack;
      }
      if (streamType === 'sub' /* TRTCStreamType.Sub */ && remotePublishedUser?.muteState.hasAuxiliary) {
        remoteTrack = remotePublishedUser.remoteAuxiliaryTrack;
      }
      const prevRemoteConfig = this._remoteVideoConfigMap.get(key);
      if (remoteTrack) {
        // 大小流切换
        if (streamType === 'main' /* TRTCStreamType.Main */ && option && !isUndefined(option.small)) {
          this._room.changeType(option.small, remoteTrack.user.userId);
        }
        await this._updateVideoPlayOption({ view, playOption, track: remoteTrack, prevConfig: prevRemoteConfig });
      }
      deepMerge(prevRemoteConfig, config);
    }
    /**
    * 用于停止远端视频播放。<br>
    * @param {object} config - 远端视频配置
    * @param {string} config.userId - 远端用户 userId，'*' 代表所有用户。
    * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} [config.streamType] - 远端流类型，当 userId 不为 '*' 时，该字段必填。
    * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: 主流（远端用户的摄像头）
    * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: 辅流（远端用户的屏幕分享）
    * @throws {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * // 停止播放所有远端用户
    * await trtc.stopRemoteVideo({ userId: '*' });
    * */
    @validate(validateConfig.TRTC.stopRemoteVideo)
    @createDecorator(next => async function (config) {
      if (config.userId === '*') {
        const promises = [];
        this._room.remotePublishedUserMap.forEach(user => {
          if (this._remoteVideoConfigMap.has(`${user.userId}_${'main' /* TRTCStreamType.Main */}`)) promises.push(this.stopRemoteVideo({ streamType: 'main' /* TRTCStreamType.Main */, userId: user.userId }).catch(() => { }));
          if (this._remoteVideoConfigMap.has(`${user.userId}_${'sub' /* TRTCStreamType.Sub */}`)) promises.push(this.stopRemoteVideo({ streamType: 'sub' /* TRTCStreamType.Sub */, userId: user.userId }).catch(() => { }));
        });
        return Promise.all(promises);
      }
      return next.call(this, config);
    })
    @addAPICallLog({ getRemoteId: config => `${config.userId}_${config.streamType}` })
    async stopRemoteVideo(config) {
      return this._stopRemoteVideo(config);
    }
    @randStop(config => `v${config.userId}${config.streamType}`)
    async _stopRemoteVideo(config, callUnsubscribe = true) {
      const remoteVideoTrackList = [];
      const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
      if (remotePublishedUser) {
        const { muteState, remoteVideoTrack, remoteAuxiliaryTrack } = remotePublishedUser;
        if (config.streamType === 'main' /* TRTCStreamType.Main */) {
          remoteVideoTrack.stop();
          if (muteState.hasVideo) {
            remoteVideoTrackList.push(remoteVideoTrack);
          }
        }
        if (config.streamType === 'sub' /* TRTCStreamType.Sub */) {
          remoteAuxiliaryTrack.stop();
          if (muteState.hasAuxiliary) {
            remoteVideoTrackList.push(remoteAuxiliaryTrack);
          }
        }
      }
      for (const remoteTrack of remoteVideoTrackList) {
        if (callUnsubscribe) {
          await this._room.unsubscribe(remoteTrack);
        }
      }
      this._remoteVideoConfigMap.delete(`${config.userId}_${config.streamType}`);
    }
    /**
    * 静音某个远端用户，并且不再拉取该用户的音频数据。仅对当前用户有效，房间内的其他用户依然可以听到被静音用户的声音。<br>
    *
    * 注意：
    * - 默认情况下，在进房后，SDK 会自动播放远端音频。您可以调用该接口将远端用户静音及取消静音。
    * - 进房时如果传入参数 autoReceiveAudio = false，则不会自动播放远端音频。当需要播放音频时，需要调用该方法（mute 传入 false）播放远端音频。
    * - 在进入房间（enterRoom）之前或之后调用本接口均生效，静音状态在退出房间（exitRoom）之后会被重置为 false。
    * - 如果您希望继续拉取该用户的音频数据，仅仅是不播放，可以调用 setRemoteAudioVolume(userId, 0)
    * @param {string} userId - 远端用户 userId，'*' 代表所有用户。
    * @param {boolean} mute - 是否静音
    * @throws
    * - {@link module:ERROR_CODE.INVALID_PARAMETER INVALID_PARAMETER}
    * - {@link module:ERROR_CODE.INVALID_OPERATION INVALID_OPERATION}
    * - {@link module:ERROR_CODE.OPERATION_FAILED OPERATION_FAILED}
    * - {@link module:ERROR_CODE.OPERATION_ABORT OPERATION_ABORT}
    * @example
    * // 静音所有远端用户
    * await trtc.muteRemoteAudio('*', true);
    * */
    @validate(...validateConfig.TRTC.muteRemoteAudio)
    @addAPICallLog({ getRemoteId: userId => userId })
    async muteRemoteAudio(userId, mute) {
      if (userId === '*') {
        if (mute) {
          await this._stopRemoteAudio({ userId });
        } else {
          const remotePublishedUserList = [...this._room.remotePublishedUserMap.values()];
          for (const remotePublishedUser of remotePublishedUserList) {
            if (remotePublishedUser.muteState.hasAudio) {
              await this._startRemoteAudio({ userId: remotePublishedUser.userId });
            }
          }
        }
      } else {
        if (mute) {
          await this._stopRemoteAudio({ userId });
        } else {
          await this._startRemoteAudio({ userId });
        }
      }
      this._remoteAudioMuteMap.set(userId, mute);
    }
    /**
    * 用于控制远端音频的播放音量。远端用户若重新进房，则需要重新设置音量。<br>
    *
    * - 不支持 iOS Safari
    * @param {string} userId - 远端用户 userId。'*' 代表所有远端用户。
    * @param {number} volume - 音量大小，取值范围为0 - 100，默认值为 100。<br>
    * 自 `v5.1.3+` 版本支持设置 volume 大于100。需注意，设置超过 100 可能有爆音风险。
    * @example
    * await trtc.setRemoteAudioVolume('123', 90);
    * */
    @validateSync(...validateConfig.TRTC.setRemoteAudioVolume)
    @debounce(200, userId => userId)
    @addAPICallLog({ getRemoteId: userId => userId })
    setRemoteAudioVolume(userId, volume) {
      if (userId === '*') {
        const remotePublishedUserList = [...this._room.remotePublishedUserMap.values()];
        for (const remotePublishedUser of remotePublishedUserList) {
          this._updateAudioPlayOption({ playOption: { volume }, track: remotePublishedUser.remoteAudioTrack });
        }
      } else if (userId) {
        const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
        if (remotePublishedUser) {
          this._updateAudioPlayOption({ playOption: { volume }, track: remotePublishedUser.remoteAudioTrack });
        }
      }
    }
    /**
     * 开启插件
     *
     * | pluginName | 插件名称 | 参考教程 | 参数类型 |
     * | --- | --- | --- | --- |
     * | 'AudioMixer' | 背景音乐插件 | {@tutorial 22-advanced-audio-mixer} | [AudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#AudioMixerOptions) |
     * | 'AIDenoiser' | 降噪插件 | {@tutorial 35-advanced-ai-denoiser} | [AIDenoiserOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#AIDenoiserOptions) |
     * | 'CDNStreaming' | CDN混流插件 | {@tutorial 26-advanced-publish-cdn-stream} | [CDNStreamingOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#CDNStreamingOptions) |
     * | 'VirtualBackground' | 虚拟背景插件 | {@tutorial 36-advanced-virtual-background} | [VirtualBackgroundOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#VirtualBackgroundOptions) |
     * | 'Watermark' | 水印插件 | {@tutorial 29-advanced-water-mark} | [WatermarkOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#WatermarkOptions) |
     * | 'BasicBeauty' | 基础美颜插件 | {@tutorial 38-advanced-basic-beauty} | [BasicBeautyOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#BasicBeautyOptions) |
     * | 'SmallStreamAutoSwitcher' | 小流自动切换插件 | {@tutorial 41-advanced-small-stream-auto-switcher} | [SmallStreamAutoSwitcherOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#SmallStreamAutoSwitcherOptions) |
     *
     * @param {PluginName} plugin
     * @param {AudioMixerOptions|AIDenoiserOptions|CDNStreamingOptions|VirtualBackgroundOptions|WatermarkOptions|BasicBeautyOptions|SmallStreamAutoSwitcherOptions} options
     * @returns {Promise<void>}
     */
    @validatePlugin('start')
    @randStart((plugin, options) => plugin.getAlias() + plugin.getGroup(options))
    @addAPICallLog({ namePrefix: plugin => plugin.getName() })
    async startPlugin(plugin, options) {
      // 装饰器改变了参数类型，但是生成声明文件需要原始类型
      return plugin.start(options);
    }
    /**
     * 更新插件
     *
     * | pluginName | 插件名称 | 参考教程 | 参数类型 |
     * | --- | --- | --- | --- |
     * | 'AudioMixer' | 背景音乐插件 | {@tutorial 22-advanced-audio-mixer} | [UpdateAudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#UpdateAudioMixerOptions) |
     * | 'CDNStreaming' | CDN混流插件 | {@tutorial 26-advanced-publish-cdn-stream} | [CDNStreamingOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#CDNStreamingOptions) |
     * | 'VirtualBackground' | 虚拟背景插件 | {@tutorial 36-advanced-virtual-background} | [UpdateVirtualBackgroundOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#UpdateVirtualBackgroundOptions) |
     * | 'BasicBeauty' | 基础美颜插件 | {@tutorial 38-advanced-basic-beauty} | [BasicBeautyOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#BasicBeautyOptions) |
     *
     * @param {PluginName} plugin
     * @param {UpdateAudioMixerOptions|CDNStreamingOptions|UpdateVirtualBackgroundOptions|BasicBeautyOptions} options
     * @returns {Promise<void>}
     */
    @validatePlugin('update')
    @randUpdate((plugin, options) => plugin.getAlias() + plugin.getGroup(options))
    @addAPICallLog({ namePrefix: plugin => plugin.getName() })
    async updatePlugin(plugin, options) {
      // 装饰器改变了参数类型，但是生成声明文件需要原始类型
      return plugin.update(options);
    }
    /**
     * 停止插件
     *
     * | pluginName | 插件名称 | 参考教程 | 参数类型 |
     * | --- | --- | --- | --- |
     * | 'AudioMixer' | 背景音乐插件 | {@tutorial 22-advanced-audio-mixer} | [StopAudioMixerOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#StopAudioMixerOptions) |
     * | 'AIDenoiser' | 降噪插件 | {@tutorial 35-advanced-ai-denoiser} | |
     * | 'CDNStreaming' | CDN混流插件 | {@tutorial 26-advanced-publish-cdn-stream} | [CDNStreamingOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#CDNStreamingOptions) |
     * | 'VirtualBackground' | 虚拟背景插件 | {@tutorial 36-advanced-virtual-background} | |
     * | 'Watermark' | 水印插件 | {@tutorial 29-advanced-water-mark} | |
     * | 'BasicBeauty' | 基础美颜插件 | {@tutorial 38-advanced-basic-beauty} | |
     * | 'SmallStreamAutoSwitcher' | 小流自动切换插件 | {@tutorial 41-advanced-small-stream-auto-switcher} | |
     * @param {PluginName} plugin
     * @param {StopAudioMixerOptions|CDNStreamingOptions|StopVirtualBackgroundOptions|StopWatermarkOptions|StopBasicBeautyOptions|StopSmallStreamAutoSwitcherOptions} options
     * @returns {Promise<void>}
     */
    @validatePlugin('stop')
    @randStop((plugin, options) => plugin.getAlias() + plugin.getGroup(options))
    @addAPICallLog({ namePrefix: plugin => plugin.getName() })
    async stopPlugin(plugin, options) {
      // 装饰器改变了参数类型，但是生成声明文件需要原始类型
      return plugin.stop(options);
    }
    /**
   * 开启或关闭音量大小回调<br>
   *
   * - 开启此功能后，无论房间内是否有人说话，SDK 会定时抛出 {@link module:EVENT.AUDIO_VOLUME TRTC.on(TRTC.EVENT.AUDIO_VOLUME)} 事件，反馈每一个用户的的音量大小评估值。<br>
   *
   * @param {number} [interval=2000] 用于设置音量回调事件定时触发的时间间隔。默认为 2000(ms)，最小值为100(ms)。若设置小于等于0时，则关闭音量大小回调。
   * @param {boolean} [enableInBackground=false] 出于性能的考虑，当页面切换到后台时，SDK 不会抛出音量回调事件。如需在页面切后台时接收音量回调事件，可设置该参数为 true。
   * @memberof TRTC
   * @example
   * trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   *    event.result.forEach(({ userId, volume }) => {
   *        const isMe = userId === ''; // 当 userId 为空串时，代表本地麦克风音量。
   *        if (isMe) {
   *            console.log(`my volume: ${volume}`);
   *        } else {
   *            console.log(`user: ${userId} volume: ${volume}`);
   *        }
   *    })
   * });
   *
   * // 开启音量回调，并设置每 1000ms 触发一次事件
   * trtc.enableAudioVolumeEvaluation(1000);
   *
   * // 如需关闭音量回调，传入 interval 值小于等于0即可
   * trtc.enableAudioVolumeEvaluation(-1);
   */
    @validateSync(...validateConfig.TRTC.enableAudioVolumeEvaluation)
    enableAudioVolumeEvaluation(interval = 2000, enableInBackground = false) {
      this._room.enableAudioVolumeEvaluation(interval, enableInBackground);
    }
    /**
    * 监听 TRTC 事件<br><br>
    * 详细事件列表请参见：{@link module:EVENT TRTC.EVENT}
    *
    * @param {string} eventName 事件名
    * @param {function} handler 事件回调函数
    * @param {context} context 上下文
    * @memberof TRTC
    * @example
    * trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
    *   // REMOTE_VIDEO_AVAILABLE event handler
    * });
    */
    on(event, handler, context) {
      super.on(event, handler, context);
      this._eventListened.add(event);
      return this;
    }
    /**
     * 取消事件监听<br>
     *
     * @param {string} eventName 事件名，传入通配符 '*' 会解除所有事件监听。
     * @param {function} handler 事件回调函数
     * @param {context} context 上下文
     * @memberof TRTC
     * @example
     * trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, function peerJoinHandler(event) {
     *   // REMOTE_USER_ENTER event handler
     *   console.log('remote user enter');
     *
     *   trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, peerJoinHandler);
     * });
     *
     * // 解除所有事件绑定
     * trtc.off('*');
     */
    // @ts-ignore
    off(event, handler, context) {
      if (event === '*') {
        this._eventListened.clear();
        this.removeAllListeners();
      } else {
        // @ts-ignore
        super.off(event, handler, context);
      }
      return this;
    }
    /**
     * 获取音频轨道
     *
     * @returns {MediaStreamTrack?} 音频轨道
     * @param {Object|string} [config] 不传则获取本地的 audioTrack
     * @param {string} [config.userId] 不传或传空串，代表获取本地的 audioTrack。传远端用户的 userId，代表获取远端用户的 audioTrack。
     * @param {STREAM_TYPE_MAIN|STREAM_TYPE_SUB} [config.streamType] - stream type:
     * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: 主流（用户麦克风）（默认值）
     * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: 辅流（用户屏幕分享），只对获取本地屏幕分享 audioTrack 有效，因为一个远端用户只有一个 audioTrack， 不区分 Main 和 Sub。
     * @param {boolean} [config.processed=false] - 是否获取处理（包括 AI 降噪、增益、混音等）后的音频轨道,默认为 false。
     * @example
     * // Version before v5.4.3
     * trtc.getAudioTrack(); // 获取本地麦克风 audioTrack
     * trtc.getAudioTrack('remoteUserId'); // 获取远端 audioTrack
     *
     * // Since v5.4.3+
     * trtc.getAudioTrack({ streamType: TRTC.STREAM_TYPE_SUB }); // 获取本地屏幕分享 audioTrack
     *
     * // Since v5.8.2+
     * trtc.getAudioTrack({ processed: true }); // 获取处理后的 audioTrack
     * @memberof TRTC
     */
    getAudioTrack(userId) {
      if (userId) {
        const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
        if (remotePublishedUser) return remotePublishedUser.remoteAudioTrack.mediaTrack;
      } else if (this._localAudioTrack) {
        return this._localAudioTrack.mediaTrack;
      }
      return null;
    }
    /**
     * 获取视频轨道
     *
     * @param {string} [config] 不传则获取本地摄像头 videoTrack
     * @param {string} [config.userId] 不传或传空串，代表获取本地的 videoTrack。传远端用户的 userId，代表获取远端用户的 videoTrack。
     * @param {STREAM_TYPE_MAIN|STREAM_TYPE_SUB} [config.streamType] - 远端流类型：
     * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: 主流（用户的摄像头）（默认值）
     * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: 辅流（用户的屏幕分享）
     * @returns {MediaStreamTrack|null} 视频轨道
     * @param {boolean} [config.processed=false] - 是否获取处理（虚拟背景、镜像、水印、旋转等）后的视频轨道,默认为 false。
     * @memberof TRTC
     * @example
     * // 获取本地摄像头 videoTrack
     * const videoTrack = trtc.getVideoTrack();
     * // 获取本地屏幕分享 videoTrack
     * const screenVideoTrack = trtc.getVideoTrack({ streamType: TRTC.TYPE.STREAM_TYPE_SUB });
     * // 获取远端用户的主流 videoTrack
     * const remoteMainVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
     * // 获取远端用户的辅流 videoTrack
     * const remoteSubVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_SUB });
     * // Since v5.8.2+, 获取处理后的 videoTrack
     * const processedVideoTrack = trtc.getVideoTrack({ processed: true });
    */
    getVideoTrack(config = { userId: '', streamType: 'main' /* TRTCStreamType.Main */ }) {
      const { userId = '', streamType = 'main' /* TRTCStreamType.Main */ } = config;
      if (userId === '') {
        if (streamType === 'main' /* TRTCStreamType.Main */ && this._localVideoTrack) {
          return this._localVideoTrack.mediaTrack;
        }
        if (streamType === 'sub' /* TRTCStreamType.Sub */ && this._localScreenTrack) {
          return this._localScreenTrack.mediaTrack;
        }
      } else {
        const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
        if (remotePublishedUser) {
          return streamType === 'main' /* TRTCStreamType.Main */ ? remotePublishedUser.remoteVideoTrack.mediaTrack : remotePublishedUser.remoteAuxiliaryTrack.mediaTrack;
        }
      }
      return null;
    }
    /**
   * 获取视频帧 <br>
   * 注意事项: Stream 必须经过播放才可以获取视频帧，如果没有播放将会返回空字符串
   * @param {string} config.userId - Remote user ID
   * @param {TRTC.TYPE.STREAM_TYPE_MAIN|TRTC.TYPE.STREAM_TYPE_SUB} config.streamType - 获取视频帧的 stream 类型
   * - {@link module:TYPE.STREAM_TYPE_MAIN TRTC.TYPE.STREAM_TYPE_MAIN}: Main stream 主流
   * - {@link module:TYPE.STREAM_TYPE_SUB TRTC.TYPE.STREAM_TYPE_SUB}: Sub stream 辅流
   * @since 5.4.0
   * @example
   * // 获取本地主流视频帧
   * trtc.getVideoSnapshot()
   * // 获取本地辅流视频帧
   * trtc.getVideoSnapshot({streamType:TRTC.TYPE.STREAM_TYPE_SUB})
   * // 获取远端主流视频帧
   * let frameData = trtc.getVideoSnapshot({userId: 'remote userId', streamType:TRTC.TYPE.STREAM_TYPE_MAIN})
   *
   * // 显示视频帧
   * const img = document.createElement('img');
   * img.width = '640';
   * img.height = '480';
   * img.src = frameData;
   * document.body.appendChild(img);
   * @memberof TRTC
   */
    getVideoSnapshot(config) {
      return '';
    }

    setCurrentSpeaker(speakerId) {
      this._localAudioTrack?.setAudioOutput(speakerId);
      this._room.remotePublishedUserMap.forEach(remotePublishedUser => remotePublishedUser.remoteAudioTrack.setAudioOutput(speakerId));
    }
    @randStart(config => `a${config.userId}`, () => true)
    async _startRemoteAudio(config) {
      const { userId, option } = config;
      if (this._remoteAudioConfigMap.has(userId)) {
        this._log.warn(`remote audio has already started. userId:${userId}`);
        return;
      }
      const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
      if (!remotePublishedUser) return;
      const playOption = {};
      if (option) {
        if (!isUndefined(option.volume)) {
          playOption.volume = option.volume;
        }
      }
      const remoteTrack = remotePublishedUser.remoteAudioTrack;
      await this._room.subscribe(remoteTrack);
      await this._updateAudioPlayOption({ playOption, track: remoteTrack });
      this._remoteAudioConfigMap.set(userId, config);
    }
    @createDecorator(next => async function (config) {
      if (config.userId === '*') {
        return Promise.all([...this._room.remotePublishedUserMap.values()].map(user => this._stopRemoteAudio({ ...config, userId: user.userId }).catch(() => { })));
      }
      return next.call(this, config);
    })
    @randStop(config => `a${config.userId}`)
    async _stopRemoteAudio(config, callUnsubscribe = true) {
      const remotePublishedUser = this._room.remotePublishedUserMap.get(config.userId);
      if (remotePublishedUser) {
        remotePublishedUser.remoteAudioTrack.stop();
        if (remotePublishedUser.muteState.hasAudio) {
          if (callUnsubscribe) {
            await this._room.unsubscribe(remotePublishedUser.remoteAudioTrack);
          }
        }
      }
      this._remoteAudioConfigMap.delete(`${config.userId}`);
    }
    // 更新 playOption
    async _updateVideoPlayOption({ view, playOption, track, prevConfig }) {
      if (isUndefined(view) && prevConfig && prevConfig.view && !isEmpty(playOption)) {
        let container;
        if (isArray(prevConfig.view)) {
          container = prevConfig.view;
        } else {
          container = getContainerFromElement(prevConfig.view);
        }
        if (container) {
          await track.play(container, playOption);
        }
      }
      if (!isUndefined(view)) {
        let container;
        if (isArray(view)) {
          container = view;
        } else {
          container = getContainerFromElement(view);
        }
        if (container) {
          await track.play(container, playOption);
        } else {
          track.stop();
        }
      }
    }
    async _updateAudioPlayOption({ playOption = {}, track, prevConfig }) {
      if (!track.isPlayCalled) {
        try {
          await track.play(null, playOption);
        } catch (error) {
          // 自动播放失败不抛出给接入侧，接入侧统一监听 AUTOPLAY_FAILED 处理。
        }
      }
      if (!isUndefined(playOption.muted)) {
        track.setPlayerMute(playOption.muted);
      }
      if (!isUndefined(playOption.volume)) {
        track.setAudioVolume(playOption.volume / 100);
      }
    }
    _checkTrackToPublish() {
      const needPublishTrack = [];
      if (this._localAudioConfig?.publish && this._localAudioTrack) needPublishTrack.push(this._localAudioTrack);
      if (this._localVideoConfig?.publish && this._localVideoTrack) needPublishTrack.push(this._localVideoTrack);
      if (this._localScreenConfig?.publish) {
        if (this._localScreenTrack) {
          needPublishTrack.push(this._localScreenTrack);
        }
        if (this._localScreenAudioTrack) {
          needPublishTrack.push(this._localScreenAudioTrack);
        }
      }
      if (needPublishTrack.length === 0) return;
      return this._room.publish(...needPublishTrack).catch(() => { });
    }
    _handleReceiveMode() {
      // 自动拉音频
      if (this._room.autoReceiveAudio) {
        eventManager.create(this, this)
          .add(TRTCEvent.REMOTE_AUDIO_AVAILABLE, async ({ userId }) => {
            // 用户在进房前调用了 muteRemoteAudio，则不自动拉音频。
            if (this._remoteAudioMuteMap.get('*') || this._remoteAudioMuteMap.get(userId)) {
              return;
            }
            const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
            if (remotePublishedUser) {
              await this._room.subscribe(remotePublishedUser.remoteAudioTrack).catch(() => { });
              await this._updateAudioPlayOption({ track: remotePublishedUser.remoteAudioTrack }).catch(() => { });
              this._remoteAudioConfigMap.set(userId, { userId });
            }
          });
      }
      // 自动拉视频
      if (this._room.autoReceiveVideo) {
        eventManager.create(this, this)
          .add(TRTCEvent.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
            const remotePublishedUser = this._room.remotePublishedUserMap.get(userId);
            if (remotePublishedUser) {
              if (streamType === 'main' /* TRTCStreamType.Main */) {
                this._room.subscribe(remotePublishedUser.remoteVideoTrack).catch(() => { });
              } else if (streamType === 'sub' /* TRTCStreamType.Sub */) {
                this._room.subscribe(remotePublishedUser.remoteAuxiliaryTrack).catch(() => { });
              }
            }
          });
      }
    }
    @randStop('room')
    async _exitRoom() {
      if (this._room.isJoined) {
        await this._room.leave();
      }
      [...this._remoteAudioConfigMap.keys()].forEach(userId => {
        this._stopRemoteAudio({ userId }).catch();
      });
      [...this._remoteVideoConfigMap.keys()].forEach(key => {
        const streamType = key.includes('main' /* TRTCStreamType.Main */) ? 'main' /* TRTCStreamType.Main */ : 'sub' /* TRTCStreamType.Sub */;
        const userId = key.split(`_${streamType}`)[0];
        if (userId) {
          this._stopRemoteVideo({ userId, streamType }).catch();
        }
      });
      this._remoteVideoConfigMap.clear();
      this._remoteAudioConfigMap.clear();
      this._remoteAudioMuteMap.clear();
      this._room.remotePublishedUserMap.forEach(remotePublisherUser => {
        eventManager.remove(remotePublisherUser.remoteAudioTrack);
        eventManager.remove(remotePublisherUser.remoteVideoTrack);
        eventManager.remove(remotePublisherUser.remoteAuxiliaryTrack);
      });
      eventManager.remove(this);
    }
    @randStop('screen')
    async _stopScreenShare() {
      if (!this._localScreenTrack) return;
      if (this._room.isJoined) {
        const trackListToUnpublish = [this._localScreenTrack];
        if (this._localScreenAudioTrack) trackListToUnpublish.push(this._localScreenAudioTrack);
        // unpublish 不会失败，catch error
        await this._room?.unpublish(...trackListToUnpublish).catch(() => { });
      }
      this._localScreenTrack.stop();
      this._localScreenTrack.close();
      this._localScreenAudioTrack?.stop();
      this._localScreenAudioTrack?.close();
      eventManager.remove(this._localScreenTrack);
      this._localScreenTrack = null;
      this._localScreenAudioTrack = null;
      this._localScreenConfig = null;
    }
    /**
     * 发送 SEI Message <br>
     *
     * 视频帧的头部有一个叫做 SEI 的头部数据块，该接口的原理就是利用这个被称为 SEI 的头部数据块，将您要发送的自定义信令嵌入其中，使其同视频帧一并发送出去。
     * SEI 消息可以伴随着视频帧一直传输到直播 CDN 上。
     *
     * 适用场景：视频画面的精准布局、歌词同步、直播答题等。
     * 调用时机：在 {@link TRTC#startLocalVideo trtc.startLocalVideo} （如果设置了 toSubStream 为 true，则{@link TRTC#startLocalScreen trtc.startLocalScreen}） 后调用。
     *
     *
     * 注意：
     * 1. 单次最大发送1KB(Byte)，每秒最大调用次数30次，每秒最多发送8KB。
     * 2. 支持的浏览器： Chrome 86+, Edge 86+, Opera 72+，Safari 15.4+，Firefox 117+。注意：自 v5.8.0 起支持 Safari 和 Firefox。
     * 3. 由于 SEI 跟随视频帧一起发送，视频帧有丢失的可能，因此 SEI 也可能丢失。可在使用频次限制范围内，增加发送次数，业务侧需要在接收端做消息去重。
     * 4. 没有推视频流（如果设置了 toSubStream 为 true 则是辅流）时，无法发送 SEI；没有订阅视频流时，无法接收 SEI。
     * 5. 仅支持 H264 编码器发送 SEI。
     * @see {@link module:EVENT.SEI_MESSAGE TRTC.EVENT.SEI_MESSAGE}
     * @since v5.3.0
     * @param {ArrayBuffer} buffer 待发送的 SEI 数据
     * @param {Object=} options
     * @param {Number} options.seiPayloadType 设置 SEI payload type。SDK 默认使用自定义 payloadType 243，业务侧可使用该参数将 payloadType 设置为标准的 5。当业务侧使用 5 payloadType 时，需按照规范确保 `buffer` 的前16字节是业务侧自定义的 uuid。
     * @param {Boolean} [options.toSubStream=false] 设置通过辅流传输 SEI 数据。必须先调用 trtc.startLocalScreen 后才能生效。支持 v5.7.0+。
     * @example
     * // 1. 开启 SEI
     * const trtc = TRTC.create({
     *    enableSEI: true // 开启 SEI 收发功能
     * })
     *
     * // 2. 推流端发送 SEI
     * try {
     *  await trtc.enterRoom({
     *   userId: 'user_1',
     *   roomId: 12345,
     * })
     *  await trtc.startLocalVideo();
     *  const unit8Array = new Uint8Array([1, 2, 3]);
     *  trtc.sendSEIMessage(unit8Array.buffer);
     *  // 消息已通过限制，等待后续视频帧发送。
     * } catch(error) {
     *  // 发送失败，可能原因：当前浏览器不支持、参数校验未通过、未推流、没推视频流、调用次数超出限制、使用非 H264 编码器等。
     *  console.warn(error);
     * }
     *
     * // 3. 拉流端接收 SEI
     * trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
     *  console.warn(`收到 ${event.userId} 的 sei ${event.data}`);
     * })
     */
    @validate(validateConfig.TRTC.sendSEIMessage)
    @limitCallFrequency({
      timesInSecond: 30,
      maxSizeInSecond: 8000,
      getSize: (...args) => args[0].byteLength
    })
    sendSEIMessage(buffer, options) {
      this._room.sendSEI(buffer, options || { seiPayloadType: 243 });
    }

    /**
     * 发送自定义消息，消息会广播给房间内的所有用户。 <br>
     *
     * Note:
     *
     * 1. 只有 {@link module:TYPE.ROLE_ANCHOR TRTC.TYPE.ROLE_ANCHOR} 角色可以调用 sendCustomMessage
     * 2. 需要在进房成功后才能发送自定义消息
     * 3. SDK 会保序和尽可能可靠地发送自定义消息，但是在弱网环境下是有可能丢消息的。接收端也会按顺序收到消息。
     * @since v5.6.0
     * @see {@link module:EVENT.CUSTOM_MESSAGE TRTC.EVENT.CUSTOM_MESSAGE} 监听该事件以接收消息
     * @param {object} message
     * @param {number} message.cmdId 消息 Id，整数，取值范围 1-10。你可以为不同类型的消息设置不同的 cmdId，以降低传输延迟。
     * @param {ArrayBuffer} message.data - 消息内容 <br/>
     * - 一次最大发送 1KB(Byte) 数据。
     * - 每秒限频调用 30 次，每秒最多发送 8KB 数据。
     * @example
     * // send custom message
     * trtc.sendCustomMessage({
     *   cmdId: 1,
     *   data: new TextEncoder().encode('hello').buffer
     * });
     *
     * // receive custom message
     * trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, event => {
     *    // event.userId: 远端发消息的 userId
     *    // event.cmdId: 您自定义的消息 Id
     *    // event.seq: 消息的序号
     *    // event.data: 消息内容，ArrayBuffer 类型
     *    console.log(`received custom msg from ${event.userId}, message: ${new TextDecoder().decode(event.data)}`)
     * })
     */
    sendCustomMessage(message) {}
    /**
     * call experimental API
     *
     * | APIName | name | param |
     * | --- | --- | --- |
     * | 'enableAudioFrameEvent' | Config the pcm data of Audio Frame Event | [EnableAudioFrameEventOptions](https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/global.html#EnableAudioFrameEventOptions) |
     * @param {string} name
     * @param {EnableAudioFrameEventOptions} options
     * @example
     * // 监听远端用户 user_A 的音频数据，默认 pcm 数据为 48kHZ，单声道
     * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: 'user_A'})
     * // 监听所有远端音频数据，并设置 pcm 数据为 16kHZ，双声道
     * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: '*', sampleRate: 16000, channelCount: 2 })
     * // 设置本地麦克风数据回调的 MessagePort
     * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: true, userId: '', port })
     * // 取消监听本地麦克风数据
     * await trtc.callExperimentalAPI('enableAudioFrameEvent', { enable: false, userId: '' })
     */
    async callExperimentalAPI(name, options) {}

    static EVENT = TRTCEvent;
    static ERROR_CODE = ErrorCode;
    static TYPE = TRTCType;
    static frameWorkType = 30;
    /**
     * 设置日志输出等级
     * <br>
     * 建议在开发测试阶段设置为 DEBUG 等级，该日志等级包含详细的提示信息。
     * 默认输出 INFO 日志等级，该日志等级包含 SDK 主要功能的日志信息。
     *
     * @param {0-5} [level] 日志输出等级 0: TRACE 1: DEBUG 2: INFO 3: WARN 4: ERROR 5: NONE
     * @param {boolean} [enableUploadLog=true] 是否开启日志上传，默认开启。不建议关闭，关闭后将影响问题排障。
     * @example
     * // 输出 DEBUG 以上日志等级
     * TRTC.setLogLevel(1);
     */
    static setLogLevel(level, enableUploadLog) {
      loggerManager.setLogLevel(level);
      if (!isUndefined(enableUploadLog)) {
        enableUploadLog ? loggerManager.enableUploadLog() : loggerManager.disableUploadLog();
      }
    }
    /**
     * 检测 TRTC Web SDK 是否支持当前浏览器
     *
     * - 参考：{@tutorial 05-info-browser}。
     * @example
     * TRTC.isSupported().then((checkResult) => {
     *    if(!checkResult.result) {
     *       console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
     *       // SDK 不支持当前浏览器，引导用户使用最新版的 Chrome 浏览器。
     *    }
     *    const { isBrowserSupported, isWebRTCSupported, isMediaDevicesSupported, isH264EncodeSupported, isH264DecodeSupported } = checkResult.detail;
     *    // 不同的业务场景对检测的粒度要求不一样，有点场景只需推流、有的场景只需拉流
     *    // 判断浏览器是否支持采集摄像头、麦克风推流。
     *    const isPublishAudioStreamSupported = isBrowserSupported && isWebRTCSupported && isMediaDevicesSupported;
     *    const isPublishVideoStreamSupported = isBrowserSupported && isWebRTCSupported && isMediaDevicesSupported && isH264EncodeSupported;
     *    // 判断浏览器是否支持拉流。
     *    const isRemoteAudioStreamSupported = isBrowserSupported && isWebRTCSupported;
     *    const isRemoteVideoStreamSupported = isBrowserSupported && isWebRTCSupported && isH264DecodeSupported;
     * });
     *
     * @returns {Promise.<object>} Promise 返回检测结果
     * | Property                                   | Type    | Description                         |
     * |--------------------------------------------|---------|-------------------------------------|
     * | checkResult.result                         | boolean | 检测结果, true 则代表当前环境支持最基本的采集摄像头、麦克风进行推拉流的能力。    |
     * | checkResult.detail.isBrowserSupported      | boolean | 当前浏览器是否是 SDK 支持的浏览器        |
     * | checkResult.detail.isWebRTCSupported       | boolean | 当前浏览器是否支持 WebRTC，若不支持则无法使用 WebRTC 进行推拉流 |
     * | checkResult.detail.isWebCodecsSupported    | boolean | 当前浏览器是否支持 WebCodecs            |
     * | checkResult.detail.isMediaDevicesSupported | boolean | 当前浏览器是否支持采集麦克风和摄像头     |
     * | checkResult.detail.isScreenShareSupported | boolean | 当前浏览器是否支持屏幕分享    |
     * | checkResult.detail.isSmallStreamSupported | boolean | 当前浏览器是否支持小流     |
     * | checkResult.detail.isH264EncodeSupported   | boolean | 当前浏览器上行是否支持 H264 编码         |
     * | checkResult.detail.isH264DecodeSupported   | boolean | 当前浏览器下行是否支持 H264 解码         |
     * | checkResult.detail.isVp8EncodeSupported    | boolean | 当前浏览器上行是否支持 VP8 编码          |
     * | checkResult.detail.isVp8DecodeSupported    | boolean | 当前浏览器下行是否支持 VP8 解码          |
     */
    static isSupported() {
      return checkSystemRequirementsInternal();
    }
    /**
    * 返回摄像头设备列表
    * <br>
    * **Note**
    * - 该接口不支持在 http 协议下使用，请使用 https 协议部署您的网站。{@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security Privacy and security}
    * - 调用该接口可能会短暂打开摄像头来获取摄像头权限，以保证正常获取摄像头列表，随后 SDK 会自动释放摄像头采集。
    * - 可以调用浏览器原生接口 [getCapabilities](https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities) 获取摄像头支持的最大分辨率、帧率、移动端设备区分前后置摄像头等。该接口支持 Chrome 67+, Edge 79+, Safari 17+, Opera 54+ 浏览器。
    *   - 华为手机后摄可能会采集到长焦摄像头，此时可通过该接口获取到支持分辨率最大的后摄，一般手机的主摄的分辨率是最大的。
    * @param {boolean} [requestPermission=true] `Since v5.6.3`. 是否请求获取设备权限。如果为 true，调用该接口可能会短暂打开摄像头来获取摄像头权限，以保证正常获取摄像头列表，随后 SDK 会自动释放采集。
    * @example
    * const cameraList = await TRTC.getCameraList();
    * if (cameraList[0] && cameraList[0].getCapabilities) {
    *   const { width, height, frameRate, facingMode } = cameraList[0].getCapabilities();
    *   // 摄像头支持的最大分辨率、帧率。
    *   console.log(width.max, height.max, frameRate.max);
    *
    *   // 在移动端区分该摄像头是前置还是后置。
    *   if (facingMode) {
    *     if (facingMode[0] === 'user') {
    *       // 前置摄像头
    *     } else if (facingMode[0] === 'environment') {
    *       // 后置摄像头
    *     }
    *   }
    * }
    * @returns {Promise.<MediaDeviceInfo[]>} Promise 返回 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo} 数组
    */
    static getCameraList(requestPermission = true) {
      return getCameras();
    }
    /**
    * 返回麦克风设备列表
    * <br>
    * **Note**
    * - 该接口不支持在 http 协议下使用，请使用 https 协议部署您的网站。{@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security Privacy and security}
    * - 可以调用浏览器原生接口 [getCapabilities](https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities) 获取麦克风能力信息，例如支持的最大声道数等。该接口支持 Chrome 67+, Edge 79+, Safari 17+, Opera 54+ 浏览器。
    * - Android 端一般会有多个麦克风，label 列表为： ['default', 'Speakerphone', 'Headset earpiece']，如果在 trtc.startLocalAudio 时，不指定麦克风，浏览器默认麦克风可能会采集到 Headset earpiece，此时声音会从听筒放出。若需要通过扬声器外放，则需要指定采集 label 为 Speakerphone 的麦克风。
    * @param {boolean} [requestPermission=true] `Since v5.6.3`. 是否请求获取设备权限。如果为 true，调用该接口可能会短暂打开麦克风来获取麦克风权限，以保证正常获取麦克风列表，随后 SDK 会自动释放麦克风采集。
    * @example
    * const microphoneList = await TRTC.getMicrophoneList();
    * if (microphoneList[0] && microphoneList[0].getCapabilities) {
    *   const { channelCount } = microphoneList[0].getCapabilities();
    *   console.log(channelCount.max);
    * }
    * @returns {Promise.<MediaDeviceInfo[]>} Promise 返回 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo} 数组
    */
    static getMicrophoneList(requestPermission = true) {
      return getMicrophones();
    }
    /**
    * 返回扬声器设备列表
    *
    * 不支持移动端设备
    * <br>
    * @param {boolean} [requestPermission=true] `Since v5.6.3`. 是否请求获取设备权限。如果为 true，调用该接口可能会短暂打开麦克风来获取麦克风权限，以保证正常获取扬声器列表，随后 SDK 会自动释放麦克风采集。
    *
    * @returns {Promise.<MediaDeviceInfo[]>} Promise 返回 {@link https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo|MediaDeviceInfo} 数组
    */
    static getSpeakerList(requestPermission = true) {
      return getSpeakers();
    }
    /**
     * 设置当前音频播放的扬声器。
     *
     *  **注意**
     *  - 仅支持 PC 和安卓端
     *  - 安卓端要求：
     *    - SDK 版本号需大于等于 5.9.0
     *    - 支持切换扬声器和听筒，即传入 TRTC.TYPE.SPEAKER 或 TRTC.TYPE.HEADSET
     *    - 需要先调用 {@link TRTC#startLocalAudio startLocalAudio()} 采集麦克风
     *
     * @param {string | TRTC.TYPE.SPEAKER | TRTC.TYPE.HEADSET} speakerId 扬声器 ID，支持传入扬声器 ID 或 TRTC.TYPE.SPEAKER 或 TRTC.TYPE.HEADSET
     * @example
     * // 针对 PC
     * TRTC.setCurrentSpeaker('你的扬声器ID');
     *
     * // 针对安卓设备（sdk 版本号 >= 5.9.0）
     * TRTC.setCurrentSpeaker(TRTC.TYPE.SPEAKER); // 切换扬声器
     * TRTC.setCurrentSpeaker(TRTC.TYPE.HEADSET); // 或切换听筒
    */
    static async setCurrentSpeaker(speakerId) {
      const speakerList = await getSpeakers();
      speakerList.forEach(speaker => {
        if (speaker.deviceId === speakerId) {
          trtcInstanceSet.forEach(trtc => {
            trtc.setCurrentSpeaker(speakerId);
            trtc.emit(TRTCEvent.DEVICE_CHANGED, { type: TRTCDeviceType.Speaker, action: TRTCDeviceAction.Active, device: speaker });
          });
          lastSpeakerDeviceInfo = speaker;
        }
      });
    }
}
export default TRTC;
