
>!本文适用于4.x.x版本的 TRTC Web SDK。

## 错误码定义

| Key                         | 错误码 | 描述                   |
| --------------------------- | ------ | ---------------------- |
| INVALID_PARAMETER           | 0x1000 | 无效参数               |
| INVALID_OPERATION           | 0x1001 | 非法操作               |
| SIGNAL_CAHNNEL_SETUP_FAILED | 0x4001 | 信令通道建立失败       |
| SIGNAL_CHANNEL_ERROR        | 0x4002 | 信令通道错误           |
| ICE_TRANSPORT_ERROR         | 0x4003 | ICE Transport 连接错误 |
| JOIN_ROOM_FAILED            | 0x4004 | 进房失败               |
| CREATE_OFFER_FAILED         | 0x4005 | 创建 sdp offer 失败    |
| CLIENT_BANNED               | 0x4040 | 用户被踢出房间         |
| SERVER_TIMEOUT              | 0x4041 | 媒体传输服务超时       |
| SUBSCRIPTION_TIMEOUT        | 0x4042 | 远端流订阅超时         |
| UNKOWN                      | 0xFFFF | 未知错误               |

## 账号相关错误信息

| 错误码 | 错误类型 | 描述                                                                                                           |
| :----- | :------- | :------------------------------------------------------------------------------------------------------------- |
| 70001  | 帐号系统 | userSig 过期，请尝试重新生成。如果是刚生成就过期，请检查有效期填写的是否过小或者误填为0                       |
| 70002  | 帐号系统 | userSig 长度为0，请确认签名计算是否正确，访问 sign_src 获取计算签名的傻瓜式源码，核对参数，确保签名计算正确性 |
| 70003  | 帐号系统 | userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断                            |
| 70004  | 帐号系统 | userSig 校验失败，请确认下 userSig 内容是否被截断，例如缓冲区长度不够导致的内容截断                             |
| 70005  | 帐号系统 | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
| 70006  | 帐号系统 | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
| 70007  | 帐号系统 | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
| 70008  | 帐号系统 | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
| 70009  | 帐号系统 | 用业务公钥验证 userSig 失败，请确认生成的 userSig 使用的私钥和 sdkAppId 是否对应                               |
| 70010  | 帐号系统 | userSig 校验失败，通过工具来验证生成的 userSig 是否正确                                                        |
| 70013  | 帐号系统 | userSig 中 userId 与请求时的 userId 不匹配，请检查登录时填写的 userId 与 userSig 中的是否一致                  |
| 70014  | 帐号系统 | userSig 中 sdkAppId 与请求时的 sdkAppId 不匹配，请检查登录时填写的 sdkAppId 与 userSig 中的是否一致            |
| 70015  | 帐号系统 | 未找到该 sdkAppId 和帐号类型对应的验证方式，请确认是否已进行帐号集成操作                                       |
| 70016  | 帐号系统 | 拉取到的公钥长度为0，请确认是否已上传公钥，如果是重新上传的公钥需要十分钟后再尝试                             |
| 70017  | 帐号系统 | 内部第三方票据验证超时，请重试，如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                      |
| 70018  | 帐号系统 | 内部验证第三方票据失败                                                                                         |
| 70019  | 帐号系统 | 通过 HTTPS 方式验证的票据字段为空，请正确填写 userSig                                                          |
| 70020  | 帐号系统 | sdkAppId 未找到，请确认是否已在腾讯云上配置                                                                    |
| 70052  | 帐号系统 | userSig 已经失效，请重新生成，再次尝试                                                                         |
| 70101  | 帐号系统 | 请求包信息为空                                                                                                 |
| 70102  | 帐号系统 | 请求包帐号类型错误                                                                                             |
| 70103  | 帐号系统 | 电话号码格式错误                                                                                               |
| 70104  | 帐号系统 | 邮箱格式错误                                                                                                   |
| 70105  | 帐号系统 | TLS 帐号格式错误                                                                                               |
| 70106  | 帐号系统 | 非法帐号格式类型                                                                                               |
| 70107  | 帐号系统 | userId 没有注册                                                                                                |
| 70113  | 帐号系统 | 批量数量不合法                                                                                                 |
| 70114  | 帐号系统 | 安全原因被限制                                                                                                 |
| 70115  | 帐号系统 | uin 不是对应 sdkAppId 的开发者 uin                                                                             |
| 70140  | 帐号系统 | sdkAppId 和 acctype 不匹配                                                                                     |
| 70145  | 帐号系统 | 帐号类型错误                                                                                                   |
| 70169  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70201  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70202  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70203  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70204  | 帐号系统 | sdkAppId 没有对应的 acctype                                                                                    |
| 70205  | 帐号系统 | 查找 acctype 失败，请重试                                                                                      |
| 70206  | 帐号系统 | 请求中批量数量不合法                                                                                           |
| 70207  | 帐号系统 | 内部错误，请重试                                                                                               |
| 70208  | 帐号系统 | 内部错误，请重试                                                                                               |
| 70209  | 帐号系统 | 获取开发者 uin 标志失败                                                                                        |
| 70210  | 帐号系统 | 请求中 uin 为非开发者 uin                                                                                      |
| 70211  | 帐号系统 | 请求中 uin 非法                                                                                                |
| 70212  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70213  | 帐号系统 | 访问内部数据失败，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                            |
| 70214  | 帐号系统 | 验证内部票据失败                                                                                               |
| 70221  | 帐号系统 | 登录状态无效，请使用 UserSig 重新鉴权                                                                          |
| 70222  | 帐号系统 | 内部错误，请重试                                                                                               |
| 70225  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70231  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70236  | 帐号系统 | 验证 user signature 失败                                                                                       |
| 70308  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70346  | 帐号系统 | 票据校验失败。                                                                                                 |
| 70347  | 帐号系统 | 票据因过期原因校验失败                                                                                         |
| 70348  | 帐号系统 | 内部错误，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70362  | 帐号系统 | 内部超时，请重试。如果多次重试仍不成功，请联系 TLS 帐号支持，QQ：3268519604                                    |
| 70401  | 帐号系统 | 内部错误，请重试                                                                                               |
| 70402  | 帐号系统 | 参数非法。请检查必填字段是否填充，或者字段的填充是否满足协议要求                                               |
| 70403  | 帐号系统 | 发起操作者不是 App 管理员，没有权限操作                                                                        |
| 70050  | 帐号系统 | 因失败且重试次数过多导致被限制，请检查票据是否正确，一分钟之后再试                                             |
| 70051  | 帐号系统 | 帐号已被拉入黑名单，请联系 TLS 帐号支持，QQ：3268519604                                                        |
