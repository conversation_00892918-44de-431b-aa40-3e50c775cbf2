
# TRTC Web SDK

## Introduction

TRTC Web SDK is the Web SDK of Tencent Cloud's real-time audio and video communication solution. It is a JavaScript library loaded through HTML web pages. Developers can use the APIs provided by TRTC Web SDK to establish connections, control real-time audio and video calls or live streaming services.

Currently, TRTC Web SDK mainly supports Chrome M58+ and Safari browsers.

```
Please be sure to use HTTPS protocol or localhost to deploy your Web App, otherwise the error of navigator.mediaDevices not found will occur!
```

## Basic Working Principle

TRTC Web SDK is a real-time audio and video call solution based on WebRTC. It establishes a signaling channel with Tencent Cloud's real-time audio and video backend server through WebSocket. The signaling channel is mainly used for signaling communication such as user entering and exiting the room, media capability exchange, and room user status change notification; audio and video data transmission is performed through WebRTC's RTP/RTCP.

## API Overview

- {@link TRTC TRTC} is the main entry point of the entire SDK, providing functions such as creating TRTC objects {@link TRTC.create TRTC.create}, obtaining camera lists, microphone lists, checking browser compatibility, controlling log level and log upload.
- {@link TRTC trtc} object created by {@link TRTC.create TRTC.create}, providing the core capabilities of real-time audio and video calls:
  - Enter room {@link TRTC#enterRoom enterRoom()}
  - Exit room {@link TRTC#exitRoom exitRoom()}
  - Publish/preview local video {@link TRTC#startLocalVideo startLocalVideo()}
  - Publish/preview local audio {@link TRTC#startLocalAudio startLocalAudio()}
  - Unpublish/unpreview local video {@link TRTC#stopLocalVideo stopLocalVideo()}
  - Unpublish/unpreview local audio {@link TRTC#stopLocalAudio stopLocalAudio()}
  - Watch remote video {@link TRTC#startRemoteVideo startRemoteVideo()}
  - Stop watching video {@link TRTC#stopRemoteVideo stopRemoteVideo()}
  - Mute/unmute remote audio {@link TRTC#muteRemoteAudio muteRemoteAudio()}

## Tutotials

- {@tutorial 11-basic-video-call}
- {@tutorial 12-basic-live-video}



