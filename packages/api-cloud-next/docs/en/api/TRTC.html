<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Tencent RTC SDK - TRTC - Documentation</title>
    <meta name="description" content="RTC SDK Documentation. Run a demo within one minute and build solutions for audio/video calls or interactive live streaming within 30 minutes"/>
    <meta name="keywords" content="call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="zh-cn" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="切换至中文" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/en/', '/zh-cn/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1" title="contact us on telegram" class="contact_us_tg" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1685076466271" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="1951" width="24" height="24"><path d="M679.424 746.862l84.005-395.996c7.424-34.852-12.581-48.567-35.438-40.009L234.277 501.138c-33.72 13.13-33.134 32-5.706 40.558l126.282 39.424 293.156-184.576c13.714-9.143 26.295-3.986 16.018 5.157L426.898 615.973l-9.143 130.304c13.13 0 18.871-5.706 25.71-12.581l61.696-59.429 128 94.282c23.442 13.129 40.01 6.29 46.3-21.724zM1024 512c0 282.843-229.157 512-512 512S0 794.843 0 512 229.157 0 512 0s512 229.157 512 512z" fill="#1296DB" p-id="1952"/></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SEI_MESSAGE">SEI_MESSAGE</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Upstream Users Limitation</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Quick Start Call<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Camera/Mic</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Camera Profile</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detect Audio Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Detect Network Quality</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Handle Device Change</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enable Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Calls</a></li>
        <li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li></ul>
        <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
        </ul>
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">TRTC</h1>
      <section>
        <header style="display:none">
          <h2>
            TRTC
          </h2>
        </header>
        <article>
          <div class="container-overview">
            <h4 class="name" id="TRTC"><span class="type-signature"></span>new TRTC<span class="signature">()</span><span class="type-signature"></span></h4>
            <div class="description">
              <p>The TRTC object is created using <a href="TRTC.html#.create">TRTC.create()</a> and provides core real-time audio and video capabilities:<br></p>
              <ul>
                <li>Enter an audio and video room <a href="TRTC.html#enterRoom">enterRoom()</a></li>
                <li>Exit the current audio and video room <a href="TRTC.html#exitRoom">exitRoom()</a></li>
                <li>Preview/publish local video <a href="TRTC.html#startLocalVideo">startLocalVideo()</a></li>
                <li>Capture/publish local audio <a href="TRTC.html#startLocalAudio">startLocalAudio()</a></li>
                <li>Stop previewing/publishing local video <a href="TRTC.html#stopLocalVideo">stopLocalVideo()</a></li>
                <li>Stop capturing/publishing local audio <a href="TRTC.html#stopLocalAudio">stopLocalAudio()</a></li>
                <li>Watch remote video <a href="TRTC.html#startRemoteVideo">startRemoteVideo()</a></li>
                <li>Stop watching remote video <a href="TRTC.html#stopRemoteVideo">stopRemoteVideo()</a></li>
                <li>Mute/unmute remote audio <a href="TRTC.html#muteRemoteAudio">muteRemoteAudio()</a></li>
              </ul>
              <p>The TRTC lifecycle is shown in the following figure:<br /></p>
              <img src="./assets/client-life-cycle.png" width="600" />
            </div>
            <dl class="details">
            </dl>
          </div>
          <h3 class="subsection-title">Methods</h3>
          <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="TRTC.html">TRTC</a>}</span></h4>
          <div class="description">
            <p>Create a TRTC object for implementing functions such as entering a room, previewing, pushing, and pulling streams.<br></p>
            <p><strong>Note:</strong></p>
            <ul>
              <li>You must create a TRTC object first and call its methods and listen to its events to implement various functions required by the business.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Create a TRTC object
const trtc = TRTC.create();</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>TRTC object</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type"><a href="TRTC.html">TRTC</a></span>
            </dd>
          </dl>
          <h4 class="name" id="enterRoom"><span class="type-signature">(async) </span>enterRoom<span class="signature">(options)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Enter a video call room.<br></p>
            <ul>
              <li>Entering a room means starting a video call session. Only after entering the room successfully can you make audio and video calls with other users in the room.</li>
              <li>You can publish local audio and video streams through <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> and <a href="TRTC.html#startLocalAudio">startLocalAudio()</a> respectively. After successful publishing, other users in the room will receive the <a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a> and <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a> event notifications.</li>
              <li>By default, the SDK automatically plays remote audio. You need to call <a href="TRTC.html#startRemoteVideo">startRemoteVideo()</a> to play remote video.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const trtc = TRTC.create();
await trtc.enterRoom({ roomId: 8888, sdkAppId, userId, userSig });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Enter room parameters</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Default</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>sdkAppId</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>sdkAppId <br>
                            You can obtain the sdkAppId information in the <strong>Application Information</strong> section after creating a new application by clicking <strong>Application Management</strong> &gt; <strong>Create Application</strong> in the <a target="_blank" href="https://console.intl.cloud.tencent.com/trtc">TRTC Console</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>User ID <br>
                            It is recommended to limit the length to 32 bytes, and only allow uppercase and lowercase English letters (a-zA-Z), numbers (0-9), underscores, and hyphens.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userSig</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>UserSig signature <br>
                            Please refer to <a target="_blank" href="https://www.tencentcloud.com/document/product/647/35166">UserSig related</a> for the calculation method of userSig.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>roomId</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>the value must be an integer between 1 and 4294967294<br>
                            <font color="red">If you need to use a string type room id, please use the strRoomId parameter. One of roomId and strRoomId must be passed in. If both are passed in, the roomId will be selected first.</font>
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>strRoomId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>String type room id, the length is limited to 64 bytes, and only supports the following characters:</p>
                          <ul>
                            <li>Uppercase and lowercase English letters (a-zA-Z)</li>
                            <li>Numbers (0-9)</li>
                            <li>Space ! # $ % &amp; ( ) + - : ; &lt; = . &gt; ? @ [ ] ^ _ { } | ~ ,
                              <font color="red">Note: It is recommended to use a numeric type roomId. The string type room id &quot;123&quot; is not the same room as the numeric type room id 123.</font>
                            </li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>scene</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>Application scene, currently supports the following two scenes:</p>
                          <ul>
                            <li><a href="module-TYPE.html#.SCENE_RTC">TRTC.TYPE.SCENE_RTC</a> (default) Real-time call scene, which is suitable for 1-to-1 audio and video calls, or online meetings with up to 300 participants. <a href="tutorial-04-info-uplink-limits.html">Upstream Users Limitation</a>.</li>
                            <li><a href="module-TYPE.html#.SCENE_LIVE">TRTC.TYPE.SCENE_LIVE</a> Interactive live streaming scene, which is suitable for online live streaming scenes with up to 100,000 people, but you need to specify the role field in the options parameter introduced next.</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>role</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>User role, only meaningful in the <a href="module-TYPE.html#.SCENE_LIVE">TRTC.TYPE.SCENE_LIVE</a> scene, and the <a href="module-TYPE.html#.SCENE_RTC">TRTC.TYPE.SCENE_RTC</a> scene does not need to specify the role. Currently supports two roles:</p>
                          <ul>
                            <li><a href="module-TYPE.html#.ROLE_ANCHOR">TRTC.TYPE.ROLE_ANCHOR</a> (default) Anchor</li>
                            <li><a href="module-TYPE.html#.ROLE_AUDIENCE">TRTC.TYPE.ROLE_AUDIENCE</a> Audience
                              Note: The audience role does not have the permission to publish local audio and video, only the permission to watch remote streams. If the audience wants to interact with the anchor by connecting to the microphone, please switch the role to the anchor through <a href="TRTC.html#switchRole">switchRole()</a> before publishing local audio and video.</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>autoReceiveAudio</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>Whether to automatically receive audio. When a remote user publishes audio, the SDK automatically plays the remote user's audio.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>autoReceiveVideo</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>Whether to automatically receive video. When a remote user publishes video, the SDK automatically pulls and decodes the remote video. You need to call <a href="TRTC.html#startLocalVideo">startLocalVideo</a> to play the remote video.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>enableAutoPlayDialog</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>Whether to enable the SDK's automatic playback failure dialog box, default: true.</p>
                          <ul>
                            <li>Enabled by default. When automatic playback fails, the SDK will pop up a dialog box to guide the user to click the page to restore audio and video playback.</li>
                            <li>Can be set to false in order to turn off. Refer to <a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a>.</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>proxy</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type"><a href="global.html#ProxyServer">ProxyServer</a></span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>proxy config. Refer to <a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>privateMapKey</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>Key for entering a room. If permission control is required, please carry this parameter (empty or incorrect value will cause a failure in entering the room).<br><a target="_blank" href="https://www.tencentcloud.com/document/product/647/35157?lang=en&amp;pg=">privateMapKey permission configuration</a>.</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="exitRoom"><span class="type-signature">(async) </span>exitRoom<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Exit the current audio and video call room.</p>
            <ul>
              <li>After exiting the room, the connection with remote users will be closed, and remote audio and video will no longer be received and played, and the publishing of local audio and video will be stopped.</li>
              <li>The capture and preview of the local camera and microphone will not stop. You can call <a href="TRTC.html#stopLocalVideo">stopLocalVideo()</a> and <a href="TRTC.html#stopLocalAudio">stopLocalAudio()</a> to stop capturing local microphone and camera.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.exitRoom();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="switchRole"><span class="type-signature">(async) </span>switchRole<span class="signature">(role)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Switches the user role, only effective in TRTC.TYPE.SCENE_LIVE interactive live streaming mode.</p>
            <p>In interactive live streaming mode, a user may need to switch between &quot;audience&quot; and &quot;anchor&quot;.
              You can determine the role through the role field in <a href="TRTC.html#enterRoom">enterRoom()</a>, or switch roles after entering the room through switchRole.</p>
            <ul>
              <li>Audience switches to anchor, call trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR) to convert the user role to TRTC.TYPE.ROLE_ANCHOR anchor role, and then call <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> and <a href="TRTC.html#startLocalAudio">startLocalAudio()</a> to publish local audio and video as needed.</li>
              <li>Anchor switches to audience, call trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE) to convert the user role to TRTC.TYPE.ROLE_AUDIENCE audience role. If there is already published local audio and video, the SDK will cancel the publishing of local audio and video.</li>
            </ul>
            <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
              <p>Notice：</p>
              <ul>
                <li>This interface can only be called after entering the room successfully.</li>
                <li>After closing the camera and microphone, it is recommended to switch to the audience role in time to avoid the anchor role occupying the resources of 50 upstreams.</li>
              </ul>
            </blockquote>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// After entering the room successfully
// TRTC.TYPE.SCENE_LIVE interactive live streaming mode, audience switches to anchor
await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);
// Switch from audience role to anchor role and start streaming
await trtc.startLocalVideo();
// TRTC.TYPE.SCENE_LIVE interactive live streaming mode, anchor switches to audience
await trtc.switchRole(TRTC.TYPE.ROLE_AUDIENCE);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>role</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>User role</p>
                  <ul>
                    <li>TRTC.TYPE.ROLE_ANCHOR anchor, can publish local audio and video, up to 50 anchors can publish local audio and video in a single room at the same time.</li>
                    <li>TRTC.TYPE.ROLE_AUDIENCE audience, cannot publish local audio and video, can only watch remote streams, and there is no upper limit on the number of audience members in a single room.</li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="destroy"><span class="type-signature"></span>destroy<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Destroy the TRTC instance <br /></p>
            <p>After exiting the room, if the business side no longer needs to use trtc, you need to call this interface to destroy the trtc instance in time and release related resources.</p>
            <p>Note:</p>
            <ul>
              <li>The trtc instance after destruction cannot be used again.</li>
              <li>If you have entered the room, you need to call the <a href="TRTC.html#exitRoom">TRTC.exitRoom</a> interface to exit the room successfully before calling this interface to destroy trtc.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// When the call is over
await trtc.exitRoom();
// If the trtc is no longer needed, destroy the trtc and release the reference.
trtc.destroy();
trtc = null;</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></p>
          </div>
          <h4 class="name" id="startLocalAudio"><span class="type-signature">(async) </span>startLocalAudio<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Start collecting audio from the local microphone and publish it to the current room.</p>
            <ul>
              <li>When to call: can be called before or after entering the room, cannot be called repeatedly.</li>
              <li>Only one microphone can be opened for a trtc instance. If you need to open another microphone for testing in the case of already opening one microphone, you can create multiple trtc instances to achieve it.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>// Collect the default microphone and publish
await trtc.startLocalAudio();</code></pre>
          <pre class="highlight lang-javascript"><code>// The following is a code example for testing microphone volume, which can be used for microphone volume detection.
trtc.enableAudioVolumeEvaluation();
trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => { });
// No need to publish audio for testing microphone
await trtc.startLocalAudio({ publish: false });
// After the test is completed, turn off the microphone
await trtc.stopLocalAudio();</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <p>Configuration item</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to publish local audio to the room, default is true. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening this event <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to mute microphone. Refer to: <a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Local audio options</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>microphoneId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>Specify which microphone to use</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>audioTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>Custom audioTrack. <a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>captureVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>Set the capture volume of microphone. The default value is 100. Setting above 100 enlarges the capture volume. Since v5.2.1+.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>earMonitorVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>Set the ear return volume, value range [0, 100], the local microphone is muted by default.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>Audio encoding configuration, default <a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">TRTC.TYPE.AUDIO_PROFILE_STANDARD</a></p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateLocalAudio"><span class="type-signature">(async) </span>updateLocalAudio<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Update the configuration of the local microphone.</p>
            <ul>
              <li>When to call: This interface needs to be called after <a href="TRTC.html#startLocalAudio">startLocalAudio()</a> is successful and can be called multiple times.</li>
              <li>This method uses incremental update: only update the passed parameters, and keep the parameters that are not passed unchanged.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Switch microphone
const microphoneList = await TRTC.getMicrophoneList();
if (microphoneList[1]) {
  await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
}</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to publish local audio to the room. You can get the publish state by listening this event <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to mute microphone. Refer to: <a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Local audio configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>microphoneId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>Specify which microphone to use to switch microphones.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>audioTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>Custom audioTrack. <a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>captureVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>Set the capture volume of microphone. The default value is 100. Setting above 100 enlarges the capture volume. Since v5.2.1+.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>earMonitorVolume</code></td>
                                <td class="type">
                                  <span class="param-type">number</span>
                                </td>
                                <td class="description last">
                                  <p>Set the ear return volume, value range [0, 100], the local microphone is muted by default.</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopLocalAudio"><span class="type-signature">(async) </span>stopLocalAudio<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Stop collecting and publishing the local microphone.</p>
            <ul>
              <li>If you just want to mute the microphone, please use updateLocalAudio({ mute: true }). Refer to: <a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a>.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopLocalAudio();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startLocalVideo"><span class="type-signature">(async) </span>startLocalVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Start collecting video from the local camera, play the camera's video on the specified HTMLElement tag, and publish the camera's video to the current room.</p>
            <ul>
              <li>When to call: can be called before or after entering the room, but cannot be called repeatedly.</li>
              <li>Only one camera can be started per trtc instance. If you need to start another camera for testing while one camera is already started, you can create multiple trtc instances to achieve this.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>// Preview and publish the camera
await trtc.startLocalVideo({
  view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
});</code></pre>
          <pre class="highlight lang-javascript"><code>// Preview the camera without publishing. Can be used for camera testing.
const config = {
  view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
  publish: false // Do not publish the camera
}
await trtc.startLocalVideo(config);
// Call updateLocalVideo when you need to publish the video
await trtc.updateLocalVideo({ publish:true });</code></pre>
          <pre class="highlight lang-javascript"><code>// Use a specified camera.
const cameraList = await TRTC.getCameraList();
if (cameraList[0]) {
  await trtc.startLocalVideo({
    view: document.getElementById('localVideo'), // Preview the video on the element with the DOM elementId of localVideo.
    option: {
      cameraId: cameraList[0].deviceId,
    }
  });
}
// use front camera on mobile device.
await trtc.startLocalVideo({ view, option: { useFrontCamera: true }});
// use rear camera on mobile device.
await trtc.startLocalVideo({ view, option: { useFrontCamera: false }});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or ID for local video preview. If not passed or passed as null, the video will not be played.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to publish the local video to the room. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening this event <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to mute camera, refer to: <a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Local video configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>cameraId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>Specify which camera to use for switching cameras.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>useFrontCamera</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to use the front camera.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>videoTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>Custom videoTrack. <a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to enable local preview mirroring. The default is true.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. The default is <code>cover</code>. Refer to the <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>Video encoding parameters for the main video.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>Video encoding parameters for the small video. Refer to <a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>Set the video encoding strategy for weak networks. Smooth first(default) (<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>) or Clear first (<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_ PREFERENCE_SMOOTH</a>)</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateLocalVideo"><span class="type-signature">(async) </span>updateLocalVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Update the local camera configuration.</p>
            <ul>
              <li>This interface needs to be called after <a href="TRTC.html#startLocalVideo">startLocalVideo()</a> is successful.</li>
              <li>This interface can be called multiple times.</li>
              <li>This method uses incremental update: only updates the passed-in parameters, and keeps the parameters that are not passed in unchanged.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>// Switch camera
const cameraList = await TRTC.getCameraList();
if (cameraList[1]) {
  await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
}</code></pre>
          <pre class="highlight lang-javascript"><code>// Stop publishing video, but keep local preview
await trtc.updateLocalVideo({ publish:false });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or Id of the preview camera. If not passed in or passed in null, the video will not be rendered, but still pushes the stream and consumes bandwidth.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to publish the local video to the room. You can get the publish state by listening this event <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mute</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to mute camera, refer to: <a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Local video configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>cameraId</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="description last">
                                  <p>Specify which camera to use</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>useFrontCamera</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to use the front camera</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>videoTrack</code></td>
                                <td class="type">
                                  <span class="param-type">MediaStreamTrack</span>
                                </td>
                                <td class="description last">
                                  <p>Custom videoTrack. <a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a>.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to enable mirror</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. Refer to the <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit"> CSS object-fit</a> property</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>Video encoding parameters for the main stream</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#VideoProfile">VideoProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>Video encoding parameters for the small video. Refer to <a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>Set the video encoding strategy for weak networks. Smooth first (<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>) or Clear first (<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_ PREFERENCE_SMOOTH</a>)</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopLocalVideo"><span class="type-signature">(async) </span>stopLocalVideo<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Stop capturing, previewing, and publishing the local camera.</p>
            <ul>
              <li>If you only want to stop publishing video but keep the local camera preview, you can use the <a href="TRTC.html#updateLocalVideo">updateLocalVideo({ publish:false </a>)} method.<br></li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopLocalVideo();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startScreenShare"><span class="type-signature">(async) </span>startScreenShare<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Start screen sharing.</p>
            <ul>
              <li>After starting screen sharing, other users in the room will receive the <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a> event, with streamType as <a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a>, and other users can play screen sharing through <a href="TRTC.html#startRemoteVideo">startRemoteVideo</a>.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Start screen sharing
await trtc.startScreenShare();</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or Id for previewing local screen sharing. If not passed or passed as null, local screen sharing will not be rendered.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to publish screen sharing to the room. The default is true. If you call this interface before entering the room and publish = true, the SDK will automatically publish after entering the room. You can get the publish state by listening this event <a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a>.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Screen sharing configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>systemAudio</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to capture system audio. The default is false.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. The default is <code>contain</code>, refer to <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>profile</code></td>
                                <td class="type">
                                  <span class="param-type"><a href="global.html#ScreenShareProfile">ScreenShareProfile</a></span>
                                </td>
                                <td class="description last">
                                  <p>Screen sharing encoding configuration.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>Set the video encoding strategy for weak networks. Smooth first (<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>) or Clear first(default) (<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_ PREFERENCE_SMOOTH</a>)</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateScreenShare"><span class="type-signature">(async) </span>updateScreenShare<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Update screen sharing configuration</p>
            <ul>
              <li>This interface needs to be called after <a href="TRTC.html#startScreenShare">startScreenShare()</a> is successful.</li>
              <li>This interface can be called multiple times.</li>
              <li>This method uses incremental update: only update the passed-in parameters, and keep the parameters that are not passed-in unchanged.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Stop screen sharing, but keep the local preview of screen sharing
await trtc.updateScreenShare({publish:false});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Default</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or Id for screen sharing preview. If not passed in or passed in null, the screen sharing will not be rendered.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>publish</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>Whether to publish screen sharing to the room</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="default">
                        </td>
                        <td class="description last">
                          <p>Screen sharing configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. The default is <code>contain</code>, refer to <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>qosPreference</code></td>
                                <td class="type">
                                  <span class="param-type">QOS_PREFERENCE_SMOOTH</span>
                                  |
                                  <span class="param-type">QOS_PREFERENCE_CLEAR</span>
                                </td>
                                <td class="description last">
                                  <p>Set the video encoding strategy for weak networks. Smooth first (<a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a>) or Clear first (<a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_ PREFERENCE_SMOOTH</a>)</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopScreenShare"><span class="type-signature">(async) </span>stopScreenShare<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Stop screen sharing.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.stopScreenShare();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="startRemoteVideo"><span class="type-signature">(async) </span>startRemoteVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Play remote video</p>
            <ul>
              <li>When to call: Call after receiving the <a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">TRTC.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE)</a> event.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // You need to place the video container in the DOM in advance, and it is recommended to use `${userId}_${streamType}` as the element id.
  trtc.startRemoteVideo({ userId, streamType, view: `${userId}_${streamType}` });
})</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or Id used to play remote video. If not passed or passed null, the video will not be rendered, but the bandwidth will still be consumed.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Remote user ID</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Remote stream type</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: Main stream (remote user's camera)</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: Sub stream (remote user's screen sharing)</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Remote video configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to pull small streams</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to enable mirror</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. Refer to the <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
              <li><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            </ul>
          </div>
          <h4 class="name" id="updateRemoteVideo"><span class="type-signature">(async) </span>updateRemoteVideo<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Update remote video playback configuration<br></p>
            <ul>
              <li>This method should be called after <a href="TRTC.html#startRemoteVideo">startRemoteVideo</a> is successful.</li>
              <li>This method can be called multiple times.</li>
              <li>This method uses incremental updates, so only the configuration items that need to be updated need to be passed in.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const config = {
 view: document.getElementById(userId),
 userId,
}
await trtc.updateRemoteVideo(config);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>view</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                          |
                          <span class="param-type">HTMLElement</span>
                          |
                          <span class="param-type">Array.&lt;HTMLElement></span>
                          |
                          <span class="param-type">null</span>
                        </td>
                        <td class="description last">
                          <p>The HTMLElement instance or Id used to play remote video. If not passed or passed null, the video will not be rendered, but the bandwidth will still be consumed.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Remote user ID</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Remote stream type</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: Main stream (remote user's camera)</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: Sub stream (remote user's screen sharing)</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>option</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>Remote video configuration</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>small</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to pull small streams. Refer to: <a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a>.</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>mirror</code></td>
                                <td class="type">
                                  <span class="param-type">boolean</span>
                                </td>
                                <td class="description last">
                                  <p>Whether to enable mirror</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>fillMode</code></td>
                                <td class="type">
                                  <span class="param-type">'contain'</span>
                                  |
                                  <span class="param-type">'cover'</span>
                                  |
                                  <span class="param-type">'fill'</span>
                                </td>
                                <td class="description last">
                                  <p>Video fill mode. Refer to the <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="stopRemoteVideo"><span class="type-signature">(async) </span>stopRemoteVideo<span class="signature">(config)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Used to stop remote video playback.<br></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Stop playing all remote users
await trtc.stopRemoteVideo({ userId: '*' });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Remote video configuration</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Remote user ID, '*' represents all users.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">TRTC.TYPE.STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <p>Remote stream type. This field is required when userId is not '*'.</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: Main stream (remote user's camera)</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: Sub stream (remote user's screen sharing)</li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></p>
          </div>
          <h4 class="name" id="muteRemoteAudio"><span class="type-signature">(async) </span>muteRemoteAudio<span class="signature">(userId, mute)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Mute a remote user and stop pulling audio data from that user. Only effective for the current user, other users in the room can still hear the muted user's voice.<br></p>
            <p>Note:</p>
            <ul>
              <li>By default, after entering the room, the SDK will automatically play remote audio. You can call this interface to mute or unmute remote users.</li>
              <li>If the parameter autoReceiveAudio = false is passed in when entering the room, remote audio will not be played automatically. When audio playback is required, you need to call this method (mute is passed in false) to play remote audio.</li>
              <li>This interface is effective before or after entering the room (enterRoom), and the mute state will be reset to false after exiting the room (exitRoom).</li>
              <li>If you want to continue pulling audio data from the user but not play it, you can call setRemoteAudioVolume(userId, 0)</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Mute all remote users
await trtc.muteRemoteAudio('*', true);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Remote user ID, '*' represents all users.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>mute</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Whether to mute</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <ul>
              <li><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
              <li><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
              <li><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            </ul>
          </div>
          <h4 class="name" id="setRemoteAudioVolume"><span class="type-signature"></span>setRemoteAudioVolume<span class="signature">(userId, volume)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Used to control the playback volume of remote audio.<br></p>
            <ul>
              <li>Not supported by iOS Safari</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await trtc.setRemoteAudioVolume('123', 90);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Remote user ID</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>volume</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Volume, ranging from 0 to 100. The default value is 100.<br>
                    Since <code>v5.1.3+</code>, the volume can be set higher than 100.</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="startPlugin"><span class="type-signature">(async) </span>startPlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>start plugin</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>name</th>
                  <th>tutorial</th>
                  <th>param</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>audio mixer plugin</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#AudioMixerOptions">AudioMixerOptions</a></td>
                </tr>
                <tr>
                  <td>'AIDenoiser'</td>
                  <td>ai denoiser plugin</td>
                  <td><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#AIDenoiserOptions">AIDenoiserOptions</a></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#AudioMixerOptions">AudioMixerOptions</a></span>
                  |
                  <span class="param-type"><a href="global.html#AIDenoiserOptions">AIDenoiserOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="updatePlugin"><span class="type-signature">(async) </span>updatePlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>Update plugin</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>name</th>
                  <th>tutorial</th>
                  <th>param</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>audio mixer plugin</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#UpdateAudioMixerOptions">UpdateAudioMixerOptions</a></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#UpdateAudioMixerOptions">UpdateAudioMixerOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="stopPlugin"><span class="type-signature">(async) </span>stopPlugin<span class="signature">(plugin, options)</span><span class="type-signature"> &rarr; {Promise.&lt;void>}</span></h4>
          <div class="description">
            <p>Stop plugin</p>
            <table>
              <thead>
                <tr>
                  <th>pluginName</th>
                  <th>name</th>
                  <th>tutorial</th>
                  <th>param</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>'AudioMixer'</td>
                  <td>audio mixer plugin</td>
                  <td><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></td>
                  <td><a target="_blank" href="https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/global.html#StopAudioMixerOptions">StopAudioMixerOptions</a></td>
                </tr>
                <tr>
                  <td>'AIDenoiser'</td>
                  <td>ai denoiser plugin</td>
                  <td><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></td>
                  <td></td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>plugin</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#PluginName">PluginName</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type"><a href="global.html#StopAudioMixerOptions">StopAudioMixerOptions</a></span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;void></span>
            </dd>
          </dl>
          <h4 class="name" id="enableAudioVolumeEvaluation"><span class="type-signature"></span>enableAudioVolumeEvaluation<span class="signature">(interval<span class="signature-attributes">opt</span>, enableInBackground<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Enables or disables the volume callback.<br></p>
            <ul>
              <li>After enabling this function, whether someone is speaking in the room or not, the SDK will regularly throw the <a href="module-EVENT.html#.AUDIO_VOLUME">TRTC.on(TRTC.EVENT.AUDIO_VOLUME)</a> event, which feedbacks the volume evaluation value of each user.<br></li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   event.result.forEach(({ userId, volume }) => {
       const isMe = userId === ''; // When userId is an empty string, it represents the local microphone volume.
       if (isMe) {
           console.log(`my volume: ${volume}`);
       } else {
           console.log(`user: ${userId} volume: ${volume}`);
       }
   })
});
// Enable volume callback and trigger the event every 1000ms
trtc.enableAudioVolumeEvaluation(1000);
// To turn off the volume callback, pass in an interval value less than or equal to 0
trtc.enableAudioVolumeEvaluation(-1);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>interval</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="default">
                  <code>2000</code>
                </td>
                <td class="description last">
                  <p>Used to set the time interval for triggering the volume callback event. The default is 2000(ms), and the minimum value is 100(ms). If set to less than or equal to 0, the volume callback will be turned off.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>enableInBackground</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="default">
                  <code>false</code>
                </td>
                <td class="description last">
                  <p>For performance reasons, when the page switches to the background, the SDK will not throw volume callback events. If you need to receive volume callback events when the page is switched to the background, you can set this parameter to true.</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="on"><span class="type-signature"></span>on<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Listen to TRTC events<br><br>
              For a detailed list of events, please refer to: <a href="module-EVENT.html">TRTC.EVENT</a></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
  // REMOTE_VIDEO_AVAILABLE event handler
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Event name</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Event callback function</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Context</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="off"><span class="type-signature"></span>off<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Remove event listener<br></p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, function peerJoinHandler(event) {
  // REMOTE_USER_ENTER event handler
  console.log('remote user enter');
  trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, peerJoinHandler);
});
// Remove all event listeners
trtc.off('*');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Event name. Passing in the wildcard '*' will remove all event listeners.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Event callback function</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Context</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="getVideoTrack"><span class="type-signature"></span>getVideoTrack<span class="signature">(config<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; {MediaStreamTrack|null}</span></h4>
          <div class="description">
            <p>Get video track</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Get local camera videoTrack
const videoTrack = trtc.getVideoTrack();
// Get local screen sharing videoTrack
const screenVideoTrack = trtc.getVideoTrack({ streamType: TRTC.TYPE.STREAM_TYPE_SUB });
// Get remote user's main stream videoTrack
const remoteMainVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
// Get remote user's sub stream videoTrack
const remoteSubVideoTrack = trtc.getVideoTrack({ userId: 'test', streamType: TRTC.TYPE.STREAM_TYPE_SUB });</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>config</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>If not passed, get the local camera videoTrack</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>userId</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>If not passed or passed an empty string, get the local videoTrack. Pass the userId of the remote user to get the remote user's videoTrack.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>streamType</code></td>
                        <td class="type">
                          <span class="param-type">STREAM_TYPE_MAIN</span>
                          |
                          <span class="param-type">STREAM_TYPE_SUB</span>
                        </td>
                        <td class="description last">
                          <p>Remote stream type:</p>
                          <ul>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a>: Main stream (remote user's camera)(default)</li>
                            <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a>: Sub stream (remote user's screen sharing)</li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Video track</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
              |
              <span class="param-type">null</span>
            </dd>
          </dl>
          <h4 class="name" id="getAudioTrack"><span class="type-signature"></span>getAudioTrack<span class="signature">(userId<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; (nullable) {MediaStreamTrack}</span></h4>
          <div class="description">
            <p>Get audio track</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>userId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>If not passed, get the local audioTrack</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Audio track</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
            </dd>
          </dl>
          <h4 class="name" id="sendSEIMessage"><span class="type-signature"></span>sendSEIMessage<span class="signature">(buffer, options<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Send SEI Message <br></p>
            <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
              <p>The header of a video frame has a header block called SEI.
                The principle of this interface is to use the SEI to embed the custom data you want to send along with the video frame.
                SEI messages can accompany video frames all the way to the live CDN.</p>
            </blockquote>
            <p>Applicable scenarios: synchronization of lyrics, live answering questions, etc.</p>
            <p>When to call: call after <a href="TRTC.html#startLocalVideo">trtc.startLocalVideo</a> successfully.</p>
            <p>Note:</p>
            <ol>
              <li>Maximum 1KB(Byte) sent in a single call, maximum 30 calls per second, maximum 8KB sent per second.</li>
              <li>Currently only support Chrome 86+, Edge 86+, Opera 72+ browsers.</li>
              <li>Since SEI is sent along with video frames, there is a possibility that video frames may be lost, and therefore SEI may be lost as well. The number of times it can be sent can be increased within the frequency limit, and the business side needs to do message de-duplication on the receiving side.</li>
              <li>SEI cannot be sent without trtc.startLocalVideo; SEI cannot be received without startRemoteVideo.</li>
              <li>Only H264 encoder is supported to send SEI.</li>
              <li>SEI sending and receiving is not supported for small streams for the time being.</li>
            </ol>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>v5.3.0</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li><a href="module-EVENT.html#.SEI_MESSAGE">TRTC.EVENT.SEI_MESSAGE</a></li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// 1. enable SEI
const trtc = TRTC.create({
   enableSEI: true
})
// 2. send SEI
try {
 await trtc.enterRoom({
  userId: 'user_1',
  roomId: 12345,
})
 await trtc.startLocalVideo();
 const unit8Array = new Uint8Array([1, 2, 3]);
 trtc.sendSEIMessage(unit8Array.buffer);
} catch(error) {
 console.warn(error);
}
// 3. receive SEI
trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
 console.warn(`sei ${event.data} from ${event.userId}`);
})</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>buffer</code></td>
                <td class="type">
                  <span class="param-type">ArrayBuffer</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>SEI data to be sent</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">Object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>seiPayloadType</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <code class="type">required</code>
                          <p>Set the SEI payload type. SDK uses the custom payloadType 243 by default, the business side can use this parameter to set the payloadType to the standard 5. When the business side uses the 5 payloadType, you need to follow the specification to make sure that the first 16 bytes of the <code>buffer</code> are the business side's customized uuid.</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id=".setLogLevel"><span class="type-signature">(static) </span>setLogLevel<span class="signature">(level<span class="signature-attributes">opt</span>, enableUploadLog<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set the log output level
              <br>
              It is recommended to set the DEBUG level during development and testing, which includes detailed prompt information.
              The default output level is INFO, which includes the log information of the main functions of the SDK.
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Output log levels above DEBUG
TRTC.setLogLevel(1);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>level</code></td>
                <td class="type">
                  <span class="param-type">0-5</span>
                </td>
                <td class="default">
                </td>
                <td class="description last">
                  <p>Log output level 0: TRACE 1: DEBUG 2: INFO 3: WARN 4: ERROR 5: NONE</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>enableUploadLog</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="default">
                  <code>true</code>
                </td>
                <td class="description last">
                  <p>Whether to enable log upload, which is enabled by default. It is not recommended to turn it off, which will affect problem troubleshooting.</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id=".isSupported"><span class="type-signature">(static) </span>isSupported<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;object>}</span></h4>
          <div class="description">
            <p>Check if the TRTC Web SDK is supported by the current browser</p>
            <ul>
              <li>Reference: <a href="tutorial-05-info-browser.html">Browsers Supported</a>.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>TRTC.isSupported().then((checkResult) => {
  if(!checkResult.result) {
     console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
     // The SDK is not supported by the current browser, guide the user to use the latest version of Chrome browser.
  }
});</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise returns the detection result</p>
            <table>
              <thead>
                <tr>
                  <th>Property</th>
                  <th>Type</th>
                  <th>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>checkResult.result</td>
                  <td>boolean</td>
                  <td>Detection result</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isBrowserSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser is supported by the SDK</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isWebRTCSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports WebRTC</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isWebCodecsSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports WebCodecs</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isMediaDevicesSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports obtaining media devices and media streams</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isScreenShareSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports screen sharing</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isSmallStreamSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports small streams</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isH264EncodeSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports H264 encoding for uplink</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isH264DecodeSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports H264 decoding for downlink</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isVp8EncodeSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports VP8 encoding for uplink</td>
                </tr>
                <tr>
                  <td>checkResult.detail.isVp8DecodeSupported</td>
                  <td>boolean</td>
                  <td>Whether the current browser supports VP8 decoding for downlink</td>
                </tr>
              </tbody>
            </table>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;object></span>
            </dd>
          </dl>
          <h4 class="name" id=".getCameraList"><span class="type-signature">(static) </span>getCameraList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>Returns the list of camera devices
              <br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>This interface does not support use under the http protocol, please use the https protocol to deploy your website. <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security">Privacy and security</a></li>
              <li>For security reasons, the label and deviceId fields may be empty before the user authorizes access to the camera or microphone. Therefore, it is recommended to call this interface to obtain device details after the user authorizes access.</li>
              <li>You can call the browser's native interface <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities">getCapabilities</a> to get the maximum resolutions supported by the camera, frame rate, mobile devices to distinguish between front and rear cameras, etc. This interface supports Chrome 67+, Edge 79+, Safari 17+, Opera 54+.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const cameraList = await TRTC.getCameraList();
if (cameraList[0] &amp;&amp; cameraList[0].getCapabilities) {
  const { width, height, frameRate, facingMode } = cameraList[0].getCapabilities();
  console.log(width.max, height.max, frameRate.max);
  if (facingMode) {
    if (facingMode[0] === 'user') {
      // front camera
    } else if (facingMode[0] === 'environment') {
      // rear camera
    }
  }
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise returns an array of <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".getMicrophoneList"><span class="type-signature">(static) </span>getMicrophoneList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>Returns the list of microphone devices
              <br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>This interface does not support use under the http protocol, please use the https protocol to deploy your website. <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#Privacy_and_security">Privacy and security</a></li>
              <li>For security reasons, the label and deviceId fields may be empty before the user authorizes access to the camera or microphone. Therefore, it is recommended to call this interface to obtain device details after the user authorizes access.</li>
              <li>You can call the browser's native interface <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/InputDeviceInfo/getCapabilities">getCapabilities</a> to get information about the microphone's capabilities, e.g. the maximum number of channels supported, etc. This interface supports Chrome 67+, Edge 79+, Safari 17+, Opera 54+.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const microphoneList = await TRTC.getMicrophoneList();
if (microphoneList[0] &amp;&amp; microphoneList[0].getCapabilities) {
  const { channelCount } = microphoneList[0].getCapabilities();
  console.log(channelCount.max);
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise returns an array of <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".getSpeakerList"><span class="type-signature">(static) </span>getSpeakerList<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;Array.&lt;MediaDeviceInfo>>}</span></h4>
          <div class="description">
            <p>Returns the list of speaker devices
              <br>
              For security reasons, the label and deviceId fields may be empty before the user authorizes access to the camera or microphone. Therefore, it is recommended to call this interface to obtain device details after the user authorizes access.
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Promise returns an array of <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;Array.&lt;MediaDeviceInfo>></span>
            </dd>
          </dl>
          <h4 class="name" id=".setCurrentSpeaker"><span class="type-signature">(async, static) </span>setCurrentSpeaker<span class="signature">(speakerId)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set the current speaker for audio playback</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>speakerId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <code class="type">required</code>
                  <p>Speaker ID</p>
                </td>
              </tr>
            </tbody>
          </table>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:50 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      