<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Tencent RTC SDK - Tutorial: Handle Firewall Restriction - Documentation</title>
    <meta name="description" content="RTC SDK Documentation. Run a demo within one minute and build solutions for audio/video calls or interactive live streaming within 30 minutes"/>
    <meta name="keywords" content="call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="zh-cn" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="切换至中文" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/en/', '/zh-cn/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1" title="contact us on telegram" class="contact_us_tg" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1685076466271" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="1951" width="24" height="24"><path d="M679.424 746.862l84.005-395.996c7.424-34.852-12.581-48.567-35.438-40.009L234.277 501.138c-33.72 13.13-33.134 32-5.706 40.558l126.282 39.424 293.156-184.576c13.714-9.143 26.295-3.986 16.018 5.157L426.898 615.973l-9.143 130.304c13.13 0 18.871-5.706 25.71-12.581l61.696-59.429 128 94.282c23.442 13.129 40.01 6.29 46.3-21.724zM1024 512c0 282.843-229.157 512-512 512S0 794.843 0 512 229.157 0 512 0s512 229.157 512 512z" fill="#1296DB" p-id="1952"/></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SEI_MESSAGE">SEI_MESSAGE</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Upstream Users Limitation</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Quick Start Call<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Camera/Mic</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Camera Profile</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detect Audio Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Detect Network Quality</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Handle Device Change</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enable Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Calls</a></li>
        <li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li></ul>
        <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
        </ul>
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Tutorial: Handle Firewall Restriction</h1>
      <section>
        <header style="display:none">
          <h2>Handle Firewall Restriction</h2>
          <link type="text/css" rel="stylesheet" href="styles/toc.css">
        </header>
        <article>
          <h2>Function Description</h2>
          <p>This article mainly introduces the best practices for dealing with firewall restrictions. For example, in a network environment with a firewall such as an enterprise intranet, TRTC Web SDK cannot be used normally due to firewall restrictions. In this case, there are two solutions:</p>
          <ul>
            <li>Solution 1: Listen for SDK errors and guide users to change networks or configure firewall whitelists.</li>
            <li>Solution 2: Use the Nginx + coturn proxy solution.</li>
          </ul>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>The TRTC Web SDK uses UDP to communicate with the TRTC server by default, and has a built-in Turn Server that supports relaying media data through UDP or TCP.
              In the public network, users do not need to set up any proxies, as the SDK will attempt to establish media connections in the order of direct connection, Turn Server UDP, and Turn Server TCP.
              If it is known that the user will be using the SDK within an internal network firewall, it may not be possible to establish a media connection, and a proxy will need to be set up.</p>
          </blockquote>
          <h2>Prerequisites</h2>
          <ul>
            <li>Solution 2 requires the deployment of two servers, Nginx + Turn Server. You can contact your company's operations and maintenance colleagues to assist in building. The Nginx proxy server is used to proxy and forward the Websocket signaling data packets of TRTC Web SDK; the Turn Server is used to forward audio and video data packets.</li>
          </ul>
          <h2>Implementation Process</h2>
          <h3>Solution 1</h3>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>This solution is suitable for: You cannot determine whether the user's network will be restricted by the firewall. At this time, you can listen for SDK errors, guide users to change networks or check firewalls.</p>
          </blockquote>
          <p>When you call APIs such as startLocalVideo, startLocalAudio, startRemoteVideo, etc., the SDK will establish a media connection channel internally for transmitting audio and video data. When encountering firewall restrictions, the SDK may fail to establish a connection, and the SDK will throw a firewall-restricted error and continue to retry.</p>
          <p>You can refer to the following code example to listen for this error and guide users to change networks or check network firewalls and whitelist the domains and ports used by TRTC Web SDK. Reference: <a target="_blank" href="https://www.tencentcloud.com/document/product/647/35164#what-ports-and-domain-names-should-i-add-to-the-allowlist-of-my-firewall-for-webrtc.3F">TRTC Web SDK Domain and Port Whitelist</a>.</p>
          <pre class="highlight lang-javascript source lang-js"><code>trtc.on(TRTC.EVENT.ERROR, error => {
  // User network firewall restrictions may cause audio and video calls to fail.
  // At this time, guide users to change networks or check network firewall settings.
  if (error.code === TRTC.ERROR_CODE.OPERATION_FAILED && error.extraCode === 5501) {
  }
});
</code></pre>
          <h3>Solution 2</h3>
          <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
            <p>This solution is suitable for: You determine that the user's network is restricted by the firewall, and you need to set up a proxy server to solve the problem.</p>
          </blockquote>
          <table>
            <thead>
              <tr>
                <th>Solution</th>
                <th>Applicable scenarios</th>
                <th>Network requirements</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>A</td>
                <td>Users can access a specific external proxy server on the network</td>
                <td>The proxy server is deployed on the external network, and the internal network firewall needs to open a whitelist to allow internal network users to access the external proxy server.</td>
              </tr>
              <tr>
                <td>B</td>
                <td>Users can only access an internal proxy server on the network</td>
                <td>The proxy server is deployed on the internal network, and the internal network firewall needs to open a whitelist to allow the internal proxy server to access the external network.</td>
              </tr>
            </tbody>
          </table>
          <div style="display: flex;justify-content: center;">
            <img src="./assets/proxy-1.png" />
          </div>
          <div style="display: flex;justify-content: center;">
            Example of Solution A
          </div>
          <div style="display: flex;justify-content: center;">
            <img src="./assets/proxy-2.png" />
          </div>
          <div style="display: flex;justify-content: center;">Example of Solution B</div>
          <h3>Setting up a proxy server</h3>
          <p>After you have set up Nginx and Turn server, you can refer to the following example to set up a proxy server.</p>
          <pre class="highlight lang-javascript source lang-javascript"><code>const trtc = TRTC.create(); 
await trtc.enterRoom({
  ...,
  proxy: {
    // Set up a Websocket proxy to relay signaling data packets between the SDK and the TRTC backend.
    websocketProxy: 'wss://proxy.example.com/ws/',
    // Set up a turn server to relay media data packets between the SDK and the TRTC backend. ********:3478 is the IP address and port of the turn server.
    turnServer: { url: '********:3478', username: 'turn', credential: 'turn', credentialType: 'password' }
    // By default, the SDK reports logs to the yun.tim.qq.com domain name. If this domain name cannot be accessed in your internal network, you need to whitelist the domain name or configure the following log proxy.
    // Set up a log reporting proxy. Logs are key data for troubleshooting, so be sure to set up this proxy.
    loggerProxy: 'https://proxy.example.com/logger/',
  }
})
</code></pre>
          <h3>Solution A</h3>
          <h4>Setting up Nginx server</h4>
          <ol>
            <li>
              <p>Deploy Nginx server</p>
              <p>Refer to the Nginx server deployment tutorial found on the Internet for deployment. If the enterprise has already deployed the Nginx service, it can be configured directly.</p>
            </li>
            <li>
              <p>Configure Nginx server</p>
              <pre class="highlight lang-javascript source lang-sh"><code>vi /etc/nginx/nginx.conf 
</code></pre>
              <pre class="highlight lang-javascript source"><code>http {
  server { 
    # The access domain name of the Nginx server
    server_name proxy.example.com; 
    # The access port of the Nginx server
    listen 443; 
    ssl on; 
    location /ws/ { # Corresponding to the websocketProxy parameter in setProxyServer
      proxy_pass https://signaling.rtc.qq.com/; # TRTC server
      proxy_http_version 1.1; 
      proxy_set_header Upgrade $http_upgrade; 
      proxy_set_header Connection &quot;upgrade&quot;; 
    }
    location /logger/ { # Corresponding to the loggerProxy parameter in setProxyServer
      proxy_pass https://yun.tim.qq.com/;
    }
    # SSL certificate corresponding to the domain name, used for HTTPS, users need to apply for it themselves
    ssl_certificate ./crt/1_proxy.trtcapi.com_bundle.crt; 
    ssl_certificate_key ./crt/2_proxy.trtcapi.com.key; 
  }
}
</code></pre>
            </li>
          </ol>
          <pre class="highlight lang-javascript source"><code>
3. Reload Nginx
   ```sh
   sudo nginx -s reload
</code></pre>
          <ol start="4">
            <li>Confirm that the company's firewall allows access to the Nginx server IP and port.</li>
          </ol>
          <h4>Setting up Turn server</h4>
          <p>You can search for turn server setup tutorials on the Internet for setup, or you can use the following script to set up a turn server in <code>Centos</code>.</p>
          <ol>
            <li>
              <p>Create a script file <code>turn.sh</code> in the Linux server, and the script content is as follows:</p>
              <pre class="highlight lang-javascript source lang-sh"><code>#!/usr/bin/env bash
# current file name is turn.sh
# ref:
# https://gabrieltanner.org/blog/turn-server    STEP 3 testing turn server
# https://medium.com/av-transcode/what-is-webrtc-and-how-to-setup-stun-turn-server-for-webrtc-communication-63314728b9d0
# as super-user
# usage:  current_program &lt;external-ip>
set -x
set -e
ip a
pwd
whoami
display_usage() {
        echo &quot;This script must be run with super-user privileges.&quot;
        echo -e &quot;\nUsage: $0 &lt;external-ip> \ne.g. $0 *************&quot;
}
# if less than two arguments supplied, display usage
if [ $# -lt 1 ]
then
        display_usage
        exit 1
fi
if [[ $1 =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
  echo &quot;get external ip $1&quot;
else
  echo &quot;wrong external ip $1 , must not have whitespace, tab and other char&quot;
  exit 2
fi
yum install -y coturn
# $1 is &lt;external-ip>
cat &lt;&lt;EOF > /etc/coturn/turnserver.conf
external-ip=$1
listening-port=3478
lt-cred-mech
max-port=65535
min-port=20000
no-dtls
no-tls
realm=tencent
user=turn:turn
verbose
EOF
</code></pre>
            </li>
            <li>
              <p>Add executable permission</p>
              <pre class="highlight lang-javascript source lang-sh"><code>chmod +x turn.sh
</code></pre>
            </li>
            <li>
              <p>Execute the script as root, <code>sudo ./turn.sh &lt;server public IP&gt;</code>, for example:</p>
              <pre class="highlight lang-javascript source lang-sh"><code>sudo ./turn.sh ********
</code></pre>
            </li>
            <li>
              <p>Start the turn server</p>
              <pre class="highlight lang-javascript source lang-sh"><code>systemctl start coturn
# Check if turn is started successfully
ps aux | grep coturn
# If you want to restart the service, execute
service coturn restart 
</code></pre>
            </li>
            <li>
              <p>Configure the firewall for the turn server, open inbound port 3478 (TCP &amp; UDP), and outbound ports (UDP) between min and max ports in the configuration above.</p>
            </li>
            <li>
              <p>Configure the company's internal network firewall to allow access to the turn server's IP and open outbound port 3478 (TCP &amp; UDP).</p>
            </li>
            <li>
              <p>Test the turn server</p>
              <p>Use this <a target="_blank" href="https://webrtc.github.io/samples/src/content/peerconnection/trickle-ice/">test page</a> to test whether the turn server is accessible. If the result shows &quot;done&quot; as shown in the screenshot below, the turn server is working properly.</p>
              <img src="./assets/turn-test.png" alt="turn-test" style="zoom: 33%;" />
            </li>
          </ol>
          <h3>Solution B</h3>
          <p>Solution B builds the Nginx proxy in the same way as Scenario A. There are two main differences:</p>
          <ol>
            <li>When building a turn server, the external-ip field in the configuration file must be filled in with the address of the server on your corporate intranet.</li>
          </ol>
          <pre class="highlight lang-javascript source lang-sh"><code># The start script in option A is the server's external address, e.g. ********
sudo . /turn.sh ********
# In option B, the start script fills in the server's intranet address, e.g. ******** for the intranet
sudo . /turn.sh ********
</code></pre>
          <ol start="2">
            <li>Firewall configuration:</li>
          </ol>
          <ul>
            <li>For the Nginx server, the domain name whitelist needs to be configured in the company's intranet firewall to allow the Nginx server to access TRTC's related domain names. Refer to: <a target="_blank" href="https://www.tencentcloud.com/document/product/647/35164#what-ports-and-domain-names-should-i-add-to-the-allowlist-of-my-firewall-for-webrtc.3F">TRTC Web SDK domain and port whitelist</a></li>
            <li>For the Turn Server, allow the Turn Server to access the external network.</li>
          </ul>
        </article>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tocbot/4.11.1/tocbot.min.js"></script>
        <script src="scripts/add-toc.js"></script>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:50 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      