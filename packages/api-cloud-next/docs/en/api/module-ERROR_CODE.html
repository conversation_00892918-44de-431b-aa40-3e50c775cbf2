<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Tencent RTC SDK - ERROR_CODE - Documentation</title>
    <meta name="description" content="RTC SDK Documentation. Run a demo within one minute and build solutions for audio/video calls or interactive live streaming within 30 minutes"/>
    <meta name="keywords" content="call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="zh-cn" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="切换至中文" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/en/', '/zh-cn/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1" title="contact us on telegram" class="contact_us_tg" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1685076466271" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="1951" width="24" height="24"><path d="M679.424 746.862l84.005-395.996c7.424-34.852-12.581-48.567-35.438-40.009L234.277 501.138c-33.72 13.13-33.134 32-5.706 40.558l126.282 39.424 293.156-184.576c13.714-9.143 26.295-3.986 16.018 5.157L426.898 615.973l-9.143 130.304c13.13 0 18.871-5.706 25.71-12.581l61.696-59.429 128 94.282c23.442 13.129 40.01 6.29 46.3-21.724zM1024 512c0 282.843-229.157 512-512 512S0 794.843 0 512 229.157 0 512 0s512 229.157 512 512z" fill="#1296DB" p-id="1952"/></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SEI_MESSAGE">SEI_MESSAGE</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Upstream Users Limitation</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Quick Start Call<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Camera/Mic</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Camera Profile</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detect Audio Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Detect Network Quality</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Handle Device Change</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enable Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Calls</a></li>
        <li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li></ul>
        <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
        </ul>
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">ERROR_CODE</h1>
      <section>
        <header style="display:none">
        </header>
        <article>
          <div class="container-overview">
            <div class="description">
              <p>TRTC SDK v5.0 defines 8 types of error codes, and handles them through the RtcError object.</p>
            </div>
            <dl class="details">
              <dt class="tag-see">See:</dt>
              <dd class="tag-see">
                <ul>
                  <li><a href="RtcError.html">RtcError</a></li>
                  <li><a href="module-EVENT.html#.ERROR">TRTC.EVENT.ERROR</a></li>
                </ul>
              </dd>
            </dl>
            <h5>Example</h5>
            <pre class="highlight lang-javascript"><code>// Usage:
// 1. API call error
trtc.startLocalVideo().catch(error => {
 if (error.code === TRTC.ERROR_CODE.DEVICE_ERROR) {}
});
// 2. Non-API call error, the error that the SDK cannot recover after internal retry
trtc.on(TRTC.EVENT.ERROR, (error) => {
   if (error.code === TRTC.ERROR_CODE.OPERATION_FAILED) {}
});</code></pre>
          </div>
          <h3 class="subsection-title">Members</h3>
          <h4 class="name" id=".INVALID_PARAMETER"><span class="type-signature">(static) </span>INVALID_PARAMETER<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5000</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: The parameters passed in when calling the interface do not meet the API requirements. <br>
              Handling suggestion: Please check whether the passed-in parameters comply with the API specifications, such as whether the parameter type is correct.</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5001</td>
                  <td style="text-align:left">The required parameter is not passed in</td>
                </tr>
                <tr>
                  <td style="text-align:left">5002</td>
                  <td style="text-align:left">The parameter type is incorrect</td>
                </tr>
                <tr>
                  <td style="text-align:left">5003</td>
                  <td style="text-align:left">The parameter is empty</td>
                </tr>
                <tr>
                  <td style="text-align:left">5004</td>
                  <td style="text-align:left">The parameter instance is incorrect</td>
                </tr>
                <tr>
                  <td style="text-align:left">5005</td>
                  <td style="text-align:left">The parameter value is out of range</td>
                </tr>
                <tr>
                  <td style="text-align:left">5006</td>
                  <td style="text-align:left">The parameter value is less than 0</td>
                </tr>
                <tr>
                  <td style="text-align:left">5007</td>
                  <td style="text-align:left">The parameter value is less than the minimum value</td>
                </tr>
                <tr>
                  <td style="text-align:left">5008</td>
                  <td style="text-align:left">The parameter value is greater than the maximum value</td>
                </tr>
                <tr>
                  <td style="text-align:left">5009</td>
                  <td style="text-align:left">The elementId cannot be found on the page or was not found on the page when searching</td>
                </tr>
                <tr>
                  <td style="text-align:left">5010</td>
                  <td style="text-align:left">The parameter passed in is not of type HTMLElement</td>
                </tr>
                <tr>
                  <td style="text-align:left">5011</td>
                  <td style="text-align:left">streamId does not meet the specifications</td>
                </tr>
                <tr>
                  <td style="text-align:left">5012</td>
                  <td style="text-align:left">The range of the incoming roomId does not comply with the specifications [1, 4294967294]</td>
                </tr>
                <tr>
                  <td style="text-align:left">5013</td>
                  <td style="text-align:left">The strRoomId type passed in is not a legal string</td>
                </tr>
                <tr>
                  <td style="text-align:left">5014</td>
                  <td style="text-align:left">When the userId is not '*', a streamType needs to be passed in</td>
                </tr>
                <tr>
                  <td style="text-align:left">5015</td>
                  <td style="text-align:left">roomId or strRoomId not passed in, one of them must be passed in</td>
                </tr>
                <tr>
                  <td style="text-align:left">5016</td>
                  <td style="text-align:left">roomId must be a numeric type. Currently, a string type has been passed in. If you need to use a string as the room id, please use strRoomId</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".INVALID_OPERATION"><span class="type-signature">(static) </span>INVALID_OPERATION<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5100</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: The prerequisite requirements of the API are not met when calling the interface. <br>
              Handling suggestion: Please check whether the calling logic complies with the API prerequisite requirements according to the corresponding API document. For example: 1. Switching roles before entering the room successfully, 2. The remote user and stream being played do not exist.</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5101</td>
                  <td style="text-align:left">The API is called without entering the room, such as startRemoteVideo muteRemoteAudio switchRole and other APIs need to be called after entering the room</td>
                </tr>
                <tr>
                  <td style="text-align:left">5102</td>
                  <td style="text-align:left">The remote user does not exist, for example, when startRemoteVideo is called, the userId passed in corresponds to a remote user who is not in the room.</td>
                </tr>
                <tr>
                  <td style="text-align:left">5103</td>
                  <td style="text-align:left">The remote stream type does not exist, for example, when startRemoteVideo is called, streamType = TRTC.TYPE.STREAM_TYPE.SUB is passed in, but the corresponding remote user does not share the screen.</td>
                </tr>
                <tr>
                  <td style="text-align:left">5104</td>
                  <td style="text-align:left">Repeatedly calling the API, for example, after entering the room successfully, enterRoom interface is called again.</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".ENV_NOT_SUPPORTED"><span class="type-signature">(static) </span>ENV_NOT_SUPPORTED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5200</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: The current environment does not support this function, indicating that the current browser does not support calling the corresponding API. <br>
              Handling suggestion: Usually, TRTC.isSupported can be used to perceive which capabilities the current browser supports. If the browser does not support it, you need to guide the user to use a browser that supports this capability. Reference: <a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a></p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5201</td>
                  <td style="text-align:left">The current page protocol is not HTTPS, and the audio and video capture (pushing, linking) capabilities are not supported</td>
                </tr>
                <tr>
                  <td style="text-align:left">5202</td>
                  <td style="text-align:left">The current browser does not support WebRTC capabilities, the browser version is too low, or</td>
                </tr>
                <tr>
                  <td style="text-align:left">5203</td>
                  <td style="text-align:left">The browser does not support H264 encoding capability</td>
                </tr>
                <tr>
                  <td style="text-align:left">5204</td>
                  <td style="text-align:left">The browser does not support H264 decoding capability</td>
                </tr>
                <tr>
                  <td style="text-align:left">5205</td>
                  <td style="text-align:left">The browser does not support screen sharing capability</td>
                </tr>
                <tr>
                  <td style="text-align:left">5206</td>
                  <td style="text-align:left">The browser does not support small stream encoding capability</td>
                </tr>
                <tr>
                  <td style="text-align:left">5207</td>
                  <td style="text-align:left">The browser does not support SEI receiving and sending capabilities</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".DEVICE_ERROR"><span class="type-signature">(static) </span>DEVICE_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5300</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: Exception occurred when obtaining device or capturing microphone/camera/screen sharing.<br></p>
            <p>The following API will throw this error code when an exception occurs: <a href="TRTC.html#startLocalAudio">trtc.startLocalAudio</a> <a href="TRTC.html#startLocalVideo">trtc.startLocalVideo</a> <a href="TRTC.html#startScreenShare">trtc.startScreenShare</a><br></p>
            <p>Suggestion: Guide the user to check whether the device has a camera and microphone, whether the system has authorized the browser, and whether the browser has authorized the page. It is recommended to increase the device detection process before entering the room to confirm whether the microphone and camera exist and can be captured normally before proceeding to the next call operation. Usually, this exception can be avoided after the device check. <a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a><br></p>
            <p>If you need to distinguish more detailed exception categories, you can process according to the following extraCode:</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5301</td>
                  <td style="text-align:left">NotFoundError(DOMException) thrown by the browser <br> Reason: The media device type that meets the request parameters cannot be found (including: audio, video, screen sharing). For example: The PC does not have a camera, but the browser is requested to obtain a video stream, which will report this error. <br> Suggestion: It is recommended to guide users to check the camera or microphone and other external devices required for the call before the call.</td>
                </tr>
                <tr>
                  <td style="text-align:left">5302</td>
                  <td style="text-align:left">NotAllowedError(DOMException) thrown by the browser <br> Reason: <li>The user refused the microphone, camera, and screen sharing requests of the current browser instance.</li>
                    <li>The permission of camera/microphone/screen sharing has been denied by system.</li><br> Suggestion: <li>Prompt the user to authorize the camera/microphone access before audio and video calls can be made.</li>
                    <li>If the browser permission is denied by the system, rtcError will have <a href="RtcError.html#.handler">rtcError.handler</a> method, which can be called to jump to the System Permission Setting APP, so as to facilitate the user to open the permission.</li>
                  </td>
                </tr>
                <tr>
                  <td style="text-align:left">5303</td>
                  <td style="text-align:left">NotReadableError(DOMException) thrown by the browser <br> Reason: Although the user has authorized the use of the corresponding device, due to errors that occurred on some hardware, browser, or webpage levels on the operating system, or other applications occupying the device, the device cannot be accessed by the browser.<br> Suggestion: Handle according to the browser's error message, and prompt the user: &quot;Unable to access the camera/microphone temporarily, please ensure that there are no other applications requesting access to the camera/microphone, and try again&quot;</td>
                </tr>
                <tr>
                  <td style="text-align:left">5303</td>
                  <td style="text-align:left">NotReadableError(DOMException) thrown by the browser <br> Reason: Although the user has authorized the use of the corresponding device, due to errors that occurred on some hardware, browser, or webpage levels on the operating system, or other applications occupying the device, the device cannot be accessed by the browser.<br> Suggestion: 1-If this problem occurs with Windows Camera, the user will be directed to check whether the device is occupied. Only the camera of Windows system will be exclusive, but the microphone will not be exclusive. 2-If this error occurs with the microphone and camera of other systems, the user will be directed to check whether the device is normal or try to restart the browser.</td>
                </tr>
                <tr>
                  <td style="text-align:left">5304</td>
                  <td style="text-align:left">OverconstrainedError(DOMException) thrown by the browser <br> Reason: The value of the cameraId/microphoneId parameter is invalid <br> Suggestion: Check whether cameraId/microphoneId is the value returned by the device information acquisition interface</td>
                </tr>
                <tr>
                  <td style="text-align:left">5305</td>
                  <td style="text-align:left">InvalidStateError(DOMException) thrown by the browser <br> Reason: The current page has not generated interaction, and the page is not fully activated <br> Suggestion: It is recommended to turn on the camera and microphone after the user has clicked on the page to generate interaction</td>
                </tr>
                <tr>
                  <td style="text-align:left">5306</td>
                  <td style="text-align:left">SecurityError(DOMException) thrown by the browser <br> Reason: The system security policy prohibits the use of the device<br> Suggestion: Check whether the system restricts the use of the device, and recommend turning on the camera and microphone after the user clicks on the page to generate interaction</td>
                </tr>
                <tr>
                  <td style="text-align:left">5307</td>
                  <td style="text-align:left">AbortError(DOMException) thrown by the browser <br> Reason: The device cannot be used due to some unknown reasons <br> Suggestion: It is recommended to replace the device or browser, and recheck whether the device is normal</td>
                </tr>
                <tr>
                  <td style="text-align:left">5308</td>
                  <td style="text-align:left">Camera capture exception, after SDK retry, the capture cannot be restored. <br> Reason: Camera exception or the user manually closes the capture permission of the browser <br> Suggestion: Prompt the user that the camera capture is abnormal, and guide the user to check whether the camera is normal and whether there is a capture permission</td>
                </tr>
                <tr>
                  <td style="text-align:left">5309</td>
                  <td style="text-align:left">Microphone capture exception, after SDK retry, the capture cannot be restored. <br> Reason: Microphone exception or the user manually closes the capture permission of the browser <br> Suggestion: Prompt the user that the microphone capture is abnormal, and guide the user to check whether the microphone is normal and whether there is a capture permission</td>
                </tr>
              </tbody>
            </table>
            <p>Reference: <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia#%E5%BC%82%E5%B8%B8">getUserMedia exception</a> and <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia#%E5%BC%82%E5%B8%B8">getDisplayMedia exception</a></p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.startLocalVideo(...).catch(function(rtcError) {
 if(rtcError.code == TRTC.ERROR_CODE.DEVICE_ERROR) {
   // Guide the user to check the device
   switch(rtcError.extraCode) {
     case 5301:
       // Can't find a camera or microphone, guide the user to check if the microphone and camera are working.
       break;
     case 5302:
       if (error.handler) {
         // Prompt the user the browser permission(camera/microphone/screen sharing) has been denied by system. The browser will jump to the System Settings APP, please enable the relevant permissions!
       } else {
         // Prompt the user to allow camera, microphone, and screen share capture permissions on the page.
       }
       break;
     // ...
   }
 }
})</code></pre>
          <h4 class="name" id=".SERVER_ERROR"><span class="type-signature">(static) </span>SERVER_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5400</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: This error code is thrown when abnormal data is returned from the server.<br>
              The following interfaces will throw this error code when an exception occurs: <code>enterRoom</code>, <code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              Handling suggestion: Server exceptions are usually handled during development. Common exceptions include: expired userSig, Tencent Cloud account arrears, TRTC service not enabled, etc. The server returns abnormal data for the following reasons.</p>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">-8</td>
                  <td style="text-align:left">Incorrect sdkAppId, please check if sdkAppId is correctly filled in</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10012</td>
                  <td style="text-align:left">roomId is not passed in or roomId does not meet the specifications. If you need to use the string type roomId, please use strRoomId instead when calling trtc.enterRoom</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10015</td>
                  <td style="text-align:left">Failed to obtain server node</td>
                </tr>
                <tr>
                  <td style="text-align:left">-10016</td>
                  <td style="text-align:left">Server internal communication timeout, timeout is 3s</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100006</td>
                  <td style="text-align:left">Failed to check permissions. After enabling advanced permission control, please check whether the privateMapKey parameter carried by <a href="TRTC.html#enterRoom">enterRoom</a> is correct. Please see <a target="_blank" href="https://www.tencentcloud.com/document/product/647/35157">Enable advanced permission settings</a></td>
                </tr>
                <tr>
                  <td style="text-align:left">-100013</td>
                  <td style="text-align:left">Customer service arrears, please log in to the <a target="_blank" href="https://console.intl.cloud.tencent.com/trtc">TRTC Console</a>, click the application you created, click [Account Information], and you can confirm the service status in the account information panel</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100021</td>
                  <td style="text-align:left">Server overload, failed to enter the room</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100022</td>
                  <td style="text-align:left">Server allocation failed</td>
                </tr>
                <tr>
                  <td style="text-align:left">-100024</td>
                  <td style="text-align:left">Failed to enter the room due to TRTC service not enabled. Please go to the <a target="_blank" href="https://console.intl.cloud.tencent.com/im">IM Console</a> to enable TRTC service for your application</td>
                </tr>
                <tr>
                  <td style="text-align:left">-102006</td>
                  <td style="text-align:left">Flow control defined error code (add user failed)</td>
                </tr>
                <tr>
                  <td style="text-align:left">-102010</td>
                  <td style="text-align:left">After enabling advanced permission control, the user does not have the permission to create a room. Please see <a target="_blank" href="https://www.tencentcloud.com/document/product/647/35157">Enable advanced permission settings</a></td>
                </tr>
                <tr>
                  <td style="text-align:left">-102023</td>
                  <td style="text-align:left">Request parameter error (request parameter error generated by the backend interface service)</td>
                </tr>
                <tr>
                  <td style="text-align:left">70001</td>
                  <td style="text-align:left">userSig expired, please try to regenerate. If it expires just after generation, please check whether the validity period is too small or mistakenly filled in as 0</td>
                </tr>
                <tr>
                  <td style="text-align:left">70002</td>
                  <td style="text-align:left">userSig length is 0, please confirm whether the signature calculation is correct, access sign_src to get the idiotic source code of the calculation signature, check the parameters, and ensure the correctness of the signature calculation</td>
                </tr>
                <tr>
                  <td style="text-align:left">70003</td>
                  <td style="text-align:left">userSig verification failed, please confirm whether the userSig content is truncated, such as content truncation caused by insufficient buffer length</td>
                </tr>
                <tr>
                  <td style="text-align:left">70004</td>
                  <td style="text-align:left">userSig verification failed, please confirm whether the userSig content is truncated, such as content truncation caused by insufficient buffer length</td>
                </tr>
                <tr>
                  <td style="text-align:left">70005</td>
                  <td style="text-align:left">userSig verification failed, use tools to verify whether the generated userSig is correct</td>
                </tr>
                <tr>
                  <td style="text-align:left">70006</td>
                  <td style="text-align:left">userSig verification failed, use tools to verify whether the generated userSig is correct</td>
                </tr>
                <tr>
                  <td style="text-align:left">70007</td>
                  <td style="text-align:left">userSig verification failed, use tools to verify whether the generated userSig is correct</td>
                </tr>
                <tr>
                  <td style="text-align:left">70008</td>
                  <td style="text-align:left">userSig verification failed, use tools to verify whether the generated userSig is correct</td>
                </tr>
                <tr>
                  <td style="text-align:left">70009</td>
                  <td style="text-align:left">Failed to verify userSig with business public key, please confirm whether the userSig used for generation corresponds to the private key and sdkAppId</td>
                </tr>
                <tr>
                  <td style="text-align:left">70010</td>
                  <td style="text-align:left">userSig verification failed, use tools to verify whether the generated userSig is correct</td>
                </tr>
                <tr>
                  <td style="text-align:left">70013</td>
                  <td style="text-align:left">userId in userSig does not match the userId filled in when requesting, please check whether the userId filled in when logging in is consistent with that in userSig</td>
                </tr>
                <tr>
                  <td style="text-align:left">70014</td>
                  <td style="text-align:left">sdkAppId in userSig does not match the sdkAppId filled in when requesting, please check whether the sdkAppId filled in when logging in is consistent with that in userSig</td>
                </tr>
                <tr>
                  <td style="text-align:left">70015</td>
                  <td style="text-align:left">No verification method corresponding to the sdkAppId and account type was found. Please confirm whether the account integration operation has been performed</td>
                </tr>
                <tr>
                  <td style="text-align:left">70016</td>
                  <td style="text-align:left">The length of the pulled public key is 0. Please confirm whether the public key has been uploaded. If the public key is re-uploaded, please try again after ten minutes.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70017</td>
                  <td style="text-align:left">Internal third-party ticket verification timed out. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70018</td>
                  <td style="text-align:left">Internal verification of third-party tickets failed.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70019</td>
                  <td style="text-align:left">The ticket field verified by HTTPS is empty. Please fill in userSig correctly.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70020</td>
                  <td style="text-align:left">sdkAppId not found. Please confirm whether it has been configured on Tencent Cloud.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70052</td>
                  <td style="text-align:left">userSig has expired. Please regenerate and try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70101</td>
                  <td style="text-align:left">Request packet information is empty.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70102</td>
                  <td style="text-align:left">Account type in request packet is incorrect.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70103</td>
                  <td style="text-align:left">Phone number format is incorrect.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70104</td>
                  <td style="text-align:left">Email format is incorrect.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70105</td>
                  <td style="text-align:left">TLS account format is incorrect.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70106</td>
                  <td style="text-align:left">Illegal account format type.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70107</td>
                  <td style="text-align:left">userId is not registered.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70113</td>
                  <td style="text-align:left">Batch quantity is invalid.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70114</td>
                  <td style="text-align:left">Restricted for security reasons.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70115</td>
                  <td style="text-align:left">uin is not the developer uin corresponding to sdkAppId.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70140</td>
                  <td style="text-align:left">sdkAppId and acctype do not match.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70145</td>
                  <td style="text-align:left">Account type is incorrect.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70169</td>
                  <td style="text-align:left">Internal error. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70201</td>
                  <td style="text-align:left">Internal error. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70202</td>
                  <td style="text-align:left">Internal error. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70203</td>
                  <td style="text-align:left">Internal error. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70204</td>
                  <td style="text-align:left">sdkAppId does not have a corresponding acctype.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70205</td>
                  <td style="text-align:left">Failed to find acctype. Please try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70206</td>
                  <td style="text-align:left">Batch quantity in request is invalid.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70207</td>
                  <td style="text-align:left">Internal error. Please try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70208</td>
                  <td style="text-align:left">Internal error. Please try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70209</td>
                  <td style="text-align:left">Failed to obtain developer uin flag.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70210</td>
                  <td style="text-align:left">uin in request is not a developer uin.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70211</td>
                  <td style="text-align:left">uin in request is illegal.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70212</td>
                  <td style="text-align:left">Internal error. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70213</td>
                  <td style="text-align:left">Failed to access internal data. Please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70214</td>
                  <td style="text-align:left">Failed to verify internal ticket.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70221</td>
                  <td style="text-align:left">Invalid login status, please re-authenticate with UserSig.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70222</td>
                  <td style="text-align:left">Internal error, please try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70225</td>
                  <td style="text-align:left">Internal error, please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70231</td>
                  <td style="text-align:left">Internal error, please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70236</td>
                  <td style="text-align:left">Failed to verify user signature.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70308</td>
                  <td style="text-align:left">Internal error, please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70346</td>
                  <td style="text-align:left">Ticket verification failed.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70347</td>
                  <td style="text-align:left">Ticket verification failed due to expiration.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70348</td>
                  <td style="text-align:left">Internal error, please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70362</td>
                  <td style="text-align:left">Internal timeout, please try again. If multiple retries are unsuccessful, please contact TLS account support, QQ: **********.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70401</td>
                  <td style="text-align:left">Internal error, please try again.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70402</td>
                  <td style="text-align:left">Invalid parameter. Please check if the required fields are filled in and if the filling meets the protocol requirements.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70403</td>
                  <td style="text-align:left">The initiator is not an App administrator and does not have permission to operate.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70050</td>
                  <td style="text-align:left">Limited due to failure and too many retries. Please check if the ticket is correct and try again in one minute.</td>
                </tr>
                <tr>
                  <td style="text-align:left">70051</td>
                  <td style="text-align:left">The account has been blacklisted. Please contact TLS account support, QQ: **********.</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".OPERATION_FAILED"><span class="type-signature">(static) </span>OPERATION_FAILED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5500</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: The exception that the SDK cannot solve after multiple retries under the condition of meeting the API call requirements, usually caused by browser or network problems. <br>
              The following interfaces will throw this error code when an exception occurs: <code>enterRoom</code>, <code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              Handling suggestions:</p>
            <ul>
              <li>Confirm whether the domain name and port required for communication meet your network environment requirements, refer to the document <a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
              <li>Other issues need to be handled by engineers. <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1">Contact us on telegram</a></li>
            </ul>
            <table>
              <thead>
                <tr>
                  <th style="text-align:left">extraCode</th>
                  <th style="text-align:left">Description</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style="text-align:left">5501</td>
                  <td style="text-align:left">Firewall restriction: After multiple retries, the SDK still cannot establish a media connection, which will cause streaming and pulling to fail. <br />Handling suggestions: Refer to the tutorial <a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></td>
                </tr>
                <tr>
                  <td style="text-align:left">5502</td>
                  <td style="text-align:left">Re-entering the room failed: When the user experiences a network outage of more than 30s, the SDK will try to re-enter the room to restore the call, but the re-entry may fail due to the expiration of userSig, and this error will be thrown.<br />Handling suggestions: When encountering this error, you can use the latest userSig to call <a href="TRTC.html#enterRoom">TRTC.enterRoom</a> to enter the room again</td>
                </tr>
                <tr>
                  <td style="text-align:left">5503</td>
                  <td style="text-align:left">Re-entering the room failed: When the user experiences a network outage of more than 30s, the SDK will try to re-enter the room to restore the call, but the re-entry may fail due to the expiration of userSig, and this error will be thrown.<br />Handling suggestions: When encountering this error, you can use the latest userSig to call <a href="TRTC.html#enterRoom">TRTC.enterRoom</a> to enter the room again</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h4 class="name" id=".OPERATION_ABORT"><span class="type-signature">(static) </span>OPERATION_ABORT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5998</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: The error code thrown when the API execution is aborted. When the API is called or repeatedly called without meeting the API lifecycle, the API will abort execution to avoid meaningless operations. <br>For example: Call enterRoom, startLocalXxx and other interfaces continuously, and call exitRoom without entering the room.<br>
              The following interfaces will throw this error code when an exception occurs: <code>enterRoom</code>, <code>startLocalVideo</code>, <code>startLocalAudio</code>, <code>startScreenShare</code>, <code>startRemoteVideo</code>, <code>switchRole</code> <br>
              Handling suggestions: Capture and identify this error code, then avoid unnecessary calls in business logic, or you can do nothing, because the SDK has done side-effect-free processing, you only need to identify and ignore this error code when catching it.</p>
          </div>
          <h4 class="name" id=".UNKNOWN_ERROR"><span class="type-signature">(static) </span>UNKNOWN_ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>5999</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Description: Unknown error or undefined error<br>
              Handling suggestions: <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1">Contact us on telegram</a></p>
          </div>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:50 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      