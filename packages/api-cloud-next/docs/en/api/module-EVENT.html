<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Tencent RTC SDK - EVENT - Documentation</title>
    <meta name="description" content="RTC SDK Documentation. Run a demo within one minute and build solutions for audio/video calls or interactive live streaming within 30 minutes"/>
    <meta name="keywords" content="call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="zh-cn" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="切换至中文" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/en/', '/zh-cn/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    <a target="_blank" href="https://t.me/+EPk6TMZEZMM5OGY1" title="contact us on telegram" class="contact_us_tg" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" t="1685076466271" class="icon" viewBox="0 0 1024 1024" version="1.1" p-id="1951" width="24" height="24"><path d="M679.424 746.862l84.005-395.996c7.424-34.852-12.581-48.567-35.438-40.009L234.277 501.138c-33.72 13.13-33.134 32-5.706 40.558l126.282 39.424 293.156-184.576c13.714-9.143 26.295-3.986 16.018 5.157L426.898 615.973l-9.143 130.304c13.13 0 18.871-5.706 25.71-12.581l61.696-59.429 128 94.282c23.442 13.129 40.01 6.29 46.3-21.724zM1024 512c0 282.843-229.157 512-512 512S0 794.843 0 512 229.157 0 512 0s512 229.157 512 512z" fill="#1296DB" p-id="1952"/></svg></a>
    </div>
    
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Classes</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#enterRoom">enterRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#exitRoom">exitRoom</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#destroy">destroy</a></li>
            <li><span class="nav-separator">Local Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalAudio">updateLocalAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalAudio">stopLocalAudio</a></li>
            <li><span class="nav-separator">Local Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateLocalVideo">updateLocalVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopLocalVideo">stopLocalVideo</a></li>
            <li><span class="nav-separator">Local Screen Share</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateScreenShare">updateScreenShare</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopScreenShare">stopScreenShare</a></li>
            <li><span class="nav-separator">Remote Video</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updateRemoteVideo">updateRemoteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopRemoteVideo">stopRemoteVideo</a></li>
            <li><span class="nav-separator">Remote Audio</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#setRemoteAudioVolume">setRemoteAudioVolume</a></li>
            <li><span class="nav-separator">Plugins</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#startPlugin">startPlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#updatePlugin">updatePlugin</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#stopPlugin">stopPlugin</a></li>
            <li><span class="nav-separator">Others</span></li><li data-type='method' style='display: none;'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#sendSEIMessage">sendSEIMessage</a></li>
            <li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.isSupported">isSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getCameraList">getCameraList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getMicrophoneList">getMicrophoneList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.getSpeakerList">getSpeakerList</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#.setCurrentSpeaker">setCurrentSpeaker</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.code">code</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.extraCode">extraCode</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.functionName">functionName</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.message">message</a></li>
            <li data-type='member' style='display: none;'><a href="RtcError.html#.handler">handler</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-TYPE.html">TYPE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_LIVE">SCENE_LIVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.SCENE_RTC">SCENE_RTC</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_ANCHOR">ROLE_ANCHOR</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.ROLE_AUDIENCE">ROLE_AUDIENCE</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD">AUDIO_PROFILE_STANDARD</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_STANDARD_STEREO">AUDIO_PROFILE_STANDARD_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH">AUDIO_PROFILE_HIGH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.AUDIO_PROFILE_HIGH_STEREO">AUDIO_PROFILE_HIGH_STEREO</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_SMOOTH">QOS_PREFERENCE_SMOOTH</a></li>
            <li data-type='member' style='display: none;'><a href="module-TYPE.html#.QOS_PREFERENCE_CLEAR">QOS_PREFERENCE_CLEAR</a></li>
          </ul>
        </li>
        <li><a href="module-EVENT.html">EVENT</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.ERROR">ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUTOPLAY_FAILED">AUTOPLAY_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.KICKED_OUT">KICKED_OUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_ENTER">REMOTE_USER_ENTER</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_USER_EXIT">REMOTE_USER_EXIT</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">REMOTE_AUDIO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_AVAILABLE">REMOTE_VIDEO_AVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.AUDIO_PLAY_STATE_CHANGED">AUDIO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.VIDEO_PLAY_STATE_CHANGED">VIDEO_PLAY_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SCREEN_SHARE_STOPPED">SCREEN_SHARE_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.DEVICE_CHANGED">DEVICE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.PUBLISH_STATE_CHANGED">PUBLISH_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.STATISTICS">STATISTICS</a></li>
            <li data-type='member' style='display: none;'><a href="module-EVENT.html#.SEI_MESSAGE">SEI_MESSAGE</a></li>
          </ul>
        </li>
        <li><a href="module-ERROR_CODE.html">ERROR_CODE</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.ENV_NOT_SUPPORTED">ENV_NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.DEVICE_ERROR">DEVICE_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.SERVER_ERROR">SERVER_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_FAILED">OPERATION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.OPERATION_ABORT">OPERATION_ABORT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ERROR_CODE.html#.UNKNOWN_ERROR">UNKNOWN_ERROR</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Upstream Users Limitation</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Quick Start Call<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Camera/Mic</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Camera Profile</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Turn On/Off Camera/Mic</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detect Audio Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Rendering</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Check Environment and Device Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Detect Network Quality</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Handle Device Change</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enable Dual-Stream Mode</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enable Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Calls</a></li>
        <li><a href="tutorial-35-advanced-ai-denoiser.html">Implement AI noise reduction</a></li></ul>
        <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Handle Firewall Restriction</a></li>
        </ul>
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">EVENT</h1>
      <section>
        <header style="display:none">
        </header>
        <article>
          <div class="container-overview">
            <div class="description">
              <p><strong>TRTC Event List</strong><br>
                <br>
                Listen to specific events through <a href="TRTC.html#on">trtc.on(TRTC.EVENT.XXX)</a>. You can use these events to manage the room user list, manage user stream state, and perceive network state. The following is a detailed introduction to the events.
              </p>
              <blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;">
                <p>Notice：</p>
                <ul>
                  <li>Events need to be listened to before they are triggered, so that you can receive the corresponding event notifications. Therefore, it is recommended to complete event listening before entering the room, so as to ensure that no event notifications are missed.</li>
                </ul>
              </blockquote>
            </div>
            <dl class="details">
            </dl>
            <h5>Example</h5>
            <pre class="highlight lang-javascript"><code>// Usage:
trtc.on(TRTC.EVENT.ERROR, () => {});</code></pre>
          </div>
          <h3 class="subsection-title">Members</h3>
          <h4 class="name" id=".ERROR"><span class="type-signature">(static) </span>ERROR<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'error'</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li><a href="RtcError.html">RtcError</a></li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Error event, non-API call error, SDK throws when an unrecoverable error occurs during operation.</p>
            <ul>
              <li>Error code (error.code): <a href="module-ERROR_CODE.html#.OPERATION_FAILED">ErrorCode.OPERATION_FAILED</a></li>
              <li>Possible extended error codes (error.extraCode): 5501, 5502</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.ERROR, error => {
  console.error('trtc error observed: ' + error);
  const errorCode = error.code;
  const extraCode = error.extraCode;
});</code></pre>
          <h4 class="name" id=".AUTOPLAY_FAILED"><span class="type-signature">(static) </span>AUTOPLAY_FAILED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'autoplay-failed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Automatic playback failed, refer to <a href="tutorial-21-advanced-auto-play-policy.html">Handle Autoplay Restriction</a></p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUTOPLAY_FAILED, event => {
  // Guide user to click the page, SDK will resume playback automatically when user click the page.
  // Since v5.1.3+, you can get userId on this event.
  console.log(event.userId);
});</code></pre>
          <h4 class="name" id=".KICKED_OUT"><span class="type-signature">(static) </span>KICKED_OUT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'kicked-out'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Kicked out of the room for some reason, including:<br></p>
            <ul>
              <li>kick: The same user with same userId enters same room. The user who enters the room first will be kicked out of the room by the user who enters later.
                <ul>
                  <li>Entering a room with the same userId is not allowed behavior, which may lead to abnormal audio/video calls between the two parties, and should be avoided on the business side.</li>
                  <li>Users with the same userId who enter the same room with the same audience role may not receive this event.</li>
                </ul>
              </li>
              <li>banned: kicked out by the administrator using <a target="_blank" href="https://trtc.io/document/34267/34268">Server API - RemoveUser</a>.</li>
              <li>room_disband: kicked out by the administrator using <a target="_blank" href="https://trtc.io/document/34267/34269">Server API - DismissRoom</a>.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.KICKED_OUT, event => {
  console.log(event.reason)
});</code></pre>
          <h4 class="name" id=".REMOTE_USER_ENTER"><span class="type-signature">(static) </span>REMOTE_USER_ENTER<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-user-enter'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user enters the room event.</p>
            <ul>
              <li>In <code>live</code> mode, only the anchor has the notification of entering and leaving the room, and the audience does not have the notification of entering and leaving the room. The audience can receive the notification of entering and leaving the room of the anchor.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_USER_EXIT"><span class="type-signature">(static) </span>REMOTE_USER_EXIT<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-user-exit'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user exits the room event.</p>
            <ul>
              <li>In <code>live</code> mode, only the anchor has the notification of entering and leaving the room, and the audience does not have the notification of entering and leaving the room. The audience can receive the notification of entering and leaving the room of the anchor.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_AUDIO_AVAILABLE"><span class="type-signature">(static) </span>REMOTE_AUDIO_AVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-audio-available'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user publishes audio. You will receive this notification when the remote user turns on the microphone. Refer to: <a href="./tutorial-15-basic-dynamic-add-video.html">Turn on/off camera and microphone</a></p>
            <ul>
              <li>By default, the SDK automatically plays remote audio, and you do not need to call the API to play remote audio. You can listen for this event and <a href="module-EVENT.html#.REMOTE_AUDIO_UNAVAILABLE">REMOTE_AUDIO_UNAVAILABLE</a> to update the UI icon for &quot;whether the remote microphone is turned on&quot;.</li>
              <li>Note: If the user has not interacted with the page before entering the room, automatic audio playback may fail due to the <a href="./tutorial-21-advanced-auto-play-policy.html">browser's automatic playback policy restrictions</a>. You need to refer to the <a href="./tutorial-21-advanced-auto-play-policy.html">suggestions for handling automatic playback restrictions</a> for processing.</li>
              <li>If you do not want the SDK to automatically play audio, you can set receiveMode = TRTC.TYPE.RECEIVE_MODE_MANUAL to turn off automatic audio playback when <a href="TRTC.html#enterRoom">trtc.enterRoom()</a>.</li>
              <li>Listen for the <a href="module-EVENT.html#.REMOTE_AUDIO_AVAILABLE">TRTC.EVENT.REMOTE_AUDIO_AVAILABLE</a> event, record the userId with remote audio, and call the <a href="TRTC.html#muteRemoteAudio">trtc.muteRemoteAudio(userId, false)</a> method when you need to play audio.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Listen before entering the room
trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_AUDIO_UNAVAILABLE"><span class="type-signature">(static) </span>REMOTE_AUDIO_UNAVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-audio-unavailable'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user stops publishing audio. You will receive this notification when the remote user turns off the microphone.</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Listen before entering the room
trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, event => {
  const userId = event.userId;
});</code></pre>
          <h4 class="name" id=".REMOTE_VIDEO_AVAILABLE"><span class="type-signature">(static) </span>REMOTE_VIDEO_AVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-video-available'</li>
              </ul>
            </dd>
            <dt class="tag-see">See:</dt>
            <dd class="tag-see">
              <ul>
                <li><a href="module-TYPE.html#.STREAM_TYPE_MAIN">STREAM_TYPE_MAIN</a></li>
                <li><a href="module-TYPE.html#.STREAM_TYPE_SUB">STREAM_TYPE_SUB</a></li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user publishes video. You will receive this notification when the remote user turns on the camera. Refer to: <a href="./tutorial-15-basic-dynamic-add-video.html">Turn on/off camera and microphone</a></p>
            <ul>
              <li>You can listen for this event and <a href="module-EVENT.html#.REMOTE_VIDEO_UNAVAILABLE">REMOTE_VIDEO_UNAVAILABLE</a> to update the UI icon for &quot;whether the remote camera is turned on&quot;.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Listen before entering the room
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, event => {
  const userId = event.userId;
  const streamType = event.streamType;
  trtc.startRemoteVideo({userId, streamType, view});
});</code></pre>
          <h4 class="name" id=".REMOTE_VIDEO_UNAVAILABLE"><span class="type-signature">(static) </span>REMOTE_VIDEO_UNAVAILABLE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'remote-video-unavailable'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Remote user stops publishing video. You will receive this notification when the remote user turns off the camera.</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Listen before entering the room
trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, event => {
  const userId = event.userId;
  const streamType = event.streamType;
  // At this point, the SDK will automatically stop playing, and there is no need to call stopRemoteVideo.
});</code></pre>
          <h4 class="name" id=".AUDIO_VOLUME"><span class="type-signature">(static) </span>AUDIO_VOLUME<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'audio-volume'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Volume event<br>
              After calling the <a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a> interface to enable the volume callback, the SDK will throw this event regularly to notify the volume of each userId.<br>
              <strong>Note</strong>
            </p>
            <ul>
              <li>The callback contains the volume of the local microphone and the volume of the remote user. The callback will be triggered regardless of whether anyone is speaking.</li>
              <li>The event.result will be sorted from large to small according to the volume size.</li>
              <li>When userId is an empty string, it represents the volume of the local microphone.</li>
              <li>volume is a positive integer with a value of 0-100.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
   event.result.forEach(({ userId, volume }) => {
       const isMe = userId === ''; // When userId is an empty string, it represents the volume of the local microphone.
       if (isMe) {
           console.log(`my volume: ${volume}`);
       } else {
           console.log(`user: ${userId} volume: ${volume}`);
       }
   })
});
// Enable volume callback and trigger the event every 1000ms
trtc.enableAudioVolumeEvaluation(1000);</code></pre>
          <h4 class="name" id=".NETWORK_QUALITY"><span class="type-signature">(static) </span>NETWORK_QUALITY<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'network-quality'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Network quality statistics data event, which starts to be counted after entering the room and triggers every two seconds. This data reflects the network quality of your local uplink and downlink.</p>
            <ul>
              <li>
                <p>The uplink network quality (uplinkNetworkQuality) refers to the network situation of uploading local streams (uplink connection network quality from SDK to Tencent Cloud)</p>
              </li>
              <li>
                <p>The downlink network quality (downlinkNetworkQuality) refers to the average network situation of downloading all streams (average network quality of all downlink connections from Tencent Cloud to SDK)</p>
                <p>The enumeration values and meanings are shown in the following table:</p>
                <table>
                  <thead>
                    <tr>
                      <th style="text-align:left">Value</th>
                      <th style="text-align:left">Meaning</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td style="text-align:left">0</td>
                      <td style="text-align:left">Network state is unknown, indicating that the current trtc instance has not established an uplink/downlink connection</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">1</td>
                      <td style="text-align:left">Network state is excellent</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">2</td>
                      <td style="text-align:left">Network state is good</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">3</td>
                      <td style="text-align:left">Network state is average</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">4</td>
                      <td style="text-align:left">Network state is poor</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">5</td>
                      <td style="text-align:left">Network state is very poor</td>
                    </tr>
                    <tr>
                      <td style="text-align:left">6</td>
                      <td style="text-align:left">Network connection is disconnected<br />Note: If the downlink network quality is this value, it means that all downlink connections have been disconnected</td>
                    </tr>
                  </tbody>
                </table>
              </li>
              <li>
                <p>uplinkRTT, uplinkLoss are the uplink RTT (ms) and uplink packet loss rate.</p>
              </li>
              <li>
                <p>downlinkRTT, downlinkLoss are the average RTT (ms) and average packet loss rate of all downlink connections.</p>
              </li>
            </ul>
            <p><strong>Note</strong></p>
            <ul>
              <li>If you want to know the uplink and downlink network conditions of the other party, you need to broadcast the other party's network quality through IM.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.NETWORK_QUALITY, event => {
   console.log(`network-quality, uplinkNetworkQuality:${event.uplinkNetworkQuality}, downlinkNetworkQuality: ${event.downlinkNetworkQuality}`)
   console.log(`uplink rtt:${event.uplinkRTT} loss:${event.uplinkLoss}`)
   console.log(`downlink rtt:${event.downlinkRTT} loss:${event.downlinkLoss}`)
})</code></pre>
          <h4 class="name" id=".CONNECTION_STATE_CHANGED"><span class="type-signature">(static) </span>CONNECTION_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'connection-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>SDK and Tencent Cloud connection state change event, you can use this event to listen to the overall connection state of the SDK and Tencent Cloud.<br></p>
            <ul>
              <li>'DISCONNECTED': Connection disconnected</li>
              <li>'CONNECTING': Connecting</li>
              <li>'CONNECTED': Connected</li>
            </ul>
            <p>Meanings of different state changes:</p>
            <ul>
              <li>DISCONNECTED -&gt; CONNECTING: Trying to establish a connection, triggered when calling the enter room interface or when the SDK automatically reconnects.</li>
              <li>CONNECTING -&gt; DISCONNECTED: Connection establishment failed, triggered when calling the exit room interface to interrupt the connection or when the connection fails after SDK retries.</li>
              <li>CONNECTING -&gt; CONNECTED: Connection established successfully, triggered when the connection is successful.</li>
              <li>CONNECTED -&gt; DISCONNECTED: Connection interrupted, triggered when calling the exit room interface or when the connection is disconnected due to network anomalies.</li>
            </ul>
            <p>Suggestion: You can listen to this event and display different UIs in different states to remind users of the current connection state.</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.CONNECTION_STATE_CHANGED, event => {
  const prevState = event.prevState;
  const curState = event.state;
});</code></pre>
          <h4 class="name" id=".AUDIO_PLAY_STATE_CHANGED"><span class="type-signature">(static) </span>AUDIO_PLAY_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'audio-play-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Audio playback state change event</p>
            <p>event.userId When userId is an empty string, it represents the local user, and when it is a non-empty string, it represents a remote user.</p>
            <p>event.state The value is as follows:</p>
            <ul>
              <li>'PLAYING': start playing
                <ul>
                  <li>event.reason is 'playing' or 'unmute'.</li>
                </ul>
              </li>
              <li>'PAUSED': pause playback
                <ul>
                  <li>When event.reason is 'pause', it is triggered by the pause event of the &lt;audio&gt; element. The following situations will trigger:
                    <ul>
                      <li>Call the HTMLMediaElement.pause interface.</li>
                    </ul>
                  </li>
                  <li>When event.reason is 'mute'. See event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a>
                    <ul>
                      <li>When userId is oneself, this event is triggered, indicating that audio collection is paused, usually caused by device abnormalities, such as being preempted by other applications on the device, at this time, the user needs to be guided to recollect.</li>
                      <li>When userId is others, this event is triggered, indicating that the received audio data is not enough to play. Usually caused by network jitter, no processing is required on the access side. When the received data is sufficient to play, it will automatically resume.</li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li>'STOPPED': stop playing
                <ul>
                  <li>event.reason is 'ended'.</li>
                </ul>
              </li>
            </ul>
            <p>event.reason The reason for the state change, the value is as follows:</p>
            <ul>
              <li>'playing': start playing, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event%20"> HTMLMediaElement.playing_event</a></li>
              <li>'mute': The audio track cannot provide data temporarily, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a></li>
              <li>'unmute': The audio track resumes providing data, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event%20"> MediaStreamTrack.unmute_event</a></li>
              <li>'ended': The audio track has been closed</li>
              <li>'pause': Playback paused</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.AUDIO_PLAY_STATE_CHANGED, event => {
  console.log(`${event.userId} player is ${event.state} because of ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".VIDEO_PLAY_STATE_CHANGED"><span class="type-signature">(static) </span>VIDEO_PLAY_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'video-play-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Video playback state change event</p>
            <p>event.userId When userId is an empty string, it represents the local user, and when it is a non-empty string, it represents a remote user.</p>
            <p>event.streamType Stream type, value: <a href="module-TYPE.html#.STREAM_TYPE_MAIN">TRTC.TYPE.STREAM_TYPE_MAIN</a> <a href="module-TYPE.html#.STREAM_TYPE_SUB">TRTC.TYPE.STREAM_TYPE_SUB</a></p>
            <p>event.state The value is as follows:</p>
            <ul>
              <li>'PLAYING': start playing
                <ul>
                  <li>event.reason is 'playing' or 'unmute'.</li>
                </ul>
              </li>
              <li>'PAUSED': pause playback
                <ul>
                  <li>When event.reason is 'pause', it is triggered by the pause event of the &lt;video&gt; element. The following situations will trigger:
                    <ul>
                      <li>Call the HTMLMediaElement.pause interface.</li>
                      <li>After successful playback, the view container for playing the video is removed from the DOM.</li>
                    </ul>
                  </li>
                  <li>When event.reason is 'mute'. See event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a>
                    <ul>
                      <li>When userId is oneself, this event is triggered, indicating that video collection is paused, usually caused by device abnormalities, such as being preempted by other applications on the device, at this time, the user needs to be guided to recollect.</li>
                      <li>When userId is others, this event is triggered, indicating that the received video data is not enough to play. Usually caused by network jitter, no processing is required on the access side. When the received data is sufficient to play, it will automatically resume.</li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li>'STOPPED': stop playing
                <ul>
                  <li>event.reason is 'ended'.</li>
                </ul>
              </li>
            </ul>
            <p>event.reason The reason for the state change, the value is as follows:</p>
            <ul>
              <li>'playing': start playing, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/HTMLMediaElement/playing_event%20"> HTMLMediaElement.playing_event</a></li>
              <li>'mute': The video track cannot provide data temporarily, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/mute_event%20"> MediaStreamTrack.mute_event</a></li>
              <li>'unmute': The video track resumes providing data, see event <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaStreamTrack/unmute_event%20"> MediaStreamTrack.unmute_event</a></li>
              <li>'ended': The video track has been closed</li>
              <li>'pause': Playback paused</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.VIDEO_PLAY_STATE_CHANGED, event => {
  console.log(`${event.userId} ${event.streamType} video player is ${event.state} because of ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".SCREEN_SHARE_STOPPED"><span class="type-signature">(static) </span>SCREEN_SHARE_STOPPED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'screen-sharing-stopped'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Notification event for local screen sharing stop, only valid for local screen sharing streams.</p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, () => {
  console.log('screen sharing was stopped');
});</code></pre>
          <h4 class="name" id=".DEVICE_CHANGED"><span class="type-signature">(static) </span>DEVICE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'device-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Notification event for device changes such as camera and microphone.</p>
            <ul>
              <li>event.device is a <a target="_blank" href="https://developer.mozilla.org/en-US/docs/Web/API/MediaDeviceInfo">MediaDeviceInfo</a> object with properties:
                <ul>
                  <li>deviceId: device ID</li>
                  <li>label: device description information</li>
                  <li>groupId: device group ID</li>
                </ul>
              </li>
              <li>event.type value: <code>'camera'|'microphone'|'speaker'</code></li>
              <li>event.action value:
                <ul>
                  <li>'add' device has been added.</li>
                  <li>'remove' device has been removed.</li>
                  <li>'active' device has been activated, for example: after startLocalVideo is successful, this event will be triggered.</li>
                </ul>
              </li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
  console.log(`${event.type}(${event.device.label}) ${event.action}`);
});</code></pre>
          <h4 class="name" id=".PUBLISH_STATE_CHANGED"><span class="type-signature">(static) </span>PUBLISH_STATE_CHANGED<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'publish-state-changed'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>Publish state change event.</p>
            <ul>
              <li>event.mediaType media type, value: <code>'audio'|'video'|'screen'</code>.</li>
              <li>event.state current publish state, value:
                <ul>
                  <li><code>'starting'</code> trying to publish stream</li>
                  <li><code>'started'</code> publish stream succeeded</li>
                  <li><code>'stopped'</code> publish stream stopped, see event.reason field for the reason</li>
                </ul>
              </li>
              <li>event.prevState the publish state at the last event trigger, with the same type as event.state.</li>
              <li>event.reason the reason for the publish state to become <code>'stopped'</code>, value:
                <ul>
                  <li><code>'timeout'</code> publish stream timeout, usually caused by network jitter or firewall interception. The SDK will keep retrying, and the business side can guide the user to check the network or change the network at this time.</li>
                  <li><code>'error'</code> publish stream error, at this time, you can get the specific error information from event.error, usually caused by the browser not supporting H264 encoding.</li>
                  <li><code>'api-call'</code> publish stream stopped due to business side API call, for example, stopLocalVideo was called to stop the publish stream before startLocalVideo was successful, which is a normal behavior and the business side does not need to pay attention to it.</li>
                </ul>
              </li>
              <li>event.error error information when event.reason is <code>'error'</code>.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
  console.log(`${event.mediaType} ${event.state} ${event.reason}`);
});</code></pre>
          <h4 class="name" id=".STATISTICS"><span class="type-signature">(static) </span>STATISTICS<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>v5.2.0</li>
              </ul>
            </dd>
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'statistics'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>TRTC statistics.<br></p>
            <ul>
              <li>SDK will fires this event once every 2s.</li>
              <li>You can get the network quality, statistics of audio and video from this event. For detailed parameter description, please refer to <a href="global.html#TRTCStatistics">TRTCStatistics</a>.</li>
            </ul>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.STATISTICS, statistics => {
   console.warn(statistics.rtt, statistics.upLoss, statistics.downLoss);
})</code></pre>
          <h4 class="name" id=".SEI_MESSAGE"><span class="type-signature">(static) </span>SEI_MESSAGE<span class="type-signature"></span></h4>
          <dl class="details">
            <dt class="tag-default">Default Value:</dt>
            <dd class="tag-default">
              <ul class="dummy">
                <li>'sei-message'</li>
              </ul>
            </dd>
          </dl>
          <div class="description">
            <p>收到 SEI message<br></p>
          </div>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
   console.log(`收到 ${event.userId} 的 sei message: ${event.data}, streamType: ${event.streamType}`)
})</code></pre>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a target="_blank" href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Fri Nov 17 2023 19:34:50 GMT+0800 (中国标准时间) using the <a target="_blank" href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', `/${lang}/`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      