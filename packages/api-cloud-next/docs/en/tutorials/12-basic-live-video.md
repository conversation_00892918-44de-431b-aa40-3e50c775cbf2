## Function Description

This article mainly introduces how to implement the audio and video call function of the interconnection live broadcast scene.

## Anchor

The process of implementing audio and video calls on the anchor side is basically the same as the implementation process of the {@link module:TYPE.SCENE_RTC TRTC.TYPE.SCENE_RTC} scene. Refer to: [Start integrating audio and video calls](./tutorial-11-basic-video-call.html).

The main difference is the screne and role parameters set when entering the room. Refer to the following sample code:

```js
await trtc.enterRoom({ sdkAppId, userId, userSig, roomId, scene: TRTC.TYPE.SCENE_LIVE, role: TRTC.TYPE.ROLE_ANCHOR });
```

## Audience

### Enter the room as an audience

Set the role parameter to TRTC.TYPE.ROLE_AUDIENCE.

```javascript
await trtc.enterRoom({ roomId, sdkAppId, userId, userSig, scene: TRTC.TYPE.SCENE_LIVE, role: TRTC.TYPE.ROLE_AUDIENCE })
```

### Play remote audio

By default, the SDK will automatically play remote audio, and you do not need to call the API to play remote audio.

- Note: If the user has not interacted with the page before entering the room, automatic audio playback may fail due to the [automatic playback policy restrictions of the browser], and you need to refer to [suggestions for handling restricted automatic playback](./tutorial-21-advanced-auto-play-policy.html).
- If you do not want the SDK to automatically play audio, you can set autoReceiveAudio = false to turn off automatic audio playback when {@link TRTC#enterRoom trtc.enterRoom()}.
- Listen to the {@link module:EVENT.REMOTE_AUDIO_AVAILABLE TRTC.EVENT.REMOTE_AUDIO_AVAILABLE} event, record the userId with remote audio, and call the {@link TRTC#muteRemoteAudio trtc.muteRemoteAudio(userId, false)} method when you need to play audio.


### Play remote video

Listen to the {@link module:EVENT.REMOTE_VIDEO_AVAILABLE TRTC.EVENT.REMOTE_VIDEO_AVAILABLE} event before entering the room. When you receive this event, play the remote video stream through {@link TRTC#startRemoteVideo trtc.startRemoteVideo()}.

```javascript
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
  // To play the video, you need to place an HTMLElement in the DOM, which can be a div tag, assuming its id is `${userId}_${streamType}`
  const view = `${userId}_${streamType}`;
  trtc.startRemoteVideo({ userId, streamType, view });
});
```


### Connect with the anchor for interaction

The audience role does not have the permission to publish audio and video, so if the audience wants to interact with the anchor, they need to switch to the anchor role using {@link TRTC#switchRole switchRole} and then publish audio and video streams.

```javascript
// Switch to the anchor to go on the air
await trtc.switchRole(TRTC.TYPE.ROLE_ANCHOR);

// Turn on the microphone after going on the air
await trtc.startLocalAudio();
// Turn on the camera after going on the air
await trtc.startLocalVideo();

// After the interaction is over, call exitRoom to exit the room and end the interaction.
await trtc.exitRoom();
```

