
The term `upstream user` refers to the user who is currently publishing audio and video streams. After the user successfully publish the local stream by calling{@link TRTC#startLocalVideo startLocalVideo()} | {@link TRTC#startLocalAudio startLocalAudio()} | {@link TRTC#startScreenShare startScreenShare()}, the user becomes an `upstream user`. After the user unpublish the local stream by calling {@link TRTC#stopLocalVideo stopLocalVideo()} & {@link TRTC#stopLocalAudio stopLocalAudio()} & {@link TRTC#stopScreenShare stopScreenShare()}, the user is no longer an upstream user.

## Limit on the Number of Upstream Users

The real-time audio and video backend server limits the number of upstream users in a single room to 50. If there are more than 50 upstream users in a room, the 51st user who attempts to publish a local audio and video stream will fail to publish. If the App business layer wants the 51st user to publish a local stream, it should first let other users stop publishing local streams by calling {@link TRTC#stopLocalVideo stopLocalVideo()} to make room for the new user to become an `upstream user`.


