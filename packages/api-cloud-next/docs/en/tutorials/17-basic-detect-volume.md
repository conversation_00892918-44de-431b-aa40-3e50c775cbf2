
## Function Description
This article mainly introduces how to detect the volume size.

Volume size judgment is mainly applied to:

- Detect the volume of the local microphone
- Detect the volume of remote users

## Implementation Steps
Listen to the {@link module:EVENT.AUDIO_VOLUME TRTC.EVENT.AUDIO_VOLUME} event, and then call {@link TRTC#enableAudioVolumeEvaluation enableAudioVolumeEvaluation()} to enable the volume callback event.

```javascript
trtc.on(TRTC.EVENT.AUDIO_VOLUME, event => {
    event.result.forEach(({ userId, volume }) => {
        const isMe = userId === ''; // When userId is an empty string, it represents the volume of the local microphone.
        if (isMe) {
            console.log(`my volume: ${volume}`);
        } else {
            console.log(`user: ${userId} volume: ${volume}`);
        }
    })
});

// Enable volume callback and trigger the event every 500ms
trtc.enableAudioVolumeEvaluation(500);
// For performance reasons, when the page switches to the background, the SDK will not throw volume callback events. If you need to receive volume callback events when the page is switched to the background, you can set this parameter to true.
trtc.enableAudioVolumeEvaluation(500, true);

// To turn off the volume callback, pass in an interval value less than or equal to 0
trtc.enableAudioVolumeEvaluation(-1);
```



