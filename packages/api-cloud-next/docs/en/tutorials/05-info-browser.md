This article introduces the browser support status of the Web SDK, as well as suggestions for handling access protocol restrictions and firewall restrictions.
## Browser compatibility information
<table>
<tr>
<th>Operating System</th>
<th>Browser Type</th>
<th>Minimum Browser<br>Version Requirement</th>
<th>SDK Version Requirement</th>
<th>Receiving (Pulling Stream)</th>
<th>Sending (Pushing Stream)</th>
<th>Screen Sharing</th>
</tr>
<tr>
<td rowspan="11">Windows</td>
<td>Desktop Chrome Browser</td>
<td>56+</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Support Chrome72+ version</td>
</tr>
<tr>
<td>Desktop QQ Browser (Speed ​​Core)</td>
<td>10.4+</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td>Desktop Firefox Browser</td>
<td>56+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support Firefox66+ version</td>
</tr>
<tr>
<td>Desktop Edge Browser</td>
<td>80+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support</td>
</tr>
<tr>
<td>Desktop Sogou Browser (High-speed mode)</td>
<td>11+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support</td>
</tr>
<tr>
<td>Desktop Sogou Browser (compatibility mode)</td>
<td>-</td>
<td>-</td>
<td>Not supported</td>
<td>Not supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Desktop Opera Browser</td>
<td>46+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support Opera60+ version</td>
</tr>
<tr>
<td>Desktop 360 Security Browser (Speed ​​mode)</td>
<td>13+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support</td>
</tr>
<tr>
<td>Desktop 360 Security Browser (compatibility mode)</td>
<td>-</td>
<td>-</td>
<td>Not supported</td>
<td>Not supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Desktop WeChat embedded browser</td>
<td>-</td>
<td>-</td>
<td>Support</td>
<td>Not supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Desktop Enterprise WeChat embedded browser</td>
<td>4.0.8+ (Enterprise WeChat version)</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td rowspan="7">Mac OS</td>
<td>Desktop Safari Browser</td>
<td>11+</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Support Safari13+ version</td>
</tr>
<tr>
<td>Desktop Chrome Browser</td>
<td>56+</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Support Chrome72+ version</td>
</tr>
<tr>
<td>Desktop Firefox Browser</td>
<td>56+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support Firefox66+ version<a href="#attention3">(Note [3])</a></td>
</tr>
<tr>
<td>Desktop Edge Browser</td>
<td>80+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support</td>
</tr>
<tr>
<td>Desktop Opera Browser</td>
<td>46+</td>
<td>v4.7.0+</td>
<td>Support</td>
<td>Support</td>
<td>Support Opera60+ version</td>
</tr>
<tr>
<td>Desktop WeChat embedded browser</td>
<td>-</td>
<td>-</td>
<td>Support</td>
<td>Not supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Desktop Enterprise WeChat embedded browser</td>
<td>4.0.8+ (Enterprise WeChat version)</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td rowspan="6">Android</td>
<td>WeChat embedded browser (TBS kernel)</td>
<td>-</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td>WeChat embedded browser (XWEB kernel)</td>
<td>-</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td>Enterprise WeChat embedded browser</td>
<td>-</td>
<td>-</td>
<td>Support</td>
<td>Support</td>
<td>Not supported</td>
</tr>
<tr>
<td>Mobile Chrome Browser</td>
<td>-</td>
<td>-</td>
<td>Supported</td>
<td>Supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Mobile QQ Browser</td>
<td>-</td>
<td>-</td>
<td>Not supported</td>
<td>Not supported</td>
<td>Not supported</td>
</tr>
<tr>
<td>Mobile UC Browser</td> 
<td>-</td> 
<td>-</td> 
<td>Not supported</td> 
<td>Not supported</td> 
<td>Not supported</td> 
</tr> 
<tr> 
<td>iOS 12.1.4+</td> 
<td>WeChat embedded browser</td> 
<td>-</td> 
<td>-</td> 
<td>Supported</td> 
<td>Not supported</td> 
<td>Not supported</td> 
</tr> 
<tr> 
<td>iOS 14.3+</td> 
<td>WeChat embedded browser</td> 
<td>6.5+ (WeChat version)</td> 
<td>-</td> 
<td>Supported</td> 
<td>Supported</td> 
<td>Not supported</td> 
</tr> 
<tr> 
<td>iOS</td> 
<td>Enterprise WeChat embedded browser</td> 
<td>4.0.8+ (Enterprise WeChat version)</td> 
<td>-</td> 
<td>Supported</td> 
<td>Supported</td> 
<td>Not supported</td> 
</tr> 
<tr> 
<td>iOS 11.0+</td> 
<td>Mobile Safari Browser</td> 
<td>11+</td> 
<td>-</td> 
<td>Supported</td> 
<td>Supported</td> 
<td>Not supported</td>
</tr>
<tr> 
<td>iOS 12.1.4+</td> 
<td>Mobile Chrome Browser</td> 
<td>-</td> 
<td>-</td> 
<td>Supported</td> 
<td>Not supported</td> 
<td>Not supported</td> 
</tr> 
<tr> 
<td>iOS 14.3+</td> 
<td>Mobile Chrome Browser</td> 
<td>-</td> 
<td>-</td> 
<td>Supported</td> 
<td>Supported</td> 
<td>Not supported</td>
</tr>
</table>

> !
> - For other browser environments, you can open the [TRTC detection page](https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html) to check the ability support status.
> - Mac Firefox screen sharing may cause video partial area displacement, [Firefox bug](https://bugzilla.mozilla.org/show_bug.cgi?id=1536777). It cannot be avoided temporarily. It is recommended to use Chrome or Safari browser for screen sharing.
> - [WebRTC known issues and workarounds](./tutorial-02-info-webrtc-issues.html).
> - Due to H.264 copyright restrictions, Huawei Chrome below version 88 cannot use H264 encoding (that is, cannot push streams). 
> - It is recommended that you update TRTC Web SDK to the latest version in time to obtain better product stability and online support. For version upgrade precautions, please refer to: [Upgrade Guide](./tutorial-00-info-update-guideline.html).


## Explanation of Page Access Protocol

For security and privacy reasons, browser vendors restrict web pages from using all the features of TRTC Web SDK (WebRTC) unless they are accessed under the https protocol. To ensure that users in production environments can smoothly access and experience all the features of TRTC Web SDK, please use the https protocol to access the audio and video application page.

Note: Local development can be accessed through the http://localhost or file:// protocol.

The URL domain name and protocol support are shown in the following table:

| Application Scenario | Protocol | Receive (Pull Stream) | Send (Push Stream) | Screen Sharing | Remarks |
| -------------------- | :------: | :------------------: | :---------------: | :-----------: | :-----: |
| Production Environment | https | Supported | Supported | Supported | Recommended |
| Production Environment | http | Supported | Not Supported | Not Supported | |
| Local Development Environment | http://localhost | Supported | Supported | Supported | Recommended |
| Local Development Environment | http://127.0.0.1 | Supported | Supported | Supported | |
| Local Development Environment | http://[Local IP] | Supported | Not Supported | Not Supported | |

> !
> - If your development environment cannot be accessed through localhost and does not have the https protocol, it is recommended that you use a reverse proxy tool to proxy access requests from a certain https domain to your development environment, such as whistle or fiddler.

