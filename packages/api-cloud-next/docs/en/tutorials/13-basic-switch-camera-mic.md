
This article mainly introduces how to switch cameras and microphones during audio and video calls.

## Switching Cameras

```js
// Open the camera, the default is the first camera in the camera list.
await trtc.startLocalVideo();

const cameraList = await TRTC.getCameraList();
// Switch to the second camera
if (cameraList[1]) {
  await trtc.updateLocalVideo({ option: { cameraId: cameraList[1].deviceId }});
}

// On mobile device.
// switch to front camera.
await trtc.updateLocalVideo({ option: { useFrontCamera: true }});
// switch to rear camera.
await trtc.updateLocalVideo({ option: { useFrontCamera: false }});
```

## Switching Microphones


```js
// Open the microphone, the default is the first microphone in the microphone list.
await trtc.startLocalAudio();

const microphoneList = await TRTC.getMicrophoneList();
// Switch to the second microphone
if (microphoneList[1]) {
  await trtc.updateLocalAudio({ option: { microphoneId: microphoneList[1].deviceId }});
}
```


