/**
 * Plugin name
 * @typedef {'AudioMixer'|'AIDenoiser'|'Watermark'|'VirtualBackground'|'SmallStreamAutoSwitcher'} PluginName
 */


/**
 * AudioMixer start plugin options
 * @typedef {Object} AudioMixerOptions
 * @property {string} id Please set a unique ID for each incoming music url.
 * @property {string} url The url of the music address in MP3, AAC (and other audio formats supported by the browser).
 * @property {boolean} [loop] Whether the background music is repeated or not
 * @property {number} [volume] Background music playback volume (0-1)
 */

/**
 * AudioMixer update plugin options
 * @typedef {Object} UpdateAudioMixerOptions
 * @property {string} id Unique ID for music you set before.
 * @property {boolean} [loop] Whether the background music is repeated or not
 * @property {number} [volume] Background music playback volume (0-1)
 * @property {number} [seekFrom] Start seek from X seconds
 * @property {string} [operation] Operation of the background music: 'pause' ｜ 'resume' ｜ 'stop'
 */

/**
 * AudioMixer stop plugin options
 * @typedef {Object} StopAudioMixerOptions
 * @property {string} id Unique ID for music you set before.
 */

/**
 * AIDenoiser start plugin options
 * @typedef {Object} AIDenoiserOptions
 * @property {string} assetsPath denoiser wasm assets path
 * @property {number} sdkAppId application SDKAppId
 * @property {string} userId current user's userId
 * @property {string} userSig current user's userSig
 */

/**
 * VirtualBackground start plugin options, tutorial see {@tutorial 36-advanced-virtual-background}
 * @typedef {Object} VirtualBackgroundOptions
 * @property {number} sdkAppId Current application ID
 * @property {string} userId Current user ID
 * @property {string} userSig UserSig corresponding to the user ID
 * @property {string=} type - `image` for image background <br> - `blur` for background blur (default)
 * @property {number=} blurLevel - Set the level of blur when `type` is set to `blur`. Default value is 3, with a range from 1 to 10.
 * @property {string=} src Required if type is `image`  | Image URL, such as `https://picsum.photos/seed/picsum/200/300
 * @property {function=} onAbort Callback to stop plugin due to error.
 */

/**
 * VirtualBackground 插件更新参数，教程可见 {@tutorial 36-advanced-virtual-background}
 * @typedef {Object} UpdateVirtualBackgroundOptions
 * @property {string} type - `image` for image background <br> - `blur` for background blur (default)
 * @property {number=} blurLevel - Set the level of blur when `type` is set to `blur`. Default value is 3, with a range from 1 to 10.
 * @property {string=} src Required if type is `image`  | Image URL, such as `https://picsum.photos/seed/picsum/200/300
 * @property {function=} onAbort Callback to stop plugin due to error.
 */

/**
 * Watermark start plugin options, tutorial see {@tutorial 29-advanced-water-mark}
 * @typedef {Object} WatermarkOptions
 * @property {string} imageUrl - Image watermark URL. This parameter is required.
 * @property {string} [x] - Watermark left margin. This parameter is optional.
 * @property {string} [y] - Watermark top margin. This parameter is optional.
 * @property {string|number|object} [size] - Specifies the size of the watermark. This parameter is optional. The default is `cover`.
 *   When passing a string:
 *   - `"cover"` scales the background image to fully cover the background area, which may cause parts of the background image to be invisible.
 *   - `"contain"` scales the background image to fit entirely within the background area, possibly leaving some areas blank.
 *   When passing a number:
 *   - `x` scales the background image by x times, e.g., 0.5, with a valid range of `(0,1]`.
 *   When passing an object:
 *   - You can specify manually by passing `{width: 200, height: 300}`.
 *
 */

/**
 * SmallStreamAutoSwitcher start plugin options, tutorial see {@tutorial 41-advanced-small-stream-auto-switcher}
 * @typedef {Object} SmallStreamAutoSwitcherOptions
 * @property {number} sdkAppId Current application ID
 * @property {string} userId Current user ID
 * @property {string} userSig UserSig corresponding to the user ID
 */

/**
 * SmallStreamAutoSwitcher 插件配置选项
 * @typedef {Object} SmallStreamAutoSwitcherStartOptions
 * @property {number} [rttPoorLimit=200] 延迟阈值（毫秒），当网络延迟超过此值时，开始计算切换至小流的条件
 * @property {number} [lossPoorLimit=20] 丢包率阈值（百分比），当丢包率超过此值时，开始计算切换至小流的条件
 * @property {number} [poorDuration=10000] 网络状况差的持续时间阈值（毫秒），当延迟或丢包率连续超过阈值达到此时长时，开始检查是否切换至小流
 * @property {number} [rttGoodLimit=100] 良好延迟阈值（毫秒），当网络延迟低于此值时，开始计算切换至大流的条件
 * @property {number} [lossGoodLimit=10] 良好丢包率阈值（百分比），当丢包率低于此值时，开始计算切换至大流的条件
 * @property {number} [goodDuration=20000] 网络状况良好的持续时间阈值（毫秒），当延迟和丢包率连续低于阈值达到此时长时，开始检查是否切换至大流
 * @property {number} [sleepTime=30000] 切换后的休眠时间（毫秒），在此期间不会再次触发自动切换，避免频繁切换
 * @property {number} [maxAutoSwitchToSmallCount=2] 每个用户允许自动切换至小流的最大次数，超过此次数后不再自动切换至小流
 */
