{"name": "@rtc-plugin/watermark", "version": "5.12.0", "description": "TRTC Web SDK 5.x watermark plugin", "main": "./watermark.esm.js", "module": "./watermark.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-36-advanced-watermark.html", "keywords": ["webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone", "live streaming", "real-time communication", "blur background"], "types": "./watermark.esm.d.ts"}