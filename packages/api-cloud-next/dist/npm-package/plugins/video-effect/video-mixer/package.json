{"name": "@rtc-plugin/video-mixer", "version": "5.12.0", "description": "TRTC Web SDK 5.x videoMixer plugin", "main": "./video-mixer.esm.js", "module": "./video-mixer.esm.js", "repository": {"type": "git", "url": "**************:LiteAVSDK/TRTC_Web.git"}, "homepage": "https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/en/tutorial-40-advanced-video-mixer.html", "keywords": ["webrtc", "TRTC", "rtc", "call", "video call", "audio call", "javascript", "video", "audio", "camera", "microphone", "live streaming", "real-time communication", "blur background"], "types": "./video-mixer.esm.d.ts"}