var __defProp=Object.defineProperty,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,i,r)=>i in e?__defProp(e,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[i]=r,__spreadValues=(e,i)=>{for(var r in i||(i={}))__hasOwnProp.call(i,r)&&__defNormalProp(e,r,i[r]);if(__getOwnPropSymbols)for(var r of __getOwnPropSymbols(i))__propIsEnum.call(i,r)&&__defNormalProp(e,r,i[r]);return e},__publicField=(e,i,r)=>__defNormalProp(e,"symbol"!=typeof i?i+"":i,r),layoutProperty={x:{required:!0,type:"number"},y:{required:!0,type:"number"},width:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},height:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},zIndex:{required:!0,type:"number"},fillMode:{required:!1,type:"string"},mirror:{required:!1,type:"boolean"},rotation:{required:!1,type:"number"},hidden:{required:!1,type:"boolean"}},canvasValidateRule=(e,i=!1)=>({type:"object",required:i,properties:{canvasColor:{required:!1,type:["string",CanvasGradient,CanvasPattern]},width:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},height:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},frameRate:{required:!1,type:"number",notLessThanZero:!0,min:1,max:60}},validate(i,r,t){const{RtcError:a,ErrorCode:o,ErrorCodeDictionary:s}=e.errorModule;if(!i)return;const{width:n,height:l}=i;if(n&&l&&n*l>8294400)throw new a({code:o.INVALID_PARAMETER,message:"The mix resolution cannot be set higher than 3840 * 2160."})}}),viewValidateRule=e=>({required:!1,type:["string",HTMLElement,null],validate(i,r,t){const{RtcError:a,ErrorCode:o,ErrorCodeDictionary:s}=e.errorModule;if(e.utils.isString(i)){if(!document.getElementById(i))throw new a({code:o.INVALID_PARAMETER,extraCode:s.INVALID_ELEMENT_ID,fnName:t,messageParams:{key:r}})}}}),layoutValidateRule=(e,i=!0)=>({type:"object",required:i,properties:__spreadValues({},layoutProperty),validate(i,r,t){const{RtcError:a,ErrorCode:o,ErrorCodeDictionary:s}=e.errorModule;if(i){if(i.fillMode&&!["contain","cover","fill"].includes(i.fillMode))throw new a({code:o.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_TYPE,message:"The fillMode parameter must be 'contain', 'cover' or 'fill'",fnName:t});if(i.rotation&&![0,90,180,270].includes(i.rotation))throw new a({code:o.INVALID_PARAMETER,extraCode:s.INVALID_PARAMETER_TYPE,message:"The rotation parameter must be 0, 90, 180 or 270",fnName:t})}}}),cameraValidateRule=e=>({type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},cameraId:{required:!1,type:"string"},videoTrack:{required:!1,instanceof:MediaStreamTrack},profile:{required:!1,type:["string","object"],properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},layout:__spreadValues({},layoutValidateRule(e))}}}),screenValidateRule=e=>({type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},profile:{required:!1,type:["string","object"],properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},captureElement:{required:!1,type:HTMLElement},preferDisplaySurface:{required:!1,type:"string"},layout:__spreadValues({},layoutValidateRule(e))},validate(i,r,t){const{RtcError:a,ErrorCode:o,ErrorCodeDictionary:s}=e.errorModule;if(!e.rtcDectection.isScreenCaptureApiAvailable())throw new a({code:o.ENV_NOT_SUPPORTED,fnName:t,extraCode:s.NOT_SUPPORTED_SCREEN_SHARE})}}}),textValidateRule=e=>({type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},content:{required:!0,type:"string"},font:{required:!1,type:"string"},color:{required:!1,type:["string",CanvasGradient,CanvasPattern]},layout:__spreadValues({},layoutValidateRule(e))}}}),imageValidateRule=e=>({type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},url:{required:!0,type:"string"},layout:__spreadValues({},layoutValidateRule(e))}}}),videoValidateRule=e=>({type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},url:{required:!0,type:"string"},layout:__spreadValues({},layoutValidateRule(e))}}});function startValidateRule(e){return{name:"VideoMixerOptions",type:"object",required:!0,allowEmpty:!1,properties:{view:__spreadValues({},viewValidateRule(e)),canvasInfo:__spreadValues({},canvasValidateRule(e,!0)),camera:__spreadValues({},cameraValidateRule(e)),screen:__spreadValues({},screenValidateRule(e)),text:__spreadValues({},textValidateRule(e)),image:__spreadValues({},imageValidateRule(e)),video:__spreadValues({},videoValidateRule(e))},validate(i,r,t,a){const{RtcError:o,ErrorCode:s,ErrorCodeDictionary:n}=e.errorModule;if(e.environment.isMobile())throw new o({code:s.ENV_NOT_SUPPORTED,message:"VideoMixer is not supported on mobile devices currently"});const{onScreenShareStop:l}=i;if(l&&!e.utils.isFunction(l))throw new o({code:s.INVALID_PARAMETER,extraCode:n.INVALID_PARAMETER_TYPE,fnName:t,messageParams:{key:"onScreenShareStop",value:typeof l,rule:{type:"Function"}}})}}}function updateValidateRule(e){return{name:"VideoMixerOptions",type:"object",required:!1,allowEmpty:!1,properties:{view:__spreadValues({},viewValidateRule(e)),canvasInfo:__spreadValues({},canvasValidateRule(e)),camera:__spreadValues({},cameraValidateRule(e)),screen:__spreadValues({},screenValidateRule(e)),text:__spreadValues({},textValidateRule(e)),image:__spreadValues({},imageValidateRule(e)),video:__spreadValues({},videoValidateRule(e))}}}function stopValidateRule(e){return{name:"StopVideoMixerOptions",required:!1}}var videoMixSeq=0,_VideoMixer=class e{constructor(e){this.core=e,__publicField(this,"seq"),__publicField(this,"log"),__publicField(this,"localMixVideoTrack",null),__publicField(this,"_mixVideoConfig"),__publicField(this,"onScreenShareStop"),videoMixSeq+=1,this.seq=videoMixSeq,this.log=e.log.createChild({id:`${this.getAlias()}${videoMixSeq}`}),this.log.info("created")}getName(){return e.Name}getAlias(){return"vmix"}getValidateRule(e){switch(e){case"start":return startValidateRule(this.core);case"update":return updateValidateRule(this.core);case"stop":return stopValidateRule(this.core)}}getGroup(){return"vmix"}async start(e){this.localMixVideoTrack||(this.localMixVideoTrack=new this.core.LocalMixVideoTrack(this.core.room.videoManager)),this._mixVideoConfig={canvasInfo:{width:1920,height:1080}},e=this.core.utils.deepCloneBasic(e);const{view:i,onScreenShareStop:r}=e,t=await this.parseMixOptions(e);return r&&(this.onScreenShareStop=r,this._mixVideoConfig.onScreenShareStop=r),this._updatePreview({view:i,track:this.localMixVideoTrack}),this.core.utils.isUndefined(i)||(this._mixVideoConfig.view=i),await this.localMixVideoTrack.startMix(),{track:this.localMixVideoTrack._outputTrack,result:t}}async update(e){const{RtcError:i,ErrorCode:r}=this.core.errorModule;if(!this.localMixVideoTrack)throw new i({code:r.INVALID_OPERATION,message:"mixTrack doesn't initialize!"});e=this.core.utils.deepCloneBasic(e);const{view:t}=e,a=await this.parseMixOptions(e);return await this._updatePreview({view:t,track:this.localMixVideoTrack,prevConfig:this._mixVideoConfig}),this.core.utils.isUndefined(t)||(this._mixVideoConfig.view=t),{track:this.localMixVideoTrack._outputTrack,result:a}}async parseMixOptions(e){let i={successOptions:{},failedDetails:[]};const{RtcError:r,ErrorCode:t,ErrorCodeDictionary:a}=this.core.errorModule;if(!this.localMixVideoTrack||!this._mixVideoConfig)return i;const o=[],s=__spreadValues({},e),{canvasInfo:n,camera:l,screen:d,text:c,image:u,video:p}=e;let h=0,m=0;if(n){const{canvasColor:e,width:i,height:r,frameRate:t}=n;e&&this.localMixVideoTrack.setMixBackground(e),t&&this.localMixVideoTrack.setFps(t),this.localMixVideoTrack.resizeMixCanvas(i,r),this._mixVideoConfig.canvasInfo=n}if(l){h++;const{finalOptions:e,errors:i}=await this.parseCameraOptions(this.localMixVideoTrack,l,this._mixVideoConfig.camera);this._mixVideoConfig.camera=e,s.camera=e,i.length>0&&(o.push(...i),i.length===l.length&&m++)}if(d){h++;const{finalOptions:e,errors:i}=await this.parseScreenOptions(this.localMixVideoTrack,d,this._mixVideoConfig.screen);this._mixVideoConfig.screen=e,s.screen=e,i.length>0&&(o.push(...i),i.length===d.length&&m++)}if(c){h++;const{finalOptions:e,errors:i}=await this.parseTextOptions(this.localMixVideoTrack,c,this._mixVideoConfig.text);this._mixVideoConfig.text=e,s.text=e,i.length>0&&(o.push(...i),i.length===c.length&&m++)}if(u){h++;const{finalOptions:e,errors:i}=await this.parseImageOptions(this.localMixVideoTrack,u,this._mixVideoConfig.image);this._mixVideoConfig.image=e,s.image=e,i.length>0&&(o.push(...i),i.length===u.length&&m++)}if(p){h++;const{finalOptions:e,errors:i}=await this.parseVideoOptions(this.localMixVideoTrack,p,this._mixVideoConfig.video);this._mixVideoConfig.video=e,s.video=e,i.length>0&&(o.push(...i),i.length===p.length&&m++)}if(m>0&&m===h)throw new r({code:t.INVALID_PARAMETER,message:"all sources mix failed",data:{failedDetails:o}});return i={successOptions:s,failedDetails:o},i}async parseCameraOptions(e,i,r=[]){var t,a;const o=new Set(i.map((e=>e.id))),s=r.filter((e=>!o.has(e.id))).map((e=>e.id));for(const i of s)e.removeCameraSource(i);const n=new Map(r.map((e=>[e.id,e]))),l=[],d=[];for(const r of i){const{id:i,layout:o,profile:s}=r;try{if(e.inputLocalVideoTracks.has(i)){const n=null==(t=e.inputLocalVideoTracks.get(i))?void 0:t.mediaTrack;await this.updateCameraProfile(r);const l=null==(a=e.inputLocalVideoTracks.get(i))?void 0:a.mediaTrack;let d;s&&(d=this.core.utils.isString(s)?this.core.constants.videoProfileMap[s]:s),l!==n?e.updateCameraSource(i,o,l,d):e.updateCameraSource(i,o,null,d)}else{const t=await this.captureCamera(r);try{e.addCameraSource(i,t,o)}catch(e){throw t.close(),e}}l.push(r)}catch(e){d.push({id:i,error:e}),n.has(i)&&l.push(n.get(i))}}return{finalOptions:l,errors:d}}async parseScreenOptions(e,i,r=[]){const t=new Set(i.map((e=>e.id))),a=r.filter((e=>!t.has(e.id))).map((e=>e.id));for(const i of a)e.removeScreenSource(i);const o=new Map(r.map((e=>[e.id,e]))),s=[],n=[];for(const r of i){const{id:i,layout:t}=r;try{if(e.inputLocalScreenTracks.has(i))e.updateScreenSource(i,t);else{const a=await this.captureScreen(r);try{e.addScreenSource(i,a,t)}catch(e){throw a.close(),e}}s.push(r)}catch(e){n.push({id:i,error:e}),o.has(i)&&s.push(o.get(i))}}return{finalOptions:s,errors:n}}async parseTextOptions(e,i,r=[]){const t=new Set(i.map((e=>e.id))),a=r.filter((e=>!t.has(e.id))).map((e=>e.id));for(const i of a)e.removeTextSource(i);const o=new Map(r.map((e=>[e.id,e]))),s=[],n=[];for(const t of i){const{id:i}=t;try{r.some((e=>e.id===i))?e.updateTextSource(t):e.addTextSource(t),s.push(t)}catch(e){n.push({id:i,error:e}),o.has(i)&&s.push(o.get(i))}}return{finalOptions:s,errors:n}}async parseImageOptions(e,i,r=[]){const t=new Set(i.map((e=>e.id))),a=r.filter((e=>!t.has(e.id))).map((e=>e.id));for(const i of a)e.removeImageSource(i);const o=new Map(r.map((e=>[e.id,e]))),s=[],n=[];for(const t of i){const{id:i,url:a,layout:l}=t;try{const o=r.find((e=>e.id===i));if(o){let r;o.url!==a&&(r=await this.core.utils.loadImage(a)),e.updateImageSource(i,l,r)}else{const r=await this.core.utils.loadImage(a);e.addImageSource(i,r,l)}s.push(t)}catch(e){n.push({id:i,error:e}),o.has(i)&&s.push(o.get(i))}}return{finalOptions:s,errors:n}}async parseVideoOptions(e,i,r=[]){const t=new Set(i.map((e=>e.id))),a=r.filter((e=>!t.has(e.id))).map((e=>e.id));for(const i of a)e.removeVideoSource(i);const o=new Map(r.map((e=>[e.id,e]))),s=[],n=[];for(const t of i){const{id:i,url:a,layout:l}=t;try{const o=r.find((e=>e.id===i));let n;if(o)o.url!==a&&(n=await this.core.utils.loadVideo(a)),e.updateVideoSource(i,l,n);else{const r=await this.core.utils.loadVideo(a);e.addVideoSource(i,r,l)}s.push(t)}catch(e){n.push({id:i,error:e}),o.has(i)&&s.push(o.get(i))}}return{finalOptions:s,errors:n}}async captureCamera(e){const{id:i,cameraId:r,videoTrack:t,profile:a}=e,o=new this.core.LocalVideoTrack;o.log.id+=`-${i}`;const s={};return r?s.deviceId=r:this.core.utils.isUndefined(t)||(s.customSource=t),this.core.utils.isUndefined(a)||(this.core.utils.isString(a)?this.core.constants.videoProfileMap[a]&&o.setProfile(this.core.constants.videoProfileMap[a]):o.setProfile(a)),await o.capture(s),o}async updateCameraProfile(e){var i;const{id:r,cameraId:t,videoTrack:a,profile:o}=e,s=null==(i=this.localMixVideoTrack)?void 0:i.inputLocalVideoTracks.get(r);s&&(t?await s.switchDevice(t):this.core.utils.isUndefined(a)||await s.setInputMediaStreamTrack(a),this.core.utils.isUndefined(o)||(this.core.utils.isString(o)?this.core.constants.videoProfileMap[o]&&s.setProfile(this.core.constants.videoProfileMap[o]):s.setProfile(o),t&&s.isNeedToSwitchDevice(t)||await s.applyProfile()))}async captureScreen(e){const{id:i,profile:r,captureElement:t,preferDisplaySurface:a}=e,o=new this.core.LocalScreenTrack;o.log.id+=`-${i}`;const s={};return this.core.utils.isUndefined(r)||(this.core.utils.isString(r)?this.core.constants.screenProfileMap[r]&&o.setProfile(this.core.constants.screenProfileMap[r]):o.setProfile(r)),t&&(s.captureElement=t),a&&(s.preferDisplaySurface=a),await o.capture(s),o.mediaTrack.addEventListener(this.core.constants.NAME.ENDED,(()=>{var e,r,t;null==(e=this.localMixVideoTrack)||e.removeScreenSource(i),(null==(r=this._mixVideoConfig)?void 0:r.screen)&&(this._mixVideoConfig.screen=this._mixVideoConfig.screen.filter((e=>e.id!==i))),null==(t=this.onScreenShareStop)||t.call(this,i)})),o}async _updatePreview({view:e,track:i,prevConfig:r}){if(this.core.utils.isUndefined(e)&&r&&r.view){const e=this.core.utils.getViewListFromView(r.view);e.length>0&&await i.play(e)}if(!this.core.utils.isUndefined(e)){const r=this.core.utils.getViewListFromView(e);r.length>0?await i.play(r):i.stop()}}stop(){var e;null==(e=this.localMixVideoTrack)||e.close(),this.localMixVideoTrack=null,delete this.onScreenShareStop,delete this._mixVideoConfig}};__publicField(_VideoMixer,"Name","VideoMixer");var VideoMixer=_VideoMixer,index_default=VideoMixer;export{index_default as default};export{VideoMixer};