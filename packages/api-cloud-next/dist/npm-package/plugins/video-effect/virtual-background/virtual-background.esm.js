var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(A,I,g)=>I in A?__defProp(A,I,{enumerable:!0,configurable:!0,writable:!0,value:g}):A[I]=g,__spreadValues=(A,I)=>{for(var g in I||(I={}))__hasOwnProp.call(I,g)&&__defNormalProp(A,g,I[g]);if(__getOwnPropSymbols)for(var g of __getOwnPropSymbols(I))__propIsEnum.call(I,g)&&__defNormalProp(A,g,I[g]);return A},__spreadProps=(A,I)=>__defProps(A,__getOwnPropDescs(I)),__export=(A,I)=>{for(var g in I)__defProp(A,g,{get:I[g],enumerable:!0})},__publicField=(A,I,g)=>__defNormalProp(A,"symbol"!=typeof I?I+"":I,g);async function authEffect({sdkAppId:A,userId:I,userSig:g,core:C}){var B;const Q=Math.round((new Date).getTime()/1e3);try{const E=await C.schedule.getAbilityConfig(A,C.schedule.ScheduleRequestType.TRTC_AUTO_CONF,{sdkAppId:A,userId:I,userSig:g,timestamp:Q});C.log.info(`virtual background ability response: ${JSON.stringify(E)}`);const{data:t}=E;return(null==(B=null==t?void 0:t.trtcAutoConf)?void 0:B.web_ar)?{auth:!0,timestamp:Q}:{auth:!1}}catch(A){return C.log.error("virtual background fetch error",A),{auth:!1}}}var UserRule={sdkAppId:{required:!0,type:"number"},userId:{required:!0,type:"string"},userSig:{required:!0,type:"string"}};function startValidateRule(A){return{name:"VirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:__spreadProps(__spreadValues({},UserRule),{type:{required:!1,type:"string",values:["image","blur"]},src:{required:!1,type:"string"},blurLevel:{required:!1,type:"number",min:1,max:10},onAbort:{required:!1}}),validate(I,g,C,B){var Q;const{RtcError:E,ErrorCode:t,ErrorCodeDictionary:e}=A.errorModule;if(!I)return;const{type:r,src:i,onAbort:o}=I;if("image"===r&&!i)throw new E({code:t.INVALID_PARAMETER,extraCode:e.INVALID_PARAMETER_REQUIRED,fnName:C,messageParams:{key:"src"}});if(o&&!A.utils.isFunction(o))throw new E({code:t.INVALID_PARAMETER,extraCode:e.INVALID_PARAMETER_TYPE,fnName:C,messageParams:{key:"onAbort",value:typeof o,rule:{type:"Function"}}});if(!(null==(Q=A.room.videoManager.cameraTrack)?void 0:Q.mediaTrack))throw new E({code:t.INVALID_OPERATION,extraCode:e.INVALID_OPERATION_NEED_VIDEO,fnName:C})}}}function updateValidateRule(A){return{name:"UpdateVirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:{type:{required:!0,type:"string",values:["image","blur"]},src:{required:!1,type:"string"},blurLevel:{required:!1,type:"number",min:1,max:10}},validate(I,g,C,B){if(!I)return;const{RtcError:Q,ErrorCode:E,ErrorCodeDictionary:t}=A.errorModule,{type:e,src:r}=I;if("image"===e&&!r)throw new Q({code:E.INVALID_PARAMETER,extraCode:t.INVALID_PARAMETER_REQUIRED,fnName:C,messageParams:{key:"src"}})}}}function stopValidateRule(A){return{name:"StopVirtualBackgroundOptions",required:!1}}var Module=(()=>{var A="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(I={}){var g,C,B=I;B.ready=new Promise(((A,I)=>{g=A,C=I}));var Q,E=Object.assign({},B),t="";"undefined"!=typeof document&&document.currentScript&&(t=document.currentScript.src),A&&(t=A),t=0!==t.indexOf("blob:")?t.substr(0,t.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var e,r,i=B.print||console.log.bind(console),o=B.printErr||console.error.bind(console);function a(A){if(U(A))return function(A){for(var I=atob(A),g=new Uint8Array(I.length),C=0;C<I.length;++C)g[C]=I.charCodeAt(C);return g}(A.slice(f.length))}Object.assign(B,E),E=null,B.arguments&&B.arguments,B.thisProgram&&B.thisProgram,B.quit&&B.quit,B.wasmBinary&&(e=B.wasmBinary),"object"!=typeof WebAssembly&&F("no native wasm support detected");var s,n,c,h,D,y,N,l,u=!1;var w=[],G=[],M=[];var d=0,R=null,S=null;function F(A){B.onAbort&&B.onAbort(A),o(A="Aborted("+A+")"),u=!0,A+=". Build with -sASSERTIONS for more info.";var I=new WebAssembly.RuntimeError(A);throw C(I),I}var p,Y,f="data:application/octet-stream;base64,",U=A=>A.startsWith(f);function k(A){return Promise.resolve().then((()=>function(A){if(A==p&&e)return new Uint8Array(e);var I=a(A);if(I)return I;if(Q)return Q(A);throw"both async and sync fetching of the wasm failed"}(A)))}function L(A,I,g,C){return function(A,I,g){return k(A).then((A=>WebAssembly.instantiate(A,I))).then((A=>A)).then(g,(A=>{o(`failed to asynchronously prepare wasm: ${A}`),F(A)}))}(I,g,C)}U(p="data:application/octet-stream;base64,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")||(Y=p,p=B.locateFile?B.locateFile(Y,t):t+Y);var b=A=>{for(;A.length>0;)A.shift()(B)};B.noExitRuntime;function H(A){this.excPtr=A,this.ptr=A-24,this.set_type=function(A){y[this.ptr+4>>2]=A},this.get_type=function(){return y[this.ptr+4>>2]},this.set_destructor=function(A){y[this.ptr+8>>2]=A},this.get_destructor=function(){return y[this.ptr+8>>2]},this.set_caught=function(A){A=A?1:0,s[this.ptr+12|0]=A},this.get_caught=function(){return 0!=s[this.ptr+12|0]},this.set_rethrown=function(A){A=A?1:0,s[this.ptr+13|0]=A},this.get_rethrown=function(){return 0!=s[this.ptr+13|0]},this.init=function(A,I){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(I)},this.set_adjusted_ptr=function(A){y[this.ptr+16>>2]=A},this.get_adjusted_ptr=function(){return y[this.ptr+16>>2]},this.get_exception_ptr=function(){if(sI(this.get_type()))return y[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}var m,K,v,J=A=>{for(var I="",g=A;n[g];)I+=m[n[g++]];return I},O={},T={},Z={},P=A=>{throw new K(A)},V=A=>{throw new v(A)},W=(A,I,g)=>{function C(I){var C=g(I);C.length!==A.length&&V("Mismatched type converter count");for(var B=0;B<A.length;++B)j(A[B],C[B])}A.forEach((function(A){Z[A]=I}));var B=new Array(I.length),Q=[],E=0;I.forEach(((A,I)=>{T.hasOwnProperty(A)?B[I]=T[A]:(Q.push(A),O.hasOwnProperty(A)||(O[A]=[]),O[A].push((()=>{B[I]=T[A],++E===Q.length&&C(B)})))})),0===Q.length&&C(B)};function j(A,I,g={}){if(!("argPackAdvance"in I))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,I,g={}){var C=I.name;if(A||P(`type "${C}" must have a positive integer typeid pointer`),T.hasOwnProperty(A)){if(g.ignoreDuplicateRegistrations)return;P(`Cannot register type '${C}' twice`)}if(T[A]=I,delete Z[A],O.hasOwnProperty(A)){var B=O[A];delete O[A],B.forEach((A=>A()))}}(A,I,g)}var x,_=A=>{P(A.$$.ptrType.registeredClass.name+" instance already deleted")},X=!1,z=A=>{},q=A=>{A.count.value-=1,0===A.count.value&&(A=>{A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)})(A)},$=(A,I,g)=>{if(I===g)return A;if(void 0===g.baseClass)return null;var C=$(A,I,g.baseClass);return null===C?null:g.downcast(C)},AA={},IA=()=>Object.keys(EA).length,gA=()=>{var A=[];for(var I in EA)EA.hasOwnProperty(I)&&A.push(EA[I]);return A},CA=[],BA=()=>{for(;CA.length;){var A=CA.pop();A.$$.deleteScheduled=!1,A.delete()}},QA=A=>{x=A,CA.length&&x&&x(BA)},EA={},tA=(A,I)=>(I=((A,I)=>{for(void 0===I&&P("ptr should not be undefined");A.baseClass;)I=A.upcast(I),A=A.baseClass;return I})(A,I),EA[I]),eA=(A,I)=>(I.ptrType&&I.ptr||V("makeClassHandle requires ptr and ptrType"),!!I.smartPtrType!==!!I.smartPtr&&V("Both smartPtrType and smartPtr must be specified"),I.count={value:1},iA(Object.create(A,{$$:{value:I}})));function rA(A){var I=this.getPointee(A);if(!I)return this.destructor(A),null;var g=tA(this.registeredClass,I);if(void 0!==g){if(0===g.$$.count.value)return g.$$.ptr=I,g.$$.smartPtr=A,g.clone();var C=g.clone();return this.destructor(A),C}function B(){return this.isSmartPointer?eA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:I,smartPtrType:this,smartPtr:A}):eA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var Q,E=this.registeredClass.getActualType(I),t=AA[E];if(!t)return B.call(this);Q=this.isConst?t.constPointerType:t.pointerType;var e=$(I,this.registeredClass,Q.registeredClass);return null===e?B.call(this):this.isSmartPointer?eA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e,smartPtrType:this,smartPtr:A}):eA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e})}var iA=A=>"undefined"==typeof FinalizationRegistry?(iA=A=>A,A):(X=new FinalizationRegistry((A=>{q(A.$$)})),z=A=>X.unregister(A),(iA=A=>{var I=A.$$;if(!!I.smartPtr){var g={$$:I};X.register(A,g,A)}return A})(A));function oA(){}var aA=(A,I)=>Object.defineProperty(I,"name",{value:A}),sA=(A,I,g)=>{if(void 0===A[I].overloadTable){var C=A[I];A[I]=function(){return A[I].overloadTable.hasOwnProperty(arguments.length)||P(`Function '${g}' called with an invalid number of arguments (${arguments.length}) - expects one of (${A[I].overloadTable})!`),A[I].overloadTable[arguments.length].apply(this,arguments)},A[I].overloadTable=[],A[I].overloadTable[C.argCount]=C}};function nA(A,I,g,C,B,Q,E,t){this.name=A,this.constructor=I,this.instancePrototype=g,this.rawDestructor=C,this.baseClass=B,this.getActualType=Q,this.upcast=E,this.downcast=t,this.pureVirtualFunctions=[]}var cA=(A,I,g)=>{for(;I!==g;)I.upcast||P(`Expected null or instance of ${g.name}, got an instance of ${I.name}`),A=I.upcast(A),I=I.baseClass;return A};function hA(A,I){if(null===I)return this.isReference&&P(`null is not a valid ${this.name}`),0;I.$$||P(`Cannot pass "${TA(I)}" as a ${this.name}`),I.$$.ptr||P(`Cannot pass deleted object as a pointer of type ${this.name}`);var g=I.$$.ptrType.registeredClass;return cA(I.$$.ptr,g,this.registeredClass)}function DA(A,I){var g;if(null===I)return this.isReference&&P(`null is not a valid ${this.name}`),this.isSmartPointer?(g=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,g),g):0;I.$$||P(`Cannot pass "${TA(I)}" as a ${this.name}`),I.$$.ptr||P(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&I.$$.ptrType.isConst&&P(`Cannot convert argument of type ${I.$$.smartPtrType?I.$$.smartPtrType.name:I.$$.ptrType.name} to parameter type ${this.name}`);var C=I.$$.ptrType.registeredClass;if(g=cA(I.$$.ptr,C,this.registeredClass),this.isSmartPointer)switch(void 0===I.$$.smartPtr&&P("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:I.$$.smartPtrType===this?g=I.$$.smartPtr:P(`Cannot convert argument of type ${I.$$.smartPtrType?I.$$.smartPtrType.name:I.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:g=I.$$.smartPtr;break;case 2:if(I.$$.smartPtrType===this)g=I.$$.smartPtr;else{var B=I.clone();g=this.rawShare(g,mA.toHandle((()=>B.delete()))),null!==A&&A.push(this.rawDestructor,g)}break;default:P("Unsupporting sharing policy")}return g}function yA(A,I){if(null===I)return this.isReference&&P(`null is not a valid ${this.name}`),0;I.$$||P(`Cannot pass "${TA(I)}" as a ${this.name}`),I.$$.ptr||P(`Cannot pass deleted object as a pointer of type ${this.name}`),I.$$.ptrType.isConst&&P(`Cannot convert argument of type ${I.$$.ptrType.name} to parameter type ${this.name}`);var g=I.$$.ptrType.registeredClass;return cA(I.$$.ptr,g,this.registeredClass)}function NA(A){return this.fromWireType(y[A>>2])}function lA(A,I,g,C,B,Q,E,t,e,r,i){this.name=A,this.registeredClass=I,this.isReference=g,this.isConst=C,this.isSmartPointer=B,this.pointeeType=Q,this.sharingPolicy=E,this.rawGetPointee=t,this.rawConstructor=e,this.rawShare=r,this.rawDestructor=i,B||void 0!==I.baseClass?this.toWireType=DA:C?(this.toWireType=hA,this.destructorFunction=null):(this.toWireType=yA,this.destructorFunction=null)}var uA,wA,GA=[],MA=A=>{var I=GA[A];return I||(A>=GA.length&&(GA.length=A+1),GA[A]=I=uA.get(A)),I},dA=(A,I,g)=>A.includes("j")?((A,I,g)=>{var C=B["dynCall_"+A];return g&&g.length?C.apply(null,[I].concat(g)):C.call(null,I)})(A,I,g):MA(I).apply(null,g),RA=(A,I)=>{var g,C,B,Q=(A=J(A)).includes("j")?(g=A,C=I,B=[],function(){return B.length=0,Object.assign(B,arguments),dA(g,C,B)}):MA(I);return"function"!=typeof Q&&P(`unknown function pointer with signature ${A}: ${I}`),Q},SA=A=>{var I=oI(A),g=J(I);return aI(I),g},FA=(A,I)=>{var g=[],C={};throw I.forEach((function A(I){C[I]||T[I]||(Z[I]?Z[I].forEach(A):(g.push(I),C[I]=!0))})),new wA(`${A}: `+g.map(SA).join([", "]))},pA=(A,I)=>{for(var g=[],C=0;C<A;C++)g.push(y[I+4*C>>2]);return g},YA=A=>{for(;A.length;){var I=A.pop();A.pop()(I)}};function fA(A,I,g,C,B,Q){var E=I.length;E<2&&P("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var t=null!==I[1]&&null!==g,e=!1,r=1;r<I.length;++r)if(null!==I[r]&&void 0===I[r].destructorFunction){e=!0;break}var i="void"!==I[0].name,o=E-2,a=new Array(o),s=[],n=[];return aA(A,(function(){var g;arguments.length!==o&&P(`function ${A} called with ${arguments.length} arguments, expected ${o}`),n.length=0,s.length=t?2:1,s[0]=B,t&&(g=I[1].toWireType(n,this),s[1]=g);for(var Q=0;Q<o;++Q)a[Q]=I[Q+2].toWireType(n,arguments[Q]),s.push(a[Q]);return function(A){if(e)YA(n);else for(var C=t?1:2;C<I.length;C++){var B=1===C?g:a[C-2];null!==I[C].destructorFunction&&I[C].destructorFunction(B)}if(i)return I[0].fromWireType(A)}(C.apply(null,s))}))}var UA=(A,I,g)=>(A instanceof Object||P(`${g} with invalid "this": ${A}`),A instanceof I.registeredClass.constructor||P(`${g} incompatible with "this" of type ${A.constructor.name}`),A.$$.ptr||P(`cannot call emscripten binding method ${g} on deleted object`),cA(A.$$.ptr,A.$$.ptrType.registeredClass,I.registeredClass));function kA(){this.allocated=[void 0],this.freelist=[]}var LA=new kA,bA=A=>{A>=LA.reserved&&0==--LA.get(A).refcount&&LA.free(A)},HA=()=>{for(var A=0,I=LA.reserved;I<LA.allocated.length;++I)void 0!==LA.allocated[I]&&++A;return A},mA={toValue:A=>(A||P("Cannot use deleted val. handle = "+A),LA.get(A).value),toHandle:A=>{switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return LA.allocate({refcount:1,value:A})}}};function KA(A){return this.fromWireType(D[A>>2])}var vA,JA,OA,TA=A=>{if(null===A)return"null";var I=typeof A;return"object"===I||"array"===I||"function"===I?A.toString():""+A},ZA=(A,I)=>{switch(I){case 4:return function(A){return this.fromWireType(N[A>>2])};case 8:return function(A){return this.fromWireType(l[A>>3])};default:throw new TypeError(`invalid float width (${I}): ${A}`)}},PA=(A,I,g)=>{switch(I){case 1:return g?A=>s[A|0]:A=>n[A|0];case 2:return g?A=>c[A>>1]:A=>h[A>>1];case 4:return g?A=>D[A>>2]:A=>y[A>>2];default:throw new TypeError(`invalid integer width (${I}): ${A}`)}},VA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,WA=(A,I,g)=>{for(var C=I+g,B=I;A[B]&&!(B>=C);)++B;if(B-I>16&&A.buffer&&VA)return VA.decode(A.subarray(I,B));for(var Q="";I<B;){var E=A[I++];if(128&E){var t=63&A[I++];if(192!=(224&E)){var e=63&A[I++];if((E=224==(240&E)?(15&E)<<12|t<<6|e:(7&E)<<18|t<<12|e<<6|63&A[I++])<65536)Q+=String.fromCharCode(E);else{var r=E-65536;Q+=String.fromCharCode(55296|r>>10,56320|1023&r)}}else Q+=String.fromCharCode((31&E)<<6|t)}else Q+=String.fromCharCode(E)}return Q},jA=(A,I)=>A?WA(n,A,I):"",xA="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,_A=(A,I)=>{for(var g=A,C=g>>1,B=C+I/2;!(C>=B)&&h[C];)++C;if((g=C<<1)-A>32&&xA)return xA.decode(n.subarray(A,g));for(var Q="",E=0;!(E>=I/2);++E){var t=c[A+2*E>>1];if(0==t)break;Q+=String.fromCharCode(t)}return Q},XA=(A,I,g)=>{if(void 0===g&&(g=2147483647),g<2)return 0;for(var C=I,B=(g-=2)<2*A.length?g/2:A.length,Q=0;Q<B;++Q){var E=A.charCodeAt(Q);c[I>>1]=E,I+=2}return c[I>>1]=0,I-C},zA=A=>2*A.length,qA=(A,I)=>{for(var g=0,C="";!(g>=I/4);){var B=D[A+4*g>>2];if(0==B)break;if(++g,B>=65536){var Q=B-65536;C+=String.fromCharCode(55296|Q>>10,56320|1023&Q)}else C+=String.fromCharCode(B)}return C},$A=(A,I,g)=>{if(void 0===g&&(g=2147483647),g<4)return 0;for(var C=I,B=C+g-4,Q=0;Q<A.length;++Q){var E=A.charCodeAt(Q);if(E>=55296&&E<=57343)E=65536+((1023&E)<<10)|1023&A.charCodeAt(++Q);if(D[I>>2]=E,(I+=4)+4>B)break}return D[I>>2]=0,I-C},AI=A=>{for(var I=0,g=0;g<A.length;++g){var C=A.charCodeAt(g);C>=55296&&C<=57343&&++g,I+=4}return I},II=(A,I)=>{var g=T[A];return void 0===g&&P(I+" has unknown type "+SA(A)),g},gI=(A,I,g)=>{var C=[],B=A.toWireType(C,g);return C.length&&(y[I>>2]=mA.toHandle(C)),B},CI={},BI=[],QI=Reflect.construct,EI=[null,[],[]];(()=>{for(var A=new Array(256),I=0;I<256;++I)A[I]=String.fromCharCode(I);m=A})(),K=B.BindingError=class extends Error{constructor(A){super(A),this.name="BindingError"}},v=B.InternalError=class extends Error{constructor(A){super(A),this.name="InternalError"}},Object.assign(oA.prototype,{isAliasOf(A){if(!(this instanceof oA))return!1;if(!(A instanceof oA))return!1;var I=this.$$.ptrType.registeredClass,g=this.$$.ptr;A.$$=A.$$;for(var C=A.$$.ptrType.registeredClass,B=A.$$.ptr;I.baseClass;)g=I.upcast(g),I=I.baseClass;for(;C.baseClass;)B=C.upcast(B),C=C.baseClass;return I===C&&g===B},clone(){if(this.$$.ptr||_(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,I=iA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return I.$$.count.value+=1,I.$$.deleteScheduled=!1,I},delete(){this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&P("Object already scheduled for deletion"),z(this),q(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&P("Object already scheduled for deletion"),CA.push(this),1===CA.length&&x&&x(BA),this.$$.deleteScheduled=!0,this}}),B.getInheritedInstanceCount=IA,B.getLiveInheritedInstances=gA,B.flushPendingDeletes=BA,B.setDelayFunction=QA,Object.assign(lA.prototype,{getPointee(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor(A){this.rawDestructor&&this.rawDestructor(A)},argPackAdvance:8,readValueFromPointer:NA,deleteObject(A){null!==A&&A.delete()},fromWireType:rA}),wA=B.UnboundTypeError=(vA=Error,(OA=aA(JA="UnboundTypeError",(function(A){this.name=JA,this.message=A;var I=new Error(A).stack;void 0!==I&&(this.stack=this.toString()+"\n"+I.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(vA.prototype),OA.prototype.constructor=OA,OA.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},OA),Object.assign(kA.prototype,{get(A){return this.allocated[A]},has(A){return void 0!==this.allocated[A]},allocate(A){var I=this.freelist.pop()||this.allocated.length;return this.allocated[I]=A,I},free(A){this.allocated[A]=void 0,this.freelist.push(A)}}),LA.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),LA.reserved=LA.allocated.length,B.count_emval_handles=HA;var tI,eI={w:(A,I,g)=>{throw new H(A).init(I,g),A},q:(A,I,g,C,B)=>{},u:(A,I,g,C)=>{j(A,{name:I=J(I),fromWireType:function(A){return!!A},toWireType:function(A,I){return I?g:C},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(n[A])},destructorFunction:null})},y:(A,I,g,C,Q,E,t,e,r,i,o,a,s)=>{o=J(o),E=RA(Q,E),e&&(e=RA(t,e)),i&&(i=RA(r,i)),s=RA(a,s);var n=(A=>{if(void 0===A)return"_unknown";var I=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return I>=48&&I<=57?`_${A}`:A})(o);((A,I,g)=>{B.hasOwnProperty(A)?((void 0===g||void 0!==B[A].overloadTable&&void 0!==B[A].overloadTable[g])&&P(`Cannot register public name '${A}' twice`),sA(B,A,A),B.hasOwnProperty(g)&&P(`Cannot register multiple overloads of a function with the same number of arguments (${g})!`),B[A].overloadTable[g]=I):(B[A]=I,void 0!==g&&(B[A].numArguments=g))})(n,(function(){FA(`Cannot construct ${o} due to unbound types`,[C])})),W([A,I,g],C?[C]:[],(function(I){var g,Q;I=I[0],Q=C?(g=I.registeredClass).instancePrototype:oA.prototype;var t=aA(o,(function(){if(Object.getPrototypeOf(this)!==r)throw new K("Use 'new' to construct "+o);if(void 0===a.constructor_body)throw new K(o+" has no accessible constructor");var A=a.constructor_body[arguments.length];if(void 0===A)throw new K(`Tried to invoke ctor of ${o} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(a.constructor_body).toString()}) parameters instead!`);return A.apply(this,arguments)})),r=Object.create(Q,{constructor:{value:t}});t.prototype=r;var a=new nA(o,t,r,s,g,E,e,i);a.baseClass&&(void 0===a.baseClass.__derivedClasses&&(a.baseClass.__derivedClasses=[]),a.baseClass.__derivedClasses.push(a));var c=new lA(o,a,!0,!1,!1),h=new lA(o+"*",a,!1,!1,!1),D=new lA(o+" const*",a,!1,!0,!1);return AA[A]={pointerType:h,constPointerType:D},((A,I,g)=>{B.hasOwnProperty(A)||V("Replacing nonexistant public symbol"),void 0!==B[A].overloadTable&&void 0!==g?B[A].overloadTable[g]=I:(B[A]=I,B[A].argCount=g)})(n,t),[c,h,D]}))},x:(A,I,g,C,B,Q)=>{var E=pA(I,g);B=RA(C,B),W([],[A],(function(A){var g=`constructor ${(A=A[0]).name}`;if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[I-1])throw new K(`Cannot register multiple constructors with identical number of parameters (${I-1}) for class '${A.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return A.registeredClass.constructor_body[I-1]=()=>{FA(`Cannot construct ${A.name} due to unbound types`,E)},W([],E,(C=>(C.splice(1,0,null),A.registeredClass.constructor_body[I-1]=fA(g,C,null,B,Q),[]))),[]}))},i:(A,I,g,C,B,Q,E,t,e)=>{var r=pA(g,C);I=(A=>{const I=(A=A.trim()).indexOf("(");return-1!==I?A.substr(0,I):A})(I=J(I)),Q=RA(B,Q),W([],[A],(function(A){var C=`${(A=A[0]).name}.${I}`;function B(){FA(`Cannot call ${C} due to unbound types`,r)}I.startsWith("@@")&&(I=Symbol[I.substring(2)]),t&&A.registeredClass.pureVirtualFunctions.push(I);var e=A.registeredClass.instancePrototype,i=e[I];return void 0===i||void 0===i.overloadTable&&i.className!==A.name&&i.argCount===g-2?(B.argCount=g-2,B.className=A.name,e[I]=B):(sA(e,I,C),e[I].overloadTable[g-2]=B),W([],r,(function(B){var t=fA(C,B,A,Q,E);return void 0===e[I].overloadTable?(t.argCount=g-2,e[I]=t):e[I].overloadTable[g-2]=t,[]})),[]}))},k:(A,I,g,C,B,Q,E,t,e,r)=>{I=J(I),B=RA(C,B),W([],[A],(function(A){var C=`${(A=A[0]).name}.${I}`,i={get(){FA(`Cannot access ${C} due to unbound types`,[g,E])},enumerable:!0,configurable:!0};return i.set=e?()=>FA(`Cannot access ${C} due to unbound types`,[g,E]):A=>P(C+" is a read-only property"),Object.defineProperty(A.registeredClass.instancePrototype,I,i),W([],e?[g,E]:[g],(function(g){var E=g[0],i={get(){var I=UA(this,A,C+" getter");return E.fromWireType(B(Q,I))},enumerable:!0};if(e){e=RA(t,e);var o=g[1];i.set=function(I){var g=UA(this,A,C+" setter"),B=[];e(r,g,o.toWireType(B,I)),YA(B)}}return Object.defineProperty(A.registeredClass.instancePrototype,I,i),[]})),[]}))},t:(A,I)=>{j(A,{name:I=J(I),fromWireType:A=>{var I=mA.toValue(A);return bA(A),I},toWireType:(A,I)=>mA.toHandle(I),argPackAdvance:8,readValueFromPointer:KA,destructorFunction:null})},p:(A,I,g)=>{j(A,{name:I=J(I),fromWireType:A=>A,toWireType:(A,I)=>I,argPackAdvance:8,readValueFromPointer:ZA(I,g),destructorFunction:null})},g:(A,I,g,C,B)=>{I=J(I),-1===B&&(B=4294967295);var Q=A=>A;if(0===C){var E=32-8*g;Q=A=>A<<E>>>E}var t=I.includes("unsigned");j(A,{name:I,fromWireType:Q,toWireType:t?function(A,I){return this.name,I>>>0}:function(A,I){return this.name,I},argPackAdvance:8,readValueFromPointer:PA(I,g,0!==C),destructorFunction:null})},a:(A,I,g)=>{var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][I];function B(A){var I=y[A>>2],g=y[A+4>>2];return new C(s.buffer,g,I)}j(A,{name:g=J(g),fromWireType:B,argPackAdvance:8,readValueFromPointer:B},{ignoreDuplicateRegistrations:!0})},o:(A,I)=>{var g="std::string"===(I=J(I));j(A,{name:I,fromWireType(A){var I,C=y[A>>2],B=A+4;if(g)for(var Q=B,E=0;E<=C;++E){var t=B+E;if(E==C||0==n[t]){var e=jA(Q,t-Q);void 0===I?I=e:(I+=String.fromCharCode(0),I+=e),Q=t+1}}else{var r=new Array(C);for(E=0;E<C;++E)r[E]=String.fromCharCode(n[B+E]);I=r.join("")}return aI(A),I},toWireType(A,I){var C;I instanceof ArrayBuffer&&(I=new Uint8Array(I));var B="string"==typeof I;B||I instanceof Uint8Array||I instanceof Uint8ClampedArray||I instanceof Int8Array||P("Cannot pass non-string to std::string"),C=g&&B?(A=>{for(var I=0,g=0;g<A.length;++g){var C=A.charCodeAt(g);C<=127?I++:C<=2047?I+=2:C>=55296&&C<=57343?(I+=4,++g):I+=3}return I})(I):I.length;var Q=iI(4+C+1),E=Q+4;if(y[Q>>2]=C,g&&B)((A,I,g,C)=>{if(!(C>0))return 0;for(var B=g,Q=g+C-1,E=0;E<A.length;++E){var t=A.charCodeAt(E);if(t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&A.charCodeAt(++E)),t<=127){if(g>=Q)break;I[g++]=t}else if(t<=2047){if(g+1>=Q)break;I[g++]=192|t>>6,I[g++]=128|63&t}else if(t<=65535){if(g+2>=Q)break;I[g++]=224|t>>12,I[g++]=128|t>>6&63,I[g++]=128|63&t}else{if(g+3>=Q)break;I[g++]=240|t>>18,I[g++]=128|t>>12&63,I[g++]=128|t>>6&63,I[g++]=128|63&t}}I[g]=0})(I,n,E,C+1);else if(B)for(var t=0;t<C;++t){var e=I.charCodeAt(t);e>255&&(aI(E),P("String has UTF-16 code units that do not fit in 8 bits")),n[E+t]=e}else for(t=0;t<C;++t)n[E+t]=I[t];return null!==A&&A.push(aI,Q),Q},argPackAdvance:8,readValueFromPointer:NA,destructorFunction(A){aI(A)}})},l:(A,I,g)=>{var C,B,Q,E,t;g=J(g),2===I?(C=_A,B=XA,E=zA,Q=()=>h,t=1):4===I&&(C=qA,B=$A,E=AI,Q=()=>y,t=2),j(A,{name:g,fromWireType:A=>{for(var g,B=y[A>>2],E=Q(),e=A+4,r=0;r<=B;++r){var i=A+4+r*I;if(r==B||0==E[i>>t]){var o=C(e,i-e);void 0===g?g=o:(g+=String.fromCharCode(0),g+=o),e=i+I}}return aI(A),g},toWireType:(A,C)=>{"string"!=typeof C&&P(`Cannot pass non-string to C++ string type ${g}`);var Q=E(C),e=iI(4+Q+I);return y[e>>2]=Q>>t,B(C,e+4,Q+I),null!==A&&A.push(aI,e),e},argPackAdvance:8,readValueFromPointer:KA,destructorFunction(A){aI(A)}})},v:(A,I)=>{j(A,{isVoid:!0,name:I=J(I),argPackAdvance:0,fromWireType:()=>{},toWireType:(A,I)=>{}})},j:(A,I,g)=>(A=mA.toValue(A),I=II(I,"emval::as"),gI(I,g,A)),e:(A,I,g,C,B)=>{var Q,E;return(A=BI[A])(I=mA.toValue(I),I[g=void 0===(E=CI[Q=g])?J(Q):E],C,B)},d:bA,f:(A,I,g)=>{var C=((A,I)=>{for(var g=new Array(A),C=0;C<A;++C)g[C]=II(y[I+4*C>>2],"parameter "+C);return g})(A,I),B=C.shift();A--;var Q,E,t=new Array(A),e=`methodCaller<(${C.map((A=>A.name)).join(", ")}) => ${B.name}>`;return Q=aA(e,((I,Q,E,e)=>{for(var r=0,i=0;i<A;++i)t[i]=C[i].readValueFromPointer(e+r),r+=C[i].argPackAdvance;var o=1===g?QI(Q,t):Q.apply(I,t);for(i=0;i<A;++i)C[i].deleteObject&&C[i].deleteObject(t[i]);return gI(B,E,o)})),E=BI.length,BI.push(Q),E},c:A=>{A>4&&(LA.get(A).refcount+=1)},b:A=>{var I=mA.toValue(A);YA(I),bA(A)},h:(A,I)=>{var g=(A=II(A,"_emval_take_value")).readValueFromPointer(I);return mA.toHandle(g)},m:()=>{F("")},s:(A,I,g)=>n.copyWithin(A,I,I+g),r:A=>{n.length;F("OOM")},n:(A,I,g,C)=>{for(var B,Q,E,t=0,e=0;e<g;e++){var r=y[I>>2],a=y[I+4>>2];I+=8;for(var s=0;s<a;s++)B=A,Q=n[r+s],E=void 0,E=EI[B],0===Q||10===Q?((1===B?i:o)(WA(E,0)),E.length=0):E.push(Q);t+=a}return y[C>>2]=t,0}},rI=function(){var A={a:eI};function I(A,I){var g,C;return rI=A.exports,r=rI.z,g=r.buffer,B.HEAP8=s=new Int8Array(g),B.HEAP16=c=new Int16Array(g),B.HEAPU8=n=new Uint8Array(g),B.HEAPU16=h=new Uint16Array(g),B.HEAP32=D=new Int32Array(g),B.HEAPU32=y=new Uint32Array(g),B.HEAPF32=N=new Float32Array(g),B.HEAPF64=l=new Float64Array(g),uA=rI.C,C=rI.A,G.unshift(C),function(){if(d--,B.monitorRunDependencies&&B.monitorRunDependencies(d),0==d&&(null!==R&&(clearInterval(R),R=null),S)){var A=S;S=null,A()}}(),rI}if(d++,B.monitorRunDependencies&&B.monitorRunDependencies(d),B.instantiateWasm)try{return B.instantiateWasm(A,I)}catch(A){o(`Module.instantiateWasm callback failed with error: ${A}`),C(A)}return L(0,p,A,(function(A){I(A.instance)})).catch(C),{}}(),iI=A=>(iI=rI.B)(A),oI=A=>(oI=rI.D)(A),aI=A=>(aI=rI.E)(A),sI=A=>(sI=rI.F)(A);B.dynCall_jiji=(A,I,g,C,Q)=>(B.dynCall_jiji=rI.G)(A,I,g,C,Q),B._vertexShaderSource=10624;function nI(){function A(){tI||(tI=!0,B.calledRun=!0,u||(b(G),g(B),B.onRuntimeInitialized&&B.onRuntimeInitialized(),function(){if(B.postRun)for("function"==typeof B.postRun&&(B.postRun=[B.postRun]);B.postRun.length;)A=B.postRun.shift(),M.unshift(A);var A;b(M)}()))}d>0||(!function(){if(B.preRun)for("function"==typeof B.preRun&&(B.preRun=[B.preRun]);B.preRun.length;)A=B.preRun.shift(),w.unshift(A);var A;b(w)}(),d>0||(B.setStatus?(B.setStatus("Running..."),setTimeout((function(){setTimeout((function(){B.setStatus("")}),1),A()}),1)):A()))}if(S=function A(){tI||nI(),tI||(S=A)},B.preInit)for("function"==typeof B.preInit&&(B.preInit=[B.preInit]);B.preInit.length>0;)B.preInit.pop()();return nI(),I.ready}})(),allin1_default=Module,USER_AGENT="undefined"==typeof navigator?"":navigator.userAgent,isUserAgentMatch=A=>new RegExp(A,"i").test(USER_AGENT),getBrowserVersion=A=>{if(isUserAgentMatch(A)){const I=new RegExp(`${A}\\/([\\d.]+)`),g=USER_AGENT.match(I);if(g&&g[1])return g[1]}return""},getBrowserMajorVersion=A=>{if(isUserAgentMatch(A)){const I=new RegExp(`${A}\\/(\\d+)`),g=USER_AGENT.match(I);if(g&&g[1])return parseFloat(g[1])}return NaN},webkitVersionMap=/AppleWebKit\/([\d.]+)/i.exec(USER_AGENT),appleWebkitVersion=webkitVersionMap?parseFloat(webkitVersionMap[1]):NaN,IS_IPAD=isUserAgentMatch("iPad"),IS_IPAD_PRO="undefined"!=typeof navigator&&navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&isUserAgentMatch("Macintosh"),IS_IPHONE=isUserAgentMatch("iPhone")&&!IS_IPAD,IS_IPOD=isUserAgentMatch("iPod"),IS_IOS=IS_IPHONE||IS_IPAD||IS_IPOD||IS_IPAD_PRO,IS_ANDROID=isUserAgentMatch("Android"),ANDROID_VERSION=function(){if(IS_ANDROID){const A=USER_AGENT.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(A){const I=A[1]&&parseFloat(A[1]),g=A[2]&&parseFloat(A[2]);if(I&&g)return parseFloat(`${A[1]}.${A[2]}`);if(I)return I}}return NaN}(),IS_OLD_ANDROID=IS_ANDROID&&isUserAgentMatch("webkit")&&ANDROID_VERSION<2.3,IS_NATIVE_ANDROID=IS_ANDROID&&ANDROID_VERSION<5&&appleWebkitVersion<537,IS_FIREFOX=isUserAgentMatch("Firefox"),FIREFOX_VERSION=getBrowserVersion("Firefox"),FIREFOX_MAJOR_VERSION=getBrowserMajorVersion("Firefox"),IS_EDGE=isUserAgentMatch("Edge"),EDGE_VERSION=getBrowserVersion("Edge"),IS_EDG=isUserAgentMatch("Edg"),EDG_VERSION=getBrowserVersion("Edg"),EDG_MAJOR_VERSION=getBrowserMajorVersion("Edg"),IS_SOGOUM=isUserAgentMatch("SogouMobileBrowser"),SOGOUM_VERSION=getBrowserVersion("SogouMobileBrowser"),IS_SOGOU=isUserAgentMatch("MetaSr\\s"),SOGOU_VERSION=getBrowserVersion("MetaSr\\s"),IS_TBS=isUserAgentMatch("TBS"),TBS_VERSION=getBrowserVersion("TBS"),IS_XWEB=isUserAgentMatch("XWEB"),XWEB_VERSION=getBrowserVersion("XWEB"),IS_IE8=isUserAgentMatch("MSIE\\s8\\.0"),IS_IE=isUserAgentMatch("MSIE\\/\\d+"),IE_VERSION=function(){if(IS_IE){const A=/MSIE\s(\d+)\.\d/.exec(USER_AGENT);let I=A&&parseFloat(A[1]);return!I&&/Trident\/7.0/i.test(USER_AGENT)&&/rv:11.0/.test(USER_AGENT)&&(I=11),I}return NaN}(),IS_WECHAT=isUserAgentMatch("(micromessenger|webbrowser)"),WECHAT_VERSION=getBrowserVersion("MicroMessenger"),IS_X5MQQB=!IS_TBS&&isUserAgentMatch("MQQBrowser")&&isUserAgentMatch("COVC"),IS_MQQB=!IS_TBS&&isUserAgentMatch("MQQBrowser")&&!isUserAgentMatch("COVC"),MQQB_VERSION=IS_MQQB||IS_X5MQQB?getBrowserVersion("MQQBrowser"):"",IS_WQQB=!IS_TBS&&isUserAgentMatch(" QQBrowser"),WQQB_VERSION=getBrowserVersion(" QQBrowser"),IS_MACQQB=!IS_TBS&&isUserAgentMatch("QQBrowserLite"),MACQQB_VERSION=getBrowserVersion("QQBrowserLite"),IS_IPADQQB=!IS_TBS&&isUserAgentMatch("MQBHD"),IPADQQB_VERSION=getBrowserVersion("MQBHD"),IS_WIN=isUserAgentMatch("Windows"),IS_MAC=!IS_IOS&&isUserAgentMatch("MAC OS X"),IS_LINUX=!IS_ANDROID&&isUserAgentMatch("Linux"),IS_CHROME_OS=isUserAgentMatch("CrOS"),IS_WX=isUserAgentMatch("MicroMessenger"),IS_UCBROWSER=isUserAgentMatch("UCBrowser"),IS_ELECTRON=isUserAgentMatch("Electron"),IS_MIBROWSER=isUserAgentMatch("MiuiBrowser"),MI_VERSION=getBrowserVersion("MiuiBrowser"),IS_HUAWEIBROWSER=isUserAgentMatch("HuaweiBrowser"),IS_HUAWEI=isUserAgentMatch("Huawei")||isUserAgentMatch("HUAWEI"),IS_HONOR=isUserAgentMatch("Honor")||isUserAgentMatch("HONOR"),HUAWEI_VERSION=getBrowserVersion("HuaweiBrowser"),IS_SAMSUNGBROWSER=isUserAgentMatch("SamsungBrowser"),SAMSUNG_VERSION=getBrowserVersion("SamsungBrowser"),IS_OPPOBROWSER=isUserAgentMatch("HeyTapBrowser"),OPPO_VERSION=getBrowserVersion("HeyTapBrowser"),IS_VIVOBROWSER=isUserAgentMatch("VivoBrowser"),VIVO_VERSION=getBrowserVersion("VivoBrowser"),IS_OPENHARMONY=isUserAgentMatch("OpenHarmony"),OPENHARMONY_VERSION=getBrowserVersion("OpenHarmony"),getChromeMajorVersion=()=>getBrowserMajorVersion("Chrome"),IS_CHROMIUM_BASE=isUserAgentMatch("Chrome"),IS_CHROME=!IS_EDGE&&!IS_SOGOU&&!IS_SOGOUM&&!IS_TBS&&!IS_XWEB&&!IS_EDG&&!IS_WQQB&&!IS_MIBROWSER&&!IS_HUAWEIBROWSER&&!IS_SAMSUNGBROWSER&&!IS_OPPOBROWSER&&!IS_VIVOBROWSER&&IS_CHROMIUM_BASE,IS_HEADLESS_CHROME=isUserAgentMatch("HeadlessChrome"),CHROME_MAJOR_VERSION=getChromeMajorVersion(),CHROME_VERSION=getBrowserVersion("Chrome"),IS_SAFARI=!IS_CHROMIUM_BASE&&!IS_MQQB&&!IS_X5MQQB&&!IS_MACQQB&&!IS_IPADQQB&&isUserAgentMatch("Safari"),SAFARI_VERSION=getBrowserVersion("Version"),IS_ANDROID_WEBVIEW=/Android.*(wv|.0.0.0)/.test(USER_AGENT),IOS_VERSION=(()=>{if(IS_IPAD_PRO)return SAFARI_VERSION;if(IS_IOS){const A=USER_AGENT.match(/OS (\d+)_(\d+)/i);if(A&&A[1]){let I=A[1];return A[2]&&(I+=`.${A[2]}`),I}}return""})(),IOS_MAIN_VERSION=Number(IOS_VERSION.split(".")[0]),IS_IOS_13_OR_14=(()=>{const A=Number(IOS_VERSION.split(".")[0]);return 14===A||13===A})(),IS_IOS_CHROME=isUserAgentMatch("CriOS"),IS_LOCAL="undefined"!=typeof location&&("file:"===location.protocol||"localhost"===location.hostname||"127.0.0.1"===location.hostname),browserInfo=getBrowserInfo();function getBrowserInfo(){const A=new Map([[IS_FIREFOX,["Firefox",FIREFOX_VERSION]],[IS_EDG,["Edg",EDG_VERSION]],[IS_CHROME,["Chrome",CHROME_VERSION]],[IS_SAFARI,["Safari",SAFARI_VERSION]],[IS_TBS,["TBS",TBS_VERSION]],[IS_XWEB,["XWEB",XWEB_VERSION]],[IS_WECHAT&&IS_IPHONE,["WeChat",WECHAT_VERSION]],[IS_WQQB,["QQ(Win)",WQQB_VERSION]],[IS_MQQB,["QQ(Mobile)",MQQB_VERSION]],[IS_X5MQQB,["QQ(Mobile X5)",MQQB_VERSION]],[IS_MACQQB,["QQ(Mac)",MACQQB_VERSION]],[IS_IPADQQB,["QQ(iPad)",IPADQQB_VERSION]],[IS_MIBROWSER,["MI",MI_VERSION]],[IS_HUAWEIBROWSER,["HW",HUAWEI_VERSION]],[IS_SAMSUNGBROWSER,["Samsung",SAMSUNG_VERSION]],[IS_OPPOBROWSER,["OPPO",OPPO_VERSION]],[IS_VIVOBROWSER,["VIVO",VIVO_VERSION]],[IS_EDGE,["EDGE",EDGE_VERSION]],[IS_SOGOUM,["SogouMobile",SOGOUM_VERSION]],[IS_SOGOU,["Sogou",SOGOU_VERSION]]]);let I="unknown",g="unknown";return A.has(!0)&&([I,g]=A.get(!0)),{name:I,version:g}}var EPSILON=1e-6,ARRAY_TYPE="undefined"!=typeof Float32Array?Float32Array:Array,degree=Math.PI/180;Math.hypot||(Math.hypot=function(){for(var A=0,I=arguments.length;I--;)A+=arguments[I]*arguments[I];return Math.sqrt(A)});var mat4_exports={};function create(){var A=new ARRAY_TYPE(16);return ARRAY_TYPE!=Float32Array&&(A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=0,A[12]=0,A[13]=0,A[14]=0),A[0]=1,A[5]=1,A[10]=1,A[15]=1,A}function clone(A){var I=new ARRAY_TYPE(16);return I[0]=A[0],I[1]=A[1],I[2]=A[2],I[3]=A[3],I[4]=A[4],I[5]=A[5],I[6]=A[6],I[7]=A[7],I[8]=A[8],I[9]=A[9],I[10]=A[10],I[11]=A[11],I[12]=A[12],I[13]=A[13],I[14]=A[14],I[15]=A[15],I}function copy(A,I){return A[0]=I[0],A[1]=I[1],A[2]=I[2],A[3]=I[3],A[4]=I[4],A[5]=I[5],A[6]=I[6],A[7]=I[7],A[8]=I[8],A[9]=I[9],A[10]=I[10],A[11]=I[11],A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15],A}function fromValues(A,I,g,C,B,Q,E,t,e,r,i,o,a,s,n,c){var h=new ARRAY_TYPE(16);return h[0]=A,h[1]=I,h[2]=g,h[3]=C,h[4]=B,h[5]=Q,h[6]=E,h[7]=t,h[8]=e,h[9]=r,h[10]=i,h[11]=o,h[12]=a,h[13]=s,h[14]=n,h[15]=c,h}function set(A,I,g,C,B,Q,E,t,e,r,i,o,a,s,n,c,h){return A[0]=I,A[1]=g,A[2]=C,A[3]=B,A[4]=Q,A[5]=E,A[6]=t,A[7]=e,A[8]=r,A[9]=i,A[10]=o,A[11]=a,A[12]=s,A[13]=n,A[14]=c,A[15]=h,A}function identity(A){return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function transpose(A,I){if(A===I){var g=I[1],C=I[2],B=I[3],Q=I[6],E=I[7],t=I[11];A[1]=I[4],A[2]=I[8],A[3]=I[12],A[4]=g,A[6]=I[9],A[7]=I[13],A[8]=C,A[9]=Q,A[11]=I[14],A[12]=B,A[13]=E,A[14]=t}else A[0]=I[0],A[1]=I[4],A[2]=I[8],A[3]=I[12],A[4]=I[1],A[5]=I[5],A[6]=I[9],A[7]=I[13],A[8]=I[2],A[9]=I[6],A[10]=I[10],A[11]=I[14],A[12]=I[3],A[13]=I[7],A[14]=I[11],A[15]=I[15];return A}function invert(A,I){var g=I[0],C=I[1],B=I[2],Q=I[3],E=I[4],t=I[5],e=I[6],r=I[7],i=I[8],o=I[9],a=I[10],s=I[11],n=I[12],c=I[13],h=I[14],D=I[15],y=g*t-C*E,N=g*e-B*E,l=g*r-Q*E,u=C*e-B*t,w=C*r-Q*t,G=B*r-Q*e,M=i*c-o*n,d=i*h-a*n,R=i*D-s*n,S=o*h-a*c,F=o*D-s*c,p=a*D-s*h,Y=y*p-N*F+l*S+u*R-w*d+G*M;return Y?(Y=1/Y,A[0]=(t*p-e*F+r*S)*Y,A[1]=(B*F-C*p-Q*S)*Y,A[2]=(c*G-h*w+D*u)*Y,A[3]=(a*w-o*G-s*u)*Y,A[4]=(e*R-E*p-r*d)*Y,A[5]=(g*p-B*R+Q*d)*Y,A[6]=(h*l-n*G-D*N)*Y,A[7]=(i*G-a*l+s*N)*Y,A[8]=(E*F-t*R+r*M)*Y,A[9]=(C*R-g*F-Q*M)*Y,A[10]=(n*w-c*l+D*y)*Y,A[11]=(o*l-i*w-s*y)*Y,A[12]=(t*d-E*S-e*M)*Y,A[13]=(g*S-C*d+B*M)*Y,A[14]=(c*N-n*u-h*y)*Y,A[15]=(i*u-o*N+a*y)*Y,A):null}function adjoint(A,I){var g=I[0],C=I[1],B=I[2],Q=I[3],E=I[4],t=I[5],e=I[6],r=I[7],i=I[8],o=I[9],a=I[10],s=I[11],n=I[12],c=I[13],h=I[14],D=I[15];return A[0]=t*(a*D-s*h)-o*(e*D-r*h)+c*(e*s-r*a),A[1]=-(C*(a*D-s*h)-o*(B*D-Q*h)+c*(B*s-Q*a)),A[2]=C*(e*D-r*h)-t*(B*D-Q*h)+c*(B*r-Q*e),A[3]=-(C*(e*s-r*a)-t*(B*s-Q*a)+o*(B*r-Q*e)),A[4]=-(E*(a*D-s*h)-i*(e*D-r*h)+n*(e*s-r*a)),A[5]=g*(a*D-s*h)-i*(B*D-Q*h)+n*(B*s-Q*a),A[6]=-(g*(e*D-r*h)-E*(B*D-Q*h)+n*(B*r-Q*e)),A[7]=g*(e*s-r*a)-E*(B*s-Q*a)+i*(B*r-Q*e),A[8]=E*(o*D-s*c)-i*(t*D-r*c)+n*(t*s-r*o),A[9]=-(g*(o*D-s*c)-i*(C*D-Q*c)+n*(C*s-Q*o)),A[10]=g*(t*D-r*c)-E*(C*D-Q*c)+n*(C*r-Q*t),A[11]=-(g*(t*s-r*o)-E*(C*s-Q*o)+i*(C*r-Q*t)),A[12]=-(E*(o*h-a*c)-i*(t*h-e*c)+n*(t*a-e*o)),A[13]=g*(o*h-a*c)-i*(C*h-B*c)+n*(C*a-B*o),A[14]=-(g*(t*h-e*c)-E*(C*h-B*c)+n*(C*e-B*t)),A[15]=g*(t*a-e*o)-E*(C*a-B*o)+i*(C*e-B*t),A}function determinant(A){var I=A[0],g=A[1],C=A[2],B=A[3],Q=A[4],E=A[5],t=A[6],e=A[7],r=A[8],i=A[9],o=A[10],a=A[11],s=A[12],n=A[13],c=A[14],h=A[15];return(I*E-g*Q)*(o*h-a*c)-(I*t-C*Q)*(i*h-a*n)+(I*e-B*Q)*(i*c-o*n)+(g*t-C*E)*(r*h-a*s)-(g*e-B*E)*(r*c-o*s)+(C*e-B*t)*(r*n-i*s)}function multiply(A,I,g){var C=I[0],B=I[1],Q=I[2],E=I[3],t=I[4],e=I[5],r=I[6],i=I[7],o=I[8],a=I[9],s=I[10],n=I[11],c=I[12],h=I[13],D=I[14],y=I[15],N=g[0],l=g[1],u=g[2],w=g[3];return A[0]=N*C+l*t+u*o+w*c,A[1]=N*B+l*e+u*a+w*h,A[2]=N*Q+l*r+u*s+w*D,A[3]=N*E+l*i+u*n+w*y,N=g[4],l=g[5],u=g[6],w=g[7],A[4]=N*C+l*t+u*o+w*c,A[5]=N*B+l*e+u*a+w*h,A[6]=N*Q+l*r+u*s+w*D,A[7]=N*E+l*i+u*n+w*y,N=g[8],l=g[9],u=g[10],w=g[11],A[8]=N*C+l*t+u*o+w*c,A[9]=N*B+l*e+u*a+w*h,A[10]=N*Q+l*r+u*s+w*D,A[11]=N*E+l*i+u*n+w*y,N=g[12],l=g[13],u=g[14],w=g[15],A[12]=N*C+l*t+u*o+w*c,A[13]=N*B+l*e+u*a+w*h,A[14]=N*Q+l*r+u*s+w*D,A[15]=N*E+l*i+u*n+w*y,A}function translate(A,I,g){var C,B,Q,E,t,e,r,i,o,a,s,n,c=g[0],h=g[1],D=g[2];return I===A?(A[12]=I[0]*c+I[4]*h+I[8]*D+I[12],A[13]=I[1]*c+I[5]*h+I[9]*D+I[13],A[14]=I[2]*c+I[6]*h+I[10]*D+I[14],A[15]=I[3]*c+I[7]*h+I[11]*D+I[15]):(C=I[0],B=I[1],Q=I[2],E=I[3],t=I[4],e=I[5],r=I[6],i=I[7],o=I[8],a=I[9],s=I[10],n=I[11],A[0]=C,A[1]=B,A[2]=Q,A[3]=E,A[4]=t,A[5]=e,A[6]=r,A[7]=i,A[8]=o,A[9]=a,A[10]=s,A[11]=n,A[12]=C*c+t*h+o*D+I[12],A[13]=B*c+e*h+a*D+I[13],A[14]=Q*c+r*h+s*D+I[14],A[15]=E*c+i*h+n*D+I[15]),A}function scale(A,I,g){var C=g[0],B=g[1],Q=g[2];return A[0]=I[0]*C,A[1]=I[1]*C,A[2]=I[2]*C,A[3]=I[3]*C,A[4]=I[4]*B,A[5]=I[5]*B,A[6]=I[6]*B,A[7]=I[7]*B,A[8]=I[8]*Q,A[9]=I[9]*Q,A[10]=I[10]*Q,A[11]=I[11]*Q,A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15],A}function rotate(A,I,g,C){var B,Q,E,t,e,r,i,o,a,s,n,c,h,D,y,N,l,u,w,G,M,d,R,S,F=C[0],p=C[1],Y=C[2],f=Math.hypot(F,p,Y);return f<EPSILON?null:(F*=f=1/f,p*=f,Y*=f,B=Math.sin(g),E=1-(Q=Math.cos(g)),t=I[0],e=I[1],r=I[2],i=I[3],o=I[4],a=I[5],s=I[6],n=I[7],c=I[8],h=I[9],D=I[10],y=I[11],N=F*F*E+Q,l=p*F*E+Y*B,u=Y*F*E-p*B,w=F*p*E-Y*B,G=p*p*E+Q,M=Y*p*E+F*B,d=F*Y*E+p*B,R=p*Y*E-F*B,S=Y*Y*E+Q,A[0]=t*N+o*l+c*u,A[1]=e*N+a*l+h*u,A[2]=r*N+s*l+D*u,A[3]=i*N+n*l+y*u,A[4]=t*w+o*G+c*M,A[5]=e*w+a*G+h*M,A[6]=r*w+s*G+D*M,A[7]=i*w+n*G+y*M,A[8]=t*d+o*R+c*S,A[9]=e*d+a*R+h*S,A[10]=r*d+s*R+D*S,A[11]=i*d+n*R+y*S,I!==A&&(A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15]),A)}function rotateX(A,I,g){var C=Math.sin(g),B=Math.cos(g),Q=I[4],E=I[5],t=I[6],e=I[7],r=I[8],i=I[9],o=I[10],a=I[11];return I!==A&&(A[0]=I[0],A[1]=I[1],A[2]=I[2],A[3]=I[3],A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15]),A[4]=Q*B+r*C,A[5]=E*B+i*C,A[6]=t*B+o*C,A[7]=e*B+a*C,A[8]=r*B-Q*C,A[9]=i*B-E*C,A[10]=o*B-t*C,A[11]=a*B-e*C,A}function rotateY(A,I,g){var C=Math.sin(g),B=Math.cos(g),Q=I[0],E=I[1],t=I[2],e=I[3],r=I[8],i=I[9],o=I[10],a=I[11];return I!==A&&(A[4]=I[4],A[5]=I[5],A[6]=I[6],A[7]=I[7],A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15]),A[0]=Q*B-r*C,A[1]=E*B-i*C,A[2]=t*B-o*C,A[3]=e*B-a*C,A[8]=Q*C+r*B,A[9]=E*C+i*B,A[10]=t*C+o*B,A[11]=e*C+a*B,A}function rotateZ(A,I,g){var C=Math.sin(g),B=Math.cos(g),Q=I[0],E=I[1],t=I[2],e=I[3],r=I[4],i=I[5],o=I[6],a=I[7];return I!==A&&(A[8]=I[8],A[9]=I[9],A[10]=I[10],A[11]=I[11],A[12]=I[12],A[13]=I[13],A[14]=I[14],A[15]=I[15]),A[0]=Q*B+r*C,A[1]=E*B+i*C,A[2]=t*B+o*C,A[3]=e*B+a*C,A[4]=r*B-Q*C,A[5]=i*B-E*C,A[6]=o*B-t*C,A[7]=a*B-e*C,A}function fromTranslation(A,I){return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=I[0],A[13]=I[1],A[14]=I[2],A[15]=1,A}function fromScaling(A,I){return A[0]=I[0],A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=I[1],A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=I[2],A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function fromRotation(A,I,g){var C,B,Q,E=g[0],t=g[1],e=g[2],r=Math.hypot(E,t,e);return r<EPSILON?null:(E*=r=1/r,t*=r,e*=r,C=Math.sin(I),Q=1-(B=Math.cos(I)),A[0]=E*E*Q+B,A[1]=t*E*Q+e*C,A[2]=e*E*Q-t*C,A[3]=0,A[4]=E*t*Q-e*C,A[5]=t*t*Q+B,A[6]=e*t*Q+E*C,A[7]=0,A[8]=E*e*Q+t*C,A[9]=t*e*Q-E*C,A[10]=e*e*Q+B,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A)}function fromXRotation(A,I){var g=Math.sin(I),C=Math.cos(I);return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=C,A[6]=g,A[7]=0,A[8]=0,A[9]=-g,A[10]=C,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function fromYRotation(A,I){var g=Math.sin(I),C=Math.cos(I);return A[0]=C,A[1]=0,A[2]=-g,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=g,A[9]=0,A[10]=C,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function fromZRotation(A,I){var g=Math.sin(I),C=Math.cos(I);return A[0]=C,A[1]=g,A[2]=0,A[3]=0,A[4]=-g,A[5]=C,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function fromRotationTranslation(A,I,g){var C=I[0],B=I[1],Q=I[2],E=I[3],t=C+C,e=B+B,r=Q+Q,i=C*t,o=C*e,a=C*r,s=B*e,n=B*r,c=Q*r,h=E*t,D=E*e,y=E*r;return A[0]=1-(s+c),A[1]=o+y,A[2]=a-D,A[3]=0,A[4]=o-y,A[5]=1-(i+c),A[6]=n+h,A[7]=0,A[8]=a+D,A[9]=n-h,A[10]=1-(i+s),A[11]=0,A[12]=g[0],A[13]=g[1],A[14]=g[2],A[15]=1,A}function fromQuat2(A,I){var g=new ARRAY_TYPE(3),C=-I[0],B=-I[1],Q=-I[2],E=I[3],t=I[4],e=I[5],r=I[6],i=I[7],o=C*C+B*B+Q*Q+E*E;return o>0?(g[0]=2*(t*E+i*C+e*Q-r*B)/o,g[1]=2*(e*E+i*B+r*C-t*Q)/o,g[2]=2*(r*E+i*Q+t*B-e*C)/o):(g[0]=2*(t*E+i*C+e*Q-r*B),g[1]=2*(e*E+i*B+r*C-t*Q),g[2]=2*(r*E+i*Q+t*B-e*C)),fromRotationTranslation(A,I,g),A}function getTranslation(A,I){return A[0]=I[12],A[1]=I[13],A[2]=I[14],A}function getScaling(A,I){var g=I[0],C=I[1],B=I[2],Q=I[4],E=I[5],t=I[6],e=I[8],r=I[9],i=I[10];return A[0]=Math.hypot(g,C,B),A[1]=Math.hypot(Q,E,t),A[2]=Math.hypot(e,r,i),A}function getRotation(A,I){var g=new ARRAY_TYPE(3);getScaling(g,I);var C=1/g[0],B=1/g[1],Q=1/g[2],E=I[0]*C,t=I[1]*B,e=I[2]*Q,r=I[4]*C,i=I[5]*B,o=I[6]*Q,a=I[8]*C,s=I[9]*B,n=I[10]*Q,c=E+i+n,h=0;return c>0?(h=2*Math.sqrt(c+1),A[3]=.25*h,A[0]=(o-s)/h,A[1]=(a-e)/h,A[2]=(t-r)/h):E>i&&E>n?(h=2*Math.sqrt(1+E-i-n),A[3]=(o-s)/h,A[0]=.25*h,A[1]=(t+r)/h,A[2]=(a+e)/h):i>n?(h=2*Math.sqrt(1+i-E-n),A[3]=(a-e)/h,A[0]=(t+r)/h,A[1]=.25*h,A[2]=(o+s)/h):(h=2*Math.sqrt(1+n-E-i),A[3]=(t-r)/h,A[0]=(a+e)/h,A[1]=(o+s)/h,A[2]=.25*h),A}function fromRotationTranslationScale(A,I,g,C){var B=I[0],Q=I[1],E=I[2],t=I[3],e=B+B,r=Q+Q,i=E+E,o=B*e,a=B*r,s=B*i,n=Q*r,c=Q*i,h=E*i,D=t*e,y=t*r,N=t*i,l=C[0],u=C[1],w=C[2];return A[0]=(1-(n+h))*l,A[1]=(a+N)*l,A[2]=(s-y)*l,A[3]=0,A[4]=(a-N)*u,A[5]=(1-(o+h))*u,A[6]=(c+D)*u,A[7]=0,A[8]=(s+y)*w,A[9]=(c-D)*w,A[10]=(1-(o+n))*w,A[11]=0,A[12]=g[0],A[13]=g[1],A[14]=g[2],A[15]=1,A}function fromRotationTranslationScaleOrigin(A,I,g,C,B){var Q=I[0],E=I[1],t=I[2],e=I[3],r=Q+Q,i=E+E,o=t+t,a=Q*r,s=Q*i,n=Q*o,c=E*i,h=E*o,D=t*o,y=e*r,N=e*i,l=e*o,u=C[0],w=C[1],G=C[2],M=B[0],d=B[1],R=B[2],S=(1-(c+D))*u,F=(s+l)*u,p=(n-N)*u,Y=(s-l)*w,f=(1-(a+D))*w,U=(h+y)*w,k=(n+N)*G,L=(h-y)*G,b=(1-(a+c))*G;return A[0]=S,A[1]=F,A[2]=p,A[3]=0,A[4]=Y,A[5]=f,A[6]=U,A[7]=0,A[8]=k,A[9]=L,A[10]=b,A[11]=0,A[12]=g[0]+M-(S*M+Y*d+k*R),A[13]=g[1]+d-(F*M+f*d+L*R),A[14]=g[2]+R-(p*M+U*d+b*R),A[15]=1,A}function fromQuat(A,I){var g=I[0],C=I[1],B=I[2],Q=I[3],E=g+g,t=C+C,e=B+B,r=g*E,i=C*E,o=C*t,a=B*E,s=B*t,n=B*e,c=Q*E,h=Q*t,D=Q*e;return A[0]=1-o-n,A[1]=i+D,A[2]=a-h,A[3]=0,A[4]=i-D,A[5]=1-r-n,A[6]=s+c,A[7]=0,A[8]=a+h,A[9]=s-c,A[10]=1-r-o,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function frustum(A,I,g,C,B,Q,E){var t=1/(g-I),e=1/(B-C),r=1/(Q-E);return A[0]=2*Q*t,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=2*Q*e,A[6]=0,A[7]=0,A[8]=(g+I)*t,A[9]=(B+C)*e,A[10]=(E+Q)*r,A[11]=-1,A[12]=0,A[13]=0,A[14]=E*Q*2*r,A[15]=0,A}function perspectiveNO(A,I,g,C,B){var Q,E=1/Math.tan(I/2);return A[0]=E/g,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=E,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=-1,A[12]=0,A[13]=0,A[15]=0,null!=B&&B!==1/0?(Q=1/(C-B),A[10]=(B+C)*Q,A[14]=2*B*C*Q):(A[10]=-1,A[14]=-2*C),A}__export(mat4_exports,{add:()=>add,adjoint:()=>adjoint,clone:()=>clone,copy:()=>copy,create:()=>create,determinant:()=>determinant,equals:()=>equals,exactEquals:()=>exactEquals,frob:()=>frob,fromQuat:()=>fromQuat,fromQuat2:()=>fromQuat2,fromRotation:()=>fromRotation,fromRotationTranslation:()=>fromRotationTranslation,fromRotationTranslationScale:()=>fromRotationTranslationScale,fromRotationTranslationScaleOrigin:()=>fromRotationTranslationScaleOrigin,fromScaling:()=>fromScaling,fromTranslation:()=>fromTranslation,fromValues:()=>fromValues,fromXRotation:()=>fromXRotation,fromYRotation:()=>fromYRotation,fromZRotation:()=>fromZRotation,frustum:()=>frustum,getRotation:()=>getRotation,getScaling:()=>getScaling,getTranslation:()=>getTranslation,identity:()=>identity,invert:()=>invert,lookAt:()=>lookAt,mul:()=>mul,multiply:()=>multiply,multiplyScalar:()=>multiplyScalar,multiplyScalarAndAdd:()=>multiplyScalarAndAdd,ortho:()=>ortho,orthoNO:()=>orthoNO,orthoZO:()=>orthoZO,perspective:()=>perspective,perspectiveFromFieldOfView:()=>perspectiveFromFieldOfView,perspectiveNO:()=>perspectiveNO,perspectiveZO:()=>perspectiveZO,rotate:()=>rotate,rotateX:()=>rotateX,rotateY:()=>rotateY,rotateZ:()=>rotateZ,scale:()=>scale,set:()=>set,str:()=>str,sub:()=>sub,subtract:()=>subtract,targetTo:()=>targetTo,translate:()=>translate,transpose:()=>transpose});var perspective=perspectiveNO;function perspectiveZO(A,I,g,C,B){var Q,E=1/Math.tan(I/2);return A[0]=E/g,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=E,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=-1,A[12]=0,A[13]=0,A[15]=0,null!=B&&B!==1/0?(Q=1/(C-B),A[10]=B*Q,A[14]=B*C*Q):(A[10]=-1,A[14]=-C),A}function perspectiveFromFieldOfView(A,I,g,C){var B=Math.tan(I.upDegrees*Math.PI/180),Q=Math.tan(I.downDegrees*Math.PI/180),E=Math.tan(I.leftDegrees*Math.PI/180),t=Math.tan(I.rightDegrees*Math.PI/180),e=2/(E+t),r=2/(B+Q);return A[0]=e,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=r,A[6]=0,A[7]=0,A[8]=-(E-t)*e*.5,A[9]=(B-Q)*r*.5,A[10]=C/(g-C),A[11]=-1,A[12]=0,A[13]=0,A[14]=C*g/(g-C),A[15]=0,A}function orthoNO(A,I,g,C,B,Q,E){var t=1/(I-g),e=1/(C-B),r=1/(Q-E);return A[0]=-2*t,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=-2*e,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=2*r,A[11]=0,A[12]=(I+g)*t,A[13]=(B+C)*e,A[14]=(E+Q)*r,A[15]=1,A}var ortho=orthoNO;function orthoZO(A,I,g,C,B,Q,E){var t=1/(I-g),e=1/(C-B),r=1/(Q-E);return A[0]=-2*t,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=-2*e,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=r,A[11]=0,A[12]=(I+g)*t,A[13]=(B+C)*e,A[14]=Q*r,A[15]=1,A}function lookAt(A,I,g,C){var B,Q,E,t,e,r,i,o,a,s,n=I[0],c=I[1],h=I[2],D=C[0],y=C[1],N=C[2],l=g[0],u=g[1],w=g[2];return Math.abs(n-l)<EPSILON&&Math.abs(c-u)<EPSILON&&Math.abs(h-w)<EPSILON?identity(A):(i=n-l,o=c-u,a=h-w,B=y*(a*=s=1/Math.hypot(i,o,a))-N*(o*=s),Q=N*(i*=s)-D*a,E=D*o-y*i,(s=Math.hypot(B,Q,E))?(B*=s=1/s,Q*=s,E*=s):(B=0,Q=0,E=0),t=o*E-a*Q,e=a*B-i*E,r=i*Q-o*B,(s=Math.hypot(t,e,r))?(t*=s=1/s,e*=s,r*=s):(t=0,e=0,r=0),A[0]=B,A[1]=t,A[2]=i,A[3]=0,A[4]=Q,A[5]=e,A[6]=o,A[7]=0,A[8]=E,A[9]=r,A[10]=a,A[11]=0,A[12]=-(B*n+Q*c+E*h),A[13]=-(t*n+e*c+r*h),A[14]=-(i*n+o*c+a*h),A[15]=1,A)}function targetTo(A,I,g,C){var B=I[0],Q=I[1],E=I[2],t=C[0],e=C[1],r=C[2],i=B-g[0],o=Q-g[1],a=E-g[2],s=i*i+o*o+a*a;s>0&&(i*=s=1/Math.sqrt(s),o*=s,a*=s);var n=e*a-r*o,c=r*i-t*a,h=t*o-e*i;return(s=n*n+c*c+h*h)>0&&(n*=s=1/Math.sqrt(s),c*=s,h*=s),A[0]=n,A[1]=c,A[2]=h,A[3]=0,A[4]=o*h-a*c,A[5]=a*n-i*h,A[6]=i*c-o*n,A[7]=0,A[8]=i,A[9]=o,A[10]=a,A[11]=0,A[12]=B,A[13]=Q,A[14]=E,A[15]=1,A}function str(A){return"mat4("+A[0]+", "+A[1]+", "+A[2]+", "+A[3]+", "+A[4]+", "+A[5]+", "+A[6]+", "+A[7]+", "+A[8]+", "+A[9]+", "+A[10]+", "+A[11]+", "+A[12]+", "+A[13]+", "+A[14]+", "+A[15]+")"}function frob(A){return Math.hypot(A[0],A[1],A[2],A[3],A[4],A[5],A[6],A[7],A[8],A[9],A[10],A[11],A[12],A[13],A[14],A[15])}function add(A,I,g){return A[0]=I[0]+g[0],A[1]=I[1]+g[1],A[2]=I[2]+g[2],A[3]=I[3]+g[3],A[4]=I[4]+g[4],A[5]=I[5]+g[5],A[6]=I[6]+g[6],A[7]=I[7]+g[7],A[8]=I[8]+g[8],A[9]=I[9]+g[9],A[10]=I[10]+g[10],A[11]=I[11]+g[11],A[12]=I[12]+g[12],A[13]=I[13]+g[13],A[14]=I[14]+g[14],A[15]=I[15]+g[15],A}function subtract(A,I,g){return A[0]=I[0]-g[0],A[1]=I[1]-g[1],A[2]=I[2]-g[2],A[3]=I[3]-g[3],A[4]=I[4]-g[4],A[5]=I[5]-g[5],A[6]=I[6]-g[6],A[7]=I[7]-g[7],A[8]=I[8]-g[8],A[9]=I[9]-g[9],A[10]=I[10]-g[10],A[11]=I[11]-g[11],A[12]=I[12]-g[12],A[13]=I[13]-g[13],A[14]=I[14]-g[14],A[15]=I[15]-g[15],A}function multiplyScalar(A,I,g){return A[0]=I[0]*g,A[1]=I[1]*g,A[2]=I[2]*g,A[3]=I[3]*g,A[4]=I[4]*g,A[5]=I[5]*g,A[6]=I[6]*g,A[7]=I[7]*g,A[8]=I[8]*g,A[9]=I[9]*g,A[10]=I[10]*g,A[11]=I[11]*g,A[12]=I[12]*g,A[13]=I[13]*g,A[14]=I[14]*g,A[15]=I[15]*g,A}function multiplyScalarAndAdd(A,I,g,C){return A[0]=I[0]+g[0]*C,A[1]=I[1]+g[1]*C,A[2]=I[2]+g[2]*C,A[3]=I[3]+g[3]*C,A[4]=I[4]+g[4]*C,A[5]=I[5]+g[5]*C,A[6]=I[6]+g[6]*C,A[7]=I[7]+g[7]*C,A[8]=I[8]+g[8]*C,A[9]=I[9]+g[9]*C,A[10]=I[10]+g[10]*C,A[11]=I[11]+g[11]*C,A[12]=I[12]+g[12]*C,A[13]=I[13]+g[13]*C,A[14]=I[14]+g[14]*C,A[15]=I[15]+g[15]*C,A}function exactEquals(A,I){return A[0]===I[0]&&A[1]===I[1]&&A[2]===I[2]&&A[3]===I[3]&&A[4]===I[4]&&A[5]===I[5]&&A[6]===I[6]&&A[7]===I[7]&&A[8]===I[8]&&A[9]===I[9]&&A[10]===I[10]&&A[11]===I[11]&&A[12]===I[12]&&A[13]===I[13]&&A[14]===I[14]&&A[15]===I[15]}function equals(A,I){var g=A[0],C=A[1],B=A[2],Q=A[3],E=A[4],t=A[5],e=A[6],r=A[7],i=A[8],o=A[9],a=A[10],s=A[11],n=A[12],c=A[13],h=A[14],D=A[15],y=I[0],N=I[1],l=I[2],u=I[3],w=I[4],G=I[5],M=I[6],d=I[7],R=I[8],S=I[9],F=I[10],p=I[11],Y=I[12],f=I[13],U=I[14],k=I[15];return Math.abs(g-y)<=EPSILON*Math.max(1,Math.abs(g),Math.abs(y))&&Math.abs(C-N)<=EPSILON*Math.max(1,Math.abs(C),Math.abs(N))&&Math.abs(B-l)<=EPSILON*Math.max(1,Math.abs(B),Math.abs(l))&&Math.abs(Q-u)<=EPSILON*Math.max(1,Math.abs(Q),Math.abs(u))&&Math.abs(E-w)<=EPSILON*Math.max(1,Math.abs(E),Math.abs(w))&&Math.abs(t-G)<=EPSILON*Math.max(1,Math.abs(t),Math.abs(G))&&Math.abs(e-M)<=EPSILON*Math.max(1,Math.abs(e),Math.abs(M))&&Math.abs(r-d)<=EPSILON*Math.max(1,Math.abs(r),Math.abs(d))&&Math.abs(i-R)<=EPSILON*Math.max(1,Math.abs(i),Math.abs(R))&&Math.abs(o-S)<=EPSILON*Math.max(1,Math.abs(o),Math.abs(S))&&Math.abs(a-F)<=EPSILON*Math.max(1,Math.abs(a),Math.abs(F))&&Math.abs(s-p)<=EPSILON*Math.max(1,Math.abs(s),Math.abs(p))&&Math.abs(n-Y)<=EPSILON*Math.max(1,Math.abs(n),Math.abs(Y))&&Math.abs(c-f)<=EPSILON*Math.max(1,Math.abs(c),Math.abs(f))&&Math.abs(h-U)<=EPSILON*Math.max(1,Math.abs(h),Math.abs(U))&&Math.abs(D-k)<=EPSILON*Math.max(1,Math.abs(D),Math.abs(k))}var mul=multiply,sub=subtract,KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE=570703,vbSeq=0,_VirtualBackground=class A{constructor(A){this.core=A,__publicField(this,"seq"),__publicField(this,"_core"),__publicField(this,"log"),__publicField(this,"preLoadPromise"),__publicField(this,"startResolve"),__publicField(this,"startReject"),__publicField(this,"mediaPipeSolutions"),__publicField(this,"assetsPath"),__publicField(this,"currentType"),__publicField(this,"onAbort"),__publicField(this,"isAborted",!1),vbSeq+=1,this.seq=vbSeq,this._core=A,this.log=A.log.createChild({id:`${this.getAlias()}${vbSeq}`}),this.log.info("created"),A.assetsPath&&(this.preLoadPromise=this.preload(A.assetsPath))}static isSupported(){if(CHROME_MAJOR_VERSION<90)return!1;const A=document.createElement("canvas").getContext("webgl2");return!!(A&&A instanceof WebGL2RenderingContext)}async preload(A){try{this._core.room.videoManager.Wasm||(this._core.room.videoManager.Wasm=await allin1_default());const I=A=>{var I;this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!1,this.isAborted,"ABORT_IN_INFERENCE")}),this.isAborted=!0,this.log.error("mediaPipeSolutions abort",A),this.core.clearStarted(this,this.getGroup()),this.stop(),null==(I=this.onAbort)||I.call(this,A)};this._core.room.videoManager.initVirtualBackground(I,mat4_exports),await this._core.initVisionTaskRegistry(A)}catch(A){const{RtcError:I,ErrorCode:g}=this._core.errorModule;throw new I({code:g.INVALID_OPERATION,message:`VirtualBackground preload error, please redeploy the assets of the npm package. detail: ${A}`})}}getName(){return A.Name}getAlias(){return"vb"}getValidateRule(A){switch(A){case"start":return startValidateRule(this._core);case"update":return updateValidateRule(this._core);case"stop":return stopValidateRule(this._core)}}getGroup(){return"vb"}getKVTypeValue(A=!1,I=!1,g="NONE"){let C=0;switch(this.currentType){case"blur":C|=0;break;case"image":C|=1;break;case"green":C|=2}switch(A&&(C|=256),I&&(C|=512),g){case"ABORT_IN_INFERENCE":C|=4096;break;case"ABORT_IN_VIDEO_MANAGER":C|=8192;break;case"OTHER":C|=61440}return C}async start(A){const{type:I="blur",src:g,blurLevel:C=3,onAbort:B}=A;this.currentType=I,this.onAbort=B;const{auth:Q}=await authEffect({sdkAppId:A.sdkAppId,userId:A.userId,userSig:A.userSig,core:this._core}),{RtcError:E,ErrorCodeDictionary:t,ErrorCode:e}=this._core.errorModule;if(!Q){const I=this._core.utils.isOverseaSdkAppId(A.sdkAppId)?"https://trtc.io/document/56025":"https://cloud.tencent.com/document/product/647/85386";throw new E({code:t.NEED_TO_BUY,messageParams:{value:"Virtual Background",url:I}})}if(!this.preLoadPromise){if(!this._core.assetsPath)throw new E({code:e.INVALID_PARAMETER,message:"you need to deploy the assets of the npm package and set assetsPath param in TRTC.create()"});this.preLoadPromise=this.preload(this._core.assetsPath)}return await this.preLoadPromise,this.core.room.videoManager.setVirtualBackground({type:I,imageUrl:g,blurLevel:C,enableFaceCentering:A.enableFaceCentering,onAbort:A=>{var I;this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!0,this.isAborted,"ABORT_IN_VIDEO_MANAGER")}),this.isAborted=!0,this.core.clearStarted(this,this.getGroup()),this.stop(),delete this.preLoadPromise,null==(I=this.onAbort)||I.call(this,A)}}).then((()=>{this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!1,this.isAborted,"NONE")})})).catch((()=>{this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!0,this.isAborted,"OTHER")})}))}async update(A){const{type:I,src:g}=A;return I!==this.currentType&&(this.currentType=I),this.core.room.videoManager.setVirtualBackground({type:I,imageUrl:g,blurLevel:A.blurLevel,enableFaceCentering:A.enableFaceCentering}).then((()=>{this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!1,!1,"NONE")})})).catch((()=>{this.core.kvStatManager.addEnum({key:KV_REPORT_KEY_VIRTUAL_BACKGROUND_TYPE,value:this.getKVTypeValue(!0,!1,"OTHER")})}))}async stop(){return this.core.room.videoManager.setVirtualBackground()}};__publicField(_VirtualBackground,"Name","VirtualBackground");var VirtualBackground=_VirtualBackground,index_default=VirtualBackground;export{index_default as default};export{VirtualBackground};