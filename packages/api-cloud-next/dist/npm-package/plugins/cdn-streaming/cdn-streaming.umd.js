!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).CDNStreaming=e()}(this,(function(){"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function e(t,e,r,n,o,i,a){try{var s=t[i](a),u=s.value}catch(t){return void r(t)}s.done?e(u):Promise.resolve(u).then(n,o)}function r(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function s(t){e(a,o,i,s,u,"next",t)}function u(t){e(a,o,i,s,u,"throw",t)}s(void 0)}))}}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function i(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var t=n.next();return s=t.done,t},e:function(t){u=!0,a=t},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw a}}}}function a(){a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function h(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{h({},"")}catch(t){h=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new M(n||[]);return o(a,"_invoke",{value:w(t,r,s)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=d;var m="suspendedStart",p="suspendedYield",f="executing",g="completed",v={};function b(){}function y(){}function _(){}var I={};h(I,s,(function(){return this}));var S=Object.getPrototypeOf,x=S&&S(S(E([])));x&&x!==r&&n.call(x,s)&&(I=x);var C=_.prototype=b.prototype=Object.create(I);function D(t){["next","throw","return"].forEach((function(e){h(t,e,(function(t){return this._invoke(e,t)}))}))}function N(t,e){function r(o,i,a,s){var u=l(t[o],t,i);if("throw"!==u.type){var c=u.arg,h=c.value;return h&&"object"==typeof h&&n.call(h,"__await")?e.resolve(h.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(h).then((function(t){c.value=t,a(c)}),(function(t){return r("throw",t,a,s)}))}s(u.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function w(e,r,n){var o=m;return function(i,a){if(o===f)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var u=P(s,n);if(u){if(u===v)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=f;var c=l(e,r,n);if("normal"===c.type){if(o=n.done?g:p,c.arg===v)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=g,n.method="throw",n.arg=c.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function E(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return y.prototype=_,o(C,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:y,configurable:!0}),y.displayName=h(_,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,_):(t.__proto__=_,h(t,c,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},D(N.prototype),h(N.prototype,u,(function(){return this})),e.AsyncIterator=N,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new N(d(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},D(C),h(C,c,"Generator"),h(C,s,(function(){return this})),h(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=E,M.prototype={constructor:M,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:E(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function s(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}var c,h,d,l=Object.create,m=Object.defineProperty,p=Object.defineProperties,f=Object.getOwnPropertyDescriptor,g=Object.getOwnPropertyDescriptors,v=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols,y=Object.getPrototypeOf,_=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable,S=function(t,e,r){return e in t?m(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r},x=function(t,e){for(var r in e||(e={}))_.call(e,r)&&S(t,r,e[r]);if(b){var n,o=i(b(e));try{for(o.s();!(n=o.n()).done;){r=n.value;I.call(e,r)&&S(t,r,e[r])}}catch(t){o.e(t)}finally{o.f()}}return t},C=function(t,e){return p(t,g(e))},D=function(t,e,r){return r=null!=t?l(y(t)):{},function(t,e,r,n){if(e&&"object"===u(e)||"function"==typeof e){var o,a=i(v(e));try{var s=function(){var i=o.value;_.call(t,i)||i===r||m(t,i,{get:function(){return e[i]},enumerable:!(n=f(e,i))||n.enumerable})};for(a.s();!(o=a.n()).done;)s()}catch(t){a.e(t)}finally{a.f()}}return t}(m(r,"default",{value:t,enumerable:!0}),t)},N=function(t,e,r){return S(t,"symbol"!==u(e)?e+"":e,r)},w=(c={"../node_modules/.pnpm/blueimp-md5@2.19.0/node_modules/blueimp-md5/js/md5.js":function(t,e){!function(t){function r(t,e){var r=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(r>>16)<<16|65535&r}function n(t,e,n,o,i,a){return r((s=r(r(e,t),r(o,a)))<<(u=i)|s>>>32-u,n);var s,u}function o(t,e,r,o,i,a,s){return n(e&r|~e&o,t,e,i,a,s)}function i(t,e,r,o,i,a,s){return n(e&o|r&~o,t,e,i,a,s)}function a(t,e,r,o,i,a,s){return n(e^r^o,t,e,i,a,s)}function s(t,e,r,o,i,a,s){return n(r^(e|~o),t,e,i,a,s)}function c(t,e){var n,u,c,h,d;t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var l=1732584193,m=-271733879,p=-1732584194,f=271733878;for(n=0;n<t.length;n+=16)u=l,c=m,h=p,d=f,l=o(l,m,p,f,t[n],7,-680876936),f=o(f,l,m,p,t[n+1],12,-389564586),p=o(p,f,l,m,t[n+2],17,606105819),m=o(m,p,f,l,t[n+3],22,-1044525330),l=o(l,m,p,f,t[n+4],7,-176418897),f=o(f,l,m,p,t[n+5],12,1200080426),p=o(p,f,l,m,t[n+6],17,-1473231341),m=o(m,p,f,l,t[n+7],22,-45705983),l=o(l,m,p,f,t[n+8],7,1770035416),f=o(f,l,m,p,t[n+9],12,-1958414417),p=o(p,f,l,m,t[n+10],17,-42063),m=o(m,p,f,l,t[n+11],22,-1990404162),l=o(l,m,p,f,t[n+12],7,1804603682),f=o(f,l,m,p,t[n+13],12,-40341101),p=o(p,f,l,m,t[n+14],17,-1502002290),l=i(l,m=o(m,p,f,l,t[n+15],22,1236535329),p,f,t[n+1],5,-165796510),f=i(f,l,m,p,t[n+6],9,-1069501632),p=i(p,f,l,m,t[n+11],14,643717713),m=i(m,p,f,l,t[n],20,-373897302),l=i(l,m,p,f,t[n+5],5,-701558691),f=i(f,l,m,p,t[n+10],9,38016083),p=i(p,f,l,m,t[n+15],14,-660478335),m=i(m,p,f,l,t[n+4],20,-405537848),l=i(l,m,p,f,t[n+9],5,568446438),f=i(f,l,m,p,t[n+14],9,-1019803690),p=i(p,f,l,m,t[n+3],14,-187363961),m=i(m,p,f,l,t[n+8],20,1163531501),l=i(l,m,p,f,t[n+13],5,-1444681467),f=i(f,l,m,p,t[n+2],9,-51403784),p=i(p,f,l,m,t[n+7],14,1735328473),l=a(l,m=i(m,p,f,l,t[n+12],20,-1926607734),p,f,t[n+5],4,-378558),f=a(f,l,m,p,t[n+8],11,-2022574463),p=a(p,f,l,m,t[n+11],16,1839030562),m=a(m,p,f,l,t[n+14],23,-35309556),l=a(l,m,p,f,t[n+1],4,-1530992060),f=a(f,l,m,p,t[n+4],11,1272893353),p=a(p,f,l,m,t[n+7],16,-155497632),m=a(m,p,f,l,t[n+10],23,-1094730640),l=a(l,m,p,f,t[n+13],4,681279174),f=a(f,l,m,p,t[n],11,-358537222),p=a(p,f,l,m,t[n+3],16,-722521979),m=a(m,p,f,l,t[n+6],23,76029189),l=a(l,m,p,f,t[n+9],4,-640364487),f=a(f,l,m,p,t[n+12],11,-421815835),p=a(p,f,l,m,t[n+15],16,530742520),l=s(l,m=a(m,p,f,l,t[n+2],23,-995338651),p,f,t[n],6,-198630844),f=s(f,l,m,p,t[n+7],10,1126891415),p=s(p,f,l,m,t[n+14],15,-1416354905),m=s(m,p,f,l,t[n+5],21,-57434055),l=s(l,m,p,f,t[n+12],6,1700485571),f=s(f,l,m,p,t[n+3],10,-1894986606),p=s(p,f,l,m,t[n+10],15,-1051523),m=s(m,p,f,l,t[n+1],21,-2054922799),l=s(l,m,p,f,t[n+8],6,1873313359),f=s(f,l,m,p,t[n+15],10,-30611744),p=s(p,f,l,m,t[n+6],15,-1560198380),m=s(m,p,f,l,t[n+13],21,1309151649),l=s(l,m,p,f,t[n+4],6,-145523070),f=s(f,l,m,p,t[n+11],10,-1120210379),p=s(p,f,l,m,t[n+2],15,718787259),m=s(m,p,f,l,t[n+9],21,-343485551),l=r(l,u),m=r(m,c),p=r(p,h),f=r(f,d);return[l,m,p,f]}function h(t){var e,r="",n=32*t.length;for(e=0;e<n;e+=8)r+=String.fromCharCode(t[e>>5]>>>e%32&255);return r}function d(t){var e,r=[];for(r[(t.length>>2)-1]=void 0,e=0;e<r.length;e+=1)r[e]=0;var n=8*t.length;for(e=0;e<n;e+=8)r[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return r}function l(t){var e,r,n="0123456789abcdef",o="";for(r=0;r<t.length;r+=1)e=t.charCodeAt(r),o+=n.charAt(e>>>4&15)+n.charAt(15&e);return o}function m(t){return unescape(encodeURIComponent(t))}function p(t){return function(t){return h(c(d(t),8*t.length))}(m(t))}function f(t,e){return function(t,e){var r,n,o=d(t),i=[],a=[];for(i[15]=a[15]=void 0,o.length>16&&(o=c(o,8*t.length)),r=0;r<16;r+=1)i[r]=909522486^o[r],a[r]=1549556828^o[r];return n=c(i.concat(d(e)),512+8*e.length),h(c(a.concat(n),640))}(m(t),m(e))}function g(t,e,r){return e?r?f(e,t):l(f(e,t)):r?p(t):l(p(t))}"function"==typeof define&&define.amd?define((function(){return g})):"object"===u(e)&&e.exports?e.exports=g:t.md5=g}(t)}},function(){return h||(0,c[v(c)[0]])((h={exports:{}}).exports,h),h.exports}),P=D(w()),k=0,T=4,M=5,E=function(){return o((function t(e,r){n(this,t),N(this,"_core"),N(this,"_room"),N(this,"_log"),N(this,"_params"),N(this,"_publishGivenCDNData",null),this._core=e,this._room=e.room,this._log=r}),[{key:"isPublishingGivenCDN",get:function(){return!!this._params}},{key:"startPublishGivenCDN",value:(e=r(a().mark((function t(e){var r,n,o,i,s,u,c,h;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] startPublishGivenCDN() current: ".concat(JSON.stringify(this._params),", params: ").concat(JSON.stringify(e))),!this.isPublishingGivenCDN){t.next=7;break}if(r=this._params||{},n=r.appId,o=r.bizId,i=r.url,n!==e.appId||o!==e.bizId||i!==e.url){t.next=5;break}return t.abrupt("return");case 5:return t.next=7,this.stopPublishGivenCDN();case 7:return this._params=e,this._publishGivenCDNData={pushRequestTime:Date.now(),pushAppId:e.appId,pushBizId:e.bizId,pushCdnUrl:e.url,pushStreamType:this.convertStreamType(null==e?void 0:e.publishMode),pushStreamId:e.streamId},t.prev=9,t.next=12,this._room.sendStartPublishCDN(this._publishGivenCDNData,!1);case 12:if(s=t.sent,u=s.data,c=u.code,h=u.message,0!==c){t.next=18;break}this._log.info("[CDNStreaming] server success: start given cdn."),t.next=21;break;case 18:throw this.resetGivenCDN(),this._log.error("[CDNStreaming] server failed: start given cdn errCode: ".concat(c," errMsg: ").concat(h," options: ").concat(JSON.stringify(e))),new Error("[CDNStreaming] server failed: start given cdn errCode: ".concat(c," errMsg: ").concat(h));case 21:t.next=27;break;case 23:throw t.prev=23,t.t0=t.catch(9),this.resetGivenCDN(),t.t0;case 27:case"end":return t.stop()}}),t,this,[[9,23]])}))),function(t){return e.apply(this,arguments)})},{key:"stopPublishGivenCDN",value:(t=r(a().mark((function t(){var e,r,n,o,i,s,u,c,h,d,l;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] stopPublishGivenCDN"),this.isPublishingGivenCDN&&this._publishGivenCDNData){t.next=4;break}return this.resetGivenCDN(),t.abrupt("return");case 4:return e=this._publishGivenCDNData,r=e.pushAppId,n=e.pushBizId,o=e.pushCdnUrl,i=e.pushStreamType,s=e.pushStreamId,u={pushRequestTime:Date.now(),pushAppId:r,pushBizId:n,pushCdnUrl:o,pushStreamType:i,pushStreamId:s},t.next=8,this._room.sendStopPublishCDN(u,!1);case 8:if(c=t.sent,h=c.data,d=h.code,l=h.message,0!==d){t.next=15;break}this._log.info("[CDNStreaming] server success: stop given cdn."),this.resetGivenCDN(),t.next=17;break;case 15:throw this._log.error("[CDNStreaming] server failed: stop given cdn errCode: ".concat(d," errMsg: ").concat(l," data: ").concat(JSON.stringify(u))),new Error("[CDNStreaming] server failed: stop given cdn errCode: ".concat(d," errMsg: ").concat(l));case 17:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"resetGivenCDN",value:function(){this._publishGivenCDNData=null,this._params=void 0}},{key:"convertStreamType",value:function(t){return"publish-sub-stream-to-cdn"===t?"aux":"main"}}]);var t,e}(),R=function(){return o((function t(e,r){n(this,t),N(this,"_core"),N(this,"_room"),N(this,"_log"),N(this,"_config",null),N(this,"_data",null),N(this,"_givenCDNManager"),this._core=e,this._room=e.room,this._log=r,this.reset()}),[{key:"isMixing",get:function(){return!!this._data}},{key:"isStarted",get:function(){return!!this._config}},{key:"hasCustomCDN",get:function(){var t,e,r;return(null==(t=this._config)?void 0:t.target.appId)&&(null==(e=this._config)?void 0:e.target.bizId)&&(null==(r=this._config)?void 0:r.target.url)}},{key:"startMixTranscode",value:(c=r(a().mark((function t(e){var r,n,o,i,s,u,c,h,d;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("startMixTranscode: ".concat(JSON.stringify(e))),this._config=e,this.installEvents(),this._core.room.isJoined){t.next=5;break}return t.abrupt("return");case 5:if(t.prev=5,r=this.getInputParam(e),n=this.getOutputParam(e),o=this.getOutputSessionId({config:e,roomId:this._room.roomId,userId:this._room.userId}),!this.isMixing||!this._data||o===this._data.outputSessionId){t.next=13;break}return this._log.info("[CDNStreaming] streamId changed, auto stop mixing before start"),t.next=13,this.doStopMixTranscode();case 13:return t.next=15,this.doStartMixTranscode({outputSessionId:o,inputParam:r,outputParam:n});case 15:if(i=e.target,s=i.appId,u=i.bizId,c=i.url,h=i.streamId,d=void 0===h?"":h,!(s&&u&&c)){t.next=20;break}return this._givenCDNManager||(this._givenCDNManager=new E(this._core,this._log)),t.next=20,this._givenCDNManager.startPublishGivenCDN({publishMode:e.target.publishMode,appId:s,bizId:u,url:c,streamId:d});case 20:t.next=26;break;case 22:throw t.prev=22,t.t0=t.catch(5),this.reset(),t.t0;case 26:case"end":return t.stop()}}),t,this,[[5,22]])}))),function(t){return c.apply(this,arguments)})},{key:"doStartMixTranscode",value:(u=r(a().mark((function t(e){var r,n,o,i,s,u,c;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.outputSessionId,n=e.inputParam,o=e.outputParam,i={roomId:String(this._room.roomId),mcuRequestTime:Date.now(),outputSessionId:r,inputParam:n,outputParam:o},this._log.info("[CDNStreaming] doStartMixTranscode: ".concat(JSON.stringify(i))),t.next=5,this._room.sendStartMixTranscode(i);case 5:if(s=t.sent,u=s.data.code,c=s.data.message,0!==u){t.next=13;break}this._log.info("[CDNStreaming] server success: start mix"),this._data=i,t.next=16;break;case 13:throw-102083===u&&(c="Please enable relayed-push in ".concat(this._core.constants.CLOUD_CONSOLE_URL," and try later, refer to ").concat(this._core.constants.DOC_URL,"tutorial-26-advanced-publish-cdn-stream.html")),this._log.error("[CDNStreaming] server failed: start mix errCode: ".concat(u," errMsg: ").concat(c)),new Error("[CDNStreaming] server failed: start mix errCode: ".concat(u," errMsg: ").concat(c));case 16:case"end":return t.stop()}}),t,this)}))),function(t){return u.apply(this,arguments)})},{key:"stopMixTranscode",value:(s=r(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] stopMixTranscode"),t.prev=1,!this.isStarted||!this.isMixing){t.next=8;break}return t.next=5,this.doStopMixTranscode();case 5:if(!(this._config&&this.hasCustomCDN&&this._givenCDNManager)){t.next=8;break}return t.next=8,this._givenCDNManager.stopPublishGivenCDN();case 8:t.next=13;break;case 10:throw t.prev=10,t.t0=t.catch(1),t.t0;case 13:this.reset();case 14:case"end":return t.stop()}}),t,this,[[1,10]])}))),function(){return s.apply(this,arguments)})},{key:"doStopMixTranscode",value:(i=r(a().mark((function t(){var e,r,n,o,i;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e={mcuRequestTime:Date.now(),outputSessionId:this._data.outputSessionId,streamType:this._data.outputParam.streamType},this._log.info("[CDNStreaming] doStopMixTranscode: ".concat(JSON.stringify(e))),t.next=4,this._room.sendStopMixTranscode(e);case 4:if(r=t.sent,n=r.data,o=n.code,i=n.message,0!==o){t.next=11;break}this._log.info("[CDNStreaming] server success: stop mix"),this.reset(),t.next=13;break;case 11:throw this._log.error("[CDNStreaming] server failed: stop mix errCode: ".concat(o," errMsg: ").concat(i)),new Error("[CDNStreaming] server failed: stop mix errCode: ".concat(o," errMsg: ").concat(i));case 13:case"end":return t.stop()}}),t,this)}))),function(){return i.apply(this,arguments)})},{key:"reset",value:function(){this._config=null,this._data=null,this.uninstallEvents()}},{key:"installEvents",value:function(){this._core.room.on("joined",this.handleRoomJoined,this),this._core.room.on("left",this.handleRoomLeft,this)}},{key:"uninstallEvents",value:function(){this._core.room.off("joined",this.handleRoomJoined,this),this._core.room.off("left",this.handleRoomLeft,this)}},{key:"handleRoomJoined",value:(e=r(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] handleJoined: ".concat(JSON.stringify(this._config))),!this.isStarted||!this._config){t.next=4;break}return t.next=4,this.startMixTranscode(this._config);case 4:case"end":return t.stop()}}),t,this)}))),function(){return e.apply(this,arguments)})},{key:"handleRoomLeft",value:(t=r(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this._log.info("[CDNStreaming] handleRoomLeft: ".concat(JSON.stringify(this._config))),this._data=null;case 2:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"getOutputSessionId",value:function(t){var e=t.config,r=t.userId,n=t.roomId;return this._core.utils.isString(e.target.streamId)&&e.target.streamId.length>0?e.target.streamId:(0,P.default)("".concat(n,"_").concat(r,"_main"))}},{key:"getStringRoomId",value:function(t,e){return t?String(t):e}},{key:"getInputParam",value:function(t){var e=this,r=t.mix,n=void 0===r?{}:r,o=n.audioMixUserList,i=void 0===o?[]:o,a=n.videoLayoutList,s=(void 0===a?[]:a).map((function(t){return{userId:t.fixedVideoUser.userId,roomId:e.getStringRoomId(t.fixedVideoUser.roomId,t.fixedVideoUser.strRoomId)||e._core.room.roomId,width:t.width||0,height:t.height||0,locationX:t.locationX||0,locationY:t.locationY||0,zOrder:t.zOrder||1,streamType:"sub"===t.fixedVideoStreamType?1:0,inputType:M,renderMode:t.fillMode||0}}));return i.forEach((function(t){var r=e._core.room.roomId;(t.roomId||t.strRoomId)&&(r=e.getStringRoomId(t.roomId,t.strRoomId));var n=s.findIndex((function(e){return e.userId===t.userId&&e.roomId===r}));-1!==n?s[n].inputType=k:s.push({userId:t.userId,roomId:t.roomId||t.strRoomId||e._core.room.roomId,inputType:T})})),s}},{key:"getOutputParam",value:function(t){var e=t.target.streamId||"",r=t.encoding,n=void 0===r?{}:r,o=t.mix,i=void 0===o?{}:o;return{streamId:e,streamType:e.length>0?1:0,width:this._core.utils.isUndefined(n.videoWidth)?640:n.videoWidth,height:this._core.utils.isUndefined(n.videoHeight)?480:n.videoHeight,videoBps:n.videoBitrate||0,fps:n.videoFramerate||15,gop:n.videoGOP||2,audioSampleRate:n.audioSampleRate||48e3,audioBps:n.audioBitrate||64,audioChannels:n.audioChannels||1,backgroundColor:i.backgroundColor||0,backgroundImg:i.backgroundImage||"",extraInfo:"",videoCodec:2,audioCodec:0}}}]);var t,e,i,s,u,c}(),O=D(w()),L=function(){return o((function t(e,r){n(this,t),N(this,"_room"),N(this,"_core"),N(this,"_log"),N(this,"_paramsForTencentCDN"),N(this,"_initParamsForTencentCDN",{isPublished:!1,isStarted:!1}),this._core=e,this._room=e.room,this._log=r,this._paramsForTencentCDN=new Map([["publish-main-stream-to-cdn",this._initParamsForTencentCDN],["publish-sub-stream-to-cdn",this._initParamsForTencentCDN]])}),[{key:"installEvents",value:function(){this._core.innerEmitter.on("104",this.handlePublished,this),this._core.room.on("left",this.handleRoomLeft,this)}},{key:"uninstallEvents",value:function(){this._core.innerEmitter.off("104",this.handlePublished,this),this._core.room.off("left",this.handleRoomLeft,this)}},{key:"handlePublished",value:(c=r(a().mark((function t(e){var r,n,o,i;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((r=e.track).room===this._room){t.next=3;break}return t.abrupt("return");case 3:if(this._log.info("[CDNStreaming] handlePublished: mediaType ".concat(r.mediaType,", roomID ").concat(null==(n=null==r?void 0:r.room)?void 0:n.roomId)),1!==r.mediaType){t.next=6;break}return t.abrupt("return");case 6:if(o=4===r.mediaType?"publish-main-stream-to-cdn":"publish-sub-stream-to-cdn",!(null==(i=this._paramsForTencentCDN.get(o)||null)?void 0:i.target)||!i.isStarted){t.next=11;break}return t.next=11,this.startPublishTencentCDN(i.target);case 11:case"end":return t.stop()}}),t,this)}))),function(t){return c.apply(this,arguments)})},{key:"handleRoomLeft",value:(u=r(a().mark((function t(){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this._log.info("[CDNStreaming] handleRoomLeft"),this.changeDataStatus("publish-main-stream-to-cdn",{isPublished:!1}),this.changeDataStatus("publish-sub-stream-to-cdn",{isPublished:!1});case 3:case"end":return t.stop()}}),t,this)}))),function(){return u.apply(this,arguments)})},{key:"isStreamPublished",value:function(t){return"publish-main-stream-to-cdn"!==t||this._room.isMainStreamPublished?!("publish-sub-stream-to-cdn"===t&&!this._room.isAuxStreamPublished)||(this._log.info("[CDNStreaming] Sub has not already published, will auto reStart after published."),!1):(this._log.info("[CDNStreaming] Main stream has not already published, will auto reStart after published."),!1)}},{key:"changeDataStatus",value:function(t,e){var r=this._paramsForTencentCDN.get(t),n=x(x({},r),e);this._paramsForTencentCDN.set(t,n)}},{key:"startPublishTencentCDN",value:(s=r(a().mark((function t(e){var r,n,o,i,s,u,c,h,d;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] startPublishTencentCDN ".concat(JSON.stringify(e))),this.installEvents(),this.changeDataStatus(e.publishMode,{target:e,isStarted:!0}),this.isStreamPublished(e.publishMode)){t.next=5;break}return t.abrupt("return");case 5:return r=e.streamId||"",n=this.generatePublishCDNStreamId(r,e.publishMode),o=this.generatePublishCDNSessionId(e.publishMode),i="publish-sub-stream-to-cdn"===e.publishMode?1:0,s={requestTime:Date.now(),sessionId:o,streamId:n,streamType:i},t.next=12,this.doStartPublishTencentCDN(s,e.publishMode);case 12:if(u=e.appId,c=e.bizId,h=e.url,!(u&&c&&h)){t.next=18;break}return(null==(d=this._paramsForTencentCDN.get(e.publishMode)||this._initParamsForTencentCDN)?void 0:d.givenCDNManager)||(d.givenCDNManager=new E(this._core,this._log),this._paramsForTencentCDN.set(e.publishMode,d)),t.next=18,d.givenCDNManager.startPublishGivenCDN({publishMode:e.publishMode,appId:u,bizId:c,url:h,streamId:n});case 18:case"end":return t.stop()}}),t,this)}))),function(t){return s.apply(this,arguments)})},{key:"doStartPublishTencentCDN",value:(i=r(a().mark((function t(e,r){var n,o,i,s,u,c;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this._log.info("[CDNStreaming] doStartPublishTencentCDN: ".concat(JSON.stringify(e))),n=6,o=500,i=0;case 4:return t.next=7,this._room.sendStartPublishCDN(e,!0);case 7:if(s=t.sent,u=s.data.code,c=s.data.message,0!==u){t.next=16;break}return this._log.info("[CDNStreaming] server success: start tencent cdn"),this.changeDataStatus(r,{isPublished:!0}),t.abrupt("break",29);case 16:if(!(-10006===u&&i<n)){t.next=23;break}return this._log.warn("[CDNStreaming] doStartPublishTencentCDN: retry...".concat(i+1,"/6, reason: ").concat(c)),i+=1,t.next=21,new Promise((function(t){return setTimeout(t,o)}));case 21:t.next=27;break;case 23:throw this.changeDataStatus(r,{isPublished:!1}),-102083===u&&(c="Please enable relayed-push in ".concat(this._core.constants.CLOUD_CONSOLE_URL," and try later, refer to ").concat(this._core.constants.DOC_URL,"tutorial-26-advanced-publish-cdn-stream.html")),this._log.error("[CDNStreaming] server failed: start tencent cdn errCode: ".concat(u," errMsg: ").concat(c)),new Error("[CDNStreaming] server failed: start tencent cdn errCode: ".concat(u," errMsg: ").concat(c));case 27:t.next=4;break;case 29:case"end":return t.stop()}}),t,this)}))),function(t,e){return i.apply(this,arguments)})},{key:"stopPublishTencentCDN",value:(e=r(a().mark((function t(e){var r,n;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._log.info("[CDNStreaming] doStartPublishTencentCDN: ".concat(JSON.stringify(e))),!(r=this._paramsForTencentCDN.get(e)||this._initParamsForTencentCDN).isPublished){t.next=9;break}if(!((null==(n=r.target)?void 0:n.bizId)&&n.appId&&n.url&&(null==r?void 0:r.givenCDNManager))){t.next=7;break}return t.next=7,null==r?void 0:r.givenCDNManager.stopPublishGivenCDN();case 7:return t.next=9,this.doStopPublishTencentCDN(e);case 9:this._paramsForTencentCDN.set(e,this._initParamsForTencentCDN);case 10:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"doStopPublishTencentCDN",value:(t=r(a().mark((function t(e){var r,n,o,i;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r={requestTime:Date.now(),sessionId:(0,O.default)("".concat(this._room.roomId,"_").concat(this._room.userId,"_").concat(this.convertPublishModeToStreamType(e)))},this._log.info("[CDNStreaming] doStopPublishTencentCDN: ".concat(JSON.stringify(r))),t.next=4,this._room.sendStopPublishCDN(r,!0);case 4:if(n=t.sent,o=n.data.code,i=n.data.message,0!==o){t.next=13;break}this._log.info("[CDNStreaming] server success: stop tencent cdn"),this._paramsForTencentCDN.set(e,this._initParamsForTencentCDN),this.reset(e),t.next=16;break;case 13:throw-102069===o&&(this._paramsForTencentCDN.set(e,this._initParamsForTencentCDN),i="can not stop in auto relayed-push mode ".concat(i)),this._log.error("[CDNStreaming] server failed: stop tencent cdn errCode: ".concat(o," errMsg: ").concat(i)),new Error("[CDNStreaming] server failed: stop tencent cdn errCode: ".concat(o," errMsg: ").concat(i));case 16:case"end":return t.stop()}}),t,this)}))),function(e){return t.apply(this,arguments)})},{key:"reset",value:function(t){this.uninstallEvents(),this._paramsForTencentCDN.set(t,this._initParamsForTencentCDN)}},{key:"generatePublishCDNStreamId",value:function(t,e){if(""===t){var r="".concat(this._room.roomId,"_").concat(this._room.userId,"_").concat(this.convertPublishModeToStreamType(e));return/^[A-Za-z\d_-]*$/.test(r)||(r=(0,O.default)(r)),"".concat(this._room.sdkAppId,"_").concat(r)}return t}},{key:"convertPublishModeToStreamType",value:function(t){return"publish-main-stream-to-cdn"===t?"main":"aux"}},{key:"generatePublishCDNSessionId",value:function(t){return(0,O.default)("".concat(this._room.roomId,"_").concat(this._room.userId,"_").concat(this.convertPublishModeToStreamType(t)))}}]);var t,e,i,s,u,c}(),A=D(w()),j=function(){return o((function t(e,r){n(this,t),N(this,"_core"),N(this,"_room"),N(this,"_log"),N(this,"_seq"),N(this,"_taskId",null),N(this,"_startData",null),N(this,"_updateData",null),N(this,"_stopData",null),this._core=e,this._room=e.room,this._log=r,this._seq=0}),[{key:"startPushStreamToRoom",value:(i=r(a().mark((function t(e){var r,n,o,i,s,u;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._seq+=1,this._startData=x({roomid:String(this._room.roomId),roomType:this._room.useStringRoomId?1:0,sessionId:(0,A.default)("".concat(this._room.roomId,"_").concat(this._room.userId,"_start")),agentParam:{cdnRobotUserid:"mcu_robot_".concat(this._room.roomId,"_").concat(this._room.userId)}},this.getCommonParams(e)),this._log.info("startPushStreamToRoom: ".concat(JSON.stringify(this._startData))),t.next=5,this._room.sendStartPushStreamToRoom(this._startData);case 5:o=t.sent,i=o.data,s=i.code,u=i.message,0===s?(this._taskId=null==(n=null==(r=o.data)?void 0:r.data)?void 0:n.taskId,this._taskId||this.reportServerError("startPushStreamToRoom",s,"can't resolve task id: ".concat(JSON.stringify(o.data))),this._log.info("[CDNStreaming] server success: taskId",this._taskId)):this.reportServerError("startPushStreamToRoom",s,u);case 8:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"updatePushStreamToRoom",value:(e=r(a().mark((function t(e){var r,n,o,i;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._seq+=1,this._updateData=C(x({taskid:this._taskId},this.getCommonParams(e)),{enableNtpSync:!0}),this._log.info("updatePushStreamToRoom: ".concat(JSON.stringify(this._updateData))),t.next=5,this._room.sendUpdatePushStreamToRoom(this._updateData);case 5:r=t.sent,n=r.data,o=n.code,i=n.message,0===o?this._log.info("server success: updatePushStreamToRoom"):this.reportServerError("updatePushStreamToRoom",o,i);case 8:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"stopPushStreamToRoom",value:(t=r(a().mark((function t(){var e,r,n,o;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return this._seq+=1,this._stopData={sdkappid:this._room.sdkAppId,taskid:this._taskId},this._log.info("stopPushStreamToRoom: ".concat(JSON.stringify(this._stopData))),t.next=5,this._room.sendStopPushStreamToRoom(this._stopData);case 5:e=t.sent,r=e.data,n=r.code,o=r.message,0===n?this._log.info("server success: start mix"):this.reportServerError("stopPushStreamToRoom",n,o);case 8:case"end":return t.stop()}}),t,this)}))),function(){return t.apply(this,arguments)})},{key:"reportServerError",value:function(t,e,r){var n="server failed: ".concat(t," errCode: ").concat(e," errMsg: ").concat(r);throw this._log.error(n),new Error(n)}},{key:"getPushRtcRoomParams",value:function(t){var e=t.target.robotUser;return e?[{roomid:String((null==e?void 0:e.roomId)||(null==e?void 0:e.strRoomId))||this._room.roomId,roomType:(null==e?void 0:e.roomId)?0:1,pushRobotUserid:null==e?void 0:e.userId}]:[]}},{key:"getCommonParams",value:function(t){return{sdkappid:this._room.sdkAppId,transcode:!0,audioParam:this.getAudioParam(t),videoParam:this.getVideoParam(t),pushRtcRoomParams:this.getPushRtcRoomParams(t),sequenceNumber:this._seq}}},{key:"getAudioParam",value:function(t){var e=this,r=t.mix,n=void 0===r?{}:r,o=t.encoding,i=void 0===o?{}:o,a={audioSamplerate:i.audioSampleRate||48e3,audioBitrateKbps:i.audioBitrate||64,audioChannels:i.audioChannels||1},s=n.audioMixUserList;return{audioEncodeParam:a,mixAudioUsers:(null==s?void 0:s.map((function(t){return{roomid:String(t.roomId||t.strRoomId)||e._room.roomId,userid:t.userId,roomType:t.roomId?0:1}})))||[]}}},{key:"getVideoParam",value:function(t){var e=this,r=t.mix,n=void 0===r?{}:r,o=t.encoding,i=void 0===o?{}:o,a={videoCodec:2,videoWidth:i.videoWidth||640,videoHeight:i.videoHeight||480,videoFramerate:i.videoFramerate||15,videoGop:i.videoGOP||2,videoBitrateKbps:i.videoBitrate||800},s=n.videoLayoutList;return{videoEncodeParam:a,layoutParams:(null==s?void 0:s.map((function(t){return{userMediaStream:{userInfo:{roomid:String(t.fixedVideoUser.roomId||t.fixedVideoUser.strRoomId)||e._room.roomId,userid:t.fixedVideoUser.userId,roomType:t.fixedVideoUser.roomId?0:1},streamType:"sub"===t.fixedVideoStreamType?1:0},imageWidth:t.width||0,imageHeight:t.height||0,locationX:t.locationX||0,locationY:t.locationY||0,imageZorder:t.zOrder||1,renderMode:t.fillMode||0}})))||[],backgroundColor:n.backgroundColor||0,backgroundImageUrl:n.backgroundImage||""}}}]);var t,e,i}();var G={type:"number",notLessThanZero:!0},U={type:"object",properties:{userId:{required:!0,type:"string"},roomId:{type:["string","number"],validate:function(t,e,r,n){var o=d,i=o.RtcError,a=o.ErrorCode,s=o.ErrorCodeDictionary;if("string"==typeof t)throw new i({code:a.INVALID_PARAMETER,extraCode:s.INVALID_ROOM_ID_INTEGER_STRING,fnName:r,messageParams:{key:"roomId"}});if(void 0!==t&&!(/^[1-9]\d*$/.test(String(t))&&t<4294967295))throw new i({code:a.INVALID_PARAMETER,extraCode:s.INVALID_ROOM_ID_INTEGER,fnName:r,messageParams:{key:"roomId"}})}},strRoomId:{type:"string",validate:function(t,e,r,n){var o=d,i=o.RtcError,a=o.ErrorCode,s=o.ErrorCodeDictionary;if(!/^[A-Za-z\d\s!#$%&()+\-:;<=.>?@[\]^_{}|~,]{1,64}$/.test(t))throw new i({code:a.INVALID_PARAMETER,extraCode:s.INVALID_ROOM_ID_STRING,fnName:r,messageParams:{key:"strRoomId"}})}}}},V={required:!0,properties:{publishMode:{required:!0,values:["publish-main-stream-to-cdn","publish-mix-stream-to-cdn","publish-sub-stream-to-cdn","publish-mix-stream-to-room"]},streamId:{required:!1,type:"string",validate:function(t,e,r,n){if(!/^[A-Za-z\d_-]*$/.test(t)){var o=d,i=o.RtcError,a=o.ErrorCode,s=o.ErrorCodeDictionary;throw new i({code:a.INVALID_PARAMETER,extraCode:s.INVALID_STREAM_ID,messageParams:{key:"streamId"}})}}},appId:{type:"number",allowEmpty:!1},bizId:{type:"number",allowEmpty:!1},url:{type:"string",allowEmpty:!1},robotUser:x({},U)},validate:function(t,e,r,n){var o=t.publishMode,i=t.robotUser;if("publish-mix-stream-to-room"===o&&!i){var a=d;throw new(0,a.RtcError)({code:a.ErrorCode.INVALID_PARAMETER,message:"Invalid parameter target, the value of publishMode is PublishMixStreamToRoom, robotUser is required."})}}},F={required:!1,type:"object",properties:{videoWidth:G,videoHeight:G,videoBitrate:C(x({},G),{allowEmpty:!1}),videoFramerate:{type:"number",validate:function(t,e,r,n){if(t<=0||t>30){var o=d;throw new(0,o.RtcError)({code:o.ErrorCode.INVALID_PARAMETER,message:"Invalid parameter mixConfig -> videoFramerate, the value must be between (0, 30]."})}}},videoGOP:{type:"number",validate:function(t,e,r,n){if(t<1||t>8){var o=d;throw new(0,o.RtcError)({code:o.ErrorCode.INVALID_PARAMETER,message:"Invalid parameter mixConfig -> videoGOP, the value must be between [1, 8]."})}}},audioSampleRate:G,audioBitrate:{type:"number",validate:function(t,e,r,n){if(t<32||t>192){var o=d;throw new(0,o.RtcError)({code:o.ErrorCode.INVALID_PARAMETER,message:"Invalid parameter mixConfig -> audioBitrate, the value must be between [32, 192]."})}}},audioChannels:{type:"number",values:[1,2]}}},q={required:!1,type:"object",properties:{backgroundColor:{type:"number"},backgroundImage:{type:"string"},audioMixUserList:{type:"array",arrayItem:x({},U)},videoLayoutList:{type:"array",required:!0,arrayItem:{type:"object",properties:{fixedVideoUser:x({},U),fixedVideoStreamType:{type:"string",required:!0,values:["main","sub"]},fillMode:{type:"number",values:[0,1,2,4]},zOrder:{type:"number",required:!0,validate:function(t,e,r,n){if(t<1||t>15){var o=d;throw new(0,o.RtcError)({code:o.ErrorCode.INVALID_PARAMETER,message:"Invalid parameter mix -> videoLayoutList -> zOrder, the value must be between [1, 15]."})}}},width:G,height:G,locationX:G,locationY:G}}}}};var J=0,z=function(){function t(e){n(this,t),this.core=e,N(this,"_mixTranscodeManager"),N(this,"_publishCDNManager"),N(this,"_pushStreamToRoomManager"),N(this,"_core"),N(this,"_modeOptions"),N(this,"seq"),N(this,"_log"),J+=1,this.seq=J,this._log=e.log.createChild({id:"".concat(this.getAlias()).concat(J)}),this._log.info("[CDNStreaming] created id=".concat(this.getAlias()).concat(J)),this._core=e,this._modeOptions=new Map,this._mixTranscodeManager=new R(e,this._log),this._publishCDNManager=new L(e,this._log),this._pushStreamToRoomManager=new j(e,this._log)}return o(t,[{key:"getName",value:function(){return t.Name}},{key:"getAlias",value:function(){return"cdn"}},{key:"getValidateRule",value:function(t){switch(t){case"start":return e=this._core,d=e.errorModule,{name:"CDNStreamingOptions",type:"object",required:!0,allowEmpty:!1,properties:{target:x({},V),encoding:x({},F),mix:x({},q)},validate:function(t,r,n,o){var i,a,s=t.target.publishMode,u=t.encoding,c=t.mix;if("publish-mix-stream-to-cdn"===s){var h=e.errorModule,d=h.RtcError,l=h.ErrorCode,m=h.ErrorCodeDictionary;if(!u)throw new d({code:l.INVALID_PARAMETER,extraCode:m.INVALID_PARAMETER_REQUIRED,fnName:n,messageParams:{key:"encoding"}});if(!c)throw new d({code:l.INVALID_PARAMETER,extraCode:m.INVALID_PARAMETER_REQUIRED,fnName:n,messageParams:{key:"mix"}});if(c&&c.videoLayoutList){var p=0,f=0,g=[];if(c.videoLayoutList.forEach((function(t,e){g.push(t.fixedVideoUser.userId),t.width+t.locationX>p&&(p=t.width+t.locationX),t.height+t.locationY>f&&(f=t.height+t.locationY)})),g.indexOf(e.room.userId)<0)throw new d({code:l.INVALID_PARAMETER,message:"Invalid parameter mix -> videoLayoutList, the value must be include self."});var v=null!=(i=null==u?void 0:u.videoWidth)?i:640,b=null!=(a=null==u?void 0:u.videoHeight)?a:480;if(v<p||b<f)throw new d({code:l.INVALID_PARAMETER,message:"Invalid parameter encoding, the width and height of the mixed video must encompass all the mixed-in video streams."})}}}};case"update":return function(t){return d=t.errorModule,{name:"CDNStreamingOptions",type:"object",required:!0,allowEmpty:!1,properties:{target:x({},V),encoding:x({},F),mix:x({},q)}}}(this._core);case"stop":return function(t){return d=t.errorModule,{name:"CDNStreamingOptions",type:"object",required:!0,allowEmpty:!1,properties:{target:{required:!0,properties:{publishMode:{required:!0,values:["publish-main-stream-to-cdn","publish-mix-stream-to-cdn","publish-sub-stream-to-cdn","publish-mix-stream-to-room"]}}}}}}(this._core)}var e}},{key:"getGroup",value:function(t){return t.target.publishMode}},{key:"start",value:(u=r(a().mark((function t(e){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(this._modeOptions.set(e.target.publishMode,e),"publish-mix-stream-to-room"!==e.target.publishMode){t.next=3;break}return t.abrupt("return",this._pushStreamToRoomManager.startPushStreamToRoom(e));case 3:return t.next=5,this.doStart(e);case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}}),t,this)}))),function(t){return u.apply(this,arguments)})},{key:"update",value:(s=r(a().mark((function t(e){var r;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this._modeOptions.get(e.target.publishMode),this._core.utils.deepMerge(r,e),"publish-mix-stream-to-room"!==e.target.publishMode){t.next=4;break}return t.abrupt("return",this._pushStreamToRoomManager.updatePushStreamToRoom(r));case 4:return t.next=6,this.doStart(r);case 6:return t.abrupt("return",t.sent);case 7:case"end":return t.stop()}}),t,this)}))),function(t){return s.apply(this,arguments)})},{key:"stop",value:(i=r(a().mark((function t(e){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:t.t0=e.target.publishMode,t.next="publish-mix-stream-to-cdn"===t.t0?3:"publish-main-stream-to-cdn"===t.t0||"publish-sub-stream-to-cdn"===t.t0?6:"publish-mix-stream-to-room"===t.t0?9:11;break;case 3:return t.next=5,this._mixTranscodeManager.stopMixTranscode();case 5:return t.abrupt("break",11);case 6:return t.next=8,this._publishCDNManager.stopPublishTencentCDN(e.target.publishMode);case 8:return t.abrupt("break",11);case 9:return this._pushStreamToRoomManager.stopPushStreamToRoom(),t.abrupt("break",11);case 11:this._modeOptions.delete(e.target.publishMode);case 12:case"end":return t.stop()}}),t,this)}))),function(t){return i.apply(this,arguments)})},{key:"doStart",value:(e=r(a().mark((function t(e){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this._log.info("[CDNStreaming] doStart: ".concat(JSON.stringify(e))),t.t0=e.target.publishMode,t.next="publish-mix-stream-to-cdn"===t.t0?4:"publish-main-stream-to-cdn"===t.t0||"publish-sub-stream-to-cdn"===t.t0?7:10;break;case 4:return t.next=6,this._mixTranscodeManager.startMixTranscode(e);case 6:case 9:return t.abrupt("break",10);case 7:return t.next=9,this._publishCDNManager.startPublishTencentCDN(e.target);case 10:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})}]);var e,i,s,u}();return N(z,"TYPE",{PublishMode:{PublishMainStreamToCDN:"publish-main-stream-to-cdn",PublishSubStreamToCDN:"publish-sub-stream-to-cdn",PublishMixStreamToCDN:"publish-mix-stream-to-cdn",PublishMixStreamToRoom:"publish-mix-stream-to-room"}}),N(z,"Name","CDNStreaming"),z}));
