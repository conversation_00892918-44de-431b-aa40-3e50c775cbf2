var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,r)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,__spreadValues=(e,t)=>{for(var r in t||(t={}))__hasOwnProp.call(t,r)&&__defNormalProp(e,r,t[r]);if(__getOwnPropSymbols)for(var r of __getOwnPropSymbols(t))__propIsEnum.call(t,r)&&__defNormalProp(e,r,t[r]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__decorateClass=(e,t,r,o)=>{for(var s,a=o>1?void 0:o?__getOwnPropDesc(t,r):t,i=e.length-1;i>=0;i--)(s=e[i])&&(a=(o?s(t,r,a):s(a))||a);return o&&a&&__defProp(t,r,a),a},__publicField=(e,t,r)=>__defNormalProp(e,"symbol"!=typeof t?t+"":t,r),roomIValidate={properties:{roomId:{type:"number"},strRoomId:{type:"string"}}},StartValidateRule={name:"option",required:!0,properties:__spreadProps(__spreadValues({},roomIValidate.properties),{userId:{type:"string"}})},UpdateValidateRule={name:"option",required:!0,properties:{updateList:{type:"array",required:!0,arrayItem:{required:!0,type:"object",properties:__spreadProps(__spreadValues({},StartValidateRule.properties),{userId:{required:!1,type:"string"},muteAudio:{type:"boolean"},muteVideo:{type:"boolean"},muteSubStream:{type:"boolean"}})}}}},StopValidateRule={name:"option",properties:__spreadValues({},roomIValidate.properties)},isFunction=e=>"function"==typeof e,RETRY_STATE_NOT_START=0,RETRY_STATE_STARTED=1,RETRY_STATE_STOPPED=2;function promiseRetry({retryFunction:e,settings:t,onError:r,onRetrying:o,onRetryFailed:s,onRetrySuccess:a,context:i}){return function(...n){const{retries:d=5,timeout:p=1e3}=t;let c=0,l=-1,u=RETRY_STATE_NOT_START;const _=async(t,m)=>{const R=i||this;try{const r=await e.apply(R,n);c>0&&a&&a.call(this,c),c=0,t(r)}catch(e){const a=()=>{clearTimeout(l),c=0,u=RETRY_STATE_STOPPED,m(e)},i=()=>{u!==RETRY_STATE_STOPPED&&c<(isFunction(d)?d():d)?(c++,u=RETRY_STATE_STARTED,isFunction(o)&&o.call(this,c,a),l=window.setTimeout((()=>{l=-1,_(t,m)}),isFunction(p)?p(c):p)):(a(),isFunction(s)&&s.call(this,e))};isFunction(r)?r.call(this,{error:e,retry:i,reject:m,retryFuncArgs:n,retriedCount:c}):i()}};return new Promise(_)}}var retry_default=promiseRetry,retryingMap=new WeakMap;function addPromiseRetry({settings:e={retries:5,timeout:2e3},onError:t,onRetrying:r,onRetryFailed:o}){return function(s,a,i){const n=retry_default({retryFunction:i.value,settings:e,onError({error:e,retry:r,reject:o,retryFuncArgs:i}){var n;t?t.call(this,e,(()=>{var t;(null==(t=retryingMap.get(s))?void 0:t.has(a))?r():o(e)}),o,i):(null==(n=retryingMap.get(s))?void 0:n.has(a))?r():o(e)},onRetrying(e,t){var o;isFunction(r)&&r.call(this,e,t),(null==(o=retryingMap.get(s))?void 0:o.has(a))&&(retryingMap.get(s).get(a).stopRetry=t)},onRetryFailed:o});return i.value=function(...e){const t=retryingMap.get(s);return t?t.set(a,{args:e}):retryingMap.set(s,new Map([[a,{args:e}]])),n.apply(this,e).finally((()=>{var e;return null==(e=retryingMap.get(s))?void 0:e.delete(a)}))},i}}var _CrossRoom=class e{constructor(e){this.core=e,__publicField(this,"disableRandomCall",!0),__publicField(this,"connectedRoomIdSet",new Set),__publicField(this,"updateSeq",0),__publicField(this,"_log"),this._log=this.core.log.createChild({id:`${this.getAlias()}`})}getName(){return e.Name}getAlias(){return"crs-r"}getGroup(e){var t;const r=(null==e?void 0:e.userId)||(null==(t=null==e?void 0:e.updateList)?void 0:t[0].userId)||"";return r||(e?e.updateList?String(e.updateList[0].roomId)||e.updateList[0].strRoomId||"":String(e.roomId)||e.strRoomId||"":"*")}getValidateRule(e){switch(e){case"start":return StartValidateRule;case"update":return UpdateValidateRule;case"stop":return StopValidateRule}}async start({roomId:e,strRoomId:t,userId:r}){const{RtcError:o,ErrorCode:s}=this.core.errorModule;if(!this.core.room.sendSignalMessage)throw new o({code:s.ENV_NOT_SUPPORTED});const a=e||t,i=await this.core.room.sendSignalMessage({command:"connect_other_room",responseCommand:String(8209),data:{roomId:a,userId:r,localRoomId:r?void 0:this.core.room.roomId},retries:3});if(0!==i.data.code)throw new o({code:s.SERVER_ERROR,extraCode:i.data.code,message:i.data.message});r||this.connectedRoomIdSet.add(a)}async update({updateList:e}){var t;const{RtcError:r,ErrorCode:o}=this.core.errorModule;if(!this.core.room.sendSignalMessage)throw new r({code:o.ENV_NOT_SUPPORTED});const s=e.find((e=>e.userId))?0:1,a=await this.core.room.sendSignalMessage({command:"update_other_room_forward_mode",responseCommand:String(8213),data:{seq:++this.updateSeq,operationType:s,updateList:e.map((({roomId:e,strRoomId:t,userId:r,muteAudio:o,muteVideo:s,muteSubStream:a})=>({roomId:e||t,userId:r,muteAudio:o,muteVideo:s,muteSubStream:a})))},retries:3});if(a.data.data.expectSeq)return this.updateSeq=a.data.data.expectSeq,this.update({updateList:e});if(0!==a.data.code)throw new r({code:o.SERVER_ERROR,extraCode:a.data.code,message:a.data.message});if((null==(t=a.data.data.errorList)?void 0:t.length)>0)throw new r({code:o.UNKNOWN_ERROR,message:a.data.data.errorList[0].message})}async stop({roomId:e,strRoomId:t}={}){const r=e||t;if(r)await this.doStop(r);else if(this.connectedRoomIdSet.size>0)for(const e of[...this.connectedRoomIdSet.values()])await this.doStop(e);else await this.doStop()}async doStop(e){const{RtcError:t,ErrorCode:r}=this.core.errorModule;if(!this.core.room.sendSignalMessage)throw new t({code:r.ENV_NOT_SUPPORTED});const o=await this.core.room.sendSignalMessage({command:"disconnect_other_room",responseCommand:String(8211),data:{roomId:e,localRoomId:this.core.room.roomId},retries:3});if(0!==o.data.code)throw new t({code:r.SERVER_ERROR,extraCode:o.data.code,message:o.data.message});this.connectedRoomIdSet.delete(e)}destroy(){}};__publicField(_CrossRoom,"Name","CrossRoom"),__decorateClass([addPromiseRetry({settings:{retries:3,timeout:1e3},onRetrying(e){this._log.warn(`retry start: ${e}`)}})],_CrossRoom.prototype,"start",1),__decorateClass([addPromiseRetry({settings:{retries:3,timeout:1e3},onRetrying(e){this._log.warn(`retry update: ${e}`)}})],_CrossRoom.prototype,"update",1);var CrossRoom=_CrossRoom,index_default=CrossRoom;export{index_default as default};export{CrossRoom};