var __create=Object.create,__defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropNames=Object.getOwnPropertyNames,__getOwnPropSymbols=Object.getOwnPropertySymbols,__getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,V)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:V}):e[t]=V,__spreadValues=(e,t)=>{for(var V in t||(t={}))__hasOwnProp.call(t,V)&&__defNormalProp(e,V,t[V]);if(__getOwnPropSymbols)for(var V of __getOwnPropSymbols(t))__propIsEnum.call(t,V)&&__defNormalProp(e,V,t[V]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__commonJS=(e,t)=>function(){return t||(0,e[__getOwnPropNames(e)[0]])((t={exports:{}}).exports,t),t.exports},__copyProps=(e,t,V,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of __getOwnPropNames(t))__hasOwnProp.call(e,n)||n===V||__defProp(e,n,{get:()=>t[n],enumerable:!(i=__getOwnPropDesc(t,n))||i.enumerable});return e},__toESM=(e,t,V)=>(V=null!=e?__create(__getProtoOf(e)):{},__copyProps(!t&&e&&e.__esModule?V:__defProp(V,"default",{value:e,enumerable:!0}),e)),__publicField=(e,t,V)=>__defNormalProp(e,"symbol"!=typeof t?t+"":t,V),require_global_this=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/global-this.js"(e,t){"use strict";var V=function(e){return e&&e.Math===Math&&e};t.exports=V("object"==typeof globalThis&&globalThis)||V("object"==typeof window&&window)||V("object"==typeof self&&self)||V("object"==typeof global&&global)||V("object"==typeof e&&e)||function(){return this}()||Function("return this")()}}),require_fails=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/fails.js"(e,t){"use strict";t.exports=function(e){try{return!!e()}catch(e){return!0}}}}),require_descriptors=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/descriptors.js"(e,t){"use strict";var V=require_fails();t.exports=!V((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}}),require_function_bind_native=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/function-bind-native.js"(e,t){"use strict";var V=require_fails();t.exports=!V((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))}}),require_function_call=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/function-call.js"(e,t){"use strict";var V=require_function_bind_native(),i=Function.prototype.call;t.exports=V?i.bind(i):function(){return i.apply(i,arguments)}}}),require_object_property_is_enumerable=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-property-is-enumerable.js"(e){"use strict";var t={}.propertyIsEnumerable,V=Object.getOwnPropertyDescriptor,i=V&&!t.call({1:2},1);e.f=i?function(e){var t=V(this,e);return!!t&&t.enumerable}:t}}),require_create_property_descriptor=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/create-property-descriptor.js"(e,t){"use strict";t.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}}}),require_function_uncurry_this=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/function-uncurry-this.js"(e,t){"use strict";var V=require_function_bind_native(),i=Function.prototype,n=i.call,A=V&&i.bind.bind(n,n);t.exports=V?A:function(e){return function(){return n.apply(e,arguments)}}}}),require_classof_raw=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/classof-raw.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=V({}.toString),n=V("".slice);t.exports=function(e){return n(i(e),8,-1)}}}),require_indexed_object=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/indexed-object.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=require_fails(),n=require_classof_raw(),A=Object,o=V("".split);t.exports=i((function(){return!A("z").propertyIsEnumerable(0)}))?function(e){return"String"===n(e)?o(e,""):A(e)}:A}}),require_is_null_or_undefined=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-null-or-undefined.js"(e,t){"use strict";t.exports=function(e){return null==e}}}),require_require_object_coercible=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/require-object-coercible.js"(e,t){"use strict";var V=require_is_null_or_undefined(),i=TypeError;t.exports=function(e){if(V(e))throw new i("Can't call method on "+e);return e}}}),require_to_indexed_object=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-indexed-object.js"(e,t){"use strict";var V=require_indexed_object(),i=require_require_object_coercible();t.exports=function(e){return V(i(e))}}}),require_is_callable=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-callable.js"(e,t){"use strict";var V="object"==typeof document&&document.all;t.exports=void 0===V&&void 0!==V?function(e){return"function"==typeof e||e===V}:function(e){return"function"==typeof e}}}),require_is_object=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-object.js"(e,t){"use strict";var V=require_is_callable();t.exports=function(e){return"object"==typeof e?null!==e:V(e)}}}),require_get_built_in=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/get-built-in.js"(e,t){"use strict";var V=require_global_this(),i=require_is_callable();t.exports=function(e,t){return arguments.length<2?(n=V[e],i(n)?n:void 0):V[e]&&V[e][t];var n}}}),require_object_is_prototype_of=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-is-prototype-of.js"(e,t){"use strict";var V=require_function_uncurry_this();t.exports=V({}.isPrototypeOf)}}),require_environment_user_agent=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/environment-user-agent.js"(e,t){"use strict";var V=require_global_this().navigator,i=V&&V.userAgent;t.exports=i?String(i):""}}),require_environment_v8_version=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/environment-v8-version.js"(e,t){"use strict";var V,i,n=require_global_this(),A=require_environment_user_agent(),o=n.process,r=n.Deno,s=o&&o.versions||r&&r.version,q=s&&s.v8;q&&(i=(V=q.split("."))[0]>0&&V[0]<4?1:+(V[0]+V[1])),!i&&A&&(!(V=A.match(/Edge\/(\d+)/))||V[1]>=74)&&(V=A.match(/Chrome\/(\d+)/))&&(i=+V[1]),t.exports=i}}),require_symbol_constructor_detection=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/symbol-constructor-detection.js"(e,t){"use strict";var V=require_environment_v8_version(),i=require_fails(),n=require_global_this().String;t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!n(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&V&&V<41}))}}),require_use_symbol_as_uid=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/use-symbol-as-uid.js"(e,t){"use strict";var V=require_symbol_constructor_detection();t.exports=V&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}}),require_is_symbol=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-symbol.js"(e,t){"use strict";var V=require_get_built_in(),i=require_is_callable(),n=require_object_is_prototype_of(),A=require_use_symbol_as_uid(),o=Object;t.exports=A?function(e){return"symbol"==typeof e}:function(e){var t=V("Symbol");return i(t)&&n(t.prototype,o(e))}}}),require_try_to_string=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/try-to-string.js"(e,t){"use strict";var V=String;t.exports=function(e){try{return V(e)}catch(e){return"Object"}}}}),require_a_callable=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/a-callable.js"(e,t){"use strict";var V=require_is_callable(),i=require_try_to_string(),n=TypeError;t.exports=function(e){if(V(e))return e;throw new n(i(e)+" is not a function")}}}),require_get_method=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/get-method.js"(e,t){"use strict";var V=require_a_callable(),i=require_is_null_or_undefined();t.exports=function(e,t){var n=e[t];return i(n)?void 0:V(n)}}}),require_ordinary_to_primitive=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/ordinary-to-primitive.js"(e,t){"use strict";var V=require_function_call(),i=require_is_callable(),n=require_is_object(),A=TypeError;t.exports=function(e,t){var o,r;if("string"===t&&i(o=e.toString)&&!n(r=V(o,e)))return r;if(i(o=e.valueOf)&&!n(r=V(o,e)))return r;if("string"!==t&&i(o=e.toString)&&!n(r=V(o,e)))return r;throw new A("Can't convert object to primitive value")}}}),require_is_pure=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-pure.js"(e,t){"use strict";t.exports=!1}}),require_define_global_property=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/define-global-property.js"(e,t){"use strict";var V=require_global_this(),i=Object.defineProperty;t.exports=function(e,t){try{i(V,e,{value:t,configurable:!0,writable:!0})}catch(i){V[e]=t}return t}}}),require_shared_store=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/shared-store.js"(e,t){"use strict";var V=require_is_pure(),i=require_global_this(),n=require_define_global_property(),A="__core-js_shared__",o=t.exports=i[A]||n(A,{});(o.versions||(o.versions=[])).push({version:"3.40.0",mode:V?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.40.0/LICENSE",source:"https://github.com/zloirock/core-js"})}}),require_shared=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/shared.js"(e,t){"use strict";var V=require_shared_store();t.exports=function(e,t){return V[e]||(V[e]=t||{})}}}),require_to_object=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-object.js"(e,t){"use strict";var V=require_require_object_coercible(),i=Object;t.exports=function(e){return i(V(e))}}}),require_has_own_property=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/has-own-property.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=require_to_object(),n=V({}.hasOwnProperty);t.exports=Object.hasOwn||function(e,t){return n(i(e),t)}}}),require_uid=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/uid.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=0,n=Math.random(),A=V(1..toString);t.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+A(++i+n,36)}}}),require_well_known_symbol=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/well-known-symbol.js"(e,t){"use strict";var V=require_global_this(),i=require_shared(),n=require_has_own_property(),A=require_uid(),o=require_symbol_constructor_detection(),r=require_use_symbol_as_uid(),s=V.Symbol,q=i("wks"),a=r?s.for||s:s&&s.withoutSetter||A;t.exports=function(e){return n(q,e)||(q[e]=o&&n(s,e)?s[e]:a("Symbol."+e)),q[e]}}}),require_to_primitive=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-primitive.js"(e,t){"use strict";var V=require_function_call(),i=require_is_object(),n=require_is_symbol(),A=require_get_method(),o=require_ordinary_to_primitive(),r=require_well_known_symbol(),s=TypeError,q=r("toPrimitive");t.exports=function(e,t){if(!i(e)||n(e))return e;var r,a=A(e,q);if(a){if(void 0===t&&(t="default"),r=V(a,e,t),!i(r)||n(r))return r;throw new s("Can't convert object to primitive value")}return void 0===t&&(t="number"),o(e,t)}}}),require_to_property_key=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-property-key.js"(e,t){"use strict";var V=require_to_primitive(),i=require_is_symbol();t.exports=function(e){var t=V(e,"string");return i(t)?t:t+""}}}),require_document_create_element=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/document-create-element.js"(e,t){"use strict";var V=require_global_this(),i=require_is_object(),n=V.document,A=i(n)&&i(n.createElement);t.exports=function(e){return A?n.createElement(e):{}}}}),require_ie8_dom_define=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/ie8-dom-define.js"(e,t){"use strict";var V=require_descriptors(),i=require_fails(),n=require_document_create_element();t.exports=!V&&!i((function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a}))}}),require_object_get_own_property_descriptor=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-get-own-property-descriptor.js"(e){"use strict";var t=require_descriptors(),V=require_function_call(),i=require_object_property_is_enumerable(),n=require_create_property_descriptor(),A=require_to_indexed_object(),o=require_to_property_key(),r=require_has_own_property(),s=require_ie8_dom_define(),q=Object.getOwnPropertyDescriptor;e.f=t?q:function(e,t){if(e=A(e),t=o(t),s)try{return q(e,t)}catch(e){}if(r(e,t))return n(!V(i.f,e,t),e[t])}}}),require_v8_prototype_define_bug=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/v8-prototype-define-bug.js"(e,t){"use strict";var V=require_descriptors(),i=require_fails();t.exports=V&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))}}),require_an_object=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/an-object.js"(e,t){"use strict";var V=require_is_object(),i=String,n=TypeError;t.exports=function(e){if(V(e))return e;throw new n(i(e)+" is not an object")}}}),require_object_define_property=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-define-property.js"(e){"use strict";var t=require_descriptors(),V=require_ie8_dom_define(),i=require_v8_prototype_define_bug(),n=require_an_object(),A=require_to_property_key(),o=TypeError,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,q="enumerable",a="configurable",c="writable";e.f=t?i?function(e,t,V){if(n(e),t=A(t),n(V),"function"==typeof e&&"prototype"===t&&"value"in V&&c in V&&!V[c]){var i=s(e,t);i&&i[c]&&(e[t]=V.value,V={configurable:a in V?V[a]:i[a],enumerable:q in V?V[q]:i[q],writable:!1})}return r(e,t,V)}:r:function(e,t,i){if(n(e),t=A(t),n(i),V)try{return r(e,t,i)}catch(e){}if("get"in i||"set"in i)throw new o("Accessors not supported");return"value"in i&&(e[t]=i.value),e}}}),require_create_non_enumerable_property=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/create-non-enumerable-property.js"(e,t){"use strict";var V=require_descriptors(),i=require_object_define_property(),n=require_create_property_descriptor();t.exports=V?function(e,t,V){return i.f(e,t,n(1,V))}:function(e,t,V){return e[t]=V,e}}}),require_function_name=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/function-name.js"(e,t){"use strict";var V=require_descriptors(),i=require_has_own_property(),n=Function.prototype,A=V&&Object.getOwnPropertyDescriptor,o=i(n,"name"),r=o&&"something"===function(){}.name,s=o&&(!V||V&&A(n,"name").configurable);t.exports={EXISTS:o,PROPER:r,CONFIGURABLE:s}}}),require_inspect_source=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/inspect-source.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=require_is_callable(),n=require_shared_store(),A=V(Function.toString);i(n.inspectSource)||(n.inspectSource=function(e){return A(e)}),t.exports=n.inspectSource}}),require_weak_map_basic_detection=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/weak-map-basic-detection.js"(e,t){"use strict";var V=require_global_this(),i=require_is_callable(),n=V.WeakMap;t.exports=i(n)&&/native code/.test(String(n))}}),require_shared_key=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/shared-key.js"(e,t){"use strict";var V=require_shared(),i=require_uid(),n=V("keys");t.exports=function(e){return n[e]||(n[e]=i(e))}}}),require_hidden_keys=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/hidden-keys.js"(e,t){"use strict";t.exports={}}}),require_internal_state=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/internal-state.js"(e,t){"use strict";var V,i,n,A,o,r=require_weak_map_basic_detection(),s=require_global_this(),q=require_is_object(),a=require_create_non_enumerable_property(),c=require_has_own_property(),l=require_shared_store(),d=require_shared_key(),p=require_hidden_keys(),h="Object already initialized",u=s.TypeError,g=s.WeakMap;r||l.state?((A=l.state||(l.state=new g)).get=A.get,A.has=A.has,A.set=A.set,V=function(e,t){if(A.has(e))throw new u(h);return t.facade=e,A.set(e,t),t},i=function(e){return A.get(e)||{}},n=function(e){return A.has(e)}):(p[o=d("state")]=!0,V=function(e,t){if(c(e,o))throw new u(h);return t.facade=e,a(e,o,t),t},i=function(e){return c(e,o)?e[o]:{}},n=function(e){return c(e,o)}),t.exports={set:V,get:i,has:n,enforce:function(e){return n(e)?i(e):V(e,{})},getterFor:function(e){return function(t){var V;if(!q(t)||(V=i(t)).type!==e)throw new u("Incompatible receiver, "+e+" required");return V}}}}}),require_make_built_in=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/make-built-in.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=require_fails(),n=require_is_callable(),A=require_has_own_property(),o=require_descriptors(),r=require_function_name().CONFIGURABLE,s=require_inspect_source(),q=require_internal_state(),a=q.enforce,c=q.get,l=String,d=Object.defineProperty,p=V("".slice),h=V("".replace),u=V([].join),g=o&&!i((function(){return 8!==d((function(){}),"length",{value:8}).length})),m=String(String).split("String"),E=t.exports=function(e,t,V){"Symbol("===p(l(t),0,7)&&(t="["+h(l(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),V&&V.getter&&(t="get "+t),V&&V.setter&&(t="set "+t),(!A(e,"name")||r&&e.name!==t)&&(o?d(e,"name",{value:t,configurable:!0}):e.name=t),g&&V&&A(V,"arity")&&e.length!==V.arity&&d(e,"length",{value:V.arity});try{V&&A(V,"constructor")&&V.constructor?o&&d(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var i=a(e);return A(i,"source")||(i.source=u(m,"string"==typeof t?t:"")),e};Function.prototype.toString=E((function(){return n(this)&&c(this).source||s(this)}),"toString")}}),require_define_built_in=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/define-built-in.js"(e,t){"use strict";var V=require_is_callable(),i=require_object_define_property(),n=require_make_built_in(),A=require_define_global_property();t.exports=function(e,t,o,r){r||(r={});var s=r.enumerable,q=void 0!==r.name?r.name:t;if(V(o)&&n(o,q,r),r.global)s?e[t]=o:A(t,o);else{try{r.unsafe?e[t]&&(s=!0):delete e[t]}catch(e){}s?e[t]=o:i.f(e,t,{value:o,enumerable:!1,configurable:!r.nonConfigurable,writable:!r.nonWritable})}return e}}}),require_math_trunc=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/math-trunc.js"(e,t){"use strict";var V=Math.ceil,i=Math.floor;t.exports=Math.trunc||function(e){var t=+e;return(t>0?i:V)(t)}}}),require_to_integer_or_infinity=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-integer-or-infinity.js"(e,t){"use strict";var V=require_math_trunc();t.exports=function(e){var t=+e;return t!=t||0===t?0:V(t)}}}),require_to_absolute_index=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-absolute-index.js"(e,t){"use strict";var V=require_to_integer_or_infinity(),i=Math.max,n=Math.min;t.exports=function(e,t){var A=V(e);return A<0?i(A+t,0):n(A,t)}}}),require_to_length=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/to-length.js"(e,t){"use strict";var V=require_to_integer_or_infinity(),i=Math.min;t.exports=function(e){var t=V(e);return t>0?i(t,9007199254740991):0}}}),require_length_of_array_like=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/length-of-array-like.js"(e,t){"use strict";var V=require_to_length();t.exports=function(e){return V(e.length)}}}),require_array_includes=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/array-includes.js"(e,t){"use strict";var V=require_to_indexed_object(),i=require_to_absolute_index(),n=require_length_of_array_like(),A=function(e){return function(t,A,o){var r=V(t),s=n(r);if(0===s)return!e&&-1;var q,a=i(o,s);if(e&&A!=A){for(;s>a;)if((q=r[a++])!=q)return!0}else for(;s>a;a++)if((e||a in r)&&r[a]===A)return e||a||0;return!e&&-1}};t.exports={includes:A(!0),indexOf:A(!1)}}}),require_object_keys_internal=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-keys-internal.js"(e,t){"use strict";var V=require_function_uncurry_this(),i=require_has_own_property(),n=require_to_indexed_object(),A=require_array_includes().indexOf,o=require_hidden_keys(),r=V([].push);t.exports=function(e,t){var V,s=n(e),q=0,a=[];for(V in s)!i(o,V)&&i(s,V)&&r(a,V);for(;t.length>q;)i(s,V=t[q++])&&(~A(a,V)||r(a,V));return a}}}),require_enum_bug_keys=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/enum-bug-keys.js"(e,t){"use strict";t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]}}),require_object_get_own_property_names=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-get-own-property-names.js"(e){"use strict";var t=require_object_keys_internal(),V=require_enum_bug_keys().concat("length","prototype");e.f=Object.getOwnPropertyNames||function(e){return t(e,V)}}}),require_object_get_own_property_symbols=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/object-get-own-property-symbols.js"(e){"use strict";e.f=Object.getOwnPropertySymbols}}),require_own_keys=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/own-keys.js"(e,t){"use strict";var V=require_get_built_in(),i=require_function_uncurry_this(),n=require_object_get_own_property_names(),A=require_object_get_own_property_symbols(),o=require_an_object(),r=i([].concat);t.exports=V("Reflect","ownKeys")||function(e){var t=n.f(o(e)),V=A.f;return V?r(t,V(e)):t}}}),require_copy_constructor_properties=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/copy-constructor-properties.js"(e,t){"use strict";var V=require_has_own_property(),i=require_own_keys(),n=require_object_get_own_property_descriptor(),A=require_object_define_property();t.exports=function(e,t,o){for(var r=i(t),s=A.f,q=n.f,a=0;a<r.length;a++){var c=r[a];V(e,c)||o&&V(o,c)||s(e,c,q(t,c))}}}}),require_is_forced=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/is-forced.js"(e,t){"use strict";var V=require_fails(),i=require_is_callable(),n=/#|\.prototype\./,A=function(e,t){var n=r[o(e)];return n===q||n!==s&&(i(t)?V(t):!!t)},o=A.normalize=function(e){return String(e).replace(n,".").toLowerCase()},r=A.data={},s=A.NATIVE="N",q=A.POLYFILL="P";t.exports=A}}),require_export=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/internals/export.js"(e,t){"use strict";var V=require_global_this(),i=require_object_get_own_property_descriptor().f,n=require_create_non_enumerable_property(),A=require_define_built_in(),o=require_define_global_property(),r=require_copy_constructor_properties(),s=require_is_forced();t.exports=function(e,t){var q,a,c,l,d,p=e.target,h=e.global,u=e.stat;if(q=h?V:u?V[p]||o(p,{}):V[p]&&V[p].prototype)for(a in t){if(l=t[a],c=e.dontCallGetSet?(d=i(q,a))&&d.value:q[a],!s(h?a:p+(u?".":"#")+a,e.forced)&&void 0!==c){if(typeof l==typeof c)continue;r(l,c)}(e.sham||c&&c.sham)&&n(l,"sham",!0),A(q,a,l,e)}}}}),require_es_global_this=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/modules/es.global-this.js"(){"use strict";var e=require_export(),t=require_global_this();e({global:!0,forced:t.globalThis!==t},{globalThis:t})}}),require_global_this2=__commonJS({"../node_modules/.pnpm/core-js@3.40.0/node_modules/core-js/es/global-this.js"(e,t){"use strict";require_es_global_this(),t.exports=require_global_this()}}),import_global_this=__toESM(require_global_this2(),1);function startValidateRule(e){return{name:"DeviceDetectorOptions",type:"object",required:!1,allowEmpty:!1,properties:{networkDetect:{required:!1,type:"object",properties:{sdkAppId:{required:!0,type:"number"},userId:{required:!0,type:"string"},userSig:{required:!0,type:"string"},downlinkUserId:{required:!1,type:"string"},downlinkUserSig:{required:!1,type:"string"},roomId:{required:!1,type:"number"}},async validate(t,V,i,n){const{RtcError:A,ErrorCode:o,ErrorCodeDictionary:r}=e.errorModule;if(!t)return;let{sdkAppId:s,userId:q,userSig:a,downlinkUserId:c,downlinkUserSig:l,roomId:d}=t;d=d||8080;const p=e.TRTC.create(),h=e.TRTC.create();await p.enterRoom({roomId:d,sdkAppId:s,userId:q,userSig:a}),await p.exitRoom(),p.destroy(),c&&l&&(await h.enterRoom({roomId:d,sdkAppId:s,userId:c,userSig:l}),await h.exitRoom(),h.destroy())}},cameraDetect:{required:!1,type:"object",properties:{mirror:{required:!1,type:"boolean"}}}}}}function stopValidateRule(e){return{name:"StopDeviceDetectorOptions",required:!1}}var _a,_b,t=globalThis,e=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,s=Symbol(),o=new WeakMap,n=class{constructor(e,t,V){if(this._$cssResult$=!0,V!==s)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=e,this.t=t}get styleSheet(){let t=this.o;const V=this.t;if(e&&void 0===t){const e=void 0!==V&&1===V.length;e&&(t=o.get(V)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),e&&o.set(V,t))}return t}toString(){return this.cssText}},r=e=>new n("string"==typeof e?e:e+"",void 0,s),S=(V,i)=>{if(e)V.adoptedStyleSheets=i.map((e=>e instanceof CSSStyleSheet?e:e.styleSheet));else for(const e of i){const i=document.createElement("style"),n=t.litNonce;void 0!==n&&i.setAttribute("nonce",n),i.textContent=e.cssText,V.appendChild(i)}},c=e?e=>e:e=>e instanceof CSSStyleSheet?(e=>{let t="";for(const V of e.cssRules)t+=V.cssText;return r(t)})(e):e,{is:i2,defineProperty:e2,getOwnPropertyDescriptor:r2,getOwnPropertyNames:h,getOwnPropertySymbols:o2,getPrototypeOf:n2}=Object,a=globalThis,c2=a.trustedTypes,l=c2?c2.emptyScript:"",p=a.reactiveElementPolyfillSupport,d=(e,t)=>e,u={toAttribute(e,t){switch(t){case Boolean:e=e?l:null;break;case Object:case Array:e=null==e?e:JSON.stringify(e)}return e},fromAttribute(e,t){let V=e;switch(t){case Boolean:V=null!==e;break;case Number:V=null===e?null:Number(e);break;case Object:case Array:try{V=JSON.parse(e)}catch(e){V=null}}return V}},f=(e,t)=>!i2(e,t),y={attribute:!0,type:String,converter:u,reflect:!1,hasChanged:f};null!=(_a=Symbol.metadata)||(Symbol.metadata=Symbol("metadata")),null!=(_b=a.litPropertyMetadata)||(a.litPropertyMetadata=new WeakMap);var _a2,b=class extends HTMLElement{static addInitializer(e){var t;this._$Ei(),(null!=(t=this.l)?t:this.l=[]).push(e)}static get observedAttributes(){return this.finalize(),this._$Eh&&[...this._$Eh.keys()]}static createProperty(e,t=y){if(t.state&&(t.attribute=!1),this._$Ei(),this.elementProperties.set(e,t),!t.noAccessor){const V=Symbol(),i=this.getPropertyDescriptor(e,V,t);void 0!==i&&e2(this.prototype,e,i)}}static getPropertyDescriptor(e,t,V){var i;const{get:n,set:A}=null!=(i=r2(this.prototype,e))?i:{get(){return this[t]},set(e){this[t]=e}};return{get(){return null==n?void 0:n.call(this)},set(t){const i=null==n?void 0:n.call(this);A.call(this,t),this.requestUpdate(e,i,V)},configurable:!0,enumerable:!0}}static getPropertyOptions(e){var t;return null!=(t=this.elementProperties.get(e))?t:y}static _$Ei(){if(this.hasOwnProperty(d("elementProperties")))return;const e=n2(this);e.finalize(),void 0!==e.l&&(this.l=[...e.l]),this.elementProperties=new Map(e.elementProperties)}static finalize(){if(this.hasOwnProperty(d("finalized")))return;if(this.finalized=!0,this._$Ei(),this.hasOwnProperty(d("properties"))){const e=this.properties,t=[...h(e),...o2(e)];for(const V of t)this.createProperty(V,e[V])}const e=this[Symbol.metadata];if(null!==e){const t=litPropertyMetadata.get(e);if(void 0!==t)for(const[e,V]of t)this.elementProperties.set(e,V)}this._$Eh=new Map;for(const[e,t]of this.elementProperties){const V=this._$Eu(e,t);void 0!==V&&this._$Eh.set(V,e)}this.elementStyles=this.finalizeStyles(this.styles)}static finalizeStyles(e){const t=[];if(Array.isArray(e)){const V=new Set(e.flat(1/0).reverse());for(const e of V)t.unshift(c(e))}else void 0!==e&&t.push(c(e));return t}static _$Eu(e,t){const V=t.attribute;return!1===V?void 0:"string"==typeof V?V:"string"==typeof e?e.toLowerCase():void 0}constructor(){super(),this._$Ep=void 0,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Em=null,this._$Ev()}_$Ev(){var e;this._$ES=new Promise((e=>this.enableUpdating=e)),this._$AL=new Map,this._$E_(),this.requestUpdate(),null==(e=this.constructor.l)||e.forEach((e=>e(this)))}addController(e){var t,V;(null!=(t=this._$EO)?t:this._$EO=new Set).add(e),void 0!==this.renderRoot&&this.isConnected&&(null==(V=e.hostConnected)||V.call(e))}removeController(e){var t;null==(t=this._$EO)||t.delete(e)}_$E_(){const e=new Map,t=this.constructor.elementProperties;for(const V of t.keys())this.hasOwnProperty(V)&&(e.set(V,this[V]),delete this[V]);e.size>0&&(this._$Ep=e)}createRenderRoot(){var e;const t=null!=(e=this.shadowRoot)?e:this.attachShadow(this.constructor.shadowRootOptions);return S(t,this.constructor.elementStyles),t}connectedCallback(){var e;null!=this.renderRoot||(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null==(e=this._$EO)||e.forEach((e=>{var t;return null==(t=e.hostConnected)?void 0:t.call(e)}))}enableUpdating(e){}disconnectedCallback(){var e;null==(e=this._$EO)||e.forEach((e=>{var t;return null==(t=e.hostDisconnected)?void 0:t.call(e)}))}attributeChangedCallback(e,t,V){this._$AK(e,V)}_$EC(e,t){var V;const i=this.constructor.elementProperties.get(e),n=this.constructor._$Eu(e,i);if(void 0!==n&&!0===i.reflect){const A=(void 0!==(null==(V=i.converter)?void 0:V.toAttribute)?i.converter:u).toAttribute(t,i.type);this._$Em=e,null==A?this.removeAttribute(n):this.setAttribute(n,A),this._$Em=null}}_$AK(e,t){var V;const i=this.constructor,n=i._$Eh.get(e);if(void 0!==n&&this._$Em!==n){const e=i.getPropertyOptions(n),A="function"==typeof e.converter?{fromAttribute:e.converter}:void 0!==(null==(V=e.converter)?void 0:V.fromAttribute)?e.converter:u;this._$Em=n,this[n]=A.fromAttribute(t,e.type),this._$Em=null}}requestUpdate(e,t,V){var i;if(void 0!==e){if(null!=V||(V=this.constructor.getPropertyOptions(e)),!(null!=(i=V.hasChanged)?i:f)(this[e],t))return;this.P(e,t,V)}!1===this.isUpdatePending&&(this._$ES=this._$ET())}P(e,t,V){var i;this._$AL.has(e)||this._$AL.set(e,t),!0===V.reflect&&this._$Em!==e&&(null!=(i=this._$Ej)?i:this._$Ej=new Set).add(e)}async _$ET(){this.isUpdatePending=!0;try{await this._$ES}catch(e){Promise.reject(e)}const e=this.scheduleUpdate();return null!=e&&await e,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var e;if(!this.isUpdatePending)return;if(!this.hasUpdated){if(null!=this.renderRoot||(this.renderRoot=this.createRenderRoot()),this._$Ep){for(const[e,t]of this._$Ep)this[e]=t;this._$Ep=void 0}const e=this.constructor.elementProperties;if(e.size>0)for(const[t,V]of e)!0!==V.wrapped||this._$AL.has(t)||void 0===this[t]||this.P(t,this[t],V)}let t=!1;const V=this._$AL;try{t=this.shouldUpdate(V),t?(this.willUpdate(V),null==(e=this._$EO)||e.forEach((e=>{var t;return null==(t=e.hostUpdate)?void 0:t.call(e)})),this.update(V)):this._$EU()}catch(e){throw t=!1,this._$EU(),e}t&&this._$AE(V)}willUpdate(e){}_$AE(e){var t;null==(t=this._$EO)||t.forEach((e=>{var t;return null==(t=e.hostUpdated)?void 0:t.call(e)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(e)),this.updated(e)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$ES}shouldUpdate(e){return!0}update(e){this._$Ej&&(this._$Ej=this._$Ej.forEach((e=>this._$EC(e,this[e])))),this._$EU()}updated(e){}firstUpdated(e){}};b.elementStyles=[],b.shadowRootOptions={mode:"open"},b[d("elementProperties")]=new Map,b[d("finalized")]=new Map,null==p||p({ReactiveElement:b}),(null!=(_a2=a.reactiveElementVersions)?_a2:a.reactiveElementVersions=[]).push("2.0.4");var t2=globalThis,i3=t2.trustedTypes,s2=i3?i3.createPolicy("lit-html",{createHTML:e=>e}):void 0,e3="$lit$",h2=`lit$${Math.random().toFixed(9).slice(2)}$`,o3="?"+h2,n3=`<${o3}>`,r3=document,l2=()=>r3.createComment(""),c3=e=>null===e||"object"!=typeof e&&"function"!=typeof e,a2=Array.isArray,u2=e=>a2(e)||"function"==typeof(null==e?void 0:e[Symbol.iterator]),d2="[ \t\n\f\r]",f2=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,v=/-->/g,_=/>/g,m=RegExp(`>|${d2}(?:([^\\s"'>=/]+)(${d2}*=${d2}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),p2=/'/g,g=/"/g,$=/^(?:script|style|textarea|title)$/i,y2=e=>(t,...V)=>({_$litType$:e,strings:t,values:V}),x=y2(1),b2=y2(2),w=y2(3),T=Symbol.for("lit-noChange"),E=Symbol.for("lit-nothing"),A=new WeakMap,C=r3.createTreeWalker(r3,129);function P(e,t){if(!a2(e)||!e.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==s2?s2.createHTML(t):t}var V=(e,t)=>{const V=e.length-1,i=[];let n,A=2===t?"<svg>":3===t?"<math>":"",o=f2;for(let t=0;t<V;t++){const V=e[t];let r,s,q=-1,a=0;for(;a<V.length&&(o.lastIndex=a,s=o.exec(V),null!==s);)a=o.lastIndex,o===f2?"!--"===s[1]?o=v:void 0!==s[1]?o=_:void 0!==s[2]?($.test(s[2])&&(n=RegExp("</"+s[2],"g")),o=m):void 0!==s[3]&&(o=m):o===m?">"===s[0]?(o=null!=n?n:f2,q=-1):void 0===s[1]?q=-2:(q=o.lastIndex-s[2].length,r=s[1],o=void 0===s[3]?m:'"'===s[3]?g:p2):o===g||o===p2?o=m:o===v||o===_?o=f2:(o=m,n=void 0);const c=o===m&&e[t+1].startsWith("/>")?" ":"";A+=o===f2?V+n3:q>=0?(i.push(r),V.slice(0,q)+e3+V.slice(q)+h2+c):V+h2+(-2===q?t:c)}return[P(e,A+(e[V]||"<?>")+(2===t?"</svg>":3===t?"</math>":"")),i]},N=class e{constructor({strings:t,_$litType$:i},n){let A;this.parts=[];let o=0,r=0;const s=t.length-1,q=this.parts,[a,c]=V(t,i);if(this.el=e.createElement(a,n),C.currentNode=this.el.content,2===i||3===i){const e=this.el.content.firstChild;e.replaceWith(...e.childNodes)}for(;null!==(A=C.nextNode())&&q.length<s;){if(1===A.nodeType){if(A.hasAttributes())for(const e of A.getAttributeNames())if(e.endsWith(e3)){const t=c[r++],V=A.getAttribute(e).split(h2),i=/([.?@])?(.*)/.exec(t);q.push({type:1,index:o,name:i[2],strings:V,ctor:"."===i[1]?H:"?"===i[1]?I:"@"===i[1]?L:k}),A.removeAttribute(e)}else e.startsWith(h2)&&(q.push({type:6,index:o}),A.removeAttribute(e));if($.test(A.tagName)){const e=A.textContent.split(h2),t=e.length-1;if(t>0){A.textContent=i3?i3.emptyScript:"";for(let V=0;V<t;V++)A.append(e[V],l2()),C.nextNode(),q.push({type:2,index:++o});A.append(e[t],l2())}}}else if(8===A.nodeType)if(A.data===o3)q.push({type:2,index:o});else{let e=-1;for(;-1!==(e=A.data.indexOf(h2,e+1));)q.push({type:7,index:o}),e+=h2.length-1}o++}}static createElement(e,t){const V=r3.createElement("template");return V.innerHTML=e,V}};function S2(e,t,V=e,i){var n,A,o;if(t===T)return t;let r=void 0!==i?null==(n=V._$Co)?void 0:n[i]:V._$Cl;const s=c3(t)?void 0:t._$litDirective$;return(null==r?void 0:r.constructor)!==s&&(null==(A=null==r?void 0:r._$AO)||A.call(r,!1),void 0===s?r=void 0:(r=new s(e),r._$AT(e,V,i)),void 0!==i?(null!=(o=V._$Co)?o:V._$Co=[])[i]=r:V._$Cl=r),void 0!==r&&(t=S2(e,r._$AS(e,t.values),r,i)),t}var _a3,M=class{constructor(e,t){this._$AV=[],this._$AN=void 0,this._$AD=e,this._$AM=t}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(e){var t;const{el:{content:V},parts:i}=this._$AD,n=(null!=(t=null==e?void 0:e.creationScope)?t:r3).importNode(V,!0);C.currentNode=n;let A=C.nextNode(),o=0,r=0,s=i[0];for(;void 0!==s;){if(o===s.index){let t;2===s.type?t=new R(A,A.nextSibling,this,e):1===s.type?t=new s.ctor(A,s.name,s.strings,this,e):6===s.type&&(t=new z(A,this,e)),this._$AV.push(t),s=i[++r]}o!==(null==s?void 0:s.index)&&(A=C.nextNode(),o++)}return C.currentNode=r3,n}p(e){let t=0;for(const V of this._$AV)void 0!==V&&(void 0!==V.strings?(V._$AI(e,V,t),t+=V.strings.length-2):V._$AI(e[t])),t++}},R=class e{get _$AU(){var e,t;return null!=(t=null==(e=this._$AM)?void 0:e._$AU)?t:this._$Cv}constructor(e,t,V,i){var n;this.type=2,this._$AH=E,this._$AN=void 0,this._$AA=e,this._$AB=t,this._$AM=V,this.options=i,this._$Cv=null==(n=null==i?void 0:i.isConnected)||n}get parentNode(){let e=this._$AA.parentNode;const t=this._$AM;return void 0!==t&&11===(null==e?void 0:e.nodeType)&&(e=t.parentNode),e}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(e,t=this){e=S2(this,e,t),c3(e)?e===E||null==e||""===e?(this._$AH!==E&&this._$AR(),this._$AH=E):e!==this._$AH&&e!==T&&this._(e):void 0!==e._$litType$?this.$(e):void 0!==e.nodeType?this.T(e):u2(e)?this.k(e):this._(e)}O(e){return this._$AA.parentNode.insertBefore(e,this._$AB)}T(e){this._$AH!==e&&(this._$AR(),this._$AH=this.O(e))}_(e){this._$AH!==E&&c3(this._$AH)?this._$AA.nextSibling.data=e:this.T(r3.createTextNode(e)),this._$AH=e}$(e){var t;const{values:V,_$litType$:i}=e,n="number"==typeof i?this._$AC(e):(void 0===i.el&&(i.el=N.createElement(P(i.h,i.h[0]),this.options)),i);if((null==(t=this._$AH)?void 0:t._$AD)===n)this._$AH.p(V);else{const e=new M(n,this),t=e.u(this.options);e.p(V),this.T(t),this._$AH=e}}_$AC(e){let t=A.get(e.strings);return void 0===t&&A.set(e.strings,t=new N(e)),t}k(t){a2(this._$AH)||(this._$AH=[],this._$AR());const V=this._$AH;let i,n=0;for(const A of t)n===V.length?V.push(i=new e(this.O(l2()),this.O(l2()),this,this.options)):i=V[n],i._$AI(A),n++;n<V.length&&(this._$AR(i&&i._$AB.nextSibling,n),V.length=n)}_$AR(e=this._$AA.nextSibling,t){var V;for(null==(V=this._$AP)||V.call(this,!1,!0,t);e&&e!==this._$AB;){const t=e.nextSibling;e.remove(),e=t}}setConnected(e){var t;void 0===this._$AM&&(this._$Cv=e,null==(t=this._$AP)||t.call(this,e))}},k=class{get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}constructor(e,t,V,i,n){this.type=1,this._$AH=E,this._$AN=void 0,this.element=e,this.name=t,this._$AM=i,this.options=n,V.length>2||""!==V[0]||""!==V[1]?(this._$AH=Array(V.length-1).fill(new String),this.strings=V):this._$AH=E}_$AI(e,t=this,V,i){const n=this.strings;let A=!1;if(void 0===n)e=S2(this,e,t,0),A=!c3(e)||e!==this._$AH&&e!==T,A&&(this._$AH=e);else{const i=e;let o,r;for(e=n[0],o=0;o<n.length-1;o++)r=S2(this,i[V+o],t,o),r===T&&(r=this._$AH[o]),A||(A=!c3(r)||r!==this._$AH[o]),r===E?e=E:e!==E&&(e+=(null!=r?r:"")+n[o+1]),this._$AH[o]=r}A&&!i&&this.j(e)}j(e){e===E?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=e?e:"")}},H=class extends k{constructor(){super(...arguments),this.type=3}j(e){this.element[this.name]=e===E?void 0:e}},I=class extends k{constructor(){super(...arguments),this.type=4}j(e){this.element.toggleAttribute(this.name,!!e&&e!==E)}},L=class extends k{constructor(e,t,V,i,n){super(e,t,V,i,n),this.type=5}_$AI(e,t=this){var V;if((e=null!=(V=S2(this,e,t,0))?V:E)===T)return;const i=this._$AH,n=e===E&&i!==E||e.capture!==i.capture||e.once!==i.once||e.passive!==i.passive,A=e!==E&&(i===E||n);n&&this.element.removeEventListener(this.name,this,i),A&&this.element.addEventListener(this.name,this,e),this._$AH=e}handleEvent(e){var t,V;"function"==typeof this._$AH?this._$AH.call(null!=(V=null==(t=this.options)?void 0:t.host)?V:this.element,e):this._$AH.handleEvent(e)}},z=class{constructor(e,t,V){this.element=e,this.type=6,this._$AN=void 0,this._$AM=t,this.options=V}get _$AU(){return this._$AM._$AU}_$AI(e){S2(this,e)}},j=t2.litHtmlPolyfillSupport;null==j||j(N,R),(null!=(_a3=t2.litHtmlVersions)?_a3:t2.litHtmlVersions=[]).push("3.2.1");var _a4,B=(e,t,V)=>{var i,n;const A=null!=(i=null==V?void 0:V.renderBefore)?i:t;let o=A._$litPart$;if(void 0===o){const e=null!=(n=null==V?void 0:V.renderBefore)?n:null;A._$litPart$=o=new R(t.insertBefore(l2(),e),e,void 0,null!=V?V:{})}return o._$AI(e),o},r4=class extends b{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var e;const t=super.createRenderRoot();return null!=(e=this.renderOptions).renderBefore||(e.renderBefore=t.firstChild),t}update(e){const t=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(e),this._$Do=B(t,this.renderRoot,this.renderOptions)}connectedCallback(){var e;super.connectedCallback(),null==(e=this._$Do)||e.setConnected(!0)}disconnectedCallback(){var e;super.disconnectedCallback(),null==(e=this._$Do)||e.setConnected(!1)}render(){return T}};r4._$litElement$=!0,r4.finalized=!0,null==(_a4=globalThis.litElementHydrateSupport)||_a4.call(globalThis,{LitElement:r4});var _a5,i4=globalThis.litElementPolyfillSupport;null==i4||i4({LitElement:r4}),(null!=(_a5=globalThis.litElementVersions)?_a5:globalThis.litElementVersions=[]).push("4.1.1");var Button=class extends HTMLElement{constructor(){super(),this.attachShadow({mode:"open"}),this.shadowRoot.appendChild(this.createTemplate()),this.buttonEle=this.shadowRoot.querySelector("button")}createTemplate(){const e=document.createElement("template");return e.innerHTML="\n       <style>\n          .button {\n         padding: 6px 16px;\n         border-radius: 4px;\n         border: 0;\n         outline: none;\n         background-color: transparent;\n         user-select: none;\n         font-size: 0.875rem;\n         min-width: 64px;\n         box-sizing: border-box;\n         font-weight: 500;\n         line-height: 1.75;\n         transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,\n           box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,\n           border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n       }\n       .outlined { \n         /* 看不到 听不到 */\n         padding: 5px 15px;\n         border: 1px solid #006eff;\n         color: #006eff;\n         cursor: pointer;\n       }\n       .contained {\n         /* 听得到 */\n         cursor: pointer;\n         color: #ffffff;\n         background-color: #006eff;\n         box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);\n       }\n       .disabled {\n         /* 禁止 开始检测 */\n         box-shadow: none;\n         color: rgba(0, 0, 0, 0.26);\n         background-color: rgba(0, 0, 0, 0.12);\n       }\n       .close{\n        color: #eeeeee!important;\n        border-radius: 20px!important;\n        border-color: #eeeeee!important;\n       }\n      </style>\n        <button>\n            <slot></slot>\n        </button>\n      ",e.content.cloneNode(!0)}connectedCallback(){const e=this.getAttribute("type"),t=this.getAttribute("class");this.buttonEle.type="button",this.buttonEle.classList.add("button",e,t);const V=this.getAttribute("on-click");if(V){const e=new Function(V);this.buttonEle.addEventListener("click",(()=>{e()}))}}attributeChangedCallback(e,t,V){"type"===e&&(this.buttonEle.classList.remove(t),this.buttonEle.classList.add(V)),"className"===e&&(this.buttonEle.classList.remove(t),this.buttonEle.classList.add(V))}};__publicField(Button,"observedAttributes",["className","type","on-click"]),customElements.get("trtc-custom-button")||customElements.define("trtc-custom-button",Button);var core,log,language,index=9999,sheet=`\n    @media screen and (max-width: 769px) {\n        .root {\n          max-width: 32.5rem;\n        }\n        .device-detector-backdrop {\n          width: 100%;\n          height: 100%;\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: rgba(0, 0, 0, 0.8);\n          z-index: ${index};\n          opacity: 1;\n          transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n        }\n        .device-detector-backdrop .root {\n          position: relative;\n          width: 98vw;\n          height: 72vh;\n          font-size: 16px;\n          box-shadow: 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);\n          background-color: #ffffff;\n          border-radius: 4px;\n        }\n        .device-detector-backdrop .root .stepper {\n          border-radius: 5px 5px 0 0;\n          font-size: 36px;\n        }\n        .device-detector-backdrop .root .close {\n          color: #eeeeee !important;\n          border-radius: 20px !important;\n          border-color: #eeeeee !important;\n          position: absolute !important;\n          cursor: pointer;\n          top: -50px;\n          right: 2px;\n        }\n        .arrow {\n          width: 0;\n          height: 0;\n          border-left: 2vh solid transparent;\n          border-right: 2vh solid transparent;\n          border-bottom: 2vh solid red;\n          position: absolute;\n          left: 2vw;\n          animation: moveUpDown 2s infinite ease-in-out;\n          transform:rotate(45deg);\n          z-index: ${index+1};\n        }\n    \n      .arrow::after {\n          content: "";\n          width: 1.5vh;\n          height: 4vh;\n          background-color: red;\n          position: absolute;\n          // top: 9px;  /* 将柄定位在箭头的下方 */\n          left: 50%;  /* 水平居中柄 */\n          transform: translate(-50%,18%);  /* 微调柄的水平位置，使其完全居中 */\n          z-index: ${index+1};\n        }\n    \n      .remind-text {\n          position: absolute;\n          top: 10vh;  /* 调整文字的垂直位置 */\n          left: 1vw;\n          width:90vw;\n          color:red;\n          z-index: ${index+1};\n        }\n    \n        @keyframes moveUpDown {\n          0% {\n            top: 3vh;\n            left: 2vw;\n          }\n        \n          50% {\n            top: 1.5vh;\n            left: 3vw; \n          }\n        \n          100% {\n            top: 3vh;\n            left: 2vw;\n          }\n        }\n        .device-connect {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          height: 100%;\n        }\n        .device-connect .testing-title {\n          display: flex;\n          font-size: 2rem;\n          justify-content: center;\n          margin-top: 4vh;\n          color: rgba(32, 30, 30, 0.8980392157);\n        }\n        .device-connect .testing-prepare-info {\n          max-width: 500px;\n          padding: 0 1vw;\n          text-align: center;\n          display: flex;\n          font-size: 1rem;\n          justify-content: center;\n          margin-top: 3.8vh;\n          color: rgba(88, 86, 86, 0.8980392157);\n        }\n        .device-connect .device-display {\n          width: 88%;\n          margin: 3vh auto 1.5vh;\n          display: flex;\n          justify-content: space-around;\n          position: relative;\n        }\n        .device-connect .device-display .connect-success {\n          position: relative;\n        }\n        .device-connect .device-display .connect-success::before {\n          content: "";\n          width: 28px;\n          height: 28px;\n          position: absolute;\n          bottom: -34px;\n          left: 50%;\n          transform: translateX(-50%);\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACAElEQVRYR+2Vv2sUQRTH3/eOYO+f EWzmzUaSTv8CYxIUAooWAYsQJEkRRDRikUJMiiMIQRSVCNql0sqkCIFwO7NLCGm00kpEEBtzhsyT Pe5kWG9zu8p5zU658958Pnznx4L6PNBnPpUCZQJlAj1PgOu8SBW6C0HNBGYmfe17KqBCdRvA/TbU OXc+Hoq3fImeCahQLQBY8mDGahv8lwR0qOcF8sCDfSCicavtXs8F2PBNIlrxQB9dxU3EKq53evY7 boGyahDHuCRVWY04+pL3f8GGp4mo1q4H8JkcTZjAbGet0VGADUurYd8dubF4OH7fTUJZdQOCR17d t1bs707q/UNgZH/kdKPR+Oofnupx9WL9bP1T1kJseIqI1rz5HwIZjzh60008awvuQXDndzNoR0hG O20HG75ORE+8WldBZSxU4UY3eDKfeQ211UsisuAtsjlwNDC6O7z7vf1NWXUFgucp0GWr7es88BMF kklt9UMRmfUWe3t46vDCwZmDnzrUkwJZ90ECuRpx9CIvvKtAUsCWaySUnO7mEJENAC+J6JUPgmDK BOZxEXgugaZEyGsESg5a1pi22q4WhecWaEoYfkpE19IQEZmLgmj5b+CFBJJiZdU6BJNtGAS3TGD8 976wR+GfUSuJc+TomR2yi4WJqYbCAv8KTPeXAmUCZQJ9T+AXo7StIY0IqrkAAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n        }\n        .device-connect .device-display .connect-fail {\n          position: relative;\n        }\n        .device-connect .device-display .connect-fail::before {\n          content: "";\n          width: 28px;\n          height: 28px;\n          position: absolute;\n          bottom: -34px;\n          left: 50%;\n          transform: translateX(-50%);\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAB3ElEQVRYR+2Wr08cQRTHP+8ESZOq GlKBaKhpUwWmCnG7d4giwDS5NGlVMWDAtIZfrQEDpjVF0ZC0wH/QWxr+gNrKJigSBA4J98hN7pbl 9mZnZklz5tasmPfm+5nve/t2hAE/MmB9hgBDB5wOaJVx+c2/Ms2qEc+BCznm3JZvBdBpHnPNmUlU VuWYzyEQGrGC8KmdI4m92e0AbXrhbyoqfJQmWz4QWuMDymYa2+KpzcXCEmjMOrCWEV2WhJ0iCI1Z ArbTGId77h7ohRAWpcnXfhBaYwHlS4hrTgDTAnkn5iVhNwuhMe+BbyFumf7wqakF4p0kfO+svQX2 Mie3utSr5w3QF6JCw2zY4kdm45w7RYcMArA4kd0/dcXX2WAAK0SFhvzip69wN64cQJ0XtDgEnmXq /lqaHP13AK0yQcWIj+fEhGCIIAc05iUY8bGO+EbnfTusAiG8AbTOVMf20a64JGZS5udEAIQXgEbE CAfAo17xbhlyw8oTwgmgMa/AdPdDm/h9IIp/RjXmUCM+4hIvC2H/HdeZpMWfTKdvdGvu+tRy5bji iZxw2i+v6D7wBmHf9+S9m9+BKH0fiJilwgNp3pn1LgPSdY2YQbiUhBNbkrMJvdVKBg4Bhg4M3IEb uI2UIfOyj40AAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n        }\n        .device-connect .device-display .device {\n          width: 46px;\n          height: 46px;\n          position: relative;\n          justify-content: center;\n          font-size: 38px;\n        }\n        .device-connect .device-display .device svg {\n          width: 36px;\n          height: 36px;\n          fill: #47494D;\n        }\n        .device-connect .device-display .outer-progress {\n          width: 83%;\n          height: 4px;\n          border-radius: 5px;\n          position: absolute;\n          top: 70px;\n          background-color: #eeeeee;\n          overflow: hidden;\n        }\n        .device-connect .device-display .outer-progress .inner-progress {\n          width: 100%;\n          position: absolute;\n          top: 0;\n          left: -100%;\n          height: 4px;\n          border-radius: 5px;\n          background-color: #bfbfbf;\n          transform-origin: left;\n          transition: transform 0.3s linear;\n        }\n        .device-connect .text {\n          margin-top: 4.5vh;\n          font-size: 1rem;\n          max-width: 420px;\n          text-align: center;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        .device-connect .text.gray-text {\n          color: rgba(88, 86, 86, 0.8980392157);\n        }\n        .device-connect .text.green-text {\n          color: rgb(50, 205, 50);\n        }\n        .device-connect .text.red-text {\n          color: red;\n        }\n        .device-connect .button-container {\n          position: absolute;\n          bottom: 3vh;\n          width: 40%;\n          display: flex;\n          justify-content: space-around;\n        }\n        .error-connect {\n          width: 20px;\n          height: 20px;\n          margin-left: 8px;\n          display: inline;\n        }\n        .error-connect .error-icon svg {\n          width: 20px;\n          height: 20px;\n        }\n        .connect-attention-info {\n          padding: 1vw 1vh;\n          min-width: 134px;\n          min-height: 50px;\n          background: rgba(0, 0, 0, 0.6);\n          border-radius: 10px;\n          color: rgb(255, 255, 255);\n          position: absolute;\n          top:23vh;\n          display: block;\n          font-size: 0.8rem;\n          text-align: left;\n          max-width: 92vw;\n          white-space: nowrap;\n          white-space: normal;  // 添加这一行，使其自动换行\n          word-wrap: break-word;  /* 添加自动换行样式 */\n          overflow-wrap: anywhere;  /* 允许在任意位置换行 */\n        }\n        .error-connect .connect-attention-info::after {\n          content: "";\n          width: 0;\n          height: 0;\n          border: 10px transparent solid;\n          border-top-color: rgba(0, 0, 0, 0.6);\n          position: absolute;\n          left: 100%;\n          top: 100%;\n          transform: translateX(-18vw);\n        }\n        .testing-body {\n          display: flex;\n          flex-wrap: wrap;\n          justify-content: center;\n        }\n        .testing-body.hide {\n          display: none;\n        }\n        .testing-body .device-list {\n          width: 93%;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 2vh;\n        }\n        .testing-body .device-list .device-list-title {\n          margin-right: 1vw;\n          font-size: 0.8rem;\n        }\n        .testing-body .flex-col {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n        .testing-body .checkbox {\n          display: flex;\n          align-items: center;\n        }\n        .testing-body .camera-video {\n          max-width: 80vw;\n          height: 32vh;\n          object-fit: contain;  /* 保持视频的宽高比，将其完整显示在框内 */\n        }\n        .testing-body .mirror {\n          transform: scaleX(-1);\n        }\n        .testing-container {\n          width: 100%;\n          margin: 10px auto 30px;\n        }\n        .testing-container .testing-body {\n          display: flex;\n          flex-wrap: wrap;\n          justify-content: center;\n        }\n        .testing-container .testing-body.hide {\n          display: none;\n        }\n        .testing-container .testing-body .device-list {\n          width: 100%;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 20px;\n        }\n        .testing-container .testing-body .device-list .device-list-title {\n          margin-right: 10px;\n        }\n        .testing-container .testing-body .flex-col {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n        .testing-container .testing-body .checkbox {\n          display: flex;\n          align-items: center;\n        }\n        .testing-container .testing-body .camera-video {\n          width: 300px;\n          height: 180px;\n        }\n        .testing-container .testing-body .mirror {\n          transform: scaleX(-1);\n        }\n        .audio-player-container {\n          width: 340px;\n          display: flex;\n          justify-content: center;\n          flex-wrap: wrap;\n          margin: 2vh auto 0;\n          text-align: center;\n        }\n        .audio-player-container .audio-player-info {\n          margin: 0px auto 16px;\n          color: #5f5f5f;\n        }\n        .audio-player-container #audio-player {\n          width: 100%;\n        }\n        .testing-info-container {\n          display: flex;\n          width: 100%;\n          justify-content: center;\n          flex-wrap: wrap;\n          position: absolute;\n          bottom: 3vh;\n        }\n        .testing-info-container .testing-info {\n          width: 100%;\n          text-align: center;\n          display: block;\n          font-size:1rem;\n        }\n        .testing-info-container .button-list {\n          margin-top: 2vh;\n          width: 300px;\n          display: flex;\n          justify-content: space-around;\n        }\n        .device-select {\n          width: 90%;\n          padding: 6px 14px 6px 12px;\n          position: relative;\n          font-size: 0.8rem;\n          border-radius: 4px;\n          background-color: #fff;\n          cursor: pointer;\n        }\n        .device-select:focus {\n          outline: none;\n        }\n        .step-container {\n          display: flex;\n          margin-top: 2vh;\n          margin-bottom: 2vh;\n          padding: 0.3vw;\n          font-size: 0.95rem;\n        }\n        .step-container .step {\n          width: 100%;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          position: relative;\n          fill: rgba(0, 0, 0, 0.54);\n          color: rgba(0, 0, 0, 0.54);\n        }\n        .step-container .step:not(:first-child)::after {\n          position: absolute;\n          content: "";\n          height: 1px;\n          background-color: rgba(0, 0, 0, 0.16);\n          right: 100%;\n          top: 30%;\n          right: calc(50% + 20px);\n          left: calc(-50% + 20px);\n          top: 16px;\n        }\n        .step-container .step.active {\n          fill: #006EFF;\n          color: #006EFF;\n          cursor: pointer;\n        }\n        .step-container .step.active::after {\n          background-color: #006EFF;\n        }\n        .step-container .step.error {\n          fill: red;\n          color: red;\n          cursor: pointer;\n        }\n        .step-container .step.error::after {\n          background-color: #006EFF;\n        }\n        .step-container .step .step-label {\n          margin-top: 12px;\n        }\n        .mic-testing-container {\n          margin-top: 20px;\n          width: 100%;\n        }\n        .mic-testing-container .mic-testing-info {\n          text-align: center;\n        }\n        .mic-testing-container .mic-bar-container {\n          display: flex;\n          justify-content: center;\n          margin-top: 10px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar {\n          width: 8px;\n          height: 30px;\n          border: 1px solid #cccccc;\n          border-radius: 1px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar:not(:first-child) {\n          margin-left: 3px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar.active {\n          background: #006EFF;\n        }\n        .recording-container {\n          margin-top: 3vh;\n        }\n        .testing-list {\n          width: 100%;\n          display: flex;\n          flex-wrap: wrap;\n        }\n        .testing-list .testing-item-container {\n          width: 83%;\n          margin: 0 auto 10px;\n          display: flex;\n          justify-content: space-between;\n        }\n        @keyframes loading-circle {\n          0% {\n            transform: rotate(0deg);\n          }\n          25% {\n            transform: rotate(90deg);\n          }\n          50% {\n            transform: rotate(180deg);\n          }\n          75% {\n            transform: rotate(270deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n        .network-loading {\n          display: flex;\n        }\n        .network-loading::before {\n          content: "";\n          width: 16px;\n          height: 16px;\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAChElEQVRYR82WO2wTQRCGZ9YFLmmCQDRUFIgOCuhMg2QJ8OxKtpQuEggqQDz6hDIiSAg6QEo6pFi+WVsoLgkVDakgFFBAlYJ0IGRceAetdWddLF/uznacXHe38/j2n9mbRTjkB6eRn4jOWGt/jhMrMwARlRDxkogUEfETM7/zCY0xqyKyAAAe4G70PStMZgCt9WsAuBUG3mLmi5VK5ZxSajuWrM7MtazJvd1EAER0HBG/AcBcPxji8yAIHhDREiJeA4A/IvLBWruUBJUIoLX+AgC7IvLEWruZVAIimldKXXXOefstb6u1lljCbWY+nwsg3MFi6LTJzFeyyloul48Vi8V/MfvvzHw2F0Cstl6BZWvts6wA3o6I3iDizdDnBTPfzwUQGVer1bl6vb6bJ3nM92S321WtVmtHa/3QnxBE/BwEwY14vD094KXfr2HGASGiBURcjXxF5KW19l70PgDQWr8HgBIA5Kp5GpTWeh0AqjG7HWY+PUsAn9xD9J/wVA2O5YGXIGzKklJq3jn30Vq7ltgDaXJOsu7nhS+xUuqyc+6t/1/k+hNOktz7GmNYRCgWp8bM9X4JiGhZKXVCRDb8x0mTDfuPmBnepD83+gDRr3O4QaYJorX+AQC+DHuacSYKhCovIuJ1ALiAiGu9Xu9ps9n8mnkaTkuN4cvLzAGGN3J0AIjoESKuiMgda+2raUmeFmegwEED+KPom+7QSpA0aY9ODwxLY4w51el0/rbb7d9pdZxkfb9Lqb9YTvVuMAo0EcDfZAqFwq9Go7Exzg6NMStBEDxO883VA0R0e9QRTerwtOS5x3E4tPpjNAoeTrp151xt1DFLg8ilQFKwmSmQtptx1v8DVbAxMP//OLQAAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n          animation: loading-circle 2s linear infinite;\n        }\n        .report-button {\n          position: absolute;\n          bottom: 6vh;\n          cursor: pointer;\n        }\n        .gray-button {\n          position: absolute;\n          bottom: 6vh;\n        }\n        .device-testing-report {\n          display: flex;\n          flex-wrap: wrap;\n          flex-direction: column;\n          align-items: center;\n        }\n        .device-testing-report .testing-title {\n          display: flex;\n          font-size: 4vh;\n          justify-content: center;\n          margin-top: 2vh;\n          color: rgba(32, 30, 30, 0.8980392157);\n        }\n        .device-testing-report .device-report-list {\n          display: block;\n          width: 100%;\n          margin-top: 10px;\n        }\n        .device-testing-report .device-report-list .device-report {\n          display: flex;\n          width: 70%;\n          margin: 2vh auto 0;\n          justify-content: space-between;\n        }\n        .device-testing-report .device-report-list .device-report .device-info {\n          display: flex;\n          width: 80%;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .report-icon {\n          margin-right: 20px;\n          justify-content: center;\n          font-size: 22px;\n          line-height: 22px;\n          color: #515151;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .report-icon svg {\n          width: 24px;\n          height: 24px;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .device-name {\n          width: 280px;\n          height: 24px;\n          line-height: 24px;\n          display: block;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          text-align: left;\n        }\n        .device-testing-report .device-report-list .device-report .green {\n          color: green;\n        }\n        .device-testing-report .device-report-list .device-report .red {\n          color: red;\n        }\n        .device-testing-report .device-report-footer {\n          width: 65%;\n          display: flex;\n          justify-content: space-between;\n          position: absolute;\n          bottom: 36px;\n        }\n      }\n      @media screen and (min-width: 769px) {\n        .device-detector-backdrop {\n          width: 100%;\n          height: 100%;\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background-color: rgba(0, 0, 0, 0.8);\n          z-index: ${index};\n          opacity: 1;\n          transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n        }\n        .device-detector-backdrop .root {\n          position: relative;\n          width: 600px;\n          height: 480px;\n          font-size: 16px;\n          box-shadow: 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12);\n          background-color: #ffffff;\n          border-radius: 4px;\n        }\n        .device-detector-backdrop .root .stepper {\n          border-radius: 5px 5px 0 0;\n          font-size: 36px;\n        }\n        .device-detector-backdrop .root .close {\n          color: #eeeeee !important;\n          border-radius: 20px !important;\n          border-color: #eeeeee !important;\n          position: absolute !important;\n          cursor: pointer;\n          top: -50px;\n          right: 2px;\n        }\n\n        .arrow {\n          width: 0;\n          height: 0;\n          border-left: 2vh solid transparent;\n          border-right: 2vh solid transparent;\n          border-bottom: 2vh solid red;\n          position: absolute;\n          left: 2vw;\n          animation: moveUpDown 2s infinite ease-in-out;\n          transform:rotate(45deg);\n          z-index: ${index+1};\n        }\n    \n      .arrow::after {\n          content: "";\n          width: 1.5vh;\n          height: 4vh;\n          background-color: red;\n          position: absolute;\n          // top: 9px;  /* 将柄定位在箭头的下方 */\n          left: 50%;  /* 水平居中柄 */\n          transform: translate(-50%,18%);  /* 微调柄的水平位置，使其完全居中 */\n          z-index: ${index+1};\n        }\n    \n      .remind-text {\n          position: absolute;\n          top: 10vh;  /* 调整文字的垂直位置 */\n          left: 1vw;\n          width:30vw;\n          color:red;\n          z-index: ${index+1};\n        }\n    \n        @keyframes moveUpDown {\n          0% {\n            top: 3vh;\n            left: 2vw;\n          }\n        \n          50% {\n            top: 1.5vh;\n            left: 3vw; \n          }\n        \n          100% {\n            top: 3vh;\n            left: 2vw;\n          }\n        }\n        .device-connect {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n        .device-connect .testing-title {\n          display: flex;\n          font-size: 32px;\n          justify-content: center;\n          margin-top: 55px;\n          color: rgba(32, 30, 30, 0.8980392157);\n        }\n        .device-connect .testing-prepare-info {\n          max-width: 500px;\n          text-align: center;\n          display: flex;\n          font-size: 16px;\n          justify-content: center;\n          margin-top: 30px;\n          color: rgba(88, 86, 86, 0.8980392157);\n        }\n        .device-connect .device-display {\n          width: 420px;\n          margin: 40px auto 20px;\n          display: flex;\n          justify-content: space-around;\n          position: relative;\n        }\n        .device-connect .device-display .connect-success {\n          position: relative;\n        }\n        .device-connect .device-display .connect-success::before {\n          content: "";\n          width: 28px;\n          height: 28px;\n          position: absolute;\n          bottom: -34px;\n          left: 50%;\n          transform: translateX(-50%);\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACAElEQVRYR+2Vv2sUQRTH3/eOYO+f EWzmzUaSTv8CYxIUAooWAYsQJEkRRDRikUJMiiMIQRSVCNql0sqkCIFwO7NLCGm00kpEEBtzhsyT Pe5kWG9zu8p5zU658958Pnznx4L6PNBnPpUCZQJlAj1PgOu8SBW6C0HNBGYmfe17KqBCdRvA/TbU OXc+Hoq3fImeCahQLQBY8mDGahv8lwR0qOcF8sCDfSCicavtXs8F2PBNIlrxQB9dxU3EKq53evY7 boGyahDHuCRVWY04+pL3f8GGp4mo1q4H8JkcTZjAbGet0VGADUurYd8dubF4OH7fTUJZdQOCR17d t1bs707q/UNgZH/kdKPR+Oofnupx9WL9bP1T1kJseIqI1rz5HwIZjzh60008awvuQXDndzNoR0hG O20HG75ORE+8WldBZSxU4UY3eDKfeQ211UsisuAtsjlwNDC6O7z7vf1NWXUFgucp0GWr7es88BMF kklt9UMRmfUWe3t46vDCwZmDnzrUkwJZ90ECuRpx9CIvvKtAUsCWaySUnO7mEJENAC+J6JUPgmDK BOZxEXgugaZEyGsESg5a1pi22q4WhecWaEoYfkpE19IQEZmLgmj5b+CFBJJiZdU6BJNtGAS3TGD8 976wR+GfUSuJc+TomR2yi4WJqYbCAv8KTPeXAmUCZQJ9T+AXo7StIY0IqrkAAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n        }\n        .device-connect .device-display .connect-fail {\n          position: relative;\n        }\n        .device-connect .device-display .connect-fail::before {\n          content: "";\n          width: 28px;\n          height: 28px;\n          position: absolute;\n          bottom: -34px;\n          left: 50%;\n          transform: translateX(-50%);\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAB3ElEQVRYR+2Wr08cQRTHP+8ESZOq GlKBaKhpUwWmCnG7d4giwDS5NGlVMWDAtIZfrQEDpjVF0ZC0wH/QWxr+gNrKJigSBA4J98hN7pbl 9mZnZklz5tasmPfm+5nve/t2hAE/MmB9hgBDB5wOaJVx+c2/Ms2qEc+BCznm3JZvBdBpHnPNmUlU VuWYzyEQGrGC8KmdI4m92e0AbXrhbyoqfJQmWz4QWuMDymYa2+KpzcXCEmjMOrCWEV2WhJ0iCI1Z ArbTGId77h7ohRAWpcnXfhBaYwHlS4hrTgDTAnkn5iVhNwuhMe+BbyFumf7wqakF4p0kfO+svQX2 Mie3utSr5w3QF6JCw2zY4kdm45w7RYcMArA4kd0/dcXX2WAAK0SFhvzip69wN64cQJ0XtDgEnmXq /lqaHP13AK0yQcWIj+fEhGCIIAc05iUY8bGO+EbnfTusAiG8AbTOVMf20a64JGZS5udEAIQXgEbE CAfAo17xbhlyw8oTwgmgMa/AdPdDm/h9IIp/RjXmUCM+4hIvC2H/HdeZpMWfTKdvdGvu+tRy5bji iZxw2i+v6D7wBmHf9+S9m9+BKH0fiJilwgNp3pn1LgPSdY2YQbiUhBNbkrMJvdVKBg4Bhg4M3IEb uI2UIfOyj40AAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n        }\n        .device-connect .device-display .device {\n          width: 46px;\n          height: 46px;\n          position: relative;\n          justify-content: center;\n          font-size: 38px;\n        }\n        .device-connect .device-display .device svg {\n          width: 36px;\n          height: 36px;\n          fill: #47494D;\n        }\n        .device-connect .device-display .outer-progress {\n          width: 360px;\n          height: 4px;\n          border-radius: 5px;\n          position: absolute;\n          top: 70px;\n          background-color: #eeeeee;\n          overflow: hidden;\n        }\n        .device-connect .device-display .outer-progress .inner-progress {\n          width: 100%;\n          position: absolute;\n          top: 0;\n          left: -360px;\n          height: 4px;\n          border-radius: 5px;\n          background-color: #bfbfbf;\n          transform-origin: left;\n          transition: transform 0.3s linear;\n        }\n        .device-connect .text {\n          margin-top: 50px;\n          font-size: 18px;\n          max-width: 420px;\n          text-align: center;\n          height: 20px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n        }\n        .device-connect .text.gray-text {\n          color: rgba(88, 86, 86, 0.8980392157);\n        }\n        .device-connect .text.green-text {\n          color: rgb(50, 205, 50);\n        }\n        .device-connect .text.red-text {\n          color: red;\n        }\n        .device-connect .button-container {\n          margin-top: 50px;\n          width: 40%;\n          display: flex;\n          justify-content: space-around;\n        }\n        .error-connect {\n          width: 20px;\n          height: 20px;\n          margin-left: 8px;\n          position: relative;\n          display: inline;\n        }\n        .error-connect .error-icon svg {\n          width: 20px;\n          height: 20px;\n        }\n        .error-connect .connect-attention-info {\n          padding: 8px 12px;\n          min-width: 160px;\n          min-height: 50px;\n          background: rgba(0, 0, 0, 0.6);\n          border-radius: 10px;\n          color: #fff;\n          position: absolute;\n          right: 0;\n          bottom: 100%;\n          transform: translate(20px, -10px);\n          display: block;\n          white-space: nowrap;\n          font-size: 12px;\n          text-align: left;\n        }\n        .error-connect .connect-attention-info::after {\n          content: "";\n          width: 0;\n          height: 0;\n          border: 10px transparent solid;\n          border-top-color: rgba(0, 0, 0, 0.6);\n          position: absolute;\n          left: 100%;\n          top: 100%;\n          transform: translateX(-40px);\n        }\n        .testing-body {\n          display: flex;\n          flex-wrap: wrap;\n          justify-content: center;\n        }\n        .testing-body.hide {\n          display: none;\n        }\n        .testing-body .device-list {\n          width: 100%;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 20px;\n        }\n        .testing-body .device-list .device-list-title {\n          margin-right: 10px;\n        }\n        .testing-body .flex-col {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n        .testing-body .checkbox {\n          display: flex;\n          align-items: center;\n        }\n        .testing-body .camera-video {\n          width: 300px;\n          height: 180px;\n        }\n        .testing-body .mirror {\n          transform: scaleX(-1);\n        }\n        .testing-container {\n          width: 100%;\n          margin: 10px auto 30px;\n        }\n        .testing-container .testing-body {\n          display: flex;\n          flex-wrap: wrap;\n          justify-content: center;\n        }\n        .testing-container .testing-body.hide {\n          display: none;\n        }\n        .testing-container .testing-body .device-list {\n          width: 100%;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          margin-bottom: 20px;\n        }\n        .testing-container .testing-body .device-list .device-list-title {\n          margin-right: 10px;\n        }\n        .testing-container .testing-body .flex-col {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n        .testing-container .testing-body .checkbox {\n          display: flex;\n          align-items: center;\n        }\n        .testing-container .testing-body .camera-video {\n          width: 300px;\n          height: 180px;\n        }\n        .testing-container .testing-body .mirror {\n          transform: scaleX(-1);\n        }\n        .audio-player-container {\n          width: 340px;\n          display: flex;\n          justify-content: center;\n          flex-wrap: wrap;\n          margin: 20px auto 0;\n          text-align: center;\n        }\n        .audio-player-container .audio-player-info {\n          margin: 0px auto 16px;\n          color: #5f5f5f;\n        }\n        .testing-info-container {\n          display: flex;\n          width: 100%;\n          justify-content: center;\n          flex-wrap: wrap;\n          position: absolute;\n          bottom: 30px;\n        }\n        .testing-info-container .testing-info {\n          width: 100%;\n          text-align: center;\n          display: block;\n        }\n        .testing-info-container .button-list {\n          margin-top: 20px;\n          width: 300px;\n          display: flex;\n          justify-content: space-around;\n        }\n        .device-select {\n          width: 260px;\n          padding: 6px 14px 6px 12px;\n          position: relative;\n          font-size: 16px;\n          border-radius: 4px;\n          background-color: #fff;\n          cursor: pointer;\n        }\n        .device-select:focus {\n          outline: none;\n        }\n        .step-container {\n          display: flex;\n          padding: 24px;\n        }\n        .step-container .step {\n          width: 100%;\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          position: relative;\n          fill: rgba(0, 0, 0, 0.54);\n          color: rgba(0, 0, 0, 0.54);\n        }\n        .step-container .step:not(:first-child)::after {\n          position: absolute;\n          content: "";\n          height: 1px;\n          background-color: rgba(0, 0, 0, 0.16);\n          right: 100%;\n          top: 30%;\n          right: calc(50% + 20px);\n          left: calc(-50% + 20px);\n          top: 16px;\n        }\n        .step-container .step.active {\n          fill: #006EFF;\n          color: #006EFF;\n          cursor: pointer;\n        }\n        .step-container .step.active::after {\n          background-color: #006EFF;\n        }\n        .step-container .step.error {\n          fill: red;\n          color: red;\n          cursor: pointer;\n        }\n        .step-container .step.error::after {\n          background-color: #006EFF;\n        }\n        .step-container .step .step-label {\n          margin-top: 12px;\n        }\n        .mic-testing-container {\n          margin-top: 20px;\n          width: 100%;\n        }\n        .mic-testing-container .mic-testing-info {\n          text-align: center;\n        }\n        .mic-testing-container .mic-bar-container {\n          display: flex;\n          justify-content: center;\n          margin-top: 10px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar {\n          width: 8px;\n          height: 30px;\n          border: 1px solid #cccccc;\n          border-radius: 1px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar:not(:first-child) {\n          margin-left: 3px;\n        }\n        .mic-testing-container .mic-bar-container .mic-bar.active {\n          background: #006EFF;\n        }\n        .recording-container {\n          margin-top: 5em;\n        }\n        .testing-list {\n          width: 100%;\n          display: flex;\n          flex-wrap: wrap;\n        }\n        .testing-list .testing-item-container {\n          width: 70%;\n          margin: 0 auto 10px;\n          display: flex;\n          justify-content: space-between;\n        }\n        @keyframes loading-circle {\n          0% {\n            transform: rotate(0deg);\n          }\n          25% {\n            transform: rotate(90deg);\n          }\n          50% {\n            transform: rotate(180deg);\n          }\n          75% {\n            transform: rotate(270deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n        .network-loading {\n          display: flex;\n        }\n        .network-loading::before {\n          content: "";\n          width: 16px;\n          height: 16px;\n          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAChElEQVRYR82WO2wTQRCGZ9YFLmmCQDRUFIgOCuhMg2QJ8OxKtpQuEggqQDz6hDIiSAg6QEo6pFi+WVsoLgkVDakgFFBAlYJ0IGRceAetdWddLF/uznacXHe38/j2n9mbRTjkB6eRn4jOWGt/jhMrMwARlRDxkogUEfETM7/zCY0xqyKyAAAe4G70PStMZgCt9WsAuBUG3mLmi5VK5ZxSajuWrM7MtazJvd1EAER0HBG/AcBcPxji8yAIHhDREiJeA4A/IvLBWruUBJUIoLX+AgC7IvLEWruZVAIimldKXXXOefstb6u1lljCbWY+nwsg3MFi6LTJzFeyyloul48Vi8V/MfvvzHw2F0Cstl6BZWvts6wA3o6I3iDizdDnBTPfzwUQGVer1bl6vb6bJ3nM92S321WtVmtHa/3QnxBE/BwEwY14vD094KXfr2HGASGiBURcjXxF5KW19l70PgDQWr8HgBIA5Kp5GpTWeh0AqjG7HWY+PUsAn9xD9J/wVA2O5YGXIGzKklJq3jn30Vq7ltgDaXJOsu7nhS+xUuqyc+6t/1/k+hNOktz7GmNYRCgWp8bM9X4JiGhZKXVCRDb8x0mTDfuPmBnepD83+gDRr3O4QaYJorX+AQC+DHuacSYKhCovIuJ1ALiAiGu9Xu9ps9n8mnkaTkuN4cvLzAGGN3J0AIjoESKuiMgda+2raUmeFmegwEED+KPom+7QSpA0aY9ODwxLY4w51el0/rbb7d9pdZxkfb9Lqb9YTvVuMAo0EcDfZAqFwq9Go7Exzg6NMStBEDxO883VA0R0e9QRTerwtOS5x3E4tPpjNAoeTrp151xt1DFLg8ilQFKwmSmQtptx1v8DVbAxMP//OLQAAAAASUVORK5CYII=") no-repeat;\n          background-size: 100% 100%;\n          animation: loading-circle 2s linear infinite;\n        }\n        .report-button {\n          position: absolute;\n          bottom: 60px;\n          cursor: pointer;\n        }\n        .gray-button {\n          position: absolute;\n          bottom: 60px;\n        }\n        .device-testing-report {\n          display: flex;\n          flex-wrap: wrap;\n          flex-direction: column;\n          align-items: center;\n        }\n        .device-testing-report .testing-title {\n          display: flex;\n          font-size: 34px;\n          justify-content: center;\n          margin-top: 30px;\n          color: rgba(32, 30, 30, 0.8980392157);\n        }\n        .device-testing-report .device-report-list {\n          display: block;\n          width: 100%;\n          margin-top: 10px;\n        }\n        .device-testing-report .device-report-list .device-report {\n          display: flex;\n          width: 70%;\n          margin: 20px auto 0;\n          justify-content: space-between;\n        }\n        .device-testing-report .device-report-list .device-report .device-info {\n          display: flex;\n          width: 80%;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .report-icon {\n          margin-right: 20px;\n          justify-content: center;\n          font-size: 22px;\n          line-height: 22px;\n          color: #515151;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .report-icon svg {\n          width: 24px;\n          height: 24px;\n        }\n        .device-testing-report .device-report-list .device-report .device-info .device-name {\n          width: 280px;\n          height: 24px;\n          line-height: 24px;\n          display: block;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          text-align: left;\n        }\n        .device-testing-report .device-report-list .device-report .green {\n          color: green;\n        }\n        .device-testing-report .device-report-list .device-report .red {\n          color: red;\n        }\n        .device-testing-report .device-report-footer {\n          width: 50%;\n          display: flex;\n          justify-content: space-between;\n          position: absolute;\n          bottom: 36px;\n        }\n      }\n`,style1_default=sheet,USER_AGENT=window.navigator&&window.navigator.userAgent||"",webkitVersionMap=/AppleWebKit\/([\d.]+)/i.exec(USER_AGENT),appleWebkitVersion=webkitVersionMap?parseFloat(webkitVersionMap.pop()||"0"):null,IS_IPAD=/iPad/i.test(USER_AGENT),IS_IPHONE=/iPhone/i.test(USER_AGENT)&&!IS_IPAD,IS_IPOD=/iPod/i.test(USER_AGENT),IS_IOS=IS_IPHONE||IS_IPAD||IS_IPOD,IOS_VERSION=IS_IOS&&function(){const e=USER_AGENT.match(/OS (\d+)_/i);return e&&e[1]?e[1]:null}(),IS_ANDROID=/Android/i.test(USER_AGENT),ANDROID_VERSION=IS_ANDROID&&function(){const e=USER_AGENT.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;const t=e[1]&&parseFloat(e[1]),V=e[2]&&parseFloat(e[2]);return t&&V?parseFloat(`${e[1]}.${e[2]}`):t||null}(),IS_OLD_ANDROID=IS_ANDROID&&/webkit/i.test(USER_AGENT)&&ANDROID_VERSION<2.3,IS_NATIVE_ANDROID=IS_ANDROID&&ANDROID_VERSION<5&&appleWebkitVersion<537,IS_FIREFOX=/Firefox/i.test(USER_AGENT),FIREFOX_VERSION=IS_FIREFOX&&function(){const e=USER_AGENT.match(/Firefox\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),IS_EDGE=/Edge\//i.test(USER_AGENT),EDGE_VERSION=IS_EDGE&&function(){const e=USER_AGENT.match(/Edge\/(\d+)/i);if(e&&e[1])return e[1]}(),IS_EDG=/Edg\//i.test(USER_AGENT),EDG_VERSION=IS_EDG&&function(){const e=USER_AGENT.match(/Edg\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),IS_SOGOUM=/SogouMobileBrowser\//i.test(USER_AGENT),SOGOUM_VERSION=IS_SOGOUM&&function(){const e=USER_AGENT.match(/SogouMobileBrowser\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),IS_SOGOU=/MetaSr\s/i.test(USER_AGENT),SOGOU_VERSION=IS_SOGOU&&function(){const e=USER_AGENT.match(/MetaSr(\s\d+(\.\d+)+)/);return e&&e[1]?parseFloat(e[1]):null}(),IS_TBS=/TBS\/\d+/i.test(USER_AGENT),TBS_VERSION=IS_TBS&&function(){const e=USER_AGENT.match(/TBS\/(\d+)/i);if(e&&e[1])return e[1]}(),IS_XWEB=/XWEB\/\d+/i.test(USER_AGENT),XWEB_VERSION=IS_XWEB&&function(){const e=USER_AGENT.match(/XWEB\/(\d+)/i);if(e&&e[1])return e[1]}(),IS_IE8=/MSIE\s8\.0/.test(USER_AGENT),IS_IE=/MSIE\/\d+/i.test(USER_AGENT),IE_VERSION=IS_IE&&function(){const e=/MSIE\s(\d+)\.\d/.exec(USER_AGENT);let t=e&&parseFloat(e[1]);return!t&&/Trident\/7.0/i.test(USER_AGENT)&&/rv:11.0/.test(USER_AGENT)&&(t=11),t}(),IS_PC_WECHAT=/windowswechat/i.test(USER_AGENT),IS_WECHAT=/(micromessenger|webbrowser)/i.test(USER_AGENT),WECHAT_VERSION=IS_WECHAT&&function(){const e=USER_AGENT.match(/MicroMessenger\/(\d+)/i);if(e&&e[1])return e[1]}(),IS_X5MQQB=!IS_TBS&&/MQQBrowser\/\d+/i.test(USER_AGENT)&&/COVC\/\d+/i.test(USER_AGENT),IS_MQQB=!IS_TBS&&/MQQBrowser\/\d+/i.test(USER_AGENT)&&!/COVC\/\d+/i.test(USER_AGENT),MQQB_VERSION=(IS_MQQB||IS_X5MQQB)&&function(){const e=USER_AGENT.match(/ MQQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_WQQB=!IS_TBS&&/ QQBrowser\/\d+/i.test(USER_AGENT),WQQB_VERSION=IS_WQQB&&function(){const e=USER_AGENT.match(/ QQBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_MACQQB=!IS_TBS&&/QQBrowserLite\/\d+/i.test(USER_AGENT),MACQQB_VERSION=IS_MACQQB&&function(){const e=USER_AGENT.match(/QQBrowserLite\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_IPADQQB=!IS_TBS&&/MQBHD\/\d+/i.test(USER_AGENT),IPADQQB_VERSION=IS_IPADQQB&&function(){const e=USER_AGENT.match(/MQBHD\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_WIN=/Windows/i.test(USER_AGENT),IS_MAC=!IS_IOS&&/MAC OS X/i.test(USER_AGENT),IS_LINUX=!IS_ANDROID&&/Linux/i.test(USER_AGENT),IS_WX=/MicroMessenger/i.test(USER_AGENT),IS_UCBROWSER=/UCBrowser/i.test(USER_AGENT),IS_ELECTRON=/Electron/i.test(USER_AGENT),IS_MIBROWSER=/MiuiBrowser/i.test(USER_AGENT),MI_VERSION=IS_MIBROWSER&&function(){const e=USER_AGENT.match(/MiuiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_HUAWEIBROWSER=/HuaweiBrowser/i.test(USER_AGENT),HUAWEI_VERSION=IS_HUAWEIBROWSER&&function(){const e=USER_AGENT.match(/HuaweiBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_SAMSUNGBROWSER=/SamsungBrowser/i.test(USER_AGENT),SAMSUNG_VERSION=IS_SAMSUNGBROWSER&&function(){const e=USER_AGENT.match(/SamsungBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_OPPOBROWSER=/HeyTapBrowser/i.test(USER_AGENT),OPPO_VERSION=IS_OPPOBROWSER&&function(){const e=USER_AGENT.match(/HeyTapBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_VIVOBROWSER=/VivoBrowser/i.test(USER_AGENT),VIVO_VERSION=IS_VIVOBROWSER&&function(){const e=USER_AGENT.match(/VivoBrowser\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_CHROME_ONLY=/Chrome/i.test(USER_AGENT),IS_CHROME=!IS_EDGE&&!IS_SOGOU&&!IS_SOGOUM&&!IS_TBS&&!IS_XWEB&&!IS_EDG&&!IS_WQQB&&!IS_MIBROWSER&&!IS_HUAWEIBROWSER&&!IS_SAMSUNGBROWSER&&!IS_OPPOBROWSER&&!IS_VIVOBROWSER&&/Chrome/i.test(USER_AGENT),CHROME_MAJOR_VERSION=IS_CHROME&&function(){const e=USER_AGENT.match(/Chrome\/(\d+)/);return e&&e[1]?parseFloat(e[1]):null}(),CHROME_VERSION=IS_CHROME&&function(){const e=USER_AGENT.match(/Chrome\/([\d.]+)/);return e&&e[1]?e[1]:null}(),IS_SAFARI=!IS_CHROME_ONLY&&!IS_MQQB&&!IS_X5MQQB&&!IS_MACQQB&&!IS_IPADQQB&&/Safari/i.test(USER_AGENT),SAFARI_VERSION=IS_SAFARI&&function(){const e=USER_AGENT.match(/Version\/([\d.]+)/);return e&&e[1]?e[1]:null}(),BROWSER_VERSION=IS_CHROME?`Chrome/${CHROME_VERSION}`:IS_SAFARI?`Safari/${SAFARI_VERSION}`:"NotSupportedBrowser",IS_LOCAL="file:"===location.protocol||"localhost"===location.hostname||/^\d+\.\d+\.\d+\.\d+$/.test(location.hostname),OSNameMap=new Map([[IS_ANDROID,"Android"],[IS_IOS,"iOS"],[IS_WIN,"Windows"],[IS_MAC,"MacOS"],[IS_LINUX,"Linux"]]),getOSType=()=>{let e="unknown";return OSNameMap.get(!0)&&(e=OSNameMap.get(!0)),e},browserInfoMap=new Map([[IS_FIREFOX,["Firefox",FIREFOX_VERSION]],[IS_EDG,["Edg",EDG_VERSION]],[IS_CHROME,["Chrome",CHROME_VERSION]],[IS_SAFARI,["Safari",SAFARI_VERSION]],[IS_TBS,["TBS",TBS_VERSION]],[IS_XWEB,["XWEB",XWEB_VERSION]],[IS_WECHAT&&IS_IPHONE,["WeChat",WECHAT_VERSION]],[IS_WQQB,["QQ(Win)",WQQB_VERSION]],[IS_MQQB,["QQ(Mobile)",MQQB_VERSION]],[IS_X5MQQB,["QQ(Mobile X5)",MQQB_VERSION]],[IS_MACQQB,["QQ(Mac)",MACQQB_VERSION]],[IS_IPADQQB,["QQ(iPad)",IPADQQB_VERSION]],[IS_MIBROWSER,["MI",MI_VERSION]],[IS_HUAWEIBROWSER,["HW",HUAWEI_VERSION]],[IS_SAMSUNGBROWSER,["Samsung",SAMSUNG_VERSION]],[IS_OPPOBROWSER,["OPPO",OPPO_VERSION]],[IS_VIVOBROWSER,["VIVO",VIVO_VERSION]],[IS_EDGE,["EDGE",EDGE_VERSION]],[IS_SOGOUM,["SogouMobile",SOGOUM_VERSION]],[IS_SOGOU,["Sogou",SOGOU_VERSION]]]),getBrowserInfo=()=>{let e="unknown",t="unknown";return browserInfoMap.get(!0)&&([e,t]=browserInfoMap.get(!0)),{name:e,version:t}},UA=navigator.userAgent,getDisplayResolution=()=>{let e;if(screen.width){e={width:screen.width?screen.width*window.devicePixelRatio:"",height:screen.height?screen.height*window.devicePixelRatio:""}}return e},getHardwareConcurrency=()=>{var e;return null==(e=window.navigator)?void 0:e.hardwareConcurrency},getBrowserLanguage=navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||"en",isScreenCaptureAPISupported=()=>{var e;return!!(null==(e=navigator.mediaDevices)?void 0:e.getDisplayMedia)},isWebAudioSupported=()=>{const e={isSupported:!1},t=["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"];for(let V=0;V<t.length;V++)if(t[V]in window){e.isSupported=!0;break}return e.isSupported},isWebSocketSupported=()=>"WebSocket"in window&&2===window.WebSocket.CLOSING,isBrowserSupported=()=>!IS_UCBROWSER&&!IS_EDGE&&(!(IS_EDG&&"number"==typeof EDG_VERSION&&EDG_VERSION<80)&&!(IS_FIREFOX&&FIREFOX_VERSION&&FIREFOX_VERSION<56)),isWebRTCSupported=()=>["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter((e=>e in window)).length>0,isCanvasCapturingSupported=()=>{let e=!1;return["captureStream","mozCaptureStream","webkitCaptureStream"].forEach((t=>{"undefined"!=typeof document&&"function"==typeof document.createElement&&!e&&t in document.createElement("canvas")&&(e=!0)})),e},isVideoCapturingSupported=()=>{let e=!1;return["captureStream","mozCaptureStream","webkitCaptureStream"].forEach((t=>{"undefined"!=typeof document&&"function"==typeof document.createElement&&!e&&t in document.createElement("video")&&(e=!0)})),e},isRTPSenderReplaceTracksSupported=()=>{let e=!1;return IS_FIREFOX&&void 0!==window.mozRTCPeerConnection?"getSenders"in window.mozRTCPeerConnection.prototype&&(e=!0):IS_CHROME&&void 0!==window.webkitRTCPeerConnection&&"getSenders"in window.webkitRTCPeerConnection.prototype&&(e=!0),e},isApplyConstraintsSupported=()=>"undefined"!=typeof MediaStreamTrack&&"applyConstraints"in MediaStreamTrack.prototype,{toString:toString}=Object.prototype,getCore=()=>core,getLog=()=>log,setCore=e=>{core=e},setLog=e=>{log=e},setLanguage=e=>{language="zh"===e||"en"===e?e:navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||"en"},getLanguage=()=>language,clear=()=>{core=null,log=null},isUserMediaSupported=()=>{if(!navigator.mediaDevices)return!1;const e=["getUserMedia","enumerateDevices"];return e.filter((e=>e in navigator.mediaDevices)).length===e.length},getDevices=async(e={video:!1,audio:!1})=>{const t=getCore();if(!t)return[];let V=[];V=e.video&&e.audio?await t.getDevices(t.enums.CheckPermissionType.BOTH):e.video?await t.getDevices(t.enums.CheckPermissionType.CAMERA):e.audio?await t.getDevices(t.enums.CheckPermissionType.MICROPHONE):await t.getDevices(t.enums.CheckPermissionType.NONE);let i=!1,n=!1;const A=[],o=[],r=[],s={};return V.forEach((e=>{const t={};for(const V in e)try{"function"!=typeof e[V]&&(t[V]=e[V])}catch(e){}const V=t.deviceId||"",q=t.label||"",a=t.kind||"";s[V+q+a]||("audio"===t.kind&&(t.kind="audioinput"),"video"===t.kind&&(t.kind="videoinput"),t.deviceId||(t.deviceId=t.id),t.label?("videoinput"!==t.kind||i||(i=!0),"audioinput"!==t.kind||n||(n=!0)):"videoinput"===t.kind?t.label=`Camera ${r.length+1}`:"audioinput"===t.kind?t.label=`Microphone ${A.length+1}`:"audiooutput"===t.kind?t.label=`Speaker ${o.length+1}`:t.label="Please invoke getUserMedia once.","audioinput"===t.kind&&-1===A.indexOf(t)&&A.push(t),"audiooutput"===t.kind&&-1===o.indexOf(t)&&o.push(t),"videoinput"===t.kind&&-1===r.indexOf(t)&&r.push(t),s[t.deviceId+t.label+t.kind]=t)})),{hasWebCamPermission:i,hasMicrophonePermission:n,cameras:r,speakers:o,microphones:A}};async function UIGetCameras(e){return{cameraList:(await getDevices({video:!0,audio:!1})).cameras}}var UIGetMicrophones=async e=>({microphoneList:(await getDevices({video:!1,audio:!0})).microphones});async function UIGetSpeakers(){return(await getDevices({video:!1,audio:!0})).speakers}var encodeSupportStatus=async()=>{let e={sdp:"",type:"offer"},t=!1,V=!1;try{const i=new RTCPeerConnection,n=document.createElement("canvas");n.getContext("2d");const A=n.captureStream(0);return i.addTrack(A.getVideoTracks()[0],A),e=await i.createOffer(),e&&e.sdp&&(-1!==e.sdp.toLowerCase().indexOf("h264")&&(t=!0),-1!==e.sdp.toLowerCase().indexOf("vp8")&&(V=!0)),i.close(),{isH264EncodeSupported:t,isVp8EncodeSupported:V}}catch(e){return{isH264EncodeSupported:!1,isVp8EncodeSupported:!1}}},decodeSupportStatus=async()=>{let e={sdp:"",type:"offer"},t=!1,V=!1;try{const i=new RTCPeerConnection;return e=await i.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0}),e&&e.sdp&&(-1!==e.sdp.toLowerCase().indexOf("h264")&&(t=!0),-1!==e.sdp.toLowerCase().indexOf("vp8")&&(V=!0)),i.close(),{isH264DecodeSupported:t,isVp8DecodeSupported:V}}catch(e){return{isH264DecodeSupported:!1,isVp8DecodeSupported:!1}}};async function isHWEnabled(){return new Promise((async(e,t)=>{try{let V=function(){clearInterval(d),clearInterval(A),clearTimeout(p),r.close(),s.close(),o.getTracks().forEach((e=>e.stop()))};const i=document.createElement("canvas"),n=i.getContext("2d");i.width=640,i.height=480;const A=setInterval((()=>{n&&n.fillText("test",0,0)}),33),o=i.captureStream(15),r=new RTCPeerConnection({}),s=new RTCPeerConnection({});r.addEventListener("icecandidate",(e=>{s.addIceCandidate(e.candidate)})),s.addEventListener("icecandidate",(e=>{r.addIceCandidate(e.candidate)}));const q=RTCRtpSender.getCapabilities("video"),a=(q?q.codecs:[]).find((e=>"video/H264"===e.mimeType));r.addTransceiver(o.getVideoTracks()[0],{direction:"sendonly",streams:[o],sendEncodings:[{maxBitrate:5e5}]}),s.addTransceiver("video",{direction:"recvonly"}),a&&r.getTransceivers()[0].setCodecPreferences([a].filter((e=>void 0!==e)));const c=await r.createOffer();await r.setLocalDescription(c),await s.setRemoteDescription(c);const l=await s.createAnswer();await s.setLocalDescription(l),await r.setRemoteDescription(l);let d=-1,p=-1;p=setTimeout((()=>{V(),e(!1)}),3e4),d=setInterval((async()=>{(await r.getStats()).forEach((i=>{"outbound-rtp"===i.type&&"video"===i.mediaType&&(void 0===i.encoderImplementation&&(V(),t(new Error("your browser does not support to detect HW acceleration enabled."))),"ExternalEncoder"===i.encoderImplementation&&(V(),e(!0)))}))}),500)}catch(e){t(e)}}))}var RTCDetect={system:null,APISupported:null,devices:null,codecsSupported:null,report:null,networkInfo:null,TRTCInitDone:!1,mirror:!1,allowSkip:!0,async getReportAsync(){return this.getSystem(),this.getAPISupported(),await this.getCodecAsync(),await this.getDevicesAsync(),this.report={system:this.system,APISupported:this.APISupported,codecsSupported:this.codecsSupported,devices:this.devices},this.report},async isTRTCSupported(){await this.getReportAsync();const{isWebRTCSupported:e,isUserMediaSupported:t,isWebSocketSupported:V}=this.APISupported;if(!e)return{result:!1,reason:"you browser does not support WebRTC."};if(!V)return{result:!1,reason:"you browser does not support WebSocket."};if(!t)return{result:!1,reason:"you browser does not support getUserMedia."};const{isH264EncodeSupported:i,isVp8EncodeSupported:n,isH264DecodeSupported:A,isVp8DecodeSupported:o}=this.codecsSupported;return i||n?A||o?isBrowserSupported()?{result:!0}:{result:!1,reason:"your browser does not qualify, it is recommended to use Chrome and firefox."}:{result:!1,reason:`isH264DecodeSupported: ${A} isVp8DecodeSupported: ${o} `}:{result:!1,reason:`isH264EncodeSupported: ${i} isVp8EncodeSupported: ${n} `}},async getCodecAsync(){const{isH264EncodeSupported:e,isVp8EncodeSupported:t}=await encodeSupportStatus(),{isH264DecodeSupported:V,isVp8DecodeSupported:i}=await decodeSupportStatus();return this.codecsSupported={isH264EncodeSupported:e,isVp8EncodeSupported:t,isH264DecodeSupported:V,isVp8DecodeSupported:i},this.codecsSupported},async getDevicesAsync(){const{hasWebCamPermission:e,hasMicrophonePermission:t,cameras:V,microphones:i,speakers:n}=await getDevices();return this.devices={cameras:V,microphones:i,speakers:n||[],hasWebCamPermission:e,hasMicrophonePermission:t},this.devices},getSystem(){return this.system={UA:UA,OS:getOSType(),browser:getBrowserInfo(),displayResolution:getDisplayResolution(),hardwareConcurrency:getHardwareConcurrency(),language:getBrowserLanguage},this.system},getAPISupported(){return this.APISupported={isUserMediaSupported:isUserMediaSupported(),isWebRTCSupported:isWebRTCSupported(),isWebSocketSupported:isWebSocketSupported(),isWebAudioSupported:isWebAudioSupported(),isScreenCaptureAPISupported:isScreenCaptureAPISupported(),isCanvasCapturingSupported:isCanvasCapturingSupported(),isVideoCapturingSupported:isVideoCapturingSupported(),isRTPSenderReplaceTracksSupported:isRTPSenderReplaceTracksSupported(),isApplyConstraintsSupported:isApplyConstraintsSupported()},this.APISupported},isHardWareAccelerationEnabled:async()=>await isHWEnabled()},sdk_default=RTCDetect;function isOnline(){const e=`https://web.sdk.qcloud.com/trtc/electron/download/resources/media/TestSpeaker.mp3?t=${(new Date).getTime()}`;return Promise.race([fetch(e,{mode:"cors"}),new Promise(((e,t)=>setTimeout((()=>t(!1)),3e3)))]).then((e=>{if(!e.ok)throw new Error("网络错误");return!0})).catch((()=>!1))}var NETWORK_QUALITY={0:"未知",1:"极佳",2:"较好",3:"一般",4:"差",5:"极差",6:"断开"},NETWORK_QUALITY_ENGLISH={0:"Unknown",1:"Excellent",2:"Good",3:"Average",4:"Poor",5:"Very Poor",6:"Disconnected"},handleGetUserMediaError=e=>{const t=getLanguage().indexOf("zh")>-1,V=getLog();switch(V.error("getUserMedia error",e),e.name){case"NotReadableError":return void(t?V.error("暂时无法访问摄像头/麦克风，请确保系统授予当前浏览器摄像头/麦克风权限，并且没有其他应用占用摄像头/麦克风"):V.error("Cannot access camera/microphone temporarily. Please make sure the system has granted the current browser permission to access the camera/microphone and no other application is using the camera/microphone"));case"NotAllowedError":return void(t?V.error("用户/系统已拒绝授权访问摄像头或麦克风"):V.error("User/system has denied access to camera/microphone"));case"NotFoundError":return void(t?V.error("找不到摄像头或麦克风设备"):V.error("Cannot find camera or microphone device"));case"OverConstrainedError":return void(t?V.error("采集属性设置错误，如果您指定了 cameraId/microphoneId，请确保它们是一个有效的非空字符串"):V.error("Capture attribute setting error. If you specified cameraId/microphoneId, please make sure they are valid non-empty strings"));default:return void(t?V.error("初始化本地流时遇到未知错误, 请重试"):V.error("Unknown error encountered when initializing local stream, please try again"))}},_deviceConnect=class e extends r4{constructor(){super(),this.progress=0,this.remindAnimation=!1;const t=getLanguage();this.isChinese=t.indexOf("zh")>-1,this.showConnectResult=!1,this.connectResult={info:this.isChinese?"连接出错，请重试":"Connection error, please try again",success:!1,remind:this.isMobile?this.isChinese?e.mobileFailAttention:e.mobileFailAttentionEng:this.isChinese?e.deviceFailAttention:e.deviceFailAttentionEng},this.envresult=sdk_default.getSystem(),this.isMobile="iOS"===this.envresult.OS||"Android"===this.envresult.OS,this.isFirefoxOrSafariOrIOS="Firefox"===this.envresult.browser.name||"iOS"===this.envresult.OS||"Safari"===this.envresult.browser.name,this.deviceState={},this.showRemind=!1,this.startTime=(new Date).getTime(),this.attachShadow({mode:"open"})}render(){return x`
           <style>${style1_default}</style>
           <div class="device-connect">
           <div class="testing-title">${this.isChinese?"设备连接":"Device Connection"}</div>
           <div class="testing-prepare-info"></div>
           <div class="device-display">
             <!-- 摄像头连接图标 -->
                <div class="camerasvg">
                  <span class="device">
                    <svg t="1630397874793" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="958" width="24" height="24"><path d="M489.244444 0a460.8 460.8 0 1 1 0 921.6A460.8 460.8 0 0 1 489.244444 0z m0 204.8a256 256 0 1 0 0 512 256 256 0 0 0 0-512z" opacity=".8" p-id="959"></path><path d="M489.244444 460.8m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" opacity=".8" p-id="960"></path><path d="M120.604444 952.32a368.64 61.44 0 1 0 737.28 0 368.64 61.44 0 1 0-737.28 0Z" opacity=".8" p-id="961"></path></svg>
                  </span>
                </div>
             <!-- 麦克风连接图标 -->
            <div class="microphonesvg">
              <span class="device">
                <svg t="1630397938861" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1205" width="24" height="24"><path d="M841.551448 434.423172A41.666207 41.666207 0 0 1 882.758621 476.548414c0 194.701241-144.454621 355.469241-329.551449 376.514207v86.722207h164.758069a41.666207 41.666207 0 0 1 41.207173 42.089931A41.666207 41.666207 0 0 1 717.965241 1024H306.034759A41.666207 41.666207 0 0 1 264.827586 981.874759a41.666207 41.666207 0 0 1 41.207173-42.089931h164.758069v-86.722207C285.696 832.052966 141.241379 671.249655 141.241379 476.548414a41.666207 41.666207 0 0 1 41.207173-42.125242 41.666207 41.666207 0 0 1 41.171862 42.125242c0 162.78069 129.129931 294.770759 288.379586 294.770758l8.827586-0.141241c155.153655-4.766897 279.552-134.850207 279.552-294.629517a41.666207 41.666207 0 0 1 41.171862-42.125242zM512 0c119.419586 0 216.275862 88.770207 216.275862 198.232276v317.228138c0 106.990345-92.513103 194.206897-208.154483 198.091034l-8.121379 0.141242c-119.419586 0-216.275862-88.770207-216.275862-198.232276V198.232276c0-106.990345 92.513103-194.206897 208.154483-198.091035L512 0z" opacity=".8" p-id="1206"></path></svg>
              </span>
            </div>
           <!-- 扬声器连接图标 -->
             <div class="speakersvg">
               <span class="device">
                 <svg t="1629186923749" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2923" width="24" height="24"><path d="M640 181.333333c0-46.037333-54.357333-70.4-88.746667-39.850666L359.552 311.850667a32 32 0 0 1-21.248 8.106666H181.333333A96 96 0 0 0 85.333333 415.957333v191.872a96 96 0 0 0 96 96h157.013334a32 32 0 0 1 21.248 8.106667l191.616 170.410667c34.389333 30.549333 88.789333 6.144 88.789333-39.850667V181.333333z m170.325333 70.272a32 32 0 0 1 44.757334 6.698667A424.917333 424.917333 0 0 1 938.666667 512a424.96 424.96 0 0 1-83.626667 253.696 32 32 0 0 1-51.413333-38.058667A360.917333 360.917333 0 0 0 874.666667 512a360.917333 360.917333 0 0 0-71.04-215.637333 32 32 0 0 1 6.698666-44.757334zM731.434667 357.12a32 32 0 0 1 43.392 12.928c22.869333 42.24 35.84 90.666667 35.84 141.994667a297.514667 297.514667 0 0 1-35.84 141.994666 32 32 0 0 1-56.32-30.464c17.92-33.152 28.16-71.082667 28.16-111.530666s-10.24-78.378667-28.16-111.530667a32 32 0 0 1 12.928-43.392z" opacity=".8" p-id="2924"></path></svg>
               </span>
             </div>
             <!-- 网络连接图标 -->
             <div class="networksvg">
                <span class="device">
                  <svg t="1630400570252" class="icon" viewBox="0 0 1291 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1640" width="24" height="24"><path d="M992.211478 583.68A449.758609 449.758609 0 0 0 650.017391 426.295652c-136.904348 0-259.561739 61.039304-342.194087 157.384348a90.156522 90.156522 0 0 0 136.859826 117.359304 269.846261 269.846261 0 0 1 205.334261-94.430608c82.142609 0 155.737043 36.641391 205.334261 94.386087a90.156522 90.156522 0 1 0 136.859826-117.359305zM559.86087 922.134261a90.156522 90.156522 0 1 0 180.313043 0 90.156522 90.156522 0 0 0-180.313043 0z" opacity=".8" p-id="1641"></path><path d="M1253.064348 289.124174A809.316174 809.316174 0 0 0 650.017391 20.613565a809.316174 809.316174 0 0 0-603.046956 268.466087 90.156522 90.156522 0 1 0 127.777391 127.065044l0.311652 0.26713A629.581913 629.581913 0 0 1 650.017391 200.926609c189.395478 0 359.290435 83.389217 474.957913 215.485217l0.267131-0.26713a90.156522 90.156522 0 1 0 127.777391-127.065044z" opacity=".8" p-id="1642"></path></svg>
                </span>
             </div>

             <div  class="outer-progress">
               <div class="inner-progress"></div>
             </div>

           </div>

           <div  class="text gray-text">${this.isChinese?"设备正在连接中，请稍后":"The device is connecting, please wait"}</div>
          
           <div  class="text result"  style="display:none">
             <span></span>
             <div 
             @touchstart=${()=>this.setShowRemind(!0)} 
             @mouseenter=${()=>this.setShowRemind(!0)}
             @touchend=${()=>this.setShowRemind(!1)}
             @mouseleave=${()=>this.setShowRemind(!1)}
             style="display:none" class="error-connect">
              <span class="error-icon">
                <svg t="1626151898274" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3223" width="28" height="28"><path d="M1024 518.314667C1024 794.794667 794.737778 1024 505.685333 1024 229.205333 1024 0 794.737778 0 518.314667 0 229.262222 229.262222 0 505.685333 0 794.737778 0 1024 229.262222 1024 518.314667zM512 256a48.128 48.128 0 0 0-48.753778 51.370667L477.866667 614.4h68.266666l14.620445-307.029333A48.355556 48.355556 0 0 0 512 256z m0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4z" fill="#FF0000" p-id="3224"></path></svg>
              </span>
              ${this.showRemind&&!this.isMobile?x`<div class="connect-attention-info"></div>`:x``}
             </div>
           </div>
              ${this.showRemind&&this.isMobile?x`<div class="connect-attention-info"></div>`:x``}
           <div class="button-container">
             <trtc-custom-button  type='disabled' >${this.isChinese?"开始检测":"Start detection"}</trtc-custom-button>
           </div>
        `}setShowRemind(e){this.showRemind=e,this.requestUpdate()}firstUpdated(){this.getDeviceConnectResult(),this.stepNameList=JSON.parse(this.getAttribute("stepnamelist")),this.judgeList(this.stepNameList);const e=this.shadowRoot.querySelector(".testing-prepare-info");e&&(e.innerHTML=this.prepareInfo())}updated(){if(this.showRemind){const e=this.shadowRoot.querySelector(".connect-attention-info");e&&(e.innerHTML=this.connectResult.remind)}}hasCameraDetect(){return this.stepNameList.indexOf("camera")>=0}hasMicrophoneDetect(){return this.stepNameList.indexOf("microphone")>=0}hasSpeakerDetect(){return this.stepNameList.indexOf("speaker")>=0}hasNetworkDetect(){return this.stepNameList.indexOf("network")>=0}showReconnectButton(){const{deviceState:e}=this;return this.showConnectResult&&!(e.hasCameraConnect&&e.hasMicrophoneConnect&&e.hasSpeakerConnect&&e.hasNetworkConnect)}showStartDetectButton(){const{deviceState:e}=this;return this.showConnectResult&&e.hasCameraConnect&&e.hasMicrophoneConnect&&e.hasSpeakerConnect&&e.hasNetworkConnect}handler(t){const V=this.shadowRoot.querySelector(".inner-progress");t||(this.progressInterval&&clearInterval(this.progressInterval),this.progressInterval=setInterval((()=>{if(this.progress+=10,V.style.transform=`translateX(${this.progress}%)`,100===this.progress){this.progress=0,V.style.transform="translateX(0%)";const t=new CustomEvent("remind-changed",{detail:this.remindAnimation});this.dispatchEvent(t),setTimeout((()=>{this.remindAnimation&&this.isMobile&&(this.isChinese?alert(e.mobileFailAttention.replace(/<br>/g,"\n")):alert(e.mobileFailAttentionEng.replace(/<br>/g,"\n")))}),200),this.showConnectResult=!0,this.setAttribute("showconnectresult",this.showConnectResult),clearInterval(this.progressInterval)}}),150))}prepareInfo(){return this.isChinese?`设备检测前请确认设备连接了${this.hasCameraDetect()?"摄像头":""}${this.hasMicrophoneDetect()?"、麦克风":""}${this.hasSpeakerDetect()?"、扬声器":""}${this.hasNetworkDetect()?"和网络":""}`:`Make sure the device is connected before testing the device ${this.hasCameraDetect()?"camera":""}${this.hasMicrophoneDetect()?", microphone":""}${this.hasSpeakerDetect()?", speaker":""}${this.hasNetworkDetect()?" and network":""}`}judgeList(e){const t=this.shadowRoot.querySelector(".camerasvg"),V=this.shadowRoot.querySelector(".microphonesvg"),i=this.shadowRoot.querySelector(".speakersvg"),n=this.shadowRoot.querySelector(".networksvg");e.indexOf("camera")>=0?t.style.display="block":t.style.display="none",e.indexOf("microphone")>=0?V.style.display="block":V.style.display="none",e.indexOf("speaker")>=0?i.style.display="block":i.style.display="none",e.indexOf("network")>=0?n.style.display="block":n.style.display="none"}async getDeviceConnectResult(){const{cameras:e,microphones:t,speakers:V}=await getDevices();sdk_default.devices={camera:e,microphone:t,speaker:V};const i=e.length>0,n=t.length>0,A=!this.hasSpeakerDetect()||(this.isFirefoxOrSafariOrIOS||V.length>0),o=!this.hasNetworkDetect()||await isOnline();let r={hasCameraDevice:i,hasMicrophoneDevice:n,hasSpeakerDevice:A,hasNetworkConnect:o,hasCameraConnect:!1,hasMicrophoneConnect:!1,hasSpeakerConnect:A};this.deviceState=r,this.remindAnimation=!1,this.connectResult=this.getDeviceConnectInfo(r);const s=[];i&&s.push(new Promise(((e,t)=>{navigator.mediaDevices.getUserMedia({video:!0,audio:!1}).then((t=>{this.remindAnimation=!1,r=__spreadProps(__spreadValues({},r),{hasCameraConnect:!0}),this.deviceState=r,this.connectResult=this.getDeviceConnectInfo(r),t.getTracks()[0].stop(),e()})).catch((t=>{handleGetUserMediaError(t),e()}))}))),n&&s.push(new Promise(((e,t)=>{navigator.mediaDevices.getUserMedia({video:!1,audio:!0}).then((t=>{this.remindAnimation=!1,r=__spreadProps(__spreadValues({},r),{hasMicrophoneConnect:n}),this.deviceState=r,this.connectResult=this.getDeviceConnectInfo(r),t.getTracks()[0].stop(),e()})).catch((t=>{handleGetUserMediaError(t),e()}))}))),await Promise.all(s),this.handler(this.showConnectResult)}getDeviceConnectInfo(t){this.remindAnimation=!1;let V="连接出错，请重试";return this.isChinese||(V="Connection error, please try again"),t.hasCameraConnect&&t.hasMicrophoneConnect&&t.hasSpeakerConnect&&t.hasNetworkConnect?(V=this.isChinese?this.hasNetworkDetect?"设备及网络连接成功，请开始设备检测":"设备连接成功，请开始设备检测":this.hasNetworkDetect?"Device and network connection are successful, start device detection":"Device connection successful, start device detection",{info:V,success:!0}):t.hasCameraDevice&&t.hasMicrophoneDevice&&t.hasSpeakerDevice?t.hasCameraConnect&&t.hasMicrophoneConnect?t.hasNetworkConnect?{info:V,success:!1}:(V=this.isChinese?"网络连接失败，请检查网络连接":"Network connection failed, please check network connection",{info:V,success:!1,remind:this.isChinese?e.networkFailAttention:e.networkFailAttentionEng}):(V=this.isChinese?t.hasNetworkConnect?"请允许浏览器及网页访问摄像头/麦克风设备":"请允许浏览器及网页访问摄像头/麦克风设备，并检查网络连接":t.hasNetworkConnect?"Please allow browser and web access to camera/microphone devices":"Please allow browser and web access to camera/microphone devices, and check the network connection",this.remindAnimation=!0,{info:V,success:!1,remind:this.isMobile?this.isChinese?e.mobileFailAttention:e.mobileFailAttentionEng:this.isChinese?e.deviceFailAttention:e.deviceFailAttentionEng}):(V=this.isChinese?`检测到${t.hasCameraDevice?"":"【摄像头】"}${t.hasMicrophoneDevice?"":"【麦克风】"}${t.hasSpeakerDevice?"":"【扬声器】"}设备不存在，请检查设备连接`:`No ${t.hasCameraDevice?"":"【camera】"}${t.hasMicrophoneDevice?"":"【microphone】"}${t.hasSpeakerDevice?"":"【speaker】"} device detected, please check the device connection`,{info:V,success:!1})}handleReset(){this.progress=0,this.showConnectResult=!1,this.setAttribute("showconnectresult",this.showConnectResult)}async attributeChangedCallback(e,t,V){if("showconnectresult"===e)if("true"===V){const e=this.shadowRoot.querySelector(".camerasvg"),t=this.shadowRoot.querySelector(".microphonesvg"),V=this.shadowRoot.querySelector(".speakersvg"),i=this.shadowRoot.querySelector(".networksvg"),n=this.shadowRoot.querySelector(".outer-progress"),A=this.shadowRoot.querySelector("trtc-custom-button");if(this.deviceState.hasCameraConnect?(e.classList.remove("connect-fail"),e.classList.add("connect-success")):e.classList.add("connect-fail"),this.deviceState.hasMicrophoneConnect?(t.classList.remove("connect-fail"),t.classList.add("connect-success")):t.classList.add("connect-fail"),this.deviceState.hasSpeakerConnect?(V.classList.add("connect-success"),V.classList.remove("connect-fail")):V.classList.add("connect-fail"),this.deviceState.hasNetworkConnect?(i.classList.add("connect-success"),i.classList.remove("connect-fail")):i.classList.add("connect-fail"),this.showReconnectButton()&&(A.setAttribute("type","outlined"),this.isChinese?A.innerHTML="重新连接":A.innerHTML="Reconnect",A.addEventListener("click",(()=>{this.remindAnimation=!1;const e=new CustomEvent("remind-changed",{detail:this.remindAnimation});this.dispatchEvent(e);const t=(new Date).getTime(),V=getCore();V&&V.kvStatManager.addFailedEvent({key:591704,error:t-this.startTime}),this.handleReset()}))),this.showStartDetectButton()&&(A.setAttribute("type","contained"),this.setAttribute("connectstage",1),this.isChinese?A.innerHTML="开始检测":A.innerHTML="Start detection"),this.showConnectResult){n.style.display="none",this.shadowRoot.querySelector(".gray-text").style.display="none";const e=this.shadowRoot.querySelector(".result");e.style.display="block",!0===this.connectResult.success?e.classList.add("green-text"):(e.classList.add("red-text"),this.connectResult.remind&&(this.shadowRoot.querySelector(".error-connect").style.display="inline-block")),e.querySelector("span").innerHTML=this.connectResult.info}}else if("false"===V){const e=this.shadowRoot.querySelector(".camerasvg"),t=this.shadowRoot.querySelector(".microphonesvg"),V=this.shadowRoot.querySelector(".speakersvg"),i=this.shadowRoot.querySelector(".networksvg"),n=this.shadowRoot.querySelector(".outer-progress"),A=this.shadowRoot.querySelector("trtc-custom-button");n.style.display="block",this.shadowRoot.querySelector(".result").classList.remove("green-text"),this.shadowRoot.querySelector(".result").classList.remove("red-text"),e.classList.remove("connect-fail"),e.classList.remove("connect-success"),t.classList.remove("connect-fail"),t.classList.remove("connect-success"),V.classList.remove("connect-fail"),V.classList.remove("connect-success"),i.classList.remove("connect-fail"),i.classList.remove("connect-success"),A.setAttribute("type","disabled"),this.isChinese?A.innerHTML="开始检测":A.innerHTML="Start detection",this.getDeviceConnectResult(),this.shadowRoot.querySelector(".gray-text").style.display="block";this.shadowRoot.querySelector(".result").style.display="none",this.shadowRoot.querySelector(".error-connect").style.display="none"}}};__publicField(_deviceConnect,"observedAttributes",["stepnamelist","showconnectresult"]),__publicField(_deviceConnect,"properties",{showRemind:{type:Boolean},constTime:{type:Number}}),__publicField(_deviceConnect,"deviceFailAttention","1. 若浏览器弹出提示，请选择“允许”<br>2. 若杀毒软件弹出提示，请选择“允许”<br>3. 检查系统设置，允许浏览器访问摄像头及麦克风<br>4. 检查浏览器设置，允许网页访问摄像头及麦克风<br>5. 检查摄像头/麦克风是否正确连接并开启<br>6. 尝试重新连接摄像头/麦克风<br>7. 尝试重启设备后重新检测"),__publicField(_deviceConnect,"deviceFailAttentionEng",'1. If the browser prompts, select "Allow"<br>2. If the antivirus software prompts, select "Allow"<br>3. Check system settings to allow camera and microphone<br>4. Check browser settings to allow camera and microphone<br>5. Check camera/microphone turned on<br>6. Try reconnecting the camera/microphone<br>7. Try restarting the device and retesting'),__publicField(_deviceConnect,"mobileFailAttention","1. 前往手机设置,打开本应用的相机和麦克风权限<br>2. 重新刷新本页并允许相机和麦克风权限<br>"),__publicField(_deviceConnect,"mobileFailAttentionEng","1.  Go to your phone Settings to allow camera and microphone permissions for this app <br>2. Refresh this page and allow camera and microphone permissions <br>"),__publicField(_deviceConnect,"networkFailAttention","1. 请检查设备是否联网<br>2. 请刷新网页后再次检测<br>3. 请尝试更换网络后再次检测"),__publicField(_deviceConnect,"networkFailAttentionEng","1. Check if the device is connected to the network<br>2. Refresh the page and test again<br>3. Try again after changing the network");var deviceConnect=_deviceConnect;customElements.get("trtc-device-connect")||customElements.define("trtc-device-connect",deviceConnect);var DeviceSelect=class extends r4{constructor(){super(),this.deviceList=[],this.activeDevice={},this.activeDeviceId="",this.attachShadow({mode:"open"}),this.core=getCore(),this.freshDeviceList=this.freshDeviceList.bind(this)}async freshDeviceList(){var e;const t=await getDeviceList(this.deviceType),V=t.map((e=>e.deviceId));if(this.deviceList=t,0===t.length)return this.activeDevice=null,this.activeDeviceId="",this.onChange&&this.onChange(this.activeDevice),void this.requestUpdate();this.choseDevice&&V.indexOf(this.choseDevice.deviceId)>=0?(this.activeDevice=t.find((e=>e.deviceId===this.choseDevice.deviceId)),this.activeDeviceId=this.choseDevice.deviceId,this.onChange&&this.onChange(this.activeDevice),this.requestUpdate()):(null==t?void 0:t.length)>0&&(this.activeDevice=t[0],this.activeDeviceId=null==(e=null==t?void 0:t[0])?void 0:e.deviceId,this.onChange&&this.onChange(this.activeDevice),this.requestUpdate())}async firstUpdated(){await this.freshDeviceList(),navigator.mediaDevices.addEventListener("devicechange",this.freshDeviceList)}disconnectedCallback(){navigator.mediaDevices.removeEventListener("devicechange",this.freshDeviceList),this.remove()}handleChange(e){const t=e.target.value,V=getCore();V&&V.kvStatManager.addEnum({key:591713,value:1});const i=this.deviceList.find((e=>e.deviceId===t));this.activeDevice=i,this.activeDeviceId=t,this.onChange&&this.onChange(this.activeDevice),this.requestUpdate()}render(){return x`
      <style>${style1_default}</style>
      <div>
        ${this.deviceList.length>0?x`
          <select class="device-select" value=${this.activeDeviceId} @change=${this.handleChange}>
            ${this.deviceList.map(((e,t)=>x`<option value=${e.deviceId} key=${t}>${e.label}</option>`))}
          </select>
          `:""}
      </div>
    `}};__publicField(DeviceSelect,"properties",{deviceType:{type:String},onChange:{type:Function},choseDevice:{type:Object}}),customElements.get("trtc-device-select")||customElements.define("trtc-device-select",DeviceSelect);var getDeviceList=async e=>{let t=[];switch(e){case"camera":t=(await UIGetCameras()).cameraList;break;case"microphone":t=(await UIGetMicrophones()).microphoneList;break;case"speaker":t=await UIGetSpeakers()}return t},CameraDetector=class extends r4{constructor(){super(),__publicField(this,"handleCameraChange",(async e=>{var t;this.choseDevice=e;const{deviceId:V,label:i}=e;this.shadowRoot.querySelector("video");this.localStream?(null==(t=this.localStream)||t.getTracks().forEach((e=>e.stop())),this.initStream(V)):this.initStream(V),this.cameraID=V,this.cameraLabel=i,this.device=e})),__publicField(this,"handleMirrorChange",(e=>{const t=this.shadowRoot.querySelector("video");e.target.checked?t.classList.add("mirror"):t.classList.remove("mirror")})),this.currentDetector="camera",this.localStream=null,this.cameraLabel="",this.cameraID="",this.choseDevice=null,this.device=null;const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.attachShadow({mode:"open"}),this.core=getCore(),this.log=getLog()}async initStream(e){const t=this.shadowRoot.querySelector("video");t.addEventListener("touchstart",(e=>{e.preventDefault()}));const V={video:{deviceId:{exact:e}},audio:!1};navigator.mediaDevices.getUserMedia(V).then((e=>{this.localStream=e,t.srcObject=e,t.play}))}handleError(){this.log&&this.log.warn("CameraFail",this.device),this.releaseStream();const e=(new Date).getTime();this.core&&this.core.kvStatManager.addFailedEvent({key:591706,error:e-this.constructTime}),this.handleComplete("error",this.cameraLabel,!1,this.device)}handleSuccess(){const e=(new Date).getTime();this.releaseStream(),this.core&&this.core.kvStatManager.addSuccessEvent({key:591705,cost:e-this.constructTime}),this.handleComplete("success",this.cameraLabel,!0,this.device)}firstUpdated(){}updated(e){e.has("activeDetector")&&this.activeDetector===this.currentDetector&&!this.localStream&&this.cameraID&&this.initStream(this.cameraID)}disconnectedCallback(){this.releaseStream(),this.core=null,this.log=null,this.remove()}releaseStream(){var e;null==(e=this.localStream)||e.getTracks().forEach((e=>e.stop())),this.localStream=null}render(){return x`
      <style>${style1_default}</style>
      <div ?if=${this.activeDetector===this.currentDetector} class="testing-body">
        <div class="device-list">
          <span class="device-list-title">${this.isChinese?"摄像头选择":"Camera Selection"}</span>
          <trtc-device-select deviceType="camera" .choseDevice=${this.choseDevice} .onChange=${this.handleCameraChange}></trtc-device-select>
        </div>
        <div class="flex-col">
          <video id="camera-video" class="camera-video ${sdk_default.mirror?"mirror":""}" autoplay  x5-playsinline="true" playsinline="true" webkit-playsinline="true" x-webkit-airplay="true" x5-video-orientation="portraint"></video>
          ${sdk_default.mirror?x`
            <div class="checkbox">
              <input type="checkbox" id="mirror-option" name="mirror" .checked=${sdk_default.mirror} @change=${this.handleMirrorChange}>
              <label for="mirror-option">${this.isChinese?"镜像":"Mirror"}</label>
            </div>
          `:""}
        </div>
        <div class="testing-info-container">
          <div class="testing-info">${this.isChinese?"是否可以清楚地看到自己？":"Can you see yourself clearly?"}</div>
          <div class="button-list">
            <trtc-custom-button type="outlined" @click=${this.handleError}>${this.isChinese?"看不到":"No"}</trtc-custom-button>
            <trtc-custom-button type="contained" @click=${this.handleSuccess}>${this.isChinese?"看得到":"Yes"}</trtc-custom-button>
          </div>
        </div>
      </div>
    `}};__publicField(CameraDetector,"properties",{activeDetector:{type:String},handleComplete:{type:Function},constructTime:{type:Number}}),customElements.get("trtc-camera-detector")||customElements.define("trtc-camera-detector",CameraDetector);var MicDetector=class extends r4{constructor(){super(),__publicField(this,"handleMicrophoneChange",(async e=>{this.choseDevice=e;const{deviceId:t,label:V}=e;this.initStream(t),this.microphoneID=t,this.microphoneLabel=V,this.device=e})),this.currentDetector="microphone",this.localStream=null,this.microphoneID="",this.microphoneLabel="",this.volumeNum=0,this.choseDevice=null,this.timer=null,this.time=null,this.recording=!1,this.recordingTime=0,this.recorder=null,this.chunks=[],this.disconnect=!1,this.device=null;const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.attachShadow({mode:"open"}),this.core=getCore(),this.log=getLog()}updated(e){e.has("activeDetector")&&this.activeDetector===this.currentDetector&&!this.localStream&&this.microphoneID&&this.initStream(this.microphoneID)}handleError(){this.log&&this.log.warn("MicphoneFail",this.device);const e=(new Date).getTime();this.core&&this.core.kvStatManager.addFailedEvent({key:591708,error:e-this.constructTime}),this.handleComplete("error",this.microphoneLabel,!1,this.device),this.releaseStream()}handleSuccess(){const e=(new Date).getTime();this.core&&this.core.kvStatManager.addSuccessEvent({key:591707,cost:e-this.constructTime}),this.handleComplete("success",this.microphoneLabel,!0,this.device),this.releaseStream()}firstUpdated(){}async initStream(e){this.localStream&&this.releaseStream(),this.localStream=await navigator.mediaDevices.getUserMedia({audio:{deviceId:e},video:!1});const t=new AudioContext,V=t.createAnalyser(),i=t.createMediaStreamSource(this.localStream);V.smoothingTimeConstant=.8,V.fftSize=1024,i.connect(V),this.timer=setInterval((()=>{const e=new Uint8Array(V.fftSize);V.getByteTimeDomainData(e);let t=0;const{length:i}=e;for(let V=0;V<i;V++)t+=e[V];const n=(t/i-128)/128;this.volumeNum=Math.round(50*Math.abs(28*n)),this.requestUpdate()}),100)}releaseStream(){var e;null==(e=this.localStream)||e.getTracks().forEach((e=>e.stop())),this.localStream=null,this.timer&&clearInterval(this.timer),this.timer=null}disconnectedCallback(){this.releaseStream(),this.disconnect=!0,this.core=null,this.log=null,this.remove()}render(){return this.disconnect&&this.timer&&clearInterval(this.timer),this.activeDetector===this.currentDetector?x`
          <style>${style1_default}</style>
          <div class="testing-body">
            <div class="device-list">
              <span class="device-list-title">${this.isChinese?"麦克风选择":"Microphone Selection"}</span>
              <trtc-device-select deviceType="microphone" .choseDevice=${this.choseDevice} .onChange=${this.handleMicrophoneChange}></trtc-device-select>
            </div>
            <div class="mic-testing-container">
              <div class="mic-testing-info">${this.isChinese?'对着麦克风说"哈喽"试试～':'Try saying "Hello" into the microphone'}</div>
              <div class="mic-bar-container">
                ${Array.from({length:28},((e,t)=>x`<div class="${`mic-bar ${this.volumeNum>t&&"active"}`}"></div>`))}
              </div>
              <div id="audio-container"></div>
            </div>
            <div class="testing-info-container">
              <div class="testing-info">${this.isChinese?"是否可以看到音量图标跳动？":"Can see the volume icon bounce?"}</div>
              <div class="button-list">
                <trtc-custom-button type="outlined" @click=${this.handleError}>${this.isChinese?"看不到":"No"}</trtc-custom-button>
                <trtc-custom-button type="contained" @click=${this.handleSuccess}>${this.isChinese?"看得到":"Yes"}</trtc-custom-button>
              </div>
            </div>
          </div>
        `:x``}};__publicField(MicDetector,"properties",{activeDetector:{type:String},handleComplete:{type:Function},constructTime:{type:Number}}),customElements.get("trtc-mic-detector")||customElements.define("trtc-mic-detector",MicDetector);var SpeakerCNbase64="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",SpeakerEngbase64="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",SpeakerDetector=class extends r4{constructor(){super(),__publicField(this,"handleSpeakerChange",(async e=>{var t,V;if(this.choseDevice=e,!e)return;if(!this.core.rtcDectection.isSetSinkIdSupported){const e=this.isChinese?"当前设备不支持选择扬声器":"Current device does not support speaker selection";return void alert(e)}const{deviceId:i,label:n}=e;this.audioPlayer&&i&&await(null==(V=(t=this.audioPlayer).setSinkId)?void 0:V.call(t,i)),this.audioPlayer.src=this.audioURL,this.speakerLabel=n,this.device=e})),this.audioPlayer=null,this.currentDetector="speaker",this.speakerLabel="",this.choseDevice=null;const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.audioURL=`data:audio/mp3;base64,${this.isChinese?SpeakerCNbase64:SpeakerEngbase64}`,this.attachShadow({mode:"open"}),this.core=getCore(),this.log=getLog()}firstUpdated(){}updated(e){e.has("activeDetector")&&(this.activeDetector===this.currentDetector&&(this.audioPlayer=this.shadowRoot.getElementById("audio-player")),e.get("activeDetector")===this.currentDetector&&(this.audioPlayer&&!this.audioPlayer.paused&&this.audioPlayer.pause(),this.audioPlayer&&(this.audioPlayer.currentTime=0)))}handleError(){this.log&&this.log.warn("SpeakerFail",this.device);const e=(new Date).getTime();this.core&&this.core.kvStatManager.addFailedEvent({key:591710,error:e-this.constructTime}),this.handleComplete("error",this.speakerLabel,!1,this.device)}handleSuccess(){const e=(new Date).getTime();this.core&&this.core.kvStatManager.addSuccessEvent({key:591709,cost:e-this.constructTime}),this.handleComplete("success",this.speakerLabel,!0,this.device)}disconnectedCallback(){this.core=null,this.log=null,this.remove()}render(){return this.activeDetector===this.currentDetector?x`
        <style>${style1_default}</style>
          <div class="testing-body">
            <div class="device-list">
              <span class="device-list-title">${this.isChinese?"扬声器选择":"Speaker Selection"}</span>
              <trtc-device-select deviceType="speaker" .choseDevice=${this.choseDevice} .onChange=${this.handleSpeakerChange}></trtc-device-select>
            </div>
            <div class="audio-player-container">
              <div class="audio-player-info">${this.isChinese?"请调高设备音量，点击播放下面的音频试试～":"Please turn up the volume on your device and click Play the audio below to try it out"}</div>
              <audio id="audio-player" src=${this.audioURL} controls ></audio>
            </div>
            <div class="testing-info-container">
              <div class="testing-info">${this.isChinese?"是否可以听到声音？":"Can you hear the sound?"}</div>
              <div class="button-list">
                <trtc-custom-button type="outlined" @click=${this.handleError}>${this.isChinese?"听不到":"No"}</trtc-custom-button>
                <trtc-custom-button type="contained" @click=${this.handleSuccess}>${this.isChinese?"听得到":"Yes"}</trtc-custom-button>
              </div>
            </div>
          </div>
        `:x``}};__publicField(SpeakerDetector,"properties",{activeDetector:String,handleComplete:Function,constructTime:{type:Number}}),customElements.get("trtc-speaker-detector")||customElements.define("trtc-speaker-detector",SpeakerDetector);var NetworkDetector=class extends r4{constructor(){super(),this.NETWORK_QUALITY={},this.currentDetector="network",this.detectorInfo={},this.count=15,this.timer=null,this.networkInfo=null,this.core=getCore(),this.log=getLog(),this.testResult={uplinkNetworkQualities:[],downlinkNetworkQualities:[],rttList:[],average:{uplinkNetworkQuality:0,downlinkNetworkQuality:0,rtt:0},result:{rtt:0,quality:0}},this._initializeTRTC(),this.isDownlink=!1,this.networkInfo&&this.networkInfo.downlinkUserId&&this.networkInfo.downlinkUserSig&&(this.isDownlink=!0),this.Enum={0:"0：网络状况未知",1:"1：网络状况极佳",2:"2：网络状况较好",3:"3：网络状况一般",4:"4：网络状况较差",5:"5：网络状况极差",6:"6：网络连接断开"},this.EnumEng={0:"0: unknown",1:"1: excellent",2:"2: good",3:"3: fair",4:"4: poor",5:"5: very poor",6:"6: disconnected"};const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.attachShadow({mode:"open"})}_initializeTRTC(){this.networkInfo=sdk_default.networkInfo,Object.defineProperty(sdk_default,"networkInfo",{set:e=>{this.networkInfo=e},get:()=>this.networkInfo})}firstUpdated(){}updated(e){e.has("activeDetector")&&this.activeDetector===this.currentDetector&&0!==this.count&&(this.count=15,this.getDetectorInfo())}async testUplinkNetworkQuality(){const{sdkAppId:e,uplinkTRTC:t,userId:V,userSig:i,roomId:n}=this.networkInfo;try{await t.exitRoom()}catch(e){}await t.enterRoom({roomId:n||8080,sdkAppId:e,userId:V,userSig:i,scene:"rtc"}),!this.hasStartLocalVideo&&await t.startLocalVideo({option:{profile:"480p"}}),this.setHasStartLocalVideo(),this.hasStartLocalVideo=!0,t.on(this.core.TRTC.EVENT.NETWORK_QUALITY,(e=>{const{uplinkNetworkQuality:t,uplinkRTT:V}=e;this.testResult.uplinkNetworkQualities.push(t),this.testResult.rttList.push(V)}))}async testDownlinkNetworkQuality(){const{sdkAppId:e,downlinkUserId:t,downlinkTRTC:V,downlinkUserSig:i,roomId:n}=this.networkInfo;try{await V.exitRoom()}catch(e){}await V.enterRoom({roomId:n||8080,sdkAppId:e,userId:t,userSig:i,scene:"rtc"}),V.on(this.core.TRTC.EVENT.NETWORK_QUALITY,(e=>{const{downlinkNetworkQuality:t}=e;this.testResult.downlinkNetworkQualities.push(t)}))}async getDetectorInfo(){const e=sdk_default.getSystem(),t=await sdk_default.isTRTCSupported(),V=sdk_default.getAPISupported();this.detectorInfo={system:e.OS,browser:`${e.browser.name} ${e.browser.version}`,TRTCSupport:t.result?"Yes":"No",screenMediaSupport:V.isScreenCaptureAPISupported?"Yes":"No"},this.timer=setInterval((()=>{this.count=this.count-1,this.requestUpdate(),0===this.count&&clearInterval(this.timer)}),1e3),this.testUplinkNetworkQuality(),this.isDownlink&&this.testDownlinkNetworkQuality(),setTimeout((async()=>{const e=this.testResult.uplinkNetworkQualities.filter((e=>e>=1&&e<=5));e.length>0&&(this.testResult.average.uplinkNetworkQuality=Math.ceil(e.reduce(((e,t)=>e+t),0)/e.length));const t=this.testResult.downlinkNetworkQualities.filter((e=>e>=1&&e<=5));t.length>0&&(this.testResult.average.downlinkNetworkQuality=Math.ceil(t.reduce(((e,t)=>e+t),0)/t.length));const V=this.testResult.rttList.filter((e=>e>0));V.length>0&&(this.testResult.average.rtt=Math.ceil(V.reduce(((e,t)=>e+t),0)/V.length)),0===this.testResult.average.uplinkNetworkQuality||this.isDownlink&&0===this.testResult.average.downlinkNetworkQuality?this.testResult.result.quality=0:this.testResult.result.quality=Math.max(this.testResult.average.uplinkNetworkQuality,this.testResult.average.downlinkNetworkQuality),this.testResult.result.rtt=this.testResult.average.rtt,this.requestUpdate(),await this.networkInfo.uplinkTRTC.stopLocalVideo(),this.networkInfo.uplinkTRTC.exitRoom(),this.isDownlink&&this.networkInfo.downlinkTRTC.exitRoom()}),15e3)}disconnectedCallback(){this.timer&&clearInterval(this.timer),this.remove(),this.core=null,this.log=null}async handleSuccess(){this.hasStartLocalVideo&&await this.networkInfo.uplinkTRTC.stopLocalVideo(),this.generateReport();const e=(new Date).getTime();this.core&&this.core.kvStatManager.addSuccessEvent({key:591711,cost:e-this.constructTime}),0!==this.testResult.result.quality&&0!==this.testResult.result.rtt||this.log&&this.log.warn("NetworkFail",this.testResult.average),this.handleComplete("success",this.testResult.result,0!==this.testResult.result.quality)}render(){return this.activeDetector===this.currentDetector?x`
          <style>${style1_default}</style>
          <div class="testing-body">
            <div class="testing-list">
              <div class="testing-item-container">
                <div>${this.isChinese?"操作系统":"Operating System"}</div>
                <div class=${this.detectorInfo.system?"":"network-loading"}>
                  ${this.detectorInfo.system}
                </div>
              </div>
              <div class="testing-item-container">
                <div>${this.isChinese?"浏览器":"Browser"}</div>
                <div class=${this.detectorInfo.browser?"":"network-loading"}>
                  ${this.detectorInfo.browser}
                </div>
              </div>
              <div class="testing-item-container">
                <div>${this.isChinese?"是否支持TRTC":"Whether TRTC is supported"}</div>
                <div class=${this.detectorInfo.TRTCSupport?"":"network-loading"}>
                  ${this.detectorInfo.TRTCSupport}
                </div>
              </div>

              <div class="testing-item-container">
                <div>${this.isChinese?"是否支持屏幕分享":"Whether screen sharing is supported"}</div>
                <div class=${this.detectorInfo.screenMediaSupport?"":"network-loading"}>
                  ${this.detectorInfo.screenMediaSupport}
                </div>
              </div>

              <div class="testing-item-container">
                <div>${this.isChinese?"网络延时":"Network delay"}</div>
                <div class=${this.testResult.average.rtt?"":"network-loading"}>
                  ${this.testResult.average.rtt?`${this.testResult.average.rtt}ms`:""}
                </div>
              </div>
              <div class="testing-item-container">
                <div>${this.isChinese?"网络质量":"Network quality"}</div>
                <div class=${this.testResult.average.uplinkNetworkQuality?"":"network-loading"}>
                  ${this.testResult.average.uplinkNetworkQuality?this.isChinese?this.Enum[this.testResult.result.quality]:this.EnumEng[this.testResult.result.quality]:""}
              </div>
           </div>

          </div>
            ${this.count>0?x`<trtc-custom-button class="gray-button" type="disabled">${this.isChinese?"剩余检测时间":"Remaining time"}（${this.count}）s</trtc-custom-button>`:x`<trtc-custom-button class="report-button" type="contained" @click=${e=>{this.handleSuccess()}}>${this.isChinese?"查看检测报告":"View detect report"}</trtc-custom-button>`}
          </div>
        `:x``}};__publicField(NetworkDetector,"properties",{activeDetector:String,networkDetectInfo:Object,handleComplete:Function,generateReport:Function,testResult:Object,hasStartLocalVideo:Boolean,setHasStartLocalVideo:Function,constructTime:Number}),customElements.get("trtc-network-detector")||customElements.define("trtc-network-detector",NetworkDetector);var DetectorReport=class extends r4{constructor(){super();const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.attachShadow({mode:"open"})}render(){return x`
    <style>${style1_default}</style>
      <div class="device-testing-report">
        <div class="testing-title">${this.isChinese?"检测报告":"Detect Report"}</div>
        <div class="device-report-list">
          <div class="device-report">
            <div class="device-info">
              <span class="report-icon">
                <!-- 摄像头图标 -->
                <svg t="1630397874793" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="958" width="24" height="24"><path d="M489.244444 0a460.8 460.8 0 1 1 0 921.6A460.8 460.8 0 0 1 489.244444 0z m0 204.8a256 256 0 1 0 0 512 256 256 0 0 0 0-512z" fill="#47494D" opacity=".8" p-id="959"></path><path d="M489.244444 460.8m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" fill="#47494D" opacity=".8" p-id="960"></path><path d="M120.604444 952.32a368.64 61.44 0 1 0 737.28 0 368.64 61.44 0 1 0-737.28 0Z" fill="#47494D" opacity=".8" p-id="961"></path></svg>
              </span>
              <div class="device-name">${this.reportData.camera.result}</div>
            </div>
            <div class="${"success"===this.reportData.camera.type?"green":"red"}">
              ${"success"===this.reportData.camera.type?this.isChinese?"正常":"normal":this.isChinese?"异常":"abnormal"}
            </div>
          </div>
          <div class="device-report">
            <div class="device-info">
              <span class="report-icon">
                <!-- 麦克风图标 -->
                <svg t="1630397938861" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1205" width="24" height="24"><path d="M841.551448 434.423172A41.666207 41.666207 0 0 1 882.758621 476.548414c0 194.701241-144.454621 355.469241-329.551449 376.514207v86.722207h164.758069a41.666207 41.666207 0 0 1 41.207173 42.089931A41.666207 41.666207 0 0 1 717.965241 1024H306.034759A41.666207 41.666207 0 0 1 264.827586 981.874759a41.666207 41.666207 0 0 1 41.207173-42.089931h164.758069v-86.722207C285.696 832.052966 141.241379 671.249655 141.241379 476.548414a41.666207 41.666207 0 0 1 41.207173-42.125242 41.666207 41.666207 0 0 1 41.171862 42.125242c0 162.78069 129.129931 294.770759 288.379586 294.770758l8.827586-0.141241c155.153655-4.766897 279.552-134.850207 279.552-294.629517a41.666207 41.666207 0 0 1 41.171862-42.125242zM512 0c119.419586 0 216.275862 88.770207 216.275862 198.232276v317.228138c0 106.990345-92.513103 194.206897-208.154483 198.091034l-8.121379 0.141242c-119.419586 0-216.275862-88.770207-216.275862-198.232276V198.232276c0-106.990345 92.513103-194.206897 208.154483-198.091035L512 0z" fill="#47494D" opacity=".8" p-id="1206"></path></svg>
              </span>
              <div class="device-name">${this.reportData.microphone.result}</div>
            </div>
            <div class="${"success"===this.reportData.microphone.type?"green":"red"}">
              ${"success"===this.reportData.microphone.type?this.isChinese?"正常":"normal":this.isChinese?"异常":"abnormal"}
            </div>
          </div>
          ${this.reportData.speaker?x`
            <div class="device-report">
              <div class="device-info">
                <span class="report-icon">
                  <!-- 扬声器图标 -->
                  <svg t="1629186923749" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2923" width="24" height="24"><path d="M640 181.333333c0-46.037333-54.357333-70.4-88.746667-39.850666L359.552 311.850667a32 32 0 0 1-21.248 8.106666H181.333333A96 96 0 0 0 85.333333 415.957333v191.872a96 96 0 0 0 96 96h157.013334a32 32 0 0 1 21.248 8.106667l191.616 170.410667c34.389333 30.549333 88.789333 6.144 88.789333-39.850667V181.333333z m170.325333 70.272a32 32 0 0 1 44.757334 6.698667A424.917333 424.917333 0 0 1 938.666667 512a424.96 424.96 0 0 1-83.626667 253.696 32 32 0 0 1-51.413333-38.058667A360.917333 360.917333 0 0 0 874.666667 512a360.917333 360.917333 0 0 0-71.04-215.637333 32 32 0 0 1 6.698666-44.757334zM731.434667 357.12a32 32 0 0 1 43.392 12.928c22.869333 42.24 35.84 90.666667 35.84 141.994667a297.514667 297.514667 0 0 1-35.84 141.994666 32 32 0 0 1-56.32-30.464c17.92-33.152 28.16-71.082667 28.16-111.530666s-10.24-78.378667-28.16-111.530667a32 32 0 0 1 12.928-43.392z" fill="#47494D" opacity=".8" p-id="2924"></path></svg>
                </span>
                <div class="device-name">${this.reportData.speaker.result||"默认扬声器"}</div>
              </div>
              <div class="${"success"===this.reportData.speaker.type?"green":"red"}">
                ${"success"===this.reportData.speaker.type?this.isChinese?"正常":"normal":this.isChinese?"异常":"abnormal"}
              </div>
            </div>`:""}
          ${this.reportData.network?x`
            <div class="device-report">
              <div class="device-info">
                <span class="report-icon">
                  <!-- 网络图标 -->
                  <svg t="1630400570252" class="icon" viewBox="0 0 1291 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1640" width="24" height="24"><path d="M992.211478 583.68A449.758609 449.758609 0 0 0 650.017391 426.295652c-136.904348 0-259.561739 61.039304-342.194087 157.384348a90.156522 90.156522 0 0 0 136.859826 117.359304 269.846261 269.846261 0 0 1 205.334261-94.430608c82.142609 0 155.737043 36.641391 205.334261 94.386087a90.156522 90.156522 0 1 0 136.859826-117.359305zM559.86087 922.134261a90.156522 90.156522 0 1 0 180.313043 0 90.156522 90.156522 0 0 0-180.313043 0z" fill="#47494D" opacity=".8" p-id="1641"></path><path d="M1253.064348 289.124174A809.316174 809.316174 0 0 0 650.017391 20.613565a809.316174 809.316174 0 0 0-603.046956 268.466087 90.156522 90.156522 0 1 0 127.777391 127.065044l0.311652 0.26713A629.581913 629.581913 0 0 1 650.017391 200.926609c189.395478 0 359.290435 83.389217 474.957913 215.485217l0.267131-0.26713a90.156522 90.156522 0 1 0 127.777391-127.065044z" fill="#47494D" opacity=".8" p-id="1642"></path></svg>
                </span>
                <div class="device-name">${this.isChinese?"网络延时":"Network delay"}</div>
              </div>
              <div class="${this.reportData.network.result.rtt<=200?"green":"red"}">
                ${this.reportData.network.result.rtt}ms
              </div>
            </div>
            <div class="device-report">
              <div class="device-info">
                <span class="report-icon">
                  <!-- 网络图标 -->
                  <svg t="1630400570252" class="icon" viewBox="0 0 1291 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1640" width="24" height="24"><path d="M992.211478 583.68A449.758609 449.758609 0 0 0 650.017391 426.295652c-136.904348 0-259.561739 61.039304-342.194087 157.384348a90.156522 90.156522 0 0 0 136.859826 117.359304 269.846261 269.846261 0 0 1 205.334261-94.430608c82.142609 0 155.737043 36.641391 205.334261 94.386087a90.156522 90.156522 0 1 0 136.859826-117.359305zM559.86087 922.134261a90.156522 90.156522 0 1 0 180.313043 0 90.156522 90.156522 0 0 0-180.313043 0z" fill="#47494D" opacity=".8" p-id="1641"></path><path d="M1253.064348 289.124174A809.316174 809.316174 0 0 0 650.017391 20.613565a809.316174 809.316174 0 0 0-603.046956 268.466087 90.156522 90.156522 0 1 0 127.777391 127.065044l0.311652 0.26713A629.581913 629.581913 0 0 1 650.017391 200.926609c189.395478 0 359.290435 83.389217 474.957913 215.485217l0.267131-0.26713a90.156522 90.156522 0 1 0 127.777391-127.065044z" fill="#47494D" opacity=".8" p-id="1642"></path></svg>
                </span>
                <div class="device-name">${this.isChinese?"网络质量":"Network quality"}</div>
              </div>
              <div class="${this.goodUplinkQuality?"green":"red"}">
                ${this.isChinese?NETWORK_QUALITY[this.reportData.network.result.quality]:NETWORK_QUALITY_ENGLISH[this.reportData.network.result.quality]}
              </div>
            </div>
            `:""}
        </div>
        <div class="device-report-footer">
          <trtc-custom-button type="outlined"  @click=${this.handleReset}>${this.isChinese?"重新检测":"Detect Again"}</trtc-custom-button>
          <trtc-custom-button type="contained"  @click=${this.handleClose}>${this.isChinese?"完成检测":"Completed"}</trtc-custom-button>
        </div>
      </div>
    `}firstUpdated(){}goodUplinkQuality(){return this.reportData.network.result.uplinkQuality>0&&this.reportData.network.result.uplinkQuality<4}goodDownlinkQuality(){return this.reportData.network.result.downlinkQuality>0&&this.reportData.network.result.downlinkQuality<4}};__publicField(DetectorReport,"properties",{reportData:Object,handleReset:Function,handleClose:Function}),customElements.get("trtc-detect-report")||customElements.define("trtc-detect-report",DetectorReport);var _DetectorUI=class e extends r4{constructor(){super(),__publicField(this,"detectResult"),__publicField(this,"handleCompleted",((e,t,V=!1,i=null)=>{this.completed[this.stepNameList[this.activeStep]]={completed:!0,type:e,result:t},"network"===this.stepNameList[this.activeStep]?this.completedResult[this.stepNameList[this.activeStep]]={isSuccess:V,result:t}:this.completedResult[this.stepNameList[this.activeStep]]={isSuccess:V,device:i},this.activeStep<=this.stepNameList.length-1&&(this.activeStep=this.activeStep+1,this.stepNameList.indexOf("network")<0&&this.activeStep===this.stepNameList.length&&(this.detectStage=2)),this.requestUpdate()})),this.stepNameList=["camera","microphone","speaker"];const e=getLanguage();this.isChinese=e.indexOf("zh")>-1,this.detectStage=0,this.completed={},this.completedResult={},this.activeStep=0,this.isCompleted=!1,this.uplinkTRTC={},this.sdkAppId=0,this.isOpen=!0,this.hasStartLocalVideo=!1,this.remind=!1,this.envresult=sdk_default.getSystem(),this.isMobile="iOS"===this.envresult.OS||"Android"===this.envresult.OS,this.constructTime=(new Date).getTime(),this.cameraHasBeenActive=!1,this.microphoneHasBeenActive=!1,this.SpeakerHasBeenActive=!1,this.networkHasBeenActive=!1,this._initializeTRTC(),this.attachShadow({mode:"open"})}triggerCompletedEvent(){const e=new Event("detectorCompleted");this.dispatchEvent(e)}static registerTag(){customElements.get("detector-element")||customElements.define("detector-element",e)}_initializeTRTC(){let e=sdk_default.networkInfo;sdk_default.networkInfo&&this.stepNameList.indexOf("network")<0&&this.stepNameList.push("network"),Object.defineProperty(sdk_default,"networkInfo",{set:t=>{this.stepNameList.indexOf("network")<0&&this.stepNameList.push("network"),e=t},get:()=>e})}render(){return!0===this.isOpen?x`
      <style>${style1_default}</style>
      ${!0===this.remind&&"iOS"!==this.envresult.OS&&"Android"!==this.envresult.OS?x`
        <div class="arrow"></div>
        <div class="remind-text"> </div>`:x``}
      <div class="device-detector-backdrop">
        <div class="root">
          ${0===this.detectStage?x`
            ${sdk_default.allowSkip?x`<trtc-custom-button type="outlined" class="close" @click=${this.handleClose}>${this.isChinese?"跳过检测":"Skip Detection"}</trtc-custom-button>`:x``}
            <trtc-device-connect class="myconnect" stepnamelist=${JSON.stringify(this.stepNameList)} @remind-changed=${this.handleRemindChange} 
               @click=${e=>{const t=e.target.getAttribute("connectstage");if(t&&t.includes("1")){this.setDetectStage();const e=(new Date).getTime(),t=getCore();t&&t.kvStatManager.addSuccessEvent({key:591703,cost:e-this.constructTime})}}}></trtc-device-connect>`:x``}
          ${1===this.detectStage?x`
            <div class="step-container">
            ${this.stepNameList.map(((e,t)=>x`
              <div
                @click="${()=>this.handleStep(t)}"
                class="step ${[this.getLabelClassName(t)]}">
                <span class="step-icon">
                  ${"camera"===e?x`<svg  t="1630397874793" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="958" width="24" height="24"><path d="M489.244444 0a460.8 460.8 0 1 1 0 921.6A460.8 460.8 0 0 1 489.244444 0z m0 204.8a256 256 0 1 0 0 512 256 256 0 0 0 0-512z" opacity=".8" p-id="959"></path><path d="M489.244444 460.8m-153.6 0a153.6 153.6 0 1 0 307.2 0 153.6 153.6 0 1 0-307.2 0Z" opacity=".8" p-id="960"></path><path d="M120.604444 952.32a368.64 61.44 0 1 0 737.28 0 368.64 61.44 0 1 0-737.28 0Z" opacity=".8" p-id="961"></path></svg>
                    `:x``}
                  ${"microphone"===e?x`<svg  t="1630397938861" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1205" width="24" height="24"><path d="M841.551448 434.423172A41.666207 41.666207 0 0 1 882.758621 476.548414c0 194.701241-144.454621 355.469241-329.551449 376.514207v86.722207h164.758069a41.666207 41.666207 0 0 1 41.207173 42.089931A41.666207 41.666207 0 0 1 717.965241 1024H306.034759A41.666207 41.666207 0 0 1 264.827586 981.874759a41.666207 41.666207 0 0 1 41.207173-42.089931h164.758069v-86.722207C285.696 832.052966 141.241379 671.249655 141.241379 476.548414a41.666207 41.666207 0 0 1 41.207173-42.125242 41.666207 41.666207 0 0 1 41.171862 42.125242c0 162.78069 129.129931 294.770759 288.379586 294.770758l8.827586-0.141241c155.153655-4.766897 279.552-134.850207 279.552-294.629517a41.666207 41.666207 0 0 1 41.171862-42.125242zM512 0c119.419586 0 216.275862 88.770207 216.275862 198.232276v317.228138c0 106.990345-92.513103 194.206897-208.154483 198.091034l-8.121379 0.141242c-119.419586 0-216.275862-88.770207-216.275862-198.232276V198.232276c0-106.990345 92.513103-194.206897 208.154483-198.091035L512 0z" opacity=".8" p-id="1206"></path></svg>
                    `:x``}
                  ${"speaker"===e?x`<svg t="1629186923749" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2923" width="24" height="24"><path d="M640 181.333333c0-46.037333-54.357333-70.4-88.746667-39.850666L359.552 311.850667a32 32 0 0 1-21.248 8.106666H181.333333A96 96 0 0 0 85.333333 415.957333v191.872a96 96 0 0 0 96 96h157.013334a32 32 0 0 1 21.248 8.106667l191.616 170.410667c34.389333 30.549333 88.789333 6.144 88.789333-39.850667V181.333333z m170.325333 70.272a32 32 0 0 1 44.757334 6.698667A424.917333 424.917333 0 0 1 938.666667 512a424.96 424.96 0 0 1-83.626667 253.696 32 32 0 0 1-51.413333-38.058667A360.917333 360.917333 0 0 0 874.666667 512a360.917333 360.917333 0 0 0-71.04-215.637333 32 32 0 0 1 6.698666-44.757334zM731.434667 357.12a32 32 0 0 1 43.392 12.928c22.869333 42.24 35.84 90.666667 35.84 141.994667a297.514667 297.514667 0 0 1-35.84 141.994666 32 32 0 0 1-56.32-30.464c17.92-33.152 28.16-71.082667 28.16-111.530666s-10.24-78.378667-28.16-111.530667a32 32 0 0 1 12.928-43.392z" opacity=".8" p-id="2924"></path></svg>
                    `:x``}
                  ${"network"===e?x`<svg  t="1630400570252" class="icon" viewBox="0 0 1291 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1640" width="24" height="24"><path d="M992.211478 583.68A449.758609 449.758609 0 0 0 650.017391 426.295652c-136.904348 0-259.561739 61.039304-342.194087 157.384348a90.156522 90.156522 0 0 0 136.859826 117.359304 269.846261 269.846261 0 0 1 205.334261-94.430608c82.142609 0 155.737043 36.641391 205.334261 94.386087a90.156522 90.156522 0 1 0 136.859826-117.359305zM559.86087 922.134261a90.156522 90.156522 0 1 0 180.313043 0 90.156522 90.156522 0 0 0-180.313043 0z" opacity=".8" p-id="1641"></path><path d="M1253.064348 289.124174A809.316174 809.316174 0 0 0 650.017391 20.613565a809.316174 809.316174 0 0 0-603.046956 268.466087 90.156522 90.156522 0 1 0 127.777391 127.065044l0.311652 0.26713A629.581913 629.581913 0 0 1 650.017391 200.926609c189.395478 0 359.290435 83.389217 474.957913 215.485217l0.267131-0.26713a90.156522 90.156522 0 1 0 127.777391-127.065044z" opacity=".8" p-id="1642"></path></svg>
                    `:x``}
                </span>
                <span class="step-label">${e.toUpperCase()}</span>
              </div>`))}
          </div>
            `:x``}
          ${1===this.detectStage?x`
             ${this.stepNameList.map(((e,t)=>"camera"===e&&"camera"===this.stepNameList[this.activeStep]?x`<trtc-camera-detector .handleComplete=${this.handleCompleted}
                   activeDetector=${this.stepNameList[this.activeStep]}  constructTime=${this.constructTime}></trtc-camera-detector>`:"microphone"===e&&"microphone"===this.stepNameList[this.activeStep]?x`<trtc-mic-detector .handleComplete=${this.handleCompleted}
                   activeDetector=${this.stepNameList[this.activeStep]} constructTime=${this.constructTime}></trtc-mic-detector>`:"speaker"===e&&"speaker"===this.stepNameList[this.activeStep]?x`<trtc-speaker-detector .handleComplete=${this.handleCompleted}
                   activeDetector=${this.stepNameList[this.activeStep]} constructTime=${this.constructTime}></trtc-speaker-detector>`:"network"===e&&"network"===this.stepNameList[this.activeStep]?x`<trtc-network-detector .handleComplete=${this.handleCompleted}
                   activeDetector=${this.stepNameList[this.activeStep]} .generateReport=${()=>this.setDetectStage()} .hasStartLocalVideo=${this.hasStartLocalVideo} .setHasStartLocalVideo=${()=>{this.hasStartLocalVideo=!0}} constructTime=${this.constructTime}></trtc-network-detector>`:""))}
            `:x``}

          ${2===this.detectStage?x`
            <trtc-detect-report .reportData=${this.completed} .handleReset=${()=>this.handleReset()} .handleClose=${()=>this.handleClose()}></trtc-detect-report>
          `:x``}
        </div>
      </div>
    `:x``}async handleClose(){this.isOpen=!1,this.handleReset(),this.requestUpdate("detectStage",this.detectStage),this.remove(),0!==Object.keys(this.completedResult).length&&(this.detectResult=this.completedResult),this.isCompleted=!0,this.triggerCompletedEvent()}handleReset(){this.detectStage=0,this.activeStep=0}setDetectStage(){this.detectStage=this.detectStage+1}async firstUpdated(){getComputedStyle(document.documentElement).getPropertyValue("font-size")}updated(){if(this.remind&&!this.isMobile){const t=this.shadowRoot.querySelector(".remind-text");if(!t)return;this.isChinese?t.innerHTML=e.cameraFailAttention:t.innerHTML=e.cameraFailAttentionEng}}handleRemindChange(e){this.remind=e.detail,this.requestUpdate()}getLabelClassName(e){const{completed:t,stepNameList:V}=this,i=t[V[e]]&&"success"===t[V[e]].type,n=t[V[e]]&&"error"===t[V[e]].type;let A="";return this.activeStep===e||i?A="active":n&&(A="error"),A}handleStep(e){const{completed:t,stepNameList:V}=this;t[V[e]]&&t[V[e]].completed&&(this.activeStep=e)}};__publicField(_DetectorUI,"properties",{detectStage:{type:Number,value:0},stepNameList:{type:Array},completed:{type:Object},activeStep:{type:Number},trtc:{type:Object},sdkAppId:{type:Number}}),__publicField(_DetectorUI,"cameraFailAttention",'你没有打开摄像头/麦克风的权限，请点击<svg width="1rem" height="1rem" ><circle cx="0.5rem" cy="0.5rem" r="0.4rem" stroke="red" stroke-width="0.1rem" fill="none"  /><text x="0.5rem" y="0.79rem" font-size="0.7rem" text-anchor="middle" fill="red">i</text></svg>打开权限并且确保其他页面没有占用摄像头/麦克风'),__publicField(_DetectorUI,"cameraFailAttentionEng",'You didn\'t allow the permission to camera/microphone, please click<svg width="1rem" height="1rem" ><circle cx="0.5rem" cy="0.5rem" r="0.4rem" stroke="red" stroke-width="0.1rem" fill="none"  /><text x="0.5rem" y="0.79rem" font-size="0.7rem" text-anchor="middle" fill="red">i</text></svg>to allow permission and make sure other pages are not using the camera/microphone ');var listener,instance,DetectorUI=_DetectorUI,deviceSeq=0,_DeviceDetector=class e{constructor(e){if(__publicField(this,"seq"),__publicField(this,"myDetector"),__publicField(this,"_core"),__publicField(this,"_log"),__publicField(this,"Name"),__publicField(this,"detectResult"),instance)return instance;deviceSeq+=1,this.seq=deviceSeq,this._core=e,this._log=e.log.createChild({id:`${this.getAlias()}${deviceSeq}`}),setLog(this._log),setCore(this._core),DetectorUI.registerTag(),this._log.info("created")}getName(){return e.Name}getAlias(){return"dd"}getGroup(){return"dd"}getValidateRule(e){switch(e){case"start":return startValidateRule(this._core);case"update":case"stop":return stopValidateRule(this._core)}}start(e){if(!instance)return setLanguage((null==e?void 0:e.language)||"auto"),new Promise(((t,V)=>{var i;e&&e.networkDetect&&e.networkDetect.downlinkUserId&&e.networkDetect.downlinkUserSig?sdk_default.networkInfo=__spreadProps(__spreadValues({},e.networkDetect),{uplinkTRTC:this._core.TRTC.create(),downlinkTRTC:this._core.TRTC.create()}):e&&e.networkDetect?sdk_default.networkInfo=__spreadProps(__spreadValues({},e.networkDetect),{uplinkTRTC:this._core.TRTC.create()}):sdk_default.networkInfo=null,sdk_default.mirror=!!(null==(i=null==e?void 0:e.cameraDetect)?void 0:i.mirror),sdk_default.allowSkip=!!this._core.utils.isUndefined(null==e?void 0:e.allowSkip)||!!(null==e?void 0:e.allowSkip),this.myDetector=document.createElement("detector-element"),document.body.appendChild(this.myDetector),listener=()=>{this.detectResult=this.myDetector.detectResult,t(this.detectResult)},this.myDetector.addEventListener("detectorCompleted",listener)}))}afterStart(){this._core.clearStarted(this,this.getGroup()),this.stop()}stop(){var e,t,V;this.myDetector&&(this.myDetector.remove(),null==(e=sdk_default.networkInfo)||e.uplinkTRTC.destroy(),null==(V=null==(t=sdk_default.networkInfo)?void 0:t.downlinkTRTC)||V.destroy(),sdk_default.networkInfo=null,this.myDetector.removeEventListener("detectorCompleted",listener),listener=()=>{}),instance=null,this._log.info("stop")}update(){}destroy(){clear(),this.stop()}};__publicField(_DeviceDetector,"Name","DeviceDetector");var DeviceDetector=_DeviceDetector,index_default=DeviceDetector;export{index_default as default};
/*! Bundled license information:

@lit/reactive-element/css-tag.js:
  (**
   * @license
   * Copyright 2019 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

@lit/reactive-element/reactive-element.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/lit-html.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-element/lit-element.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/is-server.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
*/export{DeviceDetector};