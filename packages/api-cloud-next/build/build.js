var shell = require('shelljs');

if (!process.argv[2]) {
  shell.exec("node ./build/esbuild --prd && rollup -c build/rollup.config.mjs && node ./build/package-bundle.js");
  shell.cp('-r', './dist/npm-package/assets/', '../../examples/api-cloud-next/dist/npm-package/');

  return;
}

const newVersion = process.argv[2].replace(/^v/, ''); // 获取命令行参数中的新版本号,并过滤v字头

shell.exec("pnpm run version " + newVersion);

if (newVersion.includes('wasm')) {
  shell.exec("node ./build/esbuild --prd --room wasm && rollup -c build/rollup.config.mjs && node ./build/package-bundle.js wasm");
  shell.cp('./dist/trtc.esm.js', './dist/npm-package/trtc.esm.js');
} else {
  shell.exec("node ./build/esbuild --prd && rollup -c build/rollup.config.mjs && node ./build/package-bundle.js");
}

shell.exec("node ./build/plugin-debug/esbuild-debug-dialog --prd -f esm && rollup -c ./build/plugin-debug/rollup.config.mjs");
