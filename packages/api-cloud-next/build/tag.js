var shell = require('shelljs');
var fs = require('fs');

let newVersion = process.argv[2].replace(/^v/, ''); // 获取命令行参数中的新版本号,并过滤v字头

if (newVersion === 'latest_beta') {
  newVersion = fs.readFileSync('../../scripts/latest-beta.txt', 'utf-8').trim().split('/').pop() || 'latest_beta';
}

console.log(newVersion);

shell.echo(`打 tag`)
shell.exec(`git tag v${newVersion}`);
shell.echo(`push tag`)
shell.exec(`git push --tags`);
