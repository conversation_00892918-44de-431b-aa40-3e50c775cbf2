const replace = require('replace');
const fs = require('fs');
const pkg = require('../package.json');

var srcPaths = process.argv[2] === 'wasm' ? ['./build/template/npm-package-wasm/package.json'] : ['./build/template/npm-package/package.json'];

var distPaths = ['./dist/npm-package/package.json'];

var replacements = [
  {
    find: '<@VERSION@>',
    replace: pkg.version
  }
];

srcPaths.forEach((src, index) => {
  console.log(src);
  console.log(distPaths[index]);
  fs.writeFile(distPaths[index], fs.readFileSync(src).toString(), function (err) {
    replacements.forEach(function (obj) {
      replace({
        regex: obj.find,
        replacement: obj.replace,
        paths: [distPaths[index]],
        recursive: true,
        silent: true
      });
    });
  });
});
