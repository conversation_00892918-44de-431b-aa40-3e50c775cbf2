"use strict";var Vision=(()=>{var xn=Object.defineProperty,l2=Object.defineProperties,f2=Object.getOwnPropertyDescriptor,d2=Object.getOwnPropertyDescriptors,p2=Object.getOwnPropertyNames,Is=Object.getOwnPropertySymbols;var Us=Object.prototype.hasOwnProperty,g2=Object.prototype.propertyIsEnumerable;var Ur=(t,e,n)=>e in t?xn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Cr=(t,e)=>{for(var n in e||(e={}))Us.call(e,n)&&Ur(t,n,e[n]);if(Is)for(var n of Is(e))g2.call(e,n)&&Ur(t,n,e[n]);return t},Cs=(t,e)=>l2(t,d2(e));var m2=(t,e)=>{for(var n in e)xn(t,n,{get:e[n],enumerable:!0})},v2=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of p2(e))!Us.call(t,i)&&i!==n&&xn(t,i,{get:()=>e[i],enumerable:!(r=f2(e,i))||r.enumerable});return t};var y2=t=>v2(xn({},"__esModule",{value:!0}),t);var de=(t,e,n)=>Ur(t,typeof e!="symbol"?e+"":e,n);var X=(t,e,n)=>new Promise((r,i)=>{var s=h=>{try{a(n.next(h))}catch(c){i(c)}},o=h=>{try{a(n.throw(h))}catch(c){i(c)}},a=h=>h.done?r(h.value):Promise.resolve(h.value).then(s,o);a((n=n.apply(t,e)).next())});var ic={};m2(ic,{VisionTaskRegistry:()=>_s,VisionTaskType:()=>s2});var Re=typeof self!="undefined"?self:{};function pe(){throw Error("Invalid UTF8")}function Ps(t,e){return e=String.fromCharCode.apply(null,e),t==null?e:t+e}var kn,Pr,w2=typeof TextDecoder!="undefined",_2,E2=typeof TextEncoder!="undefined";function $o(t){if(E2)t=(_2||(_2=new TextEncoder)).encode(t);else{let n=0,r=new Uint8Array(3*t.length);for(let i=0;i<t.length;i++){var e=t.charCodeAt(i);if(e<128)r[n++]=e;else{if(e<2048)r[n++]=e>>6|192;else{if(e>=55296&&e<=57343){if(e<=56319&&i<t.length){let s=t.charCodeAt(++i);if(s>=56320&&s<=57343){e=1024*(e-55296)+s-56320+65536,r[n++]=e>>18|240,r[n++]=e>>12&63|128,r[n++]=e>>6&63|128,r[n++]=63&e|128;continue}i--}e=65533}r[n++]=e>>12|224,r[n++]=e>>6&63|128}r[n++]=63&e|128}}t=n===r.length?r:r.subarray(0,n)}return t}var vi,zn;t:{for(Dr=["CLOSURE_FLAGS"],Sn=Re,Ln=0;Ln<Dr.length;Ln++)if((Sn=Sn[Dr[Ln]])==null){zn=null;break t}zn=Sn}var Dr,Sn,Ln,fn,Ds=zn&&zn[610401301];vi=Ds!=null&&Ds;var Os=Re.navigator;function Kr(t){return!!vi&&!!fn&&fn.brands.some(({brand:e})=>e&&e.indexOf(t)!=-1)}function bt(t){var e;return(e=Re.navigator)&&(e=e.userAgent)||(e=""),e.indexOf(t)!=-1}function ee(){return!!vi&&!!fn&&fn.brands.length>0}function Or(){return ee()?Kr("Chromium"):(bt("Chrome")||bt("CriOS"))&&!(!ee()&&bt("Edge"))||bt("Silk")}function Kn(t){return Kn[" "](t),t}fn=Os&&Os.userAgentData||null,Kn[" "]=function(){};var T2=!ee()&&(bt("Trident")||bt("MSIE"));!bt("Android")||Or(),Or(),bt("Safari")&&(Or()||!ee()&&bt("Coast")||!ee()&&bt("Opera")||!ee()&&bt("Edge")||(ee()?Kr("Microsoft Edge"):bt("Edg/"))||ee()&&Kr("Opera"));var Ko={},on=null;function A2(t){let e=t.length,n=3*e/4;n%3?n=Math.floor(n):"=.".indexOf(t[e-1])!=-1&&(n="=.".indexOf(t[e-2])!=-1?n-2:n-1);let r=new Uint8Array(n),i=0;return function(s,o){function a(c){for(;h<s.length;){let l=s.charAt(h++),u=on[l];if(u!=null)return u;if(!/^[\s\xa0]*$/.test(l))throw Error("Unknown base64 encoding at char: "+l)}return c}Yo();let h=0;for(;;){let c=a(-1),l=a(0),u=a(64),f=a(64);if(f===64&&c===-1)break;o(c<<2|l>>4),u!=64&&(o(l<<4&240|u>>2),f!=64&&o(u<<6&192|f))}}(t,function(s){r[i++]=s}),i!==n?r.subarray(0,i):r}function Yo(){if(!on){on={};var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"];for(let n=0;n<5;n++){let r=t.concat(e[n].split(""));Ko[n]=r;for(let i=0;i<r.length;i++){let s=r[i];on[s]===void 0&&(on[s]=i)}}}}var qo=typeof Uint8Array!="undefined",Zo=!T2&&typeof btoa=="function";function Bs(t){if(!Zo){var e;e===void 0&&(e=0),Yo(),e=Ko[e];var n=Array(Math.floor(t.length/3)),r=e[64]||"";let h=0,c=0;for(;h<t.length-2;h+=3){var i=t[h],s=t[h+1],o=t[h+2],a=e[i>>2];i=e[(3&i)<<4|s>>4],s=e[(15&s)<<2|o>>6],o=e[63&o],n[c++]=a+i+s+o}switch(a=0,o=r,t.length-h){case 2:o=e[(15&(a=t[h+1]))<<2]||r;case 1:t=t[h],n[c]=e[t>>2]+e[(3&t)<<4|a>>4]+o+r}return n.join("")}for(e="",n=0,r=t.length-10240;n<r;)e+=String.fromCharCode.apply(null,t.subarray(n,n+=10240));return e+=String.fromCharCode.apply(null,n?t.subarray(n):t),btoa(e)}var Ns=/[-_.]/g,b2={"-":"+",_:"/",".":"="};function x2(t){return b2[t]||""}function Jo(t){if(!Zo)return A2(t);Ns.test(t)&&(t=t.replace(Ns,x2)),t=atob(t);let e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}function ye(t){return qo&&t!=null&&t instanceof Uint8Array}var Ie={};function we(){return k2||(k2=new $t(null,Ie))}function yi(t){Qo(Ie);var e=t.g;return(e=e==null||ye(e)?e:typeof e=="string"?Jo(e):null)==null?e:t.g=e}var $t=class{h(){return new Uint8Array(yi(this)||0)}constructor(t,e){if(Qo(e),this.g=t,t!=null&&t.length===0)throw Error("ByteString should be constructed with non-empty values")}},k2,Br;function Qo(t){if(t!==Ie)throw Error("illegal external caller")}function ta(t,e){t.__closure__error__context__984382||(t.__closure__error__context__984382={}),t.__closure__error__context__984382.severity=e}function Yr(t){return ta(t=Error(t),"warning"),t}function wi(t){if(t!=null){var e=Br!=null?Br:Br={},n=e[t]||0;n>=5||(e[t]=n+1,ta(t=Error(),"incident"),function(r){Re.setTimeout(()=>{throw r},0)}(t))}}var Yn=typeof Symbol=="function"&&typeof Symbol()=="symbol";function je(t,e,n=!1){return typeof Symbol=="function"&&typeof Symbol()=="symbol"?n&&Symbol.for&&t?Symbol.for(t):t!=null?Symbol(t):Symbol():e}var S2=je("jas",void 0,!0),Gs=je(void 0,"0di"),nn=je(void 0,"1oa"),Ue=je(void 0,Symbol()),L2=je(void 0,"0actk"),ea=je(void 0,"8utk"),v=Yn?S2:"Ea",na={Ea:{value:0,configurable:!0,writable:!0,enumerable:!1}},ra=Object.defineProperties;function qn(t,e){Yn||v in t||ra(t,na),t[v]|=e}function W(t,e){Yn||v in t||ra(t,na),t[v]=e}function Ve(t){return qn(t,34),t}function F2(t,e){W(e,-15615&(0|t))}function qr(t,e){W(e,-15581&(34|t))}function Zn(){return typeof BigInt=="function"}function ht(t){return Array.prototype.slice.call(t)}var _i,wn={};function Jn(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)&&t.constructor===Object}function Ei(t,e){if(t!=null){if(typeof t=="string")t=t?new $t(t,Ie):we();else if(t.constructor!==$t)if(ye(t))t=t.length?new $t(new Uint8Array(t),Ie):we();else{if(!e)throw Error();t=void 0}}return t}var zs=[];function ue(t){if(2&t)throw Error()}W(zs,55),_i=Object.freeze(zs);var jn=class{constructor(e,n,r){this.g=e,this.h=n,this.l=r}next(){let e=this.g.next();return e.done||(e.value=this.h.call(this.l,e.value)),e}[Symbol.iterator](){return this}};function Ti(t){return Ue?t[Ue]:void 0}var M2=Object.freeze({});function Qn(t){return t.Na=!0,t}var R2=Qn(t=>typeof t=="number"),js=Qn(t=>typeof t=="string"),I2=Qn(t=>typeof t=="boolean"),tr=typeof Re.BigInt=="function"&&typeof Re.BigInt(0)=="bigint";function re(t){var e=t;if(js(e)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(e))throw Error(String(e))}else if(R2(e)&&!Number.isSafeInteger(e))throw Error(String(e));return tr?BigInt(t):t=I2(t)?t?"1":"0":js(t)?t.trim()||"0":String(t)}var Zr=Qn(t=>tr?t>=C2&&t<=D2:t[0]==="-"?Vs(t,U2):Vs(t,P2)),U2=Number.MIN_SAFE_INTEGER.toString(),C2=tr?BigInt(Number.MIN_SAFE_INTEGER):void 0,P2=Number.MAX_SAFE_INTEGER.toString(),D2=tr?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Vs(t,e){if(t.length>e.length)return!1;if(t.length<e.length||t===e)return!0;for(let n=0;n<t.length;n++){let r=t[n],i=e[n];if(r>i)return!1;if(r<i)return!0}}var O2=typeof Uint8Array.prototype.slice=="function",ia,F=0,G=0;function Hs(t){let e=t>>>0;F=e,G=(t-e)/4294967296>>>0}function _e(t){if(t<0){Hs(-t);let[e,n]=ki(F,G);F=e>>>0,G=n>>>0}else Hs(t)}function Ai(t){let e=ia||(ia=new DataView(new ArrayBuffer(8)));e.setFloat32(0,+t,!0),G=0,F=e.getUint32(0,!0)}function bi(t,e){let n=4294967296*e+(t>>>0);return Number.isSafeInteger(n)?n:dn(t,e)}function xi(t,e){let n=2147483648&e;return n&&(e=~e>>>0,(t=1+~t>>>0)==0&&(e=e+1>>>0)),typeof(t=bi(t,e))=="number"?n?-t:t:n?"-"+t:t}function dn(t,e){if(t>>>=0,(e>>>=0)<=2097151)var n=""+(4294967296*e+t);else Zn()?n=""+(BigInt(e)<<BigInt(32)|BigInt(t)):(t=(16777215&t)+6777216*(n=16777215&(t>>>24|e<<8))+6710656*(e=e>>16&65535),n+=8147497*e,e*=2,t>=1e7&&(n+=t/1e7>>>0,t%=1e7),n>=1e7&&(e+=n/1e7>>>0,n%=1e7),n=e+Xs(n)+Xs(t));return n}function Xs(t){return t=String(t),"0000000".slice(t.length)+t}function sa(){var t=F,e=G;if(2147483648&e)if(Zn())t=""+(BigInt(0|e)<<BigInt(32)|BigInt(t>>>0));else{let[n,r]=ki(t,e);t="-"+dn(n,r)}else t=dn(t,e);return t}function er(t){if(t.length<16)_e(Number(t));else if(Zn())t=BigInt(t),F=Number(t&BigInt(4294967295))>>>0,G=Number(t>>BigInt(32)&BigInt(4294967295));else{let e=+(t[0]==="-");G=F=0;let n=t.length;for(let r=e,i=(n-e)%6+e;i<=n;r=i,i+=6){let s=Number(t.slice(r,i));G*=1e6,F=1e6*F+s,F>=4294967296&&(G+=Math.trunc(F/4294967296),G>>>=0,F>>>=0)}if(e){let[r,i]=ki(F,G);F=r,G=i}}}function ki(t,e){return e=~e,t?t=1+~t:e+=1,[t,e]}var pn=typeof BigInt=="function"?BigInt.asIntN:void 0,B2=typeof BigInt=="function"?BigInt.asUintN:void 0,ie=Number.isSafeInteger,nr=Number.isFinite,Ce=Math.trunc,N2=re(0);function le(t){return t==null||typeof t=="number"?t:t==="NaN"||t==="Infinity"||t==="-Infinity"?Number(t):void 0}function oa(t){return t==null||typeof t=="boolean"?t:typeof t=="number"?!!t:void 0}var G2=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function gn(t){switch(typeof t){case"bigint":return!0;case"number":return nr(t);case"string":return G2.test(t);default:return!1}}function He(t){if(t==null)return t;if(typeof t=="string"&&t)t=+t;else if(typeof t!="number")return;return nr(t)?0|t:void 0}function aa(t){if(t==null)return t;if(typeof t=="string"&&t)t=+t;else if(typeof t!="number")return;return nr(t)?t>>>0:void 0}function Ws(t){if(t[0]==="-")return!1;let e=t.length;return e<20||e===20&&Number(t.substring(0,6))<184467}function ha(t){let e=t.length;return t[0]==="-"?e<20||e===20&&Number(t.substring(0,7))>-922337:e<19||e===19&&Number(t.substring(0,6))<922337}function ca(t){return ha(t)?t:(er(t),sa())}function Si(t){return t=Ce(t),ie(t)||(_e(t),t=xi(F,G)),t}function ua(t){var e=Ce(Number(t));return ie(e)?String(e):((e=t.indexOf("."))!==-1&&(t=t.substring(0,e)),ca(t))}function $s(t){var e=Ce(Number(t));return ie(e)?re(e):((e=t.indexOf("."))!==-1&&(t=t.substring(0,e)),Zn()?re(pn(64,BigInt(t))):re(ca(t)))}function Ks(t){if(ie(t))t=re(Si(t));else{if(t=Ce(t),ie(t))t=String(t);else{let e=String(t);ha(e)?t=e:(_e(t),t=sa())}t=re(t)}return t}function Jr(t){return t==null?t:typeof t=="bigint"?(Zr(t)?t=Number(t):(t=pn(64,t),t=Zr(t)?Number(t):String(t)),t):gn(t)?typeof t=="number"?Si(t):ua(t):void 0}function z2(t){if(t==null)return t;var e=typeof t;if(e==="bigint")return String(B2(64,t));if(gn(t)){if(e==="string")return e=Ce(Number(t)),ie(e)&&e>=0?t=String(e):((e=t.indexOf("."))!==-1&&(t=t.substring(0,e)),Ws(t)||(er(t),t=dn(F,G))),t;if(e==="number")return(t=Ce(t))>=0&&ie(t)?t:function(n){if(n<0){_e(n);var r=dn(F,G);return n=Number(r),ie(n)?n:r}return Ws(r=String(n))?r:(_e(n),bi(F,G))}(t)}}function la(t){if(typeof t!="string")throw Error();return t}function Xe(t){if(t!=null&&typeof t!="string")throw Error();return t}function Pe(t){return t==null||typeof t=="string"?t:void 0}function Li(t,e,n,r){if(t!=null&&typeof t=="object"&&t.W===wn)return t;if(!Array.isArray(t))return n?2&r?((t=e[Gs])||(Ve((t=new e).u),t=e[Gs]=t),e=t):e=new e:e=void 0,e;let i=n=0|t[v];return i===0&&(i|=32&r),i|=2&r,i!==n&&W(t,i),new e(t)}function j2(t,e,n){if(e)t:{if(!gn(e=t))throw Yr("int64");switch(typeof e){case"string":e=$s(e);break t;case"bigint":e=re(pn(64,e));break t;default:e=Ks(e)}}else t=typeof(e=t),e=e==null?e:t==="bigint"?re(pn(64,e)):gn(e)?t==="string"?$s(e):Ks(e):void 0;return(t=e)==null?n?N2:void 0:t}function V2(t){return t}var H2={},X2=function(){try{return Kn(new class extends Map{constructor(){super()}}),!1}catch(t){return!0}}(),hn=class{constructor(){this.g=new Map}get(e){return this.g.get(e)}set(e,n){return this.g.set(e,n),this.size=this.g.size,this}delete(e){return e=this.g.delete(e),this.size=this.g.size,e}clear(){this.g.clear(),this.size=this.g.size}has(e){return this.g.has(e)}entries(){return this.g.entries()}keys(){return this.g.keys()}values(){return this.g.values()}forEach(e,n){return this.g.forEach(e,n)}[Symbol.iterator](){return this.entries()}},W2=X2?(Object.setPrototypeOf(hn.prototype,Map.prototype),Object.defineProperties(hn.prototype,{size:{value:0,configurable:!0,enumerable:!0,writable:!0}}),hn):class extends Map{constructor(){super()}};function Ys(t){return t}function Nr(t){if(2&t.M)throw Error("Cannot mutate an immutable Map")}var Bt=class extends W2{constructor(t,e,n=Ys,r=Ys){super();let i=0|t[v];i|=64,W(t,i),this.M=i,this.I=e,this.S=n,this.X=this.I?$2:r;for(let s=0;s<t.length;s++){let o=t[s],a=n(o[0],!1,!0),h=o[1];e?h===void 0&&(h=null):h=r(o[1],!1,!0,void 0,void 0,i),super.set(a,h)}}La(){var t=J2;if(this.size!==0)return Array.from(super.entries(),e=>(e[0]=t(e[0]),e[1]=t(e[1]),e))}da(t=K2){let e=[],n=super.entries();for(var r;!(r=n.next()).done;)(r=r.value)[0]=t(r[0]),r[1]=t(r[1]),e.push(r);return e}clear(){Nr(this),super.clear()}delete(t){return Nr(this),super.delete(this.S(t,!0,!1))}entries(){if(this.I){var t=super.keys();t=new jn(t,Y2,this)}else t=super.entries();return t}values(){if(this.I){var t=super.keys();t=new jn(t,Bt.prototype.get,this)}else t=super.values();return t}forEach(t,e){this.I?super.forEach((n,r,i)=>{t.call(e,i.get(r),r,i)}):super.forEach(t,e)}set(t,e){return Nr(this),(t=this.S(t,!0,!1))==null?this:e==null?(super.delete(t),this):super.set(t,this.X(e,!0,!0,this.I,!1,this.M))}Ja(t){let e=this.S(t[0],!1,!0);t=t[1],t=this.I?t===void 0?null:t:this.X(t,!1,!0,void 0,!1,this.M),super.set(e,t)}has(t){return super.has(this.S(t,!1,!1))}get(t){t=this.S(t,!1,!1);let e=super.get(t);if(e!==void 0){var n=this.I;return n?((n=this.X(e,!1,!0,n,this.pa,this.M))!==e&&super.set(t,n),n):e}}[Symbol.iterator](){return this.entries()}};function $2(t,e,n,r,i,s){return t=Li(t,r,n,s),i&&(t=sr(t)),t}function K2(t){return t}function Y2(t){return[t,this.get(t)]}var q2,Un,fa,Z2;function qs(){return q2||(q2=new Bt(Ve([]),void 0,void 0,void 0,H2))}function Qr(t,e,n,r,i){if(t!=null){if(Array.isArray(t)){let s=0|t[v];return t.length===0&&1&s?void 0:i&&2&s?t:rr(t,e,n,r!==void 0,i)}return e(t,r)}}function rr(t,e,n,r,i){let s=r||n?0|t[v]:0,o=r?!!(32&s):void 0,a=0,h=(r=ht(t)).length;for(let _=0;_<h;_++){var c=r[_];if(_===h-1&&Jn(c)){var l=e,u=n,f=o,w=i;let E;for(let B in c){let H=Qr(c[B],l,u,f,w);H!=null&&((E!=null?E:E={})[B]=H)}c=E}else c=Qr(r[_],e,n,o,i);r[_]=c,c!=null&&(a=_+1)}return a<h&&(r.length=a),n&&((t=Ti(t))&&(r[Ue]=ht(t)),n(s,r)),r}function J2(t){return Qr(t,Fi,void 0,void 0,!1)}function Fi(t){switch(typeof t){case"number":return Number.isFinite(t)?t:""+t;case"bigint":return Zr(t)?Number(t):""+t;case"boolean":return t?1:0;case"object":if(ye(t))return ye(t)&&wi(ea),Bs(t);if(t.W===wn)return da(t);if(t instanceof $t){let e=t.g;return e==null?"":typeof e=="string"?e:t.g=Bs(e)}return t instanceof Bt?t.La():void 0}return t}function da(t){var e=t.u;t=rr(e,Fi,void 0,void 0,!1);var n=0|e[v];if((e=t.length)&&!(512&n)){var r=t[e-1],i=!1;Jn(r)?(e--,i=!0):r=void 0;var s=e-(n=512&n?0:-1),o=(Un!=null?Un:V2)(s,n,t,r);if(r&&(t[e]=void 0),s<o&&r){for(var a in s=!0,r){let h=+a;h<=o?(t[i=h+n]=r[a],e=Math.max(i+1,e),i=!1,delete r[a]):s=!1}s&&(r=void 0)}for(s=e-1;e>0;s=e-1)if((a=t[s])==null)e--,i=!0;else{if(!((s-=n)>=o))break;(r!=null?r:r={})[s]=a,e--,i=!0}i&&(t.length=e),r&&t.push(r)}return t}function se(t,e,n){return t=pa(t,e[0],e[1],n?1:2),e!==fa&&n&&qn(t,8192),t}function pa(t,e,n,r){if(t==null){var i=96;n?(t=[n],i|=512):t=[],e&&(i=-16760833&i|(1023&e)<<14)}else{if(!Array.isArray(t))throw Error("narr");if(8192&(i=0|t[v])||!(64&i)||2&i||wi(L2),1024&i)throw Error("farr");if(64&i)return t;if(r===1||r===2||(i|=64),n&&(i|=512,n!==t[0]))throw Error("mid");t:{var s=(n=t).length;if(s){var o=s-1;if(Jn(r=n[o])){if((o-=e=512&(i|=256)?0:-1)>=1024)throw Error("pvtlmt");for(var a in r)(s=+a)<o&&(n[s+e]=r[a],delete r[a]);i=-16760833&i|(1023&o)<<14;break t}}if(e){if((a=Math.max(e,s-(512&i?0:-1)))>1024)throw Error("spvt");i=-16760833&i|(1023&a)<<14}}}return W(t,i),t}function ti(t,e,n=qr){if(t!=null){if(qo&&t instanceof Uint8Array)return e?t:new Uint8Array(t);if(Array.isArray(t)){var r=0|t[v];return 2&r?t:(e&&(e=r===0||!!(32&r)&&!(64&r||!(16&r))),e?(W(t,34|r),4&r&&Object.freeze(t),t):rr(t,ti,4&r?qr:n,!0,!0))}return t.W===wn?t=2&(r=0|(n=t.u)[v])?t:new t.constructor(ir(n,r,!0)):t instanceof Bt&&!(2&t.M)&&(n=Ve(t.da(ti)),t=new Bt(n,t.I,t.S,t.X)),t}}function ir(t,e,n){let r=n||2&e?qr:F2,i=!!(32&e);return t=function(s,o,a){let h=ht(s);var c=h.length;let l=256&o?h[c-1]:void 0;for(c+=l?-1:0,o=512&o?1:0;o<c;o++)h[o]=a(h[o]);if(l){o=h[o]={};for(let u in l)o[u]=a(l[u])}return(s=Ti(s))&&(h[Ue]=ht(s)),h}(t,e,s=>ti(s,i,r)),qn(t,32|(n?2:0)),t}function sr(t){let e=t.u,n=0|e[v];return 2&n?new t.constructor(ir(e,n,!1)):t}function De(t,e){return Zt(t=t.u,0|t[v],e)}function Zt(t,e,n){if(n===-1)return null;let r=n+(512&e?0:-1),i=t.length-1;return r>=i&&256&e?t[i][n]:r<=i?t[r]:void 0}function M(t,e,n){let r=t.u,i=0|r[v];return ue(i),O(r,i,e,n),t}function O(t,e,n,r){let i=512&e?0:-1,s=n+i;var o=t.length-1;return s>=o&&256&e?(t[o][n]=r,e):s<=o?(t[s]=r,e):(r!==void 0&&(n>=(o=e>>14&1023||536870912)?r!=null&&(t[o+i]={[n]:r},W(t,e|=256)):t[s]=r),e)}function Cn(t,e){let n=0|(t=t.u)[v],r=Zt(t,n,e),i=le(r);return i!=null&&i!==r&&O(t,n,e,i),i}function ga(t){let e=0|(t=t.u)[v],n=Zt(t,e,1),r=Ei(n,!0);return r!=null&&r!==n&&O(t,e,1,r),r}function ge(){return M2===void 0?2:4}function me(t,e,n,r,i){let s=t.u,o=2&(t=0|s[v])?1:r;i=!!i;let a=0|(r=Mi(s,t,e))[v];if(!(4&a)){4&a&&(r=ht(r),a=Kt(a,t),t=O(s,t,e,r));let h=0,c=0;for(;h<r.length;h++){let l=n(r[h]);l!=null&&(r[c++]=l)}c<h&&(r.length=c),a=Ri(a,t),n=-2049&(20|a),a=n&=-4097,W(r,a),2&a&&Object.freeze(r)}return o===1||o===4&&32&a?Wt(a)||(i=a,a|=2,a!==i&&W(r,a),Object.freeze(r)):(o===2&&Wt(a)&&(r=ht(r),a=Kt(a,t),a=oe(a,t,i),W(r,a),t=O(s,t,e,r)),Wt(a)||(e=a,a=oe(a,t,i),a!==e&&W(r,a))),r}function Mi(t,e,n){return t=Zt(t,e,n),Array.isArray(t)?t:_i}function Ri(t,e){return t===0&&(t=Kt(t,e)),1|t}function Wt(t){return!!(2&t)&&!!(4&t)||!!(1024&t)}function ma(t){t=ht(t);for(let e=0;e<t.length;e++){let n=t[e]=ht(t[e]);Array.isArray(n[1])&&(n[1]=Ve(n[1]))}return t}function ei(t,e,n,r){let i=0|(t=t.u)[v];ue(i),O(t,i,e,(r==="0"?Number(n)===0:n===r)?void 0:n)}function We(t,e,n,r){ue(e);let i=Mi(t,e,n),s=i!==_i;if(64&e||!(8192&e)||!s){let o=s?0|i[v]:0,a=o;(!s||2&a||Wt(a)||4&a&&!(32&a))&&(i=ht(i),a=Kt(a,e),e=O(t,e,n,i)),a=-13&Ri(a,e),a=oe(r?-17&a:16|a,e,!0),a!==o&&W(i,a)}return i}function Gr(t,e){var n=n1;return Ui(Ii(t=t.u),t,0|t[v],n)===e?e:-1}function Ii(t){var n;if(Yn)return(n=t[nn])!=null?n:t[nn]=new Map;if(nn in t)return t[nn];let e=new Map;return Object.defineProperty(t,nn,{value:e}),e}function va(t,e,n,r){let i=Ii(t),s=Ui(i,t,e,n);return s!==r&&(s&&(e=O(t,e,s)),i.set(n,r)),e}function Ui(t,e,n,r){let i=t.get(r);if(i!=null)return i;i=0;for(let s=0;s<r.length;s++){let o=r[s];Zt(e,n,o)!=null&&(i!==0&&(n=O(e,n,i)),i=o)}return t.set(r,i),i}function Ci(t,e,n){let r=0|t[v],i=Zt(t,r,n),s;if(i!=null&&i.W===wn)return(e=sr(i))!==i&&O(t,r,n,e),e.u;if(Array.isArray(i)){let o=0|i[v];s=2&o?se(ir(i,o,!1),e,!0):64&o?i:se(s,e,!0)}else s=se(void 0,e,!0);return s!==i&&O(t,r,n,s),s}function ya(t,e,n){let r=0|(t=t.u)[v],i=Zt(t,r,n);return(e=Li(i,e,!1,r))!==i&&e!=null&&O(t,r,n,e),e}function b(t,e,n){if((e=ya(t,e,n))==null)return e;let r=0|(t=t.u)[v];if(!(2&r)){let i=sr(e);i!==e&&O(t,r,n,e=i)}return e}function wa(t,e,n,r,i,s,o){t=t.u;var a=!!(2&e);let h=a?1:i;s=!!s,o&&(o=!a);var c=0|(i=Mi(t,e,r))[v];if(!(a=!!(4&c))){var l=i,u=e;let f=!!(2&(c=Ri(c,e)));f&&(u|=2);let w=!f,_=!0,E=0,B=0;for(;E<l.length;E++){let H=Li(l[E],n,!1,u);if(H instanceof n){if(!f){let st=!!(2&(0|H.u[v]));w&&(w=!st),_&&(_=st)}l[B++]=H}}B<E&&(l.length=B),c|=4,c=_?16|c:-17&c,W(l,c=w?8|c:-9&c),f&&Object.freeze(l)}if(o&&!(8&c||!i.length&&(h===1||h===4&&32&c))){for(Wt(c)&&(i=ht(i),c=Kt(c,e),e=O(t,e,r,i)),n=i,o=c,l=0;l<n.length;l++)(c=n[l])!==(u=sr(c))&&(n[l]=u);o|=8,W(n,o=n.length?-17&o:16|o),c=o}return h===1||h===4&&32&c?Wt(c)||(e=c,(c|=!i.length||16&c&&(!a||32&c)?2:1024)!==e&&W(i,c),Object.freeze(i)):(h===2&&Wt(c)&&(W(i=ht(i),c=oe(c=Kt(c,e),e,s)),e=O(t,e,r,i)),Wt(c)||(r=c,(c=oe(c,e,s))!==r&&W(i,c))),i}function Yt(t,e,n){let r=0|t.u[v];return wa(t,r,e,n,ge(),!1,!(2&r))}function y(t,e,n,r){return r==null&&(r=void 0),M(t,n,r)}function cn(t,e,n,r){r==null&&(r=void 0);t:{let i=0|(t=t.u)[v];if(ue(i),r==null){let s=Ii(t);if(Ui(s,t,i,n)!==e)break t;s.set(n,0)}else i=va(t,i,n,e);O(t,i,e,r)}}function Kt(t,e){return-1025&(t=32|(2&e?2|t:-3&t))}function oe(t,e,n){return 32&e&&n||(t&=-33),t}function or(t,e,n){ue(0|t.u[v]),me(t,e,Pe,2,!0).push(la(n))}function Vn(t,e,n,r){let i=0|t.u[v];ue(i),t=wa(t,i,n,e,2,!0),r=r!=null?r:new n,t.push(r),t[v]=2&(0|r.u[v])?-9&t[v]:-17&t[v]}function xt(t,e){return He(De(t,e))}function kt(t,e){return Pe(De(t,e))}function j(t,e){var n;return(n=Cn(t,e))!=null?n:0}function mn(t,e,n){if(n!=null&&typeof n!="boolean")throw t=typeof n,Error(`Expected boolean but got ${t!="object"?t:n?Array.isArray(n)?"array":t:"null"}: ${n}`);M(t,e,n)}function Nt(t,e,n){if(n!=null){if(typeof n!="number"||!nr(n))throw Yr("int32");n|=0}M(t,e,n)}function g(t,e,n){if(n!=null&&typeof n!="number")throw Error(`Value of float/double field must be a number, found ${typeof n}: ${n}`);M(t,e,n)}function Hn(t,e,n){{let o=t.u,a=0|o[v];if(ue(a),n==null)O(o,a,e);else{var r=t=0|n[v],i=Wt(t),s=i||Object.isFrozen(n);for(i||(t=0),s||(n=ht(n),r=0,t=oe(t=Kt(t,a),a,!0),s=!1),t|=21,i=0;i<n.length;i++){let h=n[i],c=la(h);Object.is(h,c)||(s&&(n=ht(n),r=0,t=oe(t=Kt(t,a),a,!0),s=!1),n[i]=c)}t!==r&&(s&&(n=ht(n),t=oe(t=Kt(t,a),a,!0)),W(n,t)),O(o,a,e,n)}}}function _a(t,e){return Error(`Invalid wire type: ${t} (at position ${e})`)}function Pi(){return Error("Failed to read varint, encoding is invalid.")}function Ea(t,e){return Error(`Tried to read past the end of the data ${e} > ${t}`)}function Di(t){if(typeof t=="string")return{buffer:Jo(t),O:!1};if(Array.isArray(t))return{buffer:new Uint8Array(t),O:!1};if(t.constructor===Uint8Array)return{buffer:t,O:!1};if(t.constructor===ArrayBuffer)return{buffer:new Uint8Array(t),O:!1};if(t.constructor===$t)return{buffer:yi(t)||new Uint8Array(0),O:!0};if(t instanceof Uint8Array)return{buffer:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),O:!1};throw Error("Type not convertible to a Uint8Array, expected a Uint8Array, an ArrayBuffer, a base64 encoded string, a ByteString or an Array of numbers")}function Oi(t,e){let n,r=0,i=0,s=0,o=t.h,a=t.g;do n=o[a++],r|=(127&n)<<s,s+=7;while(s<32&&128&n);for(s>32&&(i|=(127&n)>>4),s=3;s<32&&128&n;s+=7)n=o[a++],i|=(127&n)<<s;if(ve(t,a),n<128)return e(r>>>0,i>>>0);throw Pi()}function Bi(t){let e=0,n=t.g,r=n+10,i=t.h;for(;n<r;){let s=i[n++];if(e|=s,(128&s)==0)return ve(t,n),!!(127&e)}throw Pi()}function ae(t){let e=t.h,n=t.g,r=e[n++],i=127&r;if(128&r&&(r=e[n++],i|=(127&r)<<7,128&r&&(r=e[n++],i|=(127&r)<<14,128&r&&(r=e[n++],i|=(127&r)<<21,128&r&&(r=e[n++],i|=r<<28,128&r&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++]&&128&e[n++])))))throw Pi();return ve(t,n),i}function qt(t){return ae(t)>>>0}function ni(t){var e=t.h;let n=t.g,r=e[n],i=e[n+1],s=e[n+2];return e=e[n+3],ve(t,t.g+4),(r<<0|i<<8|s<<16|e<<24)>>>0}function ri(t){var e=ni(t);t=2*(e>>31)+1;let n=e>>>23&255;return e&=8388607,n==255?e?NaN:t*(1/0):n==0?1401298464324817e-60*t*e:t*Math.pow(2,n-150)*(e+8388608)}function Q2(t){return ae(t)}function zr(t,e,{aa:n=!1}={}){t.aa=n,e&&(e=Di(e),t.h=e.buffer,t.m=e.O,t.j=0,t.l=t.h.length,t.g=t.j)}function ve(t,e){if(t.g=e,e>t.l)throw Ea(t.l,e)}function Ta(t,e){if(e<0)throw Error(`Tried to read a negative byte length: ${e}`);let n=t.g,r=n+e;if(r>t.l)throw Ea(e,t.l-n);return t.g=r,n}function Aa(t,e){if(e==0)return we();var n=Ta(t,e);return t.aa&&t.m?n=t.h.subarray(n,n+e):(t=t.h,n=n===(e=n+e)?new Uint8Array(0):O2?t.slice(n,e):new Uint8Array(t.subarray(n,e))),n.length==0?we():new $t(n,Ie)}Bt.prototype.toJSON=void 0;var Zs=[];function ba(t){var e=t.g;if(e.g==e.l)return!1;t.l=t.g.g;var n=qt(t.g);if(e=n>>>3,!((n&=7)>=0&&n<=5))throw _a(n,t.l);if(e<1)throw Error(`Invalid field number: ${e} (at position ${t.l})`);return t.m=e,t.h=n,!0}function Pn(t){switch(t.h){case 0:t.h!=0?Pn(t):Bi(t.g);break;case 1:ve(t=t.g,t.g+8);break;case 2:if(t.h!=2)Pn(t);else{var e=qt(t.g);ve(t=t.g,t.g+e)}break;case 5:ve(t=t.g,t.g+4);break;case 3:for(e=t.m;;){if(!ba(t))throw Error("Unmatched start-group tag: stream EOF");if(t.h==4){if(t.m!=e)throw Error("Unmatched end-group tag");break}Pn(t)}break;default:throw _a(t.h,t.l)}}function _n(t,e,n){let r=t.g.l,i=qt(t.g),s=t.g.g+i,o=s-r;if(o<=0&&(t.g.l=s,n(e,t,void 0,void 0,void 0),o=s-t.g.g),o)throw Error(`Message parsing ended unexpectedly. Expected to read ${i} bytes, instead read ${i-o} bytes, either the data ended unexpectedly or the message misreported its own length`);return t.g.g=s,t.g.l=r,e}function Ni(t){var e=qt(t.g),n=Ta(t=t.g,e);if(t=t.h,w2){var r,i=t;(r=Pr)||(r=Pr=new TextDecoder("utf-8",{fatal:!0})),e=n+e,i=n===0&&e===i.length?i:i.subarray(n,e);try{var s=r.decode(i)}catch(a){if(kn===void 0){try{r.decode(new Uint8Array([128]))}catch(h){}try{r.decode(new Uint8Array([97])),kn=!0}catch(h){kn=!1}}throw!kn&&(Pr=void 0),a}}else{e=(s=n)+e,n=[];let a,h=null;for(;s<e;){var o=t[s++];o<128?n.push(o):o<224?s>=e?pe():(a=t[s++],o<194||(192&a)!=128?(s--,pe()):n.push((31&o)<<6|63&a)):o<240?s>=e-1?pe():(a=t[s++],(192&a)!=128||o===224&&a<160||o===237&&a>=160||(192&(r=t[s++]))!=128?(s--,pe()):n.push((15&o)<<12|(63&a)<<6|63&r)):o<=244?s>=e-2?pe():(a=t[s++],(192&a)!=128||a-144+(o<<28)>>30!=0||(192&(r=t[s++]))!=128||(192&(i=t[s++]))!=128?(s--,pe()):(o=(7&o)<<18|(63&a)<<12|(63&r)<<6|63&i,o-=65536,n.push(55296+(o>>10&1023),56320+(1023&o)))):pe(),n.length>=8192&&(h=Ps(h,n),n.length=0)}s=Ps(h,n)}return s}function xa(t){let e=qt(t.g);return Aa(t.g,e)}function ar(t,e,n){var r=qt(t.g);for(r=t.g.g+r;t.g.g<r;)n.push(e(t.g))}var Fn=[];function Mt(t,e,n){e.g?e.m(t,e.g,e.h,n):e.m(t,e.h,n)}var p=class{constructor(t,e){this.u=pa(t,e)}toJSON(){try{var t=da(this)}finally{Un=void 0}return t}l(){var t=Ch;return t.g?t.l(this,t.g,t.h):t.l(this,t.h,t.defaultValue)}clone(){let t=this.u;return new this.constructor(ir(t,0|t[v],!1))}O(){return!!(2&(0|this.u[v]))}};function Js(t){return t?/^\d+$/.test(t)?(er(t),new ii(F,G)):null:th||(th=new ii(0,0))}p.prototype.W=wn,p.prototype.toString=function(){return this.u.toString()};var ii=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}},th;function Qs(t){return t?/^-?\d+$/.test(t)?(er(t),new si(F,G)):null:eh||(eh=new si(0,0))}var si=class{constructor(t,e){this.h=t>>>0,this.g=e>>>0}},eh;function Le(t,e,n){for(;n>0||e>127;)t.g.push(127&e|128),e=(e>>>7|n<<25)>>>0,n>>>=7;t.g.push(e)}function $e(t,e){for(;e>127;)t.g.push(127&e|128),e>>>=7;t.g.push(e)}function hr(t,e){if(e>=0)$e(t,e);else{for(let n=0;n<9;n++)t.g.push(127&e|128),e>>=7;t.g.push(1)}}function vn(t,e){t.g.push(e>>>0&255),t.g.push(e>>>8&255),t.g.push(e>>>16&255),t.g.push(e>>>24&255)}function Oe(t,e){e.length!==0&&(t.l.push(e),t.h+=e.length)}function vt(t,e,n){$e(t.g,8*e+n)}function Gi(t,e){return vt(t,e,2),e=t.g.end(),Oe(t,e),e.push(t.h),e}function zi(t,e){var n=e.pop();for(n=t.h+t.g.length()-n;n>127;)e.push(127&n|128),n>>>=7,t.h++;e.push(n),t.h++}function cr(t,e,n){vt(t,e,2),$e(t.g,n.length),Oe(t,t.g.end()),Oe(t,n)}function Xn(t,e,n,r){n!=null&&(e=Gi(t,e),r(n,t),zi(t,e))}function Rt(){let t=class{constructor(){throw Error()}};return Object.setPrototypeOf(t,t.prototype),t}var ji=Rt(),ka=Rt(),Vi=Rt(),Hi=Rt(),Sa=Rt(),La=Rt(),Xi=Rt(),Fa=Rt(),Ma=Rt(),Ke=class{constructor(t,e,n){this.g=t,this.h=e,t=ji,this.l=!!t&&n===t||!1}};function ur(t,e){return new Ke(t,e,ji)}function Ra(t,e,n,r,i){Xn(t,n,Ua(e,r),i)}var nh=ur(function(t,e,n,r,i){return t.h===2&&(_n(t,Ci(e,r,n),i),!0)},Ra),rh=ur(function(t,e,n,r,i){return t.h===2&&(_n(t,Ci(e,r,n),i),!0)},Ra),lr=Symbol(),Wi=Symbol(),to=Symbol(),eo=Symbol(),Dn,On;function Ee(t,e,n,r){var i=r[t];if(i)return i;(i={}).Ma=r,i.T=function(u){switch(typeof u){case"boolean":return fa||(fa=[0,void 0,!0]);case"number":return u>0?void 0:u===0?Z2||(Z2=[0,void 0]):[-u,void 0];case"string":return[0,u];case"object":return u}}(r[0]);var s=r[1];let o=1;s&&s.constructor===Object&&(i.ga=s,typeof(s=r[++o])=="function"&&(i.la=!0,Dn!=null||(Dn=s),On!=null||(On=r[o+1]),s=r[o+=2]));let a={};for(;s&&Array.isArray(s)&&s.length&&typeof s[0]=="number"&&s[0]>0;){for(var h=0;h<s.length;h++)a[s[h]]=s;s=r[++o]}for(h=1;s!==void 0;){let u;typeof s=="number"&&(h+=s,s=r[++o]);var c=void 0;if(s instanceof Ke?u=s:(u=nh,o--),u==null?void 0:u.l){s=r[++o],c=r;var l=o;typeof s=="function"&&(s=s(),c[l]=s),c=s}for(l=h+1,typeof(s=r[++o])=="number"&&s<0&&(l-=s,s=r[++o]);h<l;h++){let f=a[h];c?n(i,h,u,c,f):e(i,h,u,f)}}return r[t]=i}function Ia(t){return Array.isArray(t)?t[0]instanceof Ke?t:[rh,t]:[t,void 0]}function Ua(t,e){return t instanceof p?t.u:Array.isArray(t)?se(t,e,!1):void 0}function $i(t,e,n,r){let i=n.g;t[e]=r?(s,o,a)=>i(s,o,a,r):i}function Ki(t,e,n,r,i){let s=n.g,o,a;t[e]=(h,c,l)=>s(h,c,l,a||(a=Ee(Wi,$i,Ki,r).T),o||(o=Yi(r)),i)}function Yi(t){let e=t[to];if(e!=null)return e;let n=Ee(Wi,$i,Ki,t);return e=n.la?(r,i)=>Dn(r,i,n):(r,i)=>{let s=0|r[v];for(;ba(i)&&i.h!=4;){var o=i.m,a=n[o];if(a==null){var h=n.ga;h&&(h=h[o])&&(h=ih(h))!=null&&(a=n[o]=h)}a!=null&&a(i,r,o)||(o=(a=i).l,Pn(a),a.fa?a=void 0:(h=a.g.g-o,a.g.g=o,a=Aa(a.g,h)),o=r,a&&((h=o[Ue])?h.push(a):o[Ue]=[a]))}return 8192&s&&Ve(r),!0},t[to]=e}function ih(t){let e=(t=Ia(t))[0].g;if(t=t[1]){let n=Yi(t),r=Ee(Wi,$i,Ki,t).T;return(i,s,o)=>e(i,s,o,r,n)}return e}function fr(t,e,n){t[e]=n.h}function dr(t,e,n,r){let i,s,o=n.h;t[e]=(a,h,c)=>o(a,h,c,s||(s=Ee(lr,fr,dr,r).T),i||(i=Ca(r)))}function Ca(t){let e=t[eo];if(!e){let n=Ee(lr,fr,dr,t);e=(r,i)=>Pa(r,i,n),t[eo]=e}return e}function Pa(t,e,n){(function(r,i,s){let o=512&i?0:-1,a=r.length,h=a+((i=64&i?256&i:!!a&&Jn(r[a-1]))?-1:0);for(let c=0;c<h;c++)s(c-o,r[c]);if(i){r=r[a-1];for(let c in r)!isNaN(c)&&s(+c,r[c])}})(t,0|t[v]|(n.T[1]?512:0),(r,i)=>{if(i!=null){var s=function(o,a){var h=o[a];if(h)return h;if((h=o.ga)&&(h=h[a])){var c=(h=Ia(h))[0].h;if(h=h[1]){let l=Ca(h),u=Ee(lr,fr,dr,h).T;h=o.la?On(u,l):(f,w,_)=>c(f,w,_,u,l)}else h=c;return o[a]=h}}(n,r);s&&s(e,i,r)}}),(t=Ti(t))&&function(r,i){Oe(r,r.g.end());for(let s=0;s<i.length;s++)Oe(r,yi(i[s])||new Uint8Array(0))}(e,t)}function Ye(t,e){if(Array.isArray(e)){var n=0|e[v];if(4&n)return e;for(var r=0,i=0;r<e.length;r++){let s=t(e[r]);s!=null&&(e[i++]=s)}return i<r&&(e.length=i),W(e,-6145&(5|n)),2&n&&Object.freeze(e),e}}function nt(t,e,n){return new Ke(t,e,n)}function qe(t,e,n){return new Ke(t,e,n)}function rt(t,e,n){O(t,0|t[v],e,n)}var sh=ur(function(t,e,n,r,i){return t.h===2&&(t=_n(t,se([void 0,void 0],r,!0),i),ue(r=0|e[v]),(i=Zt(e,r,n))instanceof Bt?(2&i.M)!=0?((i=i.da()).push(t),O(e,r,n,i)):i.Ja(t):Array.isArray(i)?(2&(0|i[v])&&O(e,r,n,i=ma(i)),i.push(t)):O(e,r,n,[t]),!0)},function(t,e,n,r,i){if(e instanceof Bt)e.forEach((s,o)=>{Xn(t,n,se([o,s],r,!1),i)});else if(Array.isArray(e))for(let s=0;s<e.length;s++){let o=e[s];Array.isArray(o)&&Xn(t,n,se(o,r,!1),i)}});function Da(t,e,n){if(e=function(r){if(r==null)return r;let i=typeof r;if(i==="bigint")return String(pn(64,r));if(gn(r)){if(i==="string")return ua(r);if(i==="number")return Si(r)}}(e),e!=null&&(typeof e=="string"&&Qs(e),e!=null))switch(vt(t,n,0),typeof e){case"number":t=t.g,_e(e),Le(t,F,G);break;case"bigint":n=BigInt.asUintN(64,e),n=new si(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),Le(t.g,n.h,n.g);break;default:n=Qs(e),Le(t.g,n.h,n.g)}}function Oa(t,e,n){(e=He(e))!=null&&e!=null&&(vt(t,n,0),hr(t.g,e))}function Ba(t,e,n){(e=oa(e))!=null&&(vt(t,n,0),t.g.g.push(e?1:0))}function Na(t,e,n){(e=Pe(e))!=null&&cr(t,n,$o(e))}function Ga(t,e,n,r,i){Xn(t,n,Ua(e,r),i)}function za(t,e,n){e==null||typeof e=="string"||e instanceof $t||(ye(e)?ye(e)&&wi(ea):e=void 0),e!=null&&cr(t,n,Di(e).buffer)}function ja(t,e,n){return(t.h===5||t.h===2)&&(e=We(e,0|e[v],n,!1),t.h==2?ar(t,ri,e):e.push(ri(t.g)),!0)}var Vt=nt(function(t,e,n){if(t.h!==1)return!1;var r=t.g;t=ni(r);let i=ni(r);r=2*(i>>31)+1;let s=i>>>20&2047;return t=4294967296*(1048575&i)+t,rt(e,n,s==2047?t?NaN:r*(1/0):s==0?5e-324*r*t:r*Math.pow(2,s-1075)*(t+4503599627370496)),!0},function(t,e,n){(e=le(e))!=null&&(vt(t,n,1),t=t.g,(n=ia||(ia=new DataView(new ArrayBuffer(8)))).setFloat64(0,+e,!0),F=n.getUint32(0,!0),G=n.getUint32(4,!0),vn(t,F),vn(t,G))},Rt()),$=nt(function(t,e,n){return t.h===5&&(rt(e,n,ri(t.g)),!0)},function(t,e,n){(e=le(e))!=null&&(vt(t,n,5),t=t.g,Ai(e),vn(t,F))},Xi),oh=qe(ja,function(t,e,n){if((e=Ye(le,e))!=null)for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];s!=null&&(vt(r,i,5),r=r.g,Ai(s),vn(r,F))}},Xi),qi=qe(ja,function(t,e,n){if((e=Ye(le,e))!=null&&e.length){vt(t,n,2),$e(t.g,4*e.length);for(let r=0;r<e.length;r++)n=t.g,Ai(e[r]),vn(n,F)}},Xi),he=nt(function(t,e,n){return t.h===0&&(rt(e,n,Oi(t.g,xi)),!0)},Da,La),jr=nt(function(t,e,n){return t.h===0&&(rt(e,n,(t=Oi(t.g,xi))===0?void 0:t),!0)},Da,La),ah=nt(function(t,e,n){return t.h===0&&(rt(e,n,Oi(t.g,bi)),!0)},function(t,e,n){if((e=z2(e))!=null&&(typeof e=="string"&&Js(e),e!=null))switch(vt(t,n,0),typeof e){case"number":t=t.g,_e(e),Le(t,F,G);break;case"bigint":n=BigInt.asUintN(64,e),n=new ii(Number(n&BigInt(4294967295)),Number(n>>BigInt(32))),Le(t.g,n.h,n.g);break;default:n=Js(e),Le(t.g,n.h,n.g)}},Rt()),z=nt(function(t,e,n){return t.h===0&&(rt(e,n,ae(t.g)),!0)},Oa,Hi),pr=qe(function(t,e,n){return(t.h===0||t.h===2)&&(e=We(e,0|e[v],n,!1),t.h==2?ar(t,ae,e):e.push(ae(t.g)),!0)},function(t,e,n){if((e=Ye(He,e))!=null&&e.length){n=Gi(t,n);for(let r=0;r<e.length;r++)hr(t.g,e[r]);zi(t,n)}},Hi),Se=nt(function(t,e,n){return t.h===0&&(rt(e,n,(t=ae(t.g))===0?void 0:t),!0)},Oa,Hi),P=nt(function(t,e,n){return t.h===0&&(rt(e,n,Bi(t.g)),!0)},Ba,ka),Fe=nt(function(t,e,n){return t.h===0&&(rt(e,n,(t=Bi(t.g))===!1?void 0:t),!0)},Ba,ka),Q=qe(function(t,e,n){return t.h===2&&(t=Ni(t),We(e,0|e[v],n,!1).push(t),!0)},function(t,e,n){if((e=Ye(Pe,e))!=null)for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];s!=null&&cr(r,i,$o(s))}},Vi),ne=nt(function(t,e,n){return t.h===2&&(rt(e,n,(t=Ni(t))===""?void 0:t),!0)},Na,Vi),L=nt(function(t,e,n){return t.h===2&&(rt(e,n,Ni(t)),!0)},Na,Vi),q=function(t,e,n=ji){return new Ke(t,e,n)}(function(t,e,n,r,i){return t.h===2&&(r=se(void 0,r,!0),We(e,0|e[v],n,!0).push(r),_n(t,r,i),!0)},function(t,e,n,r,i){if(Array.isArray(e))for(let s=0;s<e.length;s++)Ga(t,e[s],n,r,i)}),S=ur(function(t,e,n,r,i,s){return t.h===2&&(va(e,0|e[v],s,n),_n(t,e=Ci(e,r,n),i),!0)},Ga),Va=nt(function(t,e,n){return t.h===2&&(rt(e,n,xa(t)),!0)},za,Fa),hh=qe(function(t,e,n){return(t.h===0||t.h===2)&&(e=We(e,0|e[v],n,!1),t.h==2?ar(t,qt,e):e.push(qt(t.g)),!0)},function(t,e,n){if((e=Ye(aa,e))!=null)for(let o=0;o<e.length;o++){var r=t,i=n,s=e[o];s!=null&&(vt(r,i,0),$e(r.g,s))}},Sa),ch=nt(function(t,e,n){return t.h===0&&(rt(e,n,(t=qt(t.g))===0?void 0:t),!0)},function(t,e,n){(e=aa(e))!=null&&e!=null&&(vt(t,n,0),$e(t.g,e))},Sa),gt=nt(function(t,e,n){return t.h===0&&(rt(e,n,ae(t.g)),!0)},function(t,e,n){(e=He(e))!=null&&(e=parseInt(e,10),vt(t,n,0),hr(t.g,e))},Ma),oi=class{constructor(e,n){this.h=e,this.g=n,this.l=b,this.m=y,this.defaultValue=void 0}register(){Kn(this)}};function It(t,e){return new oi(t,e)}function fe(t,e){return(n,r)=>{if(Fn.length){let s=Fn.pop();s.o(r),zr(s.g,n,r),n=s}else n=new class{constructor(s,o){if(Zs.length){let a=Zs.pop();zr(a,s,o),s=a}else s=new class{constructor(a,h){this.h=null,this.m=!1,this.g=this.l=this.j=0,zr(this,a,h)}clear(){this.h=null,this.m=!1,this.g=this.l=this.j=0,this.aa=!1}}(s,o);this.g=s,this.l=this.g.g,this.h=this.m=-1,this.o(o)}o({fa:s=!1}={}){this.fa=s}}(n,r);try{let s=new t,o=s.u;Yi(e)(o,n);var i=s}finally{n.g.clear(),n.m=-1,n.h=-1,Fn.length<100&&Fn.push(n)}return i}}function gr(t){return function(){let e=new class{constructor(){this.l=[],this.h=0,this.g=new class{constructor(){this.g=[]}length(){return this.g.length}end(){let o=this.g;return this.g=[],o}}}};Pa(this.u,e,Ee(lr,fr,dr,t)),Oe(e,e.g.end());let n=new Uint8Array(e.h),r=e.l,i=r.length,s=0;for(let o=0;o<i;o++){let a=r[o];n.set(a,s),s+=a.length}return e.l=[n],n}}var no=class extends p{constructor(t){super(t)}},ro=[0,ne,nt(function(t,e,n){return t.h===2&&(rt(e,n,(t=xa(t))===we()?void 0:t),!0)},function(t,e,n){if(e!=null){if(e instanceof p){let r=e.Oa;return void(r&&(e=r(e),e!=null&&cr(t,n,Di(e).buffer)))}if(Array.isArray(e))return}za(t,e,n)},Fa)],Vr,io=globalThis.trustedTypes;function so(t){Vr===void 0&&(Vr=function(){let n=null;if(!io)return n;try{let r=i=>i;n=io.createPolicy("goog#html",{createHTML:r,createScript:r,createScriptURL:r})}catch(r){}return n}());var e=Vr;return new class{constructor(n){this.g=n}toString(){return this.g+""}}(e?e.createScriptURL(t):t)}function uh(t,...e){if(e.length===0)return so(t[0]);let n=t[0];for(let r=0;r<e.length;r++)n+=encodeURIComponent(e[r])+t[r+1];return so(n)}var Ha=[0,z,gt,P,-1,pr,gt,-1],lh=class extends p{constructor(t){super(t)}},Xa=[0,P,L,P,gt,-1,qe(function(t,e,n){return(t.h===0||t.h===2)&&(e=We(e,0|e[v],n,!1),t.h==2?ar(t,Q2,e):e.push(ae(t.g)),!0)},function(t,e,n){if((e=Ye(He,e))!=null&&e.length){n=Gi(t,n);for(let r=0;r<e.length;r++)hr(t.g,e[r]);zi(t,n)}},Ma),L,-1,[0,P,-1],gt,P,-1],Wa=[0,L,-2],oo=class extends p{constructor(t){super(t)}},$a=[0],Ka=[0,z,P,1,P,-3],mt=class extends p{constructor(t){super(t,2)}},K={};K[336783863]=[0,L,P,-1,z,[0,[1,2,3,4,5,6,7,8,9],S,$a,S,Xa,S,Wa,S,Ka,S,Ha,S,[0,L,-2],S,[0,L,gt],S,[0,gt,L,-1],S,[0,gt,-1]],[0,L],P,[0,[1,3],[2,4],S,[0,pr],-1,S,[0,Q],-1,q,[0,L,-1]],L];var ao=[0,jr,-1,Fe,-3,jr,pr,ne,Se,jr,-1,Fe,Se,Fe,-2,ne];function yt(t,e){ei(t,2,Xe(e),"")}function R(t,e){or(t,3,e)}function A(t,e){or(t,4,e)}var et=class extends p{constructor(t){super(t,500)}o(t){return y(this,0,7,t)}},un=[-1,{}],ho=[0,L,1,un],co=[0,L,Q,un];function wt(t,e){Vn(t,1,et,e)}function I(t,e){or(t,10,e)}function x(t,e){or(t,15,e)}var ut=class extends p{constructor(t){super(t,500)}o(t){return y(this,0,1001,t)}},Ya=[-500,q,[-500,ne,-1,Q,-3,[-2,K,P],q,ro,Se,-1,ho,co,q,[0,ne,Fe],ne,ao,Se,Q,987,Q],4,q,[-500,L,-1,[-1,{}],998,L],q,[-500,L,Q,-1,[-2,{},P],997,Q,-1],Se,q,[-500,L,Q,un,998,Q],Q,Se,ho,co,q,[0,ne,-1,un],Q,-2,ao,ne,-1,Fe,[0,Fe,ch],978,un,q,ro];ut.prototype.g=gr(Ya);var fh=fe(ut,Ya),dh=class extends p{constructor(t){super(t)}},qa=class extends p{constructor(t){super(t)}g(){return Yt(this,dh,1)}},Za=[0,q,[0,z,$,L,-1]],mr=fe(qa,Za),ph=class extends p{constructor(t){super(t)}},gh=class extends p{constructor(t){super(t)}},Hr=class extends p{constructor(t){super(t)}h(){return b(this,ph,2)}g(){return Yt(this,gh,5)}},Ja=fe(class extends p{constructor(t){super(t)}},[0,Q,pr,qi,[0,gt,[0,z,-3],[0,$,-3],[0,z,-1,[0,q,[0,z,-2]]],q,[0,$,-1,L,$]],L,-1,he,q,[0,z,$],Q,he]),Qa=class extends p{constructor(t){super(t)}},Me=fe(class extends p{constructor(t){super(t)}},[0,q,[0,$,-4]]),t1=class extends p{constructor(t){super(t)}},En=fe(class extends p{constructor(t){super(t)}},[0,q,[0,$,-4]]),mh=class extends p{constructor(t){super(t)}},vh=[0,z,-1,qi,gt],e1=class extends p{constructor(t){super(t)}};e1.prototype.g=gr([0,$,-4,he]);var yh=class extends p{constructor(t){super(t)}},wh=fe(class extends p{constructor(t){super(t)}},[0,q,[0,1,z,L,Za],he]),uo=class extends p{constructor(t){super(t)}},_h=class extends p{constructor(t){super(t)}ma(){let t=ga(this);return t==null?we():t}},Eh=class extends p{constructor(t){super(t)}},n1=[1,2],Th=fe(class extends p{constructor(t){super(t)}},[0,q,[0,n1,S,[0,qi],S,[0,Va],z,L],he]),Zi=class extends p{constructor(t){super(t)}},r1=[0,L,z,$,Q,-1],lo=class extends p{constructor(t){super(t)}},Ah=[0,P,-1],fo=class extends p{constructor(t){super(t)}},Bn=[1,2,3,4,5],Wn=class extends p{constructor(t){super(t)}g(){return ga(this)!=null}h(){return kt(this,2)!=null}},D=class extends p{constructor(t){super(t)}g(){var t;return(t=oa(De(this,2)))!=null?t:!1}},i1=[0,Va,L,[0,z,he,-1],[0,ah,he]],V=[0,i1,P,[0,Bn,S,Ka,S,Xa,S,Ha,S,$a,S,Wa],gt],vr=class extends p{constructor(t){super(t)}},Ji=[0,V,$,-1,z],bh=It(502141897,vr);K[502141897]=Ji;var xh=fe(class extends p{constructor(t){super(t)}},[0,[0,gt,-1,oh,hh],vh]),s1=class extends p{constructor(t){super(t)}},o1=class extends p{constructor(t){super(t)}},Qi=[0,V,$,[0,V],P],a1=[0,V,Ji,Qi,$,[0,[0,i1]]],kh=It(508968150,o1);K[508968150]=a1,K[508968149]=Qi;var h1=class extends p{constructor(t){super(t)}},Sh=It(513916220,h1);K[513916220]=[0,V,a1,z];var xe=class extends p{constructor(t){super(t)}h(){return b(this,Zi,2)}g(){M(this,2)}},c1=[0,V,r1];K[478825465]=c1;var Lh=class extends p{constructor(t){super(t)}},u1=class extends p{constructor(t){super(t)}},ts=class extends p{constructor(t){super(t)}},es=class extends p{constructor(t){super(t)}},l1=class extends p{constructor(t){super(t)}},po=[0,V,[0,V],c1,-1],f1=[0,V,$,z],ns=[0,V,$],d1=[0,V,f1,ns,$],Fh=It(479097054,l1);K[479097054]=[0,V,d1,po],K[463370452]=po,K[464864288]=f1;var Mh=It(462713202,es);K[462713202]=d1,K[474472470]=ns;var Rh=class extends p{constructor(t){super(t)}},p1=class extends p{constructor(t){super(t)}},g1=class extends p{constructor(t){super(t)}},m1=class extends p{constructor(t){super(t)}},rs=[0,V,$,-1,z],ai=[0,V,$,P];m1.prototype.g=gr([0,V,ns,[0,V],Ji,Qi,rs,ai]);var v1=class extends p{constructor(t){super(t)}},Ih=It(456383383,v1);K[456383383]=[0,V,r1];var y1=class extends p{constructor(t){super(t)}},Uh=It(476348187,y1);K[476348187]=[0,V,Ah];var w1=class extends p{constructor(t){super(t)}},go=class extends p{constructor(t){super(t)}},_1=[0,gt,-1],Ch=It(458105876,class extends p{constructor(t){super(t)}g(){var t=this.u;let e=0|t[v],n=2&e;return t=function(r,i,s){var o=go;let a=2&i,h=!1;if(s==null){if(a)return qs();s=[]}else if(s.constructor===Bt){if((2&s.M)==0||a)return s;s=s.da()}else Array.isArray(s)?h=!!(2&(0|s[v])):s=[];if(a){if(!s.length)return qs();h||(h=!0,Ve(s))}else h&&(h=!1,s=ma(s));return h||(64&(0|s[v])?s[v]&=-33:32&i&&qn(s,32)),O(r,i,2,o=new Bt(s,o,j2,void 0)),o}(t,e,Zt(t,e,2)),!n&&go&&(t.pa=!0),t}});K[458105876]=[0,_1,sh,[!0,he,[0,L,-1,Q]]];var is=class extends p{constructor(t){super(t)}},E1=It(458105758,is);K[458105758]=[0,V,L,_1];var T1=class extends p{constructor(t){super(t)}},Ph=It(443442058,T1);K[443442058]=[0,V,L,z,$,Q,-1,P,$],K[514774813]=rs;var A1=class extends p{constructor(t){super(t)}},Dh=It(516587230,A1);function hi(t,e){return e=e?e.clone():new Zi,t.displayNamesLocale!==void 0?M(e,1,Xe(t.displayNamesLocale)):t.displayNamesLocale===void 0&&M(e,1),t.maxResults!==void 0?Nt(e,2,t.maxResults):"maxResults"in t&&M(e,2),t.scoreThreshold!==void 0?g(e,3,t.scoreThreshold):"scoreThreshold"in t&&M(e,3),t.categoryAllowlist!==void 0?Hn(e,4,t.categoryAllowlist):"categoryAllowlist"in t&&M(e,4),t.categoryDenylist!==void 0?Hn(e,5,t.categoryDenylist):"categoryDenylist"in t&&M(e,5),e}function ss(t,e=-1,n=""){return{categories:t.map(r=>{var i,s,o,a,h,c,l;return{index:(s=(i=xt(r,1))!=null?i:0)!=null?s:-1,score:(o=j(r,2))!=null?o:0,categoryName:(h=(a=kt(r,3))!=null?a:"")!=null?h:"",displayName:(l=(c=kt(r,4))!=null?c:"")!=null?l:""}}),headIndex:e,headName:n}}function b1(t){var o,a,h,c,l,u,f,w,_,E,B,H,st;var e=me(t,3,le,ge()),n=me(t,2,He,ge()),r=me(t,1,Pe,ge()),i=me(t,9,Pe,ge());let s={categories:[],keypoints:[]};for(let U=0;U<e.length;U++)s.categories.push({score:e[U],index:(o=n[U])!=null?o:-1,categoryName:(a=r[U])!=null?a:"",displayName:(h=i[U])!=null?h:""});if((e=(c=b(t,Hr,4))==null?void 0:c.h())&&(s.boundingBox={originX:(l=xt(e,1))!=null?l:0,originY:(u=xt(e,2))!=null?u:0,width:(f=xt(e,3))!=null?f:0,height:(w=xt(e,4))!=null?w:0,angle:0}),(_=b(t,Hr,4))==null?void 0:_.g().length)for(let U of b(t,Hr,4).g())s.keypoints.push({x:(E=Cn(U,1))!=null?E:0,y:(B=Cn(U,2))!=null?B:0,score:(H=Cn(U,4))!=null?H:0,label:(st=kt(U,3))!=null?st:""});return s}function yr(t){var n,r,i,s;let e=[];for(let o of Yt(t,t1,1))e.push({x:(n=j(o,1))!=null?n:0,y:(r=j(o,2))!=null?r:0,z:(i=j(o,3))!=null?i:0,visibility:(s=j(o,4))!=null?s:0});return e}function ln(t){var n,r,i,s;let e=[];for(let o of Yt(t,Qa,1))e.push({x:(n=j(o,1))!=null?n:0,y:(r=j(o,2))!=null?r:0,z:(i=j(o,3))!=null?i:0,visibility:(s=j(o,4))!=null?s:0});return e}function mo(t){return Array.from(t,e=>e>127?e-256:e)}function vo(t,e){if(t.length!==e.length)throw Error(`Cannot compute cosine similarity between embeddings of different sizes (${t.length} vs. ${e.length}).`);let n=0,r=0,i=0;for(let s=0;s<t.length;s++)n+=t[s]*e[s],r+=t[s]*t[s],i+=e[s]*e[s];if(r<=0||i<=0)throw Error("Cannot compute cosine similarity on embedding with 0 norm.");return n/Math.sqrt(r*i)}var Mn;K[516587230]=[0,V,rs,ai,$],K[518928384]=ai;var Oh=new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11]);function x1(){return X(this,null,function*(){if(Mn===void 0)try{yield WebAssembly.instantiate(Oh),Mn=!0}catch(t){Mn=!1}return Mn})}function rn(n){return X(this,arguments,function*(t,e=uh``){let r=(yield x1())?"wasm_internal":"wasm_nosimd_internal";return{wasmLoaderPath:`${e}/${t}_${r}.js`,wasmBinaryPath:`${e}/${t}_${r}.wasm`}})}var Ht=class{};function k1(){var t=navigator;return typeof OffscreenCanvas!="undefined"&&(!function(e=navigator){return(e=e.userAgent).includes("Safari")&&!e.includes("Chrome")}(t)||!!((t=t.userAgent.match(/Version\/([\d]+).*Safari/))&&t.length>=1&&Number(t[1])>=17))}function yo(t){return X(this,null,function*(){if(typeof importScripts!="function"){let e=document.createElement("script");return e.src=t.toString(),e.crossOrigin="anonymous",new Promise((n,r)=>{e.addEventListener("load",()=>{n()},!1),e.addEventListener("error",i=>{r(i)},!1),document.body.appendChild(e)})}importScripts(t.toString())})}function S1(t){return t.videoWidth!==void 0?[t.videoWidth,t.videoHeight]:t.naturalWidth!==void 0?[t.naturalWidth,t.naturalHeight]:t.displayWidth!==void 0?[t.displayWidth,t.displayHeight]:[t.width,t.height]}function m(t,e,n){t.m||console.error("No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target"),n(e=t.i.stringToNewUTF8(e)),t.i._free(e)}function wo(t,e,n){if(!t.i.canvas)throw Error("No OpenGL canvas configured.");if(n?t.i._bindTextureToStream(n):t.i._bindTextureToCanvas(),!(n=t.i.canvas.getContext("webgl2")||t.i.canvas.getContext("webgl")))throw Error("Failed to obtain WebGL context from the provided canvas. `getContext()` should only be invoked with `webgl` or `webgl2`.");t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!0),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,e),t.i.gpuOriginForWebTexturesIsBottomLeft&&n.pixelStorei(n.UNPACK_FLIP_Y_WEBGL,!1);let[r,i]=S1(e);return!t.l||r===t.i.canvas.width&&i===t.i.canvas.height||(t.i.canvas.width=r,t.i.canvas.height=i),[r,i]}function _o(t,e,n){t.m||console.error("No wasm multistream support detected: ensure dependency inclusion of :gl_graph_runner_internal_multi_input target");let r=new Uint32Array(e.length);for(let i=0;i<e.length;i++)r[i]=t.i.stringToNewUTF8(e[i]);e=t.i._malloc(4*r.length),t.i.HEAPU32.set(r,e>>2),n(e);for(let i of r)t.i._free(i);t.i._free(e)}function Ct(t,e,n){t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=n}function te(t,e,n){let r=[];t.i.simpleListeners=t.i.simpleListeners||{},t.i.simpleListeners[e]=(i,s,o)=>{s?(n(r,o),r=[]):r.push(i)}}Ht.forVisionTasks=function(t){return rn("vision",t)},Ht.forTextTasks=function(t){return rn("text",t)},Ht.forGenAiExperimentalTasks=function(t){return rn("genai_experimental",t)},Ht.forGenAiTasks=function(t){return rn("genai",t)},Ht.forAudioTasks=function(t){return rn("audio",t)},Ht.isSimdSupported=function(){return x1()};function Bh(t,e,n,r){return X(this,null,function*(){return t=yield((i,s,o,a,h)=>X(this,null,function*(){if(s&&(yield yo(s)),!self.ModuleFactory||o&&(yield yo(o),!self.ModuleFactory))throw Error("ModuleFactory not set.");return self.Module&&h&&((s=self.Module).locateFile=h.locateFile,h.mainScriptUrlOrBlob&&(s.mainScriptUrlOrBlob=h.mainScriptUrlOrBlob)),h=yield self.ModuleFactory(self.Module||h),self.ModuleFactory=self.Module=void 0,new i(h,a)}))(t,n.wasmLoaderPath,n.assetLoaderPath,e,{locateFile:i=>i.endsWith(".wasm")?n.wasmBinaryPath.toString():n.assetBinaryPath&&i.endsWith(".data")?n.assetBinaryPath.toString():i}),yield t.o(r),t})}function Xr(t,e){let n=b(t.baseOptions,Wn,1)||new Wn;typeof e=="string"?(M(n,2,Xe(e)),M(n,1)):e instanceof Uint8Array&&(M(n,1,Ei(e,!1)),M(n,2)),y(t.baseOptions,0,1,n)}function Eo(t){try{let e=t.G.length;if(e===1)throw Error(t.G[0].message);if(e>1)throw Error("Encountered multiple errors: "+t.G.map(n=>n.message).join(", "))}finally{t.G=[]}}function d(t,e){t.B=Math.max(t.B,e)}function wr(t,e){t.A=new et,yt(t.A,"PassThroughCalculator"),R(t.A,"free_memory"),A(t.A,"free_memory_unused_out"),I(e,"free_memory"),wt(e,t.A)}function Be(t,e){R(t.A,e),A(t.A,e+"_unused_out")}function _r(t){t.g.addBoolToStream(!0,"free_memory",t.B)}var ci=class{constructor(t){this.g=t,this.G=[],this.B=0,this.g.setAutoRenderToScreen(!1)}l(t,e=!0){var n,r,i,s,o,a;if(e){let h=t.baseOptions||{};if((n=t.baseOptions)!=null&&n.modelAssetBuffer&&((r=t.baseOptions)!=null&&r.modelAssetPath))throw Error("Cannot set both baseOptions.modelAssetPath and baseOptions.modelAssetBuffer");if(!((i=b(this.baseOptions,Wn,1))!=null&&i.g()||(s=b(this.baseOptions,Wn,1))!=null&&s.h()||(o=t.baseOptions)!=null&&o.modelAssetBuffer||(a=t.baseOptions)!=null&&a.modelAssetPath))throw Error("Either baseOptions.modelAssetPath or baseOptions.modelAssetBuffer must be set");if(function(c,l){let u=b(c.baseOptions,fo,3);if(!u){var f=u=new fo,w=new oo;cn(f,4,Bn,w)}"delegate"in l&&(l.delegate==="GPU"?(l=u,f=new lh,cn(l,2,Bn,f)):(l=u,f=new oo,cn(l,4,Bn,f))),y(c.baseOptions,0,3,u)}(this,h),h.modelAssetPath)return fetch(h.modelAssetPath.toString()).then(c=>{if(c.ok)return c.arrayBuffer();throw Error(`Failed to fetch model: ${h.modelAssetPath} (${c.status})`)}).then(c=>{try{this.g.i.FS_unlink("/model.dat")}catch(l){}this.g.i.FS_createDataFile("/","model.dat",new Uint8Array(c),!0,!1,!1),Xr(this,"/model.dat"),this.m(),this.J()});if(h.modelAssetBuffer instanceof Uint8Array)Xr(this,h.modelAssetBuffer);else if(h.modelAssetBuffer)return function(c){return X(this,null,function*(){let l=[];for(var u=0;;){let{done:f,value:w}=yield c.read();if(f)break;l.push(w),u+=w.length}if(l.length===0)return new Uint8Array(0);if(l.length===1)return l[0];c=new Uint8Array(u),u=0;for(let f of l)c.set(f,u),u+=f.length;return c})}(h.modelAssetBuffer).then(c=>{Xr(this,c),this.m(),this.J()})}return this.m(),this.J(),Promise.resolve()}J(){}ca(){let t;if(this.g.ca(e=>{t=fh(e)}),!t)throw Error("Failed to retrieve CalculatorGraphConfig");return t}setGraph(t,e){this.g.attachErrorListener((n,r)=>{this.G.push(Error(r))}),this.g.Ha(),this.g.setGraph(t,e),this.A=void 0,Eo(this)}finishProcessing(){this.g.finishProcessing(),Eo(this)}close(){this.A=void 0,this.g.closeGraph()}};function tt(t,e){if(!t)throw Error(`Unable to obtain required WebGL resource: ${e}`);return t}ci.prototype.close=ci.prototype.close;var ui=class{constructor(e,n,r,i){this.g=e,this.h=n,this.m=r,this.l=i}bind(){this.g.bindVertexArray(this.h)}close(){this.g.deleteVertexArray(this.h),this.g.deleteBuffer(this.m),this.g.deleteBuffer(this.l)}};function To(t,e,n){let r=t.g;if(n=tt(r.createShader(n),"Failed to create WebGL shader"),r.shaderSource(n,e),r.compileShader(n),!r.getShaderParameter(n,r.COMPILE_STATUS))throw Error(`Could not compile WebGL shader: ${r.getShaderInfoLog(n)}`);return r.attachShader(t.h,n),n}function Ao(t,e){let n=t.g,r=tt(n.createVertexArray(),"Failed to create vertex array");n.bindVertexArray(r);let i=tt(n.createBuffer(),"Failed to create buffer");n.bindBuffer(n.ARRAY_BUFFER,i),n.enableVertexAttribArray(t.P),n.vertexAttribPointer(t.P,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,1,1,1,-1]),n.STATIC_DRAW);let s=tt(n.createBuffer(),"Failed to create buffer");return n.bindBuffer(n.ARRAY_BUFFER,s),n.enableVertexAttribArray(t.J),n.vertexAttribPointer(t.J,2,n.FLOAT,!1,0,0),n.bufferData(n.ARRAY_BUFFER,new Float32Array(e?[0,1,0,0,1,0,1,1]:[0,0,0,1,1,1,1,0]),n.STATIC_DRAW),n.bindBuffer(n.ARRAY_BUFFER,null),n.bindVertexArray(null),new ui(n,r,i,s)}function os(t,e){if(t.g){if(e!==t.g)throw Error("Cannot change GL context once initialized")}else t.g=e}function Tn(t,e,n,r){return os(t,e),t.h||(t.m(),t.C()),n?(t.s||(t.s=Ao(t,!0)),n=t.s):(t.v||(t.v=Ao(t,!1)),n=t.v),e.useProgram(t.h),n.bind(),t.l(),t=r(),n.g.bindVertexArray(null),t}function ce(t,e,n){return os(t,e),t=tt(e.createTexture(),"Failed to create texture"),e.bindTexture(e.TEXTURE_2D,t),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,n!=null?n:e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,n!=null?n:e.LINEAR),e.bindTexture(e.TEXTURE_2D,null),t}function Er(t,e,n){os(t,e),t.A||(t.A=tt(e.createFramebuffer(),"Failed to create framebuffe.")),e.bindFramebuffer(e.FRAMEBUFFER,t.A),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,n,0)}function as(t){var e;(e=t.g)==null||e.bindFramebuffer(t.g.FRAMEBUFFER,null)}var Ze=class{G(){return`
  precision mediump float;
  varying vec2 vTex;
  uniform sampler2D inputTexture;
  void main() {
    gl_FragColor = texture2D(inputTexture, vTex);
  }
 `}m(){let t=this.g;if(this.h=tt(t.createProgram(),"Failed to create WebGL program"),this.Z=To(this,`
  attribute vec2 aVertex;
  attribute vec2 aTex;
  varying vec2 vTex;
  void main(void) {
    gl_Position = vec4(aVertex, 0.0, 1.0);
    vTex = aTex;
  }`,t.VERTEX_SHADER),this.Y=To(this,this.G(),t.FRAGMENT_SHADER),t.linkProgram(this.h),!t.getProgramParameter(this.h,t.LINK_STATUS))throw Error(`Error during program linking: ${t.getProgramInfoLog(this.h)}`);this.P=t.getAttribLocation(this.h,"aVertex"),this.J=t.getAttribLocation(this.h,"aTex")}C(){}l(){}close(){if(this.h){let t=this.g;t.deleteProgram(this.h),t.deleteShader(this.Z),t.deleteShader(this.Y)}this.A&&this.g.deleteFramebuffer(this.A),this.v&&this.v.close(),this.s&&this.s.close()}},Nh=class extends Ze{G(){return`
  precision mediump float;
  uniform sampler2D backgroundTexture;
  uniform sampler2D maskTexture;
  uniform sampler2D colorMappingTexture;
  varying vec2 vTex;
  void main() {
    vec4 backgroundColor = texture2D(backgroundTexture, vTex);
    float category = texture2D(maskTexture, vTex).r;
    vec4 categoryColor = texture2D(colorMappingTexture, vec2(category, 0.0));
    gl_FragColor = mix(backgroundColor, categoryColor, categoryColor.a);
  }
 `}C(){let t=this.g;t.activeTexture(t.TEXTURE1),this.B=ce(this,t,t.LINEAR),t.activeTexture(t.TEXTURE2),this.j=ce(this,t,t.NEAREST)}m(){super.m();let t=this.g;this.L=tt(t.getUniformLocation(this.h,"backgroundTexture"),"Uniform location"),this.U=tt(t.getUniformLocation(this.h,"colorMappingTexture"),"Uniform location"),this.K=tt(t.getUniformLocation(this.h,"maskTexture"),"Uniform location")}l(){super.l();let t=this.g;t.uniform1i(this.K,0),t.uniform1i(this.L,1),t.uniform1i(this.U,2)}close(){this.B&&this.g.deleteTexture(this.B),this.j&&this.g.deleteTexture(this.j),super.close()}},Gh=class extends Ze{G(){return`
  precision mediump float;
  uniform sampler2D maskTexture;
  uniform sampler2D defaultTexture;
  uniform sampler2D overlayTexture;
  varying vec2 vTex;
  void main() {
    float confidence = texture2D(maskTexture, vTex).r;
    vec4 defaultColor = texture2D(defaultTexture, vTex);
    vec4 overlayColor = texture2D(overlayTexture, vTex);
    // Apply the alpha from the overlay and merge in the default color
    overlayColor = mix(defaultColor, overlayColor, overlayColor.a);
    gl_FragColor = mix(defaultColor, overlayColor, confidence);
  }
 `}C(){let t=this.g;t.activeTexture(t.TEXTURE1),this.j=ce(this,t),t.activeTexture(t.TEXTURE2),this.B=ce(this,t)}m(){super.m();let t=this.g;this.K=tt(t.getUniformLocation(this.h,"defaultTexture"),"Uniform location"),this.L=tt(t.getUniformLocation(this.h,"overlayTexture"),"Uniform location"),this.H=tt(t.getUniformLocation(this.h,"maskTexture"),"Uniform location")}l(){super.l();let t=this.g;t.uniform1i(this.H,0),t.uniform1i(this.K,1),t.uniform1i(this.L,2)}close(){this.j&&this.g.deleteTexture(this.j),this.B&&this.g.deleteTexture(this.B),super.close()}};function Xt(t,e){switch(e){case 0:return t.g.find(n=>n instanceof Uint8Array);case 1:return t.g.find(n=>n instanceof Float32Array);case 2:return t.g.find(n=>typeof WebGLTexture!="undefined"&&n instanceof WebGLTexture);default:throw Error(`Type is not supported: ${e}`)}}function li(t){var e=Xt(t,1);if(!e){if(e=Xt(t,0))e=new Float32Array(e).map(r=>r/255);else{e=new Float32Array(t.width*t.height);let r=Ne(t);var n=hs(t);if(Er(n,r,L1(t)),"iPad Simulator;iPhone Simulator;iPod Simulator;iPad;iPhone;iPod".split(";").includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"document"in self&&"ontouchend"in self.document){n=new Float32Array(t.width*t.height*4),r.readPixels(0,0,t.width,t.height,r.RGBA,r.FLOAT,n);for(let i=0,s=0;i<e.length;++i,s+=4)e[i]=n[s]}else r.readPixels(0,0,t.width,t.height,r.RED,r.FLOAT,e)}t.g.push(e)}return e}function L1(t){let e=Xt(t,2);if(!e){let n=Ne(t);e=M1(t);let r=li(t),i=F1(t);n.texImage2D(n.TEXTURE_2D,0,i,t.width,t.height,0,n.RED,n.FLOAT,r),fi(t)}return e}function Ne(t){if(!t.canvas)throw Error("Conversion to different image formats require that a canvas is passed when initializing the image.");return t.h||(t.h=tt(t.canvas.getContext("webgl2"),"You cannot use a canvas that is already bound to a different type of rendering context.")),t.h}function F1(t){if(t=Ne(t),!Rn)if(t.getExtension("EXT_color_buffer_float")&&t.getExtension("OES_texture_float_linear")&&t.getExtension("EXT_float_blend"))Rn=t.R32F;else{if(!t.getExtension("EXT_color_buffer_half_float"))throw Error("GPU does not fully support 4-channel float32 or float16 formats");Rn=t.R16F}return Rn}function hs(t){return t.l||(t.l=new Ze),t.l}function M1(t){let e=Ne(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=Xt(t,2);return n||(n=ce(hs(t),e,t.m?e.LINEAR:e.NEAREST),t.g.push(n),t.j=!0),e.bindTexture(e.TEXTURE_2D,n),n}function fi(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}var Rn,Y=class{constructor(t,e,n,r,i,s,o){this.g=t,this.m=e,this.j=n,this.canvas=r,this.l=i,this.width=s,this.height=o,this.j&&--bo===0&&console.error("You seem to be creating MPMask instances without invoking .close(). This leaks resources.")}Da(){return!!Xt(this,0)}ja(){return!!Xt(this,1)}R(){return!!Xt(this,2)}ia(){return(e=Xt(t=this,0))||(e=li(t),e=new Uint8Array(e.map(n=>255*n)),t.g.push(e)),e;var t,e}ha(){return li(this)}N(){return L1(this)}clone(){let t=[];for(let e of this.g){let n;if(e instanceof Uint8Array)n=new Uint8Array(e);else if(e instanceof Float32Array)n=new Float32Array(e);else{if(!(e instanceof WebGLTexture))throw Error(`Type is not supported: ${e}`);{let r=Ne(this),i=hs(this);r.activeTexture(r.TEXTURE1),n=ce(i,r,this.m?r.LINEAR:r.NEAREST),r.bindTexture(r.TEXTURE_2D,n);let s=F1(this);r.texImage2D(r.TEXTURE_2D,0,s,this.width,this.height,0,r.RED,r.FLOAT,null),r.bindTexture(r.TEXTURE_2D,null),Er(i,r,n),Tn(i,r,!1,()=>{M1(this),r.clearColor(0,0,0,0),r.clear(r.COLOR_BUFFER_BIT),r.drawArrays(r.TRIANGLE_FAN,0,4),fi(this)}),as(i),fi(this)}}t.push(n)}return new Y(t,this.m,this.R(),this.canvas,this.l,this.width,this.height)}close(){this.j&&Ne(this).deleteTexture(Xt(this,2)),bo=-1}};Y.prototype.close=Y.prototype.close,Y.prototype.clone=Y.prototype.clone,Y.prototype.getAsWebGLTexture=Y.prototype.N,Y.prototype.getAsFloat32Array=Y.prototype.ha,Y.prototype.getAsUint8Array=Y.prototype.ia,Y.prototype.hasWebGLTexture=Y.prototype.R,Y.prototype.hasFloat32Array=Y.prototype.ja,Y.prototype.hasUint8Array=Y.prototype.Da;var bo=250,zh={color:"white",lineWidth:4,radius:6};function Wr(t){return Cr(Cs(Cr({},zh),{fillColor:(t=t||{}).color}),t)}function jt(t,e){return t instanceof Function?t(e):t}function xo(t,e,n){return Math.max(Math.min(e,n),Math.min(Math.max(e,n),t))}function sn(t){if(!t.l)throw Error("CPU rendering requested but CanvasRenderingContext2D not provided.");return t.l}function yn(t){if(!t.j)throw Error("GPU rendering requested but WebGL2RenderingContext not provided.");return t.j}function ko(t,e,n){var r;if(e.R())n(e.N());else{let i=e.ja()?e.ha():e.ia();t.m=(r=t.m)!=null?r:new Ze;let s=yn(t);n((t=new Y([i],e.m,!1,s.canvas,t.m,e.width,e.height)).N()),t.close()}}function So(t,e,n,r){let i=function(a){return a.g||(a.g=new Nh),a.g}(t),s=yn(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n;Tn(i,s,!0,()=>{(function(h,c,l,u){let f=h.g;if(f.activeTexture(f.TEXTURE0),f.bindTexture(f.TEXTURE_2D,c),f.activeTexture(f.TEXTURE1),f.bindTexture(f.TEXTURE_2D,h.B),f.texImage2D(f.TEXTURE_2D,0,f.RGBA,f.RGBA,f.UNSIGNED_BYTE,l),h.H&&function(w,_){if(w!==_)return!1;w=w.entries(),_=_.entries();for(let[B,H]of w){w=B;let st=H;var E=_.next();if(E.done)return!1;let[U,Qe]=E.value;if(E=Qe,w!==U||st[0]!==E[0]||st[1]!==E[1]||st[2]!==E[2]||st[3]!==E[3])return!1}return!!_.next().done}(h.H,u))f.activeTexture(f.TEXTURE2),f.bindTexture(f.TEXTURE_2D,h.j);else{h.H=u;let w=Array(1024).fill(0);u.forEach((_,E)=>{if(_.length!==4)throw Error(`Color at index ${E} is not a four-channel value.`);w[4*E]=_[0],w[4*E+1]=_[1],w[4*E+2]=_[2],w[4*E+3]=_[3]}),f.activeTexture(f.TEXTURE2),f.bindTexture(f.TEXTURE_2D,h.j),f.texImage2D(f.TEXTURE_2D,0,f.RGBA,256,1,0,f.RGBA,f.UNSIGNED_BYTE,new Uint8Array(w))}})(i,e,o,r),s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT),s.drawArrays(s.TRIANGLE_FAN,0,4);let a=i.g;a.activeTexture(a.TEXTURE0),a.bindTexture(a.TEXTURE_2D,null),a.activeTexture(a.TEXTURE1),a.bindTexture(a.TEXTURE_2D,null),a.activeTexture(a.TEXTURE2),a.bindTexture(a.TEXTURE_2D,null)})}function Lo(t,e,n,r){let i=yn(t),s=function(h){return h.h||(h.h=new Gh),h.h}(t),o=Array.isArray(n)?new ImageData(new Uint8ClampedArray(n),1,1):n,a=Array.isArray(r)?new ImageData(new Uint8ClampedArray(r),1,1):r;Tn(s,i,!0,()=>{var h=s.g;h.activeTexture(h.TEXTURE0),h.bindTexture(h.TEXTURE_2D,e),h.activeTexture(h.TEXTURE1),h.bindTexture(h.TEXTURE_2D,s.j),h.texImage2D(h.TEXTURE_2D,0,h.RGBA,h.RGBA,h.UNSIGNED_BYTE,o),h.activeTexture(h.TEXTURE2),h.bindTexture(h.TEXTURE_2D,s.B),h.texImage2D(h.TEXTURE_2D,0,h.RGBA,h.RGBA,h.UNSIGNED_BYTE,a),i.clearColor(0,0,0,0),i.clear(i.COLOR_BUFFER_BIT),i.drawArrays(i.TRIANGLE_FAN,0,4),i.bindTexture(i.TEXTURE_2D,null),(h=s.g).activeTexture(h.TEXTURE0),h.bindTexture(h.TEXTURE_2D,null),h.activeTexture(h.TEXTURE1),h.bindTexture(h.TEXTURE_2D,null),h.activeTexture(h.TEXTURE2),h.bindTexture(h.TEXTURE_2D,null)})}var ot=class{constructor(t,e){typeof CanvasRenderingContext2D!="undefined"&&t instanceof CanvasRenderingContext2D||t instanceof OffscreenCanvasRenderingContext2D?(this.l=t,this.j=e):this.j=t}wa(t,e){if(t){var n=sn(this);e=Wr(e),n.save();var r=n.canvas,i=0;for(let s of t)n.fillStyle=jt(e.fillColor,{index:i,from:s}),n.strokeStyle=jt(e.color,{index:i,from:s}),n.lineWidth=jt(e.lineWidth,{index:i,from:s}),(t=new Path2D).arc(s.x*r.width,s.y*r.height,jt(e.radius,{index:i,from:s}),0,2*Math.PI),n.fill(t),n.stroke(t),++i;n.restore()}}va(t,e,n){if(t&&e){var r=sn(this);n=Wr(n),r.save();var i=r.canvas,s=0;for(let o of e){r.beginPath(),e=t[o.start];let a=t[o.end];e&&a&&(r.strokeStyle=jt(n.color,{index:s,from:e,to:a}),r.lineWidth=jt(n.lineWidth,{index:s,from:e,to:a}),r.moveTo(e.x*i.width,e.y*i.height),r.lineTo(a.x*i.width,a.y*i.height)),++s,r.stroke()}r.restore()}}sa(t,e){let n=sn(this);e=Wr(e),n.save(),n.beginPath(),n.lineWidth=jt(e.lineWidth,{}),n.strokeStyle=jt(e.color,{}),n.fillStyle=jt(e.fillColor,{}),n.moveTo(t.originX,t.originY),n.lineTo(t.originX+t.width,t.originY),n.lineTo(t.originX+t.width,t.originY+t.height),n.lineTo(t.originX,t.originY+t.height),n.lineTo(t.originX,t.originY),n.stroke(),n.fill(),n.restore()}ta(t,e,n=[0,0,0,255]){this.l?function(r,i,s,o){let a=yn(r);ko(r,i,h=>{So(r,h,s,o),(h=sn(r)).drawImage(a.canvas,0,0,h.canvas.width,h.canvas.height)})}(this,t,n,e):So(this,t.N(),n,e)}ua(t,e,n){this.l?function(r,i,s,o){let a=yn(r);ko(r,i,h=>{Lo(r,h,s,o),(h=sn(r)).drawImage(a.canvas,0,0,h.canvas.width,h.canvas.height)})}(this,t,e,n):Lo(this,t.N(),e,n)}close(){var t,e,n;(t=this.g)==null||t.close(),this.g=void 0,(e=this.h)==null||e.close(),this.h=void 0,(n=this.m)==null||n.close(),this.m=void 0}};function Ot(t,e){switch(e){case 0:return t.g.find(n=>n instanceof ImageData);case 1:return t.g.find(n=>typeof ImageBitmap!="undefined"&&n instanceof ImageBitmap);case 2:return t.g.find(n=>typeof WebGLTexture!="undefined"&&n instanceof WebGLTexture);default:throw Error(`Type is not supported: ${e}`)}}function R1(t){var e=Ot(t,0);if(!e){e=Ge(t);let n=Tr(t),r=new Uint8Array(t.width*t.height*4);Er(n,e,Nn(t)),e.readPixels(0,0,t.width,t.height,e.RGBA,e.UNSIGNED_BYTE,r),as(n),e=new ImageData(new Uint8ClampedArray(r.buffer),t.width,t.height),t.g.push(e)}return e}function Nn(t){let e=Ot(t,2);if(!e){let n=Ge(t);e=Gn(t);let r=Ot(t,1)||R1(t);n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,r),an(t)}return e}function Ge(t){if(!t.canvas)throw Error("Conversion to different image formats require that a canvas is passed when initializing the image.");return t.h||(t.h=tt(t.canvas.getContext("webgl2"),"You cannot use a canvas that is already bound to a different type of rendering context.")),t.h}function Tr(t){return t.l||(t.l=new Ze),t.l}function Gn(t){let e=Ge(t);e.viewport(0,0,t.width,t.height),e.activeTexture(e.TEXTURE0);let n=Ot(t,2);return n||(n=ce(Tr(t),e),t.g.push(n),t.m=!0),e.bindTexture(e.TEXTURE_2D,n),n}function an(t){t.h.bindTexture(t.h.TEXTURE_2D,null)}function Fo(t){let e=Ge(t);return Tn(Tr(t),e,!0,()=>function(n,r){let i=n.canvas;if(i.width===n.width&&i.height===n.height)return r();let s=i.width,o=i.height;return i.width=n.width,i.height=n.height,n=r(),i.width=s,i.height=o,n}(t,()=>{if(e.bindFramebuffer(e.FRAMEBUFFER,null),e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),e.drawArrays(e.TRIANGLE_FAN,0,4),!(t.canvas instanceof OffscreenCanvas))throw Error("Conversion to ImageBitmap requires that the MediaPipe Tasks is initialized with an OffscreenCanvas");return t.canvas.transferToImageBitmap()}))}ot.prototype.close=ot.prototype.close,ot.prototype.drawConfidenceMask=ot.prototype.ua,ot.prototype.drawCategoryMask=ot.prototype.ta,ot.prototype.drawBoundingBox=ot.prototype.sa,ot.prototype.drawConnectors=ot.prototype.va,ot.prototype.drawLandmarks=ot.prototype.wa,ot.lerp=function(t,e,n,r,i){return xo(r*(1-(t-e)/(n-e))+i*(1-(n-t)/(n-e)),r,i)},ot.clamp=xo;var Z=class{constructor(t,e,n,r,i,s,o){this.g=t,this.j=e,this.m=n,this.canvas=r,this.l=i,this.width=s,this.height=o,(this.j||this.m)&&--Mo===0&&console.error("You seem to be creating MPImage instances without invoking .close(). This leaks resources.")}Ca(){return!!Ot(this,0)}ka(){return!!Ot(this,1)}R(){return!!Ot(this,2)}Aa(){return R1(this)}za(){var t=Ot(this,1);return t||(Nn(this),Gn(this),t=Fo(this),an(this),this.g.push(t),this.j=!0),t}N(){return Nn(this)}clone(){let t=[];for(let e of this.g){let n;if(e instanceof ImageData)n=new ImageData(e.data,this.width,this.height);else if(e instanceof WebGLTexture){let r=Ge(this),i=Tr(this);r.activeTexture(r.TEXTURE1),n=ce(i,r),r.bindTexture(r.TEXTURE_2D,n),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,this.width,this.height,0,r.RGBA,r.UNSIGNED_BYTE,null),r.bindTexture(r.TEXTURE_2D,null),Er(i,r,n),Tn(i,r,!1,()=>{Gn(this),r.clearColor(0,0,0,0),r.clear(r.COLOR_BUFFER_BIT),r.drawArrays(r.TRIANGLE_FAN,0,4),an(this)}),as(i),an(this)}else{if(!(e instanceof ImageBitmap))throw Error(`Type is not supported: ${e}`);Nn(this),Gn(this),n=Fo(this),an(this)}t.push(n)}return new Z(t,this.ka(),this.R(),this.canvas,this.l,this.width,this.height)}close(){this.j&&Ot(this,1).close(),this.m&&Ge(this).deleteTexture(Ot(this,2)),Mo=-1}};Z.prototype.close=Z.prototype.close,Z.prototype.clone=Z.prototype.clone,Z.prototype.getAsWebGLTexture=Z.prototype.N,Z.prototype.getAsImageBitmap=Z.prototype.za,Z.prototype.getAsImageData=Z.prototype.Aa,Z.prototype.hasWebGLTexture=Z.prototype.R,Z.prototype.hasImageBitmap=Z.prototype.ka,Z.prototype.hasImageData=Z.prototype.Ca;var Mo=250;function Ut(...t){return t.map(([e,n])=>({start:e,end:n}))}var jh=function(t){return class extends t{Ha(){this.i._registerModelResourcesGraphService()}}}((Ro=class{constructor(t,e){this.l=!0,this.i=t,this.g=null,this.h=0,this.m=typeof this.i._addIntToInputStream=="function",e!==void 0?this.i.canvas=e:k1()?this.i.canvas=new OffscreenCanvas(1,1):(console.warn("OffscreenCanvas not supported and GraphRunner constructor glCanvas parameter is undefined. Creating backup canvas."),this.i.canvas=document.createElement("canvas"))}initializeGraph(t){return X(this,null,function*(){let e=yield(yield fetch(t)).arrayBuffer();t=!(t.endsWith(".pbtxt")||t.endsWith(".textproto")),this.setGraph(new Uint8Array(e),t)})}setGraphFromString(t){this.setGraph(new TextEncoder().encode(t),!1)}setGraph(t,e){let n=t.length,r=this.i._malloc(n);this.i.HEAPU8.set(t,r),e?this.i._changeBinaryGraph(n,r):this.i._changeTextGraph(n,r),this.i._free(r)}configureAudio(t,e,n,r,i){this.i._configureAudio||console.warn('Attempting to use configureAudio without support for input audio. Is build dep ":gl_graph_runner_audio" missing?'),m(this,r||"input_audio",s=>{m(this,i=i||"audio_header",o=>{this.i._configureAudio(s,o,t,e!=null?e:0,n)})})}setAutoResizeCanvas(t){this.l=t}setAutoRenderToScreen(t){this.i._setAutoRenderToScreen(t)}setGpuBufferVerticalFlip(t){this.i.gpuOriginForWebTexturesIsBottomLeft=t}ca(t){Ct(this,"__graph_config__",e=>{t(e)}),m(this,"__graph_config__",e=>{this.i._getGraphConfig(e,void 0)}),delete this.i.simpleListeners.__graph_config__}attachErrorListener(t){this.i.errorListener=t}attachEmptyPacketListener(t,e){this.i.emptyPacketListeners=this.i.emptyPacketListeners||{},this.i.emptyPacketListeners[t]=e}addAudioToStream(t,e,n){this.addAudioToStreamWithShape(t,0,0,e,n)}addAudioToStreamWithShape(t,e,n,r,i){let s=4*t.length;this.h!==s&&(this.g&&this.i._free(this.g),this.g=this.i._malloc(s),this.h=s),this.i.HEAPF32.set(t,this.g/4),m(this,r,o=>{this.i._addAudioToInputStream(this.g,e,n,o,i)})}addGpuBufferToStream(t,e,n){m(this,e,r=>{let[i,s]=wo(this,t,r);this.i._addBoundTextureToStream(r,i,s,n)})}addBoolToStream(t,e,n){m(this,e,r=>{this.i._addBoolToInputStream(t,r,n)})}addDoubleToStream(t,e,n){m(this,e,r=>{this.i._addDoubleToInputStream(t,r,n)})}addFloatToStream(t,e,n){m(this,e,r=>{this.i._addFloatToInputStream(t,r,n)})}addIntToStream(t,e,n){m(this,e,r=>{this.i._addIntToInputStream(t,r,n)})}addUintToStream(t,e,n){m(this,e,r=>{this.i._addUintToInputStream(t,r,n)})}addStringToStream(t,e,n){m(this,e,r=>{m(this,t,i=>{this.i._addStringToInputStream(i,r,n)})})}addStringRecordToStream(t,e,n){m(this,e,r=>{_o(this,Object.keys(t),i=>{_o(this,Object.values(t),s=>{this.i._addFlatHashMapToInputStream(i,s,Object.keys(t).length,r,n)})})})}addProtoToStream(t,e,n,r){m(this,n,i=>{m(this,e,s=>{let o=this.i._malloc(t.length);this.i.HEAPU8.set(t,o),this.i._addProtoToInputStream(o,t.length,s,i,r),this.i._free(o)})})}addEmptyPacketToStream(t,e){m(this,t,n=>{this.i._addEmptyPacketToInputStream(n,e)})}addBoolVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateBoolVector(t.length);if(!i)throw Error("Unable to allocate new bool vector on heap.");for(let s of t)this.i._addBoolVectorEntry(i,s);this.i._addBoolVectorToInputStream(i,r,n)})}addDoubleVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateDoubleVector(t.length);if(!i)throw Error("Unable to allocate new double vector on heap.");for(let s of t)this.i._addDoubleVectorEntry(i,s);this.i._addDoubleVectorToInputStream(i,r,n)})}addFloatVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateFloatVector(t.length);if(!i)throw Error("Unable to allocate new float vector on heap.");for(let s of t)this.i._addFloatVectorEntry(i,s);this.i._addFloatVectorToInputStream(i,r,n)})}addIntVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateIntVector(t.length);if(!i)throw Error("Unable to allocate new int vector on heap.");for(let s of t)this.i._addIntVectorEntry(i,s);this.i._addIntVectorToInputStream(i,r,n)})}addUintVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateUintVector(t.length);if(!i)throw Error("Unable to allocate new unsigned int vector on heap.");for(let s of t)this.i._addUintVectorEntry(i,s);this.i._addUintVectorToInputStream(i,r,n)})}addStringVectorToStream(t,e,n){m(this,e,r=>{let i=this.i._allocateStringVector(t.length);if(!i)throw Error("Unable to allocate new string vector on heap.");for(let s of t)m(this,s,o=>{this.i._addStringVectorEntry(i,o)});this.i._addStringVectorToInputStream(i,r,n)})}addBoolToInputSidePacket(t,e){m(this,e,n=>{this.i._addBoolToInputSidePacket(t,n)})}addDoubleToInputSidePacket(t,e){m(this,e,n=>{this.i._addDoubleToInputSidePacket(t,n)})}addFloatToInputSidePacket(t,e){m(this,e,n=>{this.i._addFloatToInputSidePacket(t,n)})}addIntToInputSidePacket(t,e){m(this,e,n=>{this.i._addIntToInputSidePacket(t,n)})}addUintToInputSidePacket(t,e){m(this,e,n=>{this.i._addUintToInputSidePacket(t,n)})}addStringToInputSidePacket(t,e){m(this,e,n=>{m(this,t,r=>{this.i._addStringToInputSidePacket(r,n)})})}addProtoToInputSidePacket(t,e,n){m(this,n,r=>{m(this,e,i=>{let s=this.i._malloc(t.length);this.i.HEAPU8.set(t,s),this.i._addProtoToInputSidePacket(s,t.length,i,r),this.i._free(s)})})}addBoolVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateBoolVector(t.length);if(!r)throw Error("Unable to allocate new bool vector on heap.");for(let i of t)this.i._addBoolVectorEntry(r,i);this.i._addBoolVectorToInputSidePacket(r,n)})}addDoubleVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateDoubleVector(t.length);if(!r)throw Error("Unable to allocate new double vector on heap.");for(let i of t)this.i._addDoubleVectorEntry(r,i);this.i._addDoubleVectorToInputSidePacket(r,n)})}addFloatVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateFloatVector(t.length);if(!r)throw Error("Unable to allocate new float vector on heap.");for(let i of t)this.i._addFloatVectorEntry(r,i);this.i._addFloatVectorToInputSidePacket(r,n)})}addIntVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateIntVector(t.length);if(!r)throw Error("Unable to allocate new int vector on heap.");for(let i of t)this.i._addIntVectorEntry(r,i);this.i._addIntVectorToInputSidePacket(r,n)})}addUintVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateUintVector(t.length);if(!r)throw Error("Unable to allocate new unsigned int vector on heap.");for(let i of t)this.i._addUintVectorEntry(r,i);this.i._addUintVectorToInputSidePacket(r,n)})}addStringVectorToInputSidePacket(t,e){m(this,e,n=>{let r=this.i._allocateStringVector(t.length);if(!r)throw Error("Unable to allocate new string vector on heap.");for(let i of t)m(this,i,s=>{this.i._addStringVectorEntry(r,s)});this.i._addStringVectorToInputSidePacket(r,n)})}attachBoolListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachBoolListener(n)})}attachBoolVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachBoolVectorListener(n)})}attachIntListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachIntListener(n)})}attachIntVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachIntVectorListener(n)})}attachUintListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachUintListener(n)})}attachUintVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachUintVectorListener(n)})}attachDoubleListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachDoubleListener(n)})}attachDoubleVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachDoubleVectorListener(n)})}attachFloatListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachFloatListener(n)})}attachFloatVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachFloatVectorListener(n)})}attachStringListener(t,e){Ct(this,t,e),m(this,t,n=>{this.i._attachStringListener(n)})}attachStringVectorListener(t,e){te(this,t,e),m(this,t,n=>{this.i._attachStringVectorListener(n)})}attachProtoListener(t,e,n){Ct(this,t,e),m(this,t,r=>{this.i._attachProtoListener(r,n||!1)})}attachProtoVectorListener(t,e,n){te(this,t,e),m(this,t,r=>{this.i._attachProtoVectorListener(r,n||!1)})}attachAudioListener(t,e,n){this.i._attachAudioListener||console.warn('Attempting to use attachAudioListener without support for output audio. Is build dep ":gl_graph_runner_audio_out" missing?'),Ct(this,t,(r,i)=>{r=new Float32Array(r.buffer,r.byteOffset,r.length/4),e(r,i)}),m(this,t,r=>{this.i._attachAudioListener(r,n||!1)})}finishProcessing(){this.i._waitUntilIdle()}closeGraph(){this.i._closeGraph(),this.i.simpleListeners=void 0,this.i.emptyPacketListeners=void 0}},class extends Ro{get ea(){return this.i}oa(t,e,n){m(this,e,r=>{let[i,s]=wo(this,t,r);this.ea._addBoundTextureAsImageToStream(r,i,s,n)})}V(t,e){Ct(this,t,e),m(this,t,n=>{this.ea._attachImageListener(n)})}ba(t,e){te(this,t,e),m(this,t,n=>{this.ea._attachImageVectorListener(n)})}})),Ro,St=class extends jh{};function T(t,e,n){return X(this,null,function*(){var r;return function(i,s,o,a){return X(this,null,function*(){return Bh(i,s,o,a)})}(t,(r=n.canvas)!=null?r:k1()?void 0:document.createElement("canvas"),e,n)})}function I1(t,e,n,r){if(t.U){let s=new e1;if(n!=null&&n.regionOfInterest){if(!t.na)throw Error("This task doesn't support region-of-interest.");var i=n.regionOfInterest;if(i.left>=i.right||i.top>=i.bottom)throw Error("Expected RectF with left < right and top < bottom.");if(i.left<0||i.top<0||i.right>1||i.bottom>1)throw Error("Expected RectF values to be in [0,1].");g(s,1,(i.left+i.right)/2),g(s,2,(i.top+i.bottom)/2),g(s,4,i.right-i.left),g(s,3,i.bottom-i.top)}else g(s,1,.5),g(s,2,.5),g(s,4,1),g(s,3,1);if(n!=null&&n.rotationDegrees){if((n==null?void 0:n.rotationDegrees)%90!=0)throw Error("Expected rotation to be a multiple of 90\xB0.");if(g(s,5,-Math.PI*n.rotationDegrees/180),(n==null?void 0:n.rotationDegrees)%180!=0){let[o,a]=S1(e);n=j(s,3)*a/o,i=j(s,4)*o/a,g(s,4,n),g(s,3,i)}}t.g.addProtoToStream(s.g(),"mediapipe.NormalizedRect",t.U,r)}t.g.oa(e,t.Z,r!=null?r:performance.now()),t.finishProcessing()}function Lt(t,e,n){var r;if((r=t.baseOptions)!=null&&r.g())throw Error("Task is not initialized with image mode. 'runningMode' must be set to 'IMAGE'.");I1(t,e,n,t.B+1)}function Gt(t,e,n,r){var i;if(!((i=t.baseOptions)!=null&&i.g()))throw Error("Task is not initialized with video mode. 'runningMode' must be set to 'VIDEO'.");I1(t,e,n,r)}function ze(t,e,n,r){var i=e.data;let s=e.width,o=s*(e=e.height);if((i instanceof Uint8Array||i instanceof Float32Array)&&i.length!==o)throw Error("Unsupported channel count: "+i.length/o);return t=new Y([i],n,!1,t.g.i.canvas,t.P,s,e),r?t.clone():t}var ct=class extends ci{constructor(t,e,n,r){super(t),this.g=t,this.Z=e,this.U=n,this.na=r,this.P=new Ze}l(t,e=!0){if("runningMode"in t&&mn(this.baseOptions,2,!!t.runningMode&&t.runningMode!=="IMAGE"),t.canvas!==void 0&&this.g.i.canvas!==t.canvas)throw Error("You must create a new task to reset the canvas.");return super.l(t,e)}close(){this.P.close(),super.close()}};ct.prototype.close=ct.prototype.close;var at=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect_in",!1),this.j={detections:[]},y(t=this.h=new vr,0,1,e=new D),g(this.h,2,.5),g(this.h,3,.3)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n;return"minDetectionConfidence"in t&&g(this.h,2,(e=t.minDetectionConfidence)!=null?e:.5),"minSuppressionThreshold"in t&&g(this.h,3,(n=t.minSuppressionThreshold)!=null?n:.3),this.l(t)}D(t,e){return this.j={detections:[]},Lt(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},Gt(this,t,n,e),this.j}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect_in"),x(t,"detections");let e=new mt;Mt(e,bh,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.face_detector.FaceDetectorGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect_in"),A(n,"DETECTIONS:detections"),n.o(e),wt(t,n),this.g.attachProtoVectorListener("detections",(r,i)=>{for(let s of r)r=Ja(s),this.j.detections.push(b1(r));d(this,i)}),this.g.attachEmptyPacketListener("detections",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};at.prototype.detectForVideo=at.prototype.F,at.prototype.detect=at.prototype.D,at.prototype.setOptions=at.prototype.o,at.createFromModelPath=function(t,e){return X(this,null,function*(){return T(at,t,{baseOptions:{modelAssetPath:e}})})},at.createFromModelBuffer=function(t,e){return T(at,t,{baseOptions:{modelAssetBuffer:e}})},at.createFromOptions=function(t,e){return T(at,t,e)};var cs=Ut([61,146],[146,91],[91,181],[181,84],[84,17],[17,314],[314,405],[405,321],[321,375],[375,291],[61,185],[185,40],[40,39],[39,37],[37,0],[0,267],[267,269],[269,270],[270,409],[409,291],[78,95],[95,88],[88,178],[178,87],[87,14],[14,317],[317,402],[402,318],[318,324],[324,308],[78,191],[191,80],[80,81],[81,82],[82,13],[13,312],[312,311],[311,310],[310,415],[415,308]),us=Ut([263,249],[249,390],[390,373],[373,374],[374,380],[380,381],[381,382],[382,362],[263,466],[466,388],[388,387],[387,386],[386,385],[385,384],[384,398],[398,362]),ls=Ut([276,283],[283,282],[282,295],[295,285],[300,293],[293,334],[334,296],[296,336]),U1=Ut([474,475],[475,476],[476,477],[477,474]),fs=Ut([33,7],[7,163],[163,144],[144,145],[145,153],[153,154],[154,155],[155,133],[33,246],[246,161],[161,160],[160,159],[159,158],[158,157],[157,173],[173,133]),ds=Ut([46,53],[53,52],[52,65],[65,55],[70,63],[63,105],[105,66],[66,107]),C1=Ut([469,470],[470,471],[471,472],[472,469]),ps=Ut([10,338],[338,297],[297,332],[332,284],[284,251],[251,389],[389,356],[356,454],[454,323],[323,361],[361,288],[288,397],[397,365],[365,379],[379,378],[378,400],[400,377],[377,152],[152,148],[148,176],[176,149],[149,150],[150,136],[136,172],[172,58],[58,132],[132,93],[93,234],[234,127],[127,162],[162,21],[21,54],[54,103],[103,67],[67,109],[109,10]),P1=[...cs,...us,...ls,...fs,...ds,...ps],D1=Ut([127,34],[34,139],[139,127],[11,0],[0,37],[37,11],[232,231],[231,120],[120,232],[72,37],[37,39],[39,72],[128,121],[121,47],[47,128],[232,121],[121,128],[128,232],[104,69],[69,67],[67,104],[175,171],[171,148],[148,175],[118,50],[50,101],[101,118],[73,39],[39,40],[40,73],[9,151],[151,108],[108,9],[48,115],[115,131],[131,48],[194,204],[204,211],[211,194],[74,40],[40,185],[185,74],[80,42],[42,183],[183,80],[40,92],[92,186],[186,40],[230,229],[229,118],[118,230],[202,212],[212,214],[214,202],[83,18],[18,17],[17,83],[76,61],[61,146],[146,76],[160,29],[29,30],[30,160],[56,157],[157,173],[173,56],[106,204],[204,194],[194,106],[135,214],[214,192],[192,135],[203,165],[165,98],[98,203],[21,71],[71,68],[68,21],[51,45],[45,4],[4,51],[144,24],[24,23],[23,144],[77,146],[146,91],[91,77],[205,50],[50,187],[187,205],[201,200],[200,18],[18,201],[91,106],[106,182],[182,91],[90,91],[91,181],[181,90],[85,84],[84,17],[17,85],[206,203],[203,36],[36,206],[148,171],[171,140],[140,148],[92,40],[40,39],[39,92],[193,189],[189,244],[244,193],[159,158],[158,28],[28,159],[247,246],[246,161],[161,247],[236,3],[3,196],[196,236],[54,68],[68,104],[104,54],[193,168],[168,8],[8,193],[117,228],[228,31],[31,117],[189,193],[193,55],[55,189],[98,97],[97,99],[99,98],[126,47],[47,100],[100,126],[166,79],[79,218],[218,166],[155,154],[154,26],[26,155],[209,49],[49,131],[131,209],[135,136],[136,150],[150,135],[47,126],[126,217],[217,47],[223,52],[52,53],[53,223],[45,51],[51,134],[134,45],[211,170],[170,140],[140,211],[67,69],[69,108],[108,67],[43,106],[106,91],[91,43],[230,119],[119,120],[120,230],[226,130],[130,247],[247,226],[63,53],[53,52],[52,63],[238,20],[20,242],[242,238],[46,70],[70,156],[156,46],[78,62],[62,96],[96,78],[46,53],[53,63],[63,46],[143,34],[34,227],[227,143],[123,117],[117,111],[111,123],[44,125],[125,19],[19,44],[236,134],[134,51],[51,236],[216,206],[206,205],[205,216],[154,153],[153,22],[22,154],[39,37],[37,167],[167,39],[200,201],[201,208],[208,200],[36,142],[142,100],[100,36],[57,212],[212,202],[202,57],[20,60],[60,99],[99,20],[28,158],[158,157],[157,28],[35,226],[226,113],[113,35],[160,159],[159,27],[27,160],[204,202],[202,210],[210,204],[113,225],[225,46],[46,113],[43,202],[202,204],[204,43],[62,76],[76,77],[77,62],[137,123],[123,116],[116,137],[41,38],[38,72],[72,41],[203,129],[129,142],[142,203],[64,98],[98,240],[240,64],[49,102],[102,64],[64,49],[41,73],[73,74],[74,41],[212,216],[216,207],[207,212],[42,74],[74,184],[184,42],[169,170],[170,211],[211,169],[170,149],[149,176],[176,170],[105,66],[66,69],[69,105],[122,6],[6,168],[168,122],[123,147],[147,187],[187,123],[96,77],[77,90],[90,96],[65,55],[55,107],[107,65],[89,90],[90,180],[180,89],[101,100],[100,120],[120,101],[63,105],[105,104],[104,63],[93,137],[137,227],[227,93],[15,86],[86,85],[85,15],[129,102],[102,49],[49,129],[14,87],[87,86],[86,14],[55,8],[8,9],[9,55],[100,47],[47,121],[121,100],[145,23],[23,22],[22,145],[88,89],[89,179],[179,88],[6,122],[122,196],[196,6],[88,95],[95,96],[96,88],[138,172],[172,136],[136,138],[215,58],[58,172],[172,215],[115,48],[48,219],[219,115],[42,80],[80,81],[81,42],[195,3],[3,51],[51,195],[43,146],[146,61],[61,43],[171,175],[175,199],[199,171],[81,82],[82,38],[38,81],[53,46],[46,225],[225,53],[144,163],[163,110],[110,144],[52,65],[65,66],[66,52],[229,228],[228,117],[117,229],[34,127],[127,234],[234,34],[107,108],[108,69],[69,107],[109,108],[108,151],[151,109],[48,64],[64,235],[235,48],[62,78],[78,191],[191,62],[129,209],[209,126],[126,129],[111,35],[35,143],[143,111],[117,123],[123,50],[50,117],[222,65],[65,52],[52,222],[19,125],[125,141],[141,19],[221,55],[55,65],[65,221],[3,195],[195,197],[197,3],[25,7],[7,33],[33,25],[220,237],[237,44],[44,220],[70,71],[71,139],[139,70],[122,193],[193,245],[245,122],[247,130],[130,33],[33,247],[71,21],[21,162],[162,71],[170,169],[169,150],[150,170],[188,174],[174,196],[196,188],[216,186],[186,92],[92,216],[2,97],[97,167],[167,2],[141,125],[125,241],[241,141],[164,167],[167,37],[37,164],[72,38],[38,12],[12,72],[38,82],[82,13],[13,38],[63,68],[68,71],[71,63],[226,35],[35,111],[111,226],[101,50],[50,205],[205,101],[206,92],[92,165],[165,206],[209,198],[198,217],[217,209],[165,167],[167,97],[97,165],[220,115],[115,218],[218,220],[133,112],[112,243],[243,133],[239,238],[238,241],[241,239],[214,135],[135,169],[169,214],[190,173],[173,133],[133,190],[171,208],[208,32],[32,171],[125,44],[44,237],[237,125],[86,87],[87,178],[178,86],[85,86],[86,179],[179,85],[84,85],[85,180],[180,84],[83,84],[84,181],[181,83],[201,83],[83,182],[182,201],[137,93],[93,132],[132,137],[76,62],[62,183],[183,76],[61,76],[76,184],[184,61],[57,61],[61,185],[185,57],[212,57],[57,186],[186,212],[214,207],[207,187],[187,214],[34,143],[143,156],[156,34],[79,239],[239,237],[237,79],[123,137],[137,177],[177,123],[44,1],[1,4],[4,44],[201,194],[194,32],[32,201],[64,102],[102,129],[129,64],[213,215],[215,138],[138,213],[59,166],[166,219],[219,59],[242,99],[99,97],[97,242],[2,94],[94,141],[141,2],[75,59],[59,235],[235,75],[24,110],[110,228],[228,24],[25,130],[130,226],[226,25],[23,24],[24,229],[229,23],[22,23],[23,230],[230,22],[26,22],[22,231],[231,26],[112,26],[26,232],[232,112],[189,190],[190,243],[243,189],[221,56],[56,190],[190,221],[28,56],[56,221],[221,28],[27,28],[28,222],[222,27],[29,27],[27,223],[223,29],[30,29],[29,224],[224,30],[247,30],[30,225],[225,247],[238,79],[79,20],[20,238],[166,59],[59,75],[75,166],[60,75],[75,240],[240,60],[147,177],[177,215],[215,147],[20,79],[79,166],[166,20],[187,147],[147,213],[213,187],[112,233],[233,244],[244,112],[233,128],[128,245],[245,233],[128,114],[114,188],[188,128],[114,217],[217,174],[174,114],[131,115],[115,220],[220,131],[217,198],[198,236],[236,217],[198,131],[131,134],[134,198],[177,132],[132,58],[58,177],[143,35],[35,124],[124,143],[110,163],[163,7],[7,110],[228,110],[110,25],[25,228],[356,389],[389,368],[368,356],[11,302],[302,267],[267,11],[452,350],[350,349],[349,452],[302,303],[303,269],[269,302],[357,343],[343,277],[277,357],[452,453],[453,357],[357,452],[333,332],[332,297],[297,333],[175,152],[152,377],[377,175],[347,348],[348,330],[330,347],[303,304],[304,270],[270,303],[9,336],[336,337],[337,9],[278,279],[279,360],[360,278],[418,262],[262,431],[431,418],[304,408],[408,409],[409,304],[310,415],[415,407],[407,310],[270,409],[409,410],[410,270],[450,348],[348,347],[347,450],[422,430],[430,434],[434,422],[313,314],[314,17],[17,313],[306,307],[307,375],[375,306],[387,388],[388,260],[260,387],[286,414],[414,398],[398,286],[335,406],[406,418],[418,335],[364,367],[367,416],[416,364],[423,358],[358,327],[327,423],[251,284],[284,298],[298,251],[281,5],[5,4],[4,281],[373,374],[374,253],[253,373],[307,320],[320,321],[321,307],[425,427],[427,411],[411,425],[421,313],[313,18],[18,421],[321,405],[405,406],[406,321],[320,404],[404,405],[405,320],[315,16],[16,17],[17,315],[426,425],[425,266],[266,426],[377,400],[400,369],[369,377],[322,391],[391,269],[269,322],[417,465],[465,464],[464,417],[386,257],[257,258],[258,386],[466,260],[260,388],[388,466],[456,399],[399,419],[419,456],[284,332],[332,333],[333,284],[417,285],[285,8],[8,417],[346,340],[340,261],[261,346],[413,441],[441,285],[285,413],[327,460],[460,328],[328,327],[355,371],[371,329],[329,355],[392,439],[439,438],[438,392],[382,341],[341,256],[256,382],[429,420],[420,360],[360,429],[364,394],[394,379],[379,364],[277,343],[343,437],[437,277],[443,444],[444,283],[283,443],[275,440],[440,363],[363,275],[431,262],[262,369],[369,431],[297,338],[338,337],[337,297],[273,375],[375,321],[321,273],[450,451],[451,349],[349,450],[446,342],[342,467],[467,446],[293,334],[334,282],[282,293],[458,461],[461,462],[462,458],[276,353],[353,383],[383,276],[308,324],[324,325],[325,308],[276,300],[300,293],[293,276],[372,345],[345,447],[447,372],[352,345],[345,340],[340,352],[274,1],[1,19],[19,274],[456,248],[248,281],[281,456],[436,427],[427,425],[425,436],[381,256],[256,252],[252,381],[269,391],[391,393],[393,269],[200,199],[199,428],[428,200],[266,330],[330,329],[329,266],[287,273],[273,422],[422,287],[250,462],[462,328],[328,250],[258,286],[286,384],[384,258],[265,353],[353,342],[342,265],[387,259],[259,257],[257,387],[424,431],[431,430],[430,424],[342,353],[353,276],[276,342],[273,335],[335,424],[424,273],[292,325],[325,307],[307,292],[366,447],[447,345],[345,366],[271,303],[303,302],[302,271],[423,266],[266,371],[371,423],[294,455],[455,460],[460,294],[279,278],[278,294],[294,279],[271,272],[272,304],[304,271],[432,434],[434,427],[427,432],[272,407],[407,408],[408,272],[394,430],[430,431],[431,394],[395,369],[369,400],[400,395],[334,333],[333,299],[299,334],[351,417],[417,168],[168,351],[352,280],[280,411],[411,352],[325,319],[319,320],[320,325],[295,296],[296,336],[336,295],[319,403],[403,404],[404,319],[330,348],[348,349],[349,330],[293,298],[298,333],[333,293],[323,454],[454,447],[447,323],[15,16],[16,315],[315,15],[358,429],[429,279],[279,358],[14,15],[15,316],[316,14],[285,336],[336,9],[9,285],[329,349],[349,350],[350,329],[374,380],[380,252],[252,374],[318,402],[402,403],[403,318],[6,197],[197,419],[419,6],[318,319],[319,325],[325,318],[367,364],[364,365],[365,367],[435,367],[367,397],[397,435],[344,438],[438,439],[439,344],[272,271],[271,311],[311,272],[195,5],[5,281],[281,195],[273,287],[287,291],[291,273],[396,428],[428,199],[199,396],[311,271],[271,268],[268,311],[283,444],[444,445],[445,283],[373,254],[254,339],[339,373],[282,334],[334,296],[296,282],[449,347],[347,346],[346,449],[264,447],[447,454],[454,264],[336,296],[296,299],[299,336],[338,10],[10,151],[151,338],[278,439],[439,455],[455,278],[292,407],[407,415],[415,292],[358,371],[371,355],[355,358],[340,345],[345,372],[372,340],[346,347],[347,280],[280,346],[442,443],[443,282],[282,442],[19,94],[94,370],[370,19],[441,442],[442,295],[295,441],[248,419],[419,197],[197,248],[263,255],[255,359],[359,263],[440,275],[275,274],[274,440],[300,383],[383,368],[368,300],[351,412],[412,465],[465,351],[263,467],[467,466],[466,263],[301,368],[368,389],[389,301],[395,378],[378,379],[379,395],[412,351],[351,419],[419,412],[436,426],[426,322],[322,436],[2,164],[164,393],[393,2],[370,462],[462,461],[461,370],[164,0],[0,267],[267,164],[302,11],[11,12],[12,302],[268,12],[12,13],[13,268],[293,300],[300,301],[301,293],[446,261],[261,340],[340,446],[330,266],[266,425],[425,330],[426,423],[423,391],[391,426],[429,355],[355,437],[437,429],[391,327],[327,326],[326,391],[440,457],[457,438],[438,440],[341,382],[382,362],[362,341],[459,457],[457,461],[461,459],[434,430],[430,394],[394,434],[414,463],[463,362],[362,414],[396,369],[369,262],[262,396],[354,461],[461,457],[457,354],[316,403],[403,402],[402,316],[315,404],[404,403],[403,315],[314,405],[405,404],[404,314],[313,406],[406,405],[405,313],[421,418],[418,406],[406,421],[366,401],[401,361],[361,366],[306,408],[408,407],[407,306],[291,409],[409,408],[408,291],[287,410],[410,409],[409,287],[432,436],[436,410],[410,432],[434,416],[416,411],[411,434],[264,368],[368,383],[383,264],[309,438],[438,457],[457,309],[352,376],[376,401],[401,352],[274,275],[275,4],[4,274],[421,428],[428,262],[262,421],[294,327],[327,358],[358,294],[433,416],[416,367],[367,433],[289,455],[455,439],[439,289],[462,370],[370,326],[326,462],[2,326],[326,370],[370,2],[305,460],[460,455],[455,305],[254,449],[449,448],[448,254],[255,261],[261,446],[446,255],[253,450],[450,449],[449,253],[252,451],[451,450],[450,252],[256,452],[452,451],[451,256],[341,453],[453,452],[452,341],[413,464],[464,463],[463,413],[441,413],[413,414],[414,441],[258,442],[442,441],[441,258],[257,443],[443,442],[442,257],[259,444],[444,443],[443,259],[260,445],[445,444],[444,260],[467,342],[342,445],[445,467],[459,458],[458,250],[250,459],[289,392],[392,290],[290,289],[290,328],[328,460],[460,290],[376,433],[433,435],[435,376],[250,290],[290,392],[392,250],[411,416],[416,433],[433,411],[341,463],[463,464],[464,341],[453,464],[464,465],[465,453],[357,465],[465,412],[412,357],[343,412],[412,399],[399,343],[360,363],[363,440],[440,360],[437,399],[399,456],[456,437],[420,456],[456,363],[363,420],[401,435],[435,288],[288,401],[372,383],[383,353],[353,372],[339,255],[255,249],[249,339],[448,261],[261,255],[255,448],[133,243],[243,190],[190,133],[133,155],[155,112],[112,133],[33,246],[246,247],[247,33],[33,130],[130,25],[25,33],[398,384],[384,286],[286,398],[362,398],[398,414],[414,362],[362,463],[463,341],[341,362],[263,359],[359,467],[467,263],[263,249],[249,255],[255,263],[466,467],[467,260],[260,466],[75,60],[60,166],[166,75],[238,239],[239,79],[79,238],[162,127],[127,139],[139,162],[72,11],[11,37],[37,72],[121,232],[232,120],[120,121],[73,72],[72,39],[39,73],[114,128],[128,47],[47,114],[233,232],[232,128],[128,233],[103,104],[104,67],[67,103],[152,175],[175,148],[148,152],[119,118],[118,101],[101,119],[74,73],[73,40],[40,74],[107,9],[9,108],[108,107],[49,48],[48,131],[131,49],[32,194],[194,211],[211,32],[184,74],[74,185],[185,184],[191,80],[80,183],[183,191],[185,40],[40,186],[186,185],[119,230],[230,118],[118,119],[210,202],[202,214],[214,210],[84,83],[83,17],[17,84],[77,76],[76,146],[146,77],[161,160],[160,30],[30,161],[190,56],[56,173],[173,190],[182,106],[106,194],[194,182],[138,135],[135,192],[192,138],[129,203],[203,98],[98,129],[54,21],[21,68],[68,54],[5,51],[51,4],[4,5],[145,144],[144,23],[23,145],[90,77],[77,91],[91,90],[207,205],[205,187],[187,207],[83,201],[201,18],[18,83],[181,91],[91,182],[182,181],[180,90],[90,181],[181,180],[16,85],[85,17],[17,16],[205,206],[206,36],[36,205],[176,148],[148,140],[140,176],[165,92],[92,39],[39,165],[245,193],[193,244],[244,245],[27,159],[159,28],[28,27],[30,247],[247,161],[161,30],[174,236],[236,196],[196,174],[103,54],[54,104],[104,103],[55,193],[193,8],[8,55],[111,117],[117,31],[31,111],[221,189],[189,55],[55,221],[240,98],[98,99],[99,240],[142,126],[126,100],[100,142],[219,166],[166,218],[218,219],[112,155],[155,26],[26,112],[198,209],[209,131],[131,198],[169,135],[135,150],[150,169],[114,47],[47,217],[217,114],[224,223],[223,53],[53,224],[220,45],[45,134],[134,220],[32,211],[211,140],[140,32],[109,67],[67,108],[108,109],[146,43],[43,91],[91,146],[231,230],[230,120],[120,231],[113,226],[226,247],[247,113],[105,63],[63,52],[52,105],[241,238],[238,242],[242,241],[124,46],[46,156],[156,124],[95,78],[78,96],[96,95],[70,46],[46,63],[63,70],[116,143],[143,227],[227,116],[116,123],[123,111],[111,116],[1,44],[44,19],[19,1],[3,236],[236,51],[51,3],[207,216],[216,205],[205,207],[26,154],[154,22],[22,26],[165,39],[39,167],[167,165],[199,200],[200,208],[208,199],[101,36],[36,100],[100,101],[43,57],[57,202],[202,43],[242,20],[20,99],[99,242],[56,28],[28,157],[157,56],[124,35],[35,113],[113,124],[29,160],[160,27],[27,29],[211,204],[204,210],[210,211],[124,113],[113,46],[46,124],[106,43],[43,204],[204,106],[96,62],[62,77],[77,96],[227,137],[137,116],[116,227],[73,41],[41,72],[72,73],[36,203],[203,142],[142,36],[235,64],[64,240],[240,235],[48,49],[49,64],[64,48],[42,41],[41,74],[74,42],[214,212],[212,207],[207,214],[183,42],[42,184],[184,183],[210,169],[169,211],[211,210],[140,170],[170,176],[176,140],[104,105],[105,69],[69,104],[193,122],[122,168],[168,193],[50,123],[123,187],[187,50],[89,96],[96,90],[90,89],[66,65],[65,107],[107,66],[179,89],[89,180],[180,179],[119,101],[101,120],[120,119],[68,63],[63,104],[104,68],[234,93],[93,227],[227,234],[16,15],[15,85],[85,16],[209,129],[129,49],[49,209],[15,14],[14,86],[86,15],[107,55],[55,9],[9,107],[120,100],[100,121],[121,120],[153,145],[145,22],[22,153],[178,88],[88,179],[179,178],[197,6],[6,196],[196,197],[89,88],[88,96],[96,89],[135,138],[138,136],[136,135],[138,215],[215,172],[172,138],[218,115],[115,219],[219,218],[41,42],[42,81],[81,41],[5,195],[195,51],[51,5],[57,43],[43,61],[61,57],[208,171],[171,199],[199,208],[41,81],[81,38],[38,41],[224,53],[53,225],[225,224],[24,144],[144,110],[110,24],[105,52],[52,66],[66,105],[118,229],[229,117],[117,118],[227,34],[34,234],[234,227],[66,107],[107,69],[69,66],[10,109],[109,151],[151,10],[219,48],[48,235],[235,219],[183,62],[62,191],[191,183],[142,129],[129,126],[126,142],[116,111],[111,143],[143,116],[118,117],[117,50],[50,118],[223,222],[222,52],[52,223],[94,19],[19,141],[141,94],[222,221],[221,65],[65,222],[196,3],[3,197],[197,196],[45,220],[220,44],[44,45],[156,70],[70,139],[139,156],[188,122],[122,245],[245,188],[139,71],[71,162],[162,139],[149,170],[170,150],[150,149],[122,188],[188,196],[196,122],[206,216],[216,92],[92,206],[164,2],[2,167],[167,164],[242,141],[141,241],[241,242],[0,164],[164,37],[37,0],[11,72],[72,12],[12,11],[12,38],[38,13],[13,12],[70,63],[63,71],[71,70],[31,226],[226,111],[111,31],[36,101],[101,205],[205,36],[203,206],[206,165],[165,203],[126,209],[209,217],[217,126],[98,165],[165,97],[97,98],[237,220],[220,218],[218,237],[237,239],[239,241],[241,237],[210,214],[214,169],[169,210],[140,171],[171,32],[32,140],[241,125],[125,237],[237,241],[179,86],[86,178],[178,179],[180,85],[85,179],[179,180],[181,84],[84,180],[180,181],[182,83],[83,181],[181,182],[194,201],[201,182],[182,194],[177,137],[137,132],[132,177],[184,76],[76,183],[183,184],[185,61],[61,184],[184,185],[186,57],[57,185],[185,186],[216,212],[212,186],[186,216],[192,214],[214,187],[187,192],[139,34],[34,156],[156,139],[218,79],[79,237],[237,218],[147,123],[123,177],[177,147],[45,44],[44,4],[4,45],[208,201],[201,32],[32,208],[98,64],[64,129],[129,98],[192,213],[213,138],[138,192],[235,59],[59,219],[219,235],[141,242],[242,97],[97,141],[97,2],[2,141],[141,97],[240,75],[75,235],[235,240],[229,24],[24,228],[228,229],[31,25],[25,226],[226,31],[230,23],[23,229],[229,230],[231,22],[22,230],[230,231],[232,26],[26,231],[231,232],[233,112],[112,232],[232,233],[244,189],[189,243],[243,244],[189,221],[221,190],[190,189],[222,28],[28,221],[221,222],[223,27],[27,222],[222,223],[224,29],[29,223],[223,224],[225,30],[30,224],[224,225],[113,247],[247,225],[225,113],[99,60],[60,240],[240,99],[213,147],[147,215],[215,213],[60,20],[20,166],[166,60],[192,187],[187,213],[213,192],[243,112],[112,244],[244,243],[244,233],[233,245],[245,244],[245,128],[128,188],[188,245],[188,114],[114,174],[174,188],[134,131],[131,220],[220,134],[174,217],[217,236],[236,174],[236,198],[198,134],[134,236],[215,177],[177,58],[58,215],[156,143],[143,124],[124,156],[25,110],[110,7],[7,25],[31,228],[228,25],[25,31],[264,356],[356,368],[368,264],[0,11],[11,267],[267,0],[451,452],[452,349],[349,451],[267,302],[302,269],[269,267],[350,357],[357,277],[277,350],[350,452],[452,357],[357,350],[299,333],[333,297],[297,299],[396,175],[175,377],[377,396],[280,347],[347,330],[330,280],[269,303],[303,270],[270,269],[151,9],[9,337],[337,151],[344,278],[278,360],[360,344],[424,418],[418,431],[431,424],[270,304],[304,409],[409,270],[272,310],[310,407],[407,272],[322,270],[270,410],[410,322],[449,450],[450,347],[347,449],[432,422],[422,434],[434,432],[18,313],[313,17],[17,18],[291,306],[306,375],[375,291],[259,387],[387,260],[260,259],[424,335],[335,418],[418,424],[434,364],[364,416],[416,434],[391,423],[423,327],[327,391],[301,251],[251,298],[298,301],[275,281],[281,4],[4,275],[254,373],[373,253],[253,254],[375,307],[307,321],[321,375],[280,425],[425,411],[411,280],[200,421],[421,18],[18,200],[335,321],[321,406],[406,335],[321,320],[320,405],[405,321],[314,315],[315,17],[17,314],[423,426],[426,266],[266,423],[396,377],[377,369],[369,396],[270,322],[322,269],[269,270],[413,417],[417,464],[464,413],[385,386],[386,258],[258,385],[248,456],[456,419],[419,248],[298,284],[284,333],[333,298],[168,417],[417,8],[8,168],[448,346],[346,261],[261,448],[417,413],[413,285],[285,417],[326,327],[327,328],[328,326],[277,355],[355,329],[329,277],[309,392],[392,438],[438,309],[381,382],[382,256],[256,381],[279,429],[429,360],[360,279],[365,364],[364,379],[379,365],[355,277],[277,437],[437,355],[282,443],[443,283],[283,282],[281,275],[275,363],[363,281],[395,431],[431,369],[369,395],[299,297],[297,337],[337,299],[335,273],[273,321],[321,335],[348,450],[450,349],[349,348],[359,446],[446,467],[467,359],[283,293],[293,282],[282,283],[250,458],[458,462],[462,250],[300,276],[276,383],[383,300],[292,308],[308,325],[325,292],[283,276],[276,293],[293,283],[264,372],[372,447],[447,264],[346,352],[352,340],[340,346],[354,274],[274,19],[19,354],[363,456],[456,281],[281,363],[426,436],[436,425],[425,426],[380,381],[381,252],[252,380],[267,269],[269,393],[393,267],[421,200],[200,428],[428,421],[371,266],[266,329],[329,371],[432,287],[287,422],[422,432],[290,250],[250,328],[328,290],[385,258],[258,384],[384,385],[446,265],[265,342],[342,446],[386,387],[387,257],[257,386],[422,424],[424,430],[430,422],[445,342],[342,276],[276,445],[422,273],[273,424],[424,422],[306,292],[292,307],[307,306],[352,366],[366,345],[345,352],[268,271],[271,302],[302,268],[358,423],[423,371],[371,358],[327,294],[294,460],[460,327],[331,279],[279,294],[294,331],[303,271],[271,304],[304,303],[436,432],[432,427],[427,436],[304,272],[272,408],[408,304],[395,394],[394,431],[431,395],[378,395],[395,400],[400,378],[296,334],[334,299],[299,296],[6,351],[351,168],[168,6],[376,352],[352,411],[411,376],[307,325],[325,320],[320,307],[285,295],[295,336],[336,285],[320,319],[319,404],[404,320],[329,330],[330,349],[349,329],[334,293],[293,333],[333,334],[366,323],[323,447],[447,366],[316,15],[15,315],[315,316],[331,358],[358,279],[279,331],[317,14],[14,316],[316,317],[8,285],[285,9],[9,8],[277,329],[329,350],[350,277],[253,374],[374,252],[252,253],[319,318],[318,403],[403,319],[351,6],[6,419],[419,351],[324,318],[318,325],[325,324],[397,367],[367,365],[365,397],[288,435],[435,397],[397,288],[278,344],[344,439],[439,278],[310,272],[272,311],[311,310],[248,195],[195,281],[281,248],[375,273],[273,291],[291,375],[175,396],[396,199],[199,175],[312,311],[311,268],[268,312],[276,283],[283,445],[445,276],[390,373],[373,339],[339,390],[295,282],[282,296],[296,295],[448,449],[449,346],[346,448],[356,264],[264,454],[454,356],[337,336],[336,299],[299,337],[337,338],[338,151],[151,337],[294,278],[278,455],[455,294],[308,292],[292,415],[415,308],[429,358],[358,355],[355,429],[265,340],[340,372],[372,265],[352,346],[346,280],[280,352],[295,442],[442,282],[282,295],[354,19],[19,370],[370,354],[285,441],[441,295],[295,285],[195,248],[248,197],[197,195],[457,440],[440,274],[274,457],[301,300],[300,368],[368,301],[417,351],[351,465],[465,417],[251,301],[301,389],[389,251],[394,395],[395,379],[379,394],[399,412],[412,419],[419,399],[410,436],[436,322],[322,410],[326,2],[2,393],[393,326],[354,370],[370,461],[461,354],[393,164],[164,267],[267,393],[268,302],[302,12],[12,268],[312,268],[268,13],[13,312],[298,293],[293,301],[301,298],[265,446],[446,340],[340,265],[280,330],[330,425],[425,280],[322,426],[426,391],[391,322],[420,429],[429,437],[437,420],[393,391],[391,326],[326,393],[344,440],[440,438],[438,344],[458,459],[459,461],[461,458],[364,434],[434,394],[394,364],[428,396],[396,262],[262,428],[274,354],[354,457],[457,274],[317,316],[316,402],[402,317],[316,315],[315,403],[403,316],[315,314],[314,404],[404,315],[314,313],[313,405],[405,314],[313,421],[421,406],[406,313],[323,366],[366,361],[361,323],[292,306],[306,407],[407,292],[306,291],[291,408],[408,306],[291,287],[287,409],[409,291],[287,432],[432,410],[410,287],[427,434],[434,411],[411,427],[372,264],[264,383],[383,372],[459,309],[309,457],[457,459],[366,352],[352,401],[401,366],[1,274],[274,4],[4,1],[418,421],[421,262],[262,418],[331,294],[294,358],[358,331],[435,433],[433,367],[367,435],[392,289],[289,439],[439,392],[328,462],[462,326],[326,328],[94,2],[2,370],[370,94],[289,305],[305,455],[455,289],[339,254],[254,448],[448,339],[359,255],[255,446],[446,359],[254,253],[253,449],[449,254],[253,252],[252,450],[450,253],[252,256],[256,451],[451,252],[256,341],[341,452],[452,256],[414,413],[413,463],[463,414],[286,441],[441,414],[414,286],[286,258],[258,441],[441,286],[258,257],[257,442],[442,258],[257,259],[259,443],[443,257],[259,260],[260,444],[444,259],[260,467],[467,445],[445,260],[309,459],[459,250],[250,309],[305,289],[289,290],[290,305],[305,290],[290,460],[460,305],[401,376],[376,435],[435,401],[309,250],[250,392],[392,309],[376,411],[411,433],[433,376],[453,341],[341,464],[464,453],[357,453],[453,465],[465,357],[343,357],[357,412],[412,343],[437,343],[343,399],[399,437],[344,360],[360,440],[440,344],[420,437],[437,456],[456,420],[360,420],[420,363],[363,360],[361,401],[401,288],[288,361],[265,372],[372,353],[353,265],[390,339],[339,249],[249,390],[339,448],[448,255],[255,339]);function Io(t){t.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]}}var N=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!1),this.j={faceLandmarks:[],faceBlendshapes:[],facialTransformationMatrixes:[]},this.outputFacialTransformationMatrixes=this.outputFaceBlendshapes=!1,y(t=this.h=new o1,0,1,e=new D),this.v=new s1,y(this.h,0,3,this.v),this.s=new vr,y(this.h,0,2,this.s),Nt(this.s,4,1),g(this.s,2,.5),g(this.v,2,.5),g(this.h,4,.5)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n,r,i;return"numFaces"in t&&Nt(this.s,4,(e=t.numFaces)!=null?e:1),"minFaceDetectionConfidence"in t&&g(this.s,2,(n=t.minFaceDetectionConfidence)!=null?n:.5),"minTrackingConfidence"in t&&g(this.h,4,(r=t.minTrackingConfidence)!=null?r:.5),"minFacePresenceConfidence"in t&&g(this.v,2,(i=t.minFacePresenceConfidence)!=null?i:.5),"outputFaceBlendshapes"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),"outputFacialTransformationMatrixes"in t&&(this.outputFacialTransformationMatrixes=!!t.outputFacialTransformationMatrixes),this.l(t)}D(t,e){return Io(this),Lt(this,t,e),this.j}F(t,e,n){return Io(this),Gt(this,t,n,e),this.j}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"face_landmarks");let e=new mt;Mt(e,kh,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.face_landmarker.FaceLandmarkerGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"NORM_LANDMARKS:face_landmarks"),n.o(e),wt(t,n),this.g.attachProtoVectorListener("face_landmarks",(r,i)=>{for(let s of r)r=En(s),this.j.faceLandmarks.push(yr(r));d(this,i)}),this.g.attachEmptyPacketListener("face_landmarks",r=>{d(this,r)}),this.outputFaceBlendshapes&&(x(t,"blendshapes"),A(n,"BLENDSHAPES:blendshapes"),this.g.attachProtoVectorListener("blendshapes",(r,i)=>{var s;if(this.outputFaceBlendshapes)for(let o of r)r=mr(o),this.j.faceBlendshapes.push(ss((s=r.g())!=null?s:[]));d(this,i)}),this.g.attachEmptyPacketListener("blendshapes",r=>{d(this,r)})),this.outputFacialTransformationMatrixes&&(x(t,"face_geometry"),A(n,"FACE_GEOMETRY:face_geometry"),this.g.attachProtoVectorListener("face_geometry",(r,i)=>{var s,o,a,h,c;if(this.outputFacialTransformationMatrixes)for(let l of r)(r=b(xh(l),mh,2))&&this.j.facialTransformationMatrixes.push({rows:(o=(s=xt(r,1))!=null?s:0)!=null?o:0,columns:(h=(a=xt(r,2))!=null?a:0)!=null?h:0,data:(c=me(r,3,le,ge()).slice())!=null?c:[]});d(this,i)}),this.g.attachEmptyPacketListener("face_geometry",r=>{d(this,r)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};N.prototype.detectForVideo=N.prototype.F,N.prototype.detect=N.prototype.D,N.prototype.setOptions=N.prototype.o,N.createFromModelPath=function(t,e){return T(N,t,{baseOptions:{modelAssetPath:e}})},N.createFromModelBuffer=function(t,e){return T(N,t,{baseOptions:{modelAssetBuffer:e}})},N.createFromOptions=function(t,e){return T(N,t,e)},N.FACE_LANDMARKS_LIPS=cs,N.FACE_LANDMARKS_LEFT_EYE=us,N.FACE_LANDMARKS_LEFT_EYEBROW=ls,N.FACE_LANDMARKS_LEFT_IRIS=U1,N.FACE_LANDMARKS_RIGHT_EYE=fs,N.FACE_LANDMARKS_RIGHT_EYEBROW=ds,N.FACE_LANDMARKS_RIGHT_IRIS=C1,N.FACE_LANDMARKS_FACE_OVAL=ps,N.FACE_LANDMARKS_CONTOURS=P1,N.FACE_LANDMARKS_TESSELATION=D1;var Pt=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!0),y(t=this.j=new h1,0,1,e=new D)}get baseOptions(){return b(this.j,D,1)}set baseOptions(t){y(this.j,0,1,t)}o(t){return super.l(t)}Ka(t,e,n){let r=typeof e!="function"?e:{};if(this.h=typeof e=="function"?e:n,Lt(this,t,r!=null?r:{}),!this.h)return this.s}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"stylized_image");let e=new mt;Mt(e,Sh,this.j);let n=new et;yt(n,"mediapipe.tasks.vision.face_stylizer.FaceStylizerGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"STYLIZED_IMAGE:stylized_image"),n.o(e),wt(t,n),this.g.V("stylized_image",(r,i)=>{var s=!this.h,o=r.data,a=r.width;let h=a*(r=r.height);if(o instanceof Uint8Array)if(o.length===3*h){let c=new Uint8ClampedArray(4*h);for(let l=0;l<h;++l)c[4*l]=o[3*l],c[4*l+1]=o[3*l+1],c[4*l+2]=o[3*l+2],c[4*l+3]=255;o=new ImageData(c,a,r)}else{if(o.length!==4*h)throw Error("Unsupported channel count: "+o.length/h);o=new ImageData(new Uint8ClampedArray(o.buffer,o.byteOffset,o.length),a,r)}else if(!(o instanceof WebGLTexture))throw Error(`Unsupported format: ${o.constructor.name}`);a=new Z([o],!1,!1,this.g.i.canvas,this.P,a,r),this.s=s=s?a.clone():a,this.h&&this.h(s),d(this,i)}),this.g.attachEmptyPacketListener("stylized_image",r=>{this.s=null,this.h&&this.h(null),d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Pt.prototype.stylize=Pt.prototype.Ka,Pt.prototype.setOptions=Pt.prototype.o,Pt.createFromModelPath=function(t,e){return T(Pt,t,{baseOptions:{modelAssetPath:e}})},Pt.createFromModelBuffer=function(t,e){return T(Pt,t,{baseOptions:{modelAssetBuffer:e}})},Pt.createFromOptions=function(t,e){return T(Pt,t,e)};var gs=Ut([0,1],[1,2],[2,3],[3,4],[0,5],[5,6],[6,7],[7,8],[5,9],[9,10],[10,11],[11,12],[9,13],[13,14],[14,15],[15,16],[13,17],[0,17],[17,18],[18,19],[19,20]);function Uo(t){t.gestures=[],t.landmarks=[],t.worldLandmarks=[],t.handedness=[]}function Co(t){return t.gestures.length===0?{gestures:[],landmarks:[],worldLandmarks:[],handedness:[],handednesses:[]}:{gestures:t.gestures,landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handedness:t.handedness,handednesses:t.handedness}}function Po(t,e=!0){var i,s,o,a,h,c;let n=[];for(let l of t){var r=mr(l);t=[];for(let u of r.g())r=e&&xt(u,1)!=null?(i=xt(u,1))!=null?i:0:-1,t.push({score:(s=j(u,2))!=null?s:0,index:r,categoryName:(a=(o=kt(u,3))!=null?o:"")!=null?a:"",displayName:(c=(h=kt(u,4))!=null?h:"")!=null?c:""});n.push(t)}return n}var lt=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!1),this.gestures=[],this.landmarks=[],this.worldLandmarks=[],this.handedness=[],y(t=this.j=new l1,0,1,e=new D),this.s=new es,y(this.j,0,2,this.s),this.C=new ts,y(this.s,0,3,this.C),this.v=new u1,y(this.s,0,2,this.v),this.h=new Lh,y(this.j,0,3,this.h),g(this.v,2,.5),g(this.s,4,.5),g(this.C,2,.5)}get baseOptions(){return b(this.j,D,1)}set baseOptions(t){y(this.j,0,1,t)}o(t){var i,s,o,a,h,c,l,u;if(Nt(this.v,3,(i=t.numHands)!=null?i:1),"minHandDetectionConfidence"in t&&g(this.v,2,(s=t.minHandDetectionConfidence)!=null?s:.5),"minTrackingConfidence"in t&&g(this.s,4,(o=t.minTrackingConfidence)!=null?o:.5),"minHandPresenceConfidence"in t&&g(this.C,2,(a=t.minHandPresenceConfidence)!=null?a:.5),t.cannedGesturesClassifierOptions){var e=new xe,n=e,r=hi(t.cannedGesturesClassifierOptions,(h=b(this.h,xe,3))==null?void 0:h.h());y(n,0,2,r),y(this.h,0,3,e)}else t.cannedGesturesClassifierOptions===void 0&&((c=b(this.h,xe,3))==null||c.g());return t.customGesturesClassifierOptions?(y(n=e=new xe,0,2,r=hi(t.customGesturesClassifierOptions,(l=b(this.h,xe,4))==null?void 0:l.h())),y(this.h,0,4,e)):t.customGesturesClassifierOptions===void 0&&((u=b(this.h,xe,4))==null||u.g()),this.l(t)}Fa(t,e){return Uo(this),Lt(this,t,e),Co(this)}Ga(t,e,n){return Uo(this),Gt(this,t,n,e),Co(this)}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"hand_gestures"),x(t,"hand_landmarks"),x(t,"world_hand_landmarks"),x(t,"handedness");let e=new mt;Mt(e,Fh,this.j);let n=new et;yt(n,"mediapipe.tasks.vision.gesture_recognizer.GestureRecognizerGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"HAND_GESTURES:hand_gestures"),A(n,"LANDMARKS:hand_landmarks"),A(n,"WORLD_LANDMARKS:world_hand_landmarks"),A(n,"HANDEDNESS:handedness"),n.o(e),wt(t,n),this.g.attachProtoVectorListener("hand_landmarks",(r,i)=>{var s,o,a,h;for(let c of r){r=En(c);let l=[];for(let u of Yt(r,t1,1))l.push({x:(s=j(u,1))!=null?s:0,y:(o=j(u,2))!=null?o:0,z:(a=j(u,3))!=null?a:0,visibility:(h=j(u,4))!=null?h:0});this.landmarks.push(l)}d(this,i)}),this.g.attachEmptyPacketListener("hand_landmarks",r=>{d(this,r)}),this.g.attachProtoVectorListener("world_hand_landmarks",(r,i)=>{var s,o,a,h;for(let c of r){r=Me(c);let l=[];for(let u of Yt(r,Qa,1))l.push({x:(s=j(u,1))!=null?s:0,y:(o=j(u,2))!=null?o:0,z:(a=j(u,3))!=null?a:0,visibility:(h=j(u,4))!=null?h:0});this.worldLandmarks.push(l)}d(this,i)}),this.g.attachEmptyPacketListener("world_hand_landmarks",r=>{d(this,r)}),this.g.attachProtoVectorListener("hand_gestures",(r,i)=>{this.gestures.push(...Po(r,!1)),d(this,i)}),this.g.attachEmptyPacketListener("hand_gestures",r=>{d(this,r)}),this.g.attachProtoVectorListener("handedness",(r,i)=>{this.handedness.push(...Po(r)),d(this,i)}),this.g.attachEmptyPacketListener("handedness",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};function Do(t){return{landmarks:t.landmarks,worldLandmarks:t.worldLandmarks,handednesses:t.handedness,handedness:t.handedness}}lt.prototype.recognizeForVideo=lt.prototype.Ga,lt.prototype.recognize=lt.prototype.Fa,lt.prototype.setOptions=lt.prototype.o,lt.createFromModelPath=function(t,e){return T(lt,t,{baseOptions:{modelAssetPath:e}})},lt.createFromModelBuffer=function(t,e){return T(lt,t,{baseOptions:{modelAssetBuffer:e}})},lt.createFromOptions=function(t,e){return T(lt,t,e)},lt.HAND_CONNECTIONS=gs;var ft=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!1),this.landmarks=[],this.worldLandmarks=[],this.handedness=[],y(t=this.h=new es,0,1,e=new D),this.s=new ts,y(this.h,0,3,this.s),this.j=new u1,y(this.h,0,2,this.j),Nt(this.j,3,1),g(this.j,2,.5),g(this.s,2,.5),g(this.h,4,.5)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n,r,i;return"numHands"in t&&Nt(this.j,3,(e=t.numHands)!=null?e:1),"minHandDetectionConfidence"in t&&g(this.j,2,(n=t.minHandDetectionConfidence)!=null?n:.5),"minTrackingConfidence"in t&&g(this.h,4,(r=t.minTrackingConfidence)!=null?r:.5),"minHandPresenceConfidence"in t&&g(this.s,2,(i=t.minHandPresenceConfidence)!=null?i:.5),this.l(t)}D(t,e){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],Lt(this,t,e),Do(this)}F(t,e,n){return this.landmarks=[],this.worldLandmarks=[],this.handedness=[],Gt(this,t,n,e),Do(this)}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"hand_landmarks"),x(t,"world_hand_landmarks"),x(t,"handedness");let e=new mt;Mt(e,Mh,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.hand_landmarker.HandLandmarkerGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"LANDMARKS:hand_landmarks"),A(n,"WORLD_LANDMARKS:world_hand_landmarks"),A(n,"HANDEDNESS:handedness"),n.o(e),wt(t,n),this.g.attachProtoVectorListener("hand_landmarks",(r,i)=>{for(let s of r)r=En(s),this.landmarks.push(yr(r));d(this,i)}),this.g.attachEmptyPacketListener("hand_landmarks",r=>{d(this,r)}),this.g.attachProtoVectorListener("world_hand_landmarks",(r,i)=>{for(let s of r)r=Me(s),this.worldLandmarks.push(ln(r));d(this,i)}),this.g.attachEmptyPacketListener("world_hand_landmarks",r=>{d(this,r)}),this.g.attachProtoVectorListener("handedness",(r,i)=>{var h,c,l,u,f,w,_;var s=this.handedness,o=s.push;let a=[];for(let E of r){r=mr(E);let B=[];for(let H of r.g())B.push({score:(h=j(H,2))!=null?h:0,index:(l=(c=xt(H,1))!=null?c:0)!=null?l:-1,categoryName:(f=(u=kt(H,3))!=null?u:"")!=null?f:"",displayName:(_=(w=kt(H,4))!=null?w:"")!=null?_:""});a.push(B)}o.call(s,...a),d(this,i)}),this.g.attachEmptyPacketListener("handedness",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};ft.prototype.detectForVideo=ft.prototype.F,ft.prototype.detect=ft.prototype.D,ft.prototype.setOptions=ft.prototype.o,ft.createFromModelPath=function(t,e){return T(ft,t,{baseOptions:{modelAssetPath:e}})},ft.createFromModelBuffer=function(t,e){return T(ft,t,{baseOptions:{modelAssetBuffer:e}})},ft.createFromOptions=function(t,e){return T(ft,t,e)},ft.HAND_CONNECTIONS=gs;var O1=Ut([0,1],[1,2],[2,3],[3,7],[0,4],[4,5],[5,6],[6,8],[9,10],[11,12],[11,13],[13,15],[15,17],[15,19],[15,21],[17,19],[12,14],[14,16],[16,18],[16,20],[16,22],[18,20],[11,23],[12,24],[23,24],[23,25],[24,26],[25,27],[26,28],[27,29],[28,30],[29,31],[30,32],[27,31],[28,32]);function Oo(t){t.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]}}function Bo(t){try{if(!t.C)return t.h;t.C(t.h)}finally{_r(t)}}function In(t,e){t=En(t),e.push(yr(t))}var C=class extends ct{constructor(t,e){super(new St(t,e),"input_frames_image",null,!1),this.h={faceLandmarks:[],faceBlendshapes:[],poseLandmarks:[],poseWorldLandmarks:[],poseSegmentationMasks:[],leftHandLandmarks:[],leftHandWorldLandmarks:[],rightHandLandmarks:[],rightHandWorldLandmarks:[]},this.outputPoseSegmentationMasks=this.outputFaceBlendshapes=!1,y(t=this.j=new m1,0,1,e=new D),this.K=new ts,y(this.j,0,2,this.K),this.Y=new Rh,y(this.j,0,3,this.Y),this.s=new vr,y(this.j,0,4,this.s),this.H=new s1,y(this.j,0,5,this.H),this.v=new p1,y(this.j,0,6,this.v),this.L=new g1,y(this.j,0,7,this.L),g(this.s,2,.5),g(this.s,3,.3),g(this.H,2,.5),g(this.v,2,.5),g(this.v,3,.3),g(this.L,2,.5),g(this.K,2,.5)}get baseOptions(){return b(this.j,D,1)}set baseOptions(t){y(this.j,0,1,t)}o(t){var e,n,r,i,s,o,a;return"minFaceDetectionConfidence"in t&&g(this.s,2,(e=t.minFaceDetectionConfidence)!=null?e:.5),"minFaceSuppressionThreshold"in t&&g(this.s,3,(n=t.minFaceSuppressionThreshold)!=null?n:.3),"minFacePresenceConfidence"in t&&g(this.H,2,(r=t.minFacePresenceConfidence)!=null?r:.5),"outputFaceBlendshapes"in t&&(this.outputFaceBlendshapes=!!t.outputFaceBlendshapes),"minPoseDetectionConfidence"in t&&g(this.v,2,(i=t.minPoseDetectionConfidence)!=null?i:.5),"minPoseSuppressionThreshold"in t&&g(this.v,3,(s=t.minPoseSuppressionThreshold)!=null?s:.3),"minPosePresenceConfidence"in t&&g(this.L,2,(o=t.minPosePresenceConfidence)!=null?o:.5),"outputPoseSegmentationMasks"in t&&(this.outputPoseSegmentationMasks=!!t.outputPoseSegmentationMasks),"minHandLandmarksConfidence"in t&&g(this.K,2,(a=t.minHandLandmarksConfidence)!=null?a:.5),this.l(t)}D(t,e,n){let r=typeof e!="function"?e:{};return this.C=typeof e=="function"?e:n,Oo(this),Lt(this,t,r),Bo(this)}F(t,e,n,r){let i=typeof n!="function"?n:{};return this.C=typeof n=="function"?n:r,Oo(this),Gt(this,t,i,e),Bo(this)}m(){var t=new ut;I(t,"input_frames_image"),x(t,"pose_landmarks"),x(t,"pose_world_landmarks"),x(t,"face_landmarks"),x(t,"left_hand_landmarks"),x(t,"left_hand_world_landmarks"),x(t,"right_hand_landmarks"),x(t,"right_hand_world_landmarks");let e=new mt,n=new no;ei(n,1,Xe("type.googleapis.com/mediapipe.tasks.vision.holistic_landmarker.proto.HolisticLandmarkerGraphOptions"),""),function(i,s){if(s!=null)if(Array.isArray(s))M(i,2,rr(s,Fi,void 0,void 0,!1));else{if(!(typeof s=="string"||s instanceof $t||ye(s)))throw Error("invalid value in Any.value field: "+s+" expected a ByteString, a base64 encoded string, a Uint8Array or a jspb array");ei(i,2,Ei(s,!1),we())}}(n,this.j.g());let r=new et;yt(r,"mediapipe.tasks.vision.holistic_landmarker.HolisticLandmarkerGraph"),Vn(r,8,no,n),R(r,"IMAGE:input_frames_image"),A(r,"POSE_LANDMARKS:pose_landmarks"),A(r,"POSE_WORLD_LANDMARKS:pose_world_landmarks"),A(r,"FACE_LANDMARKS:face_landmarks"),A(r,"LEFT_HAND_LANDMARKS:left_hand_landmarks"),A(r,"LEFT_HAND_WORLD_LANDMARKS:left_hand_world_landmarks"),A(r,"RIGHT_HAND_LANDMARKS:right_hand_landmarks"),A(r,"RIGHT_HAND_WORLD_LANDMARKS:right_hand_world_landmarks"),r.o(e),wt(t,r),wr(this,t),this.g.attachProtoListener("pose_landmarks",(i,s)=>{In(i,this.h.poseLandmarks),d(this,s)}),this.g.attachEmptyPacketListener("pose_landmarks",i=>{d(this,i)}),this.g.attachProtoListener("pose_world_landmarks",(i,s)=>{var o=this.h.poseWorldLandmarks;i=Me(i),o.push(ln(i)),d(this,s)}),this.g.attachEmptyPacketListener("pose_world_landmarks",i=>{d(this,i)}),this.outputPoseSegmentationMasks&&(A(r,"POSE_SEGMENTATION_MASK:pose_segmentation_mask"),Be(this,"pose_segmentation_mask"),this.g.V("pose_segmentation_mask",(i,s)=>{this.h.poseSegmentationMasks=[ze(this,i,!0,!this.C)],d(this,s)}),this.g.attachEmptyPacketListener("pose_segmentation_mask",i=>{this.h.poseSegmentationMasks=[],d(this,i)})),this.g.attachProtoListener("face_landmarks",(i,s)=>{In(i,this.h.faceLandmarks),d(this,s)}),this.g.attachEmptyPacketListener("face_landmarks",i=>{d(this,i)}),this.outputFaceBlendshapes&&(x(t,"extra_blendshapes"),A(r,"FACE_BLENDSHAPES:extra_blendshapes"),this.g.attachProtoListener("extra_blendshapes",(i,s)=>{var a;var o=this.h.faceBlendshapes;this.outputFaceBlendshapes&&(i=mr(i),o.push(ss((a=i.g())!=null?a:[]))),d(this,s)}),this.g.attachEmptyPacketListener("extra_blendshapes",i=>{d(this,i)})),this.g.attachProtoListener("left_hand_landmarks",(i,s)=>{In(i,this.h.leftHandLandmarks),d(this,s)}),this.g.attachEmptyPacketListener("left_hand_landmarks",i=>{d(this,i)}),this.g.attachProtoListener("left_hand_world_landmarks",(i,s)=>{var o=this.h.leftHandWorldLandmarks;i=Me(i),o.push(ln(i)),d(this,s)}),this.g.attachEmptyPacketListener("left_hand_world_landmarks",i=>{d(this,i)}),this.g.attachProtoListener("right_hand_landmarks",(i,s)=>{In(i,this.h.rightHandLandmarks),d(this,s)}),this.g.attachEmptyPacketListener("right_hand_landmarks",i=>{d(this,i)}),this.g.attachProtoListener("right_hand_world_landmarks",(i,s)=>{var o=this.h.rightHandWorldLandmarks;i=Me(i),o.push(ln(i)),d(this,s)}),this.g.attachEmptyPacketListener("right_hand_world_landmarks",i=>{d(this,i)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};C.prototype.detectForVideo=C.prototype.F,C.prototype.detect=C.prototype.D,C.prototype.setOptions=C.prototype.o,C.createFromModelPath=function(t,e){return T(C,t,{baseOptions:{modelAssetPath:e}})},C.createFromModelBuffer=function(t,e){return T(C,t,{baseOptions:{modelAssetBuffer:e}})},C.createFromOptions=function(t,e){return T(C,t,e)},C.HAND_CONNECTIONS=gs,C.POSE_CONNECTIONS=O1,C.FACE_LANDMARKS_LIPS=cs,C.FACE_LANDMARKS_LEFT_EYE=us,C.FACE_LANDMARKS_LEFT_EYEBROW=ls,C.FACE_LANDMARKS_LEFT_IRIS=U1,C.FACE_LANDMARKS_RIGHT_EYE=fs,C.FACE_LANDMARKS_RIGHT_EYEBROW=ds,C.FACE_LANDMARKS_RIGHT_IRIS=C1,C.FACE_LANDMARKS_FACE_OVAL=ps,C.FACE_LANDMARKS_CONTOURS=P1,C.FACE_LANDMARKS_TESSELATION=D1;var Tt=class extends ct{constructor(t,e){super(new St(t,e),"input_image","norm_rect",!0),this.j={classifications:[]},y(t=this.h=new v1,0,1,e=new D)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){return y(this.h,0,2,hi(t,b(this.h,Zi,2))),this.l(t)}qa(t,e){return this.j={classifications:[]},Lt(this,t,e),this.j}ra(t,e,n){return this.j={classifications:[]},Gt(this,t,n,e),this.j}m(){var t=new ut;I(t,"input_image"),I(t,"norm_rect"),x(t,"classifications");let e=new mt;Mt(e,Ih,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.image_classifier.ImageClassifierGraph"),R(n,"IMAGE:input_image"),R(n,"NORM_RECT:norm_rect"),A(n,"CLASSIFICATIONS:classifications"),n.o(e),wt(t,n),this.g.attachProtoListener("classifications",(r,i)=>{this.j=function(s){var a;let o={classifications:Yt(s,yh,1).map(h=>{var c,l,u,f;return ss((l=(c=b(h,qa,4))==null?void 0:c.g())!=null?l:[],(u=xt(h,2))!=null?u:0,(f=kt(h,3))!=null?f:"")})};return Jr(De(s,2))!=null&&(o.timestampMs=(a=Jr(De(s,2)))!=null?a:0),o}(wh(r)),d(this,i)}),this.g.attachEmptyPacketListener("classifications",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Tt.prototype.classifyForVideo=Tt.prototype.ra,Tt.prototype.classify=Tt.prototype.qa,Tt.prototype.setOptions=Tt.prototype.o,Tt.createFromModelPath=function(t,e){return T(Tt,t,{baseOptions:{modelAssetPath:e}})},Tt.createFromModelBuffer=function(t,e){return T(Tt,t,{baseOptions:{modelAssetBuffer:e}})},Tt.createFromOptions=function(t,e){return T(Tt,t,e)};var dt=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!0),this.h=new y1,this.embeddings={embeddings:[]},y(t=this.h,0,1,e=new D)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e=this.h,n=b(this.h,lo,2);return n=n?n.clone():new lo,t.l2Normalize!==void 0?mn(n,1,t.l2Normalize):"l2Normalize"in t&&M(n,1),t.quantize!==void 0?mn(n,2,t.quantize):"quantize"in t&&M(n,2),y(e,0,2,n),this.l(t)}xa(t,e){return Lt(this,t,e),this.embeddings}ya(t,e,n){return Gt(this,t,n,e),this.embeddings}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"embeddings_out");let e=new mt;Mt(e,Uh,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.image_embedder.ImageEmbedderGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"EMBEDDINGS:embeddings_out"),n.o(e),wt(t,n),this.g.attachProtoListener("embeddings_out",(r,i)=>{r=Th(r),this.embeddings=function(s){var o;return{embeddings:Yt(s,Eh,1).map(a=>{var c,l,u,f,w,_,E;let h={headIndex:(l=(c=xt(a,3))!=null?c:0)!=null?l:-1,headName:(f=(u=kt(a,4))!=null?u:"")!=null?f:""};if(ya(a,uo,Gr(a,1))!==void 0)a=me(a=b(a,uo,Gr(a,1)),1,le,ge()),h.floatEmbedding=a.slice();else{let B=new Uint8Array(0);h.quantizedEmbedding=(E=(_=(w=b(a,_h,Gr(a,2)))==null?void 0:w.ma())==null?void 0:_.h())!=null?E:B}return h}),timestampMs:(o=Jr(De(s,2)))!=null?o:0}}(r),d(this,i)}),this.g.attachEmptyPacketListener("embeddings_out",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};dt.cosineSimilarity=function(t,e){if(t.floatEmbedding&&e.floatEmbedding)t=vo(t.floatEmbedding,e.floatEmbedding);else{if(!t.quantizedEmbedding||!e.quantizedEmbedding)throw Error("Cannot compute cosine similarity between quantized and float embeddings.");t=vo(mo(t.quantizedEmbedding),mo(e.quantizedEmbedding))}return t},dt.prototype.embedForVideo=dt.prototype.ya,dt.prototype.embed=dt.prototype.xa,dt.prototype.setOptions=dt.prototype.o,dt.createFromModelPath=function(t,e){return T(dt,t,{baseOptions:{modelAssetPath:e}})},dt.createFromModelBuffer=function(t,e){return T(dt,t,{baseOptions:{modelAssetBuffer:e}})},dt.createFromOptions=function(t,e){return T(dt,t,e)};var di=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){var t,e;(t=this.confidenceMasks)==null||t.forEach(n=>{n.close()}),(e=this.categoryMask)==null||e.close()}};function No(t){t.categoryMask=void 0,t.confidenceMasks=void 0,t.qualityScores=void 0}function Go(t){try{let e=new di(t.confidenceMasks,t.categoryMask,t.qualityScores);if(!t.j)return e;t.j(e)}finally{_r(t)}}di.prototype.close=di.prototype.close;var J=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!1),this.s=[],this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new is,this.v=new w1,y(this.h,0,3,this.v),y(t=this.h,0,1,e=new D)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n;return t.displayNamesLocale!==void 0?M(this.h,2,Xe(t.displayNamesLocale)):"displayNamesLocale"in t&&M(this.h,2),"outputCategoryMask"in t&&(this.outputCategoryMask=(e=t.outputCategoryMask)!=null?e:!1),"outputConfidenceMasks"in t&&(this.outputConfidenceMasks=(n=t.outputConfidenceMasks)!=null?n:!0),super.l(t)}J(){(function(t){var n,r,i;let e=Yt(t.ca(),et,1).filter(s=>{var o;return((o=kt(s,1))!=null?o:"").includes("mediapipe.tasks.TensorsToSegmentationCalculator")});if(t.s=[],e.length>1)throw Error("The graph has more than one mediapipe.tasks.TensorsToSegmentationCalculator.");e.length===1&&((i=(r=(n=b(e[0],mt,7))==null?void 0:n.l())==null?void 0:r.g())!=null?i:new Map).forEach((s,o)=>{var a;t.s[Number(o)]=(a=kt(s,1))!=null?a:""})})(this)}segment(t,e,n){let r=typeof e!="function"?e:{};return this.j=typeof e=="function"?e:n,No(this),Lt(this,t,r),Go(this)}Ia(t,e,n,r){let i=typeof n!="function"?n:{};return this.j=typeof n=="function"?n:r,No(this),Gt(this,t,i,e),Go(this)}Ba(){return this.s}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect");let e=new mt;Mt(e,E1,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.image_segmenter.ImageSegmenterGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),n.o(e),wt(t,n),wr(this,t),this.outputConfidenceMasks&&(x(t,"confidence_masks"),A(n,"CONFIDENCE_MASKS:confidence_masks"),Be(this,"confidence_masks"),this.g.ba("confidence_masks",(r,i)=>{this.confidenceMasks=r.map(s=>ze(this,s,!0,!this.j)),d(this,i)}),this.g.attachEmptyPacketListener("confidence_masks",r=>{this.confidenceMasks=[],d(this,r)})),this.outputCategoryMask&&(x(t,"category_mask"),A(n,"CATEGORY_MASK:category_mask"),Be(this,"category_mask"),this.g.V("category_mask",(r,i)=>{this.categoryMask=ze(this,r,!1,!this.j),d(this,i)}),this.g.attachEmptyPacketListener("category_mask",r=>{this.categoryMask=void 0,d(this,r)})),x(t,"quality_scores"),A(n,"QUALITY_SCORES:quality_scores"),this.g.attachFloatVectorListener("quality_scores",(r,i)=>{this.qualityScores=r,d(this,i)}),this.g.attachEmptyPacketListener("quality_scores",r=>{this.categoryMask=void 0,d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};J.prototype.getLabels=J.prototype.Ba,J.prototype.segmentForVideo=J.prototype.Ia,J.prototype.segment=J.prototype.segment,J.prototype.setOptions=J.prototype.o,J.createFromModelPath=function(t,e){return T(J,t,{baseOptions:{modelAssetPath:e}})},J.createFromModelBuffer=function(t,e){return T(J,t,{baseOptions:{modelAssetBuffer:e}})},J.createFromOptions=function(t,e){return T(J,t,e)};var pi=class{constructor(t,e,n){this.confidenceMasks=t,this.categoryMask=e,this.qualityScores=n}close(){var t,e;(t=this.confidenceMasks)==null||t.forEach(n=>{n.close()}),(e=this.categoryMask)==null||e.close()}};pi.prototype.close=pi.prototype.close;var Vh=class extends p{constructor(t){super(t)}},ke=[0,z,-2],$n=[0,Vt,-3,P,Vt,-1],zo=[0,$n],jo=[0,$n,z,-1],$r=class extends p{constructor(t){super(t)}},Vo=[0,Vt,-1,P],Hh=class extends p{constructor(t){super(t)}},Ho=class extends p{constructor(t){super(t)}},gi=[1,2,3,4,5,6,7,8,9,10,14,15],B1=class extends p{constructor(t){super(t)}};B1.prototype.g=gr([0,q,[0,gi,S,$n,S,[0,$n,ke],S,zo,S,[0,zo,ke],S,Vo,S,[0,Vt,-3,P,gt],S,[0,Vt,-3,P],S,[0,L,Vt,-2,P,z,P,-1,2,Vt,ke],S,jo,S,[0,jo,ke],Vt,ke,L,S,[0,Vt,-3,P,ke,-1],S,[0,q,Vo]],L,[0,L,z,-1,P]]);var Dt=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect_in",!1),this.outputCategoryMask=!1,this.outputConfidenceMasks=!0,this.h=new is,this.s=new w1,y(this.h,0,3,this.s),y(t=this.h,0,1,e=new D)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n;return"outputCategoryMask"in t&&(this.outputCategoryMask=(e=t.outputCategoryMask)!=null?e:!1),"outputConfidenceMasks"in t&&(this.outputConfidenceMasks=(n=t.outputConfidenceMasks)!=null?n:!0),super.l(t)}segment(t,e,n,r){let i=typeof n!="function"?n:{};this.j=typeof n=="function"?n:r,this.qualityScores=this.categoryMask=this.confidenceMasks=void 0,n=this.B+1,r=new B1;let s=new Ho;var o=new Vh;if(Nt(o,1,255),y(s,0,12,o),e.keypoint&&e.scribble)throw Error("Cannot provide both keypoint and scribble.");if(e.keypoint){var a=new $r;mn(a,3,!0),g(a,1,e.keypoint.x),g(a,2,e.keypoint.y),cn(s,5,gi,a)}else{if(!e.scribble)throw Error("Must provide either a keypoint or a scribble.");for(a of(o=new Hh,e.scribble))mn(e=new $r,3,!0),g(e,1,a.x),g(e,2,a.y),Vn(o,1,$r,e);cn(s,15,gi,o)}Vn(r,1,Ho,s),this.g.addProtoToStream(r.g(),"drishti.RenderData","roi_in",n),Lt(this,t,i);t:{try{let c=new pi(this.confidenceMasks,this.categoryMask,this.qualityScores);if(!this.j){var h=c;break t}this.j(c)}finally{_r(this)}h=void 0}return h}m(){var t=new ut;I(t,"image_in"),I(t,"roi_in"),I(t,"norm_rect_in");let e=new mt;Mt(e,E1,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.interactive_segmenter.InteractiveSegmenterGraph"),R(n,"IMAGE:image_in"),R(n,"ROI:roi_in"),R(n,"NORM_RECT:norm_rect_in"),n.o(e),wt(t,n),wr(this,t),this.outputConfidenceMasks&&(x(t,"confidence_masks"),A(n,"CONFIDENCE_MASKS:confidence_masks"),Be(this,"confidence_masks"),this.g.ba("confidence_masks",(r,i)=>{this.confidenceMasks=r.map(s=>ze(this,s,!0,!this.j)),d(this,i)}),this.g.attachEmptyPacketListener("confidence_masks",r=>{this.confidenceMasks=[],d(this,r)})),this.outputCategoryMask&&(x(t,"category_mask"),A(n,"CATEGORY_MASK:category_mask"),Be(this,"category_mask"),this.g.V("category_mask",(r,i)=>{this.categoryMask=ze(this,r,!1,!this.j),d(this,i)}),this.g.attachEmptyPacketListener("category_mask",r=>{this.categoryMask=void 0,d(this,r)})),x(t,"quality_scores"),A(n,"QUALITY_SCORES:quality_scores"),this.g.attachFloatVectorListener("quality_scores",(r,i)=>{this.qualityScores=r,d(this,i)}),this.g.attachEmptyPacketListener("quality_scores",r=>{this.categoryMask=void 0,d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};Dt.prototype.segment=Dt.prototype.segment,Dt.prototype.setOptions=Dt.prototype.o,Dt.createFromModelPath=function(t,e){return T(Dt,t,{baseOptions:{modelAssetPath:e}})},Dt.createFromModelBuffer=function(t,e){return T(Dt,t,{baseOptions:{modelAssetBuffer:e}})},Dt.createFromOptions=function(t,e){return T(Dt,t,e)};var At=class extends ct{constructor(t,e){super(new St(t,e),"input_frame_gpu","norm_rect",!1),this.j={detections:[]},y(t=this.h=new T1,0,1,e=new D)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){return t.displayNamesLocale!==void 0?M(this.h,2,Xe(t.displayNamesLocale)):"displayNamesLocale"in t&&M(this.h,2),t.maxResults!==void 0?Nt(this.h,3,t.maxResults):"maxResults"in t&&M(this.h,3),t.scoreThreshold!==void 0?g(this.h,4,t.scoreThreshold):"scoreThreshold"in t&&M(this.h,4),t.categoryAllowlist!==void 0?Hn(this.h,5,t.categoryAllowlist):"categoryAllowlist"in t&&M(this.h,5),t.categoryDenylist!==void 0?Hn(this.h,6,t.categoryDenylist):"categoryDenylist"in t&&M(this.h,6),this.l(t)}D(t,e){return this.j={detections:[]},Lt(this,t,e),this.j}F(t,e,n){return this.j={detections:[]},Gt(this,t,n,e),this.j}m(){var t=new ut;I(t,"input_frame_gpu"),I(t,"norm_rect"),x(t,"detections");let e=new mt;Mt(e,Ph,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.ObjectDetectorGraph"),R(n,"IMAGE:input_frame_gpu"),R(n,"NORM_RECT:norm_rect"),A(n,"DETECTIONS:detections"),n.o(e),wt(t,n),this.g.attachProtoVectorListener("detections",(r,i)=>{for(let s of r)r=Ja(s),this.j.detections.push(b1(r));d(this,i)}),this.g.attachEmptyPacketListener("detections",r=>{d(this,r)}),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};At.prototype.detectForVideo=At.prototype.F,At.prototype.detect=At.prototype.D,At.prototype.setOptions=At.prototype.o,At.createFromModelPath=function(t,e){return X(this,null,function*(){return T(At,t,{baseOptions:{modelAssetPath:e}})})},At.createFromModelBuffer=function(t,e){return T(At,t,{baseOptions:{modelAssetBuffer:e}})},At.createFromOptions=function(t,e){return T(At,t,e)};var mi=class{constructor(t,e,n){this.landmarks=t,this.worldLandmarks=e,this.segmentationMasks=n}close(){var t;(t=this.segmentationMasks)==null||t.forEach(e=>{e.close()})}};function Xo(t){t.landmarks=[],t.worldLandmarks=[],t.segmentationMasks=void 0}function Wo(t){try{let e=new mi(t.landmarks,t.worldLandmarks,t.segmentationMasks);if(!t.s)return e;t.s(e)}finally{_r(t)}}mi.prototype.close=mi.prototype.close;var pt=class extends ct{constructor(t,e){super(new St(t,e),"image_in","norm_rect",!1),this.landmarks=[],this.worldLandmarks=[],this.outputSegmentationMasks=!1,y(t=this.h=new A1,0,1,e=new D),this.v=new g1,y(this.h,0,3,this.v),this.j=new p1,y(this.h,0,2,this.j),Nt(this.j,4,1),g(this.j,2,.5),g(this.v,2,.5),g(this.h,4,.5)}get baseOptions(){return b(this.h,D,1)}set baseOptions(t){y(this.h,0,1,t)}o(t){var e,n,r,i,s;return"numPoses"in t&&Nt(this.j,4,(e=t.numPoses)!=null?e:1),"minPoseDetectionConfidence"in t&&g(this.j,2,(n=t.minPoseDetectionConfidence)!=null?n:.5),"minTrackingConfidence"in t&&g(this.h,4,(r=t.minTrackingConfidence)!=null?r:.5),"minPosePresenceConfidence"in t&&g(this.v,2,(i=t.minPosePresenceConfidence)!=null?i:.5),"outputSegmentationMasks"in t&&(this.outputSegmentationMasks=(s=t.outputSegmentationMasks)!=null?s:!1),this.l(t)}D(t,e,n){let r=typeof e!="function"?e:{};return this.s=typeof e=="function"?e:n,Xo(this),Lt(this,t,r),Wo(this)}F(t,e,n,r){let i=typeof n!="function"?n:{};return this.s=typeof n=="function"?n:r,Xo(this),Gt(this,t,i,e),Wo(this)}m(){var t=new ut;I(t,"image_in"),I(t,"norm_rect"),x(t,"normalized_landmarks"),x(t,"world_landmarks"),x(t,"segmentation_masks");let e=new mt;Mt(e,Dh,this.h);let n=new et;yt(n,"mediapipe.tasks.vision.pose_landmarker.PoseLandmarkerGraph"),R(n,"IMAGE:image_in"),R(n,"NORM_RECT:norm_rect"),A(n,"NORM_LANDMARKS:normalized_landmarks"),A(n,"WORLD_LANDMARKS:world_landmarks"),n.o(e),wt(t,n),wr(this,t),this.g.attachProtoVectorListener("normalized_landmarks",(r,i)=>{this.landmarks=[];for(let s of r)r=En(s),this.landmarks.push(yr(r));d(this,i)}),this.g.attachEmptyPacketListener("normalized_landmarks",r=>{this.landmarks=[],d(this,r)}),this.g.attachProtoVectorListener("world_landmarks",(r,i)=>{this.worldLandmarks=[];for(let s of r)r=Me(s),this.worldLandmarks.push(ln(r));d(this,i)}),this.g.attachEmptyPacketListener("world_landmarks",r=>{this.worldLandmarks=[],d(this,r)}),this.outputSegmentationMasks&&(A(n,"SEGMENTATION_MASK:segmentation_masks"),Be(this,"segmentation_masks"),this.g.ba("segmentation_masks",(r,i)=>{this.segmentationMasks=r.map(s=>ze(this,s,!0,!this.s)),d(this,i)}),this.g.attachEmptyPacketListener("segmentation_masks",r=>{this.segmentationMasks=[],d(this,r)})),t=t.g(),this.setGraph(new Uint8Array(t),!0)}};pt.prototype.detectForVideo=pt.prototype.F,pt.prototype.detect=pt.prototype.D,pt.prototype.setOptions=pt.prototype.o,pt.createFromModelPath=function(t,e){return T(pt,t,{baseOptions:{modelAssetPath:e}})},pt.createFromModelBuffer=function(t,e){return T(pt,t,{baseOptions:{modelAssetBuffer:e}})},pt.createFromOptions=function(t,e){return T(pt,t,e)},pt.POSE_CONNECTIONS=O1;var N1={},Xh=function(t,e,n,r,i){var s=new Worker(N1[e]||(N1[e]=URL.createObjectURL(new Blob([t+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return s.onmessage=function(o){var a=o.data,h=a.$e$;if(h){var c=new Error(h[0]);c.code=h[1],c.stack=h[2],i(c,null)}else i(null,a)},s.postMessage(n,r),s},it=Uint8Array,Te=Uint16Array,j1=Int32Array,ms=new it([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),vs=new it([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),V1=new it([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),H1=function(t,e){for(var n=new Te(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];for(var i=new j1(n[30]),r=1;r<30;++r)for(var s=n[r];s<n[r+1];++s)i[s]=s-n[r]<<5|r;return{b:n,r:i}},X1=H1(ms,2),ys=X1.b,Wh=X1.r;ys[28]=258,Wh[258]=28;var W1=H1(vs,0),$1=W1.b,Qu=W1.r,kr=new Te(32768);for(k=0;k<32768;++k)Jt=(k&43690)>>1|(k&21845)<<1,Jt=(Jt&52428)>>2|(Jt&13107)<<2,Jt=(Jt&61680)>>4|(Jt&3855)<<4,kr[k]=((Jt&65280)>>8|(Jt&255)<<8)>>1;var Jt,k,Je=function(t,e,n){for(var r=t.length,i=0,s=new Te(e);i<r;++i)t[i]&&++s[t[i]-1];var o=new Te(e);for(i=1;i<e;++i)o[i]=o[i-1]+s[i-1]<<1;var a;if(n){a=new Te(1<<e);var h=15-e;for(i=0;i<r;++i)if(t[i])for(var c=i<<4|t[i],l=e-t[i],u=o[t[i]-1]++<<l,f=u|(1<<l)-1;u<=f;++u)a[kr[u]>>h]=c}else for(a=new Te(r),i=0;i<r;++i)t[i]&&(a[i]=kr[o[t[i]-1]++]>>15-t[i]);return a},An=new it(288);for(k=0;k<144;++k)An[k]=8;var k;for(k=144;k<256;++k)An[k]=9;var k;for(k=256;k<280;++k)An[k]=7;var k;for(k=280;k<288;++k)An[k]=8;var k,K1=new it(32);for(k=0;k<32;++k)K1[k]=5;var k;var Y1=Je(An,9,1);var q1=Je(K1,5,1),br=function(t){for(var e=t[0],n=1;n<t.length;++n)t[n]>e&&(e=t[n]);return e},Ft=function(t,e,n){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(e&7)&n},xr=function(t,e){var n=e/8|0;return(t[n]|t[n+1]<<8|t[n+2]<<16)>>(e&7)},Z1=function(t){return(t+7)/8|0},J1=function(t,e,n){return(e==null||e<0)&&(e=0),(n==null||n>t.length)&&(n=t.length),new it(t.subarray(e,n))};var Q1=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],_t=function(t,e,n){var r=new Error(e||Q1[t]);if(r.code=t,Error.captureStackTrace&&Error.captureStackTrace(r,_t),!n)throw r;return r},ws=function(t,e,n,r){var i=t.length,s=r?r.length:0;if(!i||e.f&&!e.l)return n||new it(0);var o=!n,a=o||e.i!=2,h=e.i;o&&(n=new it(i*3));var c=function(Fs){var Ms=n.length;if(Fs>Ms){var Rs=new it(Math.max(Ms*2,Fs));Rs.set(n),n=Rs}},l=e.f||0,u=e.p||0,f=e.b||0,w=e.l,_=e.d,E=e.m,B=e.n,H=i*8;do{if(!w){l=Ft(t,u,1);var st=Ft(t,u+1,3);if(u+=3,st)if(st==1)w=Y1,_=q1,E=9,B=5;else if(st==2){var Lr=Ft(t,u,31)+257,Es=Ft(t,u+10,15)+4,Ts=Lr+Ft(t,u+5,31)+1;u+=14;for(var tn=new it(Ts),Fr=new it(19),Et=0;Et<Es;++Et)Fr[V1[Et]]=Ft(t,u+Et*3,7);u+=Es*3;for(var As=br(Fr),o2=(1<<As)-1,a2=Je(Fr,As,1),Et=0;Et<Ts;){var bs=a2[Ft(t,u,o2)];u+=bs&15;var U=bs>>4;if(U<16)tn[Et++]=U;else{var Ae=0,bn=0;for(U==16?(bn=3+Ft(t,u,3),u+=2,Ae=tn[Et-1]):U==17?(bn=3+Ft(t,u,7),u+=3):U==18&&(bn=11+Ft(t,u,127),u+=7);bn--;)tn[Et++]=Ae}}var xs=tn.subarray(0,Lr),Qt=tn.subarray(Lr);E=br(xs),B=br(Qt),w=Je(xs,E,1),_=Je(Qt,B,1)}else _t(1);else{var U=Z1(u)+4,Qe=t[U-4]|t[U-3]<<8,Sr=U+Qe;if(Sr>i){h&&_t(0);break}a&&c(f+Qe),n.set(t.subarray(U,Sr),f),e.b=f+=Qe,e.p=u=Sr*8,e.f=l;continue}if(u>H){h&&_t(0);break}}a&&c(f+131072);for(var h2=(1<<E)-1,c2=(1<<B)-1,Mr=u;;Mr=u){var Ae=w[xr(t,u)&h2],be=Ae>>4;if(u+=Ae&15,u>H){h&&_t(0);break}if(Ae||_t(2),be<256)n[f++]=be;else if(be==256){Mr=u,w=null;break}else{var ks=be-254;if(be>264){var Et=be-257,en=ms[Et];ks=Ft(t,u,(1<<en)-1)+ys[Et],u+=en}var Rr=_[xr(t,u)&c2],Ir=Rr>>4;Rr||_t(3),u+=Rr&15;var Qt=$1[Ir];if(Ir>3){var en=vs[Ir];Qt+=xr(t,u)&(1<<en)-1,u+=en}if(u>H){h&&_t(0);break}a&&c(f+131072);var Ss=f+ks;if(f<Qt){var Ls=s-Qt,u2=Math.min(Qt,Ss);for(Ls+f<0&&_t(3);f<u2;++f)n[f]=r[Ls+f]}for(;f<Ss;++f)n[f]=n[f-Qt]}}e.l=w,e.p=Mr,e.b=f,e.f=l,w&&(l=1,e.m=E,e.d=_,e.n=B)}while(!l);return f!=n.length&&o?J1(n,0,f):n.subarray(0,f)};var $h=new it(0);var Kh=function(t,e){var n={};for(var r in t)n[r]=t[r];for(var r in e)n[r]=e[r];return n},G1=function(t,e,n){for(var r=t(),i=t.toString(),s=i.slice(i.indexOf("[")+1,i.lastIndexOf("]")).replace(/\s+/g,"").split(","),o=0;o<r.length;++o){var a=r[o],h=s[o];if(typeof a=="function"){e+=";"+h+"=";var c=a.toString();if(a.prototype)if(c.indexOf("[native code]")!=-1){var l=c.indexOf(" ",8)+1;e+=c.slice(l,c.indexOf("(",l))}else{e+=c;for(var u in a.prototype)e+=";"+h+".prototype."+u+"="+a.prototype[u].toString()}else e+=c}else n[h]=a}return e},Ar=[],Yh=function(t){var e=[];for(var n in t)t[n].buffer&&e.push((t[n]=new t[n].constructor(t[n])).buffer);return e},qh=function(t,e,n,r){if(!Ar[n]){for(var i="",s={},o=t.length-1,a=0;a<o;++a)i=G1(t[a],i,s);Ar[n]={c:G1(t[o],i,s),e:s}}var h=Kh({},Ar[n].e);return Xh(Ar[n].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",n,h,Yh(h),r)},Zh=function(){return[it,Te,j1,ms,vs,V1,ys,$1,Y1,q1,kr,Q1,Je,br,Ft,xr,Z1,J1,_t,ws,ec,t2,Qh]};var Jh=function(){return[e2,n2]};var t2=function(t){return postMessage(t,[t.buffer])},Qh=function(t){return t&&{out:t.size&&new it(t.size),dictionary:t.dictionary}},tc=function(t,e,n,r,i,s){var o=qh(n,r,i,function(a,h){o.terminate(),s(a,h)});return o.postMessage([t,e],e.consume?[t.buffer]:[]),function(){o.terminate()}};var e2=function(t){(t[0]!=31||t[1]!=139||t[2]!=8)&&_t(6,"invalid gzip data");var e=t[3],n=10;e&4&&(n+=(t[10]|t[11]<<8)+2);for(var r=(e>>3&1)+(e>>4&1);r>0;r-=!t[n++]);return n+(e&2)},n2=function(t){var e=t.length;return(t[e-4]|t[e-3]<<8|t[e-2]<<16|t[e-1]<<24)>>>0};function ec(t,e){return ws(t,{i:2},e&&e.out,e&&e.dictionary)}function r2(t,e,n){return n||(n=e,e={}),typeof n!="function"&&_t(7),tc(t,e,[Zh,Jh,function(){return[z1]}],function(r){return t2(z1(r.data[0],r.data[1]))},3,n)}function z1(t,e){var n=e2(t);return n+8>t.length&&_t(6,"invalid gzip data"),ws(t.subarray(n,-8),{i:2},e&&e.out||new it(n2(t)),e&&e.dictionary)}var nc=typeof TextDecoder!="undefined"&&new TextDecoder,rc=0;try{nc.decode($h,{stream:!0}),rc=1}catch(t){}var s2=(n=>(n.FaceDetector="FaceDetector",n.ImageSegmenter="ImageSegmenter",n))(s2||{}),i2={FaceDetector:[at,t=>t.detectForVideo.bind(t),"blaze_face_short_range.tflite"],ImageSegmenter:[J,t=>t.segmentForVideo.bind(t),"selfie_segmenter_landscape.tflite"]},zt=class zt{constructor(e){this.path=e;de(this,"hashTasks");de(this,"hashResults");de(this,"taskModels");de(this,"vision");de(this,"video");this.hashTasks=new Map,this.hashResults=new Map,this.taskModels=new Map}get basePath(){return this.path}get models(){return this.taskModels}get visionWasm(){return this.vision}static getInstance(e){return X(this,null,function*(){if(e){if(zt.instance&&zt.instance.path===e)return zt.instance;zt.instance=new zt(e),yield zt.instance.initVision()}return zt.instance})}preloadModels(e){return X(this,null,function*(){return yield Promise.all(e.map(n=>X(this,null,function*(){let r=this.getFinalUrl(`/mediapipe/${i2[n][2]}`),i=yield this.getBlobUrl(r,!1);this.taskModels.set(n,i)}))).catch(n=>{throw new Error(`models: ${e}. preloadModels error: ${n}`)})})}generateHash(e,n){return X(this,null,function*(){try{let r=this.normalizeOptions(n),i=JSON.stringify({taskType:e,options:r});return yield crypto.subtle.digest("SHA-256",new TextEncoder().encode(i)).then(s=>this.bufferToHex(s))}catch(r){throw new Error(`GenerateHash error: ${r}`)}})}normalizeOptions(e){let n={};for(let r of Object.keys(e).sort()){let i=e[r];i instanceof HTMLCanvasElement?n[r]={id:i.id}:typeof i=="object"&&i!==null?n[r]=this.normalizeOptions(i):n[r]=i}return n}bufferToHex(e){return Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join("")}register(e,n){return X(this,null,function*(){try{let r={baseOptions:{modelAssetPath:this.getModelUrl(e),delegate:"GPU"},runningMode:"VIDEO"};n!=null&&n.path&&r.baseOptions&&(r.baseOptions.modelAssetPath=n.path),n!=null&&n.canvas&&(r.canvas=n.canvas);let i=yield this.generateHash(e,r);if(!this.hashTasks.has(i)){let[s,o]=i2[e],a=yield s.createFromOptions(this.vision,r),h=o(a);this.hashTasks.set(i,[a,h])}return i}catch(r){throw new Error(`Register error: ${r}`)}})}getResult(e){try{if(!this.hashResults.has(e)){if(!this.hashTasks.get(e))throw new Error("Unregistered hash");this.hashResults.set(e,this.reasoning(e))}return this.hashResults.get(e)}catch(n){throw new Error(`GetResult error: ${n}`)}}reasoning(e){let n=this.hashTasks.get(e)[1];return n(this.video,performance.now())}getModelUrl(e){return this.taskModels.get(e)}getFinalUrl(e){return`${this.path}/${e}`.replace(/([^:]\/)\/+/g,"$1")}initVision(){return X(this,null,function*(){try{let e=(yield Ht.isSimdSupported())?"vision_wasm_internal":"vision_wasm_nosimd_internal",n=this.getFinalUrl(`/mediapipe/${e}`),[r,i]=[`${n}.wasm.gz`,`${n}.js.gz`],[s,o]=yield Promise.all([this.getBlobUrl(r),this.getBlobUrl(i)]);this.vision=yield Ht.forVisionTasks(""),this.vision.wasmBinaryPath=s,this.vision.wasmLoaderPath=o}catch(e){throw new Error(`initVision error: ${e}`)}})}getBlobUrl(e,n=!0){return X(this,null,function*(){let r=yield fetch(e);if(!r.ok)throw new Error(`Failed to fetch ${e}: ${r.status} ${r.statusText};`);let i;if(n){let s=yield r.arrayBuffer();i=yield new Promise((o,a)=>{r2(new Uint8Array(s),(h,c)=>{h&&a(h),o(new Blob([c],{type:this.isWasmFile(c)?"application/wasm":"application/javascript"}))})})}else i=yield r.blob();if(!(i instanceof Blob))throw new Error("Decompressed data is not a valid Blob");return URL.createObjectURL(i)})}isWasmFile(e){return e.length>=4&&e[0]===0&&e[1]===97&&e[2]===115&&e[3]===109}resetHashResults(){this.hashResults=new Map}};de(zt,"instance");var _s=zt;return y2(ic);})();

      (() => {
        if (typeof Vision !== 'undefined') {
          // 如果是 default 导出
          if (Vision.default) {
            for (const key in Vision.default) {
              if (Vision.default.hasOwnProperty(key)) {
                window[key] = Vision.default[key];
              }
            }
          } else {
            // 如果是命名导出
            for (const key in Vision) {
              if (Vision.hasOwnProperty(key)) {
                window[key] = Vision[key];
              }
            }
          }
        }
      })();
    
