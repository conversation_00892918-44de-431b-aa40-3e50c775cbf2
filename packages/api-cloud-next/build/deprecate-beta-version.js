var shell = require('shelljs');

const versions = JSON.parse(shell.exec('npm show trtc-sdk-v5@* versions --json').stdout);

const betaVersions = versions.filter(version => version.includes('beta'));
console.warn(betaVersions);

shell.cd('dist/npm-package')
betaVersions.forEach(version => {
  console.warn(`npm deprecate trtc-sdk-v5@${version} "beta version is deprecated, please use latest version"`)
  shell.exec(`npm deprecate trtc-sdk-v5@${version} "beta version is deprecated, please use latest version"`)
})