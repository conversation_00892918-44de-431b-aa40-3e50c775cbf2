const replace = require('replace');
const fs = require('fs');
const path = require('path');
const apiPath = path.join(__dirname, '../../', 'docs/zh-cn', 'api');
const pretty = require('pretty');

const files = fs.readdirSync(apiPath);
const replacements = [
  // key word
  {
    find: '</title>',
    replace: `</title>
    <meta name="description" content="实时音视频（Tencent RTC）基于腾讯21年来在网络与音视频技术上的深度积累，以多人音视频通话和低延时互动直播两大场景化方案，通过腾讯云服务向开发者开放，致力于帮助开发者快速搭建低成本、低延时、高品质的音视频互动解决方案。"/>
    <meta name="keywords" content="腾讯, 会议，通话, 语音通话, 视频通话, 直播, 互动直播, 元宇宙, 音视频解决方案, call, meeting, voice, chat, metaverse, streaming, live streaming, WebRTC, RTC, SDK, Tencent, discord"/>
    `
  },
  // Title 统一加上 腾讯云 RTC SDK
  {
    find: '<title>(Tutorial: )?(.+)</title>',
    replace: '<title>腾讯云 RTC SDK - $2</title>'
  },
  {
    find: '<header>',
    replace: '<header style="display:none">'
  },
  // 中文文档 H1 去掉 Tutorial
  {
    find: '<h4 class="name" id="TRTC">',
    replace: '<h4 class="name" style="display:none" id="TRTC">'
  },
  {
    find: '<p>!</p>',
    replace: '<p>注意：</p>'
  },
  {
    find: '<blockquote',
    replace: '<blockquote style="border-left: 6px solid #00a4ff;margin: 0 0 24px;padding: 12px;background: #dcf4ff;"'
  },
  {
    find: '<li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a>',
    replace:
      '<ul><li style="margin:10px 0 5px 0">相关信息</li><li><a href="tutorial-00-info-update-guideline.html">SDK 升级指引</a>'
  },
  {
    find: '<li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a>',
    replace:
      '</ul><ul><li style="margin:10px 0 5px 0">基础教程</li><li><a href="tutorial-10-basic-get-started-with-demo.html">快速跑通 Demo</a>'
  },
  {
    find: '<a href="tutorial-07-basic-detect-volume.html">音量大小检测</a></li>',
    replace: '<a href="tutorial-07-basic-detect-volume.html">音量大小检测</a></li><ul/>'
  },
  {
    find:
      '<li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>',
    replace:
      '</ul><ul><li style="margin:10px 0 5px 0">进阶教程</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">自定义采集与自定义播放渲染</a>'
  },
  {
    find: '<pre class="prettyprint',
    replace: '<pre class="highlight lang-javascript'
  },
  {
    find: '<html lang="en">',
    replace: '<html lang="zh-cmn-Hans">'
  },
  // 将《自动播放受限处理建议》文章放到 最佳实践 栏目中。
  {
    find: '<li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-27-advanced-small-stream.html">多人视频通话</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-33-advanced-electron-screen-share.html">Electron 使用 TRTC Web SDK 进行屏幕分享</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-36-advanced-spatial-audio.html">实现 3D 空间音频</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-28-advanced-beauty.html">开启美颜</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-38-advanced-basic-beauty.html">开启基础美颜</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-39-advanced-video-decoder.html">开启视频解码插件</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-41-advanced-small-stream-auto-switcher.html">开启大小流自动切换插件</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-37-advanced-voice-changer.html">开启美声效果</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-40-advanced-video-mixer.html">开启视频合流插件</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-41-advanced-small-stream-auto-switcher.html">开启大小流自动切换插件</a></li>',
    replace: ''
  },
  {
    find: '<li><a href="tutorial-30-advanced-cross-room-link.html">实现跨房连麦</a></li>',
    replace:
      `<li><a href="tutorial-30-advanced-cross-room-link.html">实现跨房连麦</a></li>
       <li><a href="https://cloud.tencent.com/document/product/647/108902">实时语音识别</a></li>
       <li><a href="tutorial-35-advanced-ai-denoiser.html">开启 AI 降噪</a></li>
       <li><a href="tutorial-36-advanced-virtual-background.html">开启虚拟背景</a></li>
       <li><a href="tutorial-37-advanced-voice-changer.html">开启美声效果</a></li>
       <li><a href="tutorial-38-advanced-basic-beauty.html">开启基础美颜</a></li>
       <li><a href="tutorial-39-advanced-video-decoder.html">开启视频解码插件</a></li>
       <li><a href="tutorial-40-advanced-video-mixer.html">开启视频合流插件</a></li>
       <li><a href="tutorial-41-advanced-small-stream-auto-switcher.html">开启大小流自动切换插件</a></li>
      </ul>
      <ul>
        <li style="margin:10px 0 5px 0">最佳实践</li>
        <li><a href="tutorial-27-advanced-small-stream.html">多人视频通话</a></li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">自动播放受限处理建议</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">应对防火墙受限</a></li>
      </ul>`
  },
  {
    find: '<script src="scripts/nav.js" defer></script>',
    replace: `<script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>   
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>`
  },
  {
    find: '<nav>',
    replace: `<nav><div style="display: flex;align-items: center;z-index:999; padding-top: 20px;">
    <img id="toggleLanguage" targetLang="en" src="https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png" title="change language to English" height="24px" style="cursor: pointer;" onclick="location.href=location.href.replace('/zh-cn/', '/en/')">
    <a style="margin: 0 10px;" href="https://github.com/LiteAVSDK/TRTC_Web" title="contact us on github" class="contact_us_github" target="_blank"><svg height="24" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github v-align-middle"><path d="M8 0c4.42 0 8 3.58 8 8a8.013 8.013 0 0 1-5.45 7.59c-.4.08-.55-.17-.55-.38 0-.27.01-1.13.01-2.2 0-.75-.25-1.23-.54-1.48 1.78-.2 3.65-.88 3.65-3.95 0-.88-.31-1.59-.82-2.15.08-.2.36-1.02-.08-2.12 0 0-.67-.22-2.2.82-.64-.18-1.32-.27-2-.27-.68 0-1.36.09-2 .27-1.53-1.03-2.2-.82-2.2-.82-.44 1.1-.16 1.92-.08 2.12-.51.56-.82 1.28-.82 2.15 0 3.06 1.86 3.75 3.64 3.95-.23.2-.44.55-.51 1.07-.46.21-1.61.55-2.33-.66-.15-.24-.6-.83-1.23-.82-.67.01-.27.38.01.53.34.19.73.9.82 1.13.16.45.68 1.31 2.69.94 0 .67.01 1.3.01 1.49 0 .21-.15.45-.55.38A7.995 7.995 0 0 1 0 8c0-4.42 3.58-8 8-8Z"></path></svg></a>
    </div>
    `
  },
  // 外链链接用新窗口打开
  {
    find: '<a href="http',
    replace: '<a target="_blank" href="http'
  },
  {
    find: '<a href="tutorial-11-basic-video-call.html">开始集成音视频通话</a>',
    replace: '<a href="tutorial-11-basic-video-call.html">开始集成音视频通话<img src="./assets/hot-tag.svg" class="hot-tag" style=" display: inline-block; margin-left: 5px;"></a>'
  },
  {
    find: '<li><a href="tutorial-16-basic-screencast-old.html">16-basic-screencast-old</a></li>',
    replace: ''
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#.create">create</a></li>',
    replace: `<li data-type='method' style='display: none;'><a href="TRTC.html#.create">create</a></li>
              <li><span class="nav-separator">member method</span></li>`
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>',
    replace: '<li><span class="nav-separator">Local Audio</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalAudio">startLocalAudio</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>',
    replace: '<li><span class="nav-separator">Local Video</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startLocalVideo">startLocalVideo</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>',
    replace: '<li><span class="nav-separator">Local Screen Share</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startScreenShare">startScreenShare</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>',
    replace: '<li><span class="nav-separator">Remote Video</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startRemoteVideo">startRemoteVideo</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>',
    replace: '<li><span class="nav-separator">Remote Audio</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#muteRemoteAudio">muteRemoteAudio</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startPlugin">startPlugin</a></li>',
    replace: '<li><span class="nav-separator">Plugins</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#startPlugin">startPlugin</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>',
    replace: '<li><span class="nav-separator">Others</span></li><li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>'
  },
  {
    find: '<li data-type=\'method\' style=\'display: none;\'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>',
    replace: `<li><span class="nav-separator">static method</span></li>
    <li data-type='method' style='display: none;'><a href="TRTC.html#.setLogLevel">setLogLevel</a></li>`
  },
  // 语言自动切换
  {
    find:
      '</html>',
    replace:
      `</html>
      <script>
        const localStorageLangId = 'trtc-v5-sdk-doc-language';

        const languageButton = document.getElementById('toggleLanguage');

        languageButton.addEventListener('click', function() {
          localStorage.setItem(localStorageLangId, languageButton.getAttribute('targetLang'));
        });

        function getUrlParam(key) {
          const params = new URLSearchParams(window.location.search);
          return params.get(key);
        }

        function getLanguage() {
          const lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
          return lang.indexOf('zh') > -1 ? 'zh-cn' : 'en';
        }

        const lang = getLanguage();
        const newUrl = window.location.href.replace(lang === 'en' ? '/zh-cn/' : '/en/', \`/\${lang}/\`);

        if (newUrl !== window.location.href) {
          window.location.href = newUrl;
        }
      </script>
      `
  },
  // 代码块右上角增加 copy 按钮
  {
    find:
      '</html>',
    replace:
      `</html>

      <script>
        window.onload = function () {
          const preElements = document.querySelectorAll("pre");
          preElements.forEach((preElement) => {
            const copyButton = document.createElement('button');
            copyButton.textContent = 'copy';
            copyButton.onclick = function() {
              copy(copyButton);
            };
            preElement.insertBefore(copyButton, preElement.firstChild);
          });
        };
      
        function copy(button) {
          const codeBlock = button.parentNode.querySelector('.highlight code');
          const selection = window.getSelection();
          const range = document.createRange();
          range.selectNodeContents(codeBlock);
          selection.removeAllRanges();
          selection.addRange(range);
          const code = selection.toString();
          selection.removeAllRanges();
          navigator.clipboard.writeText(code).then(() => {
            button.innerText = 'copied!';
          }, () => {
            button.innerText = 'failed to copy';
          }).finally(() => {
            setTimeout(() => {
              button.innerText = 'copy';
            }, 1000);
          });
        }
      </script>
      
      <style>
        pre {
          position: relative;
        }
      
        pre>button {
          position: absolute;
          right: 6px;
          top: 10px;
          color: #aaa;
          background-color: transparent;
          border: 1px solid transparent;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.1s ease-in-out;
          user-select: none;
        }
      
        pre>button:hover {
          color: #fff;
          border: 1px solid #fff;
        }
        .params .type {
          white-space: normal;
        }
      </style>`
  }
  /* ,
  {
    find: '<pre class="prettyprint source',
    replace: '<pre class="prettyprint source linenums'
  },
  {
    find: 'class="prettyprint"><code>',
    replace: 'class="prettyprint source linenums"><code>'
  } */
];

files.forEach(file => {
  if (path.extname(file) === '.html') {
    // console.log(__dirname, file, path.join(apiPath, file));
    // console.log(fs.readFileSync(path.join(apiPath, file)).toString());
    const htmlPath = path.join(apiPath, file);
    const htmlContent = fs.readFileSync(htmlPath).toString();

    fs.writeFile(htmlPath, pretty(htmlContent, { ocd: true }), err => {
      if (err) {
        return console.error(err);
      }
      console.log(htmlPath, '格式化成功！');

      replacements.forEach(obj => {
        replace({
          regex: obj.find,
          replacement: obj.replace,
          paths: [htmlPath],
          recursive: false,
          silent: true
        });
      });
    });
  }
});

function copyDir(fromPath, toPath) {
  if (!fs.existsSync(toPath)) {
    fs.mkdirSync(toPath);
  }
  const dirsPath = fs.readdirSync(fromPath);
  dirsPath.forEach(dirPath => {
    const from = path.join(fromPath, dirPath);
    const to = path.join(toPath, dirPath);
    if (fs.statSync(from).isDirectory()) {
      copyDir(from, to);
    } else {
      fs.copyFileSync(from, to);
    }
  });
}

// 复制 assets 资源
copyDir(path.resolve('./docs/zh-cn/tutorials/assets'), path.resolve('./docs/zh-cn/api/assets'));
