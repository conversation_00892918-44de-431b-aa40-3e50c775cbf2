{"plugins": ["plugins/markdown"], "sourceType": "module", "source": {"include": ["./docs/en/src/main.js", "./docs/en/src/rtc/client.js", "./docs/en/src/rtc/stream.js", "./docs/en/src/rtc/local-stream.js", "./docs/en/src/rtc/remote-stream.js", "./docs/en/src/rtc/event.js", "./docs/en/src/common/rtc-error.js", "./docs/en/src/common/error-code.js"]}, "opts": {"access": "undefined, public", "encoding": "utf8", "recurse": true, "verbose": true, "readme": "./docs/en/main.md", "tutorials": "./docs/en/tutorials", "template": "../../node_modules/docdash-blue", "destination": "docs/en/api"}, "docdash": {"sort": false, "search": true, "collapse": true, "static": true, "sectionOrder": ["Namespaces", "Classes", "<PERSON><PERSON><PERSON>", "Externals", "Events", "Mixins", "Tutorials", "Interfaces"]}, "templates": {"default": {"outputSourceFiles": false, "useLongnameInNav": true}}}