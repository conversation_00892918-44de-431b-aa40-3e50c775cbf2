{"plugins": ["plugins/markdown"], "sourceType": "module", "source": {"include": ["./docs/zh-cn/api-cloud-next-zh-cn/src/main.js", "./docs/zh-cn/api-cloud-next-zh-cn/src/type.js", "./docs/zh-cn/api-cloud-next-zh-cn/src/trtc-event.js", "./docs/zh-cn/api-cloud-next-zh-cn/src/common/error-code.js", "./docs/zh-cn/api-cloud-next-zh-cn/src/common/rtc-error.js", "./docs/zh-cn/plugins/jsdoc.js"]}, "opts": {"access": "undefined, public", "encoding": "utf8", "recurse": true, "verbose": true, "readme": "./docs/zh-cn/main.md", "tutorials": "./docs/zh-cn/tutorials", "template": "../../node_modules/docdash-blue", "destination": "docs/zh-cn/api"}, "docdash": {"sort": false, "search": true, "collapse": true, "static": true, "sectionOrder": ["Namespaces", "Classes", "<PERSON><PERSON><PERSON>", "Externals", "Events", "Mixins", "Tutorials", "Interfaces"]}, "templates": {"default": {"outputSourceFiles": false, "useLongnameInNav": true}}}