const path = require('path');
const apiPath = path.join(__dirname, '../../', 'docs/zh-cn', 'api');
const fs = require('fs');
var files = fs.readdirSync(apiPath);

files.forEach(file => {
  if (path.extname(file) === '.html') {
    var htmlPath = path.join(apiPath, file);
    console.log('delete file', htmlPath);
    fs.unlinkSync(htmlPath);
  }
});

const apiPathEn = path.join(__dirname, '../../', 'docs/en', 'api');
var filesEn = fs.readdirSync(apiPathEn);

filesEn.forEach(file => {
  if (path.extname(file) === '.html') {
    var htmlPath = path.join(apiPathEn, file);
    console.log('delete file', htmlPath);
    fs.unlinkSync(htmlPath);
  }
});
