const { parse } = require('node-html-parser');

const fs = require('fs');
const path = require('path');
const translatePath = path.join(__dirname, '../../', 'docs/en', 'translate');
const rtcCNPath = path.join(__dirname, '../../', 'docs/en/src/', 'rtc-cn'); // rtc-cn 是 rtc 的 .bak
// const rtcPath = path.join(__dirname, '../../', 'docs/en/src/', 'rtc');
const rtcPath = path.join(__dirname, '../../', 'docs/en/src/', '');
const htmlName = 'TRTC.html'; // Client.html  local remote stream
const jsName = 'main.js';
const className = 'TRTC';
let htmlTranslatePath = path.join(translatePath, htmlName);
let jsPath = path.join(rtcPath, jsName);
let jsCNPath = path.join(rtcCNPath, jsName);

const html = fs.readFileSync(htmlTranslatePath).toString();
const root = parse(html);
console.log(root);
const article = root.getElementsByTagName("article")[0].childNodes;

let parseJSON = {};
let h4_MethodName = null;
let h5 = null;

function parseTable(table) {
  let tableJson = [];
  const tbody = table.childNodes[3];
  const trs = tbody.childNodes;
  trs.forEach(tr => {
    if (tr.nodeType !== 1) return;
    const tds = tr.childNodes;
    const tdJson = {};
    tds.forEach(td => {
      if (td.nodeType !== 1) return;
      if (td.classNames.includes("name")) {
        tdJson['name'] = td.text;
      } else if (td.classNames.includes("type")) {
        const types = td.childNodes;
        tdJson['type'] = "";
        types.forEach(type => {
          if (type.nodeType !== 1) {
            tdJson['type'] += type.text;
            return;
          };
          if (type.tagName === "SPAN") {
            tdJson['type'] += type.text;
          }
        });
        tdJson['type'] = tdJson['type'].replaceAll('\n', '').replaceAll(' ', '');
      } else if (td.classNames.includes("attributes") && td.text.indexOf("optional") > -1) {
        tdJson['attributes'] = td.text;
      } else if (td.classNames.includes("default")) {
        tdJson['default'] = td.childNodes[1]?.text;
      } else if (td.classNames.includes("description last")) {
        const tbs = td.childNodes;
        tdJson['description'] = { "content": parseDescription(td).substring(2) };
        tbs.forEach(tb => {
          if (tb.nodeType !== 1) return;
          if (tb.tagName === "TABLE") {
            tdJson['description']['param'] = parseTable(tb);
          }
        })
      }
    });
    tableJson.push(tdJson);
  });
  return tableJson;
}

function parseUl(ul) {
  let UlString = "";
  const children = ul.childNodes;
  children.forEach(child => {
    if (child.nodeType !== 1) return;
    if (child.tagName === "LI") {
      UlString += `\n- ${child.text}`;
    }
  })
  return UlString;
}

function parseP(p) {
  let pString = "";
  const children = p.childNodes;
  children.forEach(child => {
    if (child.nodeType !== 1) {
      pString += child.text;
      return;
    };
    if (child.tagName === "BR") {
      pString += "<br>";
    } else if (child.tagName === "A") {
      pString += ` {@link ${child.attributes.href.replaceAll(".html", "")} ${child.text}}`;
    } else if (child.tagName === "P") {
      pString += parseP(child);
    } else if (child.tagName === "UL") {
      pString += parseUl(child);
    } else {
      pString += child.toString();
    }
  });
  return pString.replaceAll('<strong>', '**').replaceAll('</strong>', '**');
}

function slim(descriptionString) {
  let slimDescription = "";
  // 处理连串的空格
  for (let i in descriptionString) {
    if (descriptionString[i] === ' ' && descriptionString[parseInt(i) + 1] === ' ') {
      continue;
    }
    slimDescription += descriptionString[i];
  }
  return slimDescription;
}

function parseDescription(description) {
  let descriptionString = "\n ";
  const children = description.childNodes;
  children.forEach(child => {
    if (child.nodeType !== 1) {
      return;
    };
    if (child.tagName === "BR") {
      descriptionString += "<br>\n";
    } else if (child.tagName === "A") {
      descriptionString += ` {@link ${child.attributes.href} ${child.text}}`;
    } else if (child.tagName === "P") {
      descriptionString += parseP(child);
    } else if (child.tagName === "UL") {
      descriptionString += parseUl(child);
    }
  });
  return slim(descriptionString);
}

article.forEach((dom, index) => {
  if (dom.nodeType !== 1) return;
  if (dom.tagName === "H4") {
    h4_MethodName = dom.id;
    parseJSON[h4_MethodName] = {};
    h5 = null;
    console.log(dom.id);
    return;
  }
  if (dom.tagName === "H5") {
    h5 = dom.text;
    parseJSON[h4_MethodName][h5] = {};
    console.log("     ", h5);
    return;
  }
  if (h5) {
    if (!dom.classNames) return;
    if (dom.classNames === 'highlight lang-javascript') {
      if (!parseJSON[h4_MethodName][h5]['code']) {
        parseJSON[h4_MethodName][h5]['code'] = dom.childNodes[0].text.replace('<code>', '').replace('</code>', '');
      } else {
        parseJSON[h4_MethodName][h5]['code'] = parseJSON[h4_MethodName][h5]['code'] + '\n@example\n ' + dom.childNodes[0].text.replace('<code>', '').replace('</code>', '');
      }
    } else if (dom.classNames === 'params') {
      parseJSON[h4_MethodName][h5] = parseTable(dom);
    } else if (dom.classNames === 'param-desc') {
      const s = parseDescription(dom);
      parseJSON[h4_MethodName][h5]['desc'] = s.substring(2);
    } else if (dom.classNames === 'param-type') {
      parseJSON[h4_MethodName][h5]['type'] = dom.childNodes[3].childNodes[1].text.replaceAll('.', '');
    }
  } else {
    if (dom.classNames === "description") {
      parseJSON[h4_MethodName][dom.classNames] = slim(parseP(dom));
    }
    if (dom.classNames === "details") {
      const dd = dom.childNodes[3];
      const ul = dd?.childNodes[1];
      const li = ul?.childNodes[1];
      if (!li) return;
      parseJSON[h4_MethodName]["since"] = slim(li.text);
    }
  }
});

const jsFile = fs.readFileSync(jsCNPath).toString();
let lastCursor = 0;
let result = "";

function parseParam(param, root) {
  let paramsArray = [];
  let paramJson = {};
  paramJson.name = (root === "") ? root : `${root}.`;
  paramJson.name += param.name;
  paramJson.type = param.type;
  if (param.attributes) {
    paramJson.type += "=";
  }
  if (param.default) {
    paramJson.name = `[${paramJson.name}=${param.default}]`;
  }
  paramJson.content = param?.description?.content;
  paramsArray.push(paramJson);
  if (param.description?.param && param.description?.param.length > 0) {
    param.description?.param.forEach(paramItem => {
      paramsArray = [...paramsArray, ...parseParam(paramItem, paramJson.name)];
    })
  }
  return paramsArray;
}

function generateComment(data) {
  let result = data.description;
  const params = data["Parameters:"];
  let paramsArray = [];
  params && params.length > 0 && params.forEach(paramItem => {
    paramsArray = [...paramsArray, ...parseParam(paramItem, "")];
  })
  console.log(paramsArray);
  paramsArray.forEach(param => {
    result += `\n@param {${param.type}} ${param.name}`;
    if (param.content) {
      result += ` ${param.content}`;
    }
  });

  result += `\n@memberof ${className}`;
  if (data["Returns:"]) {
    if (data["Returns:"].type.indexOf("Array") > -1) {
      data["Returns:"].type = data["Returns:"].type.substring(6, data["Returns:"].type.length - 1) + "[]";
    }
    result += `\n@returns {${data["Returns:"].type}}`;
    if (data["Returns:"]?.desc) { // data["Returns:"]?.desc 第一位是 '\n' 其实也不加空格
      result += ` ${data["Returns:"]?.desc}\n`;
    } else {
      result += `\n`;
    }
  }
  if (data["Throws:"]) result += `\n@throws ${data["Throws:"]?.desc}\n`;
  if (data["since"]) result += `\n@since ${data["since"]}\n`;
  if (data["Example"]) result += `\n@example\n${data.Example?.code}\n`;
  if (data["Examples"]) result += `\n@example\n${data.Examples?.code}\n`;
  return result.replaceAll('\n', '\n   * ');
}

let methodArray = [];
let failedMethodArray = [];

for (let method of Object.keys(parseJSON)) {
  const asyncMethodName = "  async " + method + "(";
  const methodName = "  " + method + "(";
  let positions = new Array();
  let pos = jsFile.indexOf(asyncMethodName);
  if (pos === -1) {
    pos = jsFile.indexOf(methodName);
    while (pos > -1) {
      positions.push(pos);
      pos = jsFile.indexOf(methodName, pos + 1);
    }
  } else positions.push(pos);
  if (positions.length !== 1) {
    console.log("ERROR positions.length !== 1, need to fix it.", method);
    failedMethodArray.push(method);
    continue;
  }
  else positions = positions[0];
  console.log(">", positions, method);
  methodArray.push({ method, positions, body: parseJSON[method] });
}

methodArray.sort((a, b) => { return a.positions - b.positions });

methodArray.forEach(methodArrayItem => {
  console.log("<", methodArrayItem.positions, methodArrayItem.method);
})

methodArray.forEach(methodArrayItem => {
  const { positions, body } = methodArrayItem;
  const commentBegin = jsFile.lastIndexOf("/**", positions);
  const commentEnd = jsFile.lastIndexOf(" */", positions);
  const comment = "/**" + generateComment(body) + "\n  ";
  const beforeExample = comment.substring(0, comment.indexOf("@example")).replaceAll('*  ', '* ').replaceAll('* \n', '*\n');
  result += jsFile.substring(lastCursor, commentBegin);
  result += beforeExample;
  result += comment.substring(comment.indexOf("@example"));
  lastCursor = commentEnd;
})

result += jsFile.substring(lastCursor);
if (failedMethodArray.length !== 0)
  result += `\n\n\n// Method not found: ${JSON.stringify(failedMethodArray)}`;

fs.writeFileSync(jsPath, result.replaceAll('   * \n   */', '   */'));
fs.writeFileSync(path.join(rtcCNPath, 'convert.json'), JSON.stringify(parseJSON, null, 2));
