import babel from '@rollup/plugin-babel';
import json from '@rollup/plugin-json';
import progress from 'rollup-plugin-progress';
import commonjs from '@rollup/plugin-commonjs';
import nodePolyfills from 'rollup-plugin-polyfill-node';
import resolve from '@rollup/plugin-node-resolve';
import { terser } from 'rollup-plugin-terser';
import path from 'path';
import fs from 'fs';
// import copy from 'rollup-plugin-copy' 

// FIXME: 转 ES5 未生效
const babelDefault = babel({
  exclude: /node_modules/,
  babelrc: false,
  babelHelpers: 'bundled',
  presets: [
    [
      '@babel/preset-env',
      {
        useBuiltIns: 'usage', // 开启后可以不用 plugin-transform-runtime, 按需转换新API
        corejs: 3,
        modules: false, // 设置false 否则 Babel 会在 Rollup 做处理之前，将我们的模块转成 CommonJS，导致 Rollup 的一些处理失败
        targets: {
          "firefox": "56",
          "chrome": "56",
          "safari": "11"
        },
      }
    ]
  ]
})
export default {
  input: './dist/npm-package/assets/debug-dialog.esm.js',
  output: [{
    file: './dist/npm-package/assets/debug-dialog.js',
    format: 'umd',
    name: 'TRTCDebugDialog'
  }],
  plugins: [babelDefault,
    json(),
    progress({
      clearLine: true // default: true
    }),
    // filesize(),
    resolve({
      browser: true
    }),
    commonjs({
      sourceMap: false
    }),
    nodePolyfills(/* options */),
    
    terser({
      numWorkers: 4,
      safari10: true, // work around Safari 10/11 bugs in loop scoping and await
      compress: {
        booleans_as_integers: false, // Turn booleans into 0 and 1, also makes comparisons with booleans use == and != instead of === and !==
        keep_infinity: true, // prevent Infinity from being compressed into 1/0, which may cause performance issues on Chrome.
        module: true
      }
    }),
    {
      name: 'delete-source-file',
      buildEnd() {
        const inputFile = path.resolve('./dist/npm-package/assets/debug-dialog.esm.js');
        
        if (fs.existsSync(inputFile)) {
          fs.unlink(inputFile, (err) => {
            if (err) {
              console.warn(`无法删除源文件: ${inputFile}`, err);
            }
          });
        }
      }
    }
    // copy({
    //   targets: [
    //     { src: './dist/trtc.esm.js', dest: './dist/npm-package' },
    //   ]
    // })
  ],
}