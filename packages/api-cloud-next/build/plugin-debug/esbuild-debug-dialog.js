#!/usr/bin/env node
const esbuild = require('esbuild');
const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const fs = require('fs');
const path = require('path');
const { fileURLToPath } = require('url');
const { exec } = require('child_process');

const argv = yargs(hideBin(process.argv))
  .alias('a', 'assetsPath')
  .alias('d', 'dir')
  .alias('f', 'format')
  .options({
    prd: {
      type: 'boolean',
      describe: 'build for production',
    },
    inline: {
      type: 'boolean',
      describe: 'inline worker js and wasm',
    },
  }).parse();

const plugins = [{
  name: 'plugin',
  setup(build) {
    build.onEnd(async result => {
      if (result.errors.length > 0) return
      console.log((await esbuild.analyzeMetafile(result.metafile)).replace('\n', ''))
      // tsc() // 不用 tsc 
      polyfillGlobalName()
      console.log(`[esbuild] ${argv.format} dist build finished, path:`, cdnDistPath(argv.format))
      const trtcDevDist = '../../examples/api-cloud-next/dist/debug-dialog.iife.js';
      if (!argv.prd) {
        fs.copyFileSync(cdnDistPath(argv.format), trtcDevDist);
        console.log(`[esbuild dev] ${argv.format} dist copied, path:`, trtcDevDist)
      } 
    }),
      build.onStart(() => {
        console.log('building...')
      })
  },
}]

// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename);
// const rootDir = resolve(__dirname, '..');
const format = (argv.format === 'umd' ? 'iife' : argv.format) || 'esm'
// console.log("rootDir:", rootDir)
const cdnDistPath = (format) => argv.prd ? `./dist/npm-package/assets/debug-dialog.${format}.js` : `./dist/npm-package/assets/debug-dialog.${format}.js`;

const config = {
  entryPoints: ['./src/plugins/debug/debug-dialog.ts'],
  bundle: true,
  format,
  globalName: 'TRTCDebugDialog',
  target: 'es2017',
  metafile: true,
  sourcemap: !argv.prd,
  minify: argv.prd,
  outfile: cdnDistPath(argv.format),
  plugins
}

  ; (async () => {
    try {
      if (argv.prd) {
        await esbuild.build(config)
      }
      if (!argv.prd) {
        const builder = await esbuild.context(config)
        builder.watch()
      }
    } catch (err) {
      console.error(err);
    }
  })()

function polyfillGlobalName() {
  if (argv.format == 'iife') {
    const s = fs.readFileSync(config.outfile, 'utf8');
    const lastSeparator = s.lastIndexOf(';');
    fs.writeFileSync(config.outfile, s.substring(0, lastSeparator) + '.default' + s.substring(lastSeparator));
  }
}

function tsc() {
  exec('tsc', (error, stdout, stderr) => {
    if (error) {
      console.error(`[tsc] error: ${error.message}`)
      return
    }
    if (stderr) {
      console.error(`[tsc] error output: ${stderr}`)
      return
    }
    console.log('[tsc] declaration build finished')
    console.log('----------------------------------------------------------------')
  })
}