// packages/devtools/src/index.tsx
import { render } from 'preact';
import './style.css';
import Toolbar from './components/Toolbar';
import Aegis from 'aegis-web-sdk';

declare global {
  interface Window {
    Aegis: typeof Aegis;
  }
}

function MainContainer() {
  return (
    <div>
      <Toolbar />
    </div>
  );
}

function App() {
  return (
    <div id="trtc-devtools2">
      {/* <Toolbar visible={false} /> */}
      <MainContainer />
    </div>
  );
}

function initReport() {
  const aegis = new Aegis({ id: 'iHWefAYqgYMbhmQBIN' });
  aegis.reportEvent({ name: 'init' });
}

export function initDevTools() {
  if (document.getElementById('trtc-devtools2')) return;

  const containerElement = document.createElement('div');
  containerElement.id = 'trtc-devtools2';
  document.body.appendChild(containerElement);
  initReport();

  render(<App />, containerElement);
}

export default initDevTools;
