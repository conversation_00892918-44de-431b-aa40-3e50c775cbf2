// packages/devtools/src/index.tsx
import { DevToolsManager } from './core/DevToolsManager';
import Aegis from 'aegis-web-sdk';

declare global {
  interface Window {
    Aegis: typeof Aegis;
  }
}

function initReport() {
  const aegis = new Aegis({ id: 'iHWefAYqgYMbhmQBIN' });
  aegis.reportEvent({ name: 'init' });
}

export function initDevTools() {
  if (document.getElementById('trtc-devtools-root')) return;

  initReport();

  // 使用新的 DevToolsManager 初始化
  const devTools = new DevToolsManager();
  devTools.init();
}

export default initDevTools;
