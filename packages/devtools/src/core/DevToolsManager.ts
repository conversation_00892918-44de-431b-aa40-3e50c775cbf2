import { render } from 'preact';
import { ButtonConfig, PanelConfig, DevToolsConfig } from '../types';
import { createIsolatedStyles, DEVTOOLS_PREFIX } from '../styles/isolated';
import { icons } from '../icons';
import ToolBar from '../components/Toolbar';

// ==================== 核心类 ====================

export class DevToolsManager {
  private buttons: ButtonConfig[];
  private panels: PanelConfig[];
  private config: DevToolsConfig;
  private activePanel: string | null = null;
  private container: HTMLElement | null = null;
  private refreshTimer: number | null = null;
  private isRefreshing: boolean = false;
  private instanceColors: Map<string, string> = new Map();
  private selectedInstances: Set<string> = new Set(['all']);
  private allInstanceIds: Set<string> = new Set();
  private resizeCleanup?: () => void;
  private analyticsRefreshTimer: number | null = null;
  private isAnalyticsRefreshing: boolean = false;
  private graphRefreshTimer: number | null = null;
  private isGraphRefreshing: boolean = false;

  constructor(config: DevToolsConfig = {}) {
    this.config = { position: 'bottom-center', theme: 'dark', ...config };
    this.buttons = this.getDefaultButtons();
    this.panels = this.getDefaultPanels();
  }

  private getDefaultButtons(): ButtonConfig[] {
    return [
      {
        id: 'addButton',
        icon: icons.trtcLogo,
        tooltip: 'TRTC',
        panelId: 'trtcPanel'
      },
      {
        id: 'subtractButton',
        icon: icons.function,
        tooltip: 'Functions',
        panelId: 'clickPanel'
      },
      {
        id: 'macroButton',
        icon: icons.chartBar,
        tooltip: 'Analytics',
        panelId: 'analyticsPanel'
      },
      {
        id: 'graphButton',
        icon: icons.graph,
        tooltip: 'Graph',
        panelId: 'graphPanel'
      },
      {
        id: 'settingsButton',
        icon: icons.settings,
        tooltip: 'Settings',
        panelId: 'settingsPanel',
        active: true
      }
    ];
  }

  private getDefaultPanels(): PanelConfig[] {
    return [
      {
        id: 'trtcPanel',
        title: 'TRTC 配置',
        icon: icons.trtcLogo,
        content: this.createTRTCPanel()
      },
      {
        id: 'clickPanel',
        title: 'Functions',
        icon: icons.function,
        content: this.createFunctionsPanel()
      },
      {
        id: 'analyticsPanel',
        title: 'Analytics',
        icon: icons.chartBar,
        content: this.createAnalyticsPanel()
      },
      {
        id: 'graphPanel',
        title: 'Graph',
        icon: icons.graph,
        content: this.createGraphPanel()
      },
      {
        id: 'settingsPanel',
        title: 'Settings',
        icon: icons.settings,
        content: this.createSettingsPanel()
      }
    ];
  }

  init(): void {
    console.log('Initializing devtools...');
    
    // 注入样式
    this.injectStyles();
    
    // 创建隔离的容器
    this.createContainer();
    
    // 应用保存的主题
    const savedTheme = localStorage.getItem('trtc-devtools-theme') || 'light';
    this.updateTheme(savedTheme);
    
    // 应用保存的位置设置
    const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
    setTimeout(() => {
      this.updateToolbarPosition(savedPlacement);
    }, 100);
    
    // 创建 DOM 结构
    this.createDOM();
    
    // 延迟绑定事件确保 DOM 完全就绪
    setTimeout(() => {
      this.setupEventListeners();
    }, 0);
    
    console.log('Devtools initialized successfully');
  }

  private injectStyles(): void {
    if (!document.getElementById('trtc-devtools-styles')) {
      document.head.insertAdjacentHTML('beforeend', createIsolatedStyles());
    }
  }

  private createContainer(): void {
    // 创建完全隔离的根容器
    this.container = document.createElement('div');
    this.container.className = DEVTOOLS_PREFIX;
    this.container.id = 'trtc-devtools-root';
    
    // 确保容器不受用户样式影响
    this.container.style.cssText = `
      all: unset !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      pointer-events: none !important;
      z-index: 2147483647 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    `;
    
    document.body.appendChild(this.container);
  }

  private createDOM(): void {
    if (!this.container) return;

    // 创建面板HTML
    const panelsHTML = this.panels.map(panel => panel.content).join('');
    this.container.innerHTML = panelsHTML;

    // 创建工具栏容器
    const toolbarContainer = document.createElement('div');
    toolbarContainer.id = 'toolbar-container';
    this.container.appendChild(toolbarContainer);

    // 使用 Preact 渲染工具栏
    render(
      <ToolBar
        buttons={this.buttons}
        onButtonClick={this.handleButtonClick.bind(this)}
        activePanel={this.activePanel || undefined}
      />,
      toolbarContainer
    );

    console.log('DOM created, container children:', this.container.children.length);
    console.log('Panels created:', this.panels.map(p => p.id));
    console.log('Buttons created:', this.buttons.map(b => b.id));
  }

  private setupEventListeners(): void {
    if (!this.container) return;

    console.log('Setting up event listeners...');

    // 初始化所有面板为隐藏状态
    this.panels.forEach(panel => {
      const element = this.container!.querySelector(`#${panel.id}`) as HTMLElement;
      if (element) {
        element.style.setProperty('display', 'none', 'important');
      }
    });

    // 全局点击事件 - 点击外部关闭面板
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const isDevToolsClick = target.closest(`#${this.container!.id}`);

      if (!isDevToolsClick) {
        this.hideAllPanels();
      }
    });

    console.log('✅ All event listeners setup completed');
  }

  private handleButtonClick(buttonId: string): void {
    const button = this.buttons.find(b => b.id === buttonId);
    if (button) {
      console.log(`✅ ${button.tooltip} button clicked!`);
      this.togglePanel(button.panelId);
      this.updateToolbarState();
    }
  }

  private updateToolbarState(): void {
    if (!this.container) return;

    const toolbarContainer = this.container.querySelector('#toolbar-container');
    if (toolbarContainer) {
      render(
        <Toolbar
          buttons={this.buttons}
          onButtonClick={this.handleButtonClick.bind(this)}
          activePanel={this.activePanel || undefined}
        />,
        toolbarContainer
      );
    }
  }

  private togglePanel(panelId: string): void {
    if (!this.container) return;

    const panel = this.container.querySelector(`#${panelId}`) as HTMLElement;
    if (!panel) return;

    const isVisible = panel.style.display === 'block';
    const shouldShow = !isVisible;

    // 如果要显示新面板，先隐藏其他面板
    if (shouldShow) {
      this.hideAllPanels();
      panel.style.setProperty('display', 'block', 'important');
      this.activePanel = panelId;
      this.positionPanel(panel);
    } else {
      this.hideAllPanels();
    }

    // 更新工具栏状态
    this.updateToolbarState();
  }

  private hideAllPanels(): void {
    if (!this.container) return;

    this.panels.forEach(panel => {
      const element = this.container!.querySelector(`#${panel.id}`) as HTMLElement;
      if (element) {
        element.style.setProperty('display', 'none', 'important');
      }
    });

    this.activePanel = null;
    this.updateToolbarState();
  }

  private positionPanel(panel: HTMLElement): void {
    // 简单的面板定位逻辑
    panel.style.setProperty('bottom', '80px', 'important');
    panel.style.setProperty('left', '50%', 'important');
    panel.style.setProperty('transform', 'translateX(-50%)', 'important');
    panel.style.setProperty('max-width', '600px', 'important');
    panel.style.setProperty('width', '90vw', 'important');
    panel.style.setProperty('max-height', '70vh', 'important');
  }

  private updateTheme(theme: string): void {
    if (!this.container) return;
    
    if (theme === 'light') {
      this.container.classList.add('light-theme');
    } else {
      this.container.classList.remove('light-theme');
    }
    
    localStorage.setItem('trtc-devtools-theme', theme);
  }

  private updateToolbarPosition(position: string): void {
    if (!this.container) return;
    
    const toolbar = this.container.querySelector('.trtc-toolbar') as HTMLElement;
    if (!toolbar) return;
    
    // 重置所有位置样式
    toolbar.style.removeProperty('left');
    toolbar.style.removeProperty('right');
    toolbar.style.removeProperty('transform');
    
    switch (position) {
      case 'bottom-left':
        toolbar.style.setProperty('left', '20px', 'important');
        break;
      case 'bottom-right':
        toolbar.style.setProperty('right', '20px', 'important');
        break;
      case 'bottom-center':
      default:
        toolbar.style.setProperty('left', '50%', 'important');
        toolbar.style.setProperty('transform', 'translateX(-50%)', 'important');
        break;
    }
    
    localStorage.setItem('trtc-devtools-placement', position);
  }

  // 面板内容创建方法（占位符，后续实现）
  private createTRTCPanel(): string {
    return `<div id="trtcPanel" class="trtc-panel"><div class="trtc-panel-header">TRTC Panel</div></div>`;
  }

  private createFunctionsPanel(): string {
    return `<div id="clickPanel" class="trtc-panel"><div class="trtc-panel-header">Functions Panel</div></div>`;
  }

  private createAnalyticsPanel(): string {
    return `<div id="analyticsPanel" class="trtc-panel"><div class="trtc-panel-header">Analytics Panel</div></div>`;
  }

  private createGraphPanel(): string {
    return `<div id="graphPanel" class="trtc-panel"><div class="trtc-panel-header">Graph Panel</div></div>`;
  }

  private createSettingsPanel(): string {
    return `<div id="settingsPanel" class="trtc-panel"><div class="trtc-panel-header">Settings Panel</div></div>`;
  }
}
