// ==================== 样式系统 - 100% 隔离 ====================

const DEVTOOLS_PREFIX = 'trtc-devtools';
const CSS_NAMESPACE = `.${DEVTOOLS_PREFIX}`;

export const createIsolatedStyles = (): string => `
  <style id="trtc-devtools-styles">
    /* CSS Reset for DevTools - 精确隔离，避免过度重置 */
    ${CSS_NAMESPACE} {
      /* CSS变量定义 - 默认深色主题 */
      --trtc-primary-bg: rgba(30, 30, 30, 0.98);
      --trtc-toolbar-bg: linear-gradient(135deg, rgba(40, 40, 45, 0.98), rgba(30, 30, 35, 0.98));
      --trtc-text-primary: #ffffff;
      --trtc-text-secondary: rgba(255, 255, 255, 0.7);
      --trtc-text-muted: rgba(255, 255, 255, 0.5);
      --trtc-border-color: rgba(255, 255, 255, 0.1);
      --trtc-hover-bg: rgba(255, 255, 255, 0.12);
      --trtc-active-bg: rgba(255, 255, 255, 0.08);
      --trtc-input-bg: rgba(255, 255, 255, 0.1);
      --trtc-input-border: rgba(255, 255, 255, 0.2);
      --trtc-input-focus-bg: rgba(255, 255, 255, 0.15);
      --trtc-input-focus-border: rgba(255, 255, 255, 0.4);
      --trtc-toggle-bg: rgba(255, 255, 255, 0.2);
      --trtc-toggle-slider: #ffffff;
      --trtc-accent-color: #007AFF;
      --trtc-accent-hover: #0056CC;
      --trtc-success-color: #34C759;
      --trtc-danger-color: #ff4757;
      --trtc-blur: blur(20px);
      --trtc-border-radius: 12px;
      --trtc-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.3);
      --trtc-shadow-heavy: 0 20px 80px rgba(0, 0, 0, 0.6);
      --trtc-separator-color: rgba(255, 255, 255, 0.15);
      --trtc-icon-color: rgba(255, 255, 255, 0.85);
      --trtc-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      
      /* 根容器样式 */
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      pointer-events: none !important;
      z-index: 2147483647 !important;
      font-family: var(--trtc-font-family) !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      color: var(--trtc-text-primary) !important;
      text-align: left !important;
      direction: ltr !important;
      box-sizing: border-box !important;
    }

    /* 浅色主题变量 */
    ${CSS_NAMESPACE}.light-theme {
      --trtc-primary-bg: rgba(255, 255, 255, 0.98);
      --trtc-toolbar-bg: linear-gradient(135deg, rgba(250, 250, 252, 0.98), rgba(245, 245, 247, 0.98));
      --trtc-text-primary: #1d1d1f;
      --trtc-text-secondary: rgba(29, 29, 31, 0.7);
      --trtc-text-muted: rgba(29, 29, 31, 0.5);
      --trtc-border-color: rgba(0, 0, 0, 0.1);
      --trtc-hover-bg: rgba(0, 0, 0, 0.06);
      --trtc-active-bg: rgba(0, 0, 0, 0.04);
      --trtc-input-bg: rgba(0, 0, 0, 0.05);
      --trtc-input-border: rgba(0, 0, 0, 0.15);
      --trtc-input-focus-bg: rgba(0, 0, 0, 0.08);
      --trtc-input-focus-border: rgba(0, 0, 0, 0.3);
      --trtc-toggle-bg: rgba(0, 0, 0, 0.15);
      --trtc-toggle-slider: #ffffff;
      --trtc-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.15);
      --trtc-shadow-heavy: 0 20px 80px rgba(0, 0, 0, 0.25);
      --trtc-separator-color: rgba(0, 0, 0, 0.15);
      --trtc-icon-color: rgba(29, 29, 31, 0.85);
    }

    /* 重置只针对DevTools内部元素，但保留必要的显示属性 */
    ${CSS_NAMESPACE} * {
      box-sizing: border-box !important;
      font-family: var(--trtc-font-family) !important;
    }

    /* Panel Styles - 基础样式，位置由JS动态设置 */
    ${CSS_NAMESPACE} .trtc-panel {
      all: unset !important;
      position: fixed !important;
      /* 位置和尺寸由JS动态设置，不在CSS中固定 */
      background: var(--trtc-primary-bg) !important;
      border-radius: 20px !important;
      padding: 32px !important;
      z-index: 2147483648 !important;
      backdrop-filter: var(--trtc-blur) !important;
      box-shadow: var(--trtc-shadow-heavy), 0 0 0 1px var(--trtc-border-color) !important;
      border: 1px solid var(--trtc-border-color) !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
      display: none !important;
      pointer-events: auto !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      box-sizing: border-box !important;
      transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1) !important;
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 全屏模式样式 */
    ${CSS_NAMESPACE} .trtc-panel.fullscreen {
      position: fixed !important;
      top: 20px !important;
      left: 20px !important;
      right: 20px !important;
      bottom: 20px !important;
      width: auto !important;
      max-width: none !important;
      max-height: none !important;
      height: auto !important;
      transform: none !important;
      z-index: 999999 !important;
    }

    ${CSS_NAMESPACE} .trtc-panel-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 16px !important;
      margin-bottom: 24px !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-section {
      all: unset !important;
      display: block !important;
      margin-bottom: 32px !important;
    }

    ${CSS_NAMESPACE} .trtc-section:last-child {
      margin-bottom: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-item {
      all: unset !important;
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      margin-bottom: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-content {
      all: unset !important;
      display: block !important;
      flex: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-title {
      all: unset !important;
      display: block !important;
      font-size: 16px !important;
      font-weight: 500 !important;
      margin-bottom: 4px !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-description {
      all: unset !important;
      display: block !important;
      font-size: 14px !important;
      color: var(--trtc-text-secondary) !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.4 !important;
    }

    /* Toolbar Styles */
    ${CSS_NAMESPACE} .trtc-toolbar {
      all: unset !important;
      position: fixed !important;
      bottom: 20px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      z-index: 2147483647 !important;
      pointer-events: auto !important;
      display: inline-block !important;
      width: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-toolbar-container {
      all: unset !important;
      background: var(--trtc-toolbar-bg) !important;
      border-radius: 20px !important;
      padding: 6px !important;
      display: flex !important;
      align-items: center !important;
      gap: 2px !important;
      backdrop-filter: var(--trtc-blur) !important;
      box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.5),
        0 2px 6px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.08) !important;
      width: auto !important;
      min-width: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-btn {
      all: unset !important;
      background: transparent !important;
      border: none !important;
      color: var(--trtc-icon-color) !important;
      width: 32px !important;
      height: 32px !important;
      border-radius: var(--trtc-border-radius) !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative !important;
      overflow: hidden !important;
      pointer-events: auto !important;
      font-family: var(--trtc-font-family) !important;
      font-size: 14px !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon {
      all: unset !important;
      position: relative !important;
      width: 22px !important;
      height: 22px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      pointer-events: none !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon svg {
      width: 22px !important;
      height: 22px !important;
      pointer-events: none !important;
      display: block !important;
      -webkit-backface-visibility: hidden !important;
      backface-visibility: hidden !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
      image-rendering: -webkit-optimize-contrast !important;
      image-rendering: crisp-edges !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon img {
      width: 24px !important;
      height: 24px !important;
      pointer-events: none !important;
      display: block !important;
      -webkit-backface-visibility: hidden !important;
      backface-visibility: hidden !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
      image-rendering: -webkit-optimize-contrast !important;
      image-rendering: crisp-edges !important;
    }

    /* 深色主题的图标阴影 */
    ${CSS_NAMESPACE}:not(.light-theme) .trtc-btn-icon svg,
    ${CSS_NAMESPACE}:not(.light-theme) .trtc-btn-icon img {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
    }

    /* 浅色主题的图标阴影 */
    ${CSS_NAMESPACE}.light-theme .trtc-btn-icon svg,
    ${CSS_NAMESPACE}.light-theme .trtc-btn-icon img {
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)) !important;
      border-radius: inherit !important;
      opacity: 0 !important;
      transition: opacity 0.25s ease !important;
      pointer-events: none !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover {
      background: var(--trtc-hover-bg) !important;
      color: var(--trtc-text-primary) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--trtc-shadow-light) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover::before {
      opacity: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:active {
      transform: translateY(0) !important;
      background: var(--trtc-active-bg) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn.active {
      color: var(--trtc-text-primary) !important;
    }

    ${CSS_NAMESPACE} .trtc-separator {
      all: unset !important;
      width: 1px !important;
      height: 20px !important;
      background: var(--trtc-separator-color) !important;
      margin: 0 3px !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-tooltip {
      all: unset !important;
      position: absolute !important;
      bottom: 100% !important;
      left: 50% !important;
      transform: translateX(-50%) translateY(2px) !important;
      margin-bottom: 6px !important;
      padding: 4px 8px !important;
      background: rgba(0, 0, 0, 0.9) !important;
      color: #ffffff !important;
      font-size: 11px !important;
      font-weight: 500 !important;
      border-radius: 4px !important;
      white-space: nowrap !important;
      pointer-events: none !important;
      opacity: 0 !important;
      transition: all 0.2s ease !important;
      z-index: 2147483647 !important;
      font-family: var(--trtc-font-family) !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-tooltip::after {
      content: '' !important;
      position: absolute !important;
      top: 100% !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      border: 3px solid transparent !important;
      border-top-color: rgba(0, 0, 0, 0.9) !important;
      pointer-events: none !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover .trtc-tooltip {
      opacity: 1 !important;
      transform: translateX(-50%) translateY(0) !important;
    }
  </style>
`;

export { DEVTOOLS_PREFIX, CSS_NAMESPACE };
