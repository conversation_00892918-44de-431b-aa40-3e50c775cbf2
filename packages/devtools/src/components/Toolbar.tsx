import { ButtonConfig } from '../types';

interface ToolbarProps {
  buttons: ButtonConfig[];
  onButtonClick: (buttonId: string) => void;
  activePanel?: string;
}

interface ButtonProps {
  config: ButtonConfig;
  onClick: (buttonId: string) => void;
  isActive?: boolean;
}

function Button({ config, onClick, isActive }: ButtonProps) {
  const handleClick = (e: Event) => {
    e.preventDefault();
    e.stopPropagation();
    onClick(config.id);
  };

  return (
    <button
      className={`trtc-btn ${isActive ? 'active' : ''}`}
      id={config.id}
      data-app-id={`trtc:${config.id}`}
      onClick={handleClick}
    >
      <div className="trtc-btn-icon">
        <div dangerouslySetInnerHTML={{ __html: config.icon }} />
        {config.notification && <div className="trtc-notification active"></div>}
      </div>
      <span className="trtc-tooltip">{config.tooltip}</span>
    </button>
  );
}

function Separator() {
  return <div className="trtc-separator"></div>;
}

export default function Toolbar({ buttons, onButtonClick, activePanel }: ToolbarProps) {
  if (!buttons || buttons.length === 0) {
    return null;
  }

  const mainButtons = buttons.slice(0, -1);
  const lastButton = buttons[buttons.length - 1];

  return (
    <div className="trtc-toolbar">
      <div className="trtc-toolbar-container">
        {mainButtons.map((button) => (
          <Button
            key={button.id}
            config={button}
            onClick={onButtonClick}
            isActive={activePanel === button.panelId}
          />
        ))}
        <Separator />
        <Button
          config={lastButton}
          onClick={onButtonClick}
          isActive={activePanel === lastButton.panelId}
        />
      </div>
    </div>
  );
}
