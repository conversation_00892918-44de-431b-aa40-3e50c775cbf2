// ==================== 图标系统 ====================

export const icons = {
  graph: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M3 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" />
      <path d="M15 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" />
      <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" />
      <path d="M6 15v-1a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v1" />
      <path d="M12 9l0 3" />
    </svg>
  `,
  function: `
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M4 4m0 2.667a2.667 2.667 0 0 1 2.667 -2.667h10.666a2.667 2.667 0 0 1 2.667 2.667v10.666a2.667 2.667 0 0 1 -2.667 2.667h-10.666a2.667 2.667 0 0 1 -2.667 -2.667z" />
      <path d="M9 15.5v.25c0 .69 .56 1.25 1.25 1.25c.71 0 1.304 -.538 1.374 -1.244l.752 -7.512a1.381 1.381 0 0 1 1.374 -1.244c.69 0 1.25 .56 1.25 1.25v.25" />
      <path d="M9 12h6" />
      </svg>
  `,
  chartBar: `
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M4 20h14" />
    </svg>
  `,
  settings: `
    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" />
      <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" />
    </svg>
  `,
  trtcLogo: `<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAABelBMVEUAAAAAmP8AmP8AZ/sAYvsAif4AmP8AYvsAvP8AmP8Auf8AYvsA0P8A0P8AYvsAYvsAYvsA0P8A0P8Amf8AYvsA0P8A0P8AYvsAmP8AmP8A0P8AYvsA0P8AmP8AYvsAYvsAmP8A0P8AmP8A0P8AYvsA0P8Al/8AYvsA0P8A0P8AYvsAYvsAmP8AmP8A0P8AmP8A0P8AYvsAmP8AmP8A0P8AmP8A0P8A0P8AYvsA0P8AYvsAmP8AYvsAYvsAYfsAYvsA0P8AmP8A0P8Aqv8AYvsA0P8AYvsAmP8AYvsAmP8A0P8A0P8AYvsA0P8AmP8A0P8A0P8A0P8AYvsA0P8A0P8AmP8A0P8A0P8AYvsA0P8A0f8AmP8AYvsA0P8A0P8AYvsA0P8AYvsA0P8AYvsA0P8AmP8AYvsAmP8AYvsAYvsAmP8AYvsAmP8AmP8AmP8AmP8AYvsAmP8AmP8A0P8AmP8A0P8AmP8AmP8AYvsAmP8AYvsAmP8AYvsA0P9J4SmKAAAAe3RSTlMAO/MMxAkn6Qb0Dfb17b2xnD83Ly8J+/r4593TwruEeG9hSCYcGBIG+fLy4+LZ1s/OzcnBuLCvpqJ/ZWFaUkI+LSAfFhQQEP7z7Oji2su4squdlZSPjod0bmtpaWBbT0tKSEU1MiIhHRnt3MOno52bjId+WllRUUI7Nymc4G5LAAACeklEQVRo3u3V51oiMRiG4Xd1Z0AQASkqoqgI9rb2joKuvffedXuvE859wyVeWQYEh5nwYzf3CTzJfJMEgiAIgiAI/wHzzPTV2cmVB1x4QkHnl93N1WWfrdlrGyg3eunlJ/s7g/7OhdI44zOmYglFLuqH1/u6F1u88XR9Fj0rD1+7onBurfl9Nrb0dKVO5EG6dTWMfv6w0tVetBI+KI3nUq/92zRs976er1TuFf3CRZ/X4Ij0tbdE+UvJM5qt78zaaNE4+et3dAuqCDW9s5AlsqbtqLheKCpFNJJQPvjoaLoj0EJmDXUE5m/+zN9qPQgtwv3KoxEquutLS3j9Z2ZoUvcyY4QJbtpSG537M9BmqkPJHqGcq80sYdsKQqs6JXcEnuPuZKJ5wAnNpJpsESY0vJhoLB97oJ1clT3CRIYHBg9CyEdDZa4IY0aeRpWnRi4d48jT4RMjN7Umssd3J7Mj1YS0/gTFayZNp2UVhJCeGBij/y730BxJcIAx+Jw01raRe0dgDDrxbBhJJjcYA++upnM6jAdviqGi4xZmL6Pb0UqYj9DBkvE9SQ6DqRiHit6XcV7GWA9J0XYDXVy96kh/ccBKUtmboM/t+5KURpdropWoBKCX+XsNy7Rvy3AQFesE9LP8+FTTUVXV3vX2UAakMqKydAdDSFFZngojofg5URmC4dIjewWIvLosQKRntgCRDYB/ZAzgHjH9BrhHyiSAe6QWFOeI9RQU58hSIyh+EXbN844EQHGOzE2C4hypjoHiHBkCB+pHawR8bBDG5AYf51YWsUvgQ3KwjUyCl0b7Q+MI/MQC1VZSYbJPgqu7ibFxtwRBEARBEIR/yR9572Zi5JPyqAAAAABJRU5ErkJggg==" alt="TRTC Logo" />`,
  arrowsMaximize: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M16 4l4 0l0 4" />
      <path d="M14 10l6 -6" />
      <path d="M8 20l-4 0l0 -4" />
      <path d="M4 20l6 -6" />
      <path d="M16 20l4 0l0 -4" />
      <path d="M14 14l6 6" />
      <path d="M8 4l-4 0l0 4" />
      <path d="M4 4l6 6" />
    </svg>
  `,
  refresh: `
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4" />
      <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4" />
    </svg>
  `,
  close: `
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M18 6l-12 12" />
      <path d="M6 6l12 12" />
    </svg>
  `,
  bug: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M9 9v-1a3 3 0 0 1 6 0v1" />
      <path d="M8 9h8a6 6 0 0 1 1 3v3a5 5 0 0 1 -10 0v-3a6 6 0 0 1 1 -3" />
      <path d="M3 13l4 0" />
      <path d="M17 13l4 0" />
      <path d="M12 20l0 -6" />
      <path d="M4 19l3.35 -2" />
      <path d="M20 19l-3.35 -2" />
      <path d="M4 7l3.75 2.4" />
      <path d="M20 7l-3.75 2.4" />
    </svg>
  `,
  fileSearch: `
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M14 3v4a1 1 0 0 0 1 1h4" />
      <path d="M12 21h-5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v4.5" />
      <path d="M16.5 17.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0" />
      <path d="M18.5 19.5l2.5 2.5" />
    </svg>
  `,
  network: `
    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M6 9a6 6 0 1 0 12 0a6 6 0 0 0 -12 0" />
      <path d="M3 9c0 2.4 2.4 5 6 6" />
      <path d="M21 9c0 2.4 -2.4 5 -6 6" />
      <path d="M12 3c1.2 0 2.4 .6 3 1.5" />
      <path d="M12 3c-1.2 0 -2.4 .6 -3 1.5" />
      <path d="M12 15l0 6" />
    </svg>
  `,
  deviceCheck: `
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />
      <path d="M4 16v2a2 2 0 0 0 2 2h2" />
      <path d="M16 4h2a2 2 0 0 1 2 2v2" />
      <path d="M16 20h2a2 2 0 0 0 2 -2v-2" />
      <path d="M9 12l2 2l4 -4" />
    </svg>
  `
};

// 创建按钮组件的辅助函数
export const createButton = (config: { id: string; icon: string; tooltip: string; active?: boolean; notification?: boolean }): string => `
  <button class="trtc-btn ${config.active ? 'active' : ''}" id="${config.id}" data-app-id="trtc:${config.id}">
    <div class="trtc-btn-icon">
      ${config.icon}
      <div class="trtc-notification ${config.notification ? 'active' : ''}"></div>
    </div>
    <span class="trtc-tooltip">${config.tooltip}</span>
  </button>
`;

export const createSeparator = (): string => `
  <div class="trtc-separator"></div>
`;

export const createToolbar = (buttons: any[]): string => `
  <div class="trtc-toolbar">
    <div class="trtc-toolbar-container">
      ${buttons.slice(0, -1).map(btn => createButton(btn)).join('')}
      ${createSeparator()}
      ${createButton(buttons[buttons.length - 1])}
    </div>
  </div>
`;
