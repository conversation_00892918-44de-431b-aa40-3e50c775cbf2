## Feature Description

This document describes how to enable and disable local audio and video during an audio/video call.

## Implementation Process

### Option one: Using `mute`/`unmute`

You can call {@link LocalStream#muteAudio muteAudio()}, {@link LocalStream#unmuteAudio unmuteAudio()}, {@link LocalStream#muteVideo muteVideo()}, and {@link LocalStream#unmuteVideo unmuteVideo()} to disable and enable local audio and video during a call.

**Sample Code:**

```javascript
// Disable audio, and remote users will receive the mute-audio event callback.
localStream.muteAudio();

// Enable audio, and remote users will receive the unmute-audio event callback.
localStream.unmuteAudio();

// Disable video, and remote users will receive the mute-video event callback.
localStream.muteVideo();

// Enable video, and remote users will receive the unmute-video event callback.
localStream.unmuteVideo();
```

- Advantages: Short time-consuming and strong compatibility.
- Disadvantages: Using muteVideo/muteAudio will not stop the camera/microphone, and the lights of the camera and microphone are still on.

## Option two: Using `addTrack`/`removeTrack`/`replaceTrack`

Use {@link LocalStream#addTrack addTrack()} and {@link LocalStream#removeTrack removeTrack()} to enable/disable video.

```javascript
// disable camera
const videoTrack = localStream.getVideoTrack();
if (videoTrack) {
  await localStream.removeTrack(videoTrack)
  // stop camera capture.
  videoTrack.stop();
}

// enable camera
const videoStream = TRTC.createStream({ userId, audio: false, video: true });
// get a new video track.
await videoStream.initialize();
await localStream.addTrack(videoStream.getVideoTrack());
```

> !
>
> - You cannot remove audio through {@link LocalStream#removeTrack removeTrack()}. To disable audio, please read on.

Use {@link LocalStream#replaceTrack replaceTrack()} to enable/disable audio.

**Sample Code:**

```javascript
// disable microphone
localStream.muteAudio();
const audioTrack = localStream.getAudioTrack();
if (audioTrack) {
  // stop microphone capture
  audioTrack.stop();
}

// enable microphone
const stream = TRTC.createStream({ audio: true, video:false });
await stream.initialize();
localStream.unmuteAudio();
// replace the old audio track with the new one.
await localStream.replaceTrack(stream.getAudioTrack());
```

- Advantages: Camera/microphone will be stopped when you disable camera/microphone, and the lights of device will not continue to be on.
- Disadvantages: The compatibility of the enabling/disabling microphone is slightly worse than that of Option one. Please refer to the description of compatibility in {@link LocalStream#replaceTrack replaceTrack()}.