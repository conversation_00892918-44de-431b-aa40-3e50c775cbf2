<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>RemoteStream - Documentation</title>
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>  
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav>
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Namespaces</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="TRTC.html#.VERSION">VERSION</a></li>
          </ul>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#checkSystemRequirements">checkSystemRequirements</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#isScreenShareSupported">isScreenShareSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#isSmallStreamSupported">isSmallStreamSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getDevices">getDevices</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getCameras">getCameras</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getMicrophones">getMicrophones</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getSpeakers">getSpeakers</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#createClient">createClient</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#createStream">createStream</a></li>
          </ul>
        </li>
        <li><a href="TRTC.Logger.html">TRTC.Logger</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="TRTC.Logger.html#.LogLevel">LogLevel</a></li>
          </ul>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.enableUploadLog">enableUploadLog</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.disableUploadLog">disableUploadLog</a></li>
          </ul>
        </li>
      </ul>
      <h3>Classes</h3>
      <ul>
        <li><a href="Client.html">Client</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="Client.html#setProxyServer">setProxyServer</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setTurnServer">setTurnServer</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#join">join</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#leave">leave</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#publish">publish</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#unpublish">unpublish</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#subscribe">subscribe</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#unsubscribe">unsubscribe</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteMutedState">getRemoteMutedState</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getTransportStats">getTransportStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getLocalAudioStats">getLocalAudioStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getLocalVideoStats">getLocalVideoStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteAudioStats">getRemoteAudioStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteVideoStats">getRemoteVideoStats</a></li>
            
            
            
            
            <li data-type='method' style='display: none;'><a href="Client.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#enableSmallStream">enableSmallStream</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#disableSmallStream">disableSmallStream</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setSmallStreamProfile">setSmallStreamProfile</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setRemoteVideoStreamType">setRemoteVideoStreamType</a></li>
          </ul>
        </li>
        <li><a href="Stream.html">Stream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="Stream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#setAudioVolume">setAudioVolume</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="LocalStream.html">LocalStream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#initialize">initialize</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setAudioProfile">setAudioProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setVideoProfile">setVideoProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setScreenProfile">setScreenProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setVideoContentHint">setVideoContentHint</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#switchDevice">switchDevice</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#addTrack">addTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#removeTrack">removeTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#replaceTrack">replaceTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="RemoteStream.html">RemoteStream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getType">getType</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#setAudioVolume">setAudioVolume</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="RtcError.html#getCode">getCode</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-ClientEvent.html">ClientEvent</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_ADDED">STREAM_ADDED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_REMOVED">STREAM_REMOVED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_UPDATED">STREAM_UPDATED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_SUBSCRIBED">STREAM_SUBSCRIBED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.PEER_JOIN">PEER_JOIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.PEER_LEAVE">PEER_LEAVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.MUTE_AUDIO">MUTE_AUDIO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.MUTE_VIDEO">MUTE_VIDEO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.UNMUTE_AUDIO">UNMUTE_AUDIO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.UNMUTE_VIDEO">UNMUTE_VIDEO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.CLIENT_BANNED">CLIENT_BANNED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.ERROR">ERROR</a></li>
          </ul>
        </li>
        <li><a href="module-StreamEvent.html">StreamEvent</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.PLAYER_STATE_CHANGED">PLAYER_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.SCREEN_SHARING_STOPPED">SCREEN_SHARING_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.ERROR">ERROR</a></li>
          </ul>
        </li>
        <li><a href="module-ErrorCode.html">ErrorCode</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.NOT_SUPPORTED">NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DEVICE_NOT_FOUND">DEVICE_NOT_FOUND</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_SETUP_FAILED">SIGNAL_CHANNEL_SETUP_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_ERROR">SIGNAL_CHANNEL_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.ICE_TRANSPORT_ERROR">ICE_TRANSPORT_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.JOIN_ROOM_FAILED">JOIN_ROOM_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.CREATE_OFFER_FAILED">CREATE_OFFER_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_RECONNECTION_FAILED">SIGNAL_CHANNEL_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.UPLINK_RECONNECTION_FAILED">UPLINK_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DOWNLINK_RECONNECTION_FAILED">DOWNLINK_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.CLIENT_BANNED">CLIENT_BANNED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SERVER_TIMEOUT">SERVER_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SUBSCRIPTION_TIMEOUT">SUBSCRIPTION_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.PLAY_NOT_ALLOWED">PLAY_NOT_ALLOWED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DEVICE_AUTO_RECOVER_FAILED">DEVICE_AUTO_RECOVER_FAILED</a></li>
            
            
            
            
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.NOT_SUPPORTED_H264">NOT_SUPPORTED_H264</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SWITCH_ROLE_FAILED">SWITCH_ROLE_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.API_CALL_TIMEOUT">API_CALL_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.UNKNOWN">UNKNOWN</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">SDK Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues and Solutions</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Maximum Number of Upstream Users in a Room</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Basic Audio/Video Call</a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Cameras/Mics</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Local Video Properties</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Dynamically Enabling/Disabling Local Audio/Video</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detecting Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Playback Rendering</a></li>
        
        <li><a href="tutorial-22-advanced-audio-mixer.html">Adding Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Environment and Device Check Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Network Quality Check Before Calls</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Device Change Check</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enabling Dual-Stream Mode</a></li>
        <li><a href="tutorial-28-advanced-beauty.html">Enabling Beauty Filters</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enabling Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Communication</a></li></ul>
        <ul><li style="margin:10px 0 5px 0">Best Practices</li>
        <li><a href="tutorial-21-advanced-auto-play-policy.html">Suggested Solutions for Autoplay Restrictions</a></li>
        <li><a href="tutorial-33-advanced-electron-screen-share.html">Electron uses TRTC Web SDK for screen sharing</a></li>
        <li><a href="tutorial-34-advanced-proxy.html">Using proxy to handle firewall restrictions</a></li></ul>
        
        
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">RemoteStream</h1>
      <section>
        <header style="display:none">
          <h2>
            RemoteStream
          </h2>
        </header>
        <article>
          <div class="container-overview">
            <h4 class="name" style="display:none" id="RemoteStream"><span class="type-signature"></span>new RemoteStream<span class="signature">()</span><span class="type-signature"></span></h4>
            <div class="description">
              <p>远端音视频流，通过监听 <code>Client.on('stream-added')</code> 事件获得。<br>
                <br>
                一个远端用户可发送至多两个远端流对象，其中一个远端流对象包含一路音频和一路主路视频，另一个远端流对象仅仅包含一路辅路视频。<br>
                这两种远端流对象的类型可通过 <a href="RemoteStream.html#getType">getType()</a> 来识别。<br>
                <br>
                远端流的生命周期在事件 <code>Client.on('stream-added')</code> 到事件 <code>Client.on('stream-removed')</code> 之间，在这两个事件之间，远端流可能会通过
                <code>Client.on('stream-updated')</code> 进行更新，比如远端用户通过 <a href="LocalStream.html#addTrack">addTrack()</a> 或 <a href="LocalStream.html#removeTrack">removeTrack()</a>
                增加或删除 track 后本地就会收到此通知。
              </p>
            </div>
            <dl class="details">
            </dl>
            <h5>Example</h5>
            <pre class="highlight lang-javascript"><code>// 远端流生命周期内的操作示例
// 通过监听‘stream-added’事件获得远端流对象
const client = TRTC.createClient({ sdkAppId, userId, userSig, mode: 'live'});
client.on('stream-added', event => {
  const remoteStream = event.stream;
  const remoteUserId = remoteStream.getUserId();
  console.log('received a remoteStream ID: ' + remoteStream.getId() + ' from user: ' + remoteUserId);
  // 若需要观看该远端流，则需要订阅它
  client.subscribe(remoteStream);
  // 若不需要观看该远端流，请取消订阅它，否则SDK会接收远端流数据。
  // client.unsubscribe(remoteStream);
});
// 监听‘stream-removed’事件
client.on('stream-removed', event => {
  const remoteStream = event.stream;
  console.log('remoteStream ID: ' + remoteStream.getId() + ' has been removed');
  // 停止播放并删除相应&lt;video>标签
});
// 监听‘stream-updated’事件
client.on('stream-updated', event => {
  const remoteStream = event.stream;
  console.log('remoteStream ID: ' + remoteStream.getId() + ' was updated hasAudio: '
              + remoteStream.hasAudio() + ' hasVideo: ' + remoteStream.hasVideo());
});
// 监听‘stream-subscribed’事件
client.on('stream-subscribed', event => {
  const remoteStream = event.stream;
  // 远端流订阅成功，在HTML页面中创建一个&lt;video>标签，假设该标签ID为‘remote-video-view’
  // 播放该远端流
  remoteStream.play('remote-video-view');
});</code></pre>
          </div>
          <h3 class="subsection-title">Extends</h3>
          <ul>
            <li><a href="Stream.html">Stream</a></li>
          </ul>
          <h3 class="subsection-title">Methods</h3>
          <h4 class="name" id="getType"><span class="type-signature"></span>getType<span class="signature">()</span><span class="type-signature"> &rarr; {string}</span></h4>
          <div class="description">
            <p>Get the type of a remote stream<br>
              <br>
              <strong>Note:</strong>
            </p>
            <ul>
              <li>The TRTC SDK for web does not support publishing substreams, and screen sharing streams are published as primary streams. Therefore, if a remote screen sharing stream is from a browser, this API will return <code>main</code>.</li>
              <li>The TRTC SDK for native applications support publishing substreams, and screen sharing streams are published as substreams. Therefore, if a remote screen sharing stream is from a native application, this API will return <code>auxiliary</code>.</li>
              <li>For details, please see Screen Sharing.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Type of the remote stream</p>
            <ul>
              <li><code>main</code>: primary audio/video stream</li>
              <li><code>auxiliary</code>: substream, which is often a screen sharing stre</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">string</span>
            </dd>
          </dl>
          <h4 class="name" id="play"><span class="type-signature">(async) </span>play<span class="signature">(elementId, options<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Play the audio/video stream<br>
              This API creates an <audio> and <video> tag and plays audio and video on them. The tags will be added to the div container named <code>elementId</code> on the webpage.<br>
                  In short, an audio player and video player will be created automatically to play the stream’s audio and video.
                  <strong>NOTE</strong></p>
            <ul>
              <li>Due to browsers’ autoplay policy, a PLAY_NOT_ALLOWED error may be returned when you call this API.
                In that case, display a window on the UI and, in the callback for the window’s clicking event, call resume() to resume audio/video playback.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#play">Stream#play</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// For versions earlier than v4.8.4, capture and handle the 0x4043 error as follows.
stream.play('remote').then(() => {
  // autoplay success
}).catch((error) => {
  const errorCode = error.getCode();
  if (errorCode === 0x4043) {
    // If the `PLAY_NOT_ALLOWED` error occurs, display a window on the UI and, in the callback for the window’s clicking event, call `stream.resume` to resume audio/video playback.
    // stream.resume()
  }
});
// For v4.8.4 and later versions, we strongly recommend that you use the error callback of `Stream` to capture and handle the 0x4043 error.
stream.play('remote').catch(error => {});
stream.on('error', error => {
  const errorCode = error.getCode();
  if (errorCode === 0x4043) {
    // If the `PLAY_NOT_ALLOWED` error occurs, display a window on the UI and, in the callback for the window’s clicking event, call `stream.resume` to resume audio/video playback.
    // stream.resume()
  }
})</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>elementId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                  |
                  <span class="param-type">HTMLDivElement</span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>HTML &lt;div&gt; stag ID or <code>HTMLDivElement</code> object in DOM. This method will create video/audio tag in elementId to play stream.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>Playback options</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>objectFit</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="description last">
                          <p>Video fill mode. For details, see the <a href="https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit">CSS object-fit</a> property.</p>
                          <ul>
                            <li>'contain': The video image is scaled as large as its long side can go. The blank area is filled with black bars. This mode ensures that the image is displayed in whole.</li>
                            <li>'cover': The video image is scaled as large as the short side can go. The image fills the entire screen, but may be cropped.</li>
                            <li>'fill': Ensures that the viewport is filled and the video content is displayed in its entirety, but does not guarantee that the video size remains proportional. The width and height of the video will be stretched to match the size of the viewport. (This option value is supported since v4.12.1)</li>
                          </ul>
                          <p>The <code>cover</code> mode is used by default to play the video stream and the <code>contain</code> mode is used by default to the screen sharing stream.</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>muted</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether muting is required.<br></p>
                          <ul>
                            <li>For local streams, muted defaults to true, preventing playback of sounds captured from the microphone.</li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>mirror</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="description last">
                          <p>Whether to enable video mirroring preview. (This option is supported since v4.12.1)<strong>Note</strong></p>
                          <ul>
                            <li>For local video stream, mirror preview is enabled by default. It is recommended to enable mirroring when using the front camera and disable mirroring when using the rear camera.</li>
                            <li>For remote video stream, mirror preview is disabled by default.</li>
                            <li>For screen sharing streams, enabling mirror preview is not supported.</li>
                            <li>This Property is only valid for local preview, pushing the stream is no mirroring effect.</li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <span class="param-type"><a href="RtcError.html">RtcError</a></span>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="stop"><span class="type-signature"></span>stop<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Stop playing an audio/video stream
              <br>
              <br>
              This API will also delete the audio and video tags created via <a href="Stream.html#play">play()</a> from the HTML page.
            </p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#stop">Stream#stop</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h4 class="name" id="resume"><span class="type-signature">(async) </span>resume<span class="signature">()</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Resume playing an audio/video stream
              <br>
              <strong>NOTE</strong>
            </p>
            <ul>
              <li>On some browsers, moving the div container passed in by play() may cause the audio and video players to pause.
                In that case, you need to call this API to resume playback.</li>
              <li>If play() returns the PLAY_NOT_ALLOWED error due to the browser’s autoplay policy, display a window on the UI and, in the callback for the window’s clicking event,
                call this API to resume playback.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#resume">Stream#resume</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>stream.on('player-state-changed', event => {
  if (event.state === 'PAUSED') {
    // resume audio/video playback
    stream.resume();
  }
});</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <span class="param-type"><a href="RtcError.html">RtcError</a></span>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="close"><span class="type-signature"></span>close<span class="signature">()</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Close an audio/video stream<br>
              <br>
              For local streams, this API will turn the camera off and release the camera and mic.
            </p>
          </div>
          <dl class="details">
            <dt class="tag-overrides">Overrides:</dt>
            <dd class="tag-overrides">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#close">Stream#close</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h4 class="name" id="muteAudio"><span class="type-signature"></span>muteAudio<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Disable the audio track of a stream and keep the audio track, usually for temporary muting of local streams.<br>
              <br>
            </p>
            <ul>
              <li>For local streams, this API will stop audio sending, and remote users will receive the Client.on('mute-audio') callback.</li>
              <li>For remote streams, after this API is called, the local user will stop playing the remote user’s audio but will continue to receive audio data from the user.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#muteAudio">Stream#muteAudio</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <ul>
              <li><code>true</code>: The audio track is disabled successfully.</li>
              <li><code>false</code>: Failed to disable the audio track as it does not exist</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="muteVideo"><span class="type-signature"></span>muteVideo<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Disable the video track of a stream<br>
              <br>
            </p>
            <ul>
              <li>For local streams, this API will stop video sending, and remote users will receive the Client.on('mute-video') callback.
                If video is captured from the camera, the camera will not be turned off by this API.
                To turn the camera off, call removeTrack() to remove the video track and then.
                MediaStreamTrack.stop()
                to disable video (turn the camera off).</li>
              <li>For remote streams, after this API is called, the local user will stop playing the remote user’s video but will continue to receive video data from the user.
                If you do not want to receive video data, use Client.unsubscribe to unsubscribe, or Client.subscribe to subscribe to video only.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#muteVideo">Stream#muteVideo</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <ul>
              <li><code>true</code>: The video track is disabled successfully.</li>
              <li><code>false</code>: Failed to disable the video track as it does not exist.</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="unmuteAudio"><span class="type-signature"></span>unmuteAudio<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Enable the audio track of a stream<br>
              <br>
              For local streams, this API will trigger the <code>Client.on('unmute-audio')</code> callback for remote users.<br>
              <br>
              The audio track is enabled by default. If it is disabled via <a href="Stream.html#muteAudio">muteAudio()</a>, you can call this API to enable the audio track again.
            </p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#unmuteAudio">Stream#unmuteAudio</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <ul>
              <li><code>true</code>: The audio track is enabled successfully.</li>
              <li><code>false</code>: Failed to enable the audio track as it does not exist.</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="unmuteVideo"><span class="type-signature"></span>unmuteVideo<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Enable the video track of a stream<br>
              <br>
              For local streams, this API will trigger the <code>Client.on('unmute-video')</code> callback for remote users.<br>
              <br>
              The video track is enabled by default. If it is disabled via <a href="Stream.html#muteVideo">muteVideo()</a>, you can call this API to enable the video track again.
            </p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#unmuteVideo">Stream#unmuteVideo</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <ul>
              <li><code>true</code>: The video track is enabled successfully.</li>
              <li><code>false</code>: Failed to enable the video track as it does not exist.</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="getId"><span class="type-signature"></span>getId<span class="signature">()</span><span class="type-signature"> &rarr; {string}</span></h4>
          <div class="description">
            <p>Get the unique ID of a stream</p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getId">Stream#getId</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p><code>Id</code>: unique ID of the stream</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">string</span>
            </dd>
          </dl>
          <h4 class="name" id="getUserId"><span class="type-signature"></span>getUserId<span class="signature">()</span><span class="type-signature"> &rarr; {string}</span></h4>
          <div class="description">
            <p>Get the ID of the user to whom a stream belongs</p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getUserId">Stream#getUserId</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p><code>userId</code>: user ID</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">string</span>
            </dd>
          </dl>
          <h4 class="name" id="setAudioOutput"><span class="type-signature">(async) </span>setAudioOutput<span class="signature">(deviceId)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set the audio output device</p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#setAudioOutput">Stream#setAudioOutput</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>deviceId</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>Device ID, which can be obtained via <a href="TRTC.html#getSpeakers">getSpeakers()</a></p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="setAudioVolume"><span class="type-signature"></span>setAudioVolume<span class="signature">(volume)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set the playback volume<br></p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#setAudioVolume">Stream#setAudioVolume</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>volume</code></td>
                <td class="type">
                  <span class="param-type">double</span>
                </td>
                <td class="description last">
                  <p>Volume. Value range: 0.0-1.0. <code>0.0</code> means to mute playback, and <code>1.0</code> means to use the highest volume.</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="getAudioLevel"><span class="type-signature"></span>getAudioLevel<span class="signature">()</span><span class="type-signature"> &rarr; {number}</span></h4>
          <div class="description">
            <p>Get the current volume<br>
              This API works only if there is audio data in the local stream or a remote stream. Before calling this API, you need to <a href="Stream.html#play">play</a> the stream first.</p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getAudioLevel">Stream#getAudioLevel</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>setInterval(() => {
  const level = stream.getAudioLevel();
  if (level >= 0.1) {
    console.log(`user ${stream.getUserId()} is speaking`);
  }
}, 200);</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p><code>audioLevel</code>: volume</p>
            <ul>
              <li>Value range: 0.0-1.0. Generally, a user is considered to be speaking if the value is greater than 0.</li>
            </ul>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">number</span>
            </dd>
          </dl>
          <h4 class="name" id="hasAudio"><span class="type-signature"></span>hasAudio<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Get whether a stream has an audio track<br></p>
            <ul>
              <li>If you need to get the <code>Stream mute</code> state, you need to listen to the <code>Client.on('mute-audio')</code> event for further processing.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#hasAudio">Stream#hasAudio</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="hasVideo"><span class="type-signature"></span>hasVideo<span class="signature">()</span><span class="type-signature"> &rarr; {boolean}</span></h4>
          <div class="description">
            <p>Get whether a stream has a video track<br></p>
            <ul>
              <li>If you need to get the <code>Stream mute</code> state, you need to listen to the <code>Client.on('mute-video')</code> event for further processing.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#hasVideo">Stream#hasVideo</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">boolean</span>
            </dd>
          </dl>
          <h4 class="name" id="getAudioTrack"><span class="type-signature"></span>getAudioTrack<span class="signature">()</span><span class="type-signature"> &rarr; {MediaStreamTrack}</span></h4>
          <div class="description">
            <p>Get the audio track of a stream</p>
          </div>
          <dl class="details">
            <dt class="tag-overrides">Overrides:</dt>
            <dd class="tag-overrides">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getAudioTrack">Stream#getAudioTrack</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Audio tra</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
            </dd>
          </dl>
          <h4 class="name" id="getVideoTrack"><span class="type-signature"></span>getVideoTrack<span class="signature">()</span><span class="type-signature"> &rarr; {MediaStreamTrack}</span></h4>
          <div class="description">
            <p>Get the video track of a stream</p>
          </div>
          <dl class="details">
            <dt class="tag-overrides">Overrides:</dt>
            <dd class="tag-overrides">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getVideoTrack">Stream#getVideoTrack</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Video tra</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">MediaStreamTrack</span>
            </dd>
          </dl>
          <h4 class="name" id="getVideoFrame"><span class="type-signature"></span>getVideoFrame<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>
          <div class="description">
            <p>Get the current video frame
              <br>
              <strong>NOTE</strong>
            </p>
            <ul>
              <li>This API works only if it is called after play() and the stream contains video.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#getVideoFrame">Stream#getVideoFrame</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Get the current video frame
const frame = stream.getVideoFrame();
if (frame) {
  const img = document.createElement('img');
  img.src = frame;
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p><a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URIs">dataURL</a> of 'image/png' type</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">String</span>
            </dd>
          </dl>
          <h4 class="name" id="on"><span class="type-signature"></span>on<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Listen for <code>Stream</code> events<br>
              For the detailed event list, please see StreamEvent.</p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#on">Stream#on</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>function onPlayerStateChange(event) {
   console.log(`${event.type} player is ${event.state}`);
}
stream.on('player-state-changed', onPlayerStateChange);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>Event name</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <p>Event handling method</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <p>Context object</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="off"><span class="type-signature"></span>off<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Stop listening for <code>Stream</code> events<br></p>
          </div>
          <dl class="details">
            <dt class="inherited-from">Inherited From:</dt>
            <dd class="inherited-from">
              <ul class="dummy">
                <li>
                  <a href="Stream.html#off">Stream#off</a>
                </li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>function onPlayerStateChange(event) {
   console.log(`${event.type} player is ${event.state}`);
}
stream.on('player-state-changed', onPlayerStateChange);
stream.off('player-state-changed', onPlayerStateChange);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>Event name. If the wildcard '*' is passed in, all bound events will be unbound.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <p>Event handling method</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <p>Context object</p>
                </td>
              </tr>
            </tbody>
          </table>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Wed Jun 29 2022 17:42:53 GMT+0800 (中国标准时间) using the <a href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
      hljs.highlightAll({
        cssSelector: 'highlight'
      });
      hljs.initLineNumbersOnLoad();
    </script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>