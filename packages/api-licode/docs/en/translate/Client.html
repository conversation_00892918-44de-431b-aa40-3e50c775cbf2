<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Client - Documentation</title>
    <script src="scripts/highlight/highlight.min.js"></script>
    <script src="scripts/highlight/highlightjs-line-numbers.min.js"></script>

    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/highlight/rainbow.min.css">
    <link type="text/css" rel="stylesheet" href="styles/highlight/highlight.min.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
    <script>
      const aegis = new Aegis({
        id: 'iHWefAYqtqiqBrECeQ',
        uin: '',
        reportApiSpeed: true,
        reportAssetSpeed: true,
        hostUrl: 'https://tamaegis.com',
      })
    </script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
  </head>
  <body>
    <input type="checkbox" id="nav-trigger" class="nav-trigger" />
    <label for="nav-trigger" class="navicon-button x">
      <div class="navicon"></div>
    </label>
    <label for="nav-trigger" class="overlay"></label>
    <nav>
      <input type="text" id="nav-search" placeholder="Search" />
      <h2><a href="index.html">Home</a></h2>
      <h3>Namespaces</h3>
      <ul>
        <li><a href="TRTC.html">TRTC</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="TRTC.html#.VERSION">VERSION</a></li>
          </ul>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.html#checkSystemRequirements">checkSystemRequirements</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#isScreenShareSupported">isScreenShareSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#isSmallStreamSupported">isSmallStreamSupported</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getDevices">getDevices</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getCameras">getCameras</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getMicrophones">getMicrophones</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#getSpeakers">getSpeakers</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#createClient">createClient</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.html#createStream">createStream</a></li>
          </ul>
        </li>
        <li><a href="TRTC.Logger.html">TRTC.Logger</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="TRTC.Logger.html#.LogLevel">LogLevel</a></li>
          </ul>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.setLogLevel">setLogLevel</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.enableUploadLog">enableUploadLog</a></li>
            <li data-type='method' style='display: none;'><a href="TRTC.Logger.html#.disableUploadLog">disableUploadLog</a></li>
          </ul>
        </li>
      </ul>
      <h3>Classes</h3>
      <ul>
        <li><a href="Client.html">Client</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="Client.html#setProxyServer">setProxyServer</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setTurnServer">setTurnServer</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#join">join</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#leave">leave</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#publish">publish</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#unpublish">unpublish</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#subscribe">subscribe</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#unsubscribe">unsubscribe</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#switchRole">switchRole</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#off">off</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteMutedState">getRemoteMutedState</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getTransportStats">getTransportStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getLocalAudioStats">getLocalAudioStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getLocalVideoStats">getLocalVideoStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteAudioStats">getRemoteAudioStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#getRemoteVideoStats">getRemoteVideoStats</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#enableAudioVolumeEvaluation">enableAudioVolumeEvaluation</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#enableSmallStream">enableSmallStream</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#disableSmallStream">disableSmallStream</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setSmallStreamProfile">setSmallStreamProfile</a></li>
            <li data-type='method' style='display: none;'><a href="Client.html#setRemoteVideoStreamType">setRemoteVideoStreamType</a></li>
          </ul>
        </li>
        <li><a href="Stream.html">Stream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="Stream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#setAudioVolume">setAudioVolume</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="Stream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="LocalStream.html">LocalStream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#initialize">initialize</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setAudioProfile">setAudioProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setVideoProfile">setVideoProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setScreenProfile">setScreenProfile</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setVideoContentHint">setVideoContentHint</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#switchDevice">switchDevice</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#addTrack">addTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#removeTrack">removeTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#replaceTrack">replaceTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="LocalStream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="RemoteStream.html">RemoteStream</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getType">getType</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#play">play</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#stop">stop</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#resume">resume</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#close">close</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#muteAudio">muteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#muteVideo">muteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#unmuteAudio">unmuteAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#unmuteVideo">unmuteVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getId">getId</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getUserId">getUserId</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#setAudioOutput">setAudioOutput</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#setAudioVolume">setAudioVolume</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getAudioLevel">getAudioLevel</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#hasAudio">hasAudio</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#hasVideo">hasVideo</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getAudioTrack">getAudioTrack</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getVideoTrack">getVideoTrack</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#getVideoFrame">getVideoFrame</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#on">on</a></li>
            <li data-type='method' style='display: none;'><a href="RemoteStream.html#off">off</a></li>
          </ul>
        </li>
        <li><a href="RtcError.html">RtcError</a>
          <ul class='methods'>
            <li data-type='method' style='display: none;'><a href="RtcError.html#getCode">getCode</a></li>
          </ul>
        </li>
      </ul>
      <h3>Modules</h3>
      <ul>
        <li><a href="module-ClientEvent.html">ClientEvent</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_ADDED">STREAM_ADDED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_REMOVED">STREAM_REMOVED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_UPDATED">STREAM_UPDATED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.STREAM_SUBSCRIBED">STREAM_SUBSCRIBED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.PEER_JOIN">PEER_JOIN</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.PEER_LEAVE">PEER_LEAVE</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.MUTE_AUDIO">MUTE_AUDIO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.MUTE_VIDEO">MUTE_VIDEO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.UNMUTE_AUDIO">UNMUTE_AUDIO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.UNMUTE_VIDEO">UNMUTE_VIDEO</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.CLIENT_BANNED">CLIENT_BANNED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.NETWORK_QUALITY">NETWORK_QUALITY</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.AUDIO_VOLUME">AUDIO_VOLUME</a></li>
            <li data-type='member' style='display: none;'><a href="module-ClientEvent.html#.ERROR">ERROR</a></li>
          </ul>
        </li>
        <li><a href="module-StreamEvent.html">StreamEvent</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.PLAYER_STATE_CHANGED">PLAYER_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.SCREEN_SHARING_STOPPED">SCREEN_SHARING_STOPPED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.CONNECTION_STATE_CHANGED">CONNECTION_STATE_CHANGED</a></li>
            <li data-type='member' style='display: none;'><a href="module-StreamEvent.html#.ERROR">ERROR</a></li>
          </ul>
        </li>
        <li><a href="module-ErrorCode.html">ErrorCode</a>
          <ul class='members'>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.INVALID_PARAMETER">INVALID_PARAMETER</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.INVALID_OPERATION">INVALID_OPERATION</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.NOT_SUPPORTED">NOT_SUPPORTED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DEVICE_NOT_FOUND">DEVICE_NOT_FOUND</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.INITIALIZE_FAILED">INITIALIZE_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_SETUP_FAILED">SIGNAL_CHANNEL_SETUP_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_ERROR">SIGNAL_CHANNEL_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.ICE_TRANSPORT_ERROR">ICE_TRANSPORT_ERROR</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.JOIN_ROOM_FAILED">JOIN_ROOM_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.CREATE_OFFER_FAILED">CREATE_OFFER_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SIGNAL_CHANNEL_RECONNECTION_FAILED">SIGNAL_CHANNEL_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.UPLINK_RECONNECTION_FAILED">UPLINK_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DOWNLINK_RECONNECTION_FAILED">DOWNLINK_RECONNECTION_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.REMOTE_STREAM_NOT_EXIST">REMOTE_STREAM_NOT_EXIST</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.CLIENT_BANNED">CLIENT_BANNED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SERVER_TIMEOUT">SERVER_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SUBSCRIPTION_TIMEOUT">SUBSCRIPTION_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.PLAY_NOT_ALLOWED">PLAY_NOT_ALLOWED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.DEVICE_AUTO_RECOVER_FAILED">DEVICE_AUTO_RECOVER_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.NOT_SUPPORTED_H264">NOT_SUPPORTED_H264</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.SWITCH_ROLE_FAILED">SWITCH_ROLE_FAILED</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.API_CALL_TIMEOUT">API_CALL_TIMEOUT</a></li>
            <li data-type='member' style='display: none;'><a href="module-ErrorCode.html#.UNKNOWN">UNKNOWN</a></li>
          </ul>
        </li>
      </ul>
      <h3>Tutorials</h3>
      <ul>
        <ul><li style="margin:10px 0 5px 0">Related Information</li><li><a href="tutorial-00-info-update-guideline.html">SDK Upgrade Guide</a></li>
        <li><a href="tutorial-01-info-changelog.html">SDK Changelog</a></li>
        <li><a href="tutorial-02-info-webrtc-issues.html">WebRTC Known Issues and Solutions</a></li>
        <li><a href="tutorial-03-info-error-code-tips.html">Error Codes and Solutions</a></li>
        <li><a href="tutorial-04-info-uplink-limits.html">Maximum Number of Upstream Users in a Room</a></li>
        <li><a href="tutorial-05-info-browser.html">Browsers Supported</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Basic Tutorials</li><li><a href="tutorial-10-basic-get-started-with-demo.html">Demo Quick Run</a></li>
        <li><a href="tutorial-11-basic-video-call.html">Basic Audio/Video Call</a></li>
        <li><a href="tutorial-12-basic-live-video.html">Interactive Live Streaming</a></li>
        <li><a href="tutorial-13-basic-switch-camera-mic.html">Switching Cameras/Mics</a></li>
        <li><a href="tutorial-14-basic-set-video-profile.html">Setting Local Video Properties</a></li>
        <li><a href="tutorial-15-basic-dynamic-add-video.html">Dynamically Enabling/Disabling Local Audio/Video</a></li>
        <li><a href="tutorial-16-basic-screencast.html">Screen Sharing</a></li>
        <li><a href="tutorial-17-basic-detect-volume.html">Detecting Volume</a></li>
        </ul><ul><li style="margin:10px 0 5px 0">Advanced Tutorials</li><li><a href="tutorial-20-advanced-customized-capture-rendering.html">Custom Capturing and Playback Rendering</a></li>

        <li><a href="tutorial-22-advanced-audio-mixer.html">Adding Music and Audio Effects</a></li>
        <li><a href="tutorial-23-advanced-support-detection.html">Environment and Device Check Before Calls</a></li>
        <li><a href="tutorial-24-advanced-network-quality.html">Network Quality Check Before Calls</a></li>
        <li><a href="tutorial-25-advanced-device-change.html">Device Plugging/Unplugging Check</a></li>
        <li><a href="tutorial-26-advanced-publish-cdn-stream.html">Publishing to CDN</a></li>
        <li><a href="tutorial-27-advanced-small-stream.html">Enabling Dual-Channel Mode</a></li>
        <li><a href="tutorial-28-advanced-beauty.html">Enabling Beauty Filters</a></li>
        <li><a href="tutorial-29-advance-water-mark.html">Enabling Watermarking</a></li>
        <li><a href="tutorial-30-advanced-cross-room-link.html">Cross-Room Link</a></li></ul><ul><li style="margin:10px 0 5px 0">Best Practices</li><li><a href="tutorial-21-advanced-auto-play-policy.html">Suggested Solutions for Autoplay Restrictions</a></li></ul>
      </ul>
      <h3><a href="global.html">Global</a></h3>
    </nav>
    <div id="main">
      <h1 class="page-title">Client</h1>
      <section>
        <header style="display:none">
          <h2>
            Client
          </h2>
        </header>
        <article>
          <div class="container-overview">
            <h4 class="name" style="display:none" id="Client"><span class="type-signature"></span>new Client<span class="signature">()</span><span class="type-signature"></span></h4>
            <div class="description">
              <p>An audio/video call client object is created via <a href="TRTC.html#createClient">createClient()</a> and it represents an audio/video call.<br>
                A client object provides the core features of the TRTC SDK for Web, including:</p>
              <ul>
                <li>Entering a room <a href="Client.html#join">join()</a></li>
                <li>Leaving a room <a href="Client.html#leave">leave()</a></li>
                <li>Publishing a local stream <a href="Client.html#publish">publish()</a></li>
                <li>Unpublishing a local stream <a href="Client.html#unpublish">unpublish()</a></li>
                <li>Subscribing to a remote stream <a href="Client.html#subscribe">subscribe()</a></li>
                <li>Unsubscribing from a remote stream <a href="Client.html#unsubscribe">unsubscribe()</a></li>
              </ul>
            </div>
            <dl class="details">
            </dl>
          </div>
          <h3 class="subsection-title">Methods</h3>
          <h4 class="name" id="setProxyServer"><span class="type-signature"></span>setProxyServer<span class="signature">(url)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set a proxy server.
              <br>
              This API is suitable where you deploy proxy servers, e.g., Nginx+Coturn, by yourself.
            </p>
            <br>
            Note:
            <ul>
              <li>This API works only if it is called before <a href="Client.html#join">join()</a>.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.setProxyServer('wss://proxy.example.com:443');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>url</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>WebSocket proxy server address, for example, wss://proxy.example.com:443</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="setTurnServer"><span class="type-signature"></span>setTurnServer<span class="signature">(options)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set TURN servers.
              <br>
              This API is used along with <a href="Client.html#setProxyServer">setProxyServer()</a> and is suitable where you deploy proxy and TURN servers by yourself.
            </p>
            <br>
            Note:
            <ul>
              <li>This API works only if it is called before <a href="Client.html#join">join()</a>.</li>
              <li>For versions earlier than v4.8.5, this API can be used to set only one TURN server, and a value of the Object type must be passed in.</li>
              <li>For v4.8.5 and later versions, this API can be used to set multiple TURN servers, and a value of the Array type must be passed in.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// For versions earlier than v4.8.5, this API can be used to set only one TURN server
client.setTurnServer({ url: '*************:3478', username: 'bob', credential: 'bobspassword', credentialType: 'password' });
// For v4.8.5 and later versions, this API can be used to set multiple TURN servers
client.setTurnServer([{ url: '*************:3478', username: 'bob', credential: 'bobspassword', credentialType: 'password' }]);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">array</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>config</code></td>
                        <td class="type">
                          <span class="param-type">object</span>
                        </td>
                        <td class="description last">
                          <p>TURN server configuration items</p>
                          <h6>Properties</h6>
                          <table class="params">
                            <thead>
                              <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Attributes</th>
                                <th class="last">Description</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td class="name"><code>url</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="attributes">
                                </td>
                                <td class="description last">
                                  <p>TURN server URL</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>username</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="attributes">
                                  &lt;optional><br>
                                </td>
                                <td class="description last">
                                  <p>TURN server verification username</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>credential</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="attributes">
                                  &lt;optional><br>
                                </td>
                                <td class="description last">
                                  <p>TURN server verification password</p>
                                </td>
                              </tr>
                              <tr>
                                <td class="name"><code>credentialType</code></td>
                                <td class="type">
                                  <span class="param-type">string</span>
                                </td>
                                <td class="attributes">
                                  &lt;optional><br>
                                </td>
                                <td class="description last">
                                  <p>Type of the TURN server verification password. The default value is `password`
                                    <br>
                                    <a href="https://developer.mozilla.org/en-US/docs/Web/API/RTCIceServer/credentialType">credentialType reference</a>
                                  </p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="join"><span class="type-signature">(async) </span>join<span class="signature">(options)</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Enter an audio/video call room.<br>
              Room entering indicates the start of an audio/video call. After an audio/video call starts, the SDK listens for the room entering and leaving events of remote users. If a remote user enters the room and publishes a stream,<br>
              the local user will receive a 'stream-added' event notification. After entering a room, a user can use <a href="Client.html#publish">publish()</a> to publish a local stream. After the local stream is successfully published, the remote user can receive the corresponding 'stream-added' event notification. In this way, a two-way audio/video call connection is completed.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const client = TRTC.createClient({ mode: 'live', sdkAppId, userId, userSig });
client.join({ roomId: 8888, role: 'anchor' }).then(() => {
  // join room success
}).catch(error => {
  console.error('Join room failed: ' + error);
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="description last">
                  <p>Room entry parameters</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Attributes</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>roomId</code></td>
                        <td class="type">
                          <span class="param-type">number</span>
                          |
                          <span class="param-type">string</span>
                        </td>
                        <td class="attributes">
                        </td>
                        <td class="description last">
                          <p>The room ID is of the number type by default. To use a room ID of the string type, set the `useStringRoomId` parameter to `true` in <a href="TRTC.html#createClient">createClient()</a>.<br>
                            If `roomId` is of the number type, the value must be an integer in the range of [1, 4294967294].<br>
                            If `roomId` is of the string type, the value can be up to 64 bytes, and the following character sets are supported:</p>
                          <ul>
                            <li>Letters (a-z, A-Z)</li>
                            <li>Digits (0-9)</li>
                            <li>Space, &quot;!&quot;, &quot;#&quot;, &quot;$&quot;, &quot;%&quot;, &quot;&amp;&quot;, &quot;(&quot;, &quot;)&quot;, &quot;+&quot;, &quot;-&quot;, &quot;:&quot;, &quot;;&quot;, &quot;&lt;&quot;, &quot;=&quot;, &quot;.&quot;, &quot;&gt;&quot;, &quot;?&quot;, &quot;@&quot;, &quot;[&quot;, &quot;]&quot;, &quot;^&quot;, &quot;_&quot;, &quot; {&quot;, &quot;}&quot;, &quot;|&quot;, &quot;~&quot;, &quot;,&quot;;</li>
                          </ul>
                          <p>
                            <font color="red">Note: if `roomId` is of the string type and the room ID needs to be displayed on GUIs, you are advised to escape special characters.</font>
                          </p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>role</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="attributes">
                          &lt;optional><br>
                        </td>
                        <td class="description last">
                          <p>User role. This parameter is valid only in `live` mode. Currently, two roles are supported:</p>
                          <ul>
                            <li>`anchor`</li>
                            <li>`audience`
                              <br>
                              Note: in live mode, users of the `audience` role do not have the permission to publish local streams. They have only the permission to view remote streams. If they want to co-anchor with the anchor for interaction, they need to use <a href="Client.html#switchRole">switchRole()</a> to switch their roles to `anchor` before publishing local streams.
                            </li>
                          </ul>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>privateMapKey</code></td>
                        <td class="type">
                          <span class="param-type">string</span>
                        </td>
                        <td class="attributes">
                          &lt;optional><br>
                        </td>
                        <td class="description last">
                          <p><code>Deprecated</code> Key for entering a room. If permission control is required, please carry this parameter (empty or incorrect value will cause a failure in entering the room).
                            <br>
                            <a href="https://cloud.tencent.com/document/product/647/32240">privateMapKey permission configuration</a>
                          </p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="leave"><span class="type-signature">(async) </span>leave<span class="signature">()</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Leave the current audio/video call room to end the audio/video call.<br>
              Before leaving a room, make sure that you have unpublished local streams via <a href="Client.html#unpublish">unpublish()</a>. Otherwise, the SDK will automatically unpublish them.
              In addition, all remote streams will be stopped when you leave the room.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.leave().then(() => {
  // leaving room success
}).catch(error => {
  console.error('leaving room failed: ' + error);
});</code></pre>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="publish"><span class="type-signature">(async) </span>publish<span class="signature">(stream)</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Publish the local audio/video stream.<br>
              This API works only if it is called after users enter a room via <a href="Client.html#join">join()</a>. Only one local stream can be published in an audio/video call. To publish another local stream, you need to unpublish the current local stream via <a href="Client.html#unpublish">unpublish()</a> first.<br>
              <br>
              After publishing a local stream, you can use <a href="LocalStream.html#removeTrack">removeTrack()</a>, <a href="LocalStream.html#addTrack">addTrack()</a>, and
              <a href="LocalStream.html#replaceTrack">replaceTrack()</a> to update an audio or video stream in the local stream.<br>
              When the current user publishes a local stream, the corresponding remote user will receive the `stream-added` event notification.
            </p>
            <p><strong>NOTE</strong></p>
            <ul>
              <li>If the client's <code>mode</code> is 'live' and <code>role</code> is 'audience', that is, under the audience role in live mode, users do not have the permission to publish local streams.
                If they want to co-anchor with the anchor for interaction, they need to use <a href="Client.html#switchRole">switchRole('anchor')</a> to switch their roles to `anchor` before publishing local streams.</li>
              <li>Do not call this API before the <a href="LocalStream.html#switchDevice">LocalStream.switchDevice</a> API call is completed. Otherwise, device switching exceptions may occur.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>localStream.initialize().then(() => {
  // Publish the local stream after successful local stream initialization
  client.publish(localStream).then(() => {
    // Publishing the local stream is successful
  });
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>stream</code></td>
                <td class="type">
                  <span class="param-type"><a href="Stream.html">Stream</a></span>
                </td>
                <td class="description last">
                  <p>Local stream, created via <a href="TRTC.html#createStream">createStream()</a></p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="unpublish"><span class="type-signature">(async) </span>unpublish<span class="signature">(stream)</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Unpublish a local stream.<br>
              After a local stream is unpublished, the remote user will receive the 'stream-removed' event notification.<br>
              You need to unpublish the local stream before leaving the room via <a href="Client.html#leave">leave()</a>.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Unpublish the local stream
client.unpublish(localStream).then(() => {
  // Unpublishing the local stream is successful
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>stream</code></td>
                <td class="type">
                  <span class="param-type"><a href="Stream.html">Stream</a></span>
                </td>
                <td class="description last">
                  <p>Local stream</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="subscribe"><span class="type-signature">(async) </span>subscribe<span class="signature">(stream, options<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Subscribe to a remote stream.<br>
              By default, when you receive the 'stream-added' event notification for a remote stream, the SDK immediately receives and decodes the audio/video data contained in that stream.<br>
              You can use this subscription API to specify whether to subscribe to audio, video, or audio/video streams. If you do not want to receive any audio/video data contained in the remote stream, use <a href="Client.html#unsubscribe">unsubscribe()</a> to unsubscribe from the stream.
            </p>
            <p><strong>NOTE</strong></p>
            <ul>
              <li>For the same remote stream, you can start the next call only after the current `subscribe`/`unsubscribe` call is completed.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>// Listen for remote stream subscription success events
client.on('stream-subscribed', event => {
  const remoteStream = event.stream;
  // Play back the remote audio/video stream after successful subscription
  remoteStream.play('remote_stream');
});
// Listen for remote stream 'stream-added' events
client.on('stream-added', event => {
  const remoteStream = event.stream;
  // Subscribe to remote audio/video streams
  client.subscribe(remoteStream, { audio: true, video: true }).catch(e => {
    console.error('failed to subscribe remoteStream');
  });
  // Subscribe to audio data only
  // client.subscribe(remoteStream, { audio: true, video: false }).catch(e => {
  //  console.error('failed to subscribe remoteStream');
  // });
});</code></pre>
          <pre class="highlight lang-javascript"><code>// For the same remote stream, you can start the next call only after the current `subscribe`/`unsubscribe` call is completed
// bad
client.subscribe(remoteStream, { audio: true, video: true });
client.unsubscribe(remoteStream);
// good
await client.subscribe(remoteStream, { audio: true, video: true });
await client.unsubscribe(remoteStream);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>stream</code></td>
                <td class="type">
                  <span class="param-type"><a href="Stream.html">Stream</a></span>
                </td>
                <td class="attributes">
                </td>
                <td class="description last">
                  <p>Remote stream, obtained via listening for 'stream-added' events</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">object</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="description last">
                  <p>Subscription options</p>
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Attributes</th>
                        <th>Default</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>audio</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="attributes">
                          &lt;optional><br>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>Whether to subscribe to audio data</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>video</code></td>
                        <td class="type">
                          <span class="param-type">boolean</span>
                        </td>
                        <td class="attributes">
                          &lt;optional><br>
                        </td>
                        <td class="default">
                          <code>true</code>
                        </td>
                        <td class="description last">
                          <p>Whether to subscribe to video data</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Fires:</h5>
          <ul>
            <li>ClientEvent.event:STREAM_SUBSCRIBED subscription success event notification</li>
          </ul>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h4 class="name" id="unsubscribe"><span class="type-signature">(async) </span>unsubscribe<span class="signature">(stream)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Unsubscribe from the remote stream.<br>
              When receiving a remote stream 'stream-added' event notification, the SDK will immediately receive and decode the audio/video data contained in the remote stream by default.
              To refuse to receive any audio/video data from the remote stream, call this API in the 'stream-added' event processing callback.<br>
              <strong>Note:</strong>
            </p>
            <ul>
              <li>For the same remote stream, you can start the next call only after the current `subscribe`/`unsubscribe` call is completed.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Examples</h5>
          <pre class="highlight lang-javascript"><code>client.on('stream-added', event => {
  const remoteStream = event.stream;
  // Refuse to receive any audio/video data from the remote stream
  client.unsubscribe(remoteStream).catch(e => {
    console.error('failed to unsubscribe remoteStream');
  });
});</code></pre>
          <pre class="highlight lang-javascript"><code>// For the same remote stream, you can start the next call only after the current `subscribe`/`unsubscribe` call is completed
// bad
client.subscribe(remoteStream, { audio: true, video: true });
client.unsubscribe(remoteStream);
// good
await client.subscribe(remoteStream, { audio: true, video: true });
await client.unsubscribe(remoteStream);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>stream</code></td>
                <td class="type">
                  <span class="param-type"><a href="Stream.html">Stream</a></span>
                </td>
                <td class="description last">
                  <p>Remote stream, obtained via listening for 'stream-added' events</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h4 class="name" id="switchRole"><span class="type-signature">(async) </span>switchRole<span class="signature">(role)</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Switch the user role. Valid only in 'live' mode.</p>
            <p>In interactive live streaming mode, a user can switch between `audience` and `anchor` roles.
              You can use the `role` field in <a href="Client.html#join">join()</a> to specify the role or use `switchRole` to switch the role after entering a room.</p>
            <ul>
              <li>Switch from `audience` to `anchor`: call `client.switchRole('anchor')` to switch the user role from `audience` to `anchor` and call <a href="Client.html#publish">publish()</a> to publish the local stream.</li>
              <li>Switch from `anchor` to `audience`: call `client.switchRole('audience')` to switch the user role from `anchor` to `audience`. If a published local stream exists, the SDK will unpublish it.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// In `live` mode, switch the user role from `audience` to `anchor`
await client.switchRole('anchor');
// Switch the user role from `audience` to `anchor` and start pushing streams
await client.publish(localStream);
// In `live` mode, switch the user role from `anchor` to `audience`
await client.switchRole('audience');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>role</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>User role</p>
                  <ul>
                    <li>Anchors can publish local streams. A single room allows up to 50 anchors to publish local streams at the same time.</li>
                    <li>Audience cannot publish local streams. They can only watch remote streams. There is no limit on the number of audience in a single room.</li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="on"><span class="type-signature"></span>on<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Listen for client object events.<br>
              For the detailed event list, please see <a href="module-ClientEvent.html">ClientEvent</a>.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.on('stream-added', event => {
  // stream-added event handler
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>Event name</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <p>Event handling method</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <p>Context</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="off"><span class="type-signature"></span>off<span class="signature">(eventName, handler, context)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Unbind events.<br>
              This API is used to unbind events bound via <a href="Client.html#on">on()</a>.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.on('peer-join', function peerJoinHandler(event) {
  // Peer-join event handler
  console.log('peer joined');
  client.off('peer-join', peerJoinHandler);
});
// Unbind all bound events
client.off('*');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>eventName</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="description last">
                  <p>Event name. If the wildcard '*' is passed in, all bound events will be unbound.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>handler</code></td>
                <td class="type">
                  <span class="param-type">function</span>
                </td>
                <td class="description last">
                  <p>Event handling method</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>context</code></td>
                <td class="type">
                  <span class="param-type">context</span>
                </td>
                <td class="description last">
                  <p>Context</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="getRemoteMutedState"><span class="type-signature"></span>getRemoteMutedState<span class="signature">()</span><span class="type-signature"> &rarr; {Array.&lt;<a href="global.html#RemoteMutedState">RemoteMutedState</a>>}</span></h4>
          <div class="description">
            <p>Get the list of the audio/video mute status of remote users in the current room.</p>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>4.11.2
                  Added the `hasSmall` attribute to identify whether a remote stream has a substream video.</li>
              </ul>
            </dd>
          </dl>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>Array of the audio/video mute status of remote users</p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Array.&lt;<a href="global.html#RemoteMutedState">RemoteMutedState</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="getTransportStats"><span class="type-signature">(async) </span>getTransportStats<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;<a href="global.html#TransportStats">TransportStats</a>>}</span></h4>
          <div class="description">
            <p>Get current network transfer statistics.
              <br>
              <strong>NOTE</strong>
            </p>
            <ul>
              <li>This API works only if it is called after <a href="Client.html#publish">publish()</a>.</li>
              <li>Firefox does not support getting RTT statistics.</li>
            </ul>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>const stats = await client.getTransportStats();
console.log('uplink rtt: ' + stats.rtt);
// Getting downstream RTT statistics is supported starting from v4.10.1
for (let userId in stats.downlinksRTT) {
   console.log('downlink rtt:' + stats.downlinksRTT[userId]);
}</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>`Promise` successfully returns <code>TransportStats</code></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;<a href="global.html#TransportStats">TransportStats</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="getLocalAudioStats"><span class="type-signature">(async) </span>getLocalAudioStats<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;<a href="global.html#LocalAudioStatsMap">LocalAudioStatsMap</a>>}</span></h4>
          <div class="description">
            <p>Get audio statistics of published local streams.
              <br>
              This API works only if it is called after <a href="Client.html#publish">publish()</a>.
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.getLocalAudioStats().then(stats => {
  for (let userId in stats) {
    console.log('userId: ' + userId +
                ' bytesSent: ' + stats[userId].bytesSent +
                ' packetsSent: ' + stats[userId].packetsSent);
  }
});</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>`Promise` successfully returns <code>LocalAudioStatsMap</code></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;<a href="global.html#LocalAudioStatsMap">LocalAudioStatsMap</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="getLocalVideoStats"><span class="type-signature">(async) </span>getLocalVideoStats<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;<a href="global.html#LocalVideoStatsMap">LocalVideoStatsMap</a>>}</span></h4>
          <div class="description">
            <p>Get video statistics of published local streams.
              <br>
              This API works only if it is called after <a href="Client.html#publish">publish()</a>.
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.getLocalVideoStats().then(stats => {
  for (let userId in stats) {
    console.log('userId: ' + userId +
                'bytesSent: ' + stats[userId].bytesSent +
                'packetsSent: ' + stats[userId].packetsSent +
                'framesEncoded: ' + stats[userId].framesEncoded +
                'framesSent: ' + stats[userId].framesSent +
                'frameWidth: ' + stats[userId].frameWidth +
                'frameHeight: ' + stats[userId].frameHeight);
  }
});</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>`Promise` successfully returns <code>LocalVideoStatsMap</code></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;<a href="global.html#LocalVideoStatsMap">LocalVideoStatsMap</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="getRemoteAudioStats"><span class="type-signature">(async) </span>getRemoteAudioStats<span class="signature">()</span><span class="type-signature"> &rarr; {Promise.&lt;<a href="global.html#RemoteAudioStatsMap">RemoteAudioStatsMap</a>>}</span></h4>
          <div class="description">
            <p>Get audio statistics of all current remote streams.
              <br>
            </p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.getRemoteAudioStats().then(stats => {
  for (let userId in stats) {
    console.log('userId: ' + userId +
                ' bytesReceived: ' + stats[userId].bytesReceived +
                ' packetsReceived: ' + stats[userId].packetsReceived +
                ' packetsLost: ' + stats[userId].packetsLost);
  }
});</code></pre>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>`Promise` successfully returns <code>RemoteAudioStatsMap</code></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;<a href="global.html#RemoteAudioStatsMap">RemoteAudioStatsMap</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="getRemoteVideoStats"><span class="type-signature">(async) </span>getRemoteVideoStats<span class="signature">(type<span class="signature-attributes">opt</span>)</span><span class="type-signature"> &rarr; {Promise.&lt;<a href="global.html#RemoteVideoStatsMap">RemoteVideoStatsMap</a>>}</span></h4>
          <div class="description">
            <p>Get video statistics of all current remote streams.</p>
          </div>
          <dl class="details">
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Get camera video statistics of the primary stream
const stats = await client.getRemoteVideoStats('main');
// Get screen sharing statistics of the substream
const stats = await client.getRemoteVideoStats('auxiliary');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>type</code></td>
                <td class="type">
                  <span class="param-type">string</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                  <code>'main'</code>
                </td>
                <td class="description last">
                  <p>Stream type</p>
                  <ul>
                    <li><code>main</code>: get camera video statistics of the primary stream</li>
                    <li><code>auxiliary</code>: get screen sharing statistics of the substream; supported in v4.10.1 and later versions</li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Returns:</h5>
          <div class="param-desc">
            <p>`Promise` successfully returns <code>RemoteVideoStatsMap</code></p>
          </div>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise.&lt;<a href="global.html#RemoteVideoStatsMap">RemoteVideoStatsMap</a>></span>
            </dd>
          </dl>
          <h4 class="name" id="enableAudioVolumeEvaluation"><span class="type-signature"></span>enableAudioVolumeEvaluation<span class="signature">(interval<span class="signature-attributes">opt</span>, enableInBackground<span class="signature-attributes">opt</span>)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Enable/Disable the volume callback feature.<br>
              If the volume callback feature is enabled, the SDK periodically throws the <a href="module-ClientEvent.html#.AUDIO_VOLUME">Client.on('audio-volume')</a> volume callback event to provide the evaluated volume of each user regardless of whether someone is speaking in the room.<br></p>
            <p><strong>NOTE</strong></p>
            <ul>
              <li>The SDK provides evaluated volume values only for audio/video streams that call the <a href="Stream.html#play">stream.play</a> API.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>v4.9.0</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.on('audio-volume', event => {
    event.result.forEach(({ userId, audioVolume, stream }) => {
        console.log(`userId: ${userId}, audioVolume: ${audioVolume}`);
    })
})
// Enable volume callback and set the SDK to trigger the volume callback event at an interval of 1000 ms
client.enableAudioVolumeEvaluation(1000);
// To disable volume callback, you only need to pass in a value less than or equal to 0 for `interval`
client.enableAudioVolumeEvaluation(-1);</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Attributes</th>
                <th>Default</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>interval</code></td>
                <td class="type">
                  <span class="param-type">number</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                  <code>2000</code>
                </td>
                <td class="description last">
                  <p>Time interval for triggering the volume callback event. The default value is 2000 (ms), and the minimum value is 16 (ms). If this parameter is set to a value less than or equal to 0, volume callback is disabled.</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>enableInBackground</code></td>
                <td class="type">
                  <span class="param-type">boolean</span>
                </td>
                <td class="attributes">
                  &lt;optional><br>
                </td>
                <td class="default">
                  <code>false</code>
                </td>
                <td class="description last">
                  <p>For performance reasons, the SDK does not throw a volume callback event after your application is switched to the background. If you want to receive the volume callback event after your application is switched to the background, set this parameter to `true`.</p>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="enableSmallStream"><span class="type-signature">(async) </span>enableSmallStream<span class="signature">()</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Enable the big/small stream mode on the push end.<br>
              When the big/small stream mode is enabled, do not perform operations (including `addTrack`, `removeTrack`, and `replaceTrack`) on tracks. Otherwise, performance inconsistency between the big and small streams occurs.<br></p>
            <br>
            Note:
            <ul>
              <li>This API works only if it is called before stream publishing (<a href="Client.html#publish">publish</a>) or after stream unpublishing (<a href="Client.html#unpublish">unpublish</a>).</li>
              <li>The big/small stream mode cannot be enabled during stream publishing.</li>
              <li>The big/small stream mode cannot be enabled on iOS and WeChat.</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>4.11.0</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Enable the small stream mode
await client.enableSmallStream();
// Local stream initialized successfully
await localStream.initialize();
// Publish a local stream
await client.publish(localStream);</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="disableSmallStream"><span class="type-signature">(async) </span>disableSmallStream<span class="signature">()</span><span class="type-signature"> &rarr; {Promise}</span></h4>
          <div class="description">
            <p>Disable the big/small stream mode on the push end.<br>
              Note that this API must be called before stream publishing or after stream unpublishing. If it is called after stream publishing and before stream unpublishing, performance inconsistency will occur between the big and small streams.<br></p>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>4.11.0</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>await client.unpublish(localStream);
await client.disableSmallStream();</code></pre>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
          <h5>Returns:</h5>
          <dl class="param-type">
            <dt>
              Type
            </dt>
            <dd>
              <span class="param-type">Promise</span>
            </dd>
          </dl>
          <h4 class="name" id="setSmallStreamProfile"><span class="type-signature"></span>setSmallStreamProfile<span class="signature">(options)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Set small stream parameters.<br>
              If this API is not called, the SDK uses the default small stream video attributes: resolution (W × H) of 160x120, bitrate of 100 Kbps, and frame rate of 15 fps.<br>
              The SDK automatically ensures the aspect ratio consistency between the big and small streams. If the resolution aspect ratio of the small stream is different from that of the big stream, the SDK automatically adjusts the height of the video resolution of the small stream to achieve the same aspect ratio as that of the big stream.<br></p>
            <br>
            Note:
            <p>The <code>enableSmallStream</code> and <code>setSmallStreamProfile</code> APIs work only if they are called before <code>publish</code>, and the order in which they are called does not affect their functionalities.</p>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>4.11.0</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>client.setSmallStreamProfile({
  width: 160,
  height: 120,
  bitrate: 100,
  frameRate: 15
});</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>options</code></td>
                <td class="type">
                  <span class="param-type">Object</span>
                </td>
                <td class="description last">
                  <h6>Properties</h6>
                  <table class="params">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th class="last">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="name"><code>width</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <p>Width of the video resolution of the small stream</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>height</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <p>Height of the video resolution of the small stream</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>bitrate</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <p>Bitrate of the small stream</p>
                        </td>
                      </tr>
                      <tr>
                        <td class="name"><code>framerate</code></td>
                        <td class="type">
                          <span class="param-type">Number</span>
                        </td>
                        <td class="description last">
                          <p>Frame rate of the small stream</p>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <h4 class="name" id="setRemoteVideoStreamType"><span class="type-signature"></span>setRemoteVideoStreamType<span class="signature">(remoteStream, status)</span><span class="type-signature"></span></h4>
          <div class="description">
            <p>Switch the big/small stream attribute on the audience side.<br>
              The big/small stream attribute can be switched successfully only when the small stream mode is enabled on the remote end.<br></p>
            <p>You must call <code>setRemoteVideoStreamType</code> after successfully subscribing to the remote stream:<br></p>
            <ul>
              <li>In the <code>stream-subscribed</code> callback, immediately switch the remote big stream to the small stream.<br></li>
              <li>After subscribing to the big stream for some time, call <code>setRemoteVideoStreamType</code> to switch the big stream to the small stream.<br></li>
              <li>After subscribing to the small stream for some time, you can call <code>setRemoteVideoStreamType</code> to switch the small stream to the big stream.<br></li>
            </ul>
          </div>
          <div>
            Note:
            <ul>
              <li>v4.11.0 start to support switching big/small streams</li>
              <li>v4.12.0 The interface was changed from synchronous to asynchronous</li>
            </ul>
          </div>
          <dl class="details">
            <dt class="tag-since">Since:</dt>
            <dd class="tag-since">
              <ul class="dummy">
                <li>4.11.0</li>
              </ul>
            </dd>
          </dl>
          <h5>Example</h5>
          <pre class="highlight lang-javascript"><code>// Set the big/small stream attribute for the remote stream
client.setRemoteVideoStreamType(remoteStream, 'small');</code></pre>
          <h5>Parameters:</h5>
          <table class="params">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th class="last">Description</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="name"><code>remoteStream</code></td>
                <td class="type">
                  <span class="param-type"><a href="RemoteStream.html">RemoteStream</a></span>
                </td>
                <td class="description last">
                  <p>Remote stream subscribed</p>
                </td>
              </tr>
              <tr>
                <td class="name"><code>status</code></td>
                <td class="type">
                  <span class="param-type">String</span>
                </td>
                <td class="description last">
                  <ul>
                    <li>'big': manually switch to the big stream</li>
                    <li>'small': manually switch to the small stream</li>
                  </ul>
                </td>
              </tr>
            </tbody>
          </table>
          <h5>Throws:</h5>
          <div class="param-desc">
            <p>RtcError</p>
          </div>
        </article>
      </section>
    </div>
    <br class="clear">
    <footer>
      Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.7</a> on Tue Sep 14 2021 17:15:51 GMT+0800 (CST) using the <a href="https://github.com/clenemt/docdash">docdash</a> theme.
    </footer>
    <script>
    hljs.highlightAll({cssSelector:'highlight'});
    hljs.initLineNumbersOnLoad();
</script>
    <script src="scripts/polyfill.js"></script>
    <script src="scripts/linenumber.js"></script>
    <script src="scripts/search.js" defer></script>
    <script src="scripts/collapse.js" defer></script>
  </body>
</html>
