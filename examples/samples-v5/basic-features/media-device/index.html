<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="./style.css" rel="stylesheet" type="text/css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <title>Media Device</title>
    <script src="../../components/header.js" defer></script>
    <script
      src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"
      defer
    ></script>
    <script src="../../libs/lib-generate-test-usersig.min.js" defer></script>
    <script
      src="https://web.sdk.qcloud.com/trtc/webrtc/v5/dist/trtc.js"
      defer
    ></script>
    <script src="../../libs/clipboard.min.js" defer></script>
    <script src="../../util/utils.js" defer></script>
    <script src="./script.js" defer></script>
  </head>
  <body>
    <header-nav></header-nav>
    <main>
      <div class="feature-container">
        <h1 style="color: #1c66e5">Media Device</h1>
        <section>
          <h3>Step-1: Fill in the blanks</h3>
          <span class="note"
            >Get SDKAppId and SDKSecretKey from
            <a
              target="_top"
              href="https://console.trtc.io/?quickclaim=engine_trial"
              >TRTC Console</a
            >
          </span>
          <span class="warning"
            >Note: This demo is for demonstration purposes only. Before official
            launch, please migrate SDKSecretKey and the UserSig calculation code
            to your own backend server to avoid unauthorized traffic use caused
            by the key leakage.
            <a target="_top" href="https://trtc.io/document/35166"
              >View Documents</a
            ></span
          >
          <div class="input-list">
            <div class="input-group">
              <label for="sdk-app-id">SDKAppId</label>
              <input
                id="sdk-app-id"
                type="number"
                class="form-control"
                placeholder="SDKAppId"
              />
            </div>
            <div class="input-group">
              <label for="sdk-secret-key">SDKSecretKey</label>
              <input
                id="sdk-secret-key"
                type="text"
                class="form-control"
                placeholder="SDKSecretKey"
              />
            </div>
          </div>
          <div class="input-list">
            <div class="input-group">
              <label for="user-id">UserId</label>
              <input id="user-id" type="text" class="form-control" />
            </div>
            <div class="input-group">
              <label for="room-id">RoomId</label>
              <input id="room-id" type="number" class="form-control" />
            </div>
          </div>
        </section>
        <section>
          <h3>Step-2: Enter the room</h3>
          <div class="btn-list">
            <button id="enter-btn" class="btn" onclick="enterRoom()">
              <span class="btn-text">Enter Room</span>
            </button>
            <button id="exit-btn" class="btn" onclick="exitRoom()" disabled>
              <span class="btn-text">Exit Room</span>
            </button>
          </div>
        </section>
        <section>
          <h3>
            Step-3.1: Different options to turn on/off the microphone and camera
          </h3>
          <div>
            <label>Options:</label>
            <select id="option">
              <option value="mute" selected>
                Use the mute parameter (recommended)
              </option>
              <option value="publish">Use the publish parameter</option>
              <option value="stop">
                Use stopLocalVideo() & stopLocalAudio()
              </option>
            </select>
          </div>
          <div class="btn-list first-btn-list">
            <button
              id="start-audio-btn"
              class="btn"
              onclick="startLocalAudio()"
            >
              <span class="btn-text">Start Local Audio</span>
            </button>
            <button
              id="stop-audio-btn"
              class="btn"
              onclick="stopLocalAudio()"
              disabled
            >
              <span class="btn-text">Stop Local Audio</span>
            </button>
            <button id="mute-mic-btn" class="btn mute-btn" disabled>
              <span class="btn-text">Mute Microphone</span>
            </button>
            <button
              id="publish-mic-btn"
              class="btn publish-btn"
              disabled
              style="display: none"
            >
              <span class="btn-text">Publish Microphone</span>
            </button>
            <button id="unmute-mic-btn" class="btn mute-btn" disabled>
              <span class="btn-text">Unmute Microphone</span>
            </button>
            <button
              id="unpublish-mic-btn"
              class="btn publish-btn"
              disabled
              style="display: none"
            >
              <span class="btn-text">Unpublish Microphone</span>
            </button>
          </div>
          <div class="btn-list">
            <button
              id="start-video-btn"
              class="btn"
              onclick="startLocalVideo()"
            >
              <span class="btn-text">Start Local Video</span>
            </button>
            <button
              id="stop-video-btn"
              class="btn"
              onclick="stopLocalVideo()"
              disabled
            >
              <span class="btn-text">Stop Local Video</span>
            </button>
            <button id="mute-camera-btn" class="btn mute-btn" disabled>
              <span class="btn-text">Mute Camera</span>
            </button>
            <button
              id="publish-camera-btn"
              class="btn publish-btn"
              disabled
              style="display: none"
            >
              <span class="btn-text">Publish Camera</span>
            </button>
            <button id="unmute-camera-btn" class="btn mute-btn" disabled>
              <span class="btn-text">Unmute Camera</span>
            </button>
            <button
              id="unpublish-camera-btn"
              class="btn publish-btn"
              disabled
              style="display: none"
            >
              <span class="btn-text">Unpublish Camera</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Step-3.2: Switch the microphone, camera and speaker</h3>
          <div class="device-container">
            <div class="device-item">
              <label for="microphone-select">Microphones:</label>
              <select id="microphone-select"></select>
              <button
                id="update-mic-btn"
                class="btn"
                style="display: block"
                disabled
              >
                <span class="btn-text">Update Microphone</span>
              </button>
            </div>
            <div class="device-item">
              <label for="camera-select">Cameras:</label>
              <select id="camera-select"></select>
              <button
                id="update-camera-btn"
                class="btn"
                style="display: block"
                disabled
              >
                <span class="btn-text">Update Camera</span>
              </button>
            </div>
            <div class="device-item">
              <label for="speaker-select">Speakers:</label>
              <select id="speaker-select"></select>
              <button
                id="update-speaker-btn"
                class="btn"
                style="display: block"
                disabled
              >
                <span class="btn-text">Update Speaker</span>
              </button>
            </div>
          </div>
        </section>
        <section>
          <h3>Activate remote stream</h3>
          <div>
            <span class="note"
              >Use the link below to join the room as another user. The link
              will be updated after being copied.</span
            >
            <div class="copy">
              <button id="invite-btn" data-clipboard-target="#invite-url">
                <img
                  src="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/samples/icons/clippy.svg"
                  width="12px"
                  height="12px"
                  alt="Copy to clipboard"
                />
              </button>
              <input id="invite-url" class="invite-input" readonly />
            </div>
          </div>
        </section>
      </div>
      <div class="video-container">
        <div class="video-sub-container">
          <h2>Local Video</h2>
          <div id="local-video-view" class="video-view"></div>
        </div>
        <div class="video-sub-container">
          <h2>Remote Video</h2>
          <div id="remote-video-view" class="video-view"></div>
        </div>
      </div>
    </main>
  </body>
</html>
