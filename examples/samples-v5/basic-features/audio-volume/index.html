<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="./style.css" rel="stylesheet" type="text/css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <title>Audio Volume</title>
    <script src="../../components/header.js" defer></script>
    <script
      src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"
      defer
    ></script>
    <script src="../../libs/lib-generate-test-usersig.min.js" defer></script>
    <script
      src="https://web.sdk.qcloud.com/trtc/webrtc/v5/dist/trtc.js"
      defer
    ></script>
    <script src="../../libs/clipboard.min.js" defer></script>
    <script src="../../util/utils.js" defer></script>
    <script src="./script.js" defer></script>
  </head>
  <body>
    <header-nav></header-nav>
    <main>
      <div class="feature-container">
        <h1 style="color: #1c66e5">Audio Volume</h1>
        <section>
          <h3>Step-1: Fill in the blanks</h3>
          <span class="note"
            >Get SDKAppId and SDKSecretKey from
            <a
              target="_top"
              href="https://console.trtc.io/?quickclaim=engine_trial"
              >TRTC Console</a
            >
          </span>
          <span class="warning"
            >Note: This demo is for demonstration purposes only. Before official
            launch, please migrate SDKSecretKey and the UserSig calculation code
            to your own backend server to avoid unauthorized traffic use caused
            by the key leakage.
            <a target="_top" href="https://trtc.io/document/35166"
              >View Documents</a
            ></span
          >
          <div class="input-list">
            <div class="input-group">
              <label for="sdk-app-id">SDKAppId</label>
              <input
                id="sdk-app-id"
                type="number"
                class="form-control"
                placeholder="SDKAppId"
              />
            </div>
            <div class="input-group">
              <label for="sdk-secret-key">SDKSecretKey</label>
              <input
                id="sdk-secret-key"
                type="text"
                class="form-control"
                placeholder="SDKSecretKey"
              />
            </div>
          </div>
          <div class="input-list">
            <div class="input-group">
              <label for="user-id">UserId</label>
              <input id="user-id" type="text" class="form-control" />
            </div>
            <div class="input-group">
              <label for="room-id">RoomId</label>
              <input id="room-id" type="number" class="form-control" />
            </div>
          </div>
        </section>
        <section>
          <h3>Step-2: Enter the room</h3>
          <div class="btn-list">
            <button id="enter-btn" class="btn" onclick="enterRoom()">
              <span class="btn-text">Enter Room</span>
            </button>
            <button id="exit-btn" class="btn" onclick="exitRoom()" disabled>
              <span class="btn-text">Exit Room</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Step-3.1: Detect audio volume</h3>
          <span class="note">Speaking after muting can still be detected</span>
          <div>
            <div class="volume-detect">
              <label for="local-volume-detect">Local volume:</label>
              <input
                id="local-volume-detect"
                class="volume-display"
                type="text"
                readonly
              />
            </div>
            <div class="volume-detect">
              <label for="remote-volume-detect">Remote volume:</label>
              <input
                id="remote-volume-detect"
                class="volume-display"
                type="text"
                readonly
              />
            </div>
          </div>
          <div class="option-container">
            <div class="option-item">
              <input type="checkbox" id="enable-background" />
              <label for="enable-background" style="white-space: nowrap"
                >Enable in background</label
              >
            </div>
            <div class="option-item">
              <label for="detect-interval">Detect interval:</label>
              <select id="detect-interval">
                <option value="500">500ms</option>
                <option value="1000">1000ms</option>
                <option value="2000" selected>2000ms (default)</option>
              </select>
            </div>
          </div>
          <div class="btn-list">
            <button
              id="start-detect-btn"
              class="btn"
              onclick="startAudioVolumeDetection()"
              disabled
            >
              <span class="btn-text">Start Detection</span>
            </button>
            <button
              id="stop-detect-btn"
              class="btn"
              onclick="stopAudioVolumeDetection()"
              disabled
            >
              <span class="btn-text">Stop Detection</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Step-3.2: Adjust audio volume</h3>
          <div class="volume-adjust">
            <div class="volume-adjust-display">
              <label for="local-volume-display">Local volume:</label>
              <input
                id="local-volume-display"
                class="volume-display"
                type="text"
                value="100"
                readonly
              />
            </div>
            <input
              type="range"
              id="local-volume-input"
              class="volume-input"
              onchange="handleLocalAudioVolumeChange()"
              min="0"
              max="200"
              disabled
            />
            <div class="btn-list">
              <button
                id="mute-btn"
                class="btn"
                onclick="muteLocalAudio()"
                disabled
              >
                <span class="btn-text">Mute Local Audio</span>
              </button>
              <button
                id="unmute-btn"
                class="btn"
                onclick="muteLocalAudio()"
                disabled
              >
                <span class="btn-text">Unmute Local Audio</span>
              </button>
            </div>
          </div>
          <div class="volume-adjust">
            <div class="volume-adjust-display">
              <label for="remote-volume-display">Remote volume:</label>
              <input
                id="remote-volume-display"
                class="volume-display"
                type="text"
                value="100"
                readonly
              />
            </div>
            <input
              type="range"
              id="remote-volume-input"
              class="volume-input"
              onchange="handleRemoteAudioVolumeChange()"
              min="0"
              max="200"
              disabled
            />
          </div>
        </section>
        <section>
          <h3>Activate remote stream</h3>
          <div>
            <span class="note"
              >Use the link below to join the room as another user. The link
              will be updated after being copied.</span
            >
            <div class="copy">
              <button id="invite-btn" data-clipboard-target="#invite-url">
                <img
                  src="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/samples/icons/clippy.svg"
                  width="12px"
                  height="12px"
                  alt="Copy to clipboard"
                />
              </button>
              <input id="invite-url" class="invite-input" readonly />
            </div>
          </div>
        </section>
      </div>
    </main>
  </body>
</html>
