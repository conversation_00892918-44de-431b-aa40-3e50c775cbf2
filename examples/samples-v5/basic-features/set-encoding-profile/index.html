<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="./style.css" rel="stylesheet" type="text/css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <title>Set Encoding Profile</title>
    <script src="../../components/header.js" defer></script>
    <script
      src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"
      defer
    ></script>
    <script src="../../libs/lib-generate-test-usersig.min.js" defer></script>
    <script
      src="https://web.sdk.qcloud.com/trtc/webrtc/v5/dist/trtc.js"
      defer
    ></script>
    <script src="../../libs/clipboard.min.js" defer></script>
    <script src="../../util/utils.js" defer></script>
    <script src="./script.js" defer></script>
  </head>
  <body>
    <header-nav></header-nav>
    <main>
      <div class="feature-container">
        <h1 style="color: #1c66e5">Set Encoding Profile</h1>
        <section>
          <h3>Step-1: Fill in the blanks</h3>
          <span class="note"
            >Get SDKAppId and SDKSecretKey from
            <a href="https://console.trtc.io/?quickclaim=engine_trial"
              >TRTC Console</a
            >
          </span>
          <span class="warning"
            >Note: This demo is for demonstration purposes only. Before official
            launch, please migrate SDKSecretKey and the UserSig calculation code
            to your own backend server to avoid unauthorized traffic use caused
            by the key leakage.
            <a target="_top" href="https://trtc.io/document/35166"
              >View Documents</a
            ></span
          >
          <div class="input-list">
            <div class="input-group">
              <label for="sdk-app-id">SDKAppId</label>
              <input
                id="sdk-app-id"
                type="number"
                class="form-control"
                placeholder="SDKAppId"
              />
            </div>
            <div class="input-group">
              <label for="sdk-secret-key">SDKSecretKey</label>
              <input
                id="sdk-secret-key"
                type="text"
                class="form-control"
                placeholder="SDKSecretKey"
              />
            </div>
          </div>
          <div class="input-list">
            <div class="input-group">
              <label for="user-id">UserId</label>
              <input id="user-id" type="text" class="form-control" />
            </div>
            <div class="input-group">
              <label for="room-id">RoomId</label>
              <input id="room-id" type="number" class="form-control" />
            </div>
          </div>
        </section>
        <section>
          <h3 class="step-title">Step-2: Enter the room</h3>
          <div class="btn-list">
            <button id="enter-btn" class="btn" onclick="enterRoom()">
              <span class="btn-text">Enter Room</span>
            </button>
            <button id="exit-btn" class="btn" onclick="exitRoom()" disabled>
              <span class="btn-text">Exit Room</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Step-3: Choose encoding profile</h3>
          <span style="color: #084298; font-family: 'Oxygen'"
            >Note: SDK does not support dynamic adjustment of audio profile
            during the call.</span
          >
          <div class="option-container">
            <div class="option-item">
              <label>Video profile:</label>
              <select id="video-profile">
                <option value="120p">120p</option>
                <option value="120p_2">120p_2</option>
                <option value="180p">180p</option>
                <option value="180p_2">180p_2</option>
                <option value="240p">240p</option>
                <option value="240p_2">240p_2</option>
                <option value="360p">360p</option>
                <option value="360p_2">360p_2</option>
                <option value="480p">480p</option>
                <option value="480p_2" selected>480p_2 (default)</option>
                <option value="720p">720p</option>
                <option value="1080p">1080p</option>
                <option value="1440p">1440p</option>
                <option value="4K">4K</option>
              </select>
              <button
                id="update-video-btn"
                class="btn"
                onclick="updateVideoProfile()"
                disabled
              >
                <span class="btn-text">Update</span>
              </button>
            </div>
            <div class="option-item">
              <label>Audio profile:</label>
              <select id="audio-profile">
                <option value="standard" selected>Standard (default)</option>
                <option value="high">High</option>
                <option value="standard-stereo">Standard Stereo</option>
                <option value="high-stereo">High Stereo</option>
              </select>
            </div>
          </div>
          <div class="btn-list">
            <button
              id="start-audio-btn"
              class="btn"
              onclick="startLocalAudio()"
            >
              <span class="btn-text">Start Local Audio</span>
            </button>
            <button
              id="stop-audio-btn"
              class="btn"
              onclick="stopLocalAudio()"
              disabled
            >
              <span class="btn-text">Stop Local Audio</span>
            </button>
          </div>
          <div class="btn-list">
            <button
              id="start-video-btn"
              class="btn"
              onclick="startLocalVideo()"
            >
              <span class="btn-text">Start Local Video</span>
            </button>
            <button
              id="stop-video-btn"
              class="btn"
              onclick="stopLocalVideo()"
              disabled
            >
              <span class="btn-text">Stop Local Video</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Activate remote stream</h3>
          <div>
            <span class="note"
              >Use the link below to join the room as another user. The link
              will be updated after being copied.</span
            >
            <div class="copy">
              <button id="invite-btn" data-clipboard-target="#invite-url">
                <img
                  src="https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/samples/icons/clippy.svg"
                  width="12px"
                  height="12px"
                  alt="Copy to clipboard"
                />
              </button>
              <input id="invite-url" class="invite-input" readonly />
            </div>
          </div>
        </section>
      </div>
      <div class="video-container">
        <div class="video-sub-container">
          <h2 class="text-title">Local Video</h2>
          <div id="local-video-view" class="video-view"></div>
        </div>
        <div class="video-sub-container">
          <h2 class="text-title">Remote Video</h2>
          <div id="remote-video-view" class="video-view"></div>
        </div>
      </div>
    </main>
  </body>
</html>
