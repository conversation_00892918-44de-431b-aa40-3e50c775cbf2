* {
  margin: 0;
  padding: 0;
}

main {
  margin: 25px auto;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  background-color: white;
  padding: 20px;
}

@media screen and (min-width: 1024px) {
  main {
    width: 60%;
    border-radius: 32px;
    box-shadow: 0 8px 24px rgba(66, 110, 175, 0.06),
      0 6px 8px rgba(32, 77, 141, 0.08);
  }

  body {
    background-image: url("https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/samples/assets/background.png");
  }
}

h1,
h2 {
  text-align: center;
  font-family: "Oxygen";
  margin-bottom: 0.8rem;
}

h3 {
  font-family: "Oxygen";
  margin-bottom: 18px;
}

@media screen and (max-width: 768px) {
  h1 {
    font-size: 1.5rem;
  }
  h2 {
    font-size: 1rem;
  }
}

.note {
  color: #084298;
  font-family: "Oxygen";
}

.warning {
  display: block;
  color: red;
  font-family: "Oxygen";
  margin: 3px 0;
}

.feature-container {
  width: 80%;
}

section {
  padding: 1rem;
  background-image: linear-gradient(
    rgb(237, 242, 252),
    rgb(255, 255, 255) 66.15%
  );
  border-radius: 16px;
  margin-bottom: 1rem;
  box-shadow: 0 8px 24px rgba(66, 110, 175, 0.06),
    0 6px 8px rgba(32, 77, 141, 0.08);
}

section:not(:nth-child(3)) {
  overflow: auto;
}

.message-container {
  display: flex;
  align-items: center;
}

.message-container .btn {
  margin-bottom: 0;
}

.message-input {
  width: 15rem;
  font-size: 1rem;
  color: #4f586b;
  border-radius: 8px;
  border: 1px solid #ced4da;
  padding: 6px;
  margin: 0 12px 0 6px;
}

.video-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.video-sub-container {
  width: 40%;
  padding: 8px;
}

.video-view {
  aspect-ratio: 16 / 9;
  background-color: black;
}

.video-view video {
  vertical-align: top;
}

.input-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 8px;
}

.input-group {
  flex-basis: 40%;
  display: flex;
}

.input-group label {
  width: 6rem;
  font-size: 0.8rem;
  font-family: "Oxygen";
  background-color: #f2f5fc;
  padding: 8px;
  border: 1px solid #ced4da;
  border-right: none;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.form-control {
  width: 15vw;
  min-width: 8rem;
  padding: 8px;
  border: 1px solid #ced4da;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  color: #4f586b;
}

.btn-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.btn {
  flex-shrink: 0;
  background-color: #0d6efd;
  padding: 0.5rem;
  border: 1px solid #0d6efd;
  border-radius: 8px;
  margin-bottom: 3px;
  cursor: pointer;
}

.btn:not(:last-child) {
  margin-right: 16px;
}

.btn:hover {
  background-color: #144fb6;
  border: 1px solid #144fb6;
  transition: all 0.2s ease-out;
}

.btn:disabled {
  background-color: rgb(182, 187, 198);
  border: 1px solid transparent;
  cursor: not-allowed;
}

.btn-text {
  font-size: 0.85rem;
  color: #ffffff;
}

.invite-container {
  margin-top: 12px;
}

.invite-input {
  border: 1px solid #d5d5d5;
  line-height: 30px;
  font-size: 12px;
  width: 100%;
  box-sizing: border-box;
  padding: 1px 6px;
}

.copy {
  display: flex;
  align-items: center;
  padding-top: 0.8rem;
}

#invite-btn {
  position: relative;
  display: inline-block;
  padding: 6px 12px;
  font-size: 18px;
  font-weight: 700;
  line-height: 20px;
  color: #333;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  background-color: #f2f5fc;
  border: 1px solid #d5d5d5;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.tooltipped {
  position: relative;
}

.tooltipped:after {
  position: absolute;
  z-index: 1000000;
  display: none;
  padding: 5px 8px;
  font: normal normal 11px/1.5 Helvetica, arial, nimbussansl, liberationsans,
    freesans, clean, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
  color: #fff;
  text-align: center;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: break-word;
  white-space: pre;
  pointer-events: none;
  content: attr(aria-label);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 3px;
  -webkit-font-smoothing: subpixel-antialiased;
}

.tooltipped:before {
  position: absolute;
  z-index: 1000001;
  display: none;
  width: 0;
  height: 0;
  color: rgba(0, 0, 0, 0.8);
  pointer-events: none;
  content: "";
  /*border: 5px solid transparent*/
}

.tooltipped:hover:before,
.tooltipped:hover:after,
.tooltipped:active:before,
.tooltipped:active:after,
.tooltipped:focus:before,
.tooltipped:focus:after {
  display: inline-block;
  text-decoration: none;
}

.tooltipped-s:after {
  top: 100%;
  right: 50%;
  margin-top: 5px;
}

.tooltipped-s:before {
  top: auto;
  right: 50%;
  bottom: -5px;
  margin-right: -5px;
  border-bottom-color: rgba(0, 0, 0, 0.8);
}

.tooltipped-s:after {
  -webkit-transform: translateX(50%);
  -ms-transform: translateX(50%);
  transform: translateX(50%);
}
