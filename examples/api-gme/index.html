<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <link rel='icon' href='/favicon.ico' />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

    <style>
        .test-panel { margin-left: 8px; margin-top: 8px;}
        .good-btn { font-size: 18px;}
    </style>
</head>
<body>
<span id='testaudio'>test audio</span>
<div id="app" class="test-panel">
    <!--页面UI展示-->
    <p style="margin: 0;">上行 发送字节数：{{local_bytesSent}} | 发送包数：{{local_packetsSent}} </p>
    <!-- 编码器：{{codecName}} | 采样率：{{sampleRate}} | 声道：{{numChannel}} | 丢包数： 收包数： 丢包率：{{pkgLossRate}}-->
<!--    <p style="margin: 0;" v-for="dsi in downStreamInfoList">下行{{dsi.uin}} 接收码率：{{dsi.brRecv}} | 延迟：{{dsi.delay}} | 抖动ms：{{dsi.jitterBufferMs}} | 抖动c：{{dsi.jitterReceived}}</p>-->
    <p style="margin: 0;" v-for="dsi in downStreamInfoList">下行{{dsi.uin}} 接收字节数：{{dsi.bytesReceived}} | 收包数：{{dsi.packetsReceived}} | 抖动：{{dsi.jitter}} | 丢包数：{{dsi.packetsLost}} </p>
    <div style="height: 8px;"></div>
    <label for="input-userid">userid:</label><input id="input-userid" type="text"><br>
    <label for="input-roomid">roomid:</label><input id="input-roomid" type="text" value="202101"><br>
    <label for="select-role">roomtype:</label>
    <select id="select-role" onchange="onSelectRoleChanged();">
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
    </select>
    <br>
    <fieldset>
        <legend>
            范围语音
        </legend>
        <label for="input-roomid">teamid:</label><input id="input-teamid" type="text" value="0">
        <button id="teamid_btn" type="button" class="good-btn" >修改</button><br>
        <label for="select-role">audiomode:</label>
        <select id="select-audio-mode" onchange="onSelectAudioModeChanged();">
            <option value="0">RANGE_AUDIO_MODE_WORLD</option>
            <option value="1">RANGE_AUDIO_MODE_TEAM</option>
        </select><br>
        <label for="input-roomid">AudioRecvRange:</label><input id="input-range" type="text" value="100"><br>
        <div class="slidecontainer">
            <label for="input-roomid">Position(X)[+-100]:</label>
            <input type="range" min="-100" max="100" value="0" class="slider" id="myRangeX"
                   onchange="onPositionChanged();"> <br>
            <label for="input-roomid">Position(Y)[+-100]:</label>
            <input type="range" min="-100" max="100" value="0" class="slider" id="myRangeY"
                   onchange="onPositionChanged();"> <br>
            <label for="input-roomid">Position(Z)[+-100]:</label>
            <input type="range" min="-100" max="100" value="0" class="slider" id="myRangeZ"
                   onchange="onPositionChanged();"> <br>
            <label for="input-roomid">Position(XYZ):</label><input id="input-position" type="text" value="(0, 0, 0)">
        </div>
    </fieldset>
    <br>
    <div class="form-group">
        <label for="microphoneId" class="bmd-label-floating">MICROPHONE</label>
        <select class="form-control" id="microphoneId" name="microphoneId"  onchange="onSelectMicrophoneChanged();">
        </select>
        <label for="speakerId" class="bmd-label-floating">SPEAKER</label>
        <select class="form-control" id="speakerId" name="speakerId"  onchange="onSelectSpeakerChanged();">
        </select>
    </div>
    <button id="start_btn" type="button" class="good-btn" >进房</button>
    <button id="quit_btn" type="button" class="good-btn" >退房</button>
    <button id="open_speaker" type="button" class="good-btn" >开扬声器</button>
    <button id="close_speaker" type="button" class="good-btn" >关扬声器</button>
    <button id="open_autio_btn" type="button" class="good-btn" >开麦</button>
    <button id="close_autio_btn" type="button" class="good-btn" >关麦</button>
    <br>
    <br>
    <label for="input-musicid">MusicID:</label><input id="input-musicid" type="text" value="music01" style="width:100px;">
    <label for="input-musicurl">MusicURL:</label><input id="input-musicurl" type="text" style="width:400px;" value="https://gmeh5-test-1256590279.cos.ap-guangzhou.myqcloud.com/song/GMEMusic.mp3">
    <br>
    <button id="start_acc_btn" type="button" class="good-btn" >开启伴奏</button>
    <button id="stop_acc_btn" type="button" class="good-btn" >关闭伴奏</button>
    <button id="pause_acc_btn" type="button" class="good-btn" >暂停</button>
    <button id="resume_acc_btn" type="button" class="good-btn" >恢复</button>
    <label for="input-seek">播放进度(s):</label><input id="input-seek" type="text" value="10">
    <button id="seek_acc_btn" type="button" class="good-btn" >设置</button>
    <input type="checkbox" id="loop-play">循环播放
<!--    <label for="input-accompany-loopCount">伴奏次数:</label><input id="input-accompany-loopCount" type="number" value="1">-->
    <label>伴奏音量:</label>
    <select id="select-accompany-volume" onchange="onSelectAccompanyVolumeChanged();">
        <option value="0">0</option>
        <option value="0.1">0.1</option>
        <option value="0.2">0.2</option>
        <option value="0.3">0.3</option>
        <option value="0.4">0.4</option>
        <option value="0.5">0.5</option>
        <option value="0.6">0.6</option>
        <option value="0.7">0.7</option>
        <option value="0.8">0.8</option>
        <option value="0.9">0.9</option>
        <option value="1.0" selected>1</option>
    </select>

<!--    <select id="select-accompany-send" onchange="onSelectAccompanySendChanged();">-->
<!--    <option value="1">混音发送</option>-->
<!--    <option value="0">混音不发送</option>-->
<!--    </select>-->
</div>

<div id="upload_stream_canvas" ></div>
<div id="download_streams_canvas" ></div>

<!--Step 2: 添加Audio容器-->
<!--容器，用来承接Audio标签， 请务必留意，不能忽略-->
<div id="gme-audio-wrap"></div>
<script src="https://code.jquery.com/jquery-3.5.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue@2.5.17/dist/vue.min.js"></script>
<script src="https://cdn.bootcss.com/eruda/1.3.0/eruda.min.js">eruda.init();</script>
<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script src="dist/WebRTCService.js"></script>
<script src="js/debug/lib-generate-test-usersig.min.js"></script>
<script src="js/debug/GenerateTestUserSig.js"></script>
<script src="js/index.js"></script>

</body>
</html>
