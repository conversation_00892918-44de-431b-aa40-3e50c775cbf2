const AudioContext = window.AudioContext || window.webkitAudioContext;
enableUI(false);
const input ="data:audio/mp3;base64,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";

window.vConsole = new window.VConsole();
console.info('start GME H5 Demo!');

function base64ToBlob(input_base64_with_mime) {
    let audioSrc = input_base64_with_mime; // 拼接最终的base64
    let arr = audioSrc.split(',');
    let array = arr[0].match(/:(.*?);/);
    let mime = (array && array.length > 1 ? array[1] : type) || type;
    // 去掉url的头，并转化为byte
    let bytes = window.atob(arr[1]);
    // 处理异常,将ascii码小于0的转换为大于0
    let ab = new ArrayBuffer(bytes.length);
    // 生成视图（直接针对内存）：8位无符号整数，长度1个字节
    let ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
    }
    return new Blob([ab], {
        type: mime
    });
}

function enableUI(isInRoom) {
    $("#teamid_btn").attr("disabled", !isInRoom);
    $("#input-range").attr("disabled", !isInRoom);
    $("#range_btn").attr("disabled", !isInRoom);
    $("#input-position").attr("disabled", !isInRoom);
    $("#start_btn").attr("disabled", isInRoom);
    $("#quit_btn").attr("disabled", !isInRoom);
    $("#open_speaker").attr("disabled", !isInRoom);
    $("#close_speaker").attr("disabled", !isInRoom);
    $("#open_autio_btn").attr("disabled", !isInRoom);
    $("#close_autio_btn").attr("disabled", !isInRoom);
    $("#start_acc_btn").attr("disabled", !isInRoom);
    $("#stop_acc_btn").attr("disabled", !isInRoom);
    $("#pause_acc_btn").attr("disabled", !isInRoom);
    $("#resume_acc_btn").attr("disabled", !isInRoom);
    $("#input-seek").attr("disabled", !isInRoom);
    $("#seek_acc_btn").attr("disabled", !isInRoom);
    $("#loop-play").attr("disabled", !isInRoom);
    $("#select-accompany-volume").attr("disabled", !isInRoom);
    $("#myRangeX").attr("disabled", !isInRoom);
    $("#myRangeY").attr("disabled", !isInRoom);
    $("#myRangeZ").attr("disabled", !isInRoom);
}

let audio = document.createElement('audio');
function testAutoPlay () {
    // 返回一个promise以告诉调用者检测结果
    return new Promise(resolve => {
        document.body.appendChild(audio);
        // require一个本地文件，会变成base64格式
        // audio.src = require('@/assets/empty-audio.mp3');
        let audioBlob = base64ToBlob(input);
        audio.src = window.URL.createObjectURL(audioBlob);
        audio.addEventListener("canplay", () => {
            // window.URL.revokeObjectURL(audio.src);
            let autoplay = true;
            // play返回的是一个promise
            audio.play().then(() => {
                // 支持自动播放
                autoplay = true;
            }).catch(err => {
                // 不支持自动播放
                autoplay = false;
            }).finally(() => {
                // audio.remove();
                // 告诉调用者结果
                resolve(autoplay);
            });
        });
    });
}
let autoPlay = false;
testAutoPlay()
    .then(auto=>{
        autoPlay = auto;
        const str = 'test audio: current browser support auto play:'+(auto?'yes':'no');
        $('#testaudio').text(str);
    });

console.info(`UserAgent:${(window.navigator && window.navigator.userAgent)}`);

const ctx = new AudioContext();
const analyser = ctx.createAnalyser()
analyser.fftSize = 128;
const bufferLength = analyser.frequencyBinCount;

const CANVAS_WIDTH = 300;
const CANVAS_HEIGHT = 30;
const BAR_HEIGHT= 20;

let stream_infos = new Map();//{0: (mySelfCanvas, mySelfCanvasContext, WebGMEAPI.Param.localStream_)};
let currentUserId = '';
function addStreamCanvas(id, desciption, parent_div) {
    $('<div>')
        .attr('id', 'div'+id)
        .attr('align', 'left')
        .attr('style', 'border:1px solid red')
        .appendTo('#' + parent_div);
    $('<label>')
        .attr('id', 'label'+id)
        .text(desciption)
        .appendTo('#' + 'div'+id);
    $('<canvas>')
        .attr('id', 'canvas'+id)
        .attr('width', CANVAS_WIDTH)
        .attr('height', CANVAS_HEIGHT)
        .appendTo('#' + 'div'+id);
    const canvas = $('#' + 'canvas'+id)[0];
    const canvas_context = canvas.getContext("2d");
    stream_infos.set(id, [canvas, canvas_context]);
    console.info(stream_infos);
}
function removeStreamCanvas(id) {
    if ($('#' + 'div'+id)[0]) {
        $('#' + 'div'+ id).remove();
    }
    stream_infos.delete(id);
    console.info(stream_infos);
}

function renderSelfFrame(result) {
    result.forEach(({ userId, volume }) => {
        const id = userId === '' ? currentUserId : userId;
        let stream = stream_infos.get(id);
        if (stream && stream.length > 1) {
            stream[1].clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            stream[1].fillStyle = "#0000ff";
            stream[1].fillRect(0, (CANVAS_HEIGHT-BAR_HEIGHT)/2, volume, BAR_HEIGHT);
        }
    })
}

function onSelectRoleChanged(e) {
    setTimeout(() => {
        console.info('select val:', document.getElementById("select-role").value);
    }, 50);
}

function onSelectAudioModeChanged(e) {
    gmeAPI.setRangeAudioMode(cAudioMode());
}

function onSelectMicrophoneChanged(e) {
    gmeAPI.selectMic(document.getElementById("microphoneId").value);
}

function onSelectSpeakerChanged(e) {
    gmeAPI.selectSpeaker(document.getElementById("speakerId").value);
}

function onPositionChanged(e) {
    const x = parseInt(document.getElementById("myRangeX").value);
    const y = parseInt(document.getElementById("myRangeY").value);
    const z = parseInt(document.getElementById("myRangeZ").value);
    const template1 = `(` + x + `,` + y + `,` + z + `）`;
    document.getElementById("input-position").value = template1;
    let position = {x, y, z};
    gmeAPI.updateAudioRecvRange(cAudioRecvRange());
    gmeAPI.updateSelfPosition(position, null, null, null);
}

function onSelectAccompanySendChanged(e) {
    setTimeout(() => {
        console.info('select accompany send val:', document.getElementById("select-accompany-send").value);
    }, 50);
}

function onSelectAccompanyVolumeChanged(e) {
    setTimeout(() => {
        let value = document.getElementById("select-accompany-volume").value;
        console.info('select accompany volume val:', value);
        gmeAPI.setMusicVolume(document.getElementById("input-musicid").value, Number(value));
    }, 50);
}

function genRandNumId() {
    var min = 11111;
    var max = 99999;
    var randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
    return randomNum;
}

const cUserID = () => document.getElementById("input-userid").value;
const cRoomNum = () => document.getElementById("input-roomid").value;
const cTeamID = () => parseInt(document.getElementById("input-teamid").value);
const cAudioRecvRange = () => parseInt(document.getElementById("input-range").value);
const cAudioMode = () => parseInt(document.getElementById("select-audio-mode").value);
const LocalDebugAuth = false;
window.onload = function () {
    var tmp_tinyid = genRandNumId();
    document.getElementById("input-userid").value = tmp_tinyid;
};

window.onload = function () {
    var tmp_tinyid = genRandNumId();
    document.getElementById("input-userid").value = tmp_tinyid;
};

<!--Step 3: New GME并预写GME的回调函数-->
let gmeAPI = WebGMEAPI.getInstance();
let onEvent = function (eventType, result) {

    switch (eventType) {
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_ENTER_ROOM:
            console.info(`client receive GME msg:eventType:${eventType},result.errorCode:${result.errorCode},result.errorMsg:${result.errorMsg}.`);
            if (result.errorCode == 0){
                enableUI(true);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_USER_UPDATE:
            if (result.type == 1) {
                console.info(`user:${result.userId} enter room`);
            }
            else {
                console.info(`user:${result.userId} exit room`);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_EXIT_ROOM:
            console.info(`client receive GME msg:eventType:${eventType},result.errorCode:${result.errorCode},result.errorMsg:${result.errorMsg}.`);
            enableUI(false);
    
            stream_infos.clear();
            $("#upload_stream_canvas").empty();
            $("#download_streams_canvas").empty();
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_ROOM_DISCONNECT:
            console.info(`client receive GME msg:eventType:${eventType},result.errorCode:${result.errorCode},result.errorMsg:${result.errorMsg}.`);
            enableUI(false);
            stream_infos.clear();
            $("#upload_stream_canvas").empty();
            $("#download_streams_canvas").empty();
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_LOCALSTREAM_READY:
            if(result.errorCode === 0)
            {
                console.info('result local stream ok!');
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_MICROPHONE_ENABLE_RESULT:
            if (result.errorCode === 1)
            {
                $("#open_autio_btn").attr("disabled",true);
                $("#close_autio_btn").attr("disabled",false);
                addStreamCanvas(`${cUserID()}`, `UploadStream:${cUserID()}`, 'upload_stream_canvas');
    
            }
            else if (result.errorCode === 0)
            {
                $("#open_autio_btn").attr("disabled",false);
                $("#close_autio_btn").attr("disabled",true);
                removeStreamCanvas(`${cUserID()}`);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_SPEAKER_ENABLE_RESULT:
            if (result.errorCode === 1)
            {
                $("#open_speaker").attr("disabled",true);
                $("#close_speaker").attr("disabled",false);
            }
            else if (result.errorCode === 0)
            {
                $("#open_speaker").attr("disabled",false);
                $("#close_speaker").attr("disabled",true);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_REMOTESTREAM_IN:
            const remote_stream = result;
            addStreamCanvas(`${result}`, `DownloadStream:${result}`, 'download_streams_canvas');
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_REMOTESTREAM_OUT:
            removeStreamCanvas(`${result}`);
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_REMOTESTREAM_PAUSED:
            $("#open_speaker").attr("disabled",true);
            $("#close_speaker").attr("disabled",true);
            $('<button>')
                .attr('id', 'btnResumeRemote')
                .attr('text', 'ResumeRemote')
                .appendTo('#app');
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_START_ACCOMPANY:
            if (result.errorCode === 0)
            {
                console.info('start accompany success');
                $("#select-accompany-volume").attr("disabled",false);
            }
            else
            {
                console.info(`start accompany failed, the reason is ,result.errorCode:${result.errorCode},result.errorMsg:${result.errorMsg}.`);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_ACCOMPANY_FINISH:
            $("#select-accompany-volume").attr("disabled",true);
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_SET_ACCOMPANY_VOLUME:
            if (result.errorCode === 0)
            {
                console.info('set accompany volume success');
            } else {
                console.info(`set accompany failed, the reason is ,result.errorCode:${result.errorCode},result.errorMsg:${result.errorMsg}.`);
            }
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_AUDIO_VOLUME:
            renderSelfFrame(result)
            break;
        case WebGMEAPI.event.ITMG_MAIN_EVENT_TYPE_DEVICE_CHANGED:
            if (result.type == "microphone") {
                onUpdateMicrophones();
            }
            else if (result.type == "speaker") {
                onUpdateSpeakers();
            }
            break;
        default:
            break
    }
};

<!--Step 5: 初始化GME-->
function initGMEWithSig(appID, userSig, privateMapKey) {
    gmeAPI.init(appID, cUserID());
    gmeAPI.setLogLevel(1);
    gmeAPI.setTMGDelegate(onEvent);
    gmeAPI.setRangeAudioTeamID(cTeamID());
    gmeAPI.setRangeAudioMode(cAudioMode());
    gmeAPI.enterRoom(cRoomNum(), 1, userSig, privateMapKey);
    currentUserId = cUserID();
}

function onUpdateMicrophones() {
    gmeAPI.getMicrophones().then(devices => {
        var select = document.getElementById("microphoneId");
        select.options.length = 0;

        devices.forEach(device => {
            if(device.deviceId && device.deviceId !== "") {
                $('<option/>', {
                    value: device.deviceId,
                    text: device.label
                }).appendTo('#microphoneId');
            }
        });
    });
}

function onUpdateSpeakers() {
    gmeAPI.getSpeakers().then(devices => {
        var selectSpeaker = document.getElementById("speakerId");
        selectSpeaker.length = 0;
        
        devices.forEach(device => {
            if(device.deviceId && device.deviceId !== "") {
                $('<option/>', {
                    value: device.deviceId,
                    text: device.label
                }).appendTo('#speakerId');
            }
        });
    });
}

function onClickEnterRoom()
{
    onUpdateMicrophones();
    onUpdateSpeakers();

    if (LocalGenUserSig) {
        let userSig; let privateMapKey;
        let gen = genTestUserSig(cUserID());
        userSig = gen.userSig;
        let appID = gen.sdkAppId;
        initGMEWithSig(appID, userSig, privateMapKey);
    }
    else {
        var FetchSigCgi = 'https://service.trtc.qcloud.com/release/UserSigService';
        $.ajax({
            type: "POST",
            url: FetchSigCgi,
            dataType: 'json',
            data: JSON.stringify({
                pwd: SECRETKEY,
                appid: SDKAPPID,
                roomnum: parseInt(cRoomNum()),
                privMap: 255,
                identifier: cUserID(),
                accounttype: 14418
            }),
            success: function (json) {
                //步骤2, 获取AuthBuffer成功
                if (json && json.errorCode === 0) {
                    const userSig = json.data.userSig;
                    const privateMapKey = json.data.privMapEncrypt;
                    initGMEWithSig(SDKAPPID, userSig, privateMapKey)
                } else {
                    console.error(json);
                }
            },
            error: function (err) {
                console.error(err);
            }
        });
    }
}

function bindButtonEvents() {
    $("#teamid_btn").click(function () {
        gmeAPI.setRangeAudioTeamID(cTeamID());
    });
    $("#start_btn").click(function () {
        onClickEnterRoom();
    });

    $("#quit_btn").click(function () {
        gmeAPI.exitRoom();
    });

    $("#open_speaker").click(function () {
        gmeAPI.enableSpeaker(true);
    });

    $("#close_speaker").click(function () {
        gmeAPI.enableSpeaker(false);
    });

    $("#open_autio_btn").click(() => {
        gmeAPI.enableMic(true, document.getElementById("microphoneId").value);
    });

    $("#close_autio_btn").click(() => {
        gmeAPI.enableMic(false);
    });

    $("#btnResumeRemote").click(()=>{
        gmeAPI.resumeAllRemoteStreams();
        console.info('result remote stream ok!');
        $("#open_speaker").attr("disabled",true);
        $("#close_speaker").attr("disabled",false);
        if ($('#btnResumeRemote')[0]) {
            $('#btnResumeRemote').remove();
        }
    });

    $("#start_acc_btn").click(() => {
        audioInput = document.getElementById('uploadedFile');
        const musicID = document.getElementById("input-musicid").value;
        const musicURL = document.getElementById("input-musicurl").value;
        const loopBack = document.getElementById("loop-play").checked;
        const valume = document.getElementById("select-accompany-volume").value;
        gmeAPI.startPlayMusic(musicID, musicURL, loopBack == 1, Number(valume));
    });

    $("#stop_acc_btn").click(() => {
        gmeAPI.stopPlayMusic(document.getElementById("input-musicid").value);
    });

    $('#loop-play').on('change', () => {
        if ($('#loop-play')[0].checked) {
        } else {
        }
    });

    $("#pause_acc_btn").click(() => {
        gmeAPI.pausePlayMusic(document.getElementById("input-musicid").value);
    });


    $("#resume_acc_btn").click(() => {
        gmeAPI.resumePlayMusic(document.getElementById("input-musicid").value);
    });


    $("#seek_acc_btn").click(() => {
        gmeAPI.seekMusicToPosInTime(document.getElementById("input-musicid").value, parseInt(document.getElementById("input-seek").value));
    });
}

var app = new Vue({
    el: '#app',
    data: {
        local_bytesSent: '?',
        local_packetsSent: '?',
        rtt: '?',
        downStreamInfoList: [{
            bytesReceived: '?',
            jitter: '?',
            packetsReceived: '?',
            packetsLost: '?'
        }]
    },
    methods: {
        //
    },
    mounted: () => {
        bindButtonEvents();
    }
});
