const merge = require('webpack-merge');

const commonConfig = require('./webpack.common.js');

module.exports = merge(commonConfig, {
  mode: 'production', // 设置Webpack的mode模式

  // 生产环境下需要的相关插件配置
  plugins: [

  ],
  optimization: {
    minimize: true, // 开发环境不压缩
    splitChunks: { // 拆分代码
      chunks: 'all', // initial: 初始模块； async：按需加载模块; all: 全部模块
      minSize: 30000, // 模块超过3k自动被抽离成公共模块
      minChunks: 1, // 模块被引用大于或等于1，便分割
      maxAsyncRequests: 5, // 异步加载chunk的并发请求数量小于或等于5
      maxInitialRequests: 3, // 一个入口并发加载的chunks数量小于或等于3
      name: true, // 默认由模块名+hash命名，名称相同时多个模块将合并为一个，可以设置为function
      automaticNameDelimiter: '~', // 命名分隔符
      cacheGroups: { // 缓存组，会继承和覆盖splitChunks的配置
        vendor: {
          test: /[\\/]node_modules[\\/](react|react-dom|trtc-js-sdk)[\\/]/,
          name: 'vendor',
          chunks: 'all',
        },
      },
    },
  },

});
