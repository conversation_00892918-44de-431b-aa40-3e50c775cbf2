import React, { useState, useEffect } from 'react';
import Cookies from 'js-cookie';
import { NETWORK_QUALITY } from '../utils';
import { useTranslation } from 'react-i18next';
import Button from '@material-ui/core/Button';
import axios from 'axios';
import TRTC from 'trtc-js-sdk';
import Loading from '../loading';
import './index.scss';

const pwd = '12345678';
const sdkAppId = 1400188366;
const fetchUrl = 'https://demo-2gfpjobv0b7026e1-1256993030.ap-shanghai.app.tcloudbase.com/userSigService';

let uplinkClient = null;
let downlinkClient = null;
let upStream = null;

const networkResult = {
	uplink: [],
	downlink: [],
	rtt: [],
};
let intervalId = -10;

const getUserSig = async ({ roomId, userId, phone, token }) => {
	const response = await axios.post(fetchUrl, {
		pwd,
		appid: sdkAppId,
		roomnum: parseInt(roomId),
		identifier: userId,
		phone,
		token,
	});
	if (response && response.data.errorCode === 0) {
		const { userSig } = response.data.data;
		return userSig;
	}
	console.error(`got invalid json:${response}`);
};

export default function Network(props) {
	const { t } = useTranslation();
	const [stage, setStage] = useState(0);
	const [token, setToken] = useState();
	const [count, setCount] = useState(15);
	const [phone, setPhone] = useState();
	const [uplink, setUplink] = useState(0);
	const [downlink, setDownlink] = useState(0);
	const [rtt, setRtt] = useState(0);

	useEffect(() => {
		const checkToken = async () => {
			console.log('checkToken');
			const trtcToken = Cookies.get('trtc-api-example-token');
			const phoneNumber = Cookies.get('trtc-api-example-phoneNumber');
			if (trtcToken && phoneNumber) {
				setToken(trtcToken);
				setPhone(phoneNumber);
				await initClient(phoneNumber, trtcToken);
				if (intervalId < 0) {
					setCount(15);
					initTimer();
				}
			} else {
				window.location = `${window.location.origin}/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html`;
			}
		};
		checkToken();
	}, []);

	const relogin = async () => {
		window.location = `${window.location.origin}/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html`;
		const trtcToken = Cookies.get('trtc-token');
		const phoneNumber = Cookies.get('phoneNumber');
		if (trtcToken && phoneNumber) {
			setToken(trtcToken);
			setPhone(phoneNumber);
			await initClient(phoneNumber, trtcToken);
			if (intervalId < 0) {
				setCount(15);
				initTimer();
			}
		}
	};

	const initClient = async (phone, token) => {
		const userUp = `rtc_detect_up${Math.random() * 100000000}`;
		const userDown = `rtc_detect_down${Math.random() * 100000000}`;
		const roomId = `rtc_detect_${Math.random() * 100000000}`;
		const userSigUp = await getUserSig({ roomId, userId: userUp, phone, token });
		const userSigDown = await getUserSig({ roomId, userId: userDown, phone, token });
		if (!userSigUp || !userSigDown) {
			setStage(-1);
			return;
		}
		uplinkClient = TRTC.createClient({
			sdkAppId,
			useStringRoomId: true,
			userId: userUp,
			userSig: userSigUp,
			mode: 'rtc',
		});
		upStream = TRTC.createStream({ audio: true, video: true });
		await upStream.initialize();
		uplinkClient.on('network-quality', async (event) => {
			const { uplinkNetworkQuality } = event;
			const { rtt } = await uplinkClient.getTransportStats();
			networkResult.uplink.push(uplinkNetworkQuality);
			networkResult.rtt.push(rtt);
		});
		await uplinkClient.join({ roomId }); // 加入用于测试的房间
		await uplinkClient.publish(upStream);

		downlinkClient = TRTC.createClient({
			sdkAppId,
			useStringRoomId: true,
			userId: userDown,
			userSig: userSigDown,
			mode: 'rtc',
		});
		downlinkClient.on('stream-added', async (event) => {
			await downlinkClient.subscribe(event.stream, { audio: true, video: true });
			downlinkClient.on('network-quality', (event) => {
				const { downlinkNetworkQuality } = event;
				networkResult.downlink.push(downlinkNetworkQuality);
			});
		});

		await downlinkClient.join({ roomId }); // 加入用于测试的房间
	};

	const initTimer = () => {
		intervalId = setInterval(() => {
			setCount((prevValue) => {
				const newValue = prevValue - 1;
				if (newValue === 0) {
					getAverageInfo();
					clearInterval(intervalId);
					return 0;
				}
				return newValue;
			});
		}, 1000);
	};
	// 获取15秒检测平均值
	const getAverageInfo = () => {
		const uplinkAverage = Math.ceil(networkResult.uplink
			.reduce((value, current) => value + current, 0) / networkResult.uplink.length);
		const downlinkAverage = Math.ceil(networkResult.downlink
			.reduce((value, current) => value + current, 0) / networkResult.downlink.length);
		const rttAverage = Math.ceil(networkResult.rtt
			.reduce((value, current) => value + current, 0) / networkResult.rtt.length);
		setUplink(uplinkAverage);
		setDownlink(downlinkAverage);
		setRtt(rttAverage);
		props.handleReport({
			uplinkAverage,
			downlinkAverage,
			rttAverage,
		});
		uplinkClient && uplinkClient.leave();
		downlinkClient && downlinkClient.leave();
		upStream.getAudioTrack().stop();
		upStream.getVideoTrack().stop();
	};


	return (
		<div className="wrapper">
			{
				stage > -1 && (
					count > 0
						? (
							<div className="loading-time">
								<Loading/>
								<div className="count-down">{t('剩余检测时间')} {count} s</div>
							</div>
						)
						: (
							<div className="wrapper-list">
								<div className="item-container">
									<div>{t('网络延时')}</div>
									<div>{rtt}ms</div>
								</div>
								<div className="item-container">
									<div>{t('上行网络质量')}</div>
									<div>{t(NETWORK_QUALITY[uplink])}</div>
								</div>
								<div className="item-container">
									<div>{t('下行网络质量')}</div>
									<div>{t(NETWORK_QUALITY[downlink])}</div>
								</div>
							</div>
						)
				)
			}
			{
				stage === -1 && (
					<React.Fragment>
						<div className="login-failed">{t('登录失败')}</div>
						<div className="re-login">
							<Button color="default" variant="contained" onClick={() => relogin()}>{t('重新登录')}</Button>
						</div>
					</React.Fragment>
				)
			}
		</div>
	);
}
