.header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content:space-between;
  user-select: none;

  > img {
    height: 36px;
    @media (max-width: 400px) {
      height: 28px;
    }
  }
  .title {
    padding: 0 40px;
    line-height: 64px;
    overflow: hidden;
    width: 100%;
  }

  .qrcode {
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
  }

  .button-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 0 40px;
  }

  .icon-lang {
    padding: 10px;
    cursor: pointer;
    user-select: none;
  }
}

.phone {
  padding: 6px;
}
