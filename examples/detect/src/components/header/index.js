import React, { useState, useEffect } from 'react';
import './index.scss';
import Grid from '@material-ui/core/Grid';
import Popover from '@material-ui/core/Popover';
import { useTranslation } from 'react-i18next';
import QRCode from 'qrcode.react';
import { getLanguage } from '../utils';


function Header() {
	const [lang, setLang] = useState('en');
	const [anchorEl, setAnchorEl] = React.useState(null);
	const { i18n } = useTranslation();

	useEffect(() => {
		handleLang(getLanguage());
	}, []);

	const handleLang = (lang) => {
		i18n.changeLanguage(lang);
		localStorage.setItem('trtc-detect-language', lang);
		document.title = lang === 'zh' ? 'TRTC 能力检测' : 'TRTC Capability Detection';
		setLang(lang);
	};

	const handleClick = (event) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	const id = open ? 'simple-popover' : undefined;

	return (
		<Grid container justifyContent="space-between">
			<Grid item>
				<div className="header">
					{
						lang === 'zh' && (
							<img src={'https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-cn-b.png'} />
						)
					}
					{
						lang === 'en' && (
							<img src={'https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-en-b.png'} />
						)
					}
				</div>
			</Grid>
			<Grid item>
				<div className="header">
					{
					 	window.innerWidth > 385 && <div className="qrcode" onClick={handleClick} aria-describedby={id}>
							<svg t="1646118025166" className="icon" viewBox="0 0 1024 1024" version="1.1"
							     xmlns="http://www.w3.org/2000/svg" p-id="1245" width="24" height="24">
								<path
									d="M491.925333 532.074667V853.333333H170.666667V532.074667h321.258666zM597.333333 789.333333v64h-64v-64h64z m256-85.333333v149.333333h-128v-64h64v-85.333333h64z m-425.408-107.925333H234.666667V789.333333h193.258666v-193.258666zM661.333333 597.333333v128h64v64h-128v-192h64z m-277.333333 42.666667v106.666667h-106.666667v-106.666667h106.666667z m405.333333 0v64h-64v-64h64z m64-106.666667v106.666667h-64v-42.666667h-64v-64h128z m-256 0v64h-64v-64h64zM491.925333 170.666667v321.258666H170.666667V170.666667h321.258666zM853.333333 170.666667v321.258666H532.074667V170.666667H853.333333z m-425.408 64H234.666667v193.258666h193.258666V234.666667zM789.333333 234.666667h-193.258666v193.258666H789.333333V234.666667z m-405.333333 42.666666v106.666667h-106.666667v-106.666667h106.666667z m362.666667 0v106.666667h-106.666667v-106.666667h106.666667z"
									p-id="1246" fill="#b9b9b9"></path>
							</svg>
						</div>
					}
					{
						lang === 'zh' && (
							<div className="icon-lang" onClick={() => handleLang('en')}>
								<img src={'https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png'} height="24px"/>
							</div>
						)
					}
					{
						lang === 'en' && (
							<div className="icon-lang" onClick={() => handleLang('zh')}>
								<img src={'https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/en.png'} height="24px"/>
							</div>
						)
					}
				</div>
				<Popover
					id={id}
					open={open}
					anchorEl={anchorEl}
					onClose={handleClose}
					anchorOrigin={{
						vertical: 'bottom',
						horizontal: 'center',
					}}
					transformOrigin={{
						vertical: 'top',
						horizontal: 'center',
					}}
				>
					<div className="phone">
						<QRCode
							value={window.location.href}
							size={200}
							fgColor="#000000"/>
					</div>
				</Popover>
			</Grid>
		</Grid>
	);
}

export default Header;
