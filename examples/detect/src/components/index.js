/* eslint-disable max-len */
import TRTC from 'trtc-js-sdk';
import React, { useState, useEffect } from 'react';
import Basic from './basic';
import Loading from './loading';


const isBoolean = param => typeof param === 'boolean';

export default function Index(props) {
	const [status, setStatus] = useState(false);
	const [mark, setMark] = useState({});

	useEffect(() => {
		const checkSystem = async () => {
			const localVideoStream = TRTC.createStream({
				video: true,
				audio: false,
			});
			try {
				await localVideoStream.initialize();
				mark.video = true;
				setMark(mark);
				localVideoStream && localVideoStream.close();
			} catch (error) {
				mark.video = false;
				setMark(mark);
				switch (error.name) {
					case 'NotReadableError':
						console.log(error.name);
						break;
					case 'NotAllowedError':
						if (error.message.includes('Permission denied by system')) {
						} else {
							console.log('You refused to share the screen');
						}
						console.log('Permission denied by system');
						break;
					default:
						break;
				}
			}
			const localAudioStream = TRTC.createStream({
				video: false,
				audio: true,
			});
			try {
				await localAudioStream.initialize();
				mark.audio = true;
				setMark(mark);
				localAudioStream && localAudioStream.close();
			} catch (error) {
				mark.audio = false;
				setMark(mark);
				switch (error.name) {
					case 'NotReadableError':
						console.log(error.name);
						break;
					case 'NotAllowedError':
						if (error.message.includes('Permission denied by system')) {
						} else {
							console.log('You refused to share the screen');
						}
						console.log('Permission denied by system');
						break;
					default:
						break;
				}
			}
			if (isBoolean(mark.video) && isBoolean(mark.audio)) {
				setStatus(true);
			}
		};
		checkSystem();
	}, []);

	return (
		<div className="block">
			{
				status
					? <Basic mark={mark} handleLoaded={props.handleLoaded}/>
					: <Loading />
			}
		</div>
	);
}
