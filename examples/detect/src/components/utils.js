
export const isUndefined = param => typeof param === 'undefined';
export const isBoolean = param => typeof param === 'boolean';


// 网络参数对照表
export const NETWORK_QUALITY = {
	0: '未知',
	1: '极佳',
	2: '较好',
	3: '一般',
	4: '差',
	5: '极差',
	6: '断开',
};

export function getUrlParam(key) {
	const url = decodeURI(window.location.href.replace(/^[^?]*\?/, ''));
	const regexp = new RegExp(`(^|&)${key}=([^&#]*)(&|$|)`, 'i');
	const paramMatch = url.match(regexp);
	return paramMatch ? paramMatch[2] : null;
};

export function getLanguage(localStorageLangId = 'trtc-detect-language') {
	let lang = getUrlParam('lang') || localStorage.getItem(localStorageLangId) || window.navigator.language?.toLowerCase();
	lang = lang.indexOf('zh') > -1 ? 'zh' : 'en';
	console.warn(lang);
	return lang;
}
