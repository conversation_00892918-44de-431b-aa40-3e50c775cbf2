/* eslint-disable max-len */
import React, { useState, useEffect } from 'react';
import './index.scss';
import RTCDetect from 'rtc-detect';
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import { makeStyles } from '@material-ui/core';
import Device from '../device';
import Network from '../network';
import { useTranslation } from 'react-i18next';
import Loading from '../loading';
import BlueBtn from '../blueBtn';

const getQueryVariable = (variable) => {
  const query = window.location.search.substring(1);
  const vars = query.split('&');
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=');
    if (pair[0] === variable) {
      return pair[1];
    }
  }
  return false;
};

const aegis = new window.Aegis({
  id: 'CUURTHMqVFbioiGGSC', // 项目key
  reportApiSpeed: true, // 接口测速
  reportAssetSpeed: true, // 静态资源测速
  hostUrl: 'https://tamaegis.com',
  pagePerformance: true, // 开启页面测速
  spa: true,
});

const rtcDetect = new RTCDetect();

const wrapperStyles = makeStyles(() => ({
  root: {
    width: '100%',
    boxShadow: 'none',
    border: '1px solid #ddd',
    backgroundImage: 'url(https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/mark.png)',
  },
}));

const detailStyles = makeStyles(() => ({
  root: {
    padding: '8px 16px',
  },
}));

const summaryStyles = makeStyles(() => ({
  root: {
    color: 'white',
    backgroundColor: '#2196f3',
    minHeight: '0 !important',
    cursor: 'default !important',
    lineHeight: '22px',
  },
  content: {
    margin: '10px 0 !important',
  },
  expanded: {
    margin: '0 !important:',
    minHeight: '0px',
  },
}));


function createList(elements, index) {
  const { t } = useTranslation();

  let id = index;
  const keys = Object.keys(elements);
  return (
    <div>
      {
        keys.map((key) => {
          id += 1;
          return (
            // eslint-disable-next-line react/jsx-key
            <React.Fragment key={`${key}wrapper`}>
              {
                typeof elements[key].result === 'string'
                  ? <div key={key} className="key-item">
                    {id ? <div className="id">{id}</div> : null}
                    <div className="key">{t(elements[key].title)}：</div>
                    {t(elements[key].result)}
                  </div>
                  : <div key={key} className="key-item">
                    {id ? <div className={elements[key].result ? 'id' : 'id-failed'}>{id}</div> : null}
                    <div className="key">{t(elements[key].title)}：
                      {(elements[key].result ? <span className="support">{t('是')}</span>
                        : <span className="support-failed">{t('否')}</span>)}
                    </div>
                  </div>
              }
              {
                elements[key].desc && <div className="desc">{t(elements[key].desc)}</div>
              }
            </React.Fragment>
          );
        })
      }
    </div>
  );
}


export default function Basic(props) {
  const wrapperClasses = wrapperStyles();
  const summaryClasses = summaryStyles();
  const detailClasses = detailStyles();
  const [basic, setBasic] = useState({});
  const [codec, setCodec] = useState({});
  const [api, setApi] = useState({});
  const [stage, setStage] = useState(0);
  const [stageNetwork, setStageNetwork] = useState(0);

  const [audioInput, setAudioInput] = useState();
  const [audioOutput, setAudioOutput] = useState();
  const [videoInput, setVideoInput] = useState();
  const [cam, setCam] = useState();
  const [microphone, setMicrophone] = useState();
  const [device, setDevice] = useState();

  const [TRTC, setTRTC] = useState();

  const [loading, setLoading] = useState(true);
  const [openNetwork, setOpenNetwork] = useState(false);
  const { t } = useTranslation();

  const lists = {
    basic: {
      title: t('基础环境'),
      params: basic,
    },
    api: {
      title: t('API 支持'),
      params: api,
    },
    codec: {
      title: t('编码支持'),
      params: codec,
    },
  };


  useEffect(() => {
    if (getQueryVariable('network')) {
      setOpenNetwork(true);
    }
    const checkSystem = async () => {
      const isTRTCSupported = await rtcDetect.isTRTCSupported();
      setTRTC(isTRTCSupported);

      const { report } = rtcDetect;
      if (!report) {
        return;
      }
      setLoading(false);
      props.handleLoaded();

      const { system, APISupported, codecsSupported, devices } = report;
      aegis.infoAll(report);
      const {
        isH264DecodeSupported,
        isH264EncodeSupported,
        isVp8DecodeSupported,
        isVp8EncodeSupported,
      } = codecsSupported;
      const codec = {
        h264Encode: {
          title: 'H264 编码',
          desc: '浏览器支持 H264 编码，则可以支持推本地流',
          result: isH264EncodeSupported,
        },
        h264Decode: {
          title: 'H264 解码',
          desc: '浏览器支持 H264 解码，则可以支持拉远端流',
          result: isH264DecodeSupported,
        },
        vp8Encode: {
          title: 'VP8 编码',
          result: isVp8EncodeSupported,
        },
        vp8Decode: {
          title: 'VP8 解码',
          result: isVp8DecodeSupported,
        },
      };
      setCodec(codec);

      const {
        UA,
        OS,
        browser: { name, version },
        displayResolution: { width, height },
        hardwareConcurrency,
      } = system;

      const basic = {
        OS: {
          title: '操作系统',
          result: OS,
        },
        browser: {
          title: '浏览器',
          result: `${name}/${version || ''}`,
        },
        UA: {
          title: 'UA',
          result: UA,
        },
        resolution: {
          title: '屏幕分辨率',
          result: `${width} x ${height}`,
        },
      };
      if (hardwareConcurrency) {
        basic.hardwareConcurrency = {
          title: '逻辑处理器数量',
          result: `${hardwareConcurrency}`,
        };
      }
      setBasic(basic);

      const {
        isUserMediaSupported,
        isWebRTCSupported,
        isWebSocketSupported,
        isWebAudioSupported,
        isScreenCaptureAPISupported,
        isCanvasCapturingSupported,
        isVideoCapturingSupported,
        isApplyConstraintsSupported,
      } = APISupported;
      const api = {
        isUserMediaSupported: {
          title: '是否支持获取媒体设备及媒体流',
          desc: '是否可以从摄像头和麦克风采集视频和音频',
          result: isUserMediaSupported,
        },
        isScreenCaptureAPISupported: {
          title: '是否支持屏幕分享',
          result: isScreenCaptureAPISupported,
        },
        isWebRTCSupported: {
          title: '是否支持 WebRTC',
          result: isWebRTCSupported,
        },
        isWebAudioSupported: {
          title: '是否支持 WebAudio',
          result: isWebAudioSupported,
        },
        isWebSocketSupported: {
          title: '是否支持 WebSocket',
          result: isWebSocketSupported,
        },
        isWebCodecsSupported: {
          title: '是否支持 WebCodecs',
          desc: `音频编码 ${'AudioEncoder' in window ? 'Yes' : 'No'}, 视频编码 ${'VideoEncoder' in window ? 'Yes' : 'No'}, 音频解码 ${'AudioDecoder' in window ? 'Yes' : 'No'}, 视频解码 ${'VideoDecoder' in window ? 'Yes' : 'No'}`,
          result: 'AudioEncoder' in window && 'VideoEncoder' in window && 'AudioDecoder' in window && 'VideoDecoder' in window,
        },
        isMediaStreamTrackGeneratorSupported: {
          title: '是否支持 MediaStreamTrackGenerator',
          result: 'MediaStreamTrackGenerator' in window,
        },
        isMediaStreamTrackProcessorSupported: {
          title: '是否支持 MediaStreamTrackProcessor',
          result: 'MediaStreamTrackProcessor' in window,
        },
        isCanvasCapturingSupported: {
          title: '是否支持从 Canvas 获取数据流',
          result: isCanvasCapturingSupported,
        },
        isVideoCapturingSupported: {
          title: '是否支持从 Video 获取数据流',
          result: isVideoCapturingSupported,
        },
        isApplyConstraintsSupported: {
          title: '是否支持 applyConstraints',
          desc: 'MediaStreamTrack 是否有 applyConstraints 方法',
          result: isApplyConstraintsSupported,
        },
      };
      setApi(api);

      const {
        hasCameraPermission,
        hasMicrophonePermission,
        cameras,
        microphones,
        speakers,
      } = devices;

      const device = {
        cam: {
          title: '是否允许使用摄像头',
          desc: '是否允许使用摄像头',
          result: hasCameraPermission || false,
        },
        microphone: {
          title: '是否允许使用麦克风',
          desc: '是否允许使用麦克风',
          result: hasMicrophonePermission || false,
        },
      };
      setDevice(device);

      setAudioInput(microphones);
      setAudioOutput(speakers);
      setVideoInput(cameras);
      setCam(hasCameraPermission);
      setMicrophone(hasMicrophonePermission);
    };
    checkSystem();
  }, []);

  const handleReport = (report) => {
    aegis.infoAll(report);
  };

  const joinRoomSupported = api?.isWebRTCSupported?.result && api?.isWebSocketSupported?.result;
  const mediaSupported = api?.isCanvasCapturingSupported?.result || api?.isVideoCapturingSupported?.result || device?.cam?.result || device?.cam?.result;
  const publishSupported = joinRoomSupported && codec?.h264Encode?.result && mediaSupported;
  const subscribeSupported = joinRoomSupported && codec?.h264Decode?.result;

  return (
    <div className="block">
      {
        loading && (
          <div className="cover">
            <Loading />
          </div>
        )
      }
      <div className="panel-item">
        <Accordion expanded square classes={wrapperClasses}>
          <AccordionSummary
            aria-controls="panel1a-content"
            id="panel1a-header"
            classes={summaryClasses}
          >
            <div className="key">{t('检测结果概览')}</div>
          </AccordionSummary>
          <AccordionDetails classes={detailClasses}>
            <div>
              <div className="key" style={{ margin: '1em 0', color: '#F56C6C' }}>
                {t('http')}<a style={{ textDecoration: 'none', color: '#2196f3' }} target="_blank"
                  href="https://cloud.tencent.com/document/product/647/32398#url-.E5.9F.9F.E5.90.8D.E5.8D.8F.E8.AE.AE.E9.99.90.E5.88.B6"
                  rel="noreferrer">{t('refer')}</a>
              </div>
              <div className="key-item">
                <div className="key">{t('是否支持 TRTC')}：
                  {
                    TRTC && TRTC.result
                      ? <span className="support">{t('是')}</span> : (
                        joinRoomSupported
                          ? <span className="support-half">{t('部分支持')}</span>
                          : <span className="support-failed">{t('否')}</span>)
                  }
                </div>
              </div>
              {TRTC && !TRTC.result && <span className="support-failed">{TRTC.reason}</span>}
              <div className="key-item" key={'join'}>
                <div className="key">{t('是否支持进房【 检测项 3 && 5 】')}：
                  {joinRoomSupported ? <span className="support">{t('是')}</span>
                    : <span className="support-failed">{t('否')}</span>
                  }
                </div>
              </div>
              <div className="key-item" key={'publish'}>
                <div className="key">{t('是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】')}：
                  {publishSupported ? <span className="support">{t('是')}</span>
                    : <span className="support-failed">{t('否')}</span>
                  }
                </div>
              </div>
              <div
                className="publish-desc">{t('如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。')}</div>
              <div className="key-item" key={'subscribe'}>
                <div className="key">{t('是否支持拉流【 检测项 3 && 5 && 12 】')}：
                  {subscribeSupported ? <span className="support">{t('是')}</span>
                    : <span className="support-failed">{t('否')}</span>
                  }
                </div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>
      </div>
      {
        Object.keys(lists)
          .map(key => (
            <div key={key} className="panel-item">
              <Accordion expanded square classes={wrapperClasses}>
                <AccordionSummary
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  classes={summaryClasses}
                >
                  <div className="key">{lists[key].title}</div>
                </AccordionSummary>
                <AccordionDetails classes={detailClasses}>
                  {key === 'basic' && createList(lists[key].params)}
                  {key === 'api' && createList(lists[key].params, 0)}
                  {key === 'codec' && createList(lists[key].params, 10)}
                </AccordionDetails>
              </Accordion>
            </div>
          ))
      }
      <div className="panel-item">
        <Accordion expanded square classes={wrapperClasses}>
          <AccordionSummary
            aria-controls="panel1a-content"
            id="panel1a-header"
            classes={summaryClasses}
          >
            <div className="key">{t('设备详情')}</div>
          </AccordionSummary>
          <AccordionDetails classes={detailClasses}>
            <div className="device-info">
              <div className="key-item">
                <div className={cam ? 'id' : 'id-failed'}>15</div>
                <div className="key">{t('是否允许使用摄像头')}：
                  {cam
                    ? <span className="support">{t('是')}</span>
                    : <span className="support-failed">{t('否')}</span>
                  }
                </div>
              </div>
              <div className="key-item">
                <div className={microphone ? 'id' : 'id-failed'}>16</div>
                <div className="key">{t('是否允许使用麦克风')}：
                  {microphone
                    ? <span className="support">{t('是')}</span>
                    : <span className="support-failed">{t('否')}</span>
                  }
                </div>
              </div>
              {
                audioInput && audioInput.length > 0 && (
                  <div className="device-list">
                    <div className="device-card-title">{t('麦克风设备列表')}</div>
                    <div className="device-item">
                      {
                        audioInput?.map((item, index) => (
                          <div key={item.deviceId + index}>{item.label}</div>
                        ))
                      }
                    </div>
                  </div>
                )
              }
              {
                videoInput && videoInput.length > 0 && (
                  <div className="device-list">
                    <div className="device-card-title">{t('摄像头设备列表')}</div>
                    <div className="device-item">
                      {
                        videoInput?.map((item, index) => (
                          <div key={item.deviceId + index}>
                            {item.label}&nbsp;&nbsp;&nbsp;&nbsp;
                            {item.resolution && item.resolution.maxWidth ? `${t('最大分辨率')}：${item.resolution.maxWidth} x ${item.resolution.maxHeight}` : ''}&nbsp;&nbsp;&nbsp;&nbsp;
                            {item.resolution && item.resolution.maxFrameRate ? `${t('最大帧率')}：${item.resolution.maxFrameRate.toFixed(0)}` : ''}
                          </div>
                        ))
                      }
                    </div>
                  </div>
                )
              }
              {
                audioOutput && audioOutput.length > 0 && (
                  <div className="device-list">
                    <div className="device-card-title">{t('扬声器设备列表')}</div>
                    <div className="device-item">
                      {
                        audioOutput?.map((item, index) => (<div key={item.deviceId + index}>{item.label}</div>))
                      }
                    </div>
                  </div>
                )
              }
            </div>
          </AccordionDetails>
        </Accordion>
      </div>
      <div className="panel-item">
        <Accordion expanded square classes={wrapperClasses}>
          <AccordionSummary
            aria-controls="panel1a-content"
            id="panel1a-header"
            classes={summaryClasses}
          >
            <div className="key">{t('设备支持度检测')}</div>
          </AccordionSummary>
          <AccordionDetails classes={detailClasses}>
            {
              stage === 0 && (
                <div className="start-detect">
                  <BlueBtn onClick={() => setStage(1)}>
                    {t('开始检测')}
                  </BlueBtn>
                </div>
              )
            }
            {
              stage === 1 && (
                <Device mark={props.mark} />
              )
            }
          </AccordionDetails>
        </Accordion>
      </div>
      {
        openNetwork && (
          <div className="panel-item">
            <Accordion expanded square classes={wrapperClasses}>
              <AccordionSummary
                aria-controls="panel1a-content"
                id="panel1a-header"
                classes={summaryClasses}
              >
                <div className="key">{t('网络检测')}</div>
              </AccordionSummary>
              <AccordionDetails classes={detailClasses}>
                {
                  stageNetwork === 0 ? (
                    <div className="start-detect">
                      <BlueBtn onClick={() => setStageNetwork(1)}>
                        {t('开始检测')}
                      </BlueBtn>
                      <div className="tip">{t('检测网络前需要先登录，如果您没有登录的话会自动跳转登录')}</div>
                    </div>
                  ) : <Network handleReport={handleReport} />
                }
              </AccordionDetails>
            </Accordion>
          </div>
        )
      }
    </div>
  );
}
