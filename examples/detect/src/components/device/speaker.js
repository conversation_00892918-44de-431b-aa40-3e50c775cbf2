import React from 'react';
import Button from '@material-ui/core/Button';
import { useTranslation } from 'react-i18next';
import BlueBtn from '../blueBtn';

export default function Speaker(props) {
	const { t } = useTranslation();

	return (
		<div className="card">
			<div className="device-card-title">{t('扬声器检测')}</div>
			<div className="device-card-content">{t('点击播放后能否听到音乐')}</div>
			<div className="speaker-container">
				<div className="device-container">
					<audio
						src="https://web.sdk.qcloud.com/trtc/webrtc/assets/testspeak.mp3"
						controlsList="nodownload"
						controls
						loop
						crossOrigin="anonymous"
						className="audio"/>
				</div>
			</div>
			<div className="next">
				<Button color="default" size="small" variant="contained"
				        onClick={() => props.handleState({ speaker: false })}>否</Button>
				<BlueBtn color="default" size="small" variant="contained"
				        onClick={() => props.handleState({ speaker: true })}>是</BlueBtn>
			</div>
		</div>
	);
}
