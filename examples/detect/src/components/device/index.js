/* eslint-disable max-len */
import React, { useState } from 'react';
import './index.scss';
import Camera from './camera';
import Microphone from './microphone';
import Speaker from './speaker';
import CameraAltOutlinedIcon from '@material-ui/icons/CameraAltOutlined';
import MicNoneOutlinedIcon from '@material-ui/icons/MicNoneOutlined';
import VolumeUpOutlinedIcon from '@material-ui/icons/VolumeUpOutlined';
import { makeStyles } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import { isBoolean } from '../utils';

const iconStyles = makeStyles(() => ({
	root: {
		fontSize: '2em',
	},
}));


export default function Device(props) {
	const { t } = useTranslation();
	const [stage, setStage] = useState(0);
	const [camera, setCamera] = useState(props.mark.video);
	const [microphone, setMicrophone] = useState(props.mark.audio);
	const [speaker, setSpeaker] = useState();
	const icon = iconStyles();

	// microphone = 0 camera = 1 speaker = 2
	const handleState = async (options) => {
		if (isBoolean(options.microphone)) {
			setMicrophone(options.microphone);
			setStage(1);
		}
		if (isBoolean(options.camera)) {
			setCamera(options.camera);
			setStage(2);
		}
		if (isBoolean(options.speaker)) {
			setSpeaker(options.speaker);
			setStage(3);
		}
	};

	return (
		<div className="wrapper">
			{
				stage === 0 && (
					<div className="three-item">
						 <Microphone handleState={handleState}/>
					</div>
				)
			}
			{
				stage === 1 && (
					<div className="three-item">
					 <Camera handleState={handleState}/>
					</div>
				)
			}
			{
				stage === 2 && (
					<div className="three-item">
						<Speaker handleState={handleState}/>
					</div>
				)
			}
			{
				stage === 3 && (
					<div className="three-item">
						<div className="device-result-title">
							{t('检测完毕')}
						</div>
						<div className="device-result">
							<div className={microphone ? 'icon-support' : 'icon-support-failed'}>
								<MicNoneOutlinedIcon classes={icon}/>
							</div>
							<div className={camera ? 'icon-support' : 'icon-support-failed'}>
								<CameraAltOutlinedIcon classes={icon}/>
							</div>
							<div className={speaker ? 'icon-support' : 'icon-support-failed'}>
								<VolumeUpOutlinedIcon classes={icon}/>
							</div>
						</div>
					</div>
				)
			}
		</div>
	);
}
