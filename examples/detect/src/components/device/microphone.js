import React, { useState, useEffect } from 'react';
import Button from '@material-ui/core/Button';
import TRTC from 'trtc-js-sdk';
import { useTranslation } from 'react-i18next';
import BlueBtn from '../blueBtn';
import Select from 'react-select';

let intervalId = 0;
let localStream = null;
export default function Microphone(props) {
	const { t } = useTranslation();
	const [volume, setVolume] = useState(0);
	const [options, setOptions] = useState([]);
	const [selected, setSelected] = useState('default');

	useEffect(() => {
		const init = async () => {
			const devices = await TRTC.getMicrophones();
			devices.forEach((item) => {
				item.value = item.deviceId;
			});
			setOptions(devices);
			setSelected(devices[0]);
			localStream = TRTC.createStream({
				video: false,
				audio: true,
				microphoneId: devices[0].deviceId,
			});
			await localStream.initialize();
			localStream.play('audio-container');
			intervalId = setInterval(() => {
				const volume = localStream.getAudioLevel();
				setVolume(volume);
			}, 200);
		};
		init();
		return () => {
			clearInterval(intervalId);
		};
	}, []);

	const handleClick = (boolean) => {
		props.handleState({ microphone: boolean });
		localStream && localStream.close();
	};

	const handleChange = async (selectedOption) => {
		setSelected(selectedOption);
		localStream.switchDevice('audio', selectedOption.value)
			.then(() => console.log('switchDevice success'))
			.catch(() => console.log('switchDevice error'));
	};

	return (
		<div className="card">
			<div className="device-card-title">{t('麦克风检测')}</div>
			<div className="device-card-content">{t('是否看到音量条变化')}</div>
			<div className="device-switch">
				<Select
					value={selected}
					onChange={e => handleChange(e)}
					options={options}
					width='200px'
					menuColor='red'
				/>
			</div>
			<div className="device-content">
				<div className="volume-wrapper">
					<div id="audio-container" className="audio-container"/>
					<div className="volume" style={{ width: `${volume * 100}%` }}/>
				</div>
			</div>
			<div className="next">
				<Button color="default"
				        size="small"
				        variant="contained"
				        onClick={() => handleClick(false)}>
					{t('否')}
				</Button>
				<BlueBtn color="default"
				        size="small"
				        variant="contained"
				        onClick={() => handleClick(true)}>
					{t('是')}
				</BlueBtn>
			</div>
		</div>
	);
}
