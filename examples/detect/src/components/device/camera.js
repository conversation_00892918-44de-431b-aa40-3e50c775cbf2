import React, { useState, useEffect } from 'react';
import Button from '@material-ui/core/Button';
import TRTC from 'trtc-js-sdk';
import { useTranslation } from 'react-i18next';
import BlueBtn from '../blueBtn';
import Select from 'react-select';

let localStream;
export default function Camera(props) {
	const { t } = useTranslation();
	const [options, setOptions] = useState([]);
	const [selected, setSelected] = useState('default');

	useEffect(() => {
		const init = async () => {
			const devices = await TRTC.getCameras();
			devices.forEach((item) => {
			  item.value = item.deviceId;
			});
			setOptions(devices);
			setSelected(devices[0]);
			localStream = TRTC.createStream({
				video: true,
				audio: false,
				cameraId: devices[0].deviceId,
			});
			await localStream.initialize();
			localStream.play('video')
				.then(() => console.log('play success'))
				.catch(() => console.log('play error'));
		};
		init();
	}, []);

	const handleClick = (boolean) => {
		props.handleState({ camera: boolean });
		localStream && localStream.close();
	};

	const handleChange = async (selectedOption) => {
		setSelected(selectedOption);
		localStream.switchDevice('video', selectedOption.value)
			.then(() => console.log('switchDevice success'))
			.catch(() => console.log('switchDevice error'));
	};


	return (
		<div className="card">
			<div className="device-card-title">{t('摄像头检测')}</div>
			<div className="device-card-content">{t('是否看到视频画面')}</div>
			<div className="device-switch">
				<Select
					value={selected}
					onChange={e => handleChange(e)}
					options={options}
					width='200px'
					menuColor='red'
				/>
			</div>
			<div className="device-content">
				<div className="device-container">
					<div id="video" className="video"/>
				</div>
			</div>
			<div className="next">
				<Button color="default" size="small" variant="contained"
				        onClick={() => handleClick(false)}>{t('否')}</Button>
				<BlueBtn color="default" size="small" variant="contained"
				        onClick={() => handleClick(true)}>{t('是')}</BlueBtn>
			</div>
		</div>
	);
}
