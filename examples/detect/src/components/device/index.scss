.device-wrapper {
  padding-top: 10px;
  border-bottom: 1px solid #eee;
  width: 100%;
}

.device-card-title {
  padding: 8px 0 0 0;
  margin-right: 12px;
  color: rgba(0, 0, 0, .85);
  font-weight: 600;
  font-size: 14px;
}

.device-card-content {
  font-size: 14px;
  line-height: 22px;
}

.device-content {
  height: 120px;
  color: rgba(0, 0, 0, .7);
  width: 160px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.device-switch {
  padding-top: 20px;
  width: 200px;
}

.volume-wrapper {
  width: 160px;
  height: 10px;
  background-color: #ccc
}

.device-list {
  padding: 0 0 10px 0;
}

.volume {
  height: 10px;
  background-color: #3f51b5;
  transition: all 0.2s ease-out;
}

.video {
  height: 100px;
  width: 120px;
}

.device-item {
  display: flex;
  flex-direction: column;
  line-height: 1.8;
}

.resolution {
  padding-right: 10px;
}

.bottom-item {
  padding-bottom: 10px;
}

.wrapper {
  width: 100%;
}

.audio {
  height: 30px;
  width: 100%;
  max-width: 300px;
}

.result-success {
  font-size: 20px;
  font-weight: bold;
  color: #4caf50
}

.result-fail {
  font-size: 20px;
  font-weight: bold;
  color: #f44336
}

.card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
  width: 100%;
}
.device-container {
  display: flex;
  justify-content: center;
}

.speaker-container {
  height: 120px;
  color: rgba(0, 0, 0, .7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 230px;
}

.next {
  height: 50px;
  padding-top: 15px;

  > button {
    margin-right: 10px;
  }
}

.device-info {
  font-size: 14px;
}


.three-item {
  width: 100%;
}

.device-result-title {
  padding: 20px 0;
  display: flex;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.85);
}

.support {
  color: #4caf50;
  font-weight: bold;
}

.support-half {
  color: #ffc53d;
  font-weight: bold;
}

.support-failed {
  color: #f44336;
  font-weight: bold;
}



.icon-support {
  color: #4caf50;
  font-weight: bold;
  padding: 10px;
}

.icon-support-failed {
  color: #f44336;
  font-weight: bold;
  padding: 10px;
}


.device-result {
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.audio-container {
  display: none;
}
