import React, { useState } from 'react';
import Header from './components/header';
import Index from './components';
import './App.scss';
import Grid from '@material-ui/core/Grid';
import './i18n';
import * as htmlToImage from 'html-to-image';
import { styled } from '@material-ui/core';
import Button from '@material-ui/core/Button';
import { useTranslation } from 'react-i18next';

const { userAgent } = navigator;
export const IS_WX = /MicroMessenger/i.test(userAgent); // 是否为微信环境

const download = () => {
	htmlToImage.toPng(document.getElementById('capture'))
		.then((dataUrl) => {
			const link = document.createElement('a');
			link.download = 'rtc-detect';
			link.href = dataUrl;
			link.click();
		});
};

const DownloadBtn = styled(Button)({
	background: 'linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)',
	border: 0,
	borderRadius: 3,
	boxShadow: '0 3px 5px 2px rgba(255, 105, 135, .3)',
	color: 'white',
	height: 36,
	padding: '0 15px',
});


function App() {
	const { t } = useTranslation();
	const [isLoaded, setIsLoaded] = useState(false);

	const handleLoaded = () => {
		setIsLoaded(true);
	};

	return (
		<Grid container justifyContent="center">
			<Grid item xl={4} lg={6} md={11} sm={11} xs={11}>
				<div id="capture">
					<div className="headline">
						<Header/>
						{
							!IS_WX && isLoaded && (
								<div className="bottom">
									<div className="downloadIcon" onClick={download}>
										<svg t="1679388965666" className="icon" viewBox="0 0 1024 1024" version="1.1"
												 xmlns="http://www.w3.org/2000/svg" p-id="4957" width="200" height="200">
											<path
												d="M531.36384 796.86144a30.72 30.72 0 0 1-43.84768 0l-266.0096-271.02208C202.43456 506.40384 216.20224 473.6 243.4304 473.6h532.0192c27.22816 0 40.99584 32.80384 21.92384 52.23936l-266.0096 271.02208zM153.6 921.6a30.72 30.72 0 0 1 30.72-30.72h655.36a30.72 30.72 0 0 1 30.72 30.72v20.48a30.72 30.72 0 0 1-30.72 30.72H184.32a30.72 30.72 0 0 1-30.72-30.72v-20.48z"
												fill="#2196f3" p-id="4958"></path>
											<path
												d="M583.68 51.2a30.72 30.72 0 0 1 30.72 30.72v532.48a30.72 30.72 0 0 1-30.72 30.72H440.32a30.72 30.72 0 0 1-30.72-30.72V81.92a30.72 30.72 0 0 1 30.72-30.72h143.36z"
												fill="#2196f3" p-id="4959"></path>
										</svg>
									</div>
								</div>
							)
						}
					</div>
					<Index handleLoaded={handleLoaded}/>
				</div>
				{
					!IS_WX && isLoaded && (
						<div className="bottom">
							<DownloadBtn onClick={download}>
								{t('生成报告图')}
							</DownloadBtn>
						</div>
					)
				}
			</Grid>
		</Grid>
	);
}

export default App;
