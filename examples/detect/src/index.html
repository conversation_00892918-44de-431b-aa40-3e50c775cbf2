<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="keywords" content="WebRTC,能力检测,实时音视频,TRTC, trtc-js-sdk" />
  <meta name="description" content="This page is used to detect whether the user's page can use WebRTC.">
  <title>TRTC 能力检测</title>
</head>
<body>
<div id="root"></div>
<script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js"></script>
<script src="https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/vconsole.min.js"></script>
<script>
	function getQueryVariable(variable)
	{
		const query = window.location.search.substring(1);
		const vars = query.split('&');
		for (let i=0;i<vars.length;i++) {
			const pair = vars[i].split('=');
			if(pair[0] === variable){
				return pair[1];
			}
		}
		return false;
	}
	if (getQueryVariable('debug')) {
		const vConsole = new VConsole();
		console.log('vconsole init');
	}
</script>
</body>
</html>
