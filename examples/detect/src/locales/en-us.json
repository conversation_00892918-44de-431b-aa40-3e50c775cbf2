{"检测结果概览": "Overview of results", "是否支持 TRTC": "Browser supports TRTC", "是否支持进房【 检测项 3 && 5 】": "Browser supports join room【 Item 3 && 5 】", "是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】": "Browser supports publish local stream【 Item  3 && 5 && 11 && (6 || 7 || 15 || 16) 】", "是否支持拉流【 检测项 3 && 5 && 12 】": "Browser supports subscribe remote stream【 Item 3 && 5 && 12 】", "如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。": "If 15 or 16 support, you can get the data stream from the media device. if 6 or 7 support, you can get the data stream from Video or Canvas.", "浏览器支持 H264 编码，则可以支持推本地流": "Browser supports H264 encoding, you can publish LocalStream", "浏览器支持 H264 解码，则可以支持拉远端流": "Browser supports H264 decoding, you can subscribe RemoteStream", "是否可以从摄像头和麦克风采集视频和音频": "Can you get media from camera and microphone", "是否支持从 Canvas 采集本地流": "Can you capture local stream from Canvas", "是否支持从 Video 采集本地流": "Can you capture local stream from Video", "MediaStreamTrack 是否有 applyConstraints 方法": "MediaStreamTrack support applyConstraints()", "replaceTrack 支持动态操作 MediaStreamTrack": "The replaceTrack method supports dynamic manipulation of MediaStreamTrack", "生成报告图": "Generate report", "基础环境": "Basic environment", "API 支持": "API support", "编码支持": "Encoding support", "H264 编码": "H264 encode", "H264 解码": "H264 decode", "VP8 编码": "VP8 encode", "VP8 解码": "VP8 decode", "设备详情": "Device details", "麦克风设备列表": "Microphone list", "摄像头设备列表": "Camera list", "扬声器设备列表": "Speaker list", "设备支持度检测": "Device support detection", "操作系统": "Operating system", "浏览器": "Browser", "UA": "UA", "屏幕分辨率": "Display resolutions", "逻辑处理器数量": "Number of logical processors", "开始检测": "Start testing", "最大分辨率": "Maximum resolution", "最大帧率": "Maximum frame rate", "是": "Yes", "否": "No", "部分支持": "Partial", "是否允许使用摄像头": "Website has camera permissions", "是否允许使用麦克风": "Website has microphone permissions", "是否看到视频画面": "Do you see the video", "是否看到音量条变化": "Can you see the volume bar change", "点击播放后能否听到音乐": "Can you hear music after clicking", "是否支持获取媒体设备及媒体流": "Browser allows getUserMedia on this page", "是否支持屏幕分享": "Browser supports screen sharing", "是否支持 WebRTC": "Browser Supports WebRTC", "是否支持 WebAudio": "Browser Supports Web Audio API", "是否支持 WebSocket": "Browser Supports WebSocket", "是否支持从 Canvas 获取数据流": "Browser supports stream capturing from Canvas", "是否支持从 Video 获取数据流": "Browser supports stream capturing from video", "是否支持 RTCRtpSender.replaceTrack 方法": "Browser supports RTCRtpSender.replaceTrack", "是否支持 applyConstraints": "Browser supports applyConstraints", "摄像头检测": "Camera detection", "麦克风检测": "Microphone detection", "扬声器检测": "Speaker detection", "当前环境不支持获取": "Browser does not support acquisition", "检测完毕": "Detection completed", "网络检测": "Network detection", "检测网络前需要先登录，如果您没有登录的话会自动跳转登录": "You need to sign in before detecting the network, or it will automatically jump to login page", "剩余检测时间": "Remaining detection time", "网络延时": "Network delay", "上行网络质量": "Upstream network quality", "下行网络质量": "Downstream network quality", "登录失败": "<PERSON><PERSON> failed", "重新登录": "Login again", "未知": "UNKNOWN", "极佳": "PERFECT", "较好": "GOOD", "一般": "GENERAL", "差": "BAD", "极差": "AWFUL", "断开": "DISCONNECTED", "http": "The full TRTC functionality is only available under the HTTPS protocol.", "refer": "Reference URL Domain Protocol Restrictions"}