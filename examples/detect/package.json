{"name": "detect", "version": "0.0.1", "private": true, "scripts": {"build": "webpack --config ./build/webpack.prod.js --mode production", "dev": "webpack-dev-server --config ./build/webpack.dev.js --mode development", "test": "echo \"Error: no test specified\" && exit 1"}, "license": "ISC", "dependencies": {"trtc-js-sdk": "^4.15.5", "qrcode.react": "^1.0.1", "react": "17.0.2", "react-dom": "17.0.2", "rtc-detect": "^1.0.2"}, "devDependencies": {"@babel/core": "^7.18.9", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-react-jsx-source": "^7.2.0", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.14.6", "@babel/preset-react": "^7.0.0", "@babel/runtime": "^7.4.5", "@material-ui/core": "^4.11.4", "@material-ui/icons": "^4.11.2", "axios": "^0.21.1", "babel-loader": "^8.0.6", "clean-webpack-plugin": "^4.0.0", "css-loader": "^3.0.0", "html-to-image": "^1.6.2", "html-webpack-plugin": "^3.2.0", "html-webpack-template": "^6.2.0", "i18next": "^20.3.2", "js-cookie": "^2.2.1", "postcss": "^8.3.5", "postcss-loader": "4.2.0", "postcss-preset-env": "^6.7.0", "postcss-px-to-viewport": "^1.1.1", "react-i18next": "^11.11.1", "react-select": "^5.3.0", "sass": "^1.50.0", "sass-loader": "^7.1.0", "style-loader": "0.23.1", "webpack": "^4.35.0", "webpack-cli": "^3.3.5", "webpack-dev-server": "^3.7.2", "webpack-merge": "^4.2.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}