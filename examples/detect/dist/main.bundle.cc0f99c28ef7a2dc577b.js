!function(e){function t(t){for(var r,o,i=t[0],c=t[1],u=t[2],d=0,f=[];d<i.length;d++)o=i[d],Object.prototype.hasOwnProperty.call(n,o)&&n[o]&&f.push(n[o][0]),n[o]=0;for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(e[r]=c[r]);for(s&&s(t);f.length;)f.shift()();return l.push.apply(l,u||[]),a()}function a(){for(var e,t=0;t<l.length;t++){for(var a=l[t],r=!0,i=1;i<a.length;i++){var c=a[i];0!==n[c]&&(r=!1)}r&&(l.splice(t--,1),e=o(o.s=a[0]))}return e}var r={},n={0:0},l=[];function o(t){if(r[t])return r[t].exports;var a=r[t]={i:t,l:!1,exports:{}};return e[t].call(a.exports,a,a.exports,o),a.l=!0,a.exports}o.m=e,o.c=r,o.d=function(e,t,a){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(o.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(a,r,function(t){return e[t]}.bind(null,r));return a},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="";var i=window.webpackJsonp=window.webpackJsonp||[],c=i.push.bind(i);i.push=t,i=i.slice();for(var u=0;u<i.length;u++)t(i[u]);var s=c;l.push([256,1,2]),a()}({135:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NETWORK_QUALITY=void 0,t.getLanguage=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"trtc-detect-language",a=r("lang")||localStorage.getItem(t)||(null===(e=window.navigator.language)||void 0===e?void 0:e.toLowerCase());return a=a.indexOf("zh")>-1?"zh":"en",console.warn(a),a},t.getUrlParam=r,t.isUndefined=t.isBoolean=void 0;t.isUndefined=function(e){return void 0===e},t.isBoolean=function(e){return"boolean"==typeof e},t.NETWORK_QUALITY={0:"未知",1:"极佳",2:"较好",3:"一般",4:"差",5:"极差",6:"断开"};function r(e){var t=decodeURI(window.location.href.replace(/^[^?]*\?/,"")),a=new RegExp("(^|&)".concat(e,"=([^&#]*)(&|$|)"),"i"),r=t.match(a);return r?r[2]:null}},141:function(e,t,a){"use strict";var r=a(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return n.default.createElement("div",{className:"loading"},n.default.createElement("div",{className:"clock-loader"}))};var n=r(a(0));a(333)},256:function(e,t,a){"use strict";var r=a(28),n=r(a(0)),l=r(a(11)),o=r(a(261));l.default.render(n.default.createElement(o.default,null),document.getElementById("root"))},261:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.IS_WX=void 0;var l=r(a(31)),o=v(a(0)),i=r(a(265)),c=r(a(281));a(337);var u=r(a(79));a(339);var s=v(a(342)),d=a(98),f=r(a(44)),p=a(49);function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(m=function(e){return e?a:t})(e)}function v(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=m(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}var h=navigator.userAgent,g=t.IS_WX=/MicroMessenger/i.test(h),w=function(){s.toPng(document.getElementById("capture")).then((function(e){var t=document.createElement("a");t.download="rtc-detect",t.href=e,t.click()}))},b=(0,d.styled)(f.default)({background:"linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%)",border:0,borderRadius:3,boxShadow:"0 3px 5px 2px rgba(255, 105, 135, .3)",color:"white",height:36,padding:"0 15px"});t.default=function(){var e=(0,p.useTranslation)().t,t=(0,o.useState)(!1),a=(0,l.default)(t,2),r=a[0],n=a[1];return o.default.createElement(u.default,{container:!0,justifyContent:"center"},o.default.createElement(u.default,{item:!0,xl:4,lg:6,md:11,sm:11,xs:11},o.default.createElement("div",{id:"capture"},o.default.createElement("div",{className:"headline"},o.default.createElement(i.default,null),!g&&r&&o.default.createElement("div",{className:"bottom"},o.default.createElement("div",{className:"downloadIcon",onClick:w},o.default.createElement("svg",{t:"1679388965666",className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"4957",width:"200",height:"200"},o.default.createElement("path",{d:"M531.36384 796.86144a30.72 30.72 0 0 1-43.84768 0l-266.0096-271.02208C202.43456 506.40384 216.20224 473.6 243.4304 473.6h532.0192c27.22816 0 40.99584 32.80384 21.92384 52.23936l-266.0096 271.02208zM153.6 921.6a30.72 30.72 0 0 1 30.72-30.72h655.36a30.72 30.72 0 0 1 30.72 30.72v20.48a30.72 30.72 0 0 1-30.72 30.72H184.32a30.72 30.72 0 0 1-30.72-30.72v-20.48z",fill:"#2196f3","p-id":"4958"}),o.default.createElement("path",{d:"M583.68 51.2a30.72 30.72 0 0 1 30.72 30.72v532.48a30.72 30.72 0 0 1-30.72 30.72H440.32a30.72 30.72 0 0 1-30.72-30.72V81.92a30.72 30.72 0 0 1 30.72-30.72h143.36z",fill:"#2196f3","p-id":"4959"}))))),o.default.createElement(c.default,{handleLoaded:function(){n(!0)}})),!g&&r&&o.default.createElement("div",{className:"bottom"},o.default.createElement(b,{onClick:w},e("生成报告图")))))}},265:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(a(31)),o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=f(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0));a(266);var i=r(a(79)),c=r(a(80)),u=a(49),s=r(a(273)),d=a(135);function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(f=function(e){return e?a:t})(e)}t.default=function(){var e=(0,o.useState)("en"),t=(0,l.default)(e,2),a=t[0],r=t[1],n=o.default.useState(null),f=(0,l.default)(n,2),p=f[0],m=f[1],v=(0,u.useTranslation)().i18n;(0,o.useEffect)((function(){h((0,d.getLanguage)())}),[]);var h=function(e){v.changeLanguage(e),localStorage.setItem("trtc-detect-language",e),document.title="zh"===e?"TRTC 能力检测":"TRTC Capability Detection",r(e)},g=Boolean(p),w=g?"simple-popover":void 0;return o.default.createElement(i.default,{container:!0,justifyContent:"space-between"},o.default.createElement(i.default,{item:!0},o.default.createElement("div",{className:"header"},"zh"===a&&o.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-cn-b.png"}),"en"===a&&o.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-en-b.png"}))),o.default.createElement(i.default,{item:!0},o.default.createElement("div",{className:"header"},window.innerWidth>385&&o.default.createElement("div",{className:"qrcode",onClick:function(e){m(e.currentTarget)},"aria-describedby":w},o.default.createElement("svg",{t:"1646118025166",className:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1245",width:"24",height:"24"},o.default.createElement("path",{d:"M491.925333 532.074667V853.333333H170.666667V532.074667h321.258666zM597.333333 789.333333v64h-64v-64h64z m256-85.333333v149.333333h-128v-64h64v-85.333333h64z m-425.408-107.925333H234.666667V789.333333h193.258666v-193.258666zM661.333333 597.333333v128h64v64h-128v-192h64z m-277.333333 42.666667v106.666667h-106.666667v-106.666667h106.666667z m405.333333 0v64h-64v-64h64z m64-106.666667v106.666667h-64v-42.666667h-64v-64h128z m-256 0v64h-64v-64h64zM491.925333 170.666667v321.258666H170.666667V170.666667h321.258666zM853.333333 170.666667v321.258666H532.074667V170.666667H853.333333z m-425.408 64H234.666667v193.258666h193.258666V234.666667zM789.333333 234.666667h-193.258666v193.258666H789.333333V234.666667z m-405.333333 42.666666v106.666667h-106.666667v-106.666667h106.666667z m362.666667 0v106.666667h-106.666667v-106.666667h106.666667z","p-id":"1246",fill:"#b9b9b9"}))),"zh"===a&&o.default.createElement("div",{className:"icon-lang",onClick:function(){return h("en")}},o.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/zh.png",height:"24px"})),"en"===a&&o.default.createElement("div",{className:"icon-lang",onClick:function(){return h("zh")}},o.default.createElement("img",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/detect/en.png",height:"24px"}))),o.default.createElement(c.default,{id:w,open:g,anchorEl:p,onClose:function(){m(null)},anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"}},o.default.createElement("div",{className:"phone"},o.default.createElement(s.default,{value:window.location.href,size:200,fgColor:"#000000"})))))}},266:function(e,t,a){var r=a(267);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},267:function(e,t,a){(t=a(61)(!1)).push([e.i,".header{height:80px;display:flex;align-items:center;justify-content:space-between;user-select:none}.header>img{height:36px}@media(max-width: 400px){.header>img{height:28px}}.header .title{padding:0 40px;line-height:64px;overflow:hidden;width:100%}.header .qrcode{cursor:pointer;user-select:none;-webkit-user-select:none}.header .button-wrapper{display:flex;justify-content:center;align-items:center;height:100%;padding:0 40px}.header .icon-lang{padding:10px;cursor:pointer;user-select:none}.phone{padding:6px}",""]),e.exports=t},281:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,u.useState)(!1),a=(0,i.default)(t,2),r=a[0],n=a[1],f=(0,u.useState)({}),m=(0,i.default)(f,2),v=m[0],h=m[1];return(0,u.useEffect)((function(){(function(){var e=(0,o.default)(l.default.mark((function e(){var t,a;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.default.createStream({video:!0,audio:!1}),e.prev=1,e.next=4,t.initialize();case 4:v.video=!0,h(v),t&&t.close(),e.next=22;break;case 9:e.prev=9,e.t0=e.catch(1),v.video=!1,h(v),e.t1=e.t0.name,e.next="NotReadableError"===e.t1?16:"NotAllowedError"===e.t1?18:21;break;case 16:return console.log(e.t0.name),e.abrupt("break",22);case 18:return e.t0.message.includes("Permission denied by system")||console.log("You refused to share the screen"),console.log("Permission denied by system"),e.abrupt("break",22);case 21:return e.abrupt("break",22);case 22:return a=c.default.createStream({video:!1,audio:!0}),e.prev=23,e.next=26,a.initialize();case 26:v.audio=!0,h(v),a&&a.close(),e.next=44;break;case 31:e.prev=31,e.t2=e.catch(23),v.audio=!1,h(v),e.t3=e.t2.name,e.next="NotReadableError"===e.t3?38:"NotAllowedError"===e.t3?40:43;break;case 38:return console.log(e.t2.name),e.abrupt("break",44);case 40:return e.t2.message.includes("Permission denied by system")||console.log("You refused to share the screen"),console.log("Permission denied by system"),e.abrupt("break",44);case 43:return e.abrupt("break",44);case 44:p(v.video)&&p(v.audio)&&n(!0);case 45:case"end":return e.stop()}}),e,null,[[1,9],[23,31]])})));return function(){return e.apply(this,arguments)}})()()}),[]),u.default.createElement("div",{className:"block"},r?u.default.createElement(s.default,{mark:v,handleLoaded:e.handleLoaded}):u.default.createElement(d.default,null))};var l=r(a(63)),o=r(a(64)),i=r(a(31)),c=r(a(92)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=f(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0)),s=r(a(283)),d=r(a(141));function f(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(f=function(e){return e?a:t})(e)}var p=function(e){return"boolean"==typeof e}},283:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,a,r,n,u,p,b,C,O=E(),P=S(),j=x(),_=(0,c.useState)({}),M=(0,i.default)(_,2),T=M[0],I=M[1],W=(0,c.useState)({}),z=(0,i.default)(W,2),R=z[0],A=z[1],D=(0,c.useState)({}),V=(0,i.default)(D,2),H=V[0],B=V[1],U=(0,c.useState)(0),L=(0,i.default)(U,2),q=L[0],F=L[1],Y=(0,c.useState)(0),G=(0,i.default)(Y,2),Q=G[0],K=G[1],J=(0,c.useState)(),X=(0,i.default)(J,2),$=X[0],Z=X[1],ee=(0,c.useState)(),te=(0,i.default)(ee,2),ae=te[0],re=te[1],ne=(0,c.useState)(),le=(0,i.default)(ne,2),oe=le[0],ie=le[1],ce=(0,c.useState)(),ue=(0,i.default)(ce,2),se=ue[0],de=ue[1],fe=(0,c.useState)(),pe=(0,i.default)(fe,2),me=pe[0],ve=pe[1],he=(0,c.useState)(),ge=(0,i.default)(he,2),we=ge[0],be=ge[1],ye=(0,c.useState)(),ke=(0,i.default)(ye,2),Ee=ke[0],xe=ke[1],Se=(0,c.useState)(!0),Ne=(0,i.default)(Se,2),Ce=Ne[0],Oe=Ne[1],Pe=(0,c.useState)(!1),je=(0,i.default)(Pe,2),_e=je[0],Me=je[1],Te=(0,h.useTranslation)().t,Ie={basic:{title:Te("基础环境"),params:T},api:{title:Te("API 支持"),params:H},codec:{title:Te("编码支持"),params:R}};(0,c.useEffect)((function(){(function(e){for(var t=window.location.search.substring(1).split("&"),a=0;a<t.length;a++){var r=t[a].split("=");if(r[0]===e)return r[1]}return!1})("network")&&Me(!0),function(){var t=(0,o.default)(l.default.mark((function t(){var a,r,n,o,i,c,u,s,d,f,p,m,v,h,g,w,b,E,x,S,N,C,O,P,j,_,M,T,W,z,R,D,V,H;return l.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,k.isTRTCSupported();case 2:if(a=t.sent,xe(a),r=k.report){t.next=7;break}return t.abrupt("return");case 7:Oe(!1),e.handleLoaded(),n=r.system,o=r.APISupported,i=r.codecsSupported,c=r.devices,y.infoAll(r),u=i.isH264DecodeSupported,s=i.isH264EncodeSupported,d=i.isVp8DecodeSupported,f=i.isVp8EncodeSupported,A({h264Encode:{title:"H264 编码",desc:"浏览器支持 H264 编码，则可以支持推本地流",result:s},h264Decode:{title:"H264 解码",desc:"浏览器支持 H264 解码，则可以支持拉远端流",result:u},vp8Encode:{title:"VP8 编码",result:f},vp8Decode:{title:"VP8 解码",result:d}}),p=n.UA,m=n.OS,v=n.browser,h=v.name,g=v.version,w=n.displayResolution,b=w.width,E=w.height,x=n.hardwareConcurrency,S={OS:{title:"操作系统",result:m},browser:{title:"浏览器",result:"".concat(h,"/").concat(g||"")},UA:{title:"UA",result:p},resolution:{title:"屏幕分辨率",result:"".concat(b," x ").concat(E)}},x&&(S.hardwareConcurrency={title:"逻辑处理器数量",result:"".concat(x)}),I(S),N=o.isUserMediaSupported,C=o.isWebRTCSupported,O=o.isWebSocketSupported,P=o.isWebAudioSupported,j=o.isScreenCaptureAPISupported,_=o.isCanvasCapturingSupported,M=o.isVideoCapturingSupported,T=o.isApplyConstraintsSupported,W={isUserMediaSupported:{title:"是否支持获取媒体设备及媒体流",desc:"是否可以从摄像头和麦克风采集视频和音频",result:N},isScreenCaptureAPISupported:{title:"是否支持屏幕分享",result:j},isWebRTCSupported:{title:"是否支持 WebRTC",result:C},isWebAudioSupported:{title:"是否支持 WebAudio",result:P},isWebSocketSupported:{title:"是否支持 WebSocket",result:O},isWebCodecsSupported:{title:"是否支持 WebCodecs",desc:"音频编码 ".concat("AudioEncoder"in window?"Yes":"No",", 视频编码 ").concat("VideoEncoder"in window?"Yes":"No",", 音频解码 ").concat("AudioDecoder"in window?"Yes":"No",", 视频解码 ").concat("VideoDecoder"in window?"Yes":"No"),result:"AudioEncoder"in window&&"VideoEncoder"in window&&"AudioDecoder"in window&&"VideoDecoder"in window},isMediaStreamTrackGeneratorSupported:{title:"是否支持 MediaStreamTrackGenerator",result:"MediaStreamTrackGenerator"in window},isMediaStreamTrackProcessorSupported:{title:"是否支持 MediaStreamTrackProcessor",result:"MediaStreamTrackProcessor"in window},isCanvasCapturingSupported:{title:"是否支持从 Canvas 获取数据流",result:_},isVideoCapturingSupported:{title:"是否支持从 Video 获取数据流",result:M},isApplyConstraintsSupported:{title:"是否支持 applyConstraints",desc:"MediaStreamTrack 是否有 applyConstraints 方法",result:T}},B(W),z=c.hasCameraPermission,R=c.hasMicrophonePermission,D=c.cameras,V=c.microphones,H=c.speakers,be({cam:{title:"是否允许使用摄像头",desc:"是否允许使用摄像头",result:z||!1},microphone:{title:"是否允许使用麦克风",desc:"是否允许使用麦克风",result:R||!1}}),Z(V),re(H),ie(D),de(z),ve(R);case 29:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}()()}),[]);var We=(null==H||null===(t=H.isWebRTCSupported)||void 0===t?void 0:t.result)&&(null==H||null===(a=H.isWebSocketSupported)||void 0===a?void 0:a.result),ze=(null==H||null===(r=H.isCanvasCapturingSupported)||void 0===r?void 0:r.result)||(null==H||null===(n=H.isVideoCapturingSupported)||void 0===n?void 0:n.result)||(null==we||null===(u=we.cam)||void 0===u?void 0:u.result)||(null==we||null===(p=we.cam)||void 0===p?void 0:p.result),Re=We&&(null==R||null===(b=R.h264Encode)||void 0===b?void 0:b.result)&&ze,Ae=We&&(null==R||null===(C=R.h264Decode)||void 0===C?void 0:C.result);return c.default.createElement("div",{className:"block"},Ce&&c.default.createElement("div",{className:"cover"},c.default.createElement(g.default,null)),c.default.createElement("div",{className:"panel-item"},c.default.createElement(s.default,{expanded:!0,square:!0,classes:O},c.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:P},c.default.createElement("div",{className:"key"},Te("检测结果概览"))),c.default.createElement(f.default,{classes:j},c.default.createElement("div",null,c.default.createElement("div",{className:"key",style:{margin:"1em 0",color:"#F56C6C"}},Te("http"),c.default.createElement("a",{style:{textDecoration:"none",color:"#2196f3"},target:"_blank",href:"https://cloud.tencent.com/document/product/647/32398#url-.E5.9F.9F.E5.90.8D.E5.8D.8F.E8.AE.AE.E9.99.90.E5.88.B6",rel:"noreferrer"},Te("refer"))),c.default.createElement("div",{className:"key-item"},c.default.createElement("div",{className:"key"},Te("是否支持 TRTC"),"：",Ee&&Ee.result?c.default.createElement("span",{className:"support"},Te("是")):We?c.default.createElement("span",{className:"support-half"},Te("部分支持")):c.default.createElement("span",{className:"support-failed"},Te("否")))),Ee&&!Ee.result&&c.default.createElement("span",{className:"support-failed"},Ee.reason),c.default.createElement("div",{className:"key-item",key:"join"},c.default.createElement("div",{className:"key"},Te("是否支持进房【 检测项 3 && 5 】"),"：",We?c.default.createElement("span",{className:"support"},Te("是")):c.default.createElement("span",{className:"support-failed"},Te("否")))),c.default.createElement("div",{className:"key-item",key:"publish"},c.default.createElement("div",{className:"key"},Te("是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】"),"：",Re?c.default.createElement("span",{className:"support"},Te("是")):c.default.createElement("span",{className:"support-failed"},Te("否")))),c.default.createElement("div",{className:"publish-desc"},Te("如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。")),c.default.createElement("div",{className:"key-item",key:"subscribe"},c.default.createElement("div",{className:"key"},Te("是否支持拉流【 检测项 3 && 5 && 12 】"),"：",Ae?c.default.createElement("span",{className:"support"},Te("是")):c.default.createElement("span",{className:"support-failed"},Te("否")))))))),Object.keys(Ie).map((function(e){return c.default.createElement("div",{key:e,className:"panel-item"},c.default.createElement(s.default,{expanded:!0,square:!0,classes:O},c.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:P},c.default.createElement("div",{className:"key"},Ie[e].title)),c.default.createElement(f.default,{classes:j},"basic"===e&&N(Ie[e].params),"api"===e&&N(Ie[e].params,0),"codec"===e&&N(Ie[e].params,10))))})),c.default.createElement("div",{className:"panel-item"},c.default.createElement(s.default,{expanded:!0,square:!0,classes:O},c.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:P},c.default.createElement("div",{className:"key"},Te("设备详情"))),c.default.createElement(f.default,{classes:j},c.default.createElement("div",{className:"device-info"},c.default.createElement("div",{className:"key-item"},c.default.createElement("div",{className:se?"id":"id-failed"},"15"),c.default.createElement("div",{className:"key"},Te("是否允许使用摄像头"),"：",se?c.default.createElement("span",{className:"support"},Te("是")):c.default.createElement("span",{className:"support-failed"},Te("否")))),c.default.createElement("div",{className:"key-item"},c.default.createElement("div",{className:me?"id":"id-failed"},"16"),c.default.createElement("div",{className:"key"},Te("是否允许使用麦克风"),"：",me?c.default.createElement("span",{className:"support"},Te("是")):c.default.createElement("span",{className:"support-failed"},Te("否")))),$&&$.length>0&&c.default.createElement("div",{className:"device-list"},c.default.createElement("div",{className:"device-card-title"},Te("麦克风设备列表")),c.default.createElement("div",{className:"device-item"},null==$?void 0:$.map((function(e,t){return c.default.createElement("div",{key:e.deviceId+t},e.label)})))),oe&&oe.length>0&&c.default.createElement("div",{className:"device-list"},c.default.createElement("div",{className:"device-card-title"},Te("摄像头设备列表")),c.default.createElement("div",{className:"device-item"},null==oe?void 0:oe.map((function(e,t){return c.default.createElement("div",{key:e.deviceId+t},e.label,"    ",e.resolution&&e.resolution.maxWidth?"".concat(Te("最大分辨率"),"：").concat(e.resolution.maxWidth," x ").concat(e.resolution.maxHeight):"","    ",e.resolution&&e.resolution.maxFrameRate?"".concat(Te("最大帧率"),"：").concat(e.resolution.maxFrameRate.toFixed(0)):"")})))),ae&&ae.length>0&&c.default.createElement("div",{className:"device-list"},c.default.createElement("div",{className:"device-card-title"},Te("扬声器设备列表")),c.default.createElement("div",{className:"device-item"},null==ae?void 0:ae.map((function(e,t){return c.default.createElement("div",{key:e.deviceId+t},e.label)})))))))),c.default.createElement("div",{className:"panel-item"},c.default.createElement(s.default,{expanded:!0,square:!0,classes:O},c.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:P},c.default.createElement("div",{className:"key"},Te("设备支持度检测"))),c.default.createElement(f.default,{classes:j},0===q&&c.default.createElement("div",{className:"start-detect"},c.default.createElement(w.default,{onClick:function(){return F(1)}},Te("开始检测"))),1===q&&c.default.createElement(m.default,{mark:e.mark})))),_e&&c.default.createElement("div",{className:"panel-item"},c.default.createElement(s.default,{expanded:!0,square:!0,classes:O},c.default.createElement(d.default,{"aria-controls":"panel1a-content",id:"panel1a-header",classes:P},c.default.createElement("div",{className:"key"},Te("网络检测"))),c.default.createElement(f.default,{classes:j},0===Q?c.default.createElement("div",{className:"start-detect"},c.default.createElement(w.default,{onClick:function(){return K(1)}},Te("开始检测")),c.default.createElement("div",{className:"tip"},Te("检测网络前需要先登录，如果您没有登录的话会自动跳转登录"))):c.default.createElement(v.default,{handleReport:function(e){y.infoAll(e)}})))))};var l=r(a(63)),o=r(a(64)),i=r(a(31)),c=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=b(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0));a(284);var u=r(a(286)),s=r(a(93)),d=r(a(94)),f=r(a(95)),p=a(98),m=r(a(288)),v=r(a(311)),h=a(49),g=r(a(141)),w=r(a(97));function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(b=function(e){return e?a:t})(e)}var y=new window.Aegis({id:"CUURTHMqVFbioiGGSC",reportApiSpeed:!0,reportAssetSpeed:!0,hostUrl:"https://tamaegis.com",pagePerformance:!0,spa:!0}),k=new u.default,E=(0,p.makeStyles)((function(){return{root:{width:"100%",boxShadow:"none",border:"1px solid #ddd",backgroundImage:"url(https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/mark.png)"}}})),x=(0,p.makeStyles)((function(){return{root:{padding:"8px 16px"}}})),S=(0,p.makeStyles)((function(){return{root:{color:"white",backgroundColor:"#2196f3",minHeight:"0 !important",cursor:"default !important",lineHeight:"22px"},content:{margin:"10px 0 !important"},expanded:{margin:"0 !important:",minHeight:"0px"}}}));function N(e,t){var a=(0,h.useTranslation)().t,r=t,n=Object.keys(e);return c.default.createElement("div",null,n.map((function(t){return r+=1,c.default.createElement(c.default.Fragment,{key:"".concat(t,"wrapper")},"string"==typeof e[t].result?c.default.createElement("div",{key:t,className:"key-item"},r?c.default.createElement("div",{className:"id"},r):null,c.default.createElement("div",{className:"key"},a(e[t].title),"："),a(e[t].result)):c.default.createElement("div",{key:t,className:"key-item"},r?c.default.createElement("div",{className:e[t].result?"id":"id-failed"},r):null,c.default.createElement("div",{className:"key"},a(e[t].title),"：",e[t].result?c.default.createElement("span",{className:"support"},a("是")):c.default.createElement("span",{className:"support-failed"},a("否")))),e[t].desc&&c.default.createElement("div",{className:"desc"},a(e[t].desc)))})))}},284:function(e,t,a){var r=a(285);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},285:function(e,t,a){(t=a(61)(!1)).push([e.i,".id{background-color:#4caf50;color:#fff;width:20px;height:20px;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:10px}.id-failed{background-color:#f44336;color:#fff;width:20px;height:20px;display:flex;justify-content:center;align-items:center;border-radius:50%;margin-right:10px}.block{padding:0;position:relative}.panel-item{padding-bottom:20px}.key-item{font-size:14px;font-weight:350;line-height:20px;margin:1em 0;display:flex}.key{font-weight:700}.desc{font-weight:400;font-size:12px;color:rgba(0,0,0,.7);margin-bottom:20px;padding-left:30px}.publish-desc{font-weight:400;font-size:12px;color:rgba(0,0,0,.7);margin-bottom:20px}.start-detect{display:flex;flex-direction:column;justify-content:space-around;align-items:center;height:120px;width:100%}.tip{font-size:14px;color:rgba(0,0,0,.8)}.cover{background:#fff;position:absolute;width:100%;height:100%;z-index:999}.description{background-color:#e6f7ff;padding:10px 16px;margin-bottom:20px;font-size:16px}",""]),e.exports=t},288:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,h.useTranslation)().t,a=(0,c.useState)(0),r=(0,i.default)(a,2),n=r[0],v=r[1],w=(0,c.useState)(e.mark.video),y=(0,i.default)(w,2),k=y[0],E=y[1],x=(0,c.useState)(e.mark.audio),S=(0,i.default)(x,2),N=S[0],C=S[1],O=(0,c.useState)(),P=(0,i.default)(O,2),j=P[0],_=P[1],M=b(),T=function(){var e=(0,o.default)(l.default.mark((function e(t){return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(0,g.isBoolean)(t.microphone)&&(C(t.microphone),v(1)),(0,g.isBoolean)(t.camera)&&(E(t.camera),v(2)),(0,g.isBoolean)(t.speaker)&&(_(t.speaker),v(3));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return c.default.createElement("div",{className:"wrapper"},0===n&&c.default.createElement("div",{className:"three-item"},c.default.createElement(s.default,{handleState:T})),1===n&&c.default.createElement("div",{className:"three-item"},c.default.createElement(u.default,{handleState:T})),2===n&&c.default.createElement("div",{className:"three-item"},c.default.createElement(d.default,{handleState:T})),3===n&&c.default.createElement("div",{className:"three-item"},c.default.createElement("div",{className:"device-result-title"},t("检测完毕")),c.default.createElement("div",{className:"device-result"},c.default.createElement("div",{className:N?"icon-support":"icon-support-failed"},c.default.createElement(p.default,{classes:M})),c.default.createElement("div",{className:k?"icon-support":"icon-support-failed"},c.default.createElement(f.default,{classes:M})),c.default.createElement("div",{className:j?"icon-support":"icon-support-failed"},c.default.createElement(m.default,{classes:M})))))};var l=r(a(63)),o=r(a(64)),i=r(a(31)),c=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=w(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0));a(289);var u=r(a(291)),s=r(a(306)),d=r(a(307)),f=r(a(308)),p=r(a(309)),m=r(a(310)),v=a(98),h=a(49),g=a(135);function w(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(w=function(e){return e?a:t})(e)}var b=(0,v.makeStyles)((function(){return{root:{fontSize:"2em"}}}))},289:function(e,t,a){var r=a(290);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},290:function(e,t,a){(t=a(61)(!1)).push([e.i,".device-wrapper{padding-top:10px;border-bottom:1px solid #eee;width:100%}.device-card-title{padding:8px 0 0 0;margin-right:12px;color:rgba(0,0,0,.85);font-weight:600;font-size:14px}.device-card-content{font-size:14px;line-height:22px}.device-content{height:120px;color:rgba(0,0,0,.7);width:160px;display:flex;flex-direction:column;justify-content:center}.device-switch{padding-top:20px;width:200px}.volume-wrapper{width:160px;height:10px;background-color:#ccc}.device-list{padding:0 0 10px 0}.volume{height:10px;background-color:#3f51b5;transition:all .2s ease-out}.video{height:100px;width:120px}.device-item{display:flex;flex-direction:column;line-height:1.8}.resolution{padding-right:10px}.bottom-item{padding-bottom:10px}.wrapper{width:100%}.audio{height:30px;width:100%;max-width:300px}.result-success{font-size:20px;font-weight:bold;color:#4caf50}.result-fail{font-size:20px;font-weight:bold;color:#f44336}.card{display:flex;flex-direction:column;justify-content:center;align-items:center;padding:16px;width:100%}.device-container{display:flex;justify-content:center}.speaker-container{height:120px;color:rgba(0,0,0,.7);display:flex;flex-direction:column;justify-content:center;width:230px}.next{height:50px;padding-top:15px}.next>button{margin-right:10px}.device-info{font-size:14px}.three-item{width:100%}.device-result-title{padding:20px 0;display:flex;justify-content:center;font-size:20px;font-weight:bold;color:rgba(0,0,0,.85)}.support{color:#4caf50;font-weight:bold}.support-half{color:#ffc53d;font-weight:bold}.support-failed{color:#f44336;font-weight:bold}.icon-support{color:#4caf50;font-weight:bold;padding:10px}.icon-support-failed{color:#f44336;font-weight:bold;padding:10px}.device-result{display:flex;justify-content:center;padding-bottom:20px}.audio-container{display:none}",""]),e.exports=t},291:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,f.useTranslation)().t,a=(0,u.useState)([]),r=(0,c.default)(a,2),n=r[0],v=r[1],h=(0,u.useState)("default"),g=(0,c.default)(h,2),w=g[0],b=g[1];(0,u.useEffect)((function(){(function(){var e=(0,i.default)(o.default.mark((function e(){var t;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,d.default.getCameras();case 2:return(t=e.sent).forEach((function(e){e.value=e.deviceId})),v(t),b(t[0]),l=d.default.createStream({video:!0,audio:!1,cameraId:t[0].deviceId}),e.next=9,l.initialize();case 9:l.play("video").then((function(){return console.log("play success")})).catch((function(){return console.log("play error")}));case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[]);var y=function(t){e.handleState({camera:t}),l&&l.close()},k=function(){var e=(0,i.default)(o.default.mark((function e(t){return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:b(t),l.switchDevice("video",t.value).then((function(){return console.log("switchDevice success")})).catch((function(){return console.log("switchDevice error")}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return u.default.createElement("div",{className:"card"},u.default.createElement("div",{className:"device-card-title"},t("摄像头检测")),u.default.createElement("div",{className:"device-card-content"},t("是否看到视频画面")),u.default.createElement("div",{className:"device-switch"},u.default.createElement(m.default,{value:w,onChange:function(e){return k(e)},options:n,width:"200px",menuColor:"red"})),u.default.createElement("div",{className:"device-content"},u.default.createElement("div",{className:"device-container"},u.default.createElement("div",{id:"video",className:"video"}))),u.default.createElement("div",{className:"next"},u.default.createElement(s.default,{color:"default",size:"small",variant:"contained",onClick:function(){return y(!1)}},t("否")),u.default.createElement(p.default,{color:"default",size:"small",variant:"contained",onClick:function(){return y(!0)}},t("是"))))};var l,o=r(a(63)),i=r(a(64)),c=r(a(31)),u=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=v(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0)),s=r(a(44)),d=r(a(92)),f=a(49),p=r(a(97)),m=r(a(243));function v(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(v=function(e){return e?a:t})(e)}},306:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,d.useTranslation)().t,a=(0,c.useState)(0),r=(0,i.default)(a,2),n=r[0],m=r[1],g=(0,c.useState)([]),w=(0,i.default)(g,2),b=w[0],y=w[1],k=(0,c.useState)("default"),E=(0,i.default)(k,2),x=E[0],S=E[1];(0,c.useEffect)((function(){return function(){var e=(0,o.default)(l.default.mark((function e(){var t;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,s.default.getMicrophones();case 2:return(t=e.sent).forEach((function(e){e.value=e.deviceId})),y(t),S(t[0]),h=s.default.createStream({video:!1,audio:!0,microphoneId:t[0].deviceId}),e.next=9,h.initialize();case 9:h.play("audio-container"),v=setInterval((function(){var e=h.getAudioLevel();m(e)}),200);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()(),function(){clearInterval(v)}}),[]);var N=function(t){e.handleState({microphone:t}),h&&h.close()},C=function(){var e=(0,o.default)(l.default.mark((function e(t){return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:S(t),h.switchDevice("audio",t.value).then((function(){return console.log("switchDevice success")})).catch((function(){return console.log("switchDevice error")}));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return c.default.createElement("div",{className:"card"},c.default.createElement("div",{className:"device-card-title"},t("麦克风检测")),c.default.createElement("div",{className:"device-card-content"},t("是否看到音量条变化")),c.default.createElement("div",{className:"device-switch"},c.default.createElement(p.default,{value:x,onChange:function(e){return C(e)},options:b,width:"200px",menuColor:"red"})),c.default.createElement("div",{className:"device-content"},c.default.createElement("div",{className:"volume-wrapper"},c.default.createElement("div",{id:"audio-container",className:"audio-container"}),c.default.createElement("div",{className:"volume",style:{width:"".concat(100*n,"%")}}))),c.default.createElement("div",{className:"next"},c.default.createElement(u.default,{color:"default",size:"small",variant:"contained",onClick:function(){return N(!1)}},t("否")),c.default.createElement(f.default,{color:"default",size:"small",variant:"contained",onClick:function(){return N(!0)}},t("是"))))};var l=r(a(63)),o=r(a(64)),i=r(a(31)),c=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=m(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0)),u=r(a(44)),s=r(a(92)),d=a(49),f=r(a(97)),p=r(a(243));function m(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(m=function(e){return e?a:t})(e)}var v=0,h=null},307:function(e,t,a){"use strict";var r=a(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,o.useTranslation)().t;return n.default.createElement("div",{className:"card"},n.default.createElement("div",{className:"device-card-title"},t("扬声器检测")),n.default.createElement("div",{className:"device-card-content"},t("点击播放后能否听到音乐")),n.default.createElement("div",{className:"speaker-container"},n.default.createElement("div",{className:"device-container"},n.default.createElement("audio",{src:"https://web.sdk.qcloud.com/trtc/webrtc/assets/testspeak.mp3",controlsList:"nodownload",controls:!0,loop:!0,crossOrigin:"anonymous",className:"audio"}))),n.default.createElement("div",{className:"next"},n.default.createElement(l.default,{color:"default",size:"small",variant:"contained",onClick:function(){return e.handleState({speaker:!1})}},"否"),n.default.createElement(i.default,{color:"default",size:"small",variant:"contained",onClick:function(){return e.handleState({speaker:!0})}},"是")))};var n=r(a(0)),l=r(a(44)),o=a(49),i=r(a(97))},311:function(e,t,a){"use strict";var r=a(28),n=a(22);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,d.useTranslation)().t,a=(0,c.useState)(0),r=(0,o.default)(a,2),n=r[0],p=r[1],h=(0,c.useState)(),x=(0,o.default)(h,2),S=(x[0],x[1]),N=(0,c.useState)(15),C=(0,o.default)(N,2),O=C[0],P=C[1],j=(0,c.useState)(),_=(0,o.default)(j,2),M=(_[0],_[1]),T=(0,c.useState)(0),I=(0,o.default)(T,2),W=I[0],z=I[1],R=(0,c.useState)(0),A=(0,o.default)(R,2),D=A[0],V=A[1],H=(0,c.useState)(0),B=(0,o.default)(H,2),U=B[0],L=B[1];(0,c.useEffect)((function(){(function(){var e=(0,i.default)(l.default.mark((function e(){var t,a;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(console.log("checkToken"),t=u.default.get("trtc-api-example-token"),a=u.default.get("trtc-api-example-phoneNumber"),!t||!a){e.next=11;break}return S(t),M(a),e.next=8,F(a,t);case 8:k<0&&(P(15),Y()),e.next=12;break;case 11:window.location="".concat(window.location.origin,"/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html");case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}})()()}),[]);var q=function(){var e=(0,i.default)(l.default.mark((function e(){var t,a;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(window.location="".concat(window.location.origin,"/trtc/webrtc/demo/api-sample/login.html?from=/trtc/webrtc/demo/detect/index.html"),t=u.default.get("trtc-token"),a=u.default.get("phoneNumber"),!t||!a){e.next=9;break}return S(t),M(a),e.next=8,F(a,t);case 8:k<0&&(P(15),Y());case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),F=function(){var e=(0,i.default)(l.default.mark((function e(t,a){var r,n,o,c,u;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r="rtc_detect_up".concat(1e8*Math.random()),n="rtc_detect_down".concat(1e8*Math.random()),o="rtc_detect_".concat(1e8*Math.random()),e.next=5,E({roomId:o,userId:r,phone:t,token:a});case 5:return c=e.sent,e.next=8,E({roomId:o,userId:n,phone:t,token:a});case 8:if(u=e.sent,c&&u){e.next=12;break}return p(-1),e.abrupt("return");case 12:return g=m.default.createClient({sdkAppId:1400188366,useStringRoomId:!0,userId:r,userSig:c,mode:"rtc"}),b=m.default.createStream({audio:!0,video:!0}),e.next=16,b.initialize();case 16:return g.on("network-quality",function(){var e=(0,i.default)(l.default.mark((function e(t){var a,r,n;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=t.uplinkNetworkQuality,e.next=3,g.getTransportStats();case 3:r=e.sent,n=r.rtt,y.uplink.push(a),y.rtt.push(n);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=19,g.join({roomId:o});case 19:return e.next=21,g.publish(b);case 21:return(w=m.default.createClient({sdkAppId:1400188366,useStringRoomId:!0,userId:n,userSig:u,mode:"rtc"})).on("stream-added",function(){var e=(0,i.default)(l.default.mark((function e(t){return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,w.subscribe(t.stream,{audio:!0,video:!0});case 2:w.on("network-quality",(function(e){var t=e.downlinkNetworkQuality;y.downlink.push(t)}));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=25,w.join({roomId:o});case 25:case"end":return e.stop()}}),e)})));return function(t,a){return e.apply(this,arguments)}}(),Y=function(){k=setInterval((function(){P((function(e){var t=e-1;return 0===t?(G(),clearInterval(k),0):t}))}),1e3)},G=function(){var t=Math.ceil(y.uplink.reduce((function(e,t){return e+t}),0)/y.uplink.length),a=Math.ceil(y.downlink.reduce((function(e,t){return e+t}),0)/y.downlink.length),r=Math.ceil(y.rtt.reduce((function(e,t){return e+t}),0)/y.rtt.length);z(t),V(a),L(r),e.handleReport({uplinkAverage:t,downlinkAverage:a,rttAverage:r}),g&&g.leave(),w&&w.leave(),b.getAudioTrack().stop(),b.getVideoTrack().stop()};return c.default.createElement("div",{className:"wrapper"},n>-1&&(O>0?c.default.createElement("div",{className:"loading-time"},c.default.createElement(v.default,null),c.default.createElement("div",{className:"count-down"},t("剩余检测时间")," ",O," s")):c.default.createElement("div",{className:"wrapper-list"},c.default.createElement("div",{className:"item-container"},c.default.createElement("div",null,t("网络延时")),c.default.createElement("div",null,U,"ms")),c.default.createElement("div",{className:"item-container"},c.default.createElement("div",null,t("上行网络质量")),c.default.createElement("div",null,t(s.NETWORK_QUALITY[W]))),c.default.createElement("div",{className:"item-container"},c.default.createElement("div",null,t("下行网络质量")),c.default.createElement("div",null,t(s.NETWORK_QUALITY[D]))))),-1===n&&c.default.createElement(c.default.Fragment,null,c.default.createElement("div",{className:"login-failed"},t("登录失败")),c.default.createElement("div",{className:"re-login"},c.default.createElement(f.default,{color:"default",variant:"contained",onClick:function(){return q()}},t("重新登录")))))};var l=r(a(63)),o=r(a(31)),i=r(a(64)),c=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var a=h(t);if(a&&a.has(e))return a.get(e);var r={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,a&&a.set(e,r),r}(a(0)),u=r(a(312)),s=a(135),d=a(49),f=r(a(44)),p=r(a(313)),m=r(a(92)),v=r(a(141));function h(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(h=function(e){return e?a:t})(e)}a(335);var g=null,w=null,b=null,y={uplink:[],downlink:[],rtt:[]},k=-10,E=function(){var e=(0,i.default)(l.default.mark((function e(t){var a,r,n,o,i,c;return l.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=t.roomId,r=t.userId,n=t.phone,o=t.token,e.next=3,p.default.post("https://demo-2gfpjobv0b7026e1-1256993030.ap-shanghai.app.tcloudbase.com/userSigService",{pwd:"12345678",appid:1400188366,roomnum:parseInt(a),identifier:r,phone:n,token:o});case 3:if(!(i=e.sent)||0!==i.data.errorCode){e.next=7;break}return c=i.data.data.userSig,e.abrupt("return",c);case 7:console.error("got invalid json:".concat(i));case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},333:function(e,t,a){var r=a(334);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},334:function(e,t,a){(t=a(61)(!1)).push([e.i,'.loading{display:flex;justify-content:center;align-items:center;min-height:200px;margin:0}.clock-loader{--clock-color: #1E88E5;--clock-width: 3rem;--clock-radius: calc(var(--clock-width) / 2);--clock-minute-length: calc(var(--clock-width) * 0.4);--clock-hour-length: calc(var(--clock-width) * 0.2);--clock-thickness: 0.2rem;position:relative;display:flex;justify-content:center;align-items:center;width:var(--clock-width);height:var(--clock-width);border:3px solid var(--clock-color);border-radius:50%}.clock-loader::before,.clock-loader::after{position:absolute;content:"";top:calc(var(--clock-radius)*.25);width:var(--clock-thickness);background:var(--clock-color);border-radius:10px;transform-origin:center calc(100% - var(--clock-thickness)/2);animation:spin infinite linear}.clock-loader::before{height:var(--clock-minute-length);animation-duration:2s}.clock-loader::after{top:calc(var(--clock-radius)*.25 + var(--clock-hour-length));height:var(--clock-hour-length);animation-duration:15s}@keyframes spin{to{transform:rotate(1turn)}}',""]),e.exports=t},335:function(e,t,a){var r=a(336);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},336:function(e,t,a){(t=a(61)(!1)).push([e.i,".loading-time{width:100%;display:flex;flex-direction:column;justify-content:center;align-items:center}.count-down{font-size:14px;color:rgba(0,0,0,.85);display:flex;justify-content:center;padding-bottom:10px}.re-login{width:100%;display:flex;justify-content:center;padding:20px 0}.wrapper-list{width:100%}.wrapper-list .item-container{font-size:14px;width:100%;display:flex;flex-direction:row;padding:10px 0;justify-content:space-between}.login-failed{display:flex;justify-content:center;padding:20px 0;color:#f44336;font-size:14px;font-weight:bold}",""]),e.exports=t},337:function(e,t,a){var r=a(338);"string"==typeof r&&(r=[[e.i,r,""]]);var n={hmr:!0,transform:void 0,insertInto:void 0};a(62)(r,n);r.locals&&(e.exports=r.locals)},338:function(e,t,a){(t=a(61)(!1)).push([e.i,"html,body{padding:0;margin:0;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;width:100%;height:100%}*{box-sizing:border-box}#capture{width:100%}.headline{display:flex}.bottom{display:flex;justify-content:center;align-items:center}.downloadIcon{width:24px;height:24px;display:flex;justify-content:center;align-items:center;border:0;border-radius:3px;color:#fff;padding:0 0;cursor:pointer;user-select:none}.downloadIcon>svg{user-select:none}",""]),e.exports=t},339:function(e,t,a){"use strict";var r=a(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(a(343)),l=r(a(340)),o=r(a(341)),i=a(49);n.default.use(i.initReactI18next).init({resources:{zh:{translation:o.default},en:{translation:l.default}},fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1}});t.default=n.default},340:function(e){e.exports=JSON.parse('{"检测结果概览":"Overview of results","是否支持 TRTC":"Browser supports TRTC","是否支持进房【 检测项 3 && 5 】":"Browser supports join room【 Item 3 && 5 】","是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】":"Browser supports publish local stream【 Item  3 && 5 && 11 && (6 || 7 || 15 || 16) 】","是否支持拉流【 检测项 3 && 5 && 12 】":"Browser supports subscribe remote stream【 Item 3 && 5 && 12 】","如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。":"If 15 or 16 support, you can get the data stream from the media device. if 6 or 7 support, you can get the data stream from Video or Canvas.","浏览器支持 H264 编码，则可以支持推本地流":"Browser supports H264 encoding, you can publish LocalStream","浏览器支持 H264 解码，则可以支持拉远端流":"Browser supports H264 decoding, you can subscribe RemoteStream","是否可以从摄像头和麦克风采集视频和音频":"Can you get media from camera and microphone","是否支持从 Canvas 采集本地流":"Can you capture local stream from Canvas","是否支持从 Video 采集本地流":"Can you capture local stream from Video","MediaStreamTrack 是否有 applyConstraints 方法":"MediaStreamTrack support applyConstraints()","replaceTrack 支持动态操作 MediaStreamTrack":"The replaceTrack method supports dynamic manipulation of MediaStreamTrack","生成报告图":"Generate report","基础环境":"Basic environment","API 支持":"API support","编码支持":"Encoding support","H264 编码":"H264 encode","H264 解码":"H264 decode","VP8 编码":"VP8 encode","VP8 解码":"VP8 decode","设备详情":"Device details","麦克风设备列表":"Microphone list","摄像头设备列表":"Camera list","扬声器设备列表":"Speaker list","设备支持度检测":"Device support detection","操作系统":"Operating system","浏览器":"Browser","UA":"UA","屏幕分辨率":"Display resolutions","逻辑处理器数量":"Number of logical processors","开始检测":"Start testing","最大分辨率":"Maximum resolution","最大帧率":"Maximum frame rate","是":"Yes","否":"No","部分支持":"Partial","是否允许使用摄像头":"Website has camera permissions","是否允许使用麦克风":"Website has microphone permissions","是否看到视频画面":"Do you see the video","是否看到音量条变化":"Can you see the volume bar change","点击播放后能否听到音乐":"Can you hear music after clicking","是否支持获取媒体设备及媒体流":"Browser allows getUserMedia on this page","是否支持屏幕分享":"Browser supports screen sharing","是否支持 WebRTC":"Browser Supports WebRTC","是否支持 WebAudio":"Browser Supports Web Audio API","是否支持 WebSocket":"Browser Supports WebSocket","是否支持从 Canvas 获取数据流":"Browser supports stream capturing from Canvas","是否支持从 Video 获取数据流":"Browser supports stream capturing from video","是否支持 RTCRtpSender.replaceTrack 方法":"Browser supports RTCRtpSender.replaceTrack","是否支持 applyConstraints":"Browser supports applyConstraints","摄像头检测":"Camera detection","麦克风检测":"Microphone detection","扬声器检测":"Speaker detection","当前环境不支持获取":"Browser does not support acquisition","检测完毕":"Detection completed","网络检测":"Network detection","检测网络前需要先登录，如果您没有登录的话会自动跳转登录":"You need to sign in before detecting the network, or it will automatically jump to login page","剩余检测时间":"Remaining detection time","网络延时":"Network delay","上行网络质量":"Upstream network quality","下行网络质量":"Downstream network quality","登录失败":"Login failed","重新登录":"Login again","未知":"UNKNOWN","极佳":"PERFECT","较好":"GOOD","一般":"GENERAL","差":"BAD","极差":"AWFUL","断开":"DISCONNECTED","http":"The full TRTC functionality is only available under the HTTPS protocol.","refer":"Reference URL Domain Protocol Restrictions"}')},341:function(e){e.exports=JSON.parse('{"检测结果概览":"检测结果概览","是否支持 TRTC":"是否支持 TRTC","是否支持进房【 检测项 3 && 5 】":"是否支持进房【 检测项 3 && 5 】","是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】":"是否支持推流【 检测项 3 && 5 && 11 && (6 || 7 || 15 || 16) 】","如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。":"如果 15 或 16 支持，您可以从媒体设备获取数据流。如果 6 或 7 支持，您可以从 Video 或者 Canvas 采集数据流。","是否支持拉流【 检测项 3 && 5 && 12 】":"是否支持拉流【 检测项 3 && 5 && 12 】","浏览器支持 H264 编码，则可以支持推本地流":"浏览器支持 H264 编码，则可以支持推本地流","浏览器支持 H264 解码，则可以支持拉远端流":"浏览器支持 H264 解码，则可以支持拉远端流","是否可以从摄像头和麦克风采集视频和音频":"是否可以从摄像头和麦克风采集视频和音频","是否支持从 Canvas 采集本地流":"是否支持从 Canvas 采集本地流","是否支持从 Video 采集本地流":"是否支持从 Video 采集本地流","MediaStreamTrack 是否有 applyConstraints 方法":"MediaStreamTrack 是否有 applyConstraints 方法","是否支持对通话能力无影响":"是否支持对通话能力无影响","生成报告图":"生成报告图","基础环境":"基础环境","API 支持":"API 支持","编码支持":"编码支持","H264 编码":"H264 编码","H264 解码":"H264 解码","VP8 编码":"VP8 编码","VP8 解码":"VP8 解码","设备详情":"设备详情","麦克风设备列表":"麦克风设备列表","摄像头设备列表":"摄像头设备列表","扬声器设备列表":"扬声器设备列表","设备支持度检测":"设备支持度检测","操作系统":"操作系统","浏览器":"浏览器","UA":"UA","屏幕分辨率":"屏幕分辨率","逻辑处理器数量":"逻辑处理器数量","开始检测":"开始检测","最大分辨率":"最大分辨率","是":"是","否":"否","部分支持":"部分支持","是否允许使用摄像头":"是否允许使用摄像头","是否允许使用麦克风":"是否允许使用麦克风","是否看到视频画面":"是否看到视频画面","是否看到音量条变化":"是否看到音量条变化","点击播放后能否听到音乐":"点击播放后能否听到音乐","是否支持获取媒体设备及媒体流":"是否支持获取媒体设备及媒体流","是否支持屏幕分享":"是否支持屏幕分享","是否支持 WebRTC":"是否支持 WebRTC","是否支持 WebAudio":"是否支持 WebAudio","是否支持 WebSocket":"是否支持 WebSocket","是否支持从 Canvas 获取数据流":"是否支持从 Canvas 获取数据流","是否支持从 Video 获取数据流":"是否支持从 Video 获取数据流","replaceTrack 支持动态操作 MediaStreamTrack":"replaceTrack 支持动态操作 MediaStreamTrack","是否支持 RTCRtpSender.replaceTrack 方法":"是否支持 RTCRtpSender.replaceTrack 方法","是否支持 applyConstraints":"是否支持 applyConstraints","摄像头检测":"摄像头检测","麦克风检测":"麦克风检测","扬声器检测":"扬声器检测","当前环境不支持获取":"当前环境不支持获取","检测完毕":"检测完毕","网络检测":"网络检测","检测网络前需要先登录，如果您没有登录的话会自动跳转登录":"检测网络前需要先登录，如果您没有登录的话会自动跳转登录","剩余检测时间":"剩余检测时间","网络延时":"网络延时","上行网络质量":"上行网络质量","下行网络质量":"下行网络质量","登录失败":"登录失败","重新登录":"重新登录","未知":"未知","极佳":"极佳","较好":"较好","一般":"一般","差":"差","极差":"极差","断开":"断开","http":"只有在 HTTPS 协议下才能使用全部的 TRTC 功能。","refer":"参考 URL 域名协议限制"}')},97:function(e,t,a){"use strict";var r=a(28);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(98),l=r(a(44)),o=(0,n.styled)(l.default)({background:"linear-gradient(45deg, #2196f3 30%, #2196f3 90%)",border:0,borderRadius:3,boxShadow:"0 2px 2px 2px rgba(33, 150, 243, 0.15)",color:"white",height:36,padding:"0 15px"});t.default=o}});