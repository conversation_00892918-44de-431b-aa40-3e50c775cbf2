<template>
  <div id="app" style="height: 100%;position: relative">
    <NavBar/>
    <div class="main">
      <Room/>
    </div>
    <div class="card" v-if="isOpen">
      <PermitCard :handleClose="handleClose"/>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/ui/NavBar';
import PermitCard from '@/components/ui/PermitCard';
import Room from '@/components/Room';
import { isChrome, isMac, isEdge } from '@/utils/utils';
import { EventBus } from '@/event-bus';

export default {
  name: 'App',
  components: {
    NavBar,
    Room,
    PermitCard,
  },
  data() {
    return {
      isOpen: false,
    };
  },
  created() {
    EventBus.$on('permit-failed', this.handleOpen);
    this.$aegis.reportEvent({
      name: 'loaded',
      ext1: 'loaded-success',
      ext2: 'webrtcExpress',
    });
  },
  methods: {
    handleClose() {
      this.isOpen = false;
    },
    handleOpen() {
      if (isMac && isChrome && !isEdge) {
        this.isOpen = true;
      }
    },
  },
};
</script>

<style lang="stylus">
*
  box-sizing border-box
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, 'Microsoft YaHei', PingFang SC, sans-serif;

body
  margin 0
  padding 0
  width 100%
  height 100vh
  background-color #fff

.main
  padding-top 10px
  display flex
  justify-content center

.rtc-btn
  width 80px
  height 30px
  cursor: pointer;
  font-size 12px
  line-height 20px

.card
  position absolute
  right 0
  bottom 0

</style>
