/* eslint-disable*/
import TRTC from 'trtc-js-sdk'


class ShareClient {
  constructor(options) {
    const {
      sdkAppId,
      userId,
      roomId,
      userSig
    } = options
    this.sdkAppId = sdkAppId
    this.userId = userId
    this.roomId = roomId
    this.userSig = userSig

    this.client = null
    this.localStream = null

    this.isJoined = false
    this.isPublished = false
    this.isLeaving = false

    this.initClient()
  }

  initClient() {
    try {
      this.client = TRTC.createClient({
        mode: 'rtc',
        sdkAppId: this.sdkAppId,
        userId: this.userId,
        userSig: this.userSig
      })
      console.log(`Client [${this.userId}] created.`)
      this.installEventHandlers()
    } catch (e) {
      console.error(`Failed to create Client [${this.userId}].`)
    }
  }

  async initLocalStream() {
    try {
      this.localStream = TRTC.createStream({
        audio: false,
        screen: true,
        userId: this.userId
      })
      this.localStream.setScreenProfile({
        width: 660,
        height: 380,
        frameRate: 15,
        bitrate: 2000
      })
      await this.localStream.initialize()
      this.localStream.on('screen-sharing-stopped', event => {
        console.log('ShareStream video track ended')
        this.leave()
      })
      return this.localStream
    } catch (e) {
      console.error(`ShareStream failed to initialize. Error: ${e.message_}`)
    }
  }

  // join room
  async join() {
    if (this.isJoined) {
      console.warn('duplicate join() observed')
      return
    }

    try {
      await this.client.join({roomId: this.roomId})
      this.isJoined = true
    } catch (e) {
      console.error('join room failed', e)
      this.isJoined = false
    }
  }

  async publish() {
    if (!this.isJoined) {
      console.warn('call publish()- please join() firstly')
      return
    }
    if (this.isPublished) {
      console.warn('duplicate publish() observed')
      return
    }
    try {
      await this.client.publish(this.localStream)
      console.log(`ShareStream is published successfully`)
      this.isPublished = true
    } catch (error) {
      console.error(`ShareStream is failed to publish. Error: ${error.message_}`)
      this.isPublished = false
    }
  }

  async unpublish() {
    if (!this.isJoined) {
      console.warn('unpublish() - please join() firstly')
      return
    }
    if (!this.isPublished) {
      console.warn('call unpublish() - you have not published yet')
      return
    }
    try {
      if (this.localStream) {
        await this.client.unpublish(this.localStream)
      }
      this.isPublished = false
      console.log(`Unpublish ShareStream success`)
    } catch (error) {
      console.error('unpublish failed', error)
    }
  }

  async leave() {
    if (!this.isJoined) {
      console.warn('leave() - please join() firstly')
      return
    }
    if (this.isLeaving) {
      console.warn('duplicate leave() observed')
      return
    }
    try {
      this.isLeaving = true

      await this.client.leave()
      this.isJoined = false
      console.log(`Local share client leave room success`)

      if (this.localStream) {
        this.localStream.stop()
        this.localStream.close()
        this.localStream = null
      }
      this.isLeaving = false
    } catch (error) {
      console.error('leave failed', error)
    }
  }

  installEventHandlers() {
    this.client.on('error', this.handleError.bind(this))
    this.client.on('client-banned', this.handleBanned.bind(this))
    this.client.on('stream-subscribed', this.handleStreamSubscribed.bind(this))
  }

  handleError(error) {
    console.error('client error', error)
    alert(error)
  }

  handleBanned(error) {
    console.error('client has been banned for ' + error)
    alert('You have been banned from the room!')
  }

  handleStreamSubscribed(event) {
    const remoteStream = event.stream
    const id = remoteStream.getId()
    const userId = remoteStream.getUserId()
    console.log(`remote stream subscribed: [${userId}] ID: ${id} type: ${remoteStream.getType()}`)

    this.client.unsubscribe(remoteStream)
  }
}

export default ShareClient
