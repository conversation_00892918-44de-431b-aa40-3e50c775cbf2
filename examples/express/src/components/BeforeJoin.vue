<!-- eslint-disable max-len -->
<template>
  <div class="before-join-wrapper">
    <div id="preview" class="preview">
      <button v-if="!isPermitting && !isPermitted" class="rtc-btn permit-btn" @click="toggleDevice">
        {{ $t('Authorizing') }}
      </button>
      <div v-if="isNotSupported" class="not-supported">
        {{ $t('notSupported') }}
      </div>
    </div>
    <div class="audio-level">
      <div class="audio-icon">
        <svg height="24" version="1.1" viewBox="0 0 21 29" width="16" xmlns="http://www.w3.org/2000/svg"
             xmlns:xlink="http://www.w3.org/1999/xlink">
          <title>audio</title>
          <g id="audio" fill="rgba(0, 110, 255, 0.8)" fill-rule="evenodd" stroke="none" stroke-width="1">
            <g id="audio" fill="" transform="translate(-7.000000, -4.000000)">
              <path
                d="M26.8333333,16.3030303 C27.4773333,16.3030303 28,16.8373333 28,17.495671 C28,23.0104416 23.9085,27.5627511 18.6666667,28.1590714 L18.6666667,28.1590714 L18.6666667,30.6147186 L23.3333333,30.6147186 C23.9773333,30.6147186 24.5,31.1490216 24.5,31.8073593 C24.5,32.465697 23.9773333,33 23.3333333,33 L23.3333333,33 L11.6666667,33 C11.0226667,33 10.5,32.465697 10.5,31.8073593 C10.5,31.1490216 11.0226667,30.6147186 11.6666667,30.6147186 L11.6666667,30.6147186 L16.3333333,30.6147186 L16.3333333,28.1590714 C11.0915,27.5639437 7,23.0104416 7,17.495671 C7,16.8373333 7.52266667,16.3030303 8.16666667,16.3030303 C8.81066667,16.3030303 9.33333333,16.8373333 9.33333333,17.495671 C9.33333333,22.1064199 12.9896667,25.8441558 17.5,25.8441558 L17.5,25.8441558 L17.7496777,25.8403286 C22.1444992,25.7054218 25.6666667,22.0210357 25.6666667,17.495671 C25.6666667,16.8373333 26.1893333,16.3030303 26.8333333,16.3030303 Z M17.5,4 C20.882225,4 23.625,6.5141633 23.625,9.61447811 L23.625,18.5976431 C23.625,21.6274962 21.0054799,24.0975383 17.7295907,24.2082479 L17.5,24.2121212 C14.117775,24.2121212 11.375,21.6979579 11.375,18.5976431 L11.375,9.61447811 C11.375,6.584625 13.9945201,4.11458291 17.2704093,4.00387328 L17.5,4 Z"></path>
            </g>
          </g>
        </svg>
      </div>
      <div class="level-container">
        <div :style="{ width: audioLevel * 100 > 100 ? 100 : audioLevel * 100 + '%' }" class="level-bar"></div>
      </div>
    </div>
  </div>
</template>

<script>
import TRTC from 'trtc-js-sdk';
import { EventBus } from '@/event-bus';

export default {
  name: 'BeforeJoin',
  props: ['handleDeviceFailed'],
  data() {
    return {
      isPermitting: false,
      isPermitted: false,
      isNotSupported: false,
      localStream: null,
      audioLevel: 0,
      intervalId: 0,
    };
  },
  methods: {
    async toggleDevice() {
      let loadingInstance;
      try {
        this.localStream = TRTC.createStream({
          audio: true,
          video: true,
        });
        this.isPermitting = true;
        loadingInstance = this.$loading({
          target: '#preview',
          text: 'Loading',
          body: this.isPermitting,
        });
        await this.localStream.initialize();
        await this.localStream.play('preview');
        this.isPermitting = false;
        this.isPermitted = true;
        loadingInstance.close();
        this.intervalId = setInterval(() => {
          this.audioLevel = this.localStream.getAudioLevel() * 4;
        }, 200);
      } catch (error) {
        console.log(error);
        this.isNotSupported = true;
        this.handleDeviceFailed();
        loadingInstance.close();
        EventBus.$emit('permit-failed');
      }
    },
  },
  beforeDestroy() {
    this.localStream && this.localStream.close();
    clearInterval(this.intervalId);
    this.localStream = null;
  },
};
</script>

<style lang="stylus" scoped>
.before-join-wrapper
  width 100%
  padding 30px 0
  display flex
  flex-direction column
  justify-content center
  align-items center

  .permit-btn
    border: 1px solid rgba(0, 110, 255, 0.2);
    color: rgb(0, 110, 255);
    background: #ecf5ff;

  .preview
    background-color #eee
    width 320px
    height 240px
    display flex
    justify-content center
    align-items center

.audio-level
  width 320px
  padding 30px 0 0 0
  display flex
  flex-direction row
  color: #eee
  justify-content space-between
  align-items center

  .audio-icon
    width 24px
    height 24px
    margin-right 10px

  .level-container
    height 12px
    width 100%
    background #eee

    .level-bar
      background rgba(0, 110, 255, 0.8)
      height 100%
      width 0
      transition 0.5s

.not-supported
  font-size 12px
  color rgba(0, 0, 0, 0.8)
  width 180px
</style>
