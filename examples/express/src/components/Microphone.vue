<template>
  <div class='icon-box'>
    <div class='icon-item' @click='handleMuteAudio'>
      <div class='icon'>
        <img v-if="!muted" height='24px' src='../assets/icons/audio.svg'/>
        <img v-else height='24px' src='../assets/icons/audio-muted.svg'/>
      </div>
      <div class='tag'>Microphone</div>
    </div>
    <el-dropdown trigger='click' @command='handleCommand'>
      <div class='triangle-up'>
        <img height='16px' src='../assets/icons/triangle-up.png' width='20px'/>
      </div>
      <el-dropdown-menu slot='dropdown'>
        <template v-for='item in microphoneList'>
          <el-dropdown-item :key='item.deviceId' :command='item'>{{ item.label }}</el-dropdown-item>
        </template>
      </el-dropdown-menu>
    </el-dropdown>
  </div>

</template>

<script>
import TRTC from 'trtc-js-sdk';
import { EventBus } from '@/event-bus';

export default {
  name: 'microphone',
  props: ['muted', 'muteAudio', 'unmuteAudio'],
  data() {
    return {
      default: 'default',
      microphoneList: [],
    };
  },
  created() {
    this.updateDevice();
    document.addEventListener('devicechange', () => {
      this.updateDevice();
    });
  },
  methods: {
    handleMuteAudio() {
      if (this.muted) {
        this.unmuteAudio();
      } else {
        this.muteAudio();
      }
    },
    async updateDevice() {
      this.microphoneList = await TRTC.getMicrophones();
    },
    handleCommand(command) {
      const { deviceId } = command;
      this.default = 'default';
      EventBus.$emit('switch-device', { audioId: deviceId });
    },
  },
};

</script>

<style lang='stylus' scoped>

</style>
