<template>
  <el-card class="box-card">
    <div class="close" @click="handleClose">×</div>
    <div class="card-wrapper">
      <div class="title">{{ $t('authorization') }}</div>
      <div class="button">{{ $t('preferences') }}</div>
      <div class="allow">{{ $t('privacy') }}</div>
      <div class="image-wrapper">
        <img v-if="language === 'zh'" src="../../assets/permit-zh.png" class="image" alt="system permit">
        <img v-if="language === 'en'" src="../../assets/permit-en.png" class="image" alt="system permit">
      </div>
    </div>
  </el-card>
</template>

<script>

export default {
  name: 'PermitCard',
  props: ['handleClose'],
  computed: {
    language: {
      get() {
        return this.$i18n.locale;
      }
    }
  },
};
</script>

<style lang="stylus" scoped>
.card-wrapper
  display flex
  flex-direction column
  align-items center
  width 500px
  position relative

.title
  padding 10px 0
  font-size 16px
  font-weight 500
  line-height 22px

.desc
  font-size 12px
  margin-bottom 10px

.button
  background-color #104EF5
  color #fff
  font-size 12px
  height 37px;
  border-radius 4px
  padding 10px

.allow
  font-size 12px
  color #666
  padding 10px 0 20px 0

.image
  width 100%

.close
  position absolute
  z-index 999
  display flex
  justify-content center
  align-items center
  font-size 20px
  right 20px
  cursor pointer
  width 20px
  height 20px
</style>
