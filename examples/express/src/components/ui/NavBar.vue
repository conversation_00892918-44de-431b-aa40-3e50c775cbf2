<!-- eslint-disable max-len -->
<template>
  <div class='text-white header'>
    <img v-if="$i18n.locale === 'en'" src='https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-en-w.png'
         style='height: 100%' />
    <img v-else src='https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-cn-w.png' />
    <div class='tag'>
      <el-popover
        placement='bottom'
        width='320'
        trigger='manual'
        v-model='isModalVisible'>
        <div style='padding: 10px'>
          <div class='triangle'></div>
          <div class='tag-close' @click='visible = false'>×</div>
          <div class='tag-title'>{{ $t('permit') }}</div>
          <div class='tag-desc'>{{$t('better')}}</div>
          <div class='tag-link'>
            <div style='display: flex;justify-content: center;align-items: center;border-radius: 50%;border: 2px solid #f56c6c;padding: 10px'>
              <img src='../../assets/tip.png' style='width: 16px; height: 16px'/>
            </div>
            <div class='tag-content'>https://web.sdk.qcloud.com/trtc/webrtc/demo/express/</div>
          </div>
          <img v-if="language === 'en'" src='../../assets/tip-en.png' style='width: 100%; height: auto'/>
          <img v-if="language === 'zh'" src='../../assets/tip-zh.png' style='width: 100%; height: auto'/>
        </div>
      </el-popover>
    </div>
    <div style='flex-direction: row;display: flex;justify-content: center;align-items: center'>
      <div @click='handleLanguage' style="cursor: pointer">中/En</div>
      <a class='github' href='https://github.com/LiteAVSDK/TRTC_Web' target='_blank'>
        <svg aria-hidden='true' class='octicon octicon-mark-github v-align-middle' data-view-component='true'
             height='32' version='1.1' viewBox='0 0 16 16'
             width='32'>
          <path
            d='M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z'
            fill-rule='evenodd'></path>
        </svg>
      </a>
    </div>
  </div>
</template>

<script>
import { EventBus } from '@/event-bus';
import { isChrome } from '@/utils/utils';

export default {
  name: 'NavBar',
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    isModalVisible: {
      get() {
        if (isChrome) {
          return this.visible;
        }
        return false;
      },
      set(val) {}
    },
    language: {
      get() {
        return this.$i18n.locale;
      }
    }
  },
  created() {
    document.title = this.$i18n.t('title');
    EventBus.$on('permit-failed', () => {
      this.visible = true;
    });
  },
  methods: {
    goToGithub() {
      window.open('https://github.com/LiteAVSDK/TRTC_Web', '_blank');
    },
    handleLanguage() {
      switch (this.$i18n.locale) {
        case 'en':
          this.$i18n.locale = 'zh';
          localStorage.setItem('trtc-express-language', 'zh');
          break;
        case 'zh':
          this.$i18n.locale = 'en';
          localStorage.setItem('trtc-express-language', 'en');
          break;
      }
      document.title = this.$i18n.t('title');
    },
  },
};
</script>

<style lang='stylus' scoped>
.triangle
  width: 0;
  height: 0;
  border-top 5px solid transparent
  border-right 10px solid transparent
  border-bottom 10px solid white
  border-left 10px solid transparent
  position absolute
  top -15px
  left 80px
.tag
  position absolute
  left 30px
  text-align left
  .tag-close
    position absolute
    top -2px
    font-size 24px
    right 10px
    color #C1C6C8
    user-select none
    cursor pointer
  .tag-title
    font-size 18px
    font-weight 500
    color #000
  .tag-desc
    padding 10px 0
    font-size 12px
    text-align left
    color #000
  .tag-link
    padding 0 6px
    display flex
    justify-content center
    align-items center
    border-radius 4px
    background rgba(0,0,0,0.10)
    width 100%
    height 38px
  .tag-content
    color #000
    font-size 16px
    padding 0 6px
    overflow hidden
    text-overflow ellipsis
    white-space nowrap
.container
  padding 0
  width 100%

.header
  height 50px
  transparent 10%
  padding 10px 10px
  background-color #00182F
  color white
  display flex;
  justify-content space-between

.content
  padding 20px 0;

.github
  color rgb(255, 255, 255)
  fill rgb(255, 255, 255)
  padding 10px
</style>
