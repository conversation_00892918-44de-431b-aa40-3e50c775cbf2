<!-- eslint-disable max-len -->
<template>
  <div>
    <p class='title'>{{ $t('welcome') }}</p>
    <p v-if='!isJoined' class='desc'>{{ $t('permit') }}</p>
    <p v-if='isJoined' class='desc'>
      <span>
        {{ $t('desc') }}
        <span v-if='isJoined' class='line-desc'>
          <el-popover
            placement='bottom'
            trigger='hover'
          >
            <span style='display: flex; justify-content: space-around'>
              <img height='80px' src='../../assets/android.png' width='80px'/>
              <span style='border: 1px solid #f2f2f2; margin: 0 10px'></span>
              <img height='80px' src='../../assets/ios.png' width='80px'/>
            </span>
            <span slot='reference'>
              <img alt='qrcode' class='qrcode' src='../../assets/icons/qrcode.svg'/>
            </span>
          </el-popover>
        </span>
      </span>
    </p>
    <div class="tips">
      <img height='16px' src='../../assets/icons/tips.svg' style="margin-top: 2px"/>
      <div v-if='!isJoined' class="tips-content">
        <p class="tips-desc">{{$t('usage')}}
          <a style="color: #006EFF;cursor: pointer;text-decoration: none" :href='url' target='_blank'>{{$t('statistics')}}
            <img style="margin-top: 1px" height='12px' src='../../assets/icons/link.svg'>
          </a>
        </p>
      </div>
      <div v-else class="tips-content">
        <p class="tips-desc">{{$t('consume')}}
          <a style="color: #006EFF;cursor: pointer;text-decoration: none" :href='url' target='_blank'>{{$t('statistics')}}
            <img style="margin-top: 1px" height='12px' src='../../assets/icons/link.svg'>
          </a>
        </p>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Welcome',
  props: ['isJoined', 'sdkAppId'],
  computed: {
    url() {
      const link = this.$i18n.locale === 'en' ? 'https://console.intl.cloud.tencent.com/trtc/statistics' : 'https://console.cloud.tencent.com/trtc/statistics';
      if (this.sdkAppId) {
        return `${link}?id=${this.sdkAppId}`;
      }
      return link;
    },
  },
};
</script>

<style lang='stylus' scoped>
.title
  font-size 20px
  line-height 20px
  font-weight 500
  color #333333

.desc
  display flex
  font-size 12px
  color rgba(0, 0, 0, 0.8)

.tips
  display flex
  background #D3E7FE
  width 100%
  padding 14px 12px 14px 20px

  .tips-content
    padding-left 10px

  .tips-desc
    margin 0
    font-size 12px
    line-height 20px


.qrcode
  margin-left 4px
  width 14px
  height 14px
</style>
