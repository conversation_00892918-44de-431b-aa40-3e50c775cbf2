<template>
  <svg xmlns="http://www.w3.org/2000/svg" t="1634818715268"
       viewBox="0 0 24 24" version="1.1" width="24px" height="24px">
    <defs>
      <clipPath id="cut-off-bottom">
        <rect x="0" y="0" width="24" :height="volume"/>
      </clipPath>
    </defs>
    <rect id="Rectangle" fill-opacity="0.7" fill="#000000" x="0" y="0" width="24" height="24"></rect>
    <path
      d="M7.5,12 C7.5,14.3198 9.5145,16.2 12,16.2 C14.4855,16.2 16.5,14.3198 16.5,12 L16.5,12 L18,12 C18,14.8532 15.7065,17.1814 12.75,17.53 L12.75,17.53 L12.75,19 L11.25,19 L11.25,17.53 C8.2935,17.1814 6,14.8532 6,12 L6,12 Z M12,5 C13.656,5 15,6.2544 15,7.8 L15,7.8 L15,12 C15,13.5456 13.656,14.8 12,14.8 C10.344,14.8 9,13.5456 9,12 L9,12 L9,7.8 C9,6.2544 10.344,5 12,5 Z"
      id="Combined-Shape"
      fill="#54FF42">
    </path>
    <path
      d="M7.5,12 C7.5,14.3198 9.5145,16.2 12,16.2 C14.4855,16.2 16.5,14.3198 16.5,12 L16.5,12 L18,12 C18,14.8532 15.7065,17.1814 12.75,17.53 L12.75,17.53 L12.75,19 L11.25,19 L11.25,17.53 C8.2935,17.1814 6,14.8532 6,12 L6,12 Z M12,5 C13.656,5 15,6.2544 15,7.8 L15,7.8 L15,12 C15,13.5456 13.656,14.8 12,14.8 C10.344,14.8 9,13.5456 9,12 L9,12 L9,7.8 C9,6.2544 10.344,5 12,5 Z"
      id="Combined-Shape"
      fill="#FFFFFF"
      clip-path="url(#cut-off-bottom)"
    >
    </path>
  </svg>
</template>

<script>
export default {
  name: 'Volume',
  props: ['volume'],
};

</script>

<style scoped>

</style>
