<!-- eslint-disable max-len -->
<template>
  <div class="after-join-wrapper">
    <p class='share-link'>{{ $t('share') }}</p>
    <div class='input-wrapper'>
      <el-input v-model="input" size="small" style='width: 100%'></el-input>
      <el-button type="primary" size="small" @click="handleInvite"
                 style='background: #104EF5; border: none; margin-left: 10px;'>{{ $t('copy') }}
      </el-button>
    </div>
    <div class="user-wrapper">
      <div v-if="isShared" :class="{ 'share-container': isShared }">
        <div id="share-player" class="share-player"></div>
      </div>
      <div id="user-container" ref="user"
           :class="{ 'user-container':!isShared, 'user-container-share': isShared }">
        <div class="stream-container">
          <div id="local-player" class="local-player">
            <div class="volume">
              <template v-if="audioMuted">
                <img height='24px' width="24px" src='../assets/icons/audio-muted-remote.svg'/>
              </template>
              <template v-if="this.localStream && this.localStream.hasAudio()">
                <Volume :volume="volume[this.userId] || 24"/>
              </template>
            </div>
            <div class="overlay" v-if="videoMuted"></div>
          </div>
        </div>
        <template v-for='item in remoteStreams'>
          <div class='stream-container' :id='item.getId()' :key='item.getId()' style="position: relative">
            <div class="volume" v-if="item.hasAudio()">
              <template v-if="!audioMutedState[item.getUserId()]">
                <Volume :volume="volume[item.getUserId()] || 24"/>
              </template>
              <template v-else>
                <img height='24px' width="24px" src='../assets/icons/audio-muted-remote.svg'/>
              </template>
            </div>
            <div class="overlay" v-if="videoMutedState[item.getUserId()]"></div>
          </div>
        </template>
      </div>
    </div>
    <div class="user-control">
      <div class="icon-group">
        <Microphone :muted="audioMuted" :muteAudio="muteAudio" :unmuteAudio="unmuteAudio"/>
        <Camera :muted="videoMuted" :muteVideo="muteVideo" :unmuteVideo="unmuteVideo"/>
        <div v-if="!isMobile" class="icon-box">
          <div class="icon-item" v-if="!isShared" @click="handleShareScreen">
            <div class="icon">
              <img height="24px" src="../assets/icons/share.svg" width="24px"/>
            </div>
            <div class="tag">Screen</div>
          </div>
          <div class="icon-item" v-else @click="handleStopShare">
            <div class="icon">
              <img height="24px" src="../assets/icons/share-success.svg" width="24px"/>
            </div>
            <div class="tag">Screen</div>
          </div>
        </div>
      </div>
      <div class="leave">
        <button class="rtc-btn leave-btn" @click="leave">Leave</button>
      </div>
    </div>
    <div class="monitor-tip">
      {{ $t('dashboard') }}
      <a style="color: #006EFF;cursor: pointer;text-decoration: none"
         :href='$i18n.locale === "en" ? "https://console.intl.cloud.tencent.com/trtc/monitor" : "https://console.cloud.tencent.com/trtc/monitor"'
         target='_blank'>{{ $t('monitor') }}
        <img style="margin-top: 1px" height='12px' src='../assets/icons/link.svg'>
      </a>
    </div>
  </div>
</template>

<script>
import TRTC from 'trtc-js-sdk';
import { EventBus } from '@/event-bus';
import ShareClient from '@/utils/shareClient';
import axios from 'axios';
import { isMobile } from '@/utils/utils';
import Camera from '@/components/Camera';
import Microphone from '@/components/Microphone';
import Volume from '@/components/ui/Volume';

export default {
  name: 'Join',
  props: ['options', 'sig'],
  components: {
    Microphone,
    Camera,
    Volume,
  },
  data() {
    return {
      userSig: '',
      userId: '',
      roomId: 0,
      client: null,
      localStream: null,
      remoteStreams: [],
      isJoined: false,
      isPublished: false,
      cameraId: '',
      microphoneId: '',
      shareUserId: '',
      shareUserSig: '',
      shareClient: null,
      shareStream: null,
      isShared: false,
      input: `${window.location.host}${window.location.pathname}?sig=${this.sig}`,
      isMobile,
      audioMuted: false,
      videoMuted: false,
      volume: {},
      audioMutedState: {},
      videoMutedState: {},
    };
  },
  created() {
    this.updateDevice();
    // check current environment is supported TRTC or not
    TRTC.checkSystemRequirements().then((checkResult) => {
      if (!checkResult.result) {
        console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
        alert('Your browser does not supported TRTC!');
        window.location.href = 'https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html';
      }
    });
    this.initParams();
    this.initClient();
    this.join();
    EventBus.$on('switch-device', (event) => {
      const {
        videoId,
        audioId,
      } = event;
      videoId && (this.cameraId = videoId);
      audioId && (this.microphoneId = audioId);
      this.switchDevice(event);
    });
  },
  methods: {
    initParams() {
      const {
        userSig,
        sdkAppId,
        roomId,
        userId,
      } = this.options;
      console.log('init options', this.options);

      this.userId = userId || `user_${Math.floor(Math.random() * 1000000)}`;
      this.roomId = roomId;
      this.sdkAppId = sdkAppId;
      this.userSig = userSig;
    },
    initClient() {
      try {
        this.client = TRTC.createClient({
          mode: 'rtc',
          sdkAppId: this.sdkAppId,
          userId: this.userId,
          userSig: this.userSig,
        });
        console.log(`Client [${this.userId}] created.`);
        this.installEventHandlers();
      } catch (e) {
        console.error(`Failed to create Client [${this.userId}].`);
      }
    },
    async initLocalStream() {
      try {
        const localStream = TRTC.createStream({
          userId: this.userId,
          audio: true,
          video: true,
          cameraId: this.cameraId,
          microphoneId: this.microphoneId,
        });
        localStream.setVideoProfile('180p');
        try {
          await localStream.initialize();
          this.localStream = localStream;
          this.localStream.play('local-player');
          console.log(`LocalStream [${this.userId}] initialized`);
        } catch (e) {
          EventBus.$emit('permit-failed');
          this.$parent.isJoinClicked = false;
          this.$notify.error({
            title: 'Initialized failed',
            message: `Failed to create LocalStream. Error: ${e.message_}`,
          });
        }
      } catch (e) {
        this.$parent.isJoinClicked = false;
        this.$notify.error({
          title: 'Initialized failed',
          message: `Failed to create LocalStream. Error: ${e.message_}`,
        });
      }
    },
    // join room
    async join() {
      console.log('join room clicked');

      try {
        await this.client.join({ roomId: this.roomId });
        this.client.enableAudioVolumeEvaluation(200);
        this.isJoined = true;
        this.reportSuccessEvent('joinRoom');
        console.log(`Join room [${this.roomId}] success`);
        try {
          await this.initLocalStream();
          await this.publish();
        } catch (error) {
          console.error(`Init LocalStream failed. Error: ${error.message_}`);
        }
      } catch (e) {
        this.reportFailedEvent('joinRoom');
        console.error(`Join room ${this.roomId} failed, please check your params. Error: ${e.message_}`);
      }
    },
    async publish() {
      if (!this.isJoined) {
        console.warn('call publish()- please join() firstly');
        return;
      }
      if (this.isPublished) {
        console.warn('duplicate publish() observed');
        return;
      }
      if (!this.localStream) {
        return;
      }
      try {
        await this.client.publish(this.localStream);
        this.isPublished = true;
        this.reportSuccessEvent('publish');
        console.log('LocalStream is published successfully');
      } catch (error) {
        this.reportFailedEvent('publish', error);
        console.error(`LocalStream is failed to publish. Error: ${error.message_}`);
      }
    },
    async unpublish() {
      if (!this.isJoined) {
        console.warn('unpublish() - please join() firstly');
        return;
      }
      if (!this.isPublished) {
        console.warn('call unpublish() - you have not published yet');
        return;
      }
      try {
        await this.client.unpublish(this.localStream);
        this.isPublished = false;
        this.reportSuccessEvent('unpublish');
        console.log('Unpublish localStream success');
      } catch (error) {
        this.reportFailedEvent('unpublish', error);
        console.error(`LocalStream is failed to unpublish. Error: ${error.message_}`);
      }
    },
    async leave() {
      this.$parent.isJoinClicked = false;
      if (!this.isJoined) {
        console.warn('leave() - please join() firstly');
        return;
      }
      await this.unpublish();
      try {
        await this.client.leave();
        console.log('Leave room success');
        this.isJoined = false;
        this.reportSuccessEvent('leaveRoom');
        if (this.localStream) {
          this.localStream.stop();
          this.localStream.close();
          this.localStream = null;
        }
        if (this.isShared) {
          try {
            await this.shareClient.unpublish();
            await this.shareClient.leave();
            this.isShared = false;
            this.reportSuccessEvent('stopScreenShare');
          } catch (error) {
            this.reportFailedEvent('stopScreenShare', error);
          }
        }
      } catch (error) {
        this.reportFailedEvent('leaveRoom', error);
        console.error('leave failed', error);
      }
    },
    async switchDevice({
      videoId,
      audioId,
    }) {
      console.log('call switchDevice');
      if (!this.isJoined) {
        return;
      }
      if (videoId) {
        try {
          await this.localStream.switchDevice('video', videoId);
          console.log('Switch video device success');
        } catch (error) {
          console.error('switchDevice failed', error);
        }
      }
      if (audioId) {
        try {
          await this.localStream.switchDevice('audio', audioId);
          console.log('Switch audio device success');
        } catch (error) {
          console.error('switchDevice failed', error);
        }
      }
    },
    installEventHandlers() {
      this.client.on('error', this.handleError.bind(this));
      this.client.on('client-banned', this.handleBanned.bind(this));
      this.client.on('peer-join', this.handlePeerJoin.bind(this));
      this.client.on('peer-leave', this.handlePeerLeave.bind(this));
      this.client.on('stream-added', this.handleStreamAdded.bind(this));
      this.client.on('stream-subscribed', this.handleStreamSubscribed.bind(this));
      this.client.on('stream-removed', this.handleStreamRemoved.bind(this));
      this.client.on('stream-updated', this.handleStreamUpdated.bind(this));
      this.client.on('connection-state-changed', this.handleConnection.bind(this));
      this.client.on('mute-video', this.handleMuteVideo.bind(this));
      this.client.on('mute-audio', this.handleMuteAudio.bind(this));
      this.client.on('unmute-video', this.handleUnmuteVideo.bind(this));
      this.client.on('unmute-audio', this.handleUnmuteAudio.bind(this));
      this.client.on('unmute-audio', this.handleUnmuteAudio.bind(this));
      this.client.on('audio-volume', this.handleAudioVolume.bind(this));
    },
    handleAudioVolume(event) {
      event.result.forEach((volume) => {
        const volumeHeight = 24 - volume.audioVolume;
        this.$set(this.volume, volume.userId, volumeHeight >= 0 ? volumeHeight : 0);
      });
    },
    handleMuteVideo(event) {
      console.log(`[${event.userId}] mute video`);
      this.$set(this.videoMutedState, event.userId, true);
    },
    handleMuteAudio(event) {
      console.log(`[${event.userId}] mute audio`);
      this.$set(this.audioMutedState, event.userId, true);
    },
    handleUnmuteVideo(event) {
      console.log(`[${event.userId}] unmute video`);
      this.$set(this.videoMutedState, event.userId, false);
    },
    handleUnmuteAudio(event) {
      console.log(`[${event.userId}] unmute audio`);
      this.$set(this.audioMutedState, event.userId, false);
    },
    handleError(error) {
      console.error('client error', error);
      alert(error);
    },
    handleBanned(error) {
      console.error(`client has been banned for ${error}`);
      alert('You have been banned from the room!');
    },
    handlePeerJoin(event) {
      const { userId } = event;
      if (userId !== 'local-screen') {
        console.log(`Peer Client [${userId}] joined`);
      }
    },
    handlePeerLeave(event) {
      const { userId } = event;
      if (userId !== 'local-screen') {
        console.log(`[${userId}] leave`);
      }
    },
    handleStreamAdded(event) {
      const remoteStream = event.stream;
      const id = remoteStream.getId();
      const userId = remoteStream.getUserId();
      if (remoteStream.getUserId() === this.shareUserId) {
        this.client.unsubscribe(remoteStream).catch((error) => {
          console.error('unsubscribe failed', error);
        });
      } else {
        if (this.remoteStreams.length < 3) {
          this.remoteStreams.push(remoteStream);
          console.log(`remote stream added: [${userId}] ID: ${id} type: ${remoteStream.getType()}`);
          this.client.subscribe(remoteStream)
            .catch((error) => {
              console.error('subscribe failed', error);
              this.remoteStreams = this.remoteStreams.filter(stream => stream.getId() !== id);
            });
        }
      }
    },
    handleStreamSubscribed(event) {
      const remoteStream = event.stream;
      const id = remoteStream.getId();
      const userId = remoteStream.getUserId();

      remoteStream.play(id)
        .then(() => {
          console.log(`play remote stream success: [${userId}], type: ${remoteStream.getType()}`);
        })
        .catch((error) => {
          console.error('play remote stream failed', error);
        });
      remoteStream.on('player-state-changed', () => {
        // TODO: handle remote stream player state changed
      });
      console.log('stream-subscribed ID: ', id);
    },
    handleStreamRemoved(event) {
      const remoteStream = event.stream;
      const id = remoteStream.getId();
      const userId = remoteStream.getUserId();
      remoteStream.stop();
      if (remoteStream.getUserId() !== `share_${this.userId}`) {
        console.log(`RemoteStream removed: [${userId}]`);
      }
      this.remoteStreams = this.remoteStreams.filter(stream => stream.getId() !== id);
      const dom = document.getElementById(`container-${id}`);
      dom && dom.remove();
      console.log(`stream-removed ID: ${id}  type: ${remoteStream.getType()}`);
    },
    handleStreamUpdated(event) {
      const remoteStream = event.stream;
      const userId = remoteStream.getUserId();
      console.log(`RemoteStream updated: [${userId}] audio:${remoteStream.hasAudio()} video:${remoteStream.hasVideo()}`);
    },
    handleConnection(event) {
      console.log(`connection state changed: ${event.state}`);
    },
    async handleStopShare() {
      try {
        await this.shareClient.unpublish();
        await this.shareClient.leave();
        this.isShared = false;
        this.reportSuccessEvent('stopScreenShare');
      } catch (error) {
        this.reportFailedEvent('stopScreenShare', error);
      }
    },
    async handleShareScreen() {
      if (this.isShared) {
        return;
      }
      const res = await axios.post('https://intl-demo.trtc.tencent-cloud.com/CreateWebRtcUserSig', { Sig: this.sig });
      const { Data, Success } = res.data;
      if (!Success) {
        this.$message.error(this.$t('shareFailed'));
        return;
      }
      const { SdkAppid, RoomId, UserId, UserSig } = Data;
      this.shareUserId = UserId;
      const shareClient = new ShareClient({
        sdkAppId: parseInt(SdkAppid, 10),
        userId: UserId,
        roomId: parseInt(RoomId, 10),
        userSig: UserSig,
      });
      this.shareClient = shareClient;
      try {
        this.isShared = true;
        await shareClient.join();
        const shareStream = await shareClient.initLocalStream();
        shareStream.on('screen-sharing-stopped', async () => {
          await this.shareClient.leave();
          this.isShared = false;
        });
        shareStream.play('share-player');
        await shareClient.publish();
        this.reportSuccessEvent('startScreenShare');
      } catch (error) {
        this.isShared = false;
        this.reportFailedEvent('startScreenShare', error);
        console.log('startShare error', error);
      }
    },
    reportSuccessEvent(name) {
      this.$aegis.reportEvent({
        name,
        ext1: `${name}-success`,
        ext2: 'webrtcExpress',
        ext3: this.sdkAppId,
      });
    },
    reportFailedEvent(name, error, type = 'rtc') {
      this.$aegis.reportEvent({
        name,
        ext1: `${name}-failed#${this.roomId}*${type === 'share' ? this.shareUserId : this.userId}*${error?.message_}`,
        ext2: 'webrtcExpress',
        ext3: this.sdkAppId,
      });
    },
    handleInvite() {
      const link = `${window.location.host}${window.location.pathname}?sig=${this.sig}`;
      this.$copyText(link).then(() => {
        this.$message.success(this.$t('copySuccess'));
      }, () => {
        this.$message.error(this.$t('copyFailed'));
      });
    },
    async updateDevice() {
      this.cameraList = await TRTC.getCameras();
      this.microphoneList = await TRTC.getMicrophones();
    },
    muteAudio() {
      this.localStream.muteAudio();
      this.audioMuted = true;
    },
    unmuteAudio() {
      this.localStream.unmuteAudio();
      this.audioMuted = false;
    },
    muteVideo() {
      this.localStream.muteVideo();
      this.videoMuted = true;
    },
    unmuteVideo() {
      this.localStream.unmuteVideo();
      this.videoMuted = false;
    },
  },
};
</script>

<style lang="stylus">
.after-join-wrapper
  width 100%
  padding-bottom 10px
  display flex
  flex-direction column

  .share-link
    font-size 12px
    color rgba(0, 0, 0, 0.8)

  .input-wrapper
    display flex
    padding-bottom 10px
  @media (min-width: 960px)
    .user-wrapper
      margin-bottom 20px
      position: relative;
      width 660px
      height 380px

    .share-container
      position: absolute;
      z-index 0
      width 660px
      height 380px
      overflow hidden

      .share-player
        width 660px
        height 380px

    .user-container
      z-index 10
      width 660px
      height 380px
      background url("../assets/bg.png") no-repeat
      background-size 100% 100%
      display flex
      flex-direction row
      flex-wrap wrap
      justify-content space-between

      .stream-container
        width 320px
        height 180px
        overflow hidden
        background-color #F2F2F2
        background url("../assets/bg-user.png") no-repeat
        background-size 100% 100%

        .local-player
          width 320px
          height 180px

        .share-player
          width 680px
          height 380px

      .stream-container:first-child
        margin-bottom 20px

    .user-container-share
      overflow hidden
      z-index 10
      flex-wrap wrap
      background-color rgba(0, 0, 0, 0.6)
      justify-content space-between
      position: absolute;
      padding 10px
      display flex
      flex-direction column
      right 0

      .stream-container
        width 146px
        height 82px
        margin-bottom 10px
        overflow hidden
        background-color #F2F2F2

      .stream-container:last-child
        margin-bottom 0

        .local-player
          width 146px
          height 82px


  @media (max-width: 960px)
    .user-wrapper
      margin-bottom 20px
      position: relative;
      width 320px
      min-height 180px
      background-color #F2F2F2

    .user-container
      z-index 10
      width 320px
      min-height 180px
      background-size 100% 100%
      display flex
      flex-direction column

      .stream-container
        width 320px
        height 180px
        overflow hidden

        .local-player
          width 320px
          height 180px
          overflow hidden
          position relative


.user-control
  height 44px
  display flex
  justify-content space-between
  align-items center

  .icon-group
    display flex
    flex-direction row

    .icon-box
      display flex
      flex-direction row
      justify-content center
      align-items center
      margin-right 8px
      cursor pointer
      user-select none

      .icon-item
        width 54px
        height 44px
        display flex
        flex-direction column
        justify-content center
        align-items center

        .icon
          display flex
          justify-content center
          width 24px
          height 24px

        .tag
          width 100%
          font-size 10px
          color #888888
          letter-spacing 0
          text-align center
          line-height 14px
          font-weight 400

  .leave-btn
    color rgba(0, 0, 0, 0.8)
    border 1px solid #C1C6C8
    background-color white

#local-player
  position relative

.volume
  width 24px
  height 24px
  display flex
  justify-content center
  align-items center
  background rgba(0, 0, 0, 0.8)
  position absolute
  z-index 9999
  right 5px
  bottom 10px

.stream-container
  position relative

  .overlay
    background url("../assets/bg-user.png") no-repeat
    background-size 100% 100%
    width 100%
    height 100%
    position absolute
    z-index 999

.monitor-tip
  padding-top 20px
  color #A1A1A1
  font-size 14px
</style>
