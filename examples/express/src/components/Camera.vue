<template>
  <div class='icon-box'>
    <div class='icon-item' @click='handleMuteVideo'>
      <div class='icon'>
        <img v-if="!muted" width='24px' src='../assets/icons/video.svg'/>
        <img v-else width='24px' src='../assets/icons/video-muted.svg'/>
      </div>
      <div class='tag'>Camera</div>
    </div>
    <el-dropdown trigger='click' @command='handleCommand'>
      <div class='triangle-up'>
        <img height='16px' src='../assets/icons/triangle-up.png' width='20px'/>
      </div>
      <el-dropdown-menu slot='dropdown'>
        <template v-for='item in cameraList'>
          <el-dropdown-item :key='item.deviceId' :command='item'>{{ item.label }}</el-dropdown-item>
        </template>
      </el-dropdown-menu>
    </el-dropdown>
  </div>

</template>

<script>
import TRTC from 'trtc-js-sdk';
import { EventBus } from '@/event-bus';

export default {
  name: 'camera',
  props: ['muted', 'muteVideo', 'unmuteVideo'],
  data() {
    return {
      default: 'default',
      cameraList: [],
    };
  },
  created() {
    this.updateDevice();
    document.addEventListener('devicechange', () => {
      this.updateDevice();
    });
  },
  methods: {
    handleMuteVideo() {
      if (this.muted) {
        this.unmuteVideo();
      } else {
        this.muteVideo();
      }
    },
    async updateDevice() {
      this.cameraList = await TRTC.getCameras();
    },
    handleCommand(command) {
      const { deviceId } = command;
      this.default = deviceId;
      EventBus.$emit('switch-device', { videoId: deviceId });
    },
  },
};

</script>

<style lang='stylus' scoped>

</style>
