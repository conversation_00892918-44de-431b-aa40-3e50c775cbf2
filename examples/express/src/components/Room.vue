<template>
  <div class='room-wrapper'>
    <div class="permit-tip" v-if="permitFailed">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="rgb(213, 73, 65)" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8ZM8.00007 14C4.69107 14 2.00007 11.309 2.00007 8C2.00007 4.691 4.69107 2 8.00007 2C11.3091 2 14.0001 4.691 14.0001 8C14.0001 11.309 11.3091 14 8.00007 14ZM7 6V4H9V6H7ZM7 12V7H9V12H7Z" fill="rgb(213, 73, 65)"/>
      </svg>
      &nbsp;&nbsp;{{ $t('permitting') }}
    </div>
    <Welcome :isJoined="isJoinClicked" :sdkAppId="this.sdkAppId"/>
    <BeforeJoin v-if="!isJoinClicked" :handleDeviceFailed="handleDeviceFailed"/>
    <Join v-if="isJoinClicked" :options="options" :sig="sig"/>
    <div v-if="!isJoinClicked" class="join">
      <button :disabled="joinDisabled" class="rtc-btn join-btn" @click="joinRoom">{{ $t('join') }}</button>
    </div>
  </div>
</template>

<script>
import Welcome from '@/components/ui/Welcome';
import BeforeJoin from '@/components/BeforeJoin';
import Join from '@/components/Join';
import { getUrlParam } from '@/utils/utils';
import axios from 'axios';
import { EventBus } from '@/event-bus';

export default {
  name: 'Room',
  components: {
    Welcome,
    BeforeJoin,
    Join,
  },
  data() {
    return {
      sig: '',
      sdkAppId: 0,
      userSig: '',
      roomId: '',
      userId: '',
      options: {},
      isJoinClicked: false,
      joinDisabled: true,
      permitFailed: false
    };
  },
  created() {
    EventBus.$on('permit-failed', () => {
      this.permitFailed = true;
    });
    this.sig = getUrlParam('sig');
    if (!this.sig) {
      this.$notify.error({
        title: this.$t('init'),
        message: this.$t('recheck'),
      });
      this.joinDisabled = false;
      return;
    }
    axios.post('https://intl-demo.trtc.tencent-cloud.com/CreateWebRtcUserSig', { Sig: this.sig })
      .then((res) => {
        const { Data, Success } = res.data;
        if (Success) {
          const { SdkAppid, RoomId, UserId, UserSig } = Data;
          this.userSig = UserSig;
          this.sdkAppId = parseInt(SdkAppid, 10);
          this.roomId = parseInt(RoomId, 10);
          this.userId = UserId;
          this.options = {
            sdkAppId: this.sdkAppId,
            userSig: this.userSig,
            roomId: this.roomId,
            userId: this.userId,
          };
          this.joinDisabled = false;
        } else {
          this.$notify.error({
            title: this.$t('sig'),
            message: this.$t('recheck'),
          });
        }
      })
      .catch(() => {
        this.$notify.error({
          title: this.$t('init'),
          message: this.$t('recheck'),
        });
        this.joinDisabled = true;
      });
  },
  methods: {
    joinRoom() {
      this.isJoinClicked = true;
    },
    handleDeviceFailed() {
      this.joinDisabled = true;
    },
  },
};
</script>

<style lang="stylus" scoped>
@media (max-width: 1040px)
  .room-wrapper
    width 380px
    padding 20px 30px
    background-color white

@media (min-width: 1040px)
  .room-wrapper
    padding 20px 30px
    background-color white
    width 720px

.join
  display flex
  justify-content center
  align-items center
  margin-bottom 40px

  .join-btn
    background-color #006EFF
    color white
    border none
    width 320px
    height 38px

  .join-btn:disabled
    background-color #a0cfff
    cursor: not-allowed;

.permit-tip
  height 30px
  font-size 14px
  display flex
  align-items center
  background-color #f7e1e0
  border-radius 4px
  padding 0 6px

</style>
