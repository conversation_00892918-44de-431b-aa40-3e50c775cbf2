import Vue from 'vue';
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Loading,
  Message,
  Notification,
  Popover,
  Select,
  Input,
  Card,
} from 'element-ui';
import App from './App.vue';
import store from './store';
import VueClipboard from 'vue-clipboard2';
import Aegis from '@/utils/aegis';
import i18n from './locales/i18n';

Vue.use(VueClipboard);
Vue.use(Button);
Vue.use(Select);
Vue.use(Popover);
Vue.use(Dropdown);
Vue.use(DropdownItem);
Vue.use(DropdownMenu);
Vue.use(Input);
Vue.use(Card);
Vue.use(Loading.directive);
Vue.prototype.$loading = Loading.service;
Vue.prototype.$message = Message;
Vue.prototype.$notify = Notification;
Vue.prototype.$aegis = Aegis;
Vue.config.productionTip = false;

new Vue({
  store,
  i18n,
  render: h => h(App),
}).$mount('#app');
