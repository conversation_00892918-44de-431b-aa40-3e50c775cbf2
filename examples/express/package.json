{"name": "express", "version": "0.1.0", "private": true, "scripts": {"start": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"aegis-web-sdk": "^1.35.23", "axios": "^0.26.1", "core-js": "^3.8.3", "trtc-js-sdk": "latest", "vue": "^2.7.9", "vue-i18n": "^8.26.3", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/eslint-parser": "^7.14.6", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-airbnb": "^6.0.0", "babel-plugin-component": "^1.1.1", "element-ui": "^2.15.6", "eslint": "^8.21.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^8.0.3", "stylus": "^0.55.0", "stylus-loader": "^7.0.0", "vue-clipboard2": "^0.3.3", "vue-template-compiler": "2.7.9"}}