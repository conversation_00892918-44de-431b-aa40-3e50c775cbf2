# CHANGELOG

## `[1.0.0]` - 2025-07-03
### 初始版本发布
本版本包含vue demo的TRTC核心功能及多实例+宏指令，相对于 `[v0.0.0]` 版本修复多处bug、优化多处UI的交互逻辑，标志着该项目进入稳定可用阶段。

#### 新增功能
1. **js demo功能迁移**
   - 显示跨房视频显示roomId
   - 实现房间切换功能
   - 实现SEI消息传输
   - 新增变声器功能
   - 新增视频旋转功能
   - 支持js demo中11个url参数

2. **屏幕共享全屏支持**
   - 实现全屏模式显示

3. **多实例支持**
   - 支持新增/删除TRTC实例

4. **宏指令系统**
   - 完整的宏指令录制和操作流程
   - 实现宏指令: 复制/上传/删除
   - 支持操作时间线显示
   - 支持显示当前宏指令、且单击立即执行
   - 支持操作撤销功能和双击删除功能
   - 支持操作间延迟时间记录
   - 支持通过修改编辑当前宏指令的action
   - 支持多处hover查看功能提示

5. **宏组合系统**
   - 支持多实例一键录制
   - 支持多实例一键播放
   - 支持组合宏指令生成宏组合
   - 实现宏组合: 复制/上传/删除/仅删除组合

#### 问题修复
**核心功能修复**
   - 解决退房后再进房视频未推流问题 (2025/06/10 众测发现)
   - 解决 watch 导致的多余副作用，例如 `startLocalVideo` 时会先 `stopLocalVideo` 和 `stopLocalAudio`
   - 修复新增实例但是本地媒体设备没更新的bug（由原先代码结构导致）

## `[1.1.0]` - 2025-07-14
### 功能补充版本发布
本版本包含一些bug的修复，功能的补充

#### 新增功能
1. **设备检测插件**
   - 新增“设备检测”选项卡，包含跳过检测复选框和两个检测按钮
   
2. **strRoomId支持**
   - 支持输入strRoomId进入房间

#### 问题修复
**核心功能修复**
   - 修复跨房连麦sendSEI问题，功能不成熟，暂时关闭
   - 修复 Vue3 示例中的小流功能相关代码
   - 优化每次进房都要genTestUserSig的情况

## `[1.2.0]` - 2025-07-18
### 功能补充版本发布
本版本包含一些功能的优化、调整

#### 功能优化
1. **toast位置调整**
   - toast改为右下角弹出，避免遮挡
   
2. **底部音频调整**
   - 去除底部音频百分比音量，消除误导性

3. **kick out清理状态**
   - 添加kick out清理状态，但是多实例状态下偶现bug暂时无法解决

## `[1.3.0]` - 2025-07-21
### 功能补充版本发布
本版本包含一些功能的优化、调整，bug的修复

#### 功能优化
1. **代码结构调整**
   - main.ts中操作实例相关代码抽离为公共方法
   
2. **合图新版宏指令支持**
   - 宏指令支持重构后的合图插件

#### 问题修复
**核心功能修复**
   - 修复远端镜像失效问题
 
## `[1.4.0]` - 2025-07-21
### 功能补充版本发布
本版本包含一些功能的优化、调整，bug的修复
视频音频选项的，调用update
#### 功能优化
1. **响应式补充**
   - 视频、音频选项进行响应式补充，启用后的操作调用update
   
2. **移动端适配**
   - 对底部bar、宏指令以及所有modal进行移动端适配

#### 问题修复
**核心功能修复**
   - 修复偶现sdkappid undefined报错
 
