import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => {
      console.log('[Router] Trying to load Home component');
      return import('../views/Home.vue').catch(error => {
        console.error('[Router] Failed to load Home component:', error);
        throw error;
      });
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => {
      console.log('[Router] Route not found, loading Home component');
      return import('../views/Home.vue');
    }
  }
];

// 获取当前应用的基础路径
const getBasePath = () => {
  const scriptPath = document.currentScript instanceof HTMLScriptElement 
    ? document.currentScript.src 
    : '';
  const baseUrl = scriptPath ? new URL('./', scriptPath).pathname : '/';
  console.log('[Router] Detected base path:', baseUrl);
  return baseUrl;
};

const router = createRouter({
  history: createWebHistory(getBasePath()),
  routes
});

router.beforeEach((to, from, next) => {
  console.log('[Router] Navigation to:', to.fullPath, 'from:', from.fullPath);
  next();
});

router.onError((error) => {
  console.error('[Router] Error:', error);
});

export default router; 