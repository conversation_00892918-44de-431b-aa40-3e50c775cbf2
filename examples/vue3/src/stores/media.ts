import { defineStore } from 'pinia';
import { ref, watch, nextTick } from 'vue';
import { useRoomStore } from './room';
import TRTC, { audioProfileMap, type screenProfileMap, type videoProfileMap } from 'trtc-sdk-v5';
import { useMacroStore } from './macro';

const query = new URLSearchParams(location.search);
const _aiNoise = Boolean(query.get('AIdenoiser')) || false
const _audioProfile = (query.get('audioProfile') || 'standard') as keyof typeof audioProfileMap

export const useMediaStore = defineStore('media', () => {
  const roomStore = useRoomStore();
  const macroStore = useMacroStore()
  // 视频状态
  const videoPreview = ref(false);
  const selectedCamera = ref<string | undefined>(undefined);
  const videoPublished = ref(true);
  const showLocalPreview = ref(false);
  const showMixVideoPreview = ref(false)
  const videoProfile = ref<keyof typeof videoProfileMap>('480p');
  const voiceType = ref('0');
  const cameraOptions = ref<{ label: string; value: string; }[]>([{
    label: 'mp4 文件',
    value: 'music_A'
  }]);
  const videoProfileOptions = ref([
    { label: '120P', value: '120p' },
    { label: '180P', value: '180p' },
    { label: '240P', value: '240p' },
    { label: '360P', value: '360p' },
    { label: '480P', value: '480p' },
    { label: '720P', value: '720p' },
    { label: '1080P', value: '1080p' },
    { label: '2K', value: '1440p' },
    { label: '4K', value: '4K' }
  ]);
  const currentInstanceId = ref('')

  // 高级视频配置
  const advancedVideoConfig = ref({
    smallStream: false,
    vp8Encode: false,
    enableHWEncode: false,
    mirror: "false",
    get localMirror() {
      return this.mirror === 'true' ? true : this.mirror === 'false' ? false : this.mirror;
    }
  });

  watch(() => advancedVideoConfig.value.mirror, newVal => {
    if (macroStore.isExecute) return
    let mirror: any = newVal;
    if (newVal === 'true') mirror = true;
    if (newVal === 'false') mirror = false;
    roomStore.updateLocalVideo({
      option: { mirror }
    })
  })
  const remoteMirror = ref(false)
  // 更新设备列表
  const updateDeviceLists = async () => {
    cameraOptions.value = [{
      label: 'mp4 文件',
      value: 'music_A'
    }];
    microphoneOptions.value = [{
      label: 'mp4 文件',
      value: 'music_A'
    }];
    try {
      const cameras = await TRTC.getCameraList();
      const microphones = await TRTC.getMicrophoneList();

      cameraOptions.value.unshift(...cameras.map(device => ({
        label: device.label || `Camera ${device.deviceId.slice(0, 8)}...`,
        value: device.deviceId
      })));

      microphoneOptions.value.unshift(...microphones.map(device => ({
        label: device.label || `Microphone ${device.deviceId.slice(0, 8)}...`,
        value: device.deviceId
      })));

      // 如果没有选中的设备，默认选择第一个
      if (!selectedCamera.value && cameraOptions.value.length > 0) {
        selectedCamera.value = cameraOptions.value[0].value;
      }
      if (!selectedMicrophone.value && microphoneOptions.value.length > 0) {
        selectedMicrophone.value = microphoneOptions.value[0].value;
      }
    } catch (error) {
      console.error('Failed to get device list:', error);
    } finally {
      console.log('Device list updated:', {
        cameras: cameraOptions.value,
        microphones: microphoneOptions.value,
        selectedCamera: selectedCamera.value,
        selectedMicrophone: selectedMicrophone.value
      });
    }
  };

  // 监听摄像头选择变化
  watch(selectedCamera, async (newDeviceId) => {
    if (macroStore.isExecute) return
    console.log('Camera selection changed:', {
      newDeviceId,
      videoPreview: videoPreview.value,
      isEnabled: videoPreview.value && newDeviceId
    });

    if (videoPreview.value && newDeviceId) {
      console.log('Attempting to update camera with config:', {
        option: {
          cameraId: newDeviceId,
          profile: videoProfile.value
        }
      });

      try {
        await roomStore.updateLocalVideo({
          option: {
            cameraId: newDeviceId,
            profile: videoProfile.value as '480p' | '720p' | '1080p'
          }
        });
        console.log('Camera update successful');
      } catch (error) {
        console.error('Failed to update camera:', error);
      }
    }
  }, { immediate: true });  // 添加 immediate: true 以确保初始值也会触发监听器

  // 音频状态
  const audioEnabled = ref(false);
  const selectedMicrophone = ref<string | undefined>(undefined);
  const audioPublished = ref(true);
  const microphoneOptions = ref<{ label: string; value: string; }[]>([{
    label: 'mp4 文件',
    value: 'music_A'
  }]);
  const audioProfile = ref<keyof typeof audioProfileMap>(_audioProfile);
  const audioProfileOptions = ref([
    { label: '单声道 40kbps', value: 'standard' },
    { label: '双声道 64kbps', value: 'standard-stereo' },
    { label: '单声道 192kbps', value: 'high' },
    { label: '双声道 192kbps', value: 'high-stereo' }
  ]);
  const captureVolume = ref(100);
  const monitorVolume = ref(50);

  // 监听麦克风选择变化
  watch(selectedMicrophone, async (newDeviceId) => {
    if (macroStore.isExecute) return
    console.log('Microphone selection changed:', {
      newDeviceId,
      audioEnabled: audioEnabled.value,
      isEnabled: audioEnabled.value && newDeviceId
    });

    if (audioEnabled.value && newDeviceId) {
      console.log('Attempting to update microphone with config:', {
        option: {
          microphoneId: newDeviceId
        }
      });

      try {
        await roomStore.updateLocalAudio({
          option: {
            microphoneId: newDeviceId
          }
        });
        console.log('Microphone update successful');
      } catch (error) {
        console.error('Failed to update microphone:', error);
      }
    }
  }, { immediate: true });  // 添加 immediate: true 以确保初始值也会触发监听器

  // 高级音频功能
  const aec = ref(true);      // 默认开启回声消除
  const ans = ref(true);      // 默认开启噪声抑制
  const agc = ref(true);      // 默认开启自动增益
  const aiNoise = ref(_aiNoise); // 默认关闭 AI 降噪
  const background = ref(false); // 默认关闭背景音

  // 屏幕分享状态
  const screenSharing = ref(false);
  const screenPublished = ref(true);
  const shareSystemAudio = ref(false);
  const muteSystemAudio = ref(false);
  const qosPreference = ref<'smooth' | 'clear'>('smooth');
  const qosPreferenceOptions = ref([
    { label: '流畅度优先', value: 'smooth' },
    { label: '清晰度优先', value: 'clear' }
  ] as const);
  const screenProfile = ref<keyof typeof screenProfileMap>('1080p');
  const screenProfileOptions = ref([
    { label: '480p (5fps)', value: '480p' },
    { label: '480p (30fps)', value: '480p_2' },
    { label: '720p (5fps)', value: '720p' },
    { label: '720p (30fps)', value: '720p_2' },
    { label: '1080p (5fps)', value: '1080p' },
    { label: '1080p (30fps)', value: '1080p_2' }
  ]);

  // 监听屏幕分享配置变化
  watch([screenProfile, shareSystemAudio, qosPreference, muteSystemAudio], async ([newProfile, newSystemAudio, newQosPreference, newMuteSystemAudio], [oldProfile, oldSystemAudio, oldQosPreference, oldMuteSystemAudio]) => {
    if (macroStore.isExecute) return
    console.log('Screen sharing config changed:', {
      profile: { old: oldProfile, new: newProfile },
      systemAudio: { old: oldSystemAudio, new: newSystemAudio },
      qosPreference: { old: oldQosPreference, new: newQosPreference }
    });

    // Only update if screen sharing is active
    if (screenSharing.value) {
      try {
        await roomStore.updateScreenShare({
          publish: screenPublished.value,
          muteSystemAudio: newMuteSystemAudio,
          option: {
            profile: newProfile,
            systemAudio: newSystemAudio,
            qosPreference: newQosPreference
          }
          // We would need to update these options if the SDK supports it
          // Currently, we can only update publish state
        });
      } catch (error) {
        console.error('Failed to update screen sharing config:', error);
      }
    }
  });

  watch(aiNoise, async (newVal) => {
    if (macroStore.isExecute) return
    if (newVal) roomStore.startPlugin('AIDenoiser', await roomStore.genTestUserSig());
    else roomStore.stopPlugin('AIDenoiser', {});
  });
  // 监听 aec 的变化
  watch(aec, (newAec) => {
    if (macroStore.isExecute) return
    roomStore.updateLocalAudio({
      option: {
        echoCancellation: newAec,
      }
    }).catch();
  });

  // 监听 ans 的变化
  watch(ans, (newAns) => {
    if (macroStore.isExecute) return
    roomStore.updateLocalAudio({
      option: {
        noiseSuppression: newAns,
      }
    }).catch();
  });

  // 监听 agc 的变化
  watch(agc, (newAgc) => {
    if (macroStore.isExecute) return
    roomStore.updateLocalAudio({
      option: {
        autoGainControl: newAgc,
      }
    }).catch();
  });
  watch(monitorVolume, (newVal) => {
    if (macroStore.isExecute) return
    roomStore.updateLocalAudio({
      option: {
        earMonitorVolume: newVal,
      }
    }).catch();
  });
  watch(captureVolume, (newVal) => {
    if (macroStore.isExecute) return
    roomStore.updateLocalAudio({
      option: {
        captureVolume: newVal,
      }
    }).catch();
  });
  // 视频方法
  const toggleVideoPreview = async () => {
    videoPreview.value = !videoPreview.value;
    if (videoPreview.value) {
      await nextTick();
      roomStore.startLocalVideo(`${currentInstanceId.value}-local-video`);
    }
    if (!videoPreview.value) {
      roomStore.stopLocalVideo();
    }
  };

  const toggleVideoPublish = () => {
    videoPublished.value = !videoPublished.value;
    if (videoPreview.value) {
      roomStore.updateLocalVideo({
        publish: videoPublished.value
      });
    }
  };

  // 音频方法
  const toggleAudio = () => {
    audioEnabled.value = !audioEnabled.value;
    if (audioEnabled.value) {
      roomStore.startLocalAudio();
    }
    if (!audioEnabled.value) {
      roomStore.stopLocalAudio();
    }
  };

  const toggleAudioPublish = () => {
    audioPublished.value = !audioPublished.value;
    if (audioEnabled.value) {
      roomStore.updateLocalAudio({
        publish: audioPublished.value
      });
    }
  };

  const toggleBackground = () => {
    background.value = !background.value;
    if (background.value) {
      roomStore.startPlugin('AudioMixer', {
        id: 'background',
        url: 'newRing.wav',
        loop: true,
      });
    } else {
      roomStore.stopPlugin('AudioMixer', {
        id: 'background',
      });
    }
  };

  // 屏幕分享方法
  const toggleScreenSharing = async () => {
    screenSharing.value = !screenSharing.value;
    if (screenSharing.value) {
      try {
        await nextTick();
        await roomStore.startScreenShare(`${currentInstanceId.value}-local-share-preview`);
        return ['Success', {
          title: "屏幕分享",
          content: "屏幕分享已开启",
          duration: 3000,
        }];
      } catch (error) {
        screenSharing.value = false;
        return ['Error', {
          title: "屏幕分享失败",
          content: (error as Error).message,
          duration: 3000,
        }]
      }
    } else if (!screenSharing.value) {
      try {
        await roomStore.stopScreenShare();
        return ['Success', {
          title: "屏幕分享",
          content: "屏幕分享已停止",
          duration: 3000,
        }];
      } catch (error) {
        return ['Error', {
          title: "停止屏幕分享失败",
          content: (error as Error).message,
          duration: 3000,
        }];
      }
    }
  };

  const toggleScreenPublish = async () => {
    screenPublished.value = !screenPublished.value;
    if (screenSharing.value) {
      roomStore.updateScreenShare({
        publish: screenPublished.value
      });
    }
  };

  const setMixPreview = (showPreview: boolean) => {
    showMixVideoPreview.value = showPreview
  }

  function $reset() {
    // 视频状态
    videoPreview.value = false;
    videoPublished.value = true;
    showLocalPreview.value = false;
    remoteMirror.value = false;
    videoProfile.value = '480p';

    // 高级视频配置
    advancedVideoConfig.value = {
      smallStream: false,
      vp8Encode: false,
      enableHWEncode: false,
      mirror: "false",
      get localMirror() {
        return this.mirror === 'true' ? true : this.mirror === 'false' ? false : this.mirror;
      }
    };

    // 音频状态
    audioEnabled.value = false;
    audioPublished.value = true;
    audioProfile.value = 'standard';
    captureVolume.value = 100;
    monitorVolume.value = 50;
    voiceType.value = '0';

    // 高级音频功能
    aec.value = true;
    ans.value = true;
    agc.value = true;
    aiNoise.value = false;
    background.value = false;

    // 屏幕分享状态
    screenSharing.value = false;
    screenPublished.value = true;
    shareSystemAudio.value = false;
    muteSystemAudio.value = false;
    qosPreference.value = 'smooth';
    screenProfile.value = '1080p';

    // 合流预览
    showMixVideoPreview.value = false;
  }

  return {
    // 视频状态
    videoPreview,
    selectedCamera,
    videoPublished,
    videoProfile,
    cameraOptions,
    videoProfileOptions,
    advancedVideoConfig,
    showLocalPreview,
    showMixVideoPreview,
    // 音频状态
    audioEnabled,
    selectedMicrophone,
    audioPublished,
    microphoneOptions,
    audioProfile,
    audioProfileOptions,
    captureVolume,
    monitorVolume,
    aec,
    ans,
    agc,
    aiNoise,
    background,
    remoteMirror,

    // 屏幕分享状态
    screenSharing,
    screenPublished,
    shareSystemAudio,
    muteSystemAudio,
    qosPreference,
    qosPreferenceOptions,
    screenProfile,
    screenProfileOptions,

    currentInstanceId,
    voiceType,

    // 方法
    toggleVideoPreview,
    toggleVideoPublish,
    toggleAudio,
    toggleAudioPublish,
    toggleBackground,
    toggleScreenSharing,
    toggleScreenPublish,
    updateDeviceLists,
    setMixPreview,
    $reset,
  };
}); 