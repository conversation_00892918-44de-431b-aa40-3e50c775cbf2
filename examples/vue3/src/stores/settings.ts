import { defineStore } from 'pinia';
import { ref, watch } from 'vue';
import { useRoomStore } from './room';

export const useSettingsStore = defineStore('settings', () => {
  const virtualBackground = ref('normal');
  const blurLevel = ref(5);
  const roomStore = useRoomStore();
  // 美颜参数 (0-9)
  const beautyValues = ref({
    beauty: 0.5,  // 美颜程度
    brightness: 0.5,  // 美白程度
    ruddy: 0.5     // 红润程度
  });

  const videoDecoderOptions = ref([
    { label: '自动选择', value: 'auto' },
    { label: '软解码', value: 'software' },
    { label: '硬解码', value: 'hardware' }
  ]);

  const videoRenderOptions = ref([
    { label: '自动选择', value: 'auto' },
    { label: 'WebGL', value: 'webgl' },
    { label: '软件渲染', value: 'software' }
  ]);

  // 代理配置
  const proxyConfig = ref({
    mode: 'none',
    server: '',
    username: '',
    password: ''
  });

  // 插件功能
  const pluginFeatures = ref({
    beauty: false,
    blur: false,
    image: false,
    watermark: false,
    advancedBeauty: false,
    enableFaceCentering: false,
    videoMix: false,
    voiceChanger: false,
    isAllowSkipDeviceCheck: true
  });

  // 跨房配置
  const crossRoomConfig = ref({
    roomId: '',
    userId: '',
    role: 'anchor',
    publishAudio: true,
    publishVideo: true,
    connected: false
  });

  const crossRoomRoleOptions = ref([
    { label: '主播', value: 'anchor' },
    { label: '观众', value: 'audience' }
  ]);

  const toggleCrossRoom = () => {
    crossRoomConfig.value.connected = !crossRoomConfig.value.connected;
  };

  watch(() => pluginFeatures.value.advancedBeauty, async (newVal) => {
    if (newVal) {
      roomStore.startPlugin('Beauty', {
        ...await roomStore.genTestUserSig(),

        whiten: 0.5, // Whitening 0-1
        dermabrasion: 0.5, // Dermabrasion 0-1
        lift: 0.5, // Face Slimming 0-1
        shave: 0.5, // Jaw Shaving 0-1
        eye: 0.5, // Enlarged Eyes 0-1
        chin: 0.5, // Chin Adjustment 0-1

        effect: [{
          id: '7A62218064EF7959', // the effect id
          intensity: 0.7, // specify the strength of the effect
        },
        {
          id: 'D7C6418064B90F4C', // the effect id
          intensity: 0.7, // specify the strength of the effect
        }]
      });
    } else {
      roomStore.stopPlugin('Beauty', {});
    }
  });

  function $reset() {
    virtualBackground.value = 'normal';
    blurLevel.value = 5;
    beautyValues.value = {
      beauty: 0.5,
      brightness: 0.5,
      ruddy: 0.5
    };

    // 重置插件功能
    pluginFeatures.value = {
      beauty: false,
      blur: false,
      image: false,
      watermark: false,
      advancedBeauty: false,
      enableFaceCentering: false,
      videoMix: false,
      voiceChanger: false
    };

    // 重置跨房配置
    crossRoomConfig.value = {
      roomId: '',
      userId: '',
      role: 'anchor',
      publishAudio: true,
      publishVideo: true,
      connected: false
    };
  }

  return {
    // 下行配置
    videoDecoderOptions,
    videoRenderOptions,
    virtualBackground,
    blurLevel,
    beautyValues,
    // 代理配置
    proxyConfig,

    // 插件功能
    pluginFeatures,

    // 跨房配置
    crossRoomConfig,
    crossRoomRoleOptions,
    toggleCrossRoom,

    $reset
  };
}); 