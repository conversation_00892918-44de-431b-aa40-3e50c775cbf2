import { defineStore } from 'pinia';
import { ref, watch } from 'vue';
import { useVersionStore } from './version';
import { useMediaStore } from './media';
import { useStatisticsStore } from './statistics';
import { useMacroStore } from './macro';
import type { NotificationApi } from 'naive-ui';
import TRTC, {
  type LocalVideoConfig,
  type LocalAudioConfig,
  type Scene,
  type UserRole,
  type PluginStartOptionsMap,
  type PluginUpdateOptionsMap,
  type PluginStopOptionsMap,
  type TRTCStreamType,
  type UpdateScreenShareConfig,
  type RemoteVideoConfig,
  type VideoProfile,
} from 'trtc-sdk-v5';
import { useProxyStore } from './proxy';
import { sm4 } from 'sm-crypto';
type TRTCClient = ReturnType<typeof TRTC.create>;

const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const query = new URLSearchParams(location.search);
const sdkAppId = Number(query.get('sdkAppId')) || 1400704311;
const secretKey = query.get('secretKey');
const latencyLevel = Number(query.get('latencyLevel')) || 0;
const enableAutoPlayDialog = query.get('enableAutoPlayDialog') !== 'false';
const _roomId = query.get('roomId') || `${new Date().getMinutes() * 10 + 66666}`;
const _enableSEI = Boolean(query.get('enableSEI')) || false
const url = new URL(window.location.href);
const _logLevel = Number(query.get("logLevel")) || 1
const _enableUploadLog = query.get("enableUploadLog") === 'false' ? false : true

export interface RemoteUser {
  hasVideo: boolean;
  hasAudio: boolean;
  hasScreenShare: boolean;
  decoder: string;
  renderer: string;
  roomId: string;
}
export const useRoomStore = defineStore('room', () => {
  const _userName = query.get('userId') || `u${Math.random() * 100000000 >> 0}`;
  async function genTestUserSig() {
    if (!secretKey) {
      let userSig;
      if (query.get('userSig')) {
        userSig = query.get('userSig')
      }
      else {
        const response = await fetch(fetchUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            pwd: '********',
            appid: sdkAppId,
            roomnum: parseInt(roomId.value),
            privMap: 255,
            identifier: userName.value,
            accounttype: 14418,
          })
        });
        const { data } = await response.json();
        userSig = data.userSig
      }
      return {
        sdkAppId,
        userId: userName.value,
        userSig: userSig
      };
    }
    const EXPIRETIME = 604800;
    // @ts-ignore
    const generator = new LibGenerateTestUserSig(sdkAppId, secretKey, EXPIRETIME);
    const userSig = generator.genTestUserSig(userName.value);
    return {
      sdkAppId,
      userId: userName.value,
      userSig
    };
  }
  const versionStore = useVersionStore();
  const mediaStore = useMediaStore();
  const statisticsStore = useStatisticsStore();
  const macroStore = useMacroStore();
  const proxyStore = useProxyStore();
  let notificationInstance: NotificationApi | null = null;

  let trtc: TRTCClient | null = null;
  const roomId = ref(_roomId);
  const userName = ref(_userName);
  const inRoom = ref(false);
  const isRTCMode = ref(true);
  const isAnchor = ref(true);
  const autoPlayAudio = ref(true);
  const autoPlayVideo = ref(true);
  const enableSEI = ref(_enableSEI);
  const videoEncodeCodec = ref(null);
  const userVolumes = ref<Record<string, number>>({});
  const localAudioMuted = ref(false);
  const localVideoMuted = ref(false);
  const remoteUsers = ref<Record<string, RemoteUser>>({});
  const useVp8 = ref(false);
  const rotationAngle = ref(0)
  const isStr = ref(false)
  const testUserSig = ref<[Record<string, any>, string]>([{}, userName.value]);
  const logLevel = ref(_logLevel)
  const enableUploadLog = ref(_enableUploadLog)
  
  watch(enableSEI, (newVal) => {
    if (newVal) {
      url.searchParams.set('enableSEI', newVal.toString());
    }
    else {
      url.searchParams.delete('enableSEI')
    }
    history.replaceState(null, '', url);
  })

  // Add a setter for the notification instance
  const setNotificationInstance = (instance: NotificationApi) => {
    notificationInstance = instance;
  };

  // Helper function to safely show notifications
  const showNotification = (options: {
    type: 'info' | 'success' | 'warning' | 'error',
    title: string,
    content: string,
    duration?: number;
  }) => {
    try {
      if (notificationInstance) {
        notificationInstance[options.type]({
          title: options.title,
          content: options.content,
          duration: options.duration || 3000
        });
      }
    } catch (error) {
      console.warn('Failed to show notification:', error);
    }
  };
  function downgradeVideoDecode(userId: string, streamType: TRTCStreamType) {
    const track = streamType === 'main' ? trtc?._room.remotePublishedUserMap.get(userId)?.remoteVideoTrack : trtc?._room.remotePublishedUserMap.get(userId)?.remoteAuxiliaryTrack;
    console.warn('updatePlugin downgrade streamType', streamType);
    trtc?.updatePlugin('TRTCVideoDecoder' as any, { type: 'mock', track }).catch(() => {
      track?.emit('decode-failed');
    });
  }
  async function createClient() {
    console.log('Creating TRTC client...');
    if (trtc) {
      await trtc.exitRoom().catch();
      trtc.destroy();
    }
    const version = versionStore.currentVersion;
    const assetsPath = () => {
      switch (version) {
        case 'dev':
          return '/static/';
        case 'build':
          return './assets/';
        case 'latest':
          return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/assets/';
        default:
          return `https://unpkg.com/trtc-sdk-v5@${version}/assets/`;
      }
    };
    TRTC.setLogLevel(_logLevel, _enableUploadLog)
    console.log(`TRTC.setLogLevel(${_logLevel}, ${_enableUploadLog})`)
    trtc = TRTC.create({
      iceTransportPolicy: Boolean(query.get('forceTurn')) ? 'relay' : 'all',
      plugins: Object.values(versionStore.pluginMap).filter(plugin => plugin.loaded && plugin.path !== 'trtc.js').map(plugin => plugin.clazz),
      assetsPath: assetsPath(),
      enableSEI: enableSEI.value
    });
    window.trtc = trtc;
    const TRTCEvent = TRTC.EVENT;
    // Create a proxy to intercept and record method calls
    const rtcProxy = new Proxy(trtc, {
      get(target, prop, receiver) {
        const value = Reflect.get(target, prop, receiver);
        if (typeof value === 'function' && !prop.toString().startsWith('_') && prop.toString() !== 'on') {
          return function (...args: any[]) {
            // Record the method call if macro recording is active
            if (macroStore.isRecording) {
              macroStore.recordAction(prop.toString(), args);
            }
            return value.apply(target, args);
          };
        }
        return value;
      }
    }) as TRTCClient;

    trtc = rtcProxy;

    // 检查并启动加密插件
    const savedEncryptionConfig = localStorage.getItem('encryptionConfig');
    if (savedEncryptionConfig) {
      const config = JSON.parse(savedEncryptionConfig);
      if (config.enabled) {
        const key = new TextEncoder().encode(config.key);
        const iv = new TextEncoder().encode(config.salt);
        (trtc.startPlugin as any)('CustomEncryption', config.algorithm === 'custom' ? {
          mode: 'udt3',
          video: true,
          audio: true,
          customCryptors: {
            encryptor: (data: Uint8Array) => {
              const result = sm4.encrypt(data, config.key, { mode: 'cbc', iv: config.key, output: 'array' });
              return new Uint8Array(result);
            },
            decryptor: (data: Uint8Array) => {
              const result = sm4.decrypt(data, config.key, { mode: 'cbc', iv: config.key, output: 'array' });
              return new Uint8Array(result);
            },
          },
        } : {
          mode: 'udt3',
          video: true,
          audio: true,
          builtinOptions: {
            algorithm: config.algorithm,
            secretKey: key,
            salt: iv,
          },
        }).catch((error: unknown) => {
          console.error('Failed to start encryption plugin on client creation:', error);
        });
      }
    }

    await mediaStore.updateDeviceLists()
    if (Object.keys(testUserSig.value[0]).length === 0) testUserSig.value[0] = await genTestUserSig();

    // Add statistics event listener
    trtc.on(TRTCEvent.STATISTICS, (stats) => {
      statisticsStore.updateStats(stats);
    });

    // Connection state changes
    trtc.on(TRTCEvent.CONNECTION_STATE_CHANGED, (event) => {
      showNotification({
        type: 'info',
        title: '连接状态变更',
        content: `连接状态: ${event.state}`,
        duration: 3000
      });
    });

    // Network quality
    trtc.on(TRTCEvent.NETWORK_QUALITY, (event) => {
      const qualityMap = {
        0: '未知',
        1: '极好',
        2: '好',
        3: '一般',
        4: '差',
        5: '很差',
        6: '断开'
      };

      if (event.uplinkNetworkQuality >= 4 || event.downlinkNetworkQuality >= 4) {
        showNotification({
          type: 'warning',
          title: '网络质量警告',
          content: `上行: ${qualityMap[event.uplinkNetworkQuality]}, 下行: ${qualityMap[event.downlinkNetworkQuality]}`,
          duration: 5000
        });
      }
    });

    // Error events
    trtc.on(TRTCEvent.ERROR, (error) => {
      showNotification({
        type: 'error',
        title: '发生错误',
        content: `错误码: ${error.code}, 信息: ${error.message}`,
        duration: 8000
      });
    });

    trtc.on(TRTC.EVENT.SEI_MESSAGE, event => {
      showNotification({
        type: 'success',
        title: '收到SEI',
        content: `收到 ${event.userId} 的 sei ${new TextDecoder().decode(event.data)}`,
        duration: 3000
      });

    })

    trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, event => {
      const message = new TextDecoder().decode(event.data)
      showNotification({
        type: 'success',
        title: '收到Custom message',
        content: `received custom msg from ${event.userId}, message: ${message}`,
        duration: 3000
      });
      if (message.startsWith('firstRoomId:')) {
        remoteUsers.value[event.userId].roomId = message.slice(12)
        const encoder = new TextEncoder();
        const buffer = encoder.encode(`lastRoomId:${roomId.value}`).buffer;
        sendCustomMessage({
          cmdId: 1,
          data: buffer
        });
      }
      if (message.startsWith('lastRoomId:')) {
        remoteUsers.value[event.userId].roomId = message.slice(11)
      }
    })

    trtc.on(TRTCEvent.REMOTE_USER_ENTER, (event) => {
      remoteUsers.value[event.userId] = {
        hasVideo: false,
        hasAudio: false,
        hasScreenShare: false,
        roomId: roomId.value
      } as any;
      showNotification({
        type: 'success',
        title: '用户加入',
        content: `用户 ${event.userId} 加入房间`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.REMOTE_USER_EXIT, (event) => {
      delete remoteUsers.value[event.userId];
      showNotification({
        type: 'info',
        title: '用户离开',
        content: `用户 ${event.userId} 离开房间`,
        duration: 3000
      });
    });

    // Device change
    trtc.on(TRTCEvent.DEVICE_CHANGED, (event) => {
      mediaStore.updateDeviceLists();
      let actionText = '';
      if (event.action === 'add') {
        actionText = '接入';
      } else if (event.action === 'remove') {
        actionText = '移除';
      } else if (event.action === 'active') {
        actionText = '激活';
      }

      const deviceType = {
        'audioinput': '麦克风',
        'audiooutput': '扬声器',
        'videoinput': '摄像头'
      }[event.device.kind] || '未知设备';

      const deviceInfo = event.device.label ?
        `${deviceType}「${event.device.label}」` :
        `${deviceType} (ID: ${event.device.deviceId})`;

      showNotification({
        type: 'info',
        title: `设备${actionText}`,
        content: `${deviceInfo}`,
        duration: 3000
      });
    });

    // Audio/Video state changes
    trtc.on(TRTCEvent.VIDEO_PLAY_STATE_CHANGED, (event) => {
      if (event.userId) { // Only show for remote users
        showNotification({
          type: 'info',
          title: '视频状态变更',
          content: `用户 ${event.userId} ${event.state === 'PLAYING' ? '开启' : '关闭'}视频`,
          duration: 3000
        });
      }
    });

    // Remote video availability events
    trtc.on(TRTCEvent.REMOTE_VIDEO_AVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          remoteUsers.value[event.userId].hasVideo = true;
        } else if (event.streamType === TRTC.TYPE.STREAM_TYPE_SUB) {
          remoteUsers.value[event.userId].hasScreenShare = true;
        }
      }
      showNotification({
        type: 'info',
        title: '远端视频可用',
        content: `用户 ${event.userId} 的${event.streamType === TRTC.TYPE.STREAM_TYPE_SUB ? '屏幕分享' : '视频'}可用`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.REMOTE_VIDEO_UNAVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          remoteUsers.value[event.userId].hasVideo = false;
        } else if (event.streamType === TRTC.TYPE.STREAM_TYPE_SUB) {
          remoteUsers.value[event.userId].hasScreenShare = false;
        }
      }
      showNotification({
        type: 'info',
        title: '远端视频不可用',
        content: `用户 ${event.userId} 的${event.streamType === TRTC.TYPE.STREAM_TYPE_SUB ? '屏幕分享' : '视频'}不可用`,
        duration: 3000
      });
    });

    // Remote audio availability events
    trtc.on(TRTCEvent.REMOTE_AUDIO_AVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        remoteUsers.value[event.userId].hasAudio = true;
      }
      showNotification({
        type: 'info',
        title: '远端音频可用',
        content: `用户 ${event.userId} 的音频可用`,
        duration: 3000
      });
    });

    trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
      if (remoteUsers.value[event.userId]) {
        remoteUsers.value[event.userId].hasAudio = false;
      }
      showNotification({
        type: 'info',
        title: '远端音频不可用',
        content: `用户 ${event.userId} 的音频不可用`,
        duration: 3000
      });
    });

    trtc.on(TRTCEvent.AUDIO_PLAY_STATE_CHANGED, (event) => {
      if (event.userId) { // Only show for remote users
        showNotification({
          type: 'info',
          title: '音频状态变更',
          content: `用户 ${event.userId} ${event.state === 'PLAYING' ? '开启' : '关闭'}音频`,
          duration: 3000
        });
      }
    });
    trtc.on(TRTCEvent.SCREEN_SHARE_STOPPED, () => {
      showNotification({
        type: 'info',
        title: '屏幕分享停止',
        content: `用户停止屏幕分享`,
        duration: 3000
      });
      mediaStore.screenSharing = false;
    });
    // Add audio volume event listener
    trtc.on(TRTCEvent.AUDIO_VOLUME, (event) => {
      // Update volumes from the result array
      event.result.forEach(volume => {
        userVolumes.value[volume.userId || userName.value] = volume.volume;
      });
    });
    trtc.enableAudioVolumeEvaluation(1000);
    // @ts-ignore
    trtc.on('video-decode-downgrade-state-changed', (event) => {
      if (!event) return
      remoteUsers.value[event.userId].decoder = event.type;
      remoteUsers.value[event.userId].renderer = event.renderer;
    });
    trtc.on(TRTC.EVENT.KICKED_OUT, (event) => {
      $reset()
      mediaStore.$reset();
      showNotification({
        type: 'info',
        title: '被踢出房间',
        content: `原因: ${event.reason}`,
        duration: 3000
      });
    });
    return trtc;
  }

  const useClient = async () => {
    if (!trtc) trtc = await createClient()
    return trtc
  }
  // 方法
  const joinRoom = async () => {
    if (!roomId.value || !userName.value) return;

    try {
      if (!trtc) await createClient();
      if (testUserSig.value[1] !== userName.value) {
        testUserSig.value[1] = userName.value;
        testUserSig.value[0] = await genTestUserSig();
      }
      const options = {
        ...testUserSig.value[0] as any,
        privateMapKey: '255',
        useVp8: mediaStore.advancedVideoConfig.vp8Encode,
        preferHW: mediaStore.advancedVideoConfig.enableHWEncode,
        proxy: proxyStore.getProxyConfig(),
        role: (isAnchor.value ? 'anchor' : 'audience') as UserRole,
        scene: (isRTCMode.value ? 'rtc' : 'live') as Scene,
        autoReceiveAudio: autoPlayAudio.value,
        autoReceiveVideo: autoPlayVideo.value,
        enableAutoPlayDialog: enableAutoPlayDialog ? Boolean(Number(enableAutoPlayDialog)) : true,
        latencyLevel: latencyLevel,
        enableSEI: enableSEI.value,
      }
      if (isStr.value) {
        await trtc!.enterRoom({
          strRoomId: roomId.value,
          ...options
        });
      }
      else if (!isStr.value) {
        await trtc!.enterRoom({
          roomId: Number(roomId.value),
          ...options
        });
      }
      inRoom.value = true;
    } catch (error) {
      showNotification({
        type: 'error',
        title: '进房失败',
        content: error instanceof Error ? error.message : 'Unknown error',
        duration: 8000
      });
      throw error;
    }
  };

  const leaveRoom = async () => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.exitRoom();
      inRoom.value = false;
    } catch (error) {
      console.error('Leave room failed:', error);
      throw error;
    }
  };

  const destroyRoom = async () => {
    if (!trtc) return;
    trtc.destroy();
    trtc = null;
    inRoom.value = false;
  };
  const publishMix = async (videoTrack: MediaStreamTrack, profile: '480p' | '720p' | '1080p' | VideoProfile) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.startLocalVideo({
        option: {
          videoTrack,
          profile
        }
      })
      console.log('publish mix')
    } catch (error) {
      console.error(error)
      throw error
    }
  }
  const startLocalVideo = async (element: HTMLElement | string) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');

      if (mediaStore.selectedCamera === 'music_A') {
        music_A.play();
        await trtc.startLocalVideo({
          view: element,
          publish: mediaStore.videoPublished,
          option: {
            videoTrack: streamGenerator.generateVideoTrack(),
            small: mediaStore.advancedVideoConfig.smallStream,
            mirror: mediaStore.advancedVideoConfig.localMirror,
            rotation: rotationAngle.value as any,
            // @ts-ignore 隐藏参数
            smallMode: query.get('smallMode') || undefined
          }
        });
      } else {
        await trtc.startLocalVideo({
          view: element,
          publish: mediaStore.videoPublished,
          option: {
            cameraId: mediaStore.selectedCamera,
            profile: mediaStore.videoProfile as '480p' | '720p' | '1080p',
            small: mediaStore.advancedVideoConfig.smallStream,
            mirror: mediaStore.advancedVideoConfig.localMirror,
            rotation: rotationAngle.value as any,
            // @ts-ignore 隐藏参数
            smallMode: query.get('smallMode') || undefined
          }
        });
      }
    } catch (error) {
      console.error('Start local video failed:', error);
      throw error;
    }
  };

  const startRemoteVideo = async (userId: string, element: HTMLElement, streamType: TRTCStreamType = TRTC.TYPE.STREAM_TYPE_MAIN) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.startRemoteVideo({
        userId,
        view: element,
        streamType,
        option: {
          mirror: mediaStore.remoteMirror,
        }
      });
    } catch (error) {
      console.error('Start remote video failed:', error);
      throw error;
    }
  };

  const stopRemoteVideo = async (userId: string, streamType: TRTCStreamType = TRTC.TYPE.STREAM_TYPE_MAIN) => {
    try {
      if (!trtc) throw new Error('TRTC not initialized');
      await trtc.stopRemoteVideo({ userId, streamType });
    } catch (error) {
      console.error('Stop remote video failed:', error);
      throw error;
    }
  };

  async function startLocalAudio() {
    try {
      if (!trtc) throw new Error('TRTC not initialized');

      if (mediaStore.selectedMicrophone === 'music_A') {
        music_A.play();
        await trtc.startLocalAudio({
          publish: mediaStore.audioPublished,
          option: {
            audioTrack: streamGenerator.generateAudioTrack(),
          }
        });
      } else {
        await trtc.startLocalAudio({
          publish: mediaStore.audioPublished,
          option: {
            microphoneId: mediaStore.selectedMicrophone,
            profile: mediaStore.audioProfile,
            echoCancellation: mediaStore.aec,
            noiseSuppression: mediaStore.ans,
            autoGainControl: mediaStore.agc
          }
        });
      }
    } catch (error) {
      console.error('Start local audio failed:', error);
      throw error;
    }
  }

  const updateRemoteVideo = async (config: RemoteVideoConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.updateRemoteVideo(config);
  }

  const updateLocalVideo = async (config: LocalVideoConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.updateLocalVideo(config);
  };

  const updateLocalAudio = async (config: LocalAudioConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.updateLocalAudio(config);
  };

  const stopLocalAudio = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.stopLocalAudio();
  };

  const stopLocalVideo = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    await trtc.stopLocalVideo();
  };

  const startScreenShare = async (element?: HTMLElement | string) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.startScreenShare({
        view: element || undefined,
        publish: true,
        option: {
          profile: mediaStore.screenProfile,
          qosPreference: mediaStore.qosPreference,
          systemAudio: mediaStore.shareSystemAudio
        }
      });
    } catch (error) {
      console.error('Start screen share failed:', error);
      throw error;
    }
  };

  const stopScreenShare = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.stopScreenShare();
    } catch (error) {
      console.error('Stop screen share failed:', error);
      throw error;
    }
  };

  const toggleLocalAudio = async (mute: boolean) => {
    if (!trtc) return;
    try {
      await trtc.updateLocalAudio({ mute });
      localAudioMuted.value = mute;
    } catch (error) {
      console.error('Failed to toggle local audio:', error);
    }
  };

  const toggleLocalVideo = async (mute: boolean) => {
    if (!trtc) return;
    try {
      await trtc.updateLocalVideo({ mute });
      localVideoMuted.value = mute;
    } catch (error) {
      console.error('Failed to toggle local video:', error);
    }
  };

  const updateScreenShare = async (config: UpdateScreenShareConfig) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.updateScreenShare(config);
    } catch (error) {
      console.error('Update screen share failed:', error);
      throw error;
    }
  };

  // Plugin management methods
  const startPlugin = async (pluginName: string, options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      return await trtc.startPlugin(pluginName as keyof PluginStartOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to start plugin ${pluginName}:`, error);
      throw error;
    }
  };

  const updatePlugin = async (pluginName: string, options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      return await trtc.updatePlugin(pluginName as keyof PluginUpdateOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to update plugin ${pluginName}:`, error);
      throw error;
    }
  };

  const stopPlugin = async (pluginName: string, options?: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await trtc.stopPlugin(pluginName as keyof PluginStopOptionsMap, options as any);
    } catch (error) {
      console.warn(`Failed to stop plugin ${pluginName}:`, error);
    }
  };

  const switchRoom = async (options: unknown) => {
    if (!trtc) throw new Error('TRTC not initialized');
    if (!inRoom.value) throw new Error('Please enter room before switch room');
    try {
      await trtc.switchRoom(options as any);
    } catch (error) {
      console.warn(`Failed to switch room:`, error);
    }
  }

  const switchRoomFallback = async () => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      await leaveRoom();
      await joinRoom();
    } catch (error) {
      console.warn(`Failed to switch room fallback:`, error);
    }
  }

  const sendSEIMessage = (buffer: ArrayBuffer, option?: any) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      if (option) trtc.sendSEIMessage(buffer, option);
      else trtc.sendSEIMessage(buffer);
    } catch (error) {
      console.warn(`Failed to send SEI message:`, error);
    }
  }

  const sendCustomMessage = (message: any) => {
    if (!trtc) throw new Error('TRTC not initialized');
    try {
      trtc.sendCustomMessage(message);
    } catch (error) {
      console.warn(`Failed to send custom message:`, error);
    }
  }

  async function $reset() {
    inRoom.value = false;
    isRTCMode.value = true;
    isAnchor.value = true;
    autoPlayAudio.value = true;
    autoPlayVideo.value = true;
    enableSEI.value = false;
    videoEncodeCodec.value = null;
    userVolumes.value = {};
    localAudioMuted.value = false;
    localVideoMuted.value = false;
    remoteUsers.value = {};
    rotationAngle.value = 0;
    await createClient()
  }

  return {
    isStr,
    roomId,
    userName,
    inRoom,
    isRTCMode,
    isAnchor,
    autoPlayAudio,
    autoPlayVideo,
    enableSEI,
    videoEncodeCodec,
    userVolumes,
    localAudioMuted,
    localVideoMuted,
    remoteUsers,
    rotationAngle,
    testUserSig,
    switchRoom,
    switchRoomFallback,
    setNotificationInstance,
    createClient,
    useClient,
    // 方法
    joinRoom,
    leaveRoom,
    destroyRoom,
    publishMix,
    startLocalVideo,
    startLocalAudio,
    updateLocalVideo,
    updateLocalAudio,
    toggleLocalAudio,
    toggleLocalVideo,
    stopLocalAudio,
    stopLocalVideo,
    startScreenShare,
    stopScreenShare,
    updateScreenShare,
    // Remote methods
    startRemoteVideo,
    updateRemoteVideo,
    stopRemoteVideo,
    // Plugin methods
    startPlugin,
    updatePlugin,
    stopPlugin,
    downgradeVideoDecode,
    // State methods
    setJoined: (value: boolean) => {
      inRoom.value = value;
    },
    genTestUserSig,
    sendSEIMessage,
    sendCustomMessage,

    $reset
  };
});
