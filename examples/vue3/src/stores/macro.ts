import { defineStore } from 'pinia';
import { ref, isRef } from 'vue';
import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { deepClone } from '@/utils/deepClone'

export interface MacroAction {
  id: string;
  macroId: string;
  method: string;
  args: any[];
  timestamp: number;
  sequence: number;
}

export interface Macro {
  id: string;
  name: string;
  createdAt: number;
}

interface MacroDBSchema extends DBSchema {
  macros: {
    key: string;
    value: Macro;
    indexes: {
      'by-created': number;
    };
  };
  actions: {
    key: string;
    value: MacroAction;
    indexes: {
      'by-macro': string;
      'by-sequence': number;
    };
  };
  settings: {
    key: 'autoRunMacro';
    value: string | null;
  };
  macroCombos: {
    key: string;
    value: string[];
  };
}

interface OldMacroFormat {
  id: string;
  name: string;
  actions: {
    method: string;
    args: any[];
    timestamp: number;
  }[];
  createdAt: number;
}



// 序列化函数，处理不能直接克隆的对象，添加循环引用检测
export function serializeArgs(args: any[], seen = new WeakSet()): any[] {
  return args.map(arg => {
    if (arg === undefined) return null;
    if (arg instanceof Uint8Array) return Array.from(arg);
    if (arg instanceof ArrayBuffer) return new TextDecoder().decode(arg)
    if (arg instanceof MediaStreamTrack) return 'generateVideoTrack'
    if (isRef(arg)) arg = arg.value
    if (Array.isArray(arg)) {
      if (seen.has(arg)) return '[Circular]';
      seen.add(arg);
      const result = serializeArgs(arg, seen);
      seen.delete(arg);
      return result;
    }
    if (typeof arg === 'object' && arg !== null) {
      if (seen.has(arg)) return '[Circular]';
      seen.add(arg);
      const result: Record<string, any> = {};
      for (const key in arg) {
        const value = arg[key];
        if (value instanceof HTMLDivElement) {
          result[key] = value.id
          continue;
        }
        result[key] = value === undefined ? null :
          Array.isArray(value) ? serializeArgs(value, seen) :
            typeof value === 'object' && value !== null ? serializeArgs([value], seen)[0] :
              value;
      }
      seen.delete(arg);
      return result;
    }
    return arg;
  });
}

// 反序列化函数，恢复序列化的数据
function deserializeArgs(args: any[]): any[] {
  return args.map(arg => {
    if (arg === '[Circular]') return undefined;
    if (arg === null) return undefined;
    if (Array.isArray(arg)) return deserializeArgs(arg);
    if (typeof arg === 'object' && arg !== null) {
      const result: Record<string, any> = {};
      for (const key in arg) {
        const value = arg[key];
        result[key] = value === null ? undefined :
          value === '[Circular]' ? undefined :
            Array.isArray(value) ? deserializeArgs(value) :
              typeof value === 'object' && value !== null ? deserializeArgs([value])[0] :
                value;
      }
      return result;
    }
    return arg;
  });
}

export const useMacroStore = defineStore('macro', () => {
  const macros = ref<(Macro & { actions: MacroAction[]; })[]>([]);
  const isRecording = ref(false);
  const isExecute = ref(false);
  const currentRecording = ref<Omit<MacroAction, 'id' | 'macroId' | 'sequence'>[]>([]);
  const autoRunMacro = ref<string | null>(null);
  const isInitialized = ref(false);
  const currentMacroName = ref("");
  const currentComboMacroName = ref('')
  const showMacroSettings = ref(false)
  const offsetTime = ref(0)
  let db: IDBPDatabase<MacroDBSchema>;
  let dbCleanupTimeout: number | null = null;

  async function initDB() {
    if (isInitialized.value) return;

    try {
      if (dbCleanupTimeout) {
        clearTimeout(dbCleanupTimeout);
        dbCleanupTimeout = null;
      }

      db = await openDB<MacroDBSchema>('macro-store', 3, {
        async upgrade(db, oldVersion) {
          if (oldVersion < 1) {
            const macrosStore = db.createObjectStore('macros', { keyPath: 'id' });
            macrosStore.createIndex('by-created', 'createdAt');

            const actionsStore = db.createObjectStore('actions', { keyPath: 'id' });
            actionsStore.createIndex('by-macro', 'macroId');
            actionsStore.createIndex('by-sequence', 'sequence');

            db.createObjectStore('settings');
          }
          if (oldVersion === 1) {
            // 迁移旧数据
            const oldMacrosStore = db.createObjectStore('macros', { keyPath: 'id' });
            oldMacrosStore.createIndex('by-created', 'createdAt');

            const actionsStore = db.createObjectStore('actions', { keyPath: 'id' });
            actionsStore.createIndex('by-macro', 'macroId');
            actionsStore.createIndex('by-sequence', 'sequence');

            // 获取旧数据
            const oldTx = db.transaction('macros', 'readonly');
            const oldMacros = await oldTx.store.getAll() as OldMacroFormat[];

            // 使用新事务写入数据
            const newTx = db.transaction(['macros', 'actions'], 'readwrite');
            for (const oldMacro of oldMacros) {
              const { actions, ...macroData } = oldMacro;
              await newTx.objectStore('macros').put(macroData);

              if (Array.isArray(actions)) {
                for (let i = 0; i < actions.length; i++) {
                  const action = actions[i];
                  await newTx.objectStore('actions').put({
                    id: `${macroData.id}_${i}`,
                    macroId: macroData.id,
                    sequence: i,
                    ...action
                  });
                }
              }
            }
            await newTx.done;
          }
          if (oldVersion <= 3) {
            db.createObjectStore('macroCombos');
          }
        },
      });

      isInitialized.value = true;

      // 初始化完成后，直接加载数据而不是通过loadMacros
      const allMacros = await db.getAll('macros');
      const macrosWithActions = await Promise.all(
        allMacros.map(async (macro) => {
          const actions = await db.getAllFromIndex(
            'actions',
            'by-macro',
            macro.id
          );
          return {
            ...macro,
            actions: actions
              .sort((a, b) => a.sequence - b.sequence)
              .map(action => ({
                ...action,
                args: deserializeArgs(action.args)
              }))
          };
        })
      );
      macros.value = macrosWithActions.sort((a, b) => b.createdAt - a.createdAt);

      // 加载设置
      const settingValue = await db.get('settings', 'autoRunMacro');
      autoRunMacro.value = settingValue ?? null;

    } catch (error) {
      console.error('Failed to initialize macro database:', error);
      throw new Error('Failed to initialize macro database');
    }
  }

  // 保存宏组合
  async function saveMacroCombo(comboName: string, macroIds: string[]) {
    await ensureInitialized();
    const tx = db.transaction('macroCombos', 'readwrite');
    await tx.store.put(macroIds, comboName);
    await tx.done;
  }

  // 获取宏组合
  async function getMacroCombo(comboName: string): Promise<string[] | undefined> {
    await ensureInitialized();
    const tx = db.transaction('macroCombos', 'readonly');
    return tx.store.get(comboName);
  }

  // 获取所有宏组合
  async function getAllMacroCombo() {
    await ensureInitialized();
    const tx = db.transaction('macroCombos', 'readonly');
    const [keys, values] = await Promise.all([tx.store.getAllKeys(), tx.store.getAll()]);
    return keys.map((key, index) => [key, values[index]])
  }

  // 删除宏组合
  async function deleteMacroCombo(comboName: string) {
    await ensureInitialized();
    const deleteMacros = await getMacroCombo(comboName)
    if (!deleteMacros) return
    macros.value.filter(macro => deleteMacros.includes(macro.name)).forEach(macro => deleteMacro(macro.id))
    const tx = db.transaction('macroCombos', 'readwrite');
    await tx.store.delete(comboName);
    await tx.done;
  }

  async function deleteMacroComboOnly(comboName: string) {
    await ensureInitialized();
    const tx = db.transaction('macroCombos', 'readwrite');
    await tx.store.delete(comboName);
    await tx.done;
  }

  async function ensureInitialized() {
    if (!isInitialized.value) {
      await initDB();
    }
  }

  async function loadMacros() {
    await ensureInitialized();
    // 确保数据库已初始化后，直接获取数据
    const allMacros = await db.getAll('macros');
    const macrosWithActions = await Promise.all(
      allMacros.map(async (macro) => {
        const actions = await db.getAllFromIndex(
          'actions',
          'by-macro',
          macro.id
        );
        return {
          ...macro,
          actions: actions
            .sort((a, b) => a.sequence - b.sequence)
            .map(action => ({
              ...action
            }))
        };
      })
    );
    macros.value = macrosWithActions.sort((a, b) => b.createdAt - a.createdAt);
  }

  async function loadSettings() {
    await ensureInitialized();
    const value = await db.get('settings', 'autoRunMacro');
    autoRunMacro.value = value ?? null;
  }

  async function createMacro(name: string, actions: typeof currentRecording.value) {
    console.log('Creating macro in store:', { name, actions });
    await ensureInitialized();

    if (!name) {
      console.error('Macro name is empty');
      throw new Error('宏名称不能为空');
    }

    if (!Array.isArray(actions) || actions.length === 0) {
      console.error('No actions to save');
      throw new Error('没有可保存的动作');
    }

    const macroId = Date.now().toString();
    const macro: Macro = {
      id: macroId,
      name,
      createdAt: Date.now(),
    };

    try {
      console.log('Saving macro:', macro);
      await db.put('macros', macro);

      // 存储动作
      console.log('Saving actions');
      await Promise.all(
        actions.map(async (action, index) => {
          const serializedAction: MacroAction = {
            id: `${macroId}_${index}`,
            macroId,
            sequence: index,
            method: action.method,
            args: serializeArgs(action.args),
            timestamp: action.timestamp,
          };
          await db.put('actions', serializedAction);
        })
      );

      console.log('Loading updated macros');
      await loadMacros();
    } catch (error) {
      console.error('Failed to save macro:', error);
      throw error;
    }
  }

  async function deleteMacro(id: string) {
    await ensureInitialized();
    // 删除所有相关的动作
    const actions = await db.getAllFromIndex('actions', 'by-macro', id);
    await Promise.all(actions.map(action => db.delete('actions', action.id)));

    // 删除宏
    await db.delete('macros', id);

    if (autoRunMacro.value === id) {
      await setAutoRunMacro(null);
    }
    await loadMacros();
  }

  async function setAutoRunMacro(macroId: string | null) {
    await ensureInitialized();
    await db.put('settings', macroId, 'autoRunMacro');
    autoRunMacro.value = macroId;
  }

  function startRecording() {
    console.log('Starting recording');
    isRecording.value = true;
    currentRecording.value = [];
  }

  function stopRecording() {
    console.log('Stopping recording, recorded actions:', currentRecording.value);
    isRecording.value = false;
  }

  function recordAction(method: string, args: any[], timestamp?: number) {
    if (isRecording.value) {
      console.log('Recording action:', { method, args });
      currentRecording.value.push({
        method,
        args: deepClone(args),
        timestamp: timestamp ?? (offsetTime.value ? Date.now() - offsetTime.value : Date.now()),
      });
    }
  }

  async function cleanup() {
    if (db) {
      db.close();
      if (dbCleanupTimeout) {
        clearTimeout(dbCleanupTimeout);
      }
      dbCleanupTimeout = window.setTimeout(() => {
        db = undefined as any;
        isInitialized.value = false;
      }, 5000);
    }
  }

  function $reset() {
    macros.value = [];
    isRecording.value = false;
    currentRecording.value = [];
    autoRunMacro.value = null;
    currentMacroName.value = "";
    currentComboMacroName.value = '';
    showMacroSettings.value = false;
    offsetTime.value = 0;
  }

  return {
    macros,
    currentRecording,
    isRecording,
    autoRunMacro,
    currentMacroName,
    isExecute,
    currentComboMacroName,
    showMacroSettings,
    offsetTime,

    initDB,
    loadMacros,
    createMacro,
    deleteMacro,
    setAutoRunMacro,
    startRecording,
    stopRecording,
    recordAction,
    cleanup,
    saveMacroCombo,
    getMacroCombo,
    getAllMacroCombo,
    deleteMacroCombo,
    deleteMacroComboOnly,

    $reset,
  };
});