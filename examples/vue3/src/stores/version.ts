import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useRoomStore } from './room';

interface PluginInfo {
  path: string;
  loaded?: boolean;
  clazz?: any;
}

interface PluginMap {
  [key: string]: PluginInfo;
}

const npmTags = ['beta', 'wasm', 'ktv'];

export const useVersionStore = defineStore('version', () => {
  const roomStore = useRoomStore();
  const versionList = ref<string[]>([(location.hostname === 'localhost' ? 'dev' : 'build'), 'latest']);
  const currentVersion = ref(new URLSearchParams(location.search).get('version') || (location.hostname === 'localhost' ? 'dev' : 'latest'));

  npmTags.forEach(tag => {
    fetch(`https://registry.npmjs.org/trtc-sdk-v5/${tag}`).then(res => res.json()).then((data) => {
      const latestBetaVersion = data.version;
      const latestNumber = latestBetaVersion.split('.').reverse()[0];
      for (let i = 1; i <= latestNumber; i++) {
        const version = `${latestBetaVersion.split('.').slice(0, -1).join('.')}.${i}`;
        versionList.value.push(version);
      }
    });
  });

  const pluginMap = ref<PluginMap>({
    'CDNStreaming': { path: 'plugins/cdn-streaming/cdn-streaming.umd.js' },
    'BasicBeauty': { path: 'plugins/video-effect/basic-beauty/basic-beauty.umd.js' },
    'VirtualBackground': { path: 'plugins/video-effect/virtual-background/virtual-background.umd.js?version=1.0.0' },
    'Watermark': { path: 'plugins/video-effect/watermark/watermark.umd.js' },
    'VideoMixer': { path: 'plugins/video-effect/video-mixer/video-mixer.umd.js' },
    'Beauty': { path: 'plugins/video-effect/beauty/beauty.umd.js' },
    // 'Debug': { path: 'plugins/debug/debug.iife.js' },
    'DeviceDetector': { path: 'plugins/device-detector/device-detector.umd.js' },
    'CrossRoom': { path: 'plugins/cross-room/cross-room.umd.js' },
    'CustomEncryption': { path: 'plugins/custom-encryption/custom-encryption.umd.js' },
    'VoiceChanger': { path: 'plugins/voice-changer/voice-changer.umd.js' },
    'TRTCVideoDecoder': { path: 'plugins/video-decoder/video-decoder.umd.js?version=1.0.1' },
    'TRTC': { path: 'trtc.js', loaded: currentVersion.value.includes('dev') },
  });

  async function load(name: string, src: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        pluginMap.value[name].loaded = true;
        pluginMap.value[name].clazz = window[name as keyof Window];
        console.log(`${name} loaded from ${src}`);
        resolve(true);
      };
      script.onerror = () => {
        pluginMap.value[name].loaded = false;
        resolve(false);
      };
      document.head.appendChild(script);
    });
  }
  function getRootPath() {
    switch (currentVersion.value) {
      case 'dev':
        return '/';
      case 'build':
        return './';
      case 'latest':
        return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/';
      default:
        return `https://unpkg.com/trtc-sdk-v5@${currentVersion.value}/`;
    }
  }
  async function loadScripts() {
    console.log('start load scripts');
    const rootPath = getRootPath();
    let promises: Promise<boolean>[] = [];
    for (const key in pluginMap.value) {
      const plugin = pluginMap.value[key];
      if (plugin.loaded) continue;
      promises.push(load(key, `${rootPath}${plugin.path}`));
    }
    await Promise.all(promises);
    if (pluginMap.value.TRTC.loaded === false && currentVersion.value) {
      alert(`The TRTC version ${currentVersion.value} you queried does not exist.`);
    }
    promises = [];
    // console.log(JSON.stringify(pluginMap.value, null, 2));
    if (currentVersion.value === 'dev') { // 如果是 dev 模式，直接重试取已经有的版本
      for (const key in pluginMap.value) {
        const plugin = pluginMap.value[key];
        if (!plugin.loaded) {
          promises.push(load(key, `${rootPath}npm-package/${plugin.path}`));
        }
      }
      await Promise.all(promises);
      const isAllLoaded = Object.values(pluginMap.value).every(plugin => plugin.loaded);
      console.log('isAllLoaded', isAllLoaded);
    } else if (currentVersion.value === 'build') {
      for (const key in pluginMap.value) {
        const plugin = pluginMap.value[key];
        if (!plugin.loaded) {
          promises.push(load(key, `https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/${plugin.path}`));
        }
      }
      await Promise.all(promises);
      const isAllLoaded = Object.values(pluginMap.value).every(plugin => plugin.loaded);
      console.log('isAllLoaded', isAllLoaded);
    }
  }

  loadScripts().finally(() => {
    roomStore.createClient();
  });

  function changeVersion(version: string) {
    roomStore.destroyRoom();
    currentVersion.value = version;
    return loadScripts().finally(() => {
      roomStore.createClient();
    });
  }

  async function $reset() {
    try {
      // 重置当前版本到默认值
      currentVersion.value = new URLSearchParams(location.search).get('version') ||
        (location.hostname === 'localhost' ? 'dev' : 'latest');

      // 重置插件加载状态（但不卸载已加载的脚本）
      for (const key in pluginMap.value) {
        pluginMap.value[key].loaded = false;
        pluginMap.value[key].clazz = undefined;
      }

      // 重新加载脚本
      await loadScripts();
    } catch (error) {
      console.error('Failed to reset version store:', error);
      throw error;
    }
  }

  return {
    pluginMap,
    currentVersion,
    versionList,
    loadScripts,
    changeVersion,

    $reset
  };
});
