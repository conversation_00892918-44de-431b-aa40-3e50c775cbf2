import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { TRTCStatistics } from 'trtc-sdk-v5';

export const useStatisticsStore = defineStore('statistics', () => {
  // 定义初始统计数据结构
  const initialStats: TRTCStatistics = {
    rtt: 0,
    downLoss: 0,
    upLoss: 0,
    bytesSent: 0,
    bytesReceived: 0,
    localStatistics: {
      audio: {
        bitrate: 0,
        audioLevel: 0
      },
      video: []
    },
    remoteStatistics: []
  };

  const stats = ref<TRTCStatistics>({ ...initialStats });

  const updateStats = (newStats: TRTCStatistics) => {
    stats.value = newStats;
  };

  // 添加 $reset 方法
  function $reset() {
    stats.value = { ...initialStats };
  }

  return {
    stats,
    updateStats,
    $reset // 添加 $reset 到返回对象
  };
});
