<template>
  <div class="home">
    <div class="preview-list">
      <LocalPreview v-if="mediaStore.showLocalPreview" />
      <ScreenPreview v-if="mediaStore.screenSharing" />
      <MixPreview v-if="mediaStore.showMixVideoPreview" />
      <RemoteVideo v-for="userId in remoteUserIds" :key="userId" :userId="userId" />
      <RemoteScreenShare v-for="userId in remoteUserIds" :key="userId" :userId="userId" />      
    </div>
    <MacroSettings v-show='showMacroSettings' style="max-width: 100%" />
  </div>
</template>

<script setup lang="ts">
import LocalPreview from "../components/LocalPreview.vue";
import ScreenPreview from "../components/ScreenPreview.vue";
import RemoteVideo from "../components/RemoteVideo.vue";
import RemoteScreenShare from "../components/RemoteScreen.vue";
import MixPreview from "../components/MixPreview.vue";
import { useMediaStore } from "../stores/media";
import { useMacroStore } from "../stores/macro";
import { useRoomStore } from "../stores/room";
import { computed, watch } from "vue";
import MacroSettings from '../components/MacroSettings.vue';
import { storeToRefs } from "pinia";

const mediaStore = useMediaStore();
const roomStore = useRoomStore();
const macroStore = useMacroStore();
const { showMacroSettings } = storeToRefs(macroStore);

const showLocalPreview = computed(
  () => mediaStore.videoPreview || mediaStore.audioEnabled
);
watch(showLocalPreview, (newVal) => {
  mediaStore.showLocalPreview = newVal;
});

const remoteUserIds = computed(() => Object.keys(roomStore.remoteUsers));
</script>

<style scoped>
.home {
  height: 100%;
  padding: 16px;
}

.preview-list {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
</style>
