<template>
    <n-card style="min-width:320px;height: 100%;">
        <n-empty v-if="macroList.length === 0" description="暂无宏" />
        <n-scrollbar v-else :style="{ maxHeight: 'calc(min(500px, 100%))' }">
            <n-list>
                <n-list-item v-for="macro in macroList" :key="macro.id">
                    <n-space justify="space-between" size="small">
                        <n-tooltip trigger="hover">
                            <template #trigger>
                                <n-space align="start" @click="clickMacro(macro)">
                                    <n-space>
                                        <span>{{ macro.name }}</span>
                                        <n-tag>{{ macro.actions.length }} 个动作</n-tag>
                                    </n-space>
                                </n-space>
                            </template>
                            点击查看细节
                        </n-tooltip>
                        <n-space>
                            <n-button @click="executeMacro(macro)" :id="'execute-btn-' + macro.name">执行</n-button>
                            <n-button @click="copyMacro(macro)">
                                复制
                            </n-button>
                            <n-button type="error" @click="deleteMacro(macro.id)">
                                删除
                            </n-button>
                        </n-space>
                    </n-space>
                </n-list-item>
            </n-list>
        </n-scrollbar>
    </n-card>
</template>

<script lang="ts" setup>
import { computed, onMounted, defineEmits } from "vue";
import { storeToRefs } from "pinia";
import { useMacroStore } from "@/stores/macro";
import { useMediaStore } from "@/stores/media";
import { useSettingsStore } from "@/stores/settings";
import { useMessage } from "naive-ui";
import type { Macro as MacroBase, MacroAction } from "@/stores/macro";
import { resetStateStores } from '@/utils/resetStores'
import { deepClone } from '@/utils/deepClone'
import { useRoomStore } from "@/stores/room";

const roomStore = useRoomStore();
const mediaStore = useMediaStore();
const settingsStore = useSettingsStore();

type Macro = MacroBase & { actions: MacroAction[] };

const message = useMessage();
const macroStore = useMacroStore();

const emit = defineEmits(['macro-clicked']);

const {
    macros,
} = storeToRefs(macroStore);
const macroList = computed(() => macros.value);

onMounted(async () => {
    if (macroStore.autoRunMacro) {
        const macro = macroStore.macros.find(m => m.id === macroStore.autoRunMacro);
        if (macro) {
            macroStore.showMacroSettings = true;
            await executeMacro(macro);
        }
    }
});

function clickMacro(macro: Macro) {
    if (macroStore.isRecording || macroStore.currentMacroName === macro.name) return
    macroStore.currentRecording = macro.actions;
    macroStore.currentMacroName = macro.name;
    emit('macro-clicked');
}

const methodActions: Record<string, (args: any[]) => Promise<void>> = {
    startLocalVideo: async (args: any[]) => {
        mediaStore.advancedVideoConfig.mirror = args[0]['option']['mirror'].toString()
        mediaStore.advancedVideoConfig.smallStream = args[0]['option']['small']
        mediaStore.videoPublished = args[0]['publish'];
        mediaStore.videoPreview = true;
        args[0]['view'] = `${mediaStore.currentInstanceId}-local-video`
        if (args[0]['option']['videoTrack']) {
            mediaStore.selectedCamera = 'music_A'
            music_A.play();
            args[0]['option']['videoTrack'] = streamGenerator.generateVideoTrack()
        }
    },
    updateLocalVideo: async (args: any[]) => {
        roomStore.rotationAngle = args[0]['option']['rotation'] ?? roomStore.rotationAngle
        if (args[0]['option']['mirror']) mediaStore.advancedVideoConfig.mirror = args[0]['option']['mirror'].toString()
    },
    stopLocalVideo: async (args: any[]) => {
        mediaStore.videoPreview = false;
    },
    startLocalAudio: async (args: any[]) => {
        mediaStore.audioEnabled = true;
        mediaStore.selectedMicrophone = args[0]['option']['microphoneId'];
        mediaStore.audioProfile = args[0]['option']['profile'];
        mediaStore.aec = args[0]['option']['echoCancellation'];
        mediaStore.ans = args[0]['option']['noiseSuppression'];
        mediaStore.agc = args[0]['option']['autoGainControl'];
        mediaStore.audioPublished = args[0]['publish'];
    },
    stopLocalAudio: async (args: any[]) => {
        mediaStore.audioEnabled = false;
    },
    updateLocalAudio: async (args: any[]) => {
        mediaStore.monitorVolume = args[0]['option']['earMonitorVolume'] ?? mediaStore.monitorVolume;
        mediaStore.captureVolume = args[0]['option']['captureVolume'] ?? mediaStore.captureVolume;
    },
    startScreenShare: async (args: any[]) => {
        mediaStore.screenSharing = true;
        args[0]['view'] = `${mediaStore.currentInstanceId}-local-share-preview`
    },
    stopScreenShare: async (args: any[]) => {
        mediaStore.screenSharing = false;
    },
    updateScreenShare: async (args: any[]) => {
        mediaStore.screenProfile = args[0]['option']['profile'] ?? mediaStore.screenProfile;
        mediaStore.shareSystemAudio = args[0]['option']['systemAudio'] ?? mediaStore.shareSystemAudio;
        mediaStore.qosPreference = args[0]['option']['qosPreference'] ?? mediaStore.qosPreference;
    },
    startPlugin: async (args: any[]) => {
        if (args[0] === 'AudioMixer') {
            if (args[1]['id'] === 'background') mediaStore.background = true
        }
        if (args[0] === 'AIDenoiser') {
            mediaStore.aiNoise = true;
        }
        if (args[0] === 'BasicBeauty') {
            settingsStore.pluginFeatures.beauty = true;
        }
        if (args[0] === 'Beauty') {
            settingsStore.pluginFeatures.advancedBeauty = true
        }
        if (args[0] === 'VirtualBackground') {
            settingsStore.virtualBackground = args[1]['type']
        }
        if (args[0] === 'Watermark') {
            settingsStore.pluginFeatures.watermark = true
        }
        if (args[0] === 'VideoMixer') {
            settingsStore.pluginFeatures.videoMix = true
            mediaStore.setMixPreview(true);
        }
        if (args[0] === 'VoiceChanger') {
            mediaStore.voiceType = args[1]['voiceType'].toString()
        }
    },
    stopPlugin: async (args: any[]) => {
        if (args[0] === 'AudioMixer') {
            if (args[1]['id'] === 'background') mediaStore.background = false
        }
        if (args[0] === 'AIDenoiser') {
            mediaStore.aiNoise = false;
        }
        if (args[0] === 'BasicBeauty') {
            settingsStore.pluginFeatures.beauty = false;
        }
        if (args[0] === 'Beauty') {
            settingsStore.pluginFeatures.advancedBeauty = false
        }
        if (args[0] === 'VirtualBackground') {
            settingsStore.virtualBackground = 'normal'
        }
        if (args[0] === 'Watermark') {
            settingsStore.pluginFeatures.watermark = false
        }
        if (args[0] === 'VideoMixer') {
            settingsStore.pluginFeatures.videoMix = false
            mediaStore.setMixPreview(false);
        }
        if (args[0] === 'VoiceChanger') {
            mediaStore.voiceType = '0'
        }
    },
    updatePlugin: async (args: any[]) => {
        if (args[0] === 'BasicBeauty') {
            settingsStore.beautyValues = { ...args[1] }
        }
        if (args[0] === 'VirtualBackground') {
            settingsStore.blurLevel = args[1]['blurLevel'] ?? settingsStore.blurLevel
            settingsStore.pluginFeatures.enableFaceCentering = args[1]['enableFaceCentering'] ?? settingsStore.pluginFeatures.enableFaceCentering
        }
        if (args[0] === 'VoiceChanger') {
            mediaStore.voiceType = args[1]['voiceType'].toString()
        }
    },
    enterRoom: async (args: any[]) => {
        roomStore.inRoom = true;
        roomStore.roomId = args[0]['roomId'];
        roomStore.userName = args[0]['userId'];
        mediaStore.advancedVideoConfig.vp8Encode = args[0]['useVp8']
        mediaStore.advancedVideoConfig.enableHWEncode = args[0]['preferHW']
    },
    exitRoom: async (args: any[]) => {
        roomStore.inRoom = false;
    },
    sendSEIMessage: async (args: any[]) => {
        const encoder = new TextEncoder();
        args[0] = encoder.encode(args[0]).buffer;
    },
};

async function executeMacro(macro: Macro) {
    if (macroStore.isRecording) return
    macroStore.isExecute = true;
    try {
        await resetStateStores()
        const trtc = await roomStore.useClient();
        if (!trtc) {
            message.error("TRTC 实例未初始化");
        }
        if (macro.actions.length === 0) {
            message.error("宏指令为空");
            return;
        }
        let startTime = macro.actions[0].timestamp;
        macroStore.currentRecording = macro.actions;
        macroStore.currentMacroName = macro.name;
        for (const action of macro.actions) {
            const intervalTime = action.timestamp - startTime;
            if (intervalTime > 0) {
                await new Promise((resolve) => setTimeout(resolve, intervalTime));
            }
            const method = action.method;
            if (typeof (trtc as any)[method] === "function") {
                if (method in methodActions) await methodActions[method](action.args);
                await ((trtc as any)[method] as Function)(...deepClone(action.args));
            }
            startTime = action.timestamp;
        }
        message.success("宏执行成功");
    } catch (error) {
        message.error("宏执行失败：" + (error as Error).message);
    }
    macroStore.isExecute = false;
}

async function deleteMacro(id: string) {
    await macroStore.deleteMacro(id);
    message.success("宏删除成功");
}

const copyMacro = (macro: Macro) => {
    const macros: any[] = []
    macro.actions.forEach((action) => {
        macros.push({ [action.method]: action.args, timestamp: action.timestamp })
    })
    navigator.clipboard.writeText(JSON.stringify({ [macro.name]: macros }))
        .then(() => {
            message.success('复制成功');
        })
        .catch(err => {
            console.error('复制失败:', err);
            message.error('复制失败，请手动选择文本复制');
        });
};

defineExpose({ executeMacro });
</script>