<template>
  <div :class="['macro-container', containerClass]">
    <div style="display: flex;flex-direction: column;flex: 5; ">
      <MacroTab />
      <MacroList @macro-clicked="lineKey++" ref="macroListRef" />
    </div>
    <div style="display: flex;flex-direction: column;flex: 7">
      <MacroLineTab @executeNow="executeNow" />
      <transition name="fade">
        <MacroLine :items="timelineItems" :initialActiveIndex="0" :key="lineKey" />
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import MacroTab from './tabs/MacroTab.vue'
import MacroLine from './MacroLine.vue'
import MacroList from './MacroList.vue';
import MacroLineTab from './tabs/MacroLineTab.vue';
import { computed, Ref, ref } from 'vue';
import { useMacroStore } from '../stores/macro'
import { storeToRefs } from 'pinia';
import { trtcInstances } from '@/global/globalState'
import isMobile from '@/utils/isMobile';
const macroListRef: Ref<any> = ref(null);
const macroStore = useMacroStore()
const { currentRecording } = storeToRefs(macroStore)
const lineKey = ref(0)
const timelineItems = computed(() => {
  return currentRecording.value.map((item, index) => ({
    method: item.method,
    args: item.args,
    value: index,
    timestamp: item.timestamp
  }))
})
const containerClass = computed(() => {
  trtcInstances.value.forEach(trtcInstance => trtcInstance.style.maxWidth = `${100 / trtcInstances.value.length}vw`)
  return trtcInstances.value.length >= 2 || isMobile ? 'column-layout' : 'row-layout';
})
const executeNow = () => {
  if (!macroListRef.value) return
  const macro = macroStore.macros.find(macro => macro.name === macroStore.currentMacroName)
  macroListRef.value.executeMacro(macro)
}
</script>

<style scoped>
.macro-container {
  display: flex;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background-color: #fff;
  transition: all 0.3s ease;
}

.row-layout {
  flex-direction: row;
}

.column-layout {
  flex-direction: column;
}

.macro-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
  max-height: 1500px;
  max-width: 2000px;
  will-change: max-height;
  transform: translateZ(0);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  max-height: 0;
  max-width: 0;
}
</style>