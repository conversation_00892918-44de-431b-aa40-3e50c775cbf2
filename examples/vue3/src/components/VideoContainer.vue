<template>
  <div class="video-container" :class="{ 'is-local': isLocal }">
    <div class="video-wrapper" ref="videoWrapperRef"></div>
    <div class="overlay">
      <div class="user-id">{{ userId }}</div>
      <div class="controls" v-if="isLocal">
        <button
          class="control-btn"
          :class="{ active: isMicMuted }"
          @click="toggleMic"
        >
          <i
            class="fas"
            :class="isMicMuted ? 'fa-microphone-slash' : 'fa-microphone'"
          ></i>
        </button>
        <button
          class="control-btn"
          :class="{ active: isVideoMuted }"
          @click="toggleVideo"
        >
          <i
            class="fas"
            :class="isVideoMuted ? 'fa-video-slash' : 'fa-video'"
          ></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, defineEmits, nextTick, watch } from "vue";

const props = defineProps<{
  userId: string;
  isLocal: boolean;
}>();

const emit = defineEmits<{
  (e: "toggleMic"): void;
  (e: "toggleVideo"): void;
}>();

const videoWrapperRef = ref<HTMLDivElement>();
const isMicMuted = ref(false);
const isVideoMuted = ref(false);

onMounted(async () => {
  console.log(
    "VideoContainer mounted, videoWrapperRef:",
    videoWrapperRef.value
  );
});

const toggleMic = () => {
  isMicMuted.value = !isMicMuted.value;
  emit("toggleMic");
};

const toggleVideo = () => {
  isVideoMuted.value = !isVideoMuted.value;
  emit("toggleVideo");
};

</script>

<style scoped>
.video-container {
  position: relative;
  width: 320px;
  height: 240px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-wrapper {
  width: 100%;
  height: 100%;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.5) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.5) 100%
  );
}

.user-id {
  color: white;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.controls {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.control-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.control-btn.active {
  background-color: #dc3545;
}

.is-local {
  transform: scaleX(-1);
}

.is-local .overlay {
  transform: scaleX(-1);
}
</style>
