<template>
  <div class="mobile-menu">
    <n-tabs type="line" animated>
      <!-- Room Configuration Tab -->
      <n-tab-pane name="room" tab="房间配置">
        <n-space vertical>
          <n-input v-model:value="roomId" placeholder="房间号" />
          <n-input v-model:value="userName" placeholder="用户名" />
          <n-space>
            <n-button type="primary" :disabled="inRoom" @click="joinRoom">
              进入房间
            </n-button>
            <n-button :disabled="!inRoom" @click="leaveRoom">
              退出房间
            </n-button>
          </n-space>
        </n-space>
      </n-tab-pane>

      <!-- Video Configuration Tab -->
      <n-tab-pane name="video" tab="视频配置">
        <n-space vertical>
          <n-space align="center">
            <span>视频预览</span>
            <n-button
              :type="videoPreview ? 'primary' : 'default'"
              @click="mediaStore.toggleVideoPreview"
            >
              {{ videoPreview ? "关闭预览" : "开启预览" }}
            </n-button>
            <n-space align="center" class="ml-4">
              <span>发布视频</span>
              <n-switch
                v-model:value="videoPublished"
                @update:value="toggleVideoPublish"
              />
            </n-space>
          </n-space>
          <n-select
            v-model:value="selectedCamera"
            :options="cameraOptions"
            placeholder="选择摄像头"
          />
          <n-select
            v-model:value="videoProfile"
            :options="videoProfileOptions"
            placeholder="视频配置"
          />
        </n-space>
      </n-tab-pane>

      <!-- Audio Configuration Tab -->
      <n-tab-pane name="audio" tab="音频配置">
        <n-space vertical>
          <n-space align="center">
            <span>麦克风</span>
            <n-button
              :type="audioEnabled ? 'primary' : 'default'"
              @click="mediaStore.toggleAudio"
            >
              {{ audioEnabled ? "关闭麦克风" : "开启麦克风" }}
            </n-button>
            <n-space align="center" class="ml-4">
              <span>发布音频</span>
              <n-switch
                v-model:value="audioPublished"
                @update:value="toggleAudioPublish"
              />
            </n-space>
          </n-space>
          <n-select
            v-model:value="selectedMicrophone"
            :options="microphoneOptions"
            placeholder="选择麦克风"
          />
          <n-select
            v-model:value="audioProfile"
            :options="audioProfileOptions"
            placeholder="音频配置"
          />
        </n-space>
      </n-tab-pane>

      <!-- Screen Share Tab -->
      <n-tab-pane name="screen" tab="屏幕共享">
        <n-space vertical>
          <n-select
            v-model:value="screenProfile"
            :options="screenProfileOptions"
            placeholder="分辨率+帧率"
          />
          <n-space align="center">
            <span>系统声音</span>
            <n-switch v-model:value="shareSystemAudio" />
          </n-space>
          <n-select
            v-model:value="qosPreference"
            :options="qosPreferenceOptions"
            placeholder="编码策略"
          />
          <n-space>
            <n-button @click="toggleScreenSharing">
              {{ screenSharing ? "停止共享" : "屏幕共享" }}
            </n-button>
            <n-button @click="toggleScreenPublish">
              {{ screenPublished ? "停止发布" : "发布屏幕" }}
            </n-button>
          </n-space>
        </n-space>
      </n-tab-pane>

      <!-- Macro Tab -->
      <n-tab-pane name="macro" tab="宏功能">
        <MacroTab />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import {
  NTabs,
  NTabPane,
  NSpace,
  NInput,
  NButton,
  NSwitch,
  NSelect,
  useNotification,
} from "naive-ui";
import { ref, computed, onMounted, watch } from "vue";
import { storeToRefs } from "pinia";
import { useRoomStore } from "@/stores/room";
import { useMediaStore } from "@/stores/media";
import MacroTab from "./tabs/MacroTab.vue";

// 初始化通知
const notification = useNotification();

// 初始化 stores
const roomStore = useRoomStore();
const mediaStore = useMediaStore();

// 从 roomStore 获取状态
const { roomId, userName, inRoom, localVideoMuted, localAudioMuted } =
  storeToRefs(roomStore);

// 从 mediaStore 获取状态
const {
  videoPreview,
  selectedCamera,
  cameraOptions,
  audioEnabled,
  selectedMicrophone,
  microphoneOptions,
  videoProfile,
  videoProfileOptions,
  audioProfile,
  audioProfileOptions,
  videoPublished,
  audioPublished,
  screenSharing,
  screenPublished,
  shareSystemAudio,
  qosPreference,
  qosPreferenceOptions,
  screenProfile,
  screenProfileOptions,
} = storeToRefs(mediaStore);

// 计算属性
const videoMuted = computed(() => localVideoMuted.value);
const audioMuted = computed(() => localAudioMuted.value);

// 初始化设备列表
onMounted(async () => {
  await mediaStore.updateDeviceLists();
});

// 房间方法
const joinRoom = async () => {
  try {
    // 进入房间前确保设备准备就绪
    if (videoPreview.value && selectedCamera.value) {
      mediaStore.toggleVideoPreview();
    }

    if (audioEnabled.value && selectedMicrophone.value) {
      mediaStore.toggleAudio();
    }

    await roomStore.joinRoom();
    notification.success({
      title: "进房成功",
      content: `房间号: ${roomId.value}, 用户名: ${userName.value}`,
      duration: 3000,
    });
  } catch (error: any) {
    console.error("进房失败:", error);
    notification.error({
      title: "进房失败",
      content: error.message,
      duration: 3000,
    });
  }
};

const leaveRoom = () => {
  roomStore.leaveRoom();
  notification.info({
    title: "退出房间",
    content: "已退出房间",
    duration: 3000,
  });
};

// 视频方法
const toggleVideoMute = () => {
  roomStore.toggleLocalVideo(!videoMuted.value);
};

const toggleVideoPublish = () => {
  mediaStore.toggleVideoPublish();
};

// 音频方法
const toggleAudioMute = () => {
  roomStore.toggleLocalAudio(!audioMuted.value);
};

const toggleAudioPublish = () => {
  mediaStore.toggleAudioPublish();
};

// 屏幕共享方法
const toggleScreenSharing = () => {
  mediaStore.toggleScreenSharing();
};

const toggleScreenPublish = () => {
  mediaStore.toggleScreenPublish();
};
</script>

<style scoped>
.mobile-menu {
  width: 100%;
  background-color: var(--n-color);
}

.ml-4 {
  margin-left: 16px;
}
</style>
