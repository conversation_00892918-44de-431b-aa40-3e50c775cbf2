<template>
  <div 
    class="image-container" 
    ref="containerRef"
    tabindex="0"
    @keydown="handleKeyDown"
    @focus="isFocused = true"
    @blur="isFocused = false"
  >
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <n-spin size="large" />
    </div>

    <!-- 错误状态 -->
    <div v-if="loadError" class="error-overlay">
      <p>{{ loadError }}</p>
      <n-button size="small" @click="retryLoad">重试</n-button>
    </div>

    <img
      v-show="!isLoading && !loadError"
      :src="props.src"
      :style="{
        width: `${imageWidth}px`,
        height: `${imageHeight}px`,
        left: `${imageX}px`,
        top: `${imageY}px`,
        cursor: isDragging ? 'grabbing' : 'grab',
        border: '2px solid #2196f3'
      }"
      @load="handleImageLoad"
      @mousedown="handleEdgeResize"
      ref="imageRef"
      draggable="false"
      class="resizable-image"
    >
    <!-- 调整大小的手柄 -->
    <div 
      v-if="isResizing"
      class="resize-handles"
    >
      <div 
        v-for="handle in resizeHandles" 
        :key="handle.position"
        :class="['resize-handle', handle.position]"
        @mousedown="startResize($event, handle.position)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { NSpin, NButton, NIcon } from 'naive-ui';

interface Props {
  src: string;
  modelValue?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  // 自定义选项
  containerStyle?: {
    backgroundColor?: string;
    border?: string;
    borderRadius?: string;
  };
  // 是否显示网格背景
  showGrid?: boolean;
  // 是否启用触摸设备支持
  touchEnabled?: boolean;
  // 最小尺寸限制
  minSize?: {
    width: number;
    height: number;
  };
}

interface ResizeHandle {
  position: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    x: 0,
    y: 0,
    width: 200,
    height: 200
  })
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: { x: number; y: number; width: number; height: number }): void;
}>();

// 状态管理
const containerRef = ref<HTMLElement | null>(null);
const imageRef = ref<HTMLElement | null>(null);
const isDragging = ref(false);
const isResizing = ref(false);
const currentResizeHandle = ref('');
const isFocused = ref(false);
const isLoading = ref(true);
const loadError = ref<string | null>(null);

// 移动步长（像素）
const MOVE_STEP = 1;
const MOVE_STEP_LARGE = 10;

// 图片位置和大小
const imageX = ref(props.modelValue.x);
const imageY = ref(props.modelValue.y);
const imageWidth = ref(props.modelValue.width);
const imageHeight = ref(props.modelValue.height);

// 拖拽相关变量
const startX = ref(0);
const startY = ref(0);
const originalX = ref(0);
const originalY = ref(0);

// 调整大小的手柄位置
const resizeHandles: ResizeHandle[] = [
  { position: 'top-left' },
  { position: 'top-right' },
  { position: 'bottom-left' },
  { position: 'bottom-right' }
];

// 监听属性变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    imageX.value = newValue.x;
    imageY.value = newValue.y;
    imageWidth.value = newValue.width;
    imageHeight.value = newValue.height;
  }
}, { deep: true });

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if (isResizing.value) return;
  
  isDragging.value = true;
  startX.value = e.clientX;
  startY.value = e.clientY;
  originalX.value = imageX.value;
  originalY.value = imageY.value;

  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
};

// 处理拖拽
const handleDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;

  const deltaX = e.clientX - startX.value;
  const deltaY = e.clientY - startY.value;

  const newX = originalX.value + deltaX;
  const newY = originalY.value + deltaY;

  // 确保图片不会完全拖出容器
  if (containerRef.value) {
    const containerRect = containerRef.value.getBoundingClientRect();
    imageX.value = Math.max(0, Math.min(newX, containerRect.width - imageWidth.value));
    imageY.value = Math.max(0, Math.min(newY, containerRect.height - imageHeight.value));
  }

  emitUpdate();
};

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 处理边缘调整大小
const handleEdgeResize = (e: MouseEvent) => {
  if (!imageRef.value) return;
  
  const img = imageRef.value as HTMLElement;
  const rect = img.getBoundingClientRect();
  const edgeThreshold = 10; // 边缘检测阈值(像素)
  
  // 检测鼠标是否在边缘
  const isLeftEdge = e.clientX - rect.left <= edgeThreshold;
  const isRightEdge = rect.right - e.clientX <= edgeThreshold;
  const isTopEdge = e.clientY - rect.top <= edgeThreshold;
  const isBottomEdge = rect.bottom - e.clientY <= edgeThreshold;
  
  if (isLeftEdge || isRightEdge || isTopEdge || isBottomEdge) {
    // 确定调整方向
    let position = '';
    if (isLeftEdge && isTopEdge) position = 'top-left';
    else if (isRightEdge && isTopEdge) position = 'top-right';
    else if (isLeftEdge && isBottomEdge) position = 'bottom-left';
    else if (isRightEdge && isBottomEdge) position = 'bottom-right';
    else if (isLeftEdge) position = 'left';
    else if (isRightEdge) position = 'right';
    else if (isTopEdge) position = 'top';
    else if (isBottomEdge) position = 'bottom';
    
    if (position) {
      startResize(e, position);
      return;
    }
  }
  
  // 如果不是边缘点击，则开始拖拽
  startDrag(e);
};

// 开始调整大小
const startResize = (e: MouseEvent, position: string) => {
  e.stopPropagation();
  isDragging.value = false;
  currentResizeHandle.value = position;
  startX.value = e.clientX;
  startY.value = e.clientY;
  originalX.value = imageX.value;
  originalY.value = imageY.value;

  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
};

// 处理调整大小
const handleResize = (e: MouseEvent) => {
  if (!currentResizeHandle.value) return;

  const deltaX = e.clientX - startX.value;
  const deltaY = e.clientY - startY.value;
  const aspectRatio = imageWidth.value / imageHeight.value;

  switch (currentResizeHandle.value) {
    case 'bottom-right':
      imageWidth.value = Math.max(50, imageWidth.value + deltaX);
      imageHeight.value = Math.max(50, imageHeight.value + deltaY);
      break;
    case 'bottom-left':
      imageWidth.value = Math.max(50, imageWidth.value - deltaX);
      imageHeight.value = Math.max(50, imageHeight.value + deltaY);
      imageX.value = originalX.value + deltaX;
      break;
    case 'top-right':
      imageWidth.value = Math.max(50, imageWidth.value + deltaX);
      imageHeight.value = Math.max(50, imageHeight.value - deltaY);
      imageY.value = originalY.value + deltaY;
      break;
    case 'top-left':
      imageWidth.value = Math.max(50, imageWidth.value - deltaX);
      imageHeight.value = Math.max(50, imageHeight.value - deltaY);
      imageX.value = originalX.value + deltaX;
      imageY.value = originalY.value + deltaY;
      break;
  }

  startX.value = e.clientX;
  startY.value = e.clientY;
  originalX.value = imageX.value;
  originalY.value = imageY.value;

  emitUpdate();
};

// 停止调整大小
const stopResize = () => {
  currentResizeHandle.value = '';
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
};

// 图片加载处理
const handleImageLoad = () => {
  isLoading.value = false;
  loadError.value = null;
};

const handleImageError = () => {
  isLoading.value = false;
  loadError.value = '图片加载失败';
};

const retryLoad = () => {
  isLoading.value = true;
  loadError.value = null;
  // 通过修改src来触发重新加载
  if (imageRef.value) {
    const img = imageRef.value as HTMLImageElement;
    const currentSrc = img.src;
    img.src = '';
    setTimeout(() => {
      img.src = currentSrc;
    }, 100);
  }
};

// 键盘事件处理
const handleKeyDown = (e: KeyboardEvent) => {
  if (!isFocused.value) return;

  const step = e.shiftKey ? MOVE_STEP_LARGE : MOVE_STEP;
  let deltaX = 0;
  let deltaY = 0;

  switch (e.key) {
    case 'ArrowLeft':
      deltaX = -step;
      break;
    case 'ArrowRight':
      deltaX = step;
      break;
    case 'ArrowUp':
      deltaY = -step;
      break;
    case 'ArrowDown':
      deltaY = step;
      break;
    default:
      return;
  }

  e.preventDefault();

  if (containerRef.value) {
    const containerRect = containerRef.value.getBoundingClientRect();
    const newX = imageX.value + deltaX;
    const newY = imageY.value + deltaY;

    imageX.value = Math.max(0, Math.min(newX, containerRect.width - imageWidth.value));
    imageY.value = Math.max(0, Math.min(newY, containerRect.height - imageHeight.value));

    emitUpdate();
  }
};

// 发送更新事件
const emitUpdate = () => {
  emit('update:modelValue', {
    x: imageX.value,
    y: imageY.value,
    width: imageWidth.value,
    height: imageHeight.value
  });
};

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);
});
</script>

<style scoped>
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border: 2px solid #ccc;
  overflow: hidden;
}

.image-container img {
  position: absolute;
  user-select: none;
}

.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #fff;
  border: 2px solid #2196f3;
  pointer-events: auto;
  z-index: 1;
}

.resizable-image {
  box-sizing: border-box;
}

.resizable-image:hover {
  cursor: default;
}

.resizable-image:hover {
  border-color: #64b5f6;
}

/* 边缘鼠标样式 */
.resizable-image:hover {
  cursor: default;
}

.resizable-image:hover:not(.is-dragging) {
  cursor: default;
}

.resizable-image:hover:not(.is-dragging):active {
  cursor: grabbing;
}

/* 边缘鼠标样式 */
.resizable-image:hover:not(.is-dragging) {
  cursor: default;
}

.resizable-image:hover:not(.is-dragging):active {
  cursor: grabbing;
}

/* 边缘鼠标样式 */
.resizable-image:hover:not(.is-dragging) {
  cursor: default;
}

.resizable-image:hover:not(.is-dragging):active {
  cursor: grabbing;
}

/* 边缘鼠标样式 */
.resizable-image:hover:not(.is-dragging) {
  cursor: default;
}

.resizable-image:hover:not(.is-dragging):active {
  cursor: grabbing;
}

.resize-handle.top-left {
  top: -5px;
  left: -5px;
  cursor: nw-resize;
}

.resize-handle.top-right {
  top: -5px;
  right: -5px;
  cursor: ne-resize;
}

.resize-handle.bottom-left {
  bottom: -5px;
  left: -5px;
  cursor: sw-resize;
}

.resize-handle.bottom-right {
  bottom: -5px;
  right: -5px;
  cursor: se-resize;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1;
}

.error-overlay {
  color: #ff4d4f;
  text-align: center;
}

.error-icon {
  margin-bottom: 8px;
}

.error-overlay p {
  margin: 8px 0;
}
</style>
