<template>
  <div class="remote-video" v-if="remoteUser">
    <div class="video-container" ref="videoRef"></div>
    <!-- Volume indicator -->
    <div class="volume-indicator" v-if="volume !== undefined">
      音量: {{ Math.round(volume) }}
    </div>
    <!-- User ID display -->
    <div class="user-id">
      {{ userId }} {{ remoteUser.decoder }} {{ remoteUser.renderer }}
    </div>
    <!-- <div class="room-id" v-if="remoteUser.hasVideo">
      {{ remoteUser.roomId }}
    </div> -->
    <div class="downgrade">
      <a @click="roomStore.downgradeVideoDecode(userId, 'main')">解码降级</a>
    </div>
    <!-- Video/Audio status -->
    <div class="status-indicators">
      <div class="status-indicator" :class="{ inactive: !remoteUser.hasVideo }">
        <n-icon>
          <videocam-outline v-if="remoteUser.hasVideo" />
          <videocam-off-outline v-else />
        </n-icon>
      </div>
      <div class="status-indicator" :class="{ inactive: !remoteUser.hasAudio }">
        <n-icon>
          <mic-outline v-if="remoteUser.hasAudio" />
          <mic-off-outline v-else />
        </n-icon>
      </div>
      <div class="status-indicator clickable" @click="handleToggleVideo">
        <n-icon>
          <play-circle-outline v-if="!shouldStartVideo" />
          <stop-circle-outline v-else />
        </n-icon>
      </div>
      <div class="status-indicator clickable" @click="handleToggleSmall">
        <span v-show="isPreferSmall"> 小 </span>
        <span v-show="!isPreferSmall"> 大 </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRoomStore, RemoteUser } from "../stores/room";
import { NIcon } from "naive-ui";
import {
  VideocamOutline,
  VideocamOffOutline,
  MicOutline,
  MicOffOutline,
  PlayCircleOutline,
  StopCircleOutline
} from "@vicons/ionicons5";

const props = defineProps<{
  userId: string;
  roomId?: string;
}>();

const roomStore = useRoomStore();
const videoRef = ref<HTMLDivElement>();
const volume = computed(() => roomStore.userVolumes[props.userId]);
const remoteUser = computed(() => roomStore.remoteUsers[props.userId]);

const shouldStartVideo = ref(false);
const isPreferSmall = ref(false);

watch(
  () => remoteUser.value?.hasVideo && !!videoRef.value,
  (newVal) => {
    if (newVal) {
      roomStore
        .startRemoteVideo(props.userId, videoRef.value!)
        .then(() => (shouldStartVideo.value = false));
    }
  },
  { immediate: true }
);

const handleToggleVideo = () => {
  if (shouldStartVideo.value) {
    roomStore
      .startRemoteVideo(props.userId, videoRef.value!)
      .then(() => (shouldStartVideo.value = false));
  } else {
    roomStore
      .stopRemoteVideo(props.userId)
      .then(() => (shouldStartVideo.value = true));
  }
};

const handleToggleSmall = async () => {
  try {
    await roomStore.updateRemoteVideo({
      userId: props.userId,
      streamType: 'main' as any,
      option: {
        small: !isPreferSmall.value
      }
    });
    isPreferSmall.value = !isPreferSmall.value;
  } catch (error) {
    console.error(error);
  }
};
</script>

<style scoped>
.remote-video {
  position: relative;
  width: 320px;
  height: 240px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-container {
  width: 100%;
  height: 100%;
}

.volume-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.user-id {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.room-id {
  position: absolute;
  top: 38px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.downgrade {
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  bottom: 8px;
  left: 8px;
  z-index: 1;
  cursor: pointer;
  user-select: none;
}

.downgrade a {
  color: white;
  text-decoration: none;
}

.downgrade:hover {
  background: rgba(0, 0, 0, 0.8);
}

.status-indicators {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 1;
}

.status-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.status-indicator.inactive {
  color: #dc3545;
}

.status-indicator.clickable {
  cursor: pointer;
  user-select: none;
}

.status-indicator.clickable:hover {
  background: rgba(255, 0, 0, 0.2);
}
</style>
