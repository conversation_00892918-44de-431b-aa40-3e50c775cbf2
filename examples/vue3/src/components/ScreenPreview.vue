<template>
  <div class="screen-preview">
    <div class="preview-header">
      <span>屏幕分享预览</span>
    </div>
    <div class="preview-container" :id="currentInstanceId + '-local-share-preview'" ref="previewRef"></div>
    <div class="controls">
      <n-button circle :type="screenPublished ? 'primary' : 'default'" @click="toggleScreenPublish">
        <template #icon>
          <n-icon>
            <ShareSocialOutline />
          </n-icon>
        </template>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from "vue";
import { useMediaStore } from "../stores/media";
import { storeToRefs } from "pinia";
import { useRoomStore } from "../stores/room";
import { NButton, NIcon } from "naive-ui";
import { ShareSocialOutline } from "@vicons/ionicons5";

const roomStore = useRoomStore();
const mediaStore = useMediaStore();
const { screenSharing, screenPublished, currentInstanceId } = storeToRefs(mediaStore);
const previewRef = ref<HTMLDivElement>();

onUnmounted(async () => {
  if (screenSharing.value) {
    try {
      await roomStore.stopScreenShare();
    } catch (error) {
      console.error("Failed to stop screen sharing:", error);
    }
  }
});

const toggleScreenPublish = () => {
  mediaStore.toggleScreenPublish();
};
</script>

<style scoped>
.screen-preview {
  width: 320px;
  background-color: var(--n-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
}

.preview-header {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--n-color);
  border-bottom: 1px solid var(--n-border-color);
}

.preview-container {
  width: 100%;
  height: 180px;
  background-color: #000;
}

.controls {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 1;
}

.controls :deep(.n-button) {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  background: rgba(0, 0, 0, 0.6);
}

.controls :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}
</style>
