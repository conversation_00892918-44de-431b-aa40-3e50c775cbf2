<template>
  <div class="status-bar">
    <n-space justify="space-between">
      <n-space>
        <n-select v-model:value="currentVersion" :options="versionOptions" size="small" style="width: 160px"
          @update:value="handleVersionChange" />
        <n-tag :type="networkQualityType" size="small" class="stat-tag">
          RTT: {{ stats.rtt }}ms
        </n-tag>
        <n-tag :type="networkQualityType" size="small" class="stat-tag">
          丢包率: ↑{{ stats.upLoss.toFixed(1) }}% ↓{{
            stats.downLoss.toFixed(1)
          }}%
        </n-tag>
        <template v-for="video in stats.localStatistics.video" :key="video.videoType">
          <n-tag type="info" size="small" class="stat-tag">
            {{
              video.videoType === "big"
                ? "主流"
                : video.videoType === "small"
                  ? "小流"
                  : "辅流"
            }}: {{ video.width }}x{{ video.height }} {{ video.frameRate }}fps
            {{ video.bitrate.toFixed(0) }}kbps
          </n-tag>
        </template>
        <template v-if="stats.localStatistics.audio">
          <n-tag type="info" size="small" class="stat-tag">
            音频:
            {{ stats.localStatistics.audio.bitrate.toFixed(0) }}kbps
          </n-tag>
        </template>
      </n-space>
      <n-space>
        <n-tag type="success" size="small" class="stat-tag">
          流量: ↑{{ formatBytes(stats.bytesSent) }}
        </n-tag>
        <n-tag type="success" size="small" class="stat-tag">
          ↓{{ formatBytes(stats.bytesReceived) }}
        </n-tag>
      </n-space>
      <template v-for="remote in stats.remoteStatistics" :key="remote.userId">
          <n-tag type="info" size="small" class="stat-tag">
            {{ remote.userId }}: <template v-for="video in remote.video"> {{video.videoType}}-{{ video.codec }} </template>
          </n-tag>
        </template>
    </n-space>

  </div>
</template>

<script setup lang="ts">
import { NSpace, NTag, NSelect } from "naive-ui";
import { computed } from "vue";
import type { TagProps } from "naive-ui";
import { useVersionStore } from "../stores/version";
import { useStatisticsStore } from "../stores/statistics";
import { storeToRefs } from "pinia";

const versionStore = useVersionStore();
const statisticsStore = useStatisticsStore();
const { currentVersion, versionList } = storeToRefs(versionStore);
const { stats } = storeToRefs(statisticsStore);

const networkQualityType = computed<TagProps["type"]>(() => {
  const rtt = stats.value.rtt;
  const upLoss = stats.value.upLoss;
  const downLoss = stats.value.downLoss;

  if (rtt > 400 || upLoss > 5 || downLoss > 5) {
    return "error";
  } else if (rtt > 200 || upLoss > 2 || downLoss > 2) {
    return "warning";
  }
  return "success";
});

const versionOptions = computed(() => {
  return versionList.value.map((version) => ({
    label: version,
    value: version,
  }));
});

const handleVersionChange = (version: string) => {
  versionStore.changeVersion(version);
};

const formatBytes = (bytes: number) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
};
</script>

<style scoped>
.status-bar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.stat-tag {
  min-width: 80px;
  text-align: center;
}

.stat-tag :deep(.n-tag__content) {
  white-space: nowrap;
}

.macro-combo-card-container {
  position: relative;
}
</style>
