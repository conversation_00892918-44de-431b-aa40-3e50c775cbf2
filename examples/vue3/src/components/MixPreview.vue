<template>
    <div class="local-mix-preview">
        <div class="video-container" :id='currentInstanceId + "-mix-video"' ref="videoRef"></div>
        <!-- User ID display -->
        <div class="user-id">
            mixPreview: {{ userName }}
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRoomStore } from "../stores/room";
import { useMediaStore } from "../stores/media";
import { storeToRefs } from "pinia";

const roomStore = useRoomStore();
const mediaStore = useMediaStore()
const { userName } = storeToRefs(roomStore);
const videoRef = ref<HTMLDivElement>();

const { currentInstanceId } = storeToRefs(mediaStore)

</script>

<style scoped>
.local-mix-preview {
    position: relative;
    /* width: 320px; */
    height: 240px;
    background: #000;
    overflow: hidden;
}

.video-container {
    width: 100%;
    height: 100%;
}

.user-id {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}
</style>