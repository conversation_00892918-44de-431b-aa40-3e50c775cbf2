<template>
  <div class="macro-tab">
    <n-space vertical>
      <n-space justify="space-between">
        <n-space>
          <n-text style="font-size: 20px;font-weight: bold;">宏列表</n-text>
          <n-select v-model:value="selectedAutoRunMacro" :options="macroOptions" placeholder="选择启动时执行的宏"
            style="min-width: 180px" />
        </n-space>
        <n-space>
          <n-button :type="isRecording ? 'error' : 'primary'" @click="handleRecordClick" id="record-btn">
            {{ isRecording ? "结束" : "录制" }}
          </n-button>
          <n-button @click="isShowMacroDialog = true">上传</n-button>
        </n-space>
      </n-space>
    </n-space>

    <n-modal v-model:show="isShowMacroDialog" preset="dialog" title="宏指令JSON" positive-text="确认" negative-text="取消"
      @positive-click="parseMacro" @negative-click="isShowMacroDialog = false" id="macro-json-input">
      <n-input v-model:value="macroJson" type="textarea" placeholder="请输入JSON"
        :autosize="{ minRows: 10, maxRows: 30 }" />
      <div style="margin-top: 12px;">
        <n-button size="small" @click="formatJson">
          格式化JSON
        </n-button>
      </div>
    </n-modal>

    <n-modal v-model:show="showNameInput" preset="dialog" title="宏名称" positive-text="确认" negative-text="取消"
      @positive-click="handleConfirmMacroName" @negative-click="handleCancelMacroName" id="macro-name-input">
      <n-input v-model:value="macroName" placeholder="请输入宏名称" />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { storeToRefs } from "pinia";
import { useMacroStore } from "@/stores/macro";
import { useMessage } from "naive-ui";
import { globalMacroState } from "@/global/globalState.ts";
import { resetStateStores } from '@/utils/resetStores'

const message = useMessage();
const macroStore = useMacroStore();

const showNameInput = ref(false);
const macroName = ref("");
const isShowMacroDialog = ref(false)
const macroJson = ref('')
const selectedAutoRunMacro = ref<string | null>(null);

const {
  isRecording,
  macros,
  currentMacroName: storeMacroName,
} = storeToRefs(macroStore);

watch(selectedAutoRunMacro, (newValue: string | null) => {
  macroStore.setAutoRunMacro(newValue);
});

// 在组件挂载时初始化
onMounted(async () => {
  await macroStore.initDB();
  await macroStore.loadMacros();
  selectedAutoRunMacro.value = macroStore.autoRunMacro;
});

const macroList = computed(() => macros.value);

const macroOptions = computed(() => [
  { label: "不执行任何宏", value: null },
  ...macroList.value.map((macro) => ({
    label: macro.name,
    value: macro.id,
  })),
]);

const formatJson = () => {
  try {
    const parsedJson = JSON.parse(macroJson.value);
    macroJson.value = JSON.stringify(parsedJson, null, 2);
  } catch (error) {
    message?.error('无效的JSON格式');
  }
};

function handleRecordClick() {
  if (isRecording.value) {
    console.log(
      "Stopping recording, current recording:",
      macroStore.currentRecording,
      "store name:",
      storeMacroName.value
    );
    if (macroStore.currentRecording.length > 0) {
      if (!storeMacroName.value) {
        console.error("Macro name is empty when stopping recording");
        message.error("宏名称丢失，请重新录制");
        macroStore.stopRecording();
        isRecording.value = false;
        storeMacroName.value = "";
        return;
      }
      finishRecording();
    } else {
      console.log("No recorded actions");
      message.warning("没有录制到任何操作");
      macroStore.stopRecording();
      isRecording.value = false;
      storeMacroName.value = "";
    }
  } else {
    console.log("Starting recording");
    if (globalMacroState.isRecording) {
      macroName.value = globalMacroState.macroName
      handleConfirmMacroName()
      return
    }
    showNameInput.value = true;
  }
}

async function handleConfirmMacroName() {
  if (!macroName.value) {
    message.error("请输入宏名称");
    return false;
  }

  if (macros.value.some(macro => macro.name === `${macroName.value}`)) {
    message.error("宏指令已存在");
    return false;
  }
  const name = macroName.value;
  macroName.value = "";
  showNameInput.value = false;

  // 设置名称
  storeMacroName.value = name;

  // 确认名称已经设置
  console.log(
    "Confirmed macro name before recording:",
    "store:",
    storeMacroName.value
  );

  await resetStateStores()
  await nextTick()

  // 开始录制
  macroStore.startRecording();
  message.success(`开始录制宏: ${name}`);
}

async function finishRecording() {
  console.log("Finishing recording with name:", storeMacroName.value);
  try {
    // 先停止录制
    macroStore.stopRecording();
    isRecording.value = false;

    // 然后创建宏
    await createMacro();

    // 等待宏列表更新
    await macroStore.loadMacros();
    message.success(`宏"${storeMacroName.value}"创建成功`);

    // 最后清理状态
    storeMacroName.value = "";
  } catch (error) {
    console.error("Failed to finish recording:", error);
    // 如果失败，也需要清理状态
    macroStore.stopRecording();
    isRecording.value = false;
    storeMacroName.value = "";
  }
}

async function createMacro() {
  console.log("Creating macro:", {
    name: storeMacroName.value,
    actions: macroStore.currentRecording,
  });

  if (!storeMacroName.value) {
    throw new Error("宏名称不能为空");
  }

  try {
    const name = storeMacroName.value;
    await macroStore.createMacro(`${name}`, macroStore.currentRecording);
  } catch (error) {
    console.error("Failed to create macro:", error);
    message.error("宏创建失败：" + (error as Error).message);
    throw error;
  }
}

function handleCancelMacroName() {
  macroName.value = "";
  showNameInput.value = false;
  if (isRecording.value) {
    macroStore.stopRecording();
    isRecording.value = false;
    storeMacroName.value = "";
  }
}

async function parseMacro() {
  macroStore.currentRecording.length = 0;
  try {
    const json = JSON.parse(macroJson.value)
    const macroName = Object.keys(json)[0]
    if (macros.value.some(macro => macro.name === macroName)) {
      message.error("宏指令已存在");
      return false;
    }
    const actions = json[macroName]
    for (const action of actions) {
      const method = Object.keys(action)[0];
      const args = action[method];
      macroStore.isRecording = true;
      macroStore.recordAction(method, args, action['timestamp']);
    }
    macroStore.isRecording = false;
    await macroStore.createMacro(`${macroName}`, macroStore.currentRecording);
  }
  catch (error) {
    message.error("宏解析失败：" + (error as Error).message);
  }
  macroJson.value = "";
}
</script>

<style scoped>
.macro-tab {
  padding: 16px;
  min-width: 400px;
}

.macro-actions {
  font-size: 12px;
  color: var(--n-text-color-2);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  padding: 4px 0;
}

.macro-actions-tooltip {
  max-width: 400px;
  font-size: 12px;
  line-height: 1.6;
}

.macro-actions-tooltip>div {
  padding: 2px 0;
}

.macro-actions-tooltip>div:not(:last-child) {
  border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
}
</style>
