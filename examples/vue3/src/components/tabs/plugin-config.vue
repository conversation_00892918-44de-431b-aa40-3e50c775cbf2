<template>
  <div class="ribbon-group">
    <div class="ribbon-group-content no-gap">
      <div class="control-buttons">
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button class="control-button" :type="showMacroSettings ? 'primary' : 'default'"
              @click="showMacroSettings = !showMacroSettings" :id="currentInstanceId + '-macro-settings'">
              <template #icon>
                <n-icon>
                  <Code />
                </n-icon>
              </template>
            </n-button>
          </template>
          宏指令
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button tertiary @click="showModal = true" class="control-button">
              <template #icon>
                <n-icon size="24">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor"
                      d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7s2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z" />
                  </svg>
                </n-icon>
              </template>
            </n-button>
          </template>
          插件
        </n-tooltip>
      </div>
    </div>
  </div>
  <n-modal v-model:show="showModal" preset="dialog" title="插件配置" style="width: auto;">
    <n-tabs type="line" animated>
      <n-tab-pane name="basic" tab="视频效果">
        <div class="tab-content"><n-space>
            <n-space vertical>
              <!-- 美颜设置 -->
              <div class="feature-group">
                <n-checkbox v-model:checked="pluginFeatures.beauty">美颜</n-checkbox>
                <div v-if="pluginFeatures.beauty" class="beauty-sliders">
                  <div class="slider-item">
                    <span>美颜程度</span>
                    <n-slider v-model:value="beautyValues.beauty" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="slider-item">
                    <span>明亮程度</span>
                    <n-slider v-model:value="beautyValues.brightness" :min="0" :max="1" :step="0.1" />
                  </div>
                  <div class="slider-item">
                    <span>红润程度</span>
                    <n-slider v-model:value="beautyValues.ruddy" :min="0" :max="1" :step="0.1" />
                  </div>
                </div>
              </div>

              <!-- 高级美颜 -->
              <n-checkbox v-model:checked="pluginFeatures.advancedBeauty">高级美颜</n-checkbox>

              <!-- 虚拟背景 -->
              <div class="feature-group">
                <span>虚拟背景</span>
                <n-radio-group v-model:value="virtualBackground" name="virtualBackground">
                  <n-space>
                    <n-radio value="normal">正常</n-radio>
                    <n-radio value="blur">虚化</n-radio>
                    <n-radio value="image">图片</n-radio>
                  </n-space>
                </n-radio-group>
                <!-- 虚化程度滑块，仅在选择虚化时显示 -->
                <div v-if="virtualBackground === 'blur'" class="blur-slider">
                  <div class="slider-item">
                    <span>虚化程度</span>
                    <n-slider v-model:value="blurLevel" :min="1" :max="10" :step="1" />
                  </div>
                </div>
                <!-- 人脸居中 -->
                <div v-if="virtualBackground === 'blur' || virtualBackground === 'image'" class="feature-group">
                  <n-space align="center">
                    <n-checkbox v-model:checked="pluginFeatures.enableFaceCentering">人脸居中</n-checkbox>
                  </n-space>
                </div>
              </div>

              <!-- 水印设置 -->
              <div class="feature-group">
                <n-space align="center">
                  <n-checkbox v-model:checked="pluginFeatures.watermark">水印</n-checkbox>
                  <n-button size="small" @click="handleUpdateWatermark">更新水印</n-button>
                </n-space>
              </div>
            </n-space>
            <image-container style="width: 500px; height: 500px; display: none" src="./trtc-watermark-test.png" />
          </n-space>
        </div>
      </n-tab-pane>
      <n-tab-pane name="encryption" tab="加密模块">
        <div class="tab-content">
          <div class="ribbon-group">
            <div class="ribbon-group-content">
              <n-space vertical>
                <div class="encryption-header">
                  <span>开启</span>
                  <n-switch v-model:value="encryptionConfig.enabled" />
                </div>
                <n-select v-model:value="encryptionConfig.algorithm" :options="algorithmOptions" placeholder="选择加密算法" />
                <n-input v-model:value="encryptionConfig.key" placeholder="输入加密密钥" />
                <n-input v-model:value="encryptionConfig.salt" placeholder="输入加密盐值" />
              </n-space>
            </div>
          </div>
        </div>
      </n-tab-pane>
      <n-tab-pane name="device-test" tab="设备检测">
        <div class="tab-content">
          <div class="ribbon-group">
            <div class="ribbon-group-content">
              <n-checkbox v-model:checked="pluginFeatures.isAllowSkipDeviceCheck">是否允许跳过</n-checkbox>
              <n-button @click="startDeviceCheck">开启</n-button>
              <n-button @click="startDeviceCheckWithNetwork">开启(包括网络检测)</n-button>
            </div>
          </div>
        </div>
      </n-tab-pane>
      <n-tab-pane name="videomix" tab="合流插件">
        <div class="tab-content">
          <n-space vertical>
            <n-space align="center">
              合流画布宽高 <n-input-number v-model:value="canvasInfo.width" size="small" :style="{ width: '60px' }"
                :show-button="false" />x
              <n-input-number v-model:value="canvasInfo.height" size="small" :style="{ width: '60px' }"
                :show-button="false" />
              背景色 <n-color-picker v-model:value="canvasInfo.canvasColor" size="small" :style="{ width: '100px' }" />
            </n-space>
            <n-space align="center">
              <n-button size="small" :type="pluginFeatures.videoMix ? 'primary' : 'default'" @click="toggleMixPreview">
                {{ pluginFeatures.videoMix ? "关闭合流" : "开启合流" }}
              </n-button>
              <n-button size="small" @click="updateVideoMixer">更新合流</n-button>
              <n-checkbox v-model:checked="simulateDrag">模拟拖动</n-checkbox>
              <n-checkbox v-model:checked="publishMix">推合流</n-checkbox>
            </n-space>
            <n-checkbox v-model:checked="mixCamera">摄像头</n-checkbox>
            <n-space align="center">
              <n-form-item label="x">
                <n-input-number v-model:value="cameraOption.layout.x" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="y">
                <n-input-number v-model:value="cameraOption.layout.y" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="width">
                <n-input-number v-model:value="cameraOption.layout.width" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="height">
                <n-input-number v-model:value="cameraOption.layout.height" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="zIndex">
                <n-input-number v-model:value="cameraOption.layout.zIndex" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="fillMode">
                <n-select v-model:value="cameraOption.layout.fillMode" :options="fillModeOptions"
                  :style="{ width: '100px' }" />
              </n-form-item>
              <n-form-item label="rotation">
                <n-select v-model:value="cameraOption.layout.rotation" :options="rotationOptions"
                  :style="{ width: '100px' }" />
              </n-form-item>
              <n-checkbox v-model:checked="cameraOption.layout.mirror">镜像</n-checkbox>
            </n-space>

            <n-checkbox v-model:checked="mixScreen">屏幕</n-checkbox>
            <n-space align="center">
              <n-form-item label="x">
                <n-input-number v-model:value="screenOption.layout.x" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="y">
                <n-input-number v-model:value="screenOption.layout.y" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="width">
                <n-input-number v-model:value="screenOption.layout.width" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="height">
                <n-input-number v-model:value="screenOption.layout.height" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="zIndex">
                <n-input-number v-model:value="screenOption.layout.zIndex" size="small" :style="{ width: '60px' }"
                  :show-button="false" />
              </n-form-item>
              <n-form-item label="fillMode">
                <n-select v-model:value="screenOption.layout.fillMode" :options="fillModeOptions"
                  :style="{ width: '100px' }" />
              </n-form-item>
              <n-form-item label="rotation">
                <n-select v-model:value="screenOption.layout.rotation" :options="rotationOptions"
                  :style="{ width: '100px' }" />
              </n-form-item>
              <n-checkbox v-model:checked="screenOption.layout.mirror">镜像</n-checkbox>
            </n-space>
          </n-space>
          <n-space>
            <n-checkbox v-model:checked="mixText1">文字1</n-checkbox>
            <n-checkbox v-model:checked="mixText2">文字2</n-checkbox>
            <n-checkbox v-model:checked="mixImage1">图片1</n-checkbox>
            <n-checkbox v-model:checked="mixImage2">图片2</n-checkbox>
            <n-checkbox v-model:checked="mixVideo1">视频1</n-checkbox>
            <n-checkbox v-model:checked="mixVideo2">视频2</n-checkbox>
          </n-space>
        </div>
      </n-tab-pane>
    </n-tabs>
  </n-modal>
  <MacroComboSettings v-if="currentInstanceId === 'TRTC-00'" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'
import {
  NButton,
  NModal,
  NTabs,
  NTabPane,
  NIcon,
  NSpace,
  NCheckbox,
  NSelect,
  NInput,
  NSwitch,
  NSlider,
  NRadioGroup,
  NRadio,
  NInputNumber,
  NColorPicker,
  useMessage
} from 'naive-ui'
import {
  Code
} from "@vicons/ionicons5";
import { useRoomStore } from "@/stores/room"
import { useMediaStore } from "@/stores/media"
import { useSettingsStore } from "@/stores/settings"
import { storeToRefs } from "pinia"
import ImageContainer from "../ImageContainer.vue"
import { useMacroStore } from "@/stores/macro";
import MacroComboSettings from '../MacroComboSettings.vue'
import { macroSettings } from '@/global/globalState'
import TRTC from "trtc-sdk-v5"
const settingsStore = useSettingsStore()
const mediaStore = useMediaStore()
const { pluginFeatures, virtualBackground, blurLevel, beautyValues } = storeToRefs(settingsStore)
const { currentInstanceId } = storeToRefs(mediaStore)

const message = useMessage()
const showModal = ref(false)

const roomStore = useRoomStore();
const macroStore = useMacroStore();
const CUSTOM = "custom";

const { showMacroSettings } = storeToRefs(macroStore)

macroSettings.push(showMacroSettings)

// 加密配置
const encryptionConfig = ref({
  enabled: false,
  algorithm: "AES-GCM",
  key: "",
  salt: "",
});

// 加密算法选项
const algorithmOptions = [
  {
    label: "AES-GCM",
    value: "AES-GCM",
  },
  {
    label: "SM4-CBC",
    value: "SM4-CBC",
  },
  {
    label: CUSTOM,
    value: CUSTOM,
  },
];

// 设置美颜效果
const setBeautyEffect = async () => {
  if (!pluginFeatures.value.beauty) return;
  try {
    await roomStore.updatePlugin('BasicBeauty', beautyValues.value);
  } catch (error) {
    console.error('设置美颜效果失败:', error);
    message.error('设置美颜效果失败');
  }
};


// 更新水印处理函数
const handleUpdateWatermark = () => {
  try {
    roomStore.updatePlugin('Watermark', {
      imageUrl: './test.png',
    });
  } catch (error) {
    console.error('更新水印失败:', error);
    message.error('更新水印失败');
  }
}
// 设置虚化背景
const setBlurBackground = async (level: number) => {
  try {
    const auth = await roomStore.genTestUserSig()
    await roomStore.startPlugin('VirtualBackground', {
      ...auth,
      type: 'blur',
      blurLevel: level
    })
    message.success('虚化背景设置成功')
  } catch (error) {
    console.error('设置虚化背景失败:', error)
    message.error('设置虚化背景失败')
  }
}

// 关闭虚拟背景
const disableVirtualBackground = async () => {
  try {
    await roomStore.stopPlugin('VirtualBackground')
  } catch (error) {
    console.error('关闭虚拟背景失败:', error)
  }
}

async function startDeviceCheck() {
  try {
    await roomStore.startPlugin('DeviceDetector', {
      allowSkip: pluginFeatures.value.isAllowSkipDeviceCheck
    })
  } catch (error) {
    console.error('开启设备检测失败', error);
    message.error('开启设备检测失败')
  }
}

async function startDeviceCheckWithNetwork() {
  try {
    const downlinkUser = await roomStore.genTestUserSig();
    await roomStore.startPlugin('DeviceDetector', {
      allowSkip: pluginFeatures.value.isAllowSkipDeviceCheck,
      networkDetect: {
        ...await roomStore.genTestUserSig(),
        downlinkUserId: downlinkUser.userId,
        downlinkUserSig: downlinkUser.userSig,
      }
    })
  } catch (error) {
    console.error('开启设备检测失败', error);
    message.error('开启设备检测失败')
  }
}

// 从 localStorage 加载配置
onMounted(() => {
  const savedConfig = localStorage.getItem("encryptionConfig");
  if (savedConfig) {
    encryptionConfig.value = JSON.parse(savedConfig);
  }
});

// 监听加密开关状态
watch(
  () => encryptionConfig.value.enabled,
  (newVal) => {
    if (macroStore.isExecute) return
    if (newVal) {
      roomStore.startPlugin(
        "CustomEncryption",
        encryptionConfig.value.algorithm === CUSTOM
          ? {
            mode: TRTC.frameWorkType === 37 ? "udt3" : "webrtc",
            video: true,
            customCryptors: {
              encryptor: (data: Uint8Array) => {
                return data;
              },
              decryptor: (data: Uint8Array) => {
                return data;
              },
            },
          }
          : {
            mode: TRTC.frameWorkType === 37 ? "udt3" : "webrtc",
            video: true,
            builtinOptions: {
              algorithm: encryptionConfig.value.algorithm,
              secretKey: new TextEncoder().encode(encryptionConfig.value.key),
              salt: new TextEncoder().encode(encryptionConfig.value.salt),
            },
          }
      );
    } else {
      roomStore.stopPlugin("CustomEncryption");
    }
  }
);

// 监听美颜开关变化
watch(
  () => pluginFeatures.value.beauty,
  (newVal) => {
    if (macroStore.isExecute) return;
    if (!newVal) {
      roomStore.stopPlugin('BasicBeauty');
    } else {
      roomStore.startPlugin('BasicBeauty', beautyValues.value);
    }
  }
);

// 监听美颜参数变化
watch(
  beautyValues,
  () => {
    if (macroStore.isExecute) return
    if (pluginFeatures.value.beauty) {
      setBeautyEffect();
    }
  },
  { deep: true }
);

// 监听虚拟背景变化
watch(
  virtualBackground,
  async (newVal, oldVal) => {
    if (macroStore.isExecute) return
    if (oldVal !== 'normal') {
      await disableVirtualBackground()
    }
    if (newVal === 'blur') {
      await setBlurBackground(blurLevel.value)
    } else if (newVal === 'image') {
      const auth = await roomStore.genTestUserSig()
      await roomStore.startPlugin('VirtualBackground', {
        ...auth,
        type: 'image',
        src: './vb.jpg'
      })
    }
  }
);

// 监听虚化程度变化
watch(
  blurLevel,
  async (newVal) => {
    if (macroStore.isExecute) return
    if (virtualBackground.value === 'blur') {
      await roomStore.updatePlugin('VirtualBackground', {
        type: 'blur',
        blurLevel: newVal
      })
    }
  }
);

watch(() => pluginFeatures.value.enableFaceCentering, async (newVal) => {
  if (macroStore.isExecute) return
  if (virtualBackground.value === 'blur') {
    await roomStore.updatePlugin('VirtualBackground', {
      type: virtualBackground.value,
      enableFaceCentering: newVal,
      blurLevel: blurLevel.value
    })
  }
  else if (virtualBackground.value === 'image') {
    const auth = await roomStore.genTestUserSig()
    await roomStore.updatePlugin('VirtualBackground', {
      type: virtualBackground.value,
      enableFaceCentering: newVal,
      src: './vb.jpg',
      ...auth,
    })
  }
});

watch(
  () => pluginFeatures.value.watermark, (newVal) => {
    if (macroStore.isExecute) return
    newVal ? roomStore.startPlugin('Watermark', {
      imageUrl: './trtc-watermark-test.png'
    }) : roomStore.stopPlugin('Watermark')
  }
)

// 合流插件
const rotationOptions = [0, 90, 180, 270].map((item) => ({ label: item, value: item }));
const fillModeOptions = ['cover', 'contain', 'fill'].map((item) => ({ label: item, value: item }));
const canvasInfo = ref({
  canvasColor: '#18A058',
  width: 1920,
  height: 1080
})
const mixCamera = ref(false);
const mixScreen = ref(false);
const mixText1 = ref(false);
const mixText2 = ref(false);
const mixImage1 = ref(false);
const mixImage2 = ref(false);
const mixVideo1 = ref(false);
const mixVideo2 = ref(false);
const cameraOption = ref({
  id: 'camera1',
  profile: { width: 1280, height: 720, frameRate: 30 },
  layout: {
    x: 0,
    y: 0,
    width: 640,
    height: 480,
    zIndex: 1,
    fillMode: 'cover',
    mirror: false,
    rotation: 0,
  },
});
const screenOption = ref({
  id: 'screen1',
  profile: { width: 1920, height: 1080, frameRate: 15 },
  layout: {
    x: 0,
    y: 0,
    width: 1920,
    height: 1080,
    zIndex: 0,
    fillMode: 'contain',
    mirror: false,
    rotation: 0,
  },
});
const textOption1 = ref({
  id: 'text1',
  content: '众🀄️AQY',
  font: 'bold 60px SimHei',
  color: 'red',
  layout: {
    x: 200,
    y: 200,
    width: 600,
    height: 150,
    zIndex: 6,
    mirror: true,
  },
})
const textOption2 = ref({
  id: 'text2',
  content: 'MultiLine\nEMOJI🥳',
  font: 'bold 60px Georgia',
  color: 'rgb(255 0 153 / 80%)',
  layout: {
    x: 0,
    y: 500,
    width: 300,
    height: 150,
    zIndex: 7,
    rotation: 90,
  },
})
const imageOption1 = ref({
  id: 'img1',
  url: './tv2.png',
  layout: {
    x: 0,
    y: 500,
    width: 800,
    height: 400,
    zIndex: 4,
    fillMode: 'fill',
    rotation: 90,
  },
})
const imageOption2 = ref({
  id: 'img2',
  url: './mute.png',
  layout: {
    x: 500,
    y: 100,
    width: 500,
    height: 400,
    zIndex: 5,
  },
})
const videoOption1 = ref({
  id: 'video1',
  url: 'https://webrtc-1252463788.cos-website.ap-guangzhou.myqcloud.com/test/jialelin/rtc/video_1920_1080_24fps.mp4',
  // url: './video_1920_1080_24fps.mp4',
  layout: {
    x: 500,
    y: 200,
    width: 1000,
    height: 500,
    zIndex: 10,
    rotation: 90,
  },
})
const videoOption2 = ref({
  id: 'video2',
  url: 'https://webrtc-1252463788.cos-website.ap-guangzhou.myqcloud.com/test/jialelin/rtc/video_1280_720_30fps.mp4',
  // url: './video_1280_720_30fps.mp4',
  layout: {
    x: 500,
    y: 300,
    width: 1000,
    height: 720,
    zIndex: 11,
    fillMode: 'fill',
    mirror: true,
  },
})
const mixCameraOptions = computed(() => {
  return mixCamera.value ? [cameraOption.value] : [];
});
const mixScreenOptions = computed(() => {
  return mixScreen.value ? [screenOption.value] : [];
});
const mixTextOptions = computed(() => {
  const text = [];
  if (mixText1.value) text.push(textOption1.value);
  if (mixText2.value) text.push(textOption2.value);
  return text;
});
const mixImageOptions = computed(() => {
  const image = [];
  if (mixImage1.value) image.push(imageOption1.value);
  if (mixImage2.value) image.push(imageOption2.value);
  return image;
});
const mixVideoOptions = computed(() => {
  const video = [];
  if (mixVideo1.value) video.push(videoOption1.value);
  if (mixVideo2.value) video.push(videoOption2.value);
  return video;
});
let mixVideoTrack: MediaStreamTrack | null = null;
const toggleMixPreview = async () => {
  pluginFeatures.value.videoMix = !pluginFeatures.value.videoMix;
  mediaStore.setMixPreview(pluginFeatures.value.videoMix)
  if (pluginFeatures.value.videoMix) {
    await nextTick();  // 等 mixPreview div 添加到 dom 中
    try {
      const { track, result } = await roomStore.startPlugin('VideoMixer', {
        view: `${currentInstanceId.value}-mix-video`,
        canvasInfo: {
          ...canvasInfo.value,
          // frameRate: 10
        },
        camera: mixCameraOptions.value,
        screen: mixScreenOptions.value,
        text: mixTextOptions.value,
        image: mixImageOptions.value,
        video: mixVideoOptions.value,
        onScreenShareStop: (id: string) => {
          console.log(`screen share stop, id: ${id}`);
        }
      });
      mixVideoTrack = track;
      console.log('mix result: ', result)
      result.failedDetails.forEach(({ id, error }: { id: string, error: any }) => {
        console.error(`${id} mix failed`, error);
        message.error(error.message);
      })
    } catch (error: any) {
      console.error(error)
      message.error(error.message);
      error?.data?.failedDetails.forEach(({ id, error }: { id: string, error: any }) => {
        console.error(`${id} mix failed`, error);
        message.error(error.message);
      })
    }
  } else {
    clearInterval(vmIntervalId);
    roomStore.stopPlugin('VideoMixer');
    mixVideoTrack = null;
  }
}
const publishMix = ref(false);
watch(publishMix, async (newVal) => {
  if (macroStore.isExecute) return;
  if (newVal) {
    if (!mixVideoTrack) {
      console.error('no mix Track, publish failed');
      return;
    }
    roomStore.publishMix(mixVideoTrack, {
      width: 1920,
      height: 1080,
      bitrate: 2000,
      frameRate: 10 // 不会起作用
    });
  } else {
    roomStore.stopLocalVideo()
  }
});

const updateVideoMixer = async () => {
  if (macroStore.isExecute) return;
  try {
    const { result } = await roomStore.updatePlugin('VideoMixer', {
      canvasInfo: {
        ...canvasInfo.value,
        // frameRate: 10
      },
      camera: mixCameraOptions.value,
      screen: mixScreenOptions.value,
      text: mixTextOptions.value,
      image: mixImageOptions.value,
      video: mixVideoOptions.value,
    });
    console.log('mix result: ', result)
    result.failedDetails.forEach(({ id, error }: { id: string, error: any }) => {
      console.error(`${id} mix failed`, error);
      message.error(error.message);
    })
  } catch (error: any) {
    console.error(error)
    message.error(error.message);
    error?.data?.failedDetails.forEach(({ id, error }: { id: string, error: any }) => {
      console.error(`${id} mix failed`, error);
      message.error(error.message);
    })
  }

};
let vmIntervalId: NodeJS.Timeout | undefined;
const simulateDrag = ref(false);
watch(simulateDrag, async (newVal) => {
  if (macroStore.isExecute) return;
  clearInterval(vmIntervalId);
  if (newVal) {
    if (!pluginFeatures.value.videoMix) {
      message.error('未开启合流！');
      return;
    }
    let x = 0;
    let y = 0;
    const mixParams: any = {
      camera: mixCameraOptions.value,
      screen: mixScreenOptions.value,
      text: mixTextOptions.value,
      image: mixImageOptions.value,
      video: mixVideoOptions.value,
    }
    await roomStore.updatePlugin('VideoMixer', mixParams); // 先更新一次，加载首次出现的资源

    vmIntervalId = setInterval(() => {
      x += 8;
      y += 6;
      if (x > 1920 || y > 1080) {
        x = 0;
        y = 0;
      }
      cameraOption.value.layout.x = x;
      cameraOption.value.layout.y = y;
      screenOption.value.layout.x = 0.5 * x;
      textOption1.value.layout.x = x * 2;
      textOption2.value.layout.x = x;
      textOption2.value.layout.y = y;
      imageOption1.value.layout.y = y;
      imageOption2.value.layout.x = x;
      imageOption2.value.layout.y = y;
      videoOption1.value.layout.x = x;
      videoOption2.value.layout.y = y;
      roomStore.updatePlugin('VideoMixer', mixParams);
    }, 50);
  } else {
  }
});
</script>

<style scoped>
.plugin-config {
  display: inline-block;
}

.tab-content {
  padding: 16px;
  min-height: 200px;
}

.ribbon-group {
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.encryption-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.feature-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.beauty-sliders {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.blur-slider {
  margin-left: 24px;
  margin-top: 8px;
}

.slider-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slider-item span {
  min-width: 60px;
}

.ribbon-group-content.no-gap {
  gap: 0;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.control-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}

.voice-changer-btn {
  margin-left: 10px;
}
</style>