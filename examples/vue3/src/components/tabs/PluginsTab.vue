<template>
  <div class="ribbon-section">
    <div class="ribbon-group">
      <div class="ribbon-group-content">
        <n-space vertical>
          <n-checkbox v-model:checked="pluginFeatures.beauty">美颜</n-checkbox>
          <n-checkbox v-model:checked="pluginFeatures.filter">滤镜</n-checkbox>
          <n-checkbox v-model:checked="pluginFeatures.greenScreen">绿幕</n-checkbox>
          <n-checkbox v-model:checked="pluginFeatures.watermark">水印</n-checkbox>
        </n-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NSpace, NCheckbox } from "naive-ui";
import { ref } from "vue";

// 插件功能
const pluginFeatures = ref({
  beauty: false,
  filter: false,
  greenScreen: false,
  watermark: false,
});
</script>

<style scoped>
.ribbon-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 16px 16px;
  background-color: var(--n-color);
}

.ribbon-group {
  min-width: 200px;
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>