<template>
  <div class="ribbon-group">
    <div class="ribbon-group-content no-gap">
      <div class="control-row">
        <div class="control-buttons">
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button :type="screenSharing ? 'primary' : 'default'" @click="toggleScreenSharing"
                class="control-button">
                <template #icon>
                  <n-icon>
                    <DesktopOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
            屏幕分享
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button @click="toggleScreenPublish" class="control-button"
                :type="screenPublished ? 'primary' : 'default'">
                <template #icon>
                  <n-icon>
                    <ShareSocialOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
            {{ screenPublished ? "停止发布" : "发布屏幕" }}
          </n-tooltip>
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button tertiary @click="showScreenConfig" class="control-button">
                <template #icon>
                  <n-icon>
                    <SettingsOutline />
                  </n-icon>
                </template>
              </n-button>
            </template>
            屏幕分享配置
          </n-tooltip>
        </div>
      </div>
    </div>
  </div>

  <!-- Screen sharing configuration modal -->
  <n-modal v-model:show="showModal" preset="dialog" title="屏幕分享配置" style="min-width: 400px;width: auto">
    <n-space vertical size="small">
      <n-form-item label="分辨率+帧率" size="small">
        <n-select v-model:value="screenProfile" :options="screenProfileOptions" placeholder="分辨率+帧率" size="small" />
      </n-form-item>
      <n-form-item label="编码策略" size="small">
        <n-select v-model:value="qosPreference" :options="qosPreferenceOptions" placeholder="编码策略" size="small" />
      </n-form-item>
      <n-form-item size="small">
        <n-checkbox v-model:checked="shareSystemAudio" size="small">分享系统声音</n-checkbox>
        <n-switch v-model:value="muteSystemAudio">
          <template #checked>muted</template>
          <template #unchecked>unmuted</template>
        </n-switch>
      </n-form-item>
      
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import {
  NSpace,
  NButton,
  NSelect,
  NIcon,
  NTooltip,
  NCheckbox,
  NModal,
  NFormItem,
} from "naive-ui";
import {
  DesktopOutline,
  ShareSocialOutline,
  SettingsOutline,
} from "@vicons/ionicons5";
import { useMediaStore } from "../../stores/media";
import { storeToRefs } from "pinia";
import { ref } from "vue";
import { useNotification } from "naive-ui";
const notification = useNotification();
type NotificationParams = {
  title: string;
  content: string;
  duration?: number;
};

const mediaStore = useMediaStore();

// 从 mediaStore 获取状态
const {
  screenSharing,
  screenPublished,
  shareSystemAudio,
  muteSystemAudio,
  qosPreference,
  qosPreferenceOptions,
  screenProfile,
  screenProfileOptions,
} = storeToRefs(mediaStore);

// Modal control
const showModal = ref(false);
const showScreenConfig = () => {
  showModal.value = true;
};

const toggleScreenSharing = async () => {
  const res = await mediaStore.toggleScreenSharing();
  if (!res) return;
  if (res[0] === 'Success') {
    notification.success(res[1] as NotificationParams)
  }
  else if (res[0] === 'Error') {
    notification.error(res[1] as NotificationParams)
  }
};

const toggleScreenPublish = () => {
  mediaStore.toggleScreenPublish();
};
</script>

<style scoped>
.ribbon-group {
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ribbon-group-content.no-gap {
  gap: 0;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.control-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}

.mt-8 {
  margin-top: 8px;
}
</style>
