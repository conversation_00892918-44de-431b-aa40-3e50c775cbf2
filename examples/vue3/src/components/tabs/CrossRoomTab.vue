<template>
  <div class="ribbon-section">
    <div class="ribbon-group">
      <div class="ribbon-group-content">
        <n-space vertical>
          <n-input-group>
            <n-input
              v-model:value="crossRoomConfig.roomId"
              placeholder="目标房间号"
            />
            <n-input
              v-model:value="crossRoomConfig.userId"
              placeholder="目标用户ID"
            />
          </n-input-group>
          <n-select
            v-model:value="crossRoomConfig.role"
            :options="crossRoomRoleOptions"
            placeholder="跨房角色"
          />
          <n-space>
            <n-checkbox v-model:checked="crossRoomConfig.publishAudio"
              >发布音频</n-checkbox
            >
            <n-checkbox v-model:checked="crossRoomConfig.publishVideo"
              >发布视频</n-checkbox
            >
          </n-space>
        </n-space>
        <n-divider />
        <div class="ribbon-buttons">
          <n-tooltip trigger="hover">
            <template #trigger>
              <n-button
                :type="crossRoomConfig.connected ? 'primary' : 'default'"
                @click="toggleCrossRoom"
                class="ribbon-button"
              >
                <template #icon>
                  <n-icon><SwapHorizontalOutline /></n-icon>
                </template>
              </n-button>
            </template>
            {{ crossRoomConfig.connected ? "断开跨房" : "开始跨房" }}
          </n-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  NSpace,
  NInput,
  NButton,
  NSelect,
  NIcon,
  NInputGroup,
  NDivider,
  NTooltip,
  NCheckbox,
} from "naive-ui";
import { SwapHorizontalOutline } from "@vicons/ionicons5";
import { ref } from "vue";

// 跨房配置
const crossRoomConfig = ref({
  roomId: "",
  userId: "",
  role: "anchor",
  publishAudio: true,
  publishVideo: true,
  connected: false,
});

const crossRoomRoleOptions = [
  { label: "主播", value: "anchor" },
  { label: "观众", value: "audience" },
];

const toggleCrossRoom = () => {
  crossRoomConfig.value.connected = !crossRoomConfig.value.connected;
};
</script>

<style scoped>
.ribbon-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 16px 16px;
  background-color: var(--n-color);
}

.ribbon-group {
  min-width: 200px;
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ribbon-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.ribbon-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.ribbon-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}
</style>
