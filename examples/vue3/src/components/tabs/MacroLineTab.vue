<template>
    <div class="macro-tab">
        <n-space justify="space-between">
            <n-text style="font-size: 20px;font-weight: bold;">时间线</n-text>
            <n-space v-if="macroStore.currentRecording.length > 0">
                <n-button @click="revoke" v-if="macroStore.isRecording">撤销</n-button>
                <n-tooltip trigger="hover" placement="bottom">
                    <template #trigger>
                        <n-tag @click="executeNow">当前宏指令: {{ macroStore.currentMacroName }}</n-tag>
                    </template>
                    <div>
                        点击执行当前宏指令
                    </div>
                </n-tooltip>
            </n-space>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { useMacroStore } from "@/stores/macro";
import { defineEmits } from "vue";

const emit = defineEmits(['executeNow'])
const macroStore = useMacroStore();

function revoke() {
    macroStore.currentRecording.pop()
    macroStore.offsetTime = Date.now() - macroStore.currentRecording.at(-1)!.timestamp
}

function executeNow() {
    emit('executeNow');
}
</script>

<style scoped lang='scss'>
.macro-tab {
    padding: 16px;
}
</style>
