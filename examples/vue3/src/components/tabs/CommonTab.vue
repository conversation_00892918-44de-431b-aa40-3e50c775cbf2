<template>
  <div class="ribbon-section">
    <!-- Room Configuration -->
    <RoomConfig />

    <!-- Video Configuration -->
    <VideoConfig />

    <!-- Audio Configuration -->
    <AudioConfig />

    <!-- Screen Sharing Configuration -->
    <ScreenConfig v-if="!isMobile" />

    <!-- Plugin Configuration -->
    <PluginConfig />
    <n-tag style="margin-top:12px">ID:{{ currentInstanceId }}</n-tag>
  </div>
</template>

<script setup lang="ts">
import RoomConfig from "./room-config.vue";
import VideoConfig from "./video-config.vue";
import AudioConfig from "./audio-config.vue";
import ScreenConfig from "./screen-config.vue";
import PluginConfig from "./plugin-config.vue";
import { useMediaStore } from "@/stores/media";
import { storeToRefs } from 'pinia'
const mediaStore = useMediaStore()
const { currentInstanceId } = storeToRefs(mediaStore)
const isMobile: boolean =
  /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
</script>

<style scoped>
.ribbon-section {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px 16px 16px;
  background-color: var(--n-color);
  align-items: flex-start;
}
</style>