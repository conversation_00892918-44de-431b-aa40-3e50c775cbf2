<template>
  <div class="ribbon-group">
    <div class="ribbon-group-content no-gap">
      <div class="control-buttons">
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button :type="mediaStore.audioEnabled ? 'primary' : 'default'" @click="mediaStore.toggleAudio"
              class="control-button">
              <template #icon>
                <n-icon>
                  <MicOutline />
                </n-icon>
              </template>
            </n-button>
          </template>
          麦克风
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button @click="toggleAudioPublish" class="control-button" :type="audioPublished ? 'primary' : 'default'">
              <template #icon>
                <n-icon>
                  <ShareSocialOutline />
                </n-icon>
              </template>
            </n-button>
          </template>
          {{ audioPublished ? "停止发布" : "发布音频" }}
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button tertiary @click="showAudioConfig" class="control-button">
              <template #icon>
                <n-icon>
                  <SettingsOutline />
                </n-icon>
              </template>
            </n-button>
          </template>
          音频设置
        </n-tooltip>
        <n-tooltip trigger="hover">
          <template #trigger>
            <n-button :type="background ? 'primary' : 'default'" @click="mediaStore.toggleBackground"
              class="control-button">
              <template #icon>
                <n-icon>
                  <MusicalNotesOutline />
                </n-icon>
              </template>
            </n-button>
          </template>
          背景音
        </n-tooltip>
      </div>
    </div>
  </div>

  <!-- Audio configuration modal -->
  <n-modal v-model:show="showAudioModal" preset="dialog" title="音频设置" style="width: auto;min-width: 400px;">
    <n-space vertical size="small">
      <n-form-item label="麦克风" size="small">
        <n-select v-model:value="selectedMicrophone" :options="microphoneOptions" placeholder="选择麦克风" size="small" />
      </n-form-item>
      <n-form-item label="音频配置" size="small">
        <n-select v-model:value="audioProfile" :options="audioProfileOptions" placeholder="音频配置" size="small" />
      </n-form-item>
      <n-divider style="margin: 8px 0" />
      <n-form-item label="音量控制" size="small">
        <n-space vertical size="small" :style="{ margin: 0 }">
          <n-space align="center" justify="space-between" :style="{ margin: 0 }">
            <span class="volume-label">采集音量</span>
            <n-slider v-model:value="captureVolume" :min="0" :max="100" :step="1" :tooltip="false"
              style="width: 120px" />
            <span class="volume-value">{{ captureVolume }}%</span>
          </n-space>
          <n-space align="center" justify="space-between" :style="{ margin: 0 }">
            <span class="volume-label">耳返音量</span>
            <n-slider v-model:value="monitorVolume" :min="0" :max="100" :step="1" :tooltip="false"
              style="width: 120px" />
            <span class="volume-value">{{ monitorVolume }}%</span>
          </n-space>
        </n-space>
      </n-form-item>
      <n-divider style="margin: 8px 0" />
      <n-form-item label="音频处理" size="small">
        <n-space wrap class="audio-features" :style="{ padding: '4px 0', margin: 0 }">
          <n-checkbox v-model:checked="aec" size="small">AEC</n-checkbox>
          <n-checkbox v-model:checked="ans" size="small">ANS</n-checkbox>
          <n-checkbox v-model:checked="agc" size="small">AGC</n-checkbox>
          <n-checkbox v-model:checked="aiNoise" size="small">AI 降噪</n-checkbox>
        </n-space>
      </n-form-item>
      <n-divider style="margin: 8px 0" />
      <!-- 变声器设置 -->
      <div class="feature-group">
        <span>变声器</span>
        <n-space align="center">
          <n-select :value="voiceType" :options="voiceTypeOptions" placeholder="选择音色类型"
            @update:value="handleVoiceTypeChange" style="width: 150px;" />
        </n-space>
      </div>
    </n-space>
  </n-modal>
</template>

<script setup lang="ts">
import {
  NSpace,
  NButton,
  NSelect,
  NIcon,
  NTooltip,
  NCheckbox,
  NModal,
  NFormItem,
  NDivider,
  NSlider,
  useMessage
} from "naive-ui";
import {
  MicOutline,
  ShareSocialOutline,
  SettingsOutline,
  MusicalNotesOutline,
} from "@vicons/ionicons5";
import { useMediaStore } from "../../stores/media";
import { storeToRefs } from "pinia";
import { ref, watch } from "vue";
import { useRoomStore } from "@/stores/room"
import { useMacroStore } from "@/stores/macro";


const mediaStore = useMediaStore();
const roomStore = useRoomStore();
const message = useMessage()
const macroStore = useMacroStore();
const { voiceType } = storeToRefs(mediaStore)

// 从 mediaStore 获取状态
const {
  selectedMicrophone,
  audioProfile,
  audioPublished,
  microphoneOptions,
  audioProfileOptions,
  captureVolume,
  monitorVolume,
  aec,
  ans,
  agc,
  aiNoise,
  background,
} = storeToRefs(mediaStore);

// Modal control
const showAudioModal = ref(false);
const showAudioConfig = () => {
  showAudioModal.value = true;
};

const toggleAudioPublish = () => {
  mediaStore.toggleAudioPublish();
};

// 添加变声器音色选项
const voiceTypeOptions = [
  { label: "未选择", value: "0" },
  { label: "熊孩子", value: "1" },
  { label: "小黄人", value: "2" },
  { label: "大叔", value: "3" },
  { label: "重金属", value: "4" },
  { label: "感冒", value: "5" },
  { label: "外语腔", value: "6" },
  { label: "困兽", value: "7" },
  { label: "肥宅", value: "8" },
  { label: "强电流", value: "9" },
  { label: "重机械", value: "10" },
  { label: "空灵", value: "11" },
]

voiceType.value = voiceTypeOptions[0].value

// 处理音色类型变化
const handleVoiceTypeChange = async (value: string) => {
  if (voiceType.value === value) return
  if (!mediaStore.audioEnabled) {
    message.error('请先开启麦克风');
    return;
  }
  if (value === '0') await roomStore.stopPlugin('VoiceChanger', { ...(await roomStore.genTestUserSig()) });
  else {
    if (voiceType.value === '0') {
      await roomStore.startPlugin('VoiceChanger', {
        voiceType: Number(value),
        ...(await roomStore.genTestUserSig())
      });
    }
    else {
      await roomStore.updatePlugin('VoiceChanger', {
        voiceType: Number(value),
        ...(await roomStore.genTestUserSig())
      });
    }
  }
  voiceType.value = value;
}

watch(audioProfile, (newVal) => {
  if (macroStore.isExecute) return;
  roomStore.updateLocalAudio({
    option: {
      profile: newVal,
    }
  });
})
</script>

<style scoped>
.ribbon-group {
  max-width: 250px;
  background-color: var(--n-color);
  position: relative;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--n-border-color);
}

.ribbon-group-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ribbon-group-content.no-gap {
  gap: 0;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.control-button :deep(.n-button__icon) {
  font-size: 20px;
  margin: 0;
}

.volume-controls {
  padding: 0;
}

.volume-label {
  font-size: 12px;
  color: var(--n-text-color-2);
  width: 60px;
}

.volume-value {
  font-size: 12px;
  color: var(--n-text-color-2);
  width: 40px;
  text-align: right;
}

.audio-features {
  gap: 12px;
  width: 100%;
}

.audio-features :deep(.n-checkbox) {
  font-size: 12px;
  white-space: nowrap;
}
</style>
