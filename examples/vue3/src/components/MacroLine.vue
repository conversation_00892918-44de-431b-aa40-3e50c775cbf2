<template>
  <div class="timeline-container">
    <n-scrollbar y-scrollable trigger="none">
      <div class="timeline">
        <div v-for="(item, index) in items" :key="index" class="timeline-item"
          :class="{ 'active': activeIndex === index }" @click="handleItemClick(index)">
          <div v-if="index > 0" class="timeline-time-gap">
            +{{ formatTimeGap(items[index - 1].timestamp, item.timestamp) }}
          </div>

          <n-tooltip trigger="hover">
            <template #trigger>
              <div class="timeline-dot" @dblclick="handleDbClick(index)">{{ index + 1 }}</div>
            </template>
            双击删除
          </n-tooltip>

          <div class="timeline-content">
            <n-text class="timeline-title">{{ item.method }}</n-text>
            <div class="timeline-arg">{{ formatActionArgs(item.method, item.args) }}</div>
            <div class="timeline-description" v-show="activeIndex === index" @mouseup="selectText">
              <pre :key="preKey">{{ formatActionArgsForDisplay(item.args) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </n-scrollbar>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { NScrollbar, NText } from 'naive-ui';
import { serializeArgs, useMacroStore } from '../stores/macro'

const preKey = ref(0);
const macroStore = useMacroStore()

const selectText = () => {
  const selection = window.getSelection();
  if (selection.rangeCount === 0 || selection.isCollapsed) {
    return;
  }

  const range = selection.getRangeAt(0);
  const selectedText = range.toString();
  const input = document.createElement("input");
  input.value = selectedText;
  input.style.width = `${selectedText.length * 8}px`;
  input.style.outlineColor = '#18A058';

  range.deleteContents();
  range.insertNode(input);

  input.focus();
  input.select();

  input.addEventListener("blur", () => {
    replaceInputWithText(input);
  });

  input.addEventListener("keydown", (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      input.blur();
    }
  });

  function replaceInputWithText(inputElement) {
    const parent = inputElement.parentNode;
    const newText = inputElement.value;
    const textNode = document.createTextNode(newText);
    parent.replaceChild(textNode, inputElement);
    macroStore.currentRecording[activeIndex.value].args = JSON.parse(parent.textContent);
    inputElement.remove();
    preKey.value++;
  }
}

const props = defineProps({
  items: Array,
  initialActiveIndex: Number,
});

const activeIndex = ref(props.initialActiveIndex);

const handleItemClick = (index) => {
  activeIndex.value = index;
};

const formatActionArgs = (method, args) => {
  if (args.length === 0) return '无参数'
  if (method === 'startLocalVideo' || method === 'startScreenShare') {
    return args[0].view
  }
  if (method === 'updateLocalVideo' || method === 'updateLocalAudio') {
    return Object.keys(args[0]['option']).join(', ')
  }
  if (method === 'startPlugin' || method === 'updatePlugin') {
    return args[0]
  }
  if (method === 'enterRoom') {
    return `${args[0].userId} ${args[0].roomId}`
  }
}

const formatActionArgsForDisplay = (args) => {
  return args.length > 0 ? serializeArgs(args) : '';
}

const formatTimeGap = (prevTimestamp, currentTimestamp) => {
  const diffInSeconds = (currentTimestamp - prevTimestamp) / 1000;
  return `${diffInSeconds.toFixed(2)}s`;
}

const handleDbClick = (index) => {
  if (macroStore.isRecording) return
  macroStore.currentRecording.splice(index, 1);
}
</script>

<style scoped>
.timeline-container {
  padding: 12px 0;
  max-width: 620px;
}

.timeline {
  display: flex;
  flex-direction: column;
  min-width: auto;
  position: relative;
  padding: 12px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 38px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e8e8e8;
  z-index: 1;
}

.timeline-item {
  cursor: pointer;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  padding: 12px 0 12px 24px;
  margin-bottom: 16px;
  margin-left: 10px;
}

.timeline-dot {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #e8e8e8;
  margin: 0 auto 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  position: absolute;
  left: 18px;
  top: 16px;
}

.timeline-item.active .timeline-dot {
  border-color: rgb(24, 160, 88);
  transform: scale(1.2);
  color: rgb(24, 160, 88);
}

.timeline-content {
  display: flex;
  flex-direction: column;
  text-align: left;
  /* 文字左对齐 */
  padding-left: 24px;
  /* 左侧留出时间轴空间 */
}

.timeline-title {
  font-size: 14px;
  margin-top: 4px;
  color: #000;
  transition: color 0.3s ease;
  max-width: 100%;
}

.timeline-description {
  font-size: 12px;
  color: #666;
  transition: color 0.3s ease;
  max-width: 100%;
  word-wrap: break-word;
  white-space: normal;
  word-break: break-word;
  margin-top: 4px;
}

.timeline-arg {
  font-size: 12px;
  color: #000;
  transition: color 0.3s ease;
  max-width: 100%;
}

.timeline-item.active .timeline-arg {
  color: rgb(24, 160, 88);
  font-weight: 500;
}

.timeline-item.active .timeline-title {
  color: rgb(24, 160, 88);
  font-weight: 500;
}

/* 垂直滚动条样式 */
.timeline-container::-webkit-scrollbar {
  width: 6px;
  /* 改为垂直滚动条宽度 */
}

.timeline-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #c0c0c0;
}

/* 时间间隔样式 */
.timeline-time-gap {
  font-size: 12px;
  color: rgb(24, 100, 88);
  position: absolute;
  top: -18px;
  left: 38px;
}
</style>