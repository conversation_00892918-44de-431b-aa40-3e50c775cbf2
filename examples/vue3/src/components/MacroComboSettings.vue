<template>
    <n-modal v-model:show="showMacroComboModal" preset="dialog" title="宏组合管理" style="width: 600px">
        <n-tabs type="line" animated>
            <n-tab-pane name="macro" tab="宏组合">
                <div class="tab-content">
                    <n-space vertical>
                        <!-- 宏组合操作按钮 -->
                        <n-space>
                            <n-button size="small" :type="isRecording ? 'error' : 'primary'" @click="globalRecord">
                                {{ isRecording ? '结束' : '录制' }}
                            </n-button>
                            <n-button size="small" @click="uploadMacroCombo()">
                                上传
                            </n-button>
                            <n-button size="small" @click="handleMacroCombine()">
                                搭配
                            </n-button>
                        </n-space>

                        <!-- 宏组合列表 -->
                        <div style="display: flex;flex-direction: column;">
                            <n-card title="宏组合列表" style="width: 100%; margin-top: 10px;">
                                <n-empty v-if="macroCombos.length === 0" description="暂无宏组合" />
                                <n-list v-else style="max-height: 300px; overflow-y: scroll;">
                                    <n-list-item v-for="([comboName, combo], index) in macroCombos" :key="comboName">
                                        <n-space justify="space-between" size="small">
                                            <n-space align="start" style="width: 200px">
                                                <n-space>
                                                    <span>{{ comboName }}</span>
                                                    <n-tag>{{ combo.length }} 个宏指令</n-tag>
                                                </n-space>
                                                <n-tooltip trigger="hover" placement="bottom">
                                                    <template #trigger>
                                                        <div>
                                                            {{ formatMacros(index) }}
                                                        </div>
                                                    </template>
                                                    <div>
                                                        <div v-for="(macro, index1) in combo" :key="index1">
                                                            {{ macro }}
                                                        </div>
                                                    </div>
                                                </n-tooltip>
                                            </n-space>
                                            <n-space>
                                                <n-button @click="executeMacroCombo(comboName)">执行</n-button>
                                                <n-button @click="copyMacroCombo(comboName)">复制</n-button>
                                                <n-button type="error"
                                                    @click="deleteMacroCombo(comboName)">删除</n-button>
                                                <n-button type="error"
                                                    @click="deleteComboOnly(comboName)">仅删除</n-button>
                                            </n-space>
                                        </n-space>
                                    </n-list-item>
                                </n-list>
                            </n-card>
                        </div>
                    </n-space>
                </div>
            </n-tab-pane>
        </n-tabs>

        <!-- 宏组合名称输入模态框 -->
        <n-modal v-model:show="showNameInput" preset="dialog" title="宏组合名称" positive-text="确认" negative-text="取消"
            @positive-click="handleConfirmMacroName" @negative-click="handleCancelMacroName">
            <n-input v-model:value="macroComboName" placeholder="请输入名称" />
        </n-modal>

        <!-- 宏组合JSON输入模态框 -->
        <n-modal v-model:show="showMacroComboInput" preset="dialog" title="宏组合JSON" positive-text="确认"
            negative-text="取消" @positive-click="handleConfirmMacroCombo" @negative-click="handleCancelMacroCombo">
            <n-input v-model:value="macroComboJson" type="textarea" placeholder="请输入JSON"
                :autosize="{ minRows: 10, maxRows: 30 }" />
            <div style="margin-top: 12px;">
                <n-button size="small" @click="formatJson">
                    格式化JSON
                </n-button>
            </div>
        </n-modal>

        <!-- 多选宏指令模态框 -->
        <n-modal v-model:show="showMultiSelectModal" preset="card" title="选择宏指令" style="width: 400px">
            <n-empty v-if="macroNames.length === 0" description="暂无宏组合" />
            <n-list v-else selectable style="max-height: 300px; overflow-y: scroll;">
                <n-list-item v-for="macroName in macroNames" :key="macroName" :value="macroName"
                    @click="toggleSelection(macroName)">
                    <n-checkbox :checked="selectedMacros.includes(macroName)">
                        {{ macroName }}
                    </n-checkbox>
                </n-list-item>
            </n-list>

            <template #footer>
                <div style="margin-top: 12px; display: flex; justify-content: flex-end">
                    <n-space>
                        <n-button @click="quitMultiSelect">取消</n-button>
                        <n-button type="primary" @click="confirmMultiSelect">确认</n-button>
                    </n-space>
                </div>
            </template>
        </n-modal>
    </n-modal>
</template>

<script setup lang="ts">
import { createAppInstance } from '@/utils/instanceMethod'
import { NSpace, NTag } from "naive-ui";
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { useMessage } from "naive-ui";
import { useMacroStore } from "@/stores/macro";
import { globalMacroState, showMacroComboModal, macroSettings, trtcInstances } from "@/global/globalState.ts";

const message = useMessage();
const macroStore = useMacroStore();
const isRecording = ref(false)
const isShowMacroComboList = ref(false);
const macroCombos = ref<any[]>([]);
const showMultiSelectModal = ref(false)
const selectedMacros = ref<string[]>([]);
const macroComboName = ref("");
const showNameInput = ref(false);
let btns: Record<string, HTMLButtonElement> = {}
const {
    currentComboMacroName,
    macros
} = storeToRefs(macroStore);

macroStore.loadMacros();
const handleRecord = async (name: string) => {
    showMacroSettings()
    for (const trtcInstance of trtcInstances.value) {
        const targetBtn = trtcInstance.querySelector('#record-btn') as HTMLButtonElement;
        if (targetBtn) {
            isRecording.value = true
            const macroName = `${trtcInstance.id}-${name}`
            globalMacroState.isRecording = true
            globalMacroState.macroName = macroName
            targetBtn.click()
            btns[macroName] = targetBtn
        }
    }
}
const stopRecord = async () => {
    isRecording.value = false;
    const delayedClick = (btn: HTMLButtonElement) => {
        return new Promise<void>(resolve => {
            setTimeout(() => {
                btn.click();
                resolve();
            }, 0);
        });
    };// 通过宏任务的形式造成点击之间的微小延迟，避免同时点击造成由时间戳分配的id相同，相互覆盖录制的结果
    await Promise.all(
        Object.values(btns).map(btn => delayedClick(btn))
    );
    await macroStore.loadMacros()
    const macroComponents: any[] = []
    Object.keys(btns).forEach(name => {
        const macroName = macroNames.value.find(macroName => macroName === name)
        if (macroName) macroComponents.push(macroName)
    })
    btns = {}
    globalMacroState.isRecording = false
    globalMacroState.macroName = ''
    if (macroComponents.length <= 0) return
    await macroStore.saveMacroCombo(currentComboMacroName.value, macroComponents)
    message.success('保存宏组合: ' + currentComboMacroName.value);
    await fetchMacroCombos()
}
const handleMacroCombine = () => {
    showMultiSelectModal.value = true
}

const showMacroSettings = () => {
    macroSettings.forEach(macroSettings => macroSettings.value = true)
}

const fetchMacroCombos = async () => {
    try {
        macroCombos.value = await macroStore.getAllMacroCombo();
    } catch (error) {
        message.error('获取宏组合失败:' + error);
    }
};
fetchMacroCombos()
const executeMacroCombo = async (comboName: string) => {
    isShowMacroComboList.value = false
    try {
        const macroCombo = await macroStore.getMacroCombo(comboName);
        if (!macroCombo) return;
        for (let i = 0; i < macroCombo.length - trtcInstances.value.length; i++) {
            createAppInstance();
        }
        if (! await pollUntilAncestorsReady(macroCombo)) return
        showMacroSettings()
        for (const [i, trtcInstance] of trtcInstances.value.entries()) {
            const targetBtn = trtcInstance.querySelector(`#execute-btn-${macroCombo[i]}`) as HTMLButtonElement;
            if (targetBtn) {
                targetBtn.click();
            } else {
                console.error(`Button not found for idStr: ${macroCombo[i]}, comboName: ${comboName}`);
            }
        }
    } catch (error) {
        message.error('执行宏组合失败:' + error);
    }
};

const pollUntilAncestorsReady = async (macroCombo: string[]): Promise<boolean> => {
    const maxAttempts = 10;
    const interval = 100;
    let attempts = 0;
    return new Promise((resolve) => {
        const check = () => {
            attempts++;
            const allReady = trtcInstances.value.every((trtcInstance, i) => trtcInstance.querySelector(`#execute-btn-${macroCombo[i]}`) !== null);
            if (allReady) {
                resolve(true);
                return;
            }
            if (attempts >= maxAttempts) {
                message.error(`中止执行宏指令! 宏指令缺失: ${trtcInstances.value.filter((trtcInstance, i) => trtcInstance.querySelector(`#execute-btn-${macroCombo[i]}`) === null)}`);
                resolve(false);
                return;
            }
            setTimeout(check, interval);
        };
        check();
    });
};

const deleteMacroCombo = async (comboName: string) => {
    try {
        const deletedMacros = (await macroStore.getMacroCombo(comboName))?.join(', ')
        await macroStore.deleteMacroCombo(comboName);
        message.success('删除宏组合与其指令: ' + comboName + ': ' + deletedMacros);
        await fetchMacroCombos();
    } catch (error) {
        message.error('删除宏组合失败:' + error);
    }
};

const deleteComboOnly = async (comboName: string) => {
    try {
        await macroStore.deleteMacroComboOnly(comboName);
        message.success('删除宏组合:' + comboName);
        await fetchMacroCombos();
    } catch (error) {
        message.error('删除宏组合失败:' + error);
    }
};

const showMacroComboInput = ref(false)
const macroComboJson = ref('')
const uploadMacroCombo = () => {
    showMacroComboInput.value = !showMacroComboInput.value
}

const handleConfirmMacroCombo = () => {
    return parseMacroCombo()
}

const handleCancelMacroCombo = () => {
    showMacroComboInput.value = false;
    macroComboJson.value = ''
}

const copyMacroCombo = async (comboName: string) => {
    const comboMacros: any[] = []
    const macroNames = await macroStore.getMacroCombo(comboName);
    if (!macroNames) return
    macroNames.forEach((macroName: string) => {
        const macro = macros.value.find(macro => macro.name === macroName)
        comboMacros.push(macro)
    })
    const outputMacros: any[] = []
    comboMacros.forEach((macro: any) => {
        const actions: any[] = []
        macro.actions.forEach((action: any) => {
            const macroJson: Record<string, any> = {}
            macroJson[action.method] = action.args;
            actions.push(macroJson)
        })
        outputMacros.push({ [macro.name]: actions })
    })
    navigator.clipboard.writeText(JSON.stringify({ [comboName]: outputMacros }))
        .then(() => {
            message.success('复制成功');
        })
        .catch(err => {
            console.error('复制失败:', err);
            message.error('复制失败，请手动选择文本复制');
        });
}

async function parseMacroCombo() {
    try {
        const json = JSON.parse(macroComboJson.value)
        const macroComboName = Object.keys(json)[0]
        if (macroCombos.value.some(combo => combo[0] === macroComboName)) {
            message.error('宏组合已存在')
            return false
        }
        const uploadMacros = json[macroComboName]
        for (const uploadMacro of uploadMacros) {
            const macroName = Object.keys(uploadMacro)[0]
            if (macroNames.value.some(name => name === macroName)) {
                message.error(`宏指令已存在: ${macroName}`);
                return false;
            }
            const actions = uploadMacro[macroName]
            macroStore.isRecording = true;
            for (const action of actions) {
                const method = Object.keys(action)[0]
                const args = action[method]
                macroStore.recordAction(method, args);
            }
            macroStore.isRecording = false;
            await macroStore.createMacro(macroName, macroStore.currentRecording);
            macroStore.currentRecording = [];
            message.success(`上传宏指令成功: ${macroName}`);
        }
        await macroStore.saveMacroCombo(macroComboName, uploadMacros.map((macro: any) => Object.keys(macro)[0]));
        message.success('保存宏组合: ' + macroComboName);
        await fetchMacroCombos()
    }
    catch (error) {
        message.error("宏解析失败：" + (error as Error).message);
    }
    macroComboJson.value = ''
}

const macroNames = computed(() => macros.value.map(macro => macro.name))

const toggleSelection = (comboName: string) => {
    const index = selectedMacros.value.indexOf(comboName)
    if (index === -1) {
        selectedMacros.value.push(comboName)
    } else {
        selectedMacros.value.splice(index, 1)
    }
}

const formatJson = () => {
    try {
        const parsedJson = JSON.parse(macroComboJson.value);
        macroComboJson.value = JSON.stringify(parsedJson, null, 2);
    } catch (error) {
        message?.error('无效的JSON格式');
    }
};


// 确认选择
const confirmMultiSelect = () => {
    if (selectedMacros.value.length <= 0) {
        message.error("请选择宏组合")
        return false
    }
    const selectedMacroIds = selectedMacros.value.map(macroName => macroName.slice(0, 7))
    const selectedMacroIdSet = new Set(selectedMacroIds)
    if (selectedMacroIds.length !== selectedMacroIdSet.size) {
        message.error('宏组合包含重复的实例');
        return
    }
    showMultiSelectModal.value = false
    showNameInput.value = true
}

const quitMultiSelect = () => {
    selectedMacros.value = []
    showMultiSelectModal.value = false
}

const handleConfirmMacroName = async () => {
    if (!macroComboName.value) {
        message.error("请输入宏名称");
        return false;
    }
    if (macroCombos.value.some(macroCombo => macroCombo[0] === macroComboName.value)) {
        message.error("宏组合已存在");
        return false
    }
    const name = macroComboName.value;
    currentComboMacroName.value = name
    macroComboName.value = "";
    showNameInput.value = false;
    if (selectedMacros.value.length > 0) {
        await macroStore.saveMacroCombo(currentComboMacroName.value, [...selectedMacros.value])
        message.success('保存宏组合: ' + currentComboMacroName.value);
        selectedMacros.value = []
        await fetchMacroCombos()
    } else {
        handleRecord(name)
        showMacroComboModal.value = false
    }
}
const handleCancelMacroName = () => {
    macroComboName.value = "";
    showNameInput.value = false;
    isRecording.value = false;
}

const globalRecord = () => {
    if (isRecording.value) {
        stopRecord();
    } else {
        showNameInput.value = !showNameInput.value
    }
}
// 格式化动作列表为简短显示
function formatMacros(combo: number): string {
    const actionsStr = macroCombos.value[combo][1].join(";");
    if (actionsStr.length > 50) {
        return actionsStr.slice(0, 47) + "...";
    }
    return actionsStr;
}
</script>

<style scoped></style>