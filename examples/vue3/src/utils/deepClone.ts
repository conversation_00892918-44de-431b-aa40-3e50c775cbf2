export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  if(obj instanceof MediaStreamTrack){
    return obj;
  }
  if (obj instanceof ArrayBuffer) {
    return obj.slice(0) as unknown as T;
  }
  if (ArrayBuffer.isView(obj)) {
    const bufferCopy = (obj.buffer as ArrayBuffer).slice(0);
    return new (obj.constructor as any)(bufferCopy, obj.byteOffset, obj.length) as T;
  }
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags) as unknown as T;
  }
  if (obj instanceof Map) {
    const copy = new Map();
    obj.forEach((value, key) => {
      copy.set(deepClone(key), deepClone(value));
    });
    return copy as unknown as T;
  }
  if (obj instanceof Set) {
    const copy = new Set();
    obj.forEach((value) => {
      copy.add(deepClone(value));
    });
    return copy as unknown as T;
  }
  if (Array.isArray(obj)) {
    const copy = [] as unknown as T;
    obj.forEach((item, index) => {
      (copy as unknown as Array<unknown>)[index] = deepClone(item);
    });
    return copy;
  }
  if (isObject(obj)) {
    const copy = {} as T;
    Object.keys(obj).forEach((key) => {
      (copy as { [key: string]: any })[key] = deepClone((obj as { [key: string]: any })[key]);
    });
    return copy;
  }
  function isObject(obj: unknown): obj is object {
    return obj !== null && typeof obj === 'object' && !Array.isArray(obj) && !(obj instanceof Date) && !(obj instanceof RegExp) && !(obj instanceof Map) && !(obj instanceof Set) && !(obj instanceof ArrayBuffer);
  }
  return obj;
}