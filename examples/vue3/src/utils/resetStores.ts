import { useRoomStore } from '@/stores/room';
import { useSettingsStore } from '@/stores/settings';
import { useStatisticsStore } from '@/stores/statistics';
import { useVersionStore } from '@/stores/version';
import { useMediaStore } from '@/stores/media';
import { useMacroStore } from '@/stores/macro';
import { useProxyStore } from '@/stores/proxy';

export async function resetAllStores() {
    const roomStore = useRoomStore();
    const settingsStore = useSettingsStore();
    const statisticsStore = useStatisticsStore();
    const versionStore = useVersionStore();
    const mediaStore = useMediaStore();
    const macroStore = useMacroStore();
    const proxyStore = useProxyStore();

    await roomStore.$reset();
    await versionStore.$reset();
    mediaStore.$reset();
    settingsStore.$reset();
    statisticsStore.$reset();
    macroStore.$reset();
    proxyStore.$reset();
}

export async function resetStateStores() {
    const roomStore = useRoomStore();
    const mediaStore = useMediaStore();
    const settingsStore = useSettingsStore();

    await roomStore.$reset();
    mediaStore.$reset();
    settingsStore.$reset();
}