import { createApp } from 'vue';
import { create<PERSON><PERSON> } from 'pinia';
import App from '../App.vue';
import router from '../router';
import { showMacroComboModal, macroSettings, trtcInstances } from '@/global/globalState'
import {
    create,
    NButton,
    NCard,
    NConfigProvider,
    NInput,
    NLayout,
    NLayoutContent,
    NLayoutFooter,
    NLayoutHeader,
    NSelect,
    NSpace,
    NSwitch,
    NTabPane,
    NTabs,
    NTag,
    NH3,
    NList,
    NListItem,
    NEmpty,
    NModal,
    NIcon,
    NTooltip,
    NDivider,
    NCheckbox,
    NInputGroup,
    NRadio,
    NRadioGroup,
    NSlider,
    NNotificationProvider,
    NMessageProvider,
    NForm,
    NFormItem,
    NText,
    NScrollbar,
} from 'naive-ui';
import isMobile from './isMobile';

const naive = create({
    components: [
        NButton,
        NCard,
        NConfigProvider,
        NInput,
        NLayout,
        NLayoutContent,
        NLayoutFooter,
        NLayoutHeader,
        NSelect,
        NSpace,
        NSwitch,
        NTabPane,
        NTabs,
        NTag,
        NH3,
        NList,
        NListItem,
        NEmpty,
        NModal,
        NIcon,
        NTooltip,
        NDivider,
        NCheckbox,
        NInputGroup,
        NRadio,
        NRadioGroup,
        NSlider,
        NNotificationProvider,
        NMessageProvider,
        NForm,
        NFormItem,
        NText,
        NScrollbar,
    ]
});

export function createAppInstance() {
    const pinia = createPinia();
    const app = createApp(App);
    app.use(router);
    app.use(naive);
    app.use(pinia);
    const instanceId = `TRTC-${trtcInstances.value.length.toString().padStart(2, '0')}`
    const appElement = document.querySelector('#TRTC')
    if (appElement) {
        const newDiv = document.createElement('div')
        newDiv.id = instanceId
        trtcInstances.value.push(newDiv)
        appElement.appendChild(newDiv)
    } else {
        console.error('#TRTC 元素未找到！')
    }
    app.config.globalProperties.$instanceId = instanceId
    app.mount(`#${instanceId}`);
}

export function deleteAppInstance() {
    trtcInstances.value?.pop()?.remove()
    macroSettings.pop()
}

document.getElementById('addButton')?.addEventListener('click', () => {
    createAppInstance();
});

document.getElementById('subtractButton')?.addEventListener('click', () => {
    deleteAppInstance();
});

document.getElementById('macroButton')?.addEventListener('click', () => {
    showMacroComboModal.value = !showMacroComboModal.value
});

if (isMobile) document.getElementById('multipleInstances')!.style.display = 'none'