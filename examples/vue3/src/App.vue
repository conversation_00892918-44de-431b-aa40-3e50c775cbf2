<template>
  <n-config-provider>
    <n-message-provider>
      <n-notification-provider :placement="'bottom-right'">
        <NotificationInitializer />
        <n-layout class="layout-container">
          <!-- Header with Ribbon Menu -->
          <n-layout-header class="layout-header" bordered>
            <desktop-menu />
          </n-layout-header>

          <!-- Main Content -->
          <n-layout-content class="layout-content">
            <router-view v-slot="{ Component }">
              <template v-if="Component">
                <component :is="Component" />
              </template>
              <template v-else>
                <n-card title="加载错误">
                  <p>组件加载失败，请检查以下几点：</p>
                  <ul>
                    <li>1. 网络连接是否正常</li>
                    <li>2. 控制台是否有错误信息</li>
                    <li>3. 路由配置是否正确</li>
                  </ul>
                </n-card>
              </template>
            </router-view>
          </n-layout-content>

          <!-- Footer Status Bar -->
          <n-layout-footer class="layout-footer" bordered position="absolute">
            <status-bar />
          </n-layout-footer>
        </n-layout>
      </n-notification-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import {
  NConfigProvider,
  NLayout,
  NLayoutHeader,
  NLayoutContent,
  NLayoutFooter,
  NNotificationProvider,
  NMessageProvider,
  NCard,
} from "naive-ui";
import { getCurrentInstance, defineComponent, onMounted } from "vue";
import DesktopMenu from "./components/DesktopMenu.vue";
import StatusBar from "./components/StatusBar.vue";
import { useRoomStore } from "./stores/room";
import { useNotification } from "naive-ui";
import { useMacroStore } from "./stores/macro";
import { useMediaStore } from "./stores/media";

const mediaStore = useMediaStore()
const instance = getCurrentInstance()
if (instance) {
  mediaStore.currentInstanceId = instance.appContext.config.globalProperties.$instanceId
}

// Create a new component for notification initialization
const NotificationInitializer = defineComponent({
  name: "NotificationInitializer",
  setup() {
    const roomStore = useRoomStore();
    const macroStore = useMacroStore();
    const notification = useNotification();
    roomStore.setNotificationInstance(notification);

    onMounted(async () => {
      await macroStore.initDB();
    });

    return () => null; // Return empty component
  },
});

console.warn("App.vue");
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.layout-header {
  height: auto;
  padding: 8px;
}

.layout-content {
  flex: 1;
  padding: 16px;
  padding-bottom: 64px;
  /* 为固定的状态栏留出空间 */
  overflow-y: auto;
}

.layout-footer {
  padding: 8px;
  background-color: var(--n-color);
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
}
</style>
