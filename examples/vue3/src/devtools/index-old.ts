// 类型定义
interface ButtonConfig {
  id: string;
  icon: string;
  tooltip: string;
  panelId: string;
  active?: boolean;
  notification?: boolean;
}

interface PanelConfig {
  id: string;
  title: string;
  icon: string;
  content: string;
}

interface DevToolsConfig {
  position?: 'bottom-center' | 'bottom-left' | 'bottom-right';
  theme?: 'dark' | 'light';
}

// ==================== 样式系统 - 100% 隔离 ====================

const DEVTOOLS_PREFIX = 'trtc-devtools';
const CSS_NAMESPACE = `.${DEVTOOLS_PREFIX}`;

const createIsolatedStyles = (): string => `
  <style id="trtc-devtools-styles">
    /* CSS Reset for DevTools - 精确隔离，避免过度重置 */
    ${CSS_NAMESPACE} {
      /* CSS变量定义 - 默认深色主题 */
      --trtc-primary-bg: rgba(30, 30, 30, 0.98);
      --trtc-toolbar-bg: linear-gradient(135deg, rgba(40, 40, 45, 0.98), rgba(30, 30, 35, 0.98));
      --trtc-text-primary: #ffffff;
      --trtc-text-secondary: rgba(255, 255, 255, 0.7);
      --trtc-text-muted: rgba(255, 255, 255, 0.5);
      --trtc-border-color: rgba(255, 255, 255, 0.1);
      --trtc-hover-bg: rgba(255, 255, 255, 0.12);
      --trtc-active-bg: rgba(255, 255, 255, 0.08);
      --trtc-input-bg: rgba(255, 255, 255, 0.1);
      --trtc-input-border: rgba(255, 255, 255, 0.2);
      --trtc-input-focus-bg: rgba(255, 255, 255, 0.15);
      --trtc-input-focus-border: rgba(255, 255, 255, 0.4);
      --trtc-toggle-bg: rgba(255, 255, 255, 0.2);
      --trtc-toggle-slider: #ffffff;
      --trtc-accent-color: #007AFF;
      --trtc-accent-hover: #0056CC;
      --trtc-success-color: #34C759;
      --trtc-danger-color: #ff4757;
      --trtc-blur: blur(20px);
      --trtc-border-radius: 12px;
      --trtc-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.3);
      --trtc-shadow-heavy: 0 20px 80px rgba(0, 0, 0, 0.6);
      --trtc-separator-color: rgba(255, 255, 255, 0.15);
      --trtc-icon-color: rgba(255, 255, 255, 0.85);
      --trtc-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      
      /* 根容器样式 */
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      pointer-events: none !important;
      z-index: 2147483647 !important;
      font-family: var(--trtc-font-family) !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      color: var(--trtc-text-primary) !important;
      text-align: left !important;
      direction: ltr !important;
      box-sizing: border-box !important;
    }

    /* 浅色主题变量 */
    ${CSS_NAMESPACE}.light-theme {
      --trtc-primary-bg: rgba(255, 255, 255, 0.98);
      --trtc-toolbar-bg: linear-gradient(135deg, rgba(250, 250, 252, 0.98), rgba(245, 245, 247, 0.98));
      --trtc-text-primary: #1d1d1f;
      --trtc-text-secondary: rgba(29, 29, 31, 0.7);
      --trtc-text-muted: rgba(29, 29, 31, 0.5);
      --trtc-border-color: rgba(0, 0, 0, 0.1);
      --trtc-hover-bg: rgba(0, 0, 0, 0.06);
      --trtc-active-bg: rgba(0, 0, 0, 0.04);
      --trtc-input-bg: rgba(0, 0, 0, 0.05);
      --trtc-input-border: rgba(0, 0, 0, 0.15);
      --trtc-input-focus-bg: rgba(0, 0, 0, 0.08);
      --trtc-input-focus-border: rgba(0, 0, 0, 0.3);
      --trtc-toggle-bg: rgba(0, 0, 0, 0.15);
      --trtc-toggle-slider: #ffffff;
      --trtc-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.15);
      --trtc-shadow-heavy: 0 20px 80px rgba(0, 0, 0, 0.25);
      --trtc-separator-color: rgba(0, 0, 0, 0.15);
      --trtc-icon-color: rgba(29, 29, 31, 0.85);
    }

    /* 重置只针对DevTools内部元素，但保留必要的显示属性 */
    ${CSS_NAMESPACE} * {
      box-sizing: border-box !important;
      font-family: var(--trtc-font-family) !important;
    }

    /* Panel Styles - 基础样式，位置由JS动态设置 */
    ${CSS_NAMESPACE} .trtc-panel {
      all: unset !important;
      position: fixed !important;
      /* 位置和尺寸由JS动态设置，不在CSS中固定 */
      background: var(--trtc-primary-bg) !important;
      border-radius: 20px !important;
      padding: 32px !important;
      z-index: 2147483648 !important;
      backdrop-filter: var(--trtc-blur) !important;
      box-shadow: var(--trtc-shadow-heavy), 0 0 0 1px var(--trtc-border-color) !important;
      border: 1px solid var(--trtc-border-color) !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
      display: none !important;
      pointer-events: auto !important;
      font-size: 14px !important;
      line-height: 1.5 !important;
      box-sizing: border-box !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 全屏模式样式 */
    ${CSS_NAMESPACE} .trtc-panel.fullscreen {
      top: 20px !important;
      bottom: 20px !important;
      left: 20px !important;
      right: 20px !important;
      width: auto !important;
      max-width: none !important;
      height: auto !important;
      transform: none !important;
      border-radius: 16px !important;
      padding: 40px !important;
      overflow-y: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-panel-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 16px !important;
      margin-bottom: 24px !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-section {
      all: unset !important;
      display: block !important;
      margin-bottom: 32px !important;
    }

    ${CSS_NAMESPACE} .trtc-section:last-child {
      margin-bottom: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-item {
      all: unset !important;
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      margin-bottom: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-content {
      all: unset !important;
      display: block !important;
      flex: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-title {
      all: unset !important;
      display: block !important;
      font-size: 16px !important;
      font-weight: 500 !important;
      margin-bottom: 4px !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-setting-description {
      all: unset !important;
      display: block !important;
      font-size: 14px !important;
      color: var(--trtc-text-secondary) !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.4 !important;
    }

    ${CSS_NAMESPACE} .trtc-select {
      all: unset !important;
      display: inline-block !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      color: var(--trtc-text-primary) !important;
      padding: 8px 12px !important;
      font-size: 14px !important;
      cursor: pointer !important;
      min-width: 140px !important;
      font-family: var(--trtc-font-family) !important;
      appearance: none !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
    }

    ${CSS_NAMESPACE} .trtc-select option {
      background: #2a2a2a !important;
      color: var(--trtc-text-primary) !important;
      padding: 4px 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-toggle {
      all: unset !important;
      position: relative !important;
      display: inline-block !important;
      width: 48px !important;
      height: 24px !important;
      cursor: pointer !important;
    }

    ${CSS_NAMESPACE} .trtc-toggle input {
      all: unset !important;
      opacity: 0 !important;
      width: 0 !important;
      height: 0 !important;
      position: absolute !important;
    }

    ${CSS_NAMESPACE} .trtc-toggle-bg {
      all: unset !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: var(--trtc-toggle-bg) !important;
      border-radius: 12px !important;
      transition: all 0.3s ease !important;
      cursor: pointer !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-toggle-slider {
      all: unset !important;
      position: absolute !important;
      height: 18px !important;
      width: 18px !important;
      left: 3px !important;
      bottom: 3px !important;
      background: var(--trtc-toggle-slider) !important;
      border-radius: 50% !important;
      transition: all 0.3s ease !important;
      cursor: pointer !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-code {
      all: unset !important;
      display: inline !important;
      background: rgba(255, 255, 255, 0.1) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 12px !important;
      color: var(--trtc-text-primary) !important;
    }

    ${CSS_NAMESPACE} .trtc-link {
      all: unset !important;
      display: inline !important;
      color: var(--trtc-text-secondary) !important;
      text-decoration: underline !important;
      cursor: pointer !important;
    }

    /* Toolbar Styles */
    ${CSS_NAMESPACE} .trtc-toolbar {
      all: unset !important;
      position: fixed !important;
      bottom: 20px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      z-index: 2147483647 !important;
      pointer-events: auto !important;
      display: inline-block !important;
      width: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-toolbar-container {
      all: unset !important;
      background: var(--trtc-toolbar-bg) !important;
      border-radius: 20px !important;
      padding: 6px !important;
      display: flex !important;
      align-items: center !important;
      gap: 2px !important;
      backdrop-filter: var(--trtc-blur) !important;
      box-shadow: 
        0 6px 24px rgba(0, 0, 0, 0.5),
        0 2px 6px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
      border: 1px solid rgba(255, 255, 255, 0.08) !important;
      width: auto !important;
      min-width: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-btn {
      all: unset !important;
      background: transparent !important;
      border: none !important;
      color: var(--trtc-icon-color) !important;
      width: 32px !important;
      height: 32px !important;
      border-radius: var(--trtc-border-radius) !important;
      cursor: pointer !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
      position: relative !important;
      overflow: hidden !important;
      pointer-events: auto !important;
      font-family: var(--trtc-font-family) !important;
      font-size: 14px !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon {
      all: unset !important;
      position: relative !important;
      width: 22px !important;
      height: 22px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      pointer-events: none !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon svg {
      width: 22px !important;
      height: 22px !important;
      pointer-events: none !important;
      display: block !important;
      -webkit-backface-visibility: hidden !important;
      backface-visibility: hidden !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
      image-rendering: -webkit-optimize-contrast !important;
      image-rendering: crisp-edges !important;
    }

    ${CSS_NAMESPACE} .trtc-btn-icon img {
      width: 24px !important;
      height: 24px !important;
      pointer-events: none !important;
      display: block !important;
      -webkit-backface-visibility: hidden !important;
      backface-visibility: hidden !important;
      -webkit-transform: translateZ(0) !important;
      transform: translateZ(0) !important;
      image-rendering: -webkit-optimize-contrast !important;
      image-rendering: crisp-edges !important;
    }

    /* 深色主题的图标阴影 */
    ${CSS_NAMESPACE}:not(.light-theme) .trtc-btn-icon svg,
    ${CSS_NAMESPACE}:not(.light-theme) .trtc-btn-icon img {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) !important;
    }

    /* 浅色主题的图标阴影 */
    ${CSS_NAMESPACE}.light-theme .trtc-btn-icon svg,
    ${CSS_NAMESPACE}.light-theme .trtc-btn-icon img {
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1)) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)) !important;
      border-radius: inherit !important;
      opacity: 0 !important;
      transition: opacity 0.25s ease !important;
      pointer-events: none !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover {
      background: var(--trtc-hover-bg) !important;
      color: var(--trtc-text-primary) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--trtc-shadow-light) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover::before {
      opacity: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:active {
      transform: translateY(0) !important;
      background: var(--trtc-active-bg) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn.active {
      color: var(--trtc-text-primary) !important;
    }

    ${CSS_NAMESPACE} .trtc-notification {
      all: unset !important;
      position: absolute !important;
      top: -1px !important;
      right: -1px !important;
      width: 6px !important;
      height: 6px !important;
      background: var(--trtc-danger-color) !important;
      border-radius: 50% !important;
      border: 1.5px solid rgba(30, 30, 35, 0.98) !important;
      opacity: 0 !important;
      transform: scale(0) !important;
      transition: all 0.2s ease !important;
      pointer-events: none !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-notification.active {
      opacity: 1 !important;
      transform: scale(1) !important;
    }

    ${CSS_NAMESPACE} .trtc-tooltip {
      all: unset !important;
      position: absolute !important;
      bottom: 100% !important;
      left: 50% !important;
      transform: translateX(-50%) translateY(2px) !important;
      margin-bottom: 6px !important;
      padding: 4px 8px !important;
      background: rgba(0, 0, 0, 0.9) !important;
      color: #ffffff !important;
      font-size: 11px !important;
      font-weight: 500 !important;
      border-radius: 4px !important;
      white-space: nowrap !important;
      pointer-events: none !important;
      opacity: 0 !important;
      transition: all 0.2s ease !important;
      z-index: 2147483647 !important;
      font-family: var(--trtc-font-family) !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-tooltip::after {
      content: '' !important;
      position: absolute !important;
      top: 100% !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      border: 3px solid transparent !important;
      border-top-color: rgba(0, 0, 0, 0.9) !important;
      pointer-events: none !important;
      display: block !important;
    }

    /* 浅色主题下的工具提示样式 */
    ${CSS_NAMESPACE}.light-theme .trtc-tooltip {
      background: rgba(0, 0, 0, 0.85) !important;
      color: #ffffff !important;
    }

    ${CSS_NAMESPACE}.light-theme .trtc-tooltip::after {
      border-top-color: rgba(0, 0, 0, 0.85) !important;
    }

    ${CSS_NAMESPACE} .trtc-btn:hover .trtc-tooltip {
      opacity: 1 !important;
      transform: translateX(-50%) translateY(0) !important;
    }

    ${CSS_NAMESPACE} .trtc-separator {
      all: unset !important;
      width: 1px !important;
      height: 20px !important;
      background: var(--trtc-separator-color) !important;
      margin: 0 3px !important;
      display: block !important;
    }

    /* Form Inputs */
    ${CSS_NAMESPACE} .trtc-input {
      all: unset !important;
      display: block !important;
      width: 100% !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      color: var(--trtc-text-primary) !important;
      padding: 10px 12px !important;
      font-size: 14px !important;
      font-family: var(--trtc-font-family) !important;
      margin-top: 8px !important;
      transition: all 0.2s ease !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-input:focus {
      border-color: var(--trtc-input-focus-border) !important;
      background: var(--trtc-input-focus-bg) !important;
      outline: none !important;
    }

    ${CSS_NAMESPACE} .trtc-input::placeholder {
      color: var(--trtc-text-muted) !important;
    }

    ${CSS_NAMESPACE} .trtc-textarea {
      all: unset !important;
      display: block !important;
      width: 100% !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      color: var(--trtc-text-primary) !important;
      padding: 10px 12px !important;
      font-size: 14px !important;
      font-family: var(--trtc-font-family) !important;
      margin-top: 8px !important;
      resize: vertical !important;
      min-height: 60px !important;
      transition: all 0.2s ease !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-textarea:focus {
      border-color: var(--trtc-input-focus-border) !important;
      background: var(--trtc-input-focus-bg) !important;
      outline: none !important;
    }

    ${CSS_NAMESPACE} .trtc-textarea::placeholder {
      color: var(--trtc-text-muted) !important;
    }

    ${CSS_NAMESPACE} .trtc-button {
      all: unset !important;
      display: inline-block !important;
      background: linear-gradient(135deg, var(--trtc-accent-color), #0051D4) !important;
      border: none !important;
      border-radius: 8px !important;
      color: var(--trtc-text-primary) !important;
      padding: 10px 20px !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;
      margin-right: 12px !important;
      font-family: var(--trtc-font-family) !important;
      text-align: center !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-button:hover {
      background: linear-gradient(135deg, var(--trtc-accent-hover), #003A9F) !important;
      transform: translateY(-1px) !important;
    }

    ${CSS_NAMESPACE} .trtc-button:active {
      transform: translateY(0) !important;
    }

    ${CSS_NAMESPACE} .trtc-button.secondary {
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
    }

    ${CSS_NAMESPACE} .trtc-button.secondary:hover {
      background: var(--trtc-input-focus-bg) !important;
    }

    /* 特殊修复 - 确保文本节点正常显示 */
    ${CSS_NAMESPACE} .trtc-panel-header,
    ${CSS_NAMESPACE} .trtc-setting-title,
    ${CSS_NAMESPACE} .trtc-setting-description,
    ${CSS_NAMESPACE} .trtc-tooltip {
      line-height: 1.4 !important;
      text-rendering: optimizeLegibility !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
    }

    /* 确保flex容器和子元素正常工作 */
    ${CSS_NAMESPACE} .trtc-panel-header img,
    ${CSS_NAMESPACE} .trtc-panel-header svg {
      flex-shrink: 0 !important;
      width: 36px !important;
      height: 36px !important;
      object-fit: contain !important;
    }

    /* Label元素特殊处理 */
    ${CSS_NAMESPACE} label {
      cursor: pointer !important;
      display: block !important;
    }

    /* TRTC Panel 专用样式 */
    ${CSS_NAMESPACE} .trtc-title-section {
      all: unset !important;
      display: block !important;
      flex: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-main-title {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      font-size: 22px !important;
      font-weight: 700 !important;
      color: var(--trtc-text-primary) !important;
      margin-bottom: 2px !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.2 !important;
      letter-spacing: -0.5px !important;
    }

    ${CSS_NAMESPACE} .trtc-sub-title {
      all: unset !important;
      display: block !important;
      font-size: 13px !important;
      color: var(--trtc-text-muted) !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.4 !important;
      font-weight: 400 !important;
    }

    ${CSS_NAMESPACE} .trtc-version-badge {
      all: unset !important;
      display: inline-block !important;
      background: var(--trtc-accent-color) !important;
      color: #ffffff !important;
      font-size: 11px !important;
      font-weight: 600 !important;
      padding: 3px 8px !important;
      border-radius: 6px !important;
      margin-left: 10px !important;
      font-family: var(--trtc-font-family) !important;
      vertical-align: middle !important;
    }

    ${CSS_NAMESPACE} .trtc-footer-links {
      all: unset !important;
      display: flex !important;
      gap: 16px !important;
      margin-top: 16px !important;
      padding-top: 16px !important;
      border-top: 1px solid var(--trtc-border-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-footer-link {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 6px !important;
      color: var(--trtc-text-secondary) !important;
      font-size: 13px !important;
      font-weight: 500 !important;
      text-decoration: none !important;
      cursor: pointer !important;
      transition: color 0.2s ease !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-footer-link:hover {
      color: var(--trtc-accent-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-footer-icon {
      all: unset !important;
      display: inline-block !important;
      width: 16px !important;
      height: 16px !important;
      flex-shrink: 0 !important;
    }

    /* Functions Panel 专用样式 */
    ${CSS_NAMESPACE} .trtc-refresh-btn {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 32px !important;
      height: 32px !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      color: var(--trtc-text-secondary) !important;
      cursor: pointer !important;
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
      margin-left: auto !important;
      position: relative !important;
      overflow: hidden !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn::before {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)) !important;
      border-radius: inherit !important;
      opacity: 0 !important;
      transition: opacity 0.25s ease !important;
      pointer-events: none !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn:hover {
      background: var(--trtc-input-focus-bg) !important;
      border-color: var(--trtc-input-focus-border) !important;
      color: var(--trtc-text-primary) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn:hover::before {
      opacity: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn:active {
      transform: translateY(0) scale(0.95) !important;
      transition: all 0.1s ease !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn.refreshing {
      pointer-events: none !important;
      background: var(--trtc-accent-color) !important;
      border-color: var(--trtc-accent-color) !important;
      color: #ffffff !important;
      transform: none !important;
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2) !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn.refreshing svg {
      animation: trtc-refresh-spin 1s linear infinite !important;
    }

    ${CSS_NAMESPACE} .trtc-refresh-btn svg {
      width: 18px !important;
      height: 18px !important;
      transition: transform 0.25s ease !important;
      position: relative !important;
      z-index: 1 !important;
    }

    @keyframes trtc-refresh-spin {
      from {
        transform: rotate(0deg) !important;
      }
      to {
        transform: rotate(360deg) !important;
      }
    }

    ${CSS_NAMESPACE} .trtc-functions-stats {
      all: unset !important;
      display: flex !important;
      gap: 20px !important;
      margin-bottom: 20px !important;
      padding: 12px 16px !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-stat-item {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 6px !important;
    }

    ${CSS_NAMESPACE} .trtc-stat-label {
      all: unset !important;
      font-size: 12px !important;
      color: var(--trtc-text-secondary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-stat-value {
      all: unset !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-container {
      all: unset !important;
      display: block !important;
      max-height: 350px !important;
      overflow-y: auto !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      background: var(--trtc-input-bg) !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-empty {
      all: unset !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 40px 20px !important;
      text-align: center !important;
    }

    ${CSS_NAMESPACE} .trtc-empty-icon {
      all: unset !important;
      font-size: 32px !important;
      margin-bottom: 12px !important;
      opacity: 0.6 !important;
    }

    ${CSS_NAMESPACE} .trtc-empty-text {
      all: unset !important;
      display: block !important;
      font-size: 14px !important;
      font-weight: 500 !important;
      color: var(--trtc-text-primary) !important;
      margin-bottom: 4px !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-empty-hint {
      all: unset !important;
      display: block !important;
      font-size: 12px !important;
      color: var(--trtc-text-muted) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-item {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      padding: 8px 12px !important;
      border-bottom: 1px solid var(--trtc-border-color) !important;
      transition: background 0.2s ease !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-item:last-child {
      border-bottom: none !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-item:hover {
      background: var(--trtc-hover-bg) !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-dot {
      all: unset !important;
      width: 8px !important;
      height: 8px !important;
      border-radius: 50% !important;
      background: var(--trtc-accent-color) !important;
      margin-right: 12px !important;
      flex-shrink: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-content {
      all: unset !important;
      display: block !important;
      flex: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-function {
      all: unset !important;
      display: block !important;
      font-size: 13px !important;
      font-weight: 500 !important;
      color: var(--trtc-text-primary) !important;
      margin-bottom: 2px !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.4 !important;
    }

    ${CSS_NAMESPACE} .trtc-function-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 4px !important;
      cursor: pointer !important;
      width: 100% !important;
    }

    ${CSS_NAMESPACE} .trtc-function-name {
      all: unset !important;
      color: var(--trtc-accent-color) !important;
      font-weight: 600 !important;
    }

    ${CSS_NAMESPACE} .trtc-function-params-preview {
      all: unset !important;
      color: var(--trtc-text-secondary) !important;
      font-weight: 400 !important;
      font-size: 12px !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
    }

    ${CSS_NAMESPACE} .trtc-param-detail-btn {
      all: unset !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 20px !important;
      height: 20px !important;
      color: var(--trtc-text-muted) !important;
      cursor: pointer !important;
      border-radius: 4px !important;
      transition: all 0.2s ease !important;
      font-size: 12px !important;
      margin-left: 4px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-detail-btn:hover {
      background: var(--trtc-hover-bg) !important;
      color: var(--trtc-text-primary) !important;
    }

    /* 浮窗样式 */
    ${CSS_NAMESPACE} .trtc-param-modal {
      all: unset !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      background: rgba(0, 0, 0, 0.5) !important;
      z-index: 2147483649 !important;
      display: none !important;
      align-items: center !important;
      justify-content: center !important;
      backdrop-filter: blur(4px) !important;
      pointer-events: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal.show {
      display: flex !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-content {
      all: unset !important;
      display: block !important;
      background: var(--trtc-primary-bg) !important;
      border-radius: 16px !important;
      padding: 24px !important;
      max-width: 600px !important;
      max-height: 80vh !important;
      overflow-y: auto !important;
      box-shadow: var(--trtc-shadow-heavy) !important;
      border: 1px solid var(--trtc-border-color) !important;
      backdrop-filter: var(--trtc-blur) !important;
      font-family: var(--trtc-font-family) !important;
      margin: 20px !important;
      width: calc(100% - 40px) !important;
      box-sizing: border-box !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      margin-bottom: 20px !important;
      padding-bottom: 12px !important;
      border-bottom: 1px solid var(--trtc-border-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-title {
      all: unset !important;
      display: block !important;
      font-size: 18px !important;
      font-weight: 600 !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-close {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 32px !important;
      height: 32px !important;
      border-radius: 8px !important;
      background: var(--trtc-input-bg) !important;
      color: var(--trtc-text-secondary) !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;
      font-size: 18px !important;
      border: 1px solid var(--trtc-input-border) !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-close:hover {
      background: var(--trtc-hover-bg) !important;
      color: var(--trtc-text-primary) !important;
    }

    ${CSS_NAMESPACE} .trtc-param-modal-body {
      all: unset !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-function-signature {
      all: unset !important;
      display: block !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
      padding: 12px !important;
      margin-bottom: 20px !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 14px !important;
      color: var(--trtc-text-primary) !important;
      line-height: 1.4 !important;
      word-break: break-all !important;
    }

    ${CSS_NAMESPACE} .trtc-param-section {
      all: unset !important;
      display: block !important;
      margin-bottom: 16px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-section-title {
      all: unset !important;
      display: block !important;
      font-size: 14px !important;
      font-weight: 600 !important;
      color: var(--trtc-text-primary) !important;
      margin-bottom: 8px !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-param-list {
      all: unset !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-param-item-modal {
      all: unset !important;
      display: block !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 6px !important;
      padding: 12px !important;
      margin-bottom: 8px !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 12px !important;
      line-height: 1.6 !important;
    }

    ${CSS_NAMESPACE} .trtc-param-item-modal:last-child {
      margin-bottom: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-param-item-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      margin-bottom: 6px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-index-modal {
      all: unset !important;
      display: inline-block !important;
      color: var(--trtc-accent-color) !important;
      font-weight: 600 !important;
      margin-right: 8px !important;
      min-width: 24px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-type {
      all: unset !important;
      display: inline-block !important;
      color: var(--trtc-text-muted) !important;
      font-size: 10px !important;
      background: rgba(0, 122, 255, 0.1) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      margin-left: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-param-value-modal {
      all: unset !important;
      display: block !important;
      color: var(--trtc-text-primary) !important;
      white-space: pre-wrap !important;
      word-break: break-all !important;
      max-height: 200px !important;
      overflow-y: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-param-item {
      all: unset !important;
      display: block !important;
      margin-bottom: 4px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-item:last-child {
      margin-bottom: 0 !important;
    }

    ${CSS_NAMESPACE} .trtc-param-index {
      all: unset !important;
      color: var(--trtc-text-muted) !important;
      font-weight: 500 !important;
      margin-right: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-param-value {
      all: unset !important;
      color: var(--trtc-text-primary) !important;
      word-break: break-all !important;
    }

    ${CSS_NAMESPACE} .trtc-function-group {
      all: unset !important;
      display: block !important;
      border-bottom: 1px solid var(--trtc-border-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-function-group:last-child {
      border-bottom: none !important;
    }

    ${CSS_NAMESPACE} .trtc-event-item {
      all: unset !important;
      display: flex !important;
      align-items: flex-start !important;
      gap: 12px !important;
      padding: 8px 12px !important;
      transition: background 0.2s ease !important;
    }

    ${CSS_NAMESPACE} .trtc-event-item:hover {
      background: var(--trtc-hover-bg) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-primary {
      background: rgba(52, 199, 89, 0.05) !important;
      border-left: 3px solid var(--trtc-success-color) !important;
      padding-left: 9px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-sync {
      background: rgba(0, 122, 255, 0.05) !important;
      border-left: 3px solid var(--trtc-accent-color) !important;
      padding-left: 9px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary {
      background: transparent !important;
      border-left: none !important;
      padding-left: 12px !important;
      opacity: 0.7 !important;
      font-size: 12px !important;
      margin-left: 20px !important;
      position: relative !important;
      padding-top: 4px !important;
      padding-bottom: 4px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary::before {
      content: '' !important;
      position: absolute !important;
      left: -23px !important;
      top: 12px !important;
      width: 20px !important;
      height: 1px !important;
      background: var(--trtc-border-color) !important;
      opacity: 0.5 !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary .trtc-event-indicator {
      padding-top: 0px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-time {
      all: unset !important;
      display: block !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 11px !important;
      font-weight: 600 !important;
      color: var(--trtc-text-primary) !important;
      min-width: 85px !important;
      text-align: right !important;
      flex-shrink: 0 !important;
      padding-top: 2px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary .trtc-event-time {
      font-size: 10px !important;
      font-weight: 400 !important;
      color: var(--trtc-text-muted) !important;
      min-width: 70px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-indicator {
      all: unset !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      flex-shrink: 0 !important;
      padding-top: 4px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-dot {
      all: unset !important;
      width: 8px !important;
      height: 8px !important;
      border-radius: 50% !important;
      margin-bottom: 4px !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-event-dot.start {
      background: var(--trtc-success-color) !important;
      box-shadow: 0 0 0 2px rgba(52, 199, 89, 0.2) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-dot.sync {
      background: var(--trtc-accent-color) !important;
      box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-dot.end {
      background: var(--trtc-danger-color) !important;
      box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.2) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-line {
      all: unset !important;
      width: 2px !important;
      height: 20px !important;
      background: var(--trtc-border-color) !important;
      display: block !important;
    }

    ${CSS_NAMESPACE} .trtc-event-content {
      all: unset !important;
      display: block !important;
      flex: 1 !important;
    }

    ${CSS_NAMESPACE} .trtc-event-header {
      all: unset !important;
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
      margin-bottom: 4px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-action {
      all: unset !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 4px !important;
      font-size: 13px !important;
      font-weight: 600 !important;
      color: var(--trtc-text-primary) !important;
      font-family: var(--trtc-font-family) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-action.start {
      color: var(--trtc-success-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-action.sync {
      color: var(--trtc-accent-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-action.end {
      color: var(--trtc-danger-color) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary .trtc-event-action {
      font-size: 10px !important;
      font-weight: 400 !important;
      color: var(--trtc-text-muted) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-secondary .trtc-event-details {
      font-size: 9px !important;
      color: var(--trtc-text-muted) !important;
    }

    ${CSS_NAMESPACE} .trtc-event-function {
      all: unset !important;
      color: var(--trtc-accent-color) !important;
      font-weight: 600 !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 13px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-duration {
      all: unset !important;
      display: inline-block !important;
      background: rgba(0, 122, 255, 0.1) !important;
      color: var(--trtc-accent-color) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      font-size: 10px !important;
      font-weight: 500 !important;
      margin-left: auto !important;
    }

    ${CSS_NAMESPACE} .trtc-event-details {
      all: unset !important;
      display: block !important;
      font-size: 11px !important;
      color: var(--trtc-text-secondary) !important;
      font-family: var(--trtc-font-family) !important;
      line-height: 1.4 !important;
    }

    ${CSS_NAMESPACE} .trtc-event-params {
      all: unset !important;
      color: var(--trtc-text-muted) !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      font-size: 10px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-instance {
      all: unset !important;
      color: var(--trtc-text-muted) !important;
      font-size: 10px !important;
    }

    ${CSS_NAMESPACE} .trtc-event-sync-note {
      all: unset !important;
      color: var(--trtc-accent-color) !important;
      font-size: 10px !important;
      font-weight: 500 !important;
    }

    ${CSS_NAMESPACE} .trtc-instance-badge {
      all: unset !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 4px !important;
      padding: 2px 6px !important;
      border-radius: 6px !important;
      font-size: 9px !important;
      font-weight: 600 !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      margin-left: 6px !important;
      vertical-align: middle !important;
      /* 颜色由JS动态设置，根据背景颜色自动调整 */
    }

    ${CSS_NAMESPACE} .trtc-instance-dot {
      all: unset !important;
      width: 6px !important;
      height: 6px !important;
      border-radius: 50% !important;
      background: currentColor !important;
      display: inline-block !important;
      opacity: 0.9 !important;
    }

    ${CSS_NAMESPACE} .trtc-instances-filter {
      all: unset !important;
      display: block !important;
      margin-bottom: 20px !important;
      padding: 12px 16px !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      border-radius: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-buttons {
      all: unset !important;
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 8px !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn {
      all: unset !important;
      display: inline-flex !important;
      align-items: center !important;
      gap: 6px !important;
      padding: 6px 12px !important;
      border-radius: 6px !important;
      font-size: 12px !important;
      font-weight: 500 !important;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
      background: var(--trtc-input-bg) !important;
      border: 1px solid var(--trtc-input-border) !important;
      color: var(--trtc-text-secondary) !important;
      cursor: pointer !important;
      transition: all 0.2s ease !important;
      user-select: none !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn:hover {
      background: var(--trtc-input-focus-bg) !important;
      border-color: var(--trtc-input-focus-border) !important;
      color: var(--trtc-text-primary) !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn.active {
      /* 背景色和文字颜色由JS动态设置，不在CSS中固定 */
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn.active:hover {
      /* 悬停时略微降低不透明度，但保持颜色 */
      opacity: 0.85 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn-dot {
      all: unset !important;
      width: 8px !important;
      height: 8px !important;
      border-radius: 50% !important;
      background: currentColor !important;
      display: inline-block !important;
      opacity: 0.8 !important;
    }

    ${CSS_NAMESPACE} .trtc-filter-btn.active .trtc-filter-btn-dot {
      opacity: 1 !important;
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
    }

    ${CSS_NAMESPACE} .trtc-timeline-duration {
      all: unset !important;
      display: inline-block !important;
      font-size: 11px !important;
      color: var(--trtc-accent-color) !important;
      background: rgba(0, 122, 255, 0.1) !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      margin-left: auto !important;
      font-family: var(--trtc-font-family) !important;
      flex-shrink: 0 !important;
      order: 999 !important;
    }
  </style>
`;

// ==================== 图标系统 ====================

const icons = {
  function: `
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M4 4m0 2.667a2.667 2.667 0 0 1 2.667 -2.667h10.666a2.667 2.667 0 0 1 2.667 2.667v10.666a2.667 2.667 0 0 1 -2.667 2.667h-10.666a2.667 2.667 0 0 1 -2.667 -2.667z" />
      <path d="M9 15.5v.25c0 .69 .56 1.25 1.25 1.25c.71 0 1.304 -.538 1.374 -1.244l.752 -7.512a1.381 1.381 0 0 1 1.374 -1.244c.69 0 1.25 .56 1.25 1.25v.25" />
      <path d="M9 12h6" />
      </svg>
  `,
  chartBar: `
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z" />
      <path d="M4 20h14" />
    </svg>
  `,
  settings: `
    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
      <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" />
      <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" />
    </svg>
  `,
  trtcLogo: `<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAABelBMVEUAAAAAmP8AmP8AZ/sAYvsAif4AmP8AYvsAvP8AmP8Auf8AYvsA0P8A0P8AYvsAYvsAYvsA0P8A0P8Amf8AYvsA0P8A0P8AYvsAmP8AmP8A0P8AYvsA0P8AmP8AYvsAYvsAmP8A0P8AmP8A0P8AYvsA0P8Al/8AYvsA0P8A0P8AYvsAYvsAmP8AmP8A0P8AmP8A0P8AYvsAmP8AmP8A0P8AmP8A0P8A0P8AYvsA0P8AYvsAmP8AYvsAYvsAYfsAYvsA0P8AmP8A0P8Aqv8AYvsA0P8AYvsAmP8AYvsAmP8A0P8A0P8AYvsA0P8AmP8A0P8A0P8A0P8AYvsA0P8A0P8AmP8A0P8A0P8AYvsA0P8A0f8AmP8AYvsA0P8A0P8AYvsA0P8AYvsA0P8AYvsA0P8AmP8AYvsAmP8AYvsAYvsAmP8AYvsAmP8AmP8AmP8AmP8AYvsAmP8AmP8A0P8AmP8A0P8AmP8AmP8AYvsAmP8AYvsAmP8AYvsA0P9J4SmKAAAAe3RSTlMAO/MMxAkn6Qb0Dfb17b2xnD83Ly8J+/r4593TwruEeG9hSCYcGBIG+fLy4+LZ1s/OzcnBuLCvpqJ/ZWFaUkI+LSAfFhQQEP7z7Oji2su4squdlZSPjod0bmtpaWBbT0tKSEU1MiIhHRnt3MOno52bjId+WllRUUI7Nymc4G5LAAACeklEQVRo3u3V51oiMRiG4Xd1Z0AQASkqoqgI9rb2joKuvffedXuvE859wyVeWQYEh5nwYzf3CTzJfJMEgiAIgiAI/wHzzPTV2cmVB1x4QkHnl93N1WWfrdlrGyg3eunlJ/s7g/7OhdI44zOmYglFLuqH1/u6F1u88XR9Fj0rD1+7onBurfl9Nrb0dKVO5EG6dTWMfv6w0tVetBI+KI3nUq/92zRs976er1TuFf3CRZ/X4Ij0tbdE+UvJM5qt78zaaNE4+et3dAuqCDW9s5AlsqbtqLheKCpFNJJQPvjoaLoj0EJmDXUE5m/+zN9qPQgtwv3KoxEquutLS3j9Z2ZoUvcyY4QJbtpSG537M9BmqkPJHqGcq80sYdsKQqs6JXcEnuPuZKJ5wAnNpJpsESY0vJhoLB97oJ1clT3CRIYHBg9CyEdDZa4IY0aeRpWnRi4d48jT4RMjN7Umssd3J7Mj1YS0/gTFayZNp2UVhJCeGBij/y730BxJcIAx+Jw01raRe0dgDDrxbBhJJjcYA++upnM6jAdviqGi4xZmL6Pb0UqYj9DBkvE9SQ6DqRiHit6XcV7GWA9J0XYDXVy96kh/ccBKUtmboM/t+5KURpdropWoBKCX+XsNy7Rvy3AQFesE9LP8+FTTUVXV3vX2UAakMqKydAdDSFFZngojofg5URmC4dIjewWIvLosQKRntgCRDYB/ZAzgHjH9BrhHyiSAe6QWFOeI9RQU58hSIyh+EXbN844EQHGOzE2C4hypjoHiHBkCB+pHawR8bBDG5AYf51YWsUvgQ3KwjUyCl0b7Q+MI/MQC1VZSYbJPgqu7ibFxtwRBEARBEIR/yR9572Zi5JPyqAAAAABJRU5ErkJggg==" alt="TRTC Logo" />`
};

// ==================== 组件模板 ====================

const createButton = (config: ButtonConfig): string => `
  <button class="trtc-btn ${config.active ? 'active' : ''}" id="${config.id}" data-app-id="trtc:${config.id}">
    <div class="trtc-btn-icon">
      ${config.icon}
      <div class="trtc-notification ${config.notification ? 'active' : ''}"></div>
    </div>
    <span class="trtc-tooltip">${config.tooltip}</span>
  </button>
`;

const createSeparator = (): string => `
  <div class="trtc-separator"></div>
`;

const createToolbar = (buttons: ButtonConfig[]): string => `
  <div class="trtc-toolbar">
    <div class="trtc-toolbar-container">
      ${buttons.slice(0, -1).map(btn => createButton(btn)).join('')}
      ${createSeparator()}
      ${createButton(buttons[buttons.length - 1])}
    </div>
  </div>
`;

// ==================== 面板模板 ====================

const createTRTCPanel = (): string => `
  <div id="trtcPanel" class="trtc-panel">
    <div class="trtc-panel-header">
      ${icons.trtcLogo}
      <div class="trtc-title-section">
        <div class="trtc-main-title">TRTC DevTools <span class="trtc-version-badge">v1.0.0</span></div>
        <div class="trtc-sub-title">腾讯云实时音视频开发工具</div>
      </div>
    </div>

    <div class="trtc-section">
      <div class="trtc-setting-description">
        实时监控 TRTC SDK 状态，提供音视频流统计和质量分析，助力开发调试。
      </div>
    </div>

    <div class="trtc-footer-links">
      <a href="https://github.com/LiteAVSDK/TRTC_Web/issues" target="_blank" class="trtc-footer-link">
        <svg class="trtc-footer-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M9 9v-1a3 3 0 0 1 6 0v1" />
          <path d="M8 9h8a6 6 0 0 1 1 3v3a5 5 0 0 1 -10 0v-3a6 6 0 0 1 1 -3" />
          <path d="M3 13l4 0" />
          <path d="M17 13l4 0" />
          <path d="M12 20l0 -6" />
          <path d="M4 19l3.35 -2" />
          <path d="M20 19l-3.35 -2" />
          <path d="M4 7l3.75 2.4" />
          <path d="M20 7l-3.75 2.4" />
        </svg>
        反馈 Bug
      </a>
      <a href="https://cloud.tencent.com/document/product/647" target="_blank" class="trtc-footer-link">
        <svg class="trtc-footer-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M14 3v4a1 1 0 0 0 1 1h4" />
          <path d="M12 21h-5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v4.5" />
          <path d="M16.5 17.5m-2.5 0a2.5 2.5 0 1 0 5 0a2.5 2.5 0 1 0 -5 0" />
          <path d="M18.5 19.5l2.5 2.5" />
        </svg>
        文档
      </a>
      <a href="#" class="trtc-footer-link" id="trtcSettingsLink">
        <svg class="trtc-footer-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" />
          <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" />
        </svg>
        设置
      </a>
    </div>
  </div>
`;

const createFunctionsPanel = (): string => `
  <div id="clickPanel" class="trtc-panel">
    <div class="trtc-panel-header">
      ${icons.function}
      Functions
      <button id="refreshFunctions" class="trtc-refresh-btn" title="手动刷新">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4" />
          <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4" />
        </svg>
      </button>
    </div>
    
    <div class="trtc-functions-stats">
      <div class="trtc-stat-item">
        <span class="trtc-stat-label">实例数:</span>
        <span class="trtc-stat-value" id="instanceCount">0</span>
      </div>
      <div class="trtc-stat-item">
        <span class="trtc-stat-label">函数调用:</span>
        <span class="trtc-stat-value" id="functionCallCount">0</span>
      </div>
      <div class="trtc-stat-item">
        <span class="trtc-stat-label">最近调用:</span>
        <span class="trtc-stat-value" id="lastCallTime">--</span>
      </div>
    </div>

    <div class="trtc-instances-filter" id="instancesFilter" style="display: none;">
      <div class="trtc-setting-title" style="margin-bottom: 12px; font-size: 14px;">实例过滤器</div>
      <div class="trtc-filter-buttons" id="filterButtons">
        <!-- 动态生成过滤按钮 -->
      </div>
    </div>

    <div class="trtc-section">
      <div class="trtc-setting-title">函数调用时间线</div>
      <div class="trtc-timeline-container" id="functionsTimeline">
        <div class="trtc-timeline-empty">
          <div class="trtc-empty-icon">📊</div>
          <div class="trtc-empty-text">暂无函数调用数据</div>
          <div class="trtc-empty-hint">开始使用 TRTC SDK 后将显示调用时间线</div>
        </div>
      </div>
    </div>
  </div>
`;

const createAnalyticsPanel = (): string => `
  <div id="analyticsPanel" class="trtc-panel">
    <div class="trtc-panel-header">
      ${icons.chartBar}
      Analytics
    </div>
    <div class="trtc-section">
      <div class="trtc-setting-title">数据分析</div>
      <div class="trtc-setting-description">
        查看实时音视频通话的质量统计、用户行为分析和性能指标。
      </div>
    </div>
  </div>
`;

const createSettingsPanel = (): string => `
  <div id="settingsPanel" class="trtc-panel">
    <div class="trtc-panel-header">
      ${icons.settings}
      设置
    </div>

    <div class="trtc-section">
      <div class="trtc-setting-item">
        <div class="trtc-setting-content">
          <div class="trtc-setting-title">主题模式</div>
          <div class="trtc-setting-description">切换开发工具栏的外观主题。</div>
        </div>
        <select id="themeSelect" class="trtc-select">
          <option value="dark">夜间模式</option>
          <option value="light">日间模式</option>
        </select>
      </div>
    </div>

    <div class="trtc-section">
      <div class="trtc-setting-item">
        <div class="trtc-setting-content">
          <div class="trtc-setting-title">工具栏位置</div>
          <div class="trtc-setting-description">调整开发工具栏在页面中的显示位置。</div>
        </div>
        <select id="placementSelect" class="trtc-select">
          <option value="bottom-center">底部居中</option>
          <option value="bottom-left">左下角</option>
          <option value="bottom-right">右下角</option>
        </select>
      </div>
    </div>

    <div class="trtc-section">
      <div class="trtc-setting-item">
        <div class="trtc-setting-content">
          <div class="trtc-setting-title">全屏显示模式</div>
          <div class="trtc-setting-description">启用后，点击工具栏图标时面板将以全屏模式显示。</div>
        </div>
        <label class="trtc-toggle">
          <input type="checkbox" id="fullscreenMode">
          <span class="trtc-toggle-bg"></span>
          <span class="trtc-toggle-slider"></span>
        </label>
      </div>
    </div>
  </div>
`;

// ==================== 配置数据 ====================

const defaultButtons: ButtonConfig[] = [
  {
    id: 'addButton',
    icon: icons.trtcLogo,
    tooltip: 'TRTC',
    panelId: 'trtcPanel'
  },
  {
    id: 'subtractButton',
    icon: icons.function,
    tooltip: 'Functions',
    panelId: 'clickPanel'
  },
  {
    id: 'macroButton',
    icon: icons.chartBar,
    tooltip: 'Analytics',
    panelId: 'analyticsPanel'
  },
  {
    id: 'settingsButton',
    icon: icons.settings,
    tooltip: 'Settings',
    panelId: 'settingsPanel',
    active: true
  }
];

const defaultPanels: PanelConfig[] = [
  {
    id: 'trtcPanel',
    title: 'TRTC 配置',
    icon: icons.trtcLogo,
    content: createTRTCPanel()
  },
  {
    id: 'clickPanel',
    title: 'Functions',
    icon: icons.function,
    content: createFunctionsPanel()
  },
  {
    id: 'analyticsPanel',
    title: 'Analytics',
    icon: icons.chartBar,
    content: createAnalyticsPanel()
  },
  {
    id: 'settingsPanel',
    title: 'Settings',
    icon: icons.settings,
    content: createSettingsPanel()
  }
];

// ==================== 核心类 ====================

class DevToolsManager {
  private buttons: ButtonConfig[];
  private panels: PanelConfig[];
  private config: DevToolsConfig;
  private activePanel: string | null = null;
  private container: HTMLElement | null = null;
  private refreshTimer: number | null = null;
  private isRefreshing: boolean = false;
  private instanceColors: Map<string, string> = new Map();
  private selectedInstances: Set<string> = new Set(['all']);
  private allInstanceIds: Set<string> = new Set();
  private resizeCleanup?: () => void;

  constructor(config: DevToolsConfig = {}) {
    this.config = { position: 'bottom-center', theme: 'dark', ...config };
    this.buttons = [...defaultButtons];
    this.panels = [...defaultPanels];
  }

  init(): void {
    console.log('Initializing devtools...');
    
    // 注入样式
    this.injectStyles();
    
    // 创建隔离的容器
    this.createContainer();
    
    // 应用保存的主题
    const savedTheme = localStorage.getItem('trtc-devtools-theme') || 'dark';
    this.updateTheme(savedTheme);
    
    // 应用保存的位置设置
    const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
    setTimeout(() => {
      this.updateToolbarPosition(savedPlacement);
    }, 100);
    
    // 创建 DOM 结构
    this.createDOM();
    
    // 延迟绑定事件确保 DOM 完全就绪
    setTimeout(() => {
      this.setupEventListeners();
    }, 0);
    
    console.log('Devtools initialized successfully');
  }

  private injectStyles(): void {
    if (!document.getElementById('trtc-devtools-styles')) {
      document.head.insertAdjacentHTML('beforeend', createIsolatedStyles());
    }
  }

  private createContainer(): void {
    // 创建完全隔离的根容器
    this.container = document.createElement('div');
    this.container.className = DEVTOOLS_PREFIX;
    this.container.id = 'trtc-devtools-root';
    
    // 确保容器不受用户样式影响
    this.container.style.cssText = `
      all: unset !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      pointer-events: none !important;
      z-index: 2147483647 !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    `;
    
    document.body.appendChild(this.container);
  }

  private createDOM(): void {
    if (!this.container) return;
    
    // 创建面板和工具栏的HTML
    const panelsHTML = this.panels.map(panel => panel.content).join('');
    const toolbarHTML = createToolbar(this.buttons);
    
    this.container.innerHTML = panelsHTML + toolbarHTML;
    
    console.log('DOM created, container children:', this.container.children.length);
    console.log('Panels created:', this.panels.map(p => p.id));
    console.log('Buttons created:', this.buttons.map(b => b.id));
  }

  private setupEventListeners(): void {
    if (!this.container) return;
    
    console.log('Setting up event listeners...');
    
    // 初始化所有面板为隐藏状态
    this.panels.forEach(panel => {
      const element = this.container!.querySelector(`#${panel.id}`) as HTMLElement;
      if (element) {
        element.style.setProperty('display', 'none', 'important');
      }
    });
    
    // 为每个按钮绑定事件
    this.buttons.forEach(button => {
      const element = this.container!.querySelector(`#${button.id}`) as HTMLElement;
      const panel = this.container!.querySelector(`#${button.panelId}`) as HTMLElement;
      
      if (element && panel) {
        element.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log(`✅ ${button.tooltip} button clicked!`);
          this.togglePanel(button.panelId);
        });
        console.log(`✓ ${button.tooltip} button event listener added`);
      } else {
        console.error(`✗ ${button.tooltip} button or panel not found!`);
        console.error(`Element found: ${!!element}, Panel found: ${!!panel}`);
      }
    });

    // 全局点击事件 - 点击外部关闭面板
  document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const isDevToolsClick = target.closest(`#${this.container!.id}`);
      
      if (!isDevToolsClick) {
        this.hideAllPanels();
      }
    });

    // 设置面板特殊事件
    this.setupSettingsPanel();
    this.setupTRTCPanelLinks();
    this.setupFunctionsPanel();
    
    console.log('✅ All event listeners setup completed');
  }

  private getInstanceColor(instanceId: string): string {
    if (!this.instanceColors.has(instanceId)) {
      let color: string;
      
      if (instanceId === 'all') {
        // 为 "all" 按钮设置固定的柔和蓝色
        color = '#6B7DB5'; // 柔和的蓝色，饱和度较低
      } else {
        // 为其他实例生成随机颜色
        color = this.generateRandomColor();
      }
      
      this.instanceColors.set(instanceId, color);
    }
    
    return this.instanceColors.get(instanceId)!;
  }

  private generateRandomColor(): string {
    // 使用HSL颜色空间生成柔和的颜色
    const hue = Math.floor(Math.random() * 360); // 色相：0-360度
    const saturation = Math.floor(Math.random() * 20 + 35); // 饱和度：35-55%（降低了饱和度）
    const lightness = Math.floor(Math.random() * 25 + 50); // 亮度：50-75%
    
    // 将HSL转换为RGB
    const hslToRgb = (h: number, s: number, l: number) => {
      h /= 360;
      s /= 100;
      l /= 100;
      
      const a = s * Math.min(l, 1 - l);
      const f = (n: number) => {
        const k = (n + h * 12) % 12;
        return l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
      };
      
      return [
        Math.round(f(0) * 255),
        Math.round(f(8) * 255),
        Math.round(f(4) * 255)
      ];
    };
    
    const [r, g, b] = hslToRgb(hue, saturation, lightness);
    
    // 转换为十六进制
    const toHex = (value: number) => value.toString(16).padStart(2, '0');
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  private getInstanceDisplayName(instanceId: string): string {
    // 简化实例ID显示，例如只显示后6位
    if (instanceId === 'unknown') return 'Unknown';
    if (instanceId.length > 8) {
      return `...${instanceId.slice(-6)}`;
    }
    return instanceId;
  }

  private getContrastColor(backgroundColor: string): string {
    // 将十六进制颜色转换为RGB
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // 计算亮度 (使用相对亮度公式)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    // 根据亮度选择对比色
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  private getInstanceShortName(instanceId: string, index: number): string {
    // 为过滤按钮生成简短名称
    if (instanceId === 'unknown') return 'Unknown';
    return `t${index + 1}`;
  }

  private togglePanel(panelId: string): void {
    if (!this.container) return;
    
    const panel = this.container.querySelector(`#${panelId}`) as HTMLElement;
    if (!panel) {
      console.warn(`Panel ${panelId} not found!`);
      return;
    }

    // 检查当前面板是否可见
    const currentDisplay = panel.style.display;
    const isVisible = currentDisplay === 'block' || (currentDisplay === '' && this.activePanel === panelId);
    
    console.log(`Panel ${panelId} current state - display: ${currentDisplay}, activePanel: ${this.activePanel}, isVisible: ${isVisible}`);
    
    // 隐藏所有面板
    this.hideAllPanels();
    
    // 如果当前面板不可见，则显示它
    if (!isVisible) {
      // 检查是否启用全屏模式
      const isFullscreenEnabled = localStorage.getItem('trtc-devtools-fullscreen') === 'true';
      
      if (isFullscreenEnabled) {
        panel.classList.add('fullscreen');
        // 强制应用全屏样式
        panel.style.setProperty('top', '20px', 'important');
        panel.style.setProperty('bottom', '20px', 'important');
        panel.style.setProperty('left', '20px', 'important');
        panel.style.setProperty('right', '20px', 'important');
        panel.style.setProperty('width', 'auto', 'important');
        panel.style.setProperty('max-width', 'none', 'important');
        panel.style.setProperty('height', 'auto', 'important');
        panel.style.setProperty('transform', 'none', 'important');
      } else {
        panel.classList.remove('fullscreen');
        // 确保应用正常模式的位置
        const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
        setTimeout(() => this.updatePanelPosition(savedPlacement), 0);
      }
      
      panel.style.setProperty('display', 'block', 'important');
      this.activePanel = panelId;
      
      // 立即确保面板交互性，特别是在右上角等位置
      this.ensurePanelInteractivity(panel);
      
      console.log(`✅ Panel ${panelId} is now visible ${isFullscreenEnabled ? '(fullscreen)' : '(normal)'}`);
    } else {
      this.activePanel = null;
      console.log(`✅ Panel ${panelId} is now hidden`);
    }
  }

  private ensurePanelInteractivity(panel: HTMLElement): void {
    // 确保面板本身可交互
    panel.style.setProperty('pointer-events', 'auto', 'important');
          panel.style.setProperty('z-index', '2147483648', 'important');
    
    // 确保所有交互元素可点击
    const interactiveSelectors = [
      'button', 'input', 'select', 'a', 'label',
      '[onclick]', '[data-func-data]', '[data-instance]',
      '.trtc-refresh-btn', '.trtc-filter-btn', '.trtc-param-detail-btn',
      '.trtc-footer-link', '.trtc-toggle', '.trtc-toggle-bg', '.trtc-toggle-slider'
    ];
    
    interactiveSelectors.forEach(selector => {
      const elements = panel.querySelectorAll(selector);
      elements.forEach(el => {
        (el as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
        if (selector.includes('btn') || selector === 'button' || selector === 'a') {
          (el as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
        }
      });
    });
    
    console.log('Panel interactivity ensured for', panel.id);
  }

  private hideAllPanels(): void {
    if (!this.container) return;
    
    this.panels.forEach(panel => {
      const element = this.container!.querySelector(`#${panel.id}`) as HTMLElement;
      if (element) {
        element.style.setProperty('display', 'none', 'important');
        element.classList.remove('fullscreen');
      }
    });
    this.activePanel = null;
  }

  private setupSettingsPanel(): void {
    if (!this.container) return;
    
    const themeSelect = this.container.querySelector('#themeSelect') as HTMLSelectElement;
    const placementSelect = this.container.querySelector('#placementSelect') as HTMLSelectElement;
    const fullscreenToggle = this.container.querySelector('#fullscreenMode') as HTMLInputElement;

    // 从localStorage加载保存的主题设置
    const savedTheme = localStorage.getItem('trtc-devtools-theme') || 'dark';
    if (themeSelect) {
      themeSelect.value = savedTheme;
    }
    this.updateTheme(savedTheme);

    // 从localStorage加载保存的位置设置
    const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
    if (placementSelect) {
      placementSelect.value = savedPlacement;
    }

    // 从localStorage加载保存的全屏模式设置
    const savedFullscreen = localStorage.getItem('trtc-devtools-fullscreen') === 'true';
    if (fullscreenToggle) {
      fullscreenToggle.checked = savedFullscreen;
      this.updateToggleState(fullscreenToggle);
    }

    // 主题选择
    themeSelect?.addEventListener('change', (e) => {
      const theme = (e.target as HTMLSelectElement).value;
      this.updateTheme(theme);
      localStorage.setItem('trtc-devtools-theme', theme);
      console.log(`Theme changed to: ${theme}`);
    });

    // 位置选择
    placementSelect?.addEventListener('change', (e) => {
      const value = (e.target as HTMLSelectElement).value;
      this.updateToolbarPosition(value);
      localStorage.setItem('trtc-devtools-placement', value);
      console.log(`Toolbar placement changed to: ${value}`);
    });

    // 全屏模式切换
    if (fullscreenToggle) {
      fullscreenToggle.addEventListener('change', () => {
        const isFullscreen = fullscreenToggle.checked;
        this.updateToggleState(fullscreenToggle);
        localStorage.setItem('trtc-devtools-fullscreen', isFullscreen.toString());
        
        // 如果当前有面板正在显示，立即应用全屏模式变化
        this.applyFullscreenModeToActivePanel(isFullscreen);
        
        console.log(`Fullscreen mode: ${isFullscreen}`);
      });
    }
  }





  private setupTRTCPanelLinks(): void {
    if (!this.container) return;
    
    // 设置链接点击事件
    const settingsLink = this.container.querySelector('#trtcSettingsLink') as HTMLElement;
    if (settingsLink) {
      settingsLink.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        // 打开设置面板
        this.togglePanel('settingsPanel');
        console.log('Settings panel opened from TRTC panel');
      });
    }
  }

  private setupFunctionsPanel(): void {
    if (!this.container) return;
    
    // 刷新按钮事件
    const refreshBtn = this.container.querySelector('#refreshFunctions') as HTMLElement;
    if (refreshBtn) {
      refreshBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.manualRefresh();
      });
    }

    // 初始化数据
    this.updateFunctionsData();
    
    // 启动定期更新
    this.startAutoRefresh();
    
    // 监听窗口大小变化，响应式调整布局
    this.setupResponsiveLayout();
  }

  private startAutoRefresh(): void {
    this.stopAutoRefresh(); // 确保不会重复启动
    
    this.refreshTimer = window.setInterval(() => {
      if (!this.isRefreshing) {
        this.updateFunctionsData();
      }
    }, 2000);
  }

  private stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  private manualRefresh(): void {
    if (this.isRefreshing) return;
    
    this.isRefreshing = true;
    this.setRefreshingState(true);
    
    // 执行刷新
    this.updateFunctionsData();
    
    // 1秒后恢复正常状态（让用户看到刷新动画）
    setTimeout(() => {
      this.isRefreshing = false;
      this.setRefreshingState(false);
    }, 1000);
    
    console.log('Functions data manually refreshed');
  }

  private setRefreshingState(refreshing: boolean): void {
    if (!this.container) return;
    
    const refreshBtn = this.container.querySelector('#refreshFunctions') as HTMLElement;
    if (refreshBtn) {
      if (refreshing) {
        refreshBtn.classList.add('refreshing');
        refreshBtn.title = '正在刷新...';
      } else {
        refreshBtn.classList.remove('refreshing');
        refreshBtn.title = '手动刷新';
      }
    }
  }

  private updateFunctionsData(): void {
    if (!this.container) return;

    try {
      // 获取全局数据
      const devtoolsData = (window as any).__TRTC_DEVTOOLS_DATA__ || {};
      
      // 统计数据
      const instanceKeys = Object.keys(devtoolsData);
      const instanceCount = instanceKeys.length;
      let totalFunctionCalls = 0;
      let lastCallTime = 0;
      const allFunctions: any[] = [];
      const instanceStats = new Map<string, { count: number; lastCall: number }>();

      // 收集所有函数调用数据
      instanceKeys.forEach(key => {
        try {
          const instanceData = devtoolsData[key];
          if (instanceData && instanceData.functions && Array.isArray(instanceData.functions)) {
            let instanceCallCount = 0;
            let instanceLastCall = 0;

            instanceData.functions.forEach((func: any) => {
              if (func && typeof func === 'object') {
                totalFunctionCalls++;
                instanceCallCount++;
                allFunctions.push({
                  ...func,
                  instanceId: key,
                  // 确保必要的属性存在
                  startTimeStamp: func.startTimeStamp || Date.now(),
                  endTimeStamp: func.endTimeStamp || func.startTimeStamp || Date.now(),
                  name: func.name || 'unknown',
                  params: Array.isArray(func.params) ? func.params : []
                });
                
                const callTime = func.startTimeStamp || 0;
                if (callTime > lastCallTime) {
                  lastCallTime = callTime;
                }
                if (callTime > instanceLastCall) {
                  instanceLastCall = callTime;
                }
              }
            });

            if (instanceCallCount > 0) {
              instanceStats.set(key, { count: instanceCallCount, lastCall: instanceLastCall });
            }
          }
        } catch (error) {
          console.warn(`Error processing instance data for ${key}:`, error);
        }
      });

      // 按时间排序（最旧的在前，从上往下显示）
      allFunctions.sort((a, b) => a.startTimeStamp - b.startTimeStamp);

      // 更新统计信息
      const instanceCountEl = this.container.querySelector('#instanceCount') as HTMLElement;
      const functionCallCountEl = this.container.querySelector('#functionCallCount') as HTMLElement;
      const lastCallTimeEl = this.container.querySelector('#lastCallTime') as HTMLElement;

      if (instanceCountEl) instanceCountEl.textContent = instanceCount.toString();
      if (functionCallCountEl) functionCallCountEl.textContent = totalFunctionCalls.toString();
      if (lastCallTimeEl) {
        lastCallTimeEl.textContent = lastCallTime > 0 
          ? new Date(lastCallTime).toLocaleTimeString() 
          : '--';
      }

      // 更新实例过滤器
      this.updateInstancesFilter(instanceStats);

      // 更新时间线
      this.updateTimeline(allFunctions);

    } catch (error) {
      console.warn('Failed to update functions data:', error);
    }
  }

  private updateInstancesFilter(instanceStats: Map<string, { count: number; lastCall: number }>): void {
    if (!this.container) return;

    const instancesFilter = this.container.querySelector('#instancesFilter') as HTMLElement;
    const filterButtons = this.container.querySelector('#filterButtons') as HTMLElement;

    if (!instancesFilter || !filterButtons) return;

    if (instanceStats.size === 0) {
      instancesFilter.style.display = 'none';
      return;
    }

    instancesFilter.style.display = 'block';

    // 更新实例ID集合
    this.allInstanceIds.clear();
    instanceStats.forEach((_, instanceId) => {
      this.allInstanceIds.add(instanceId);
    });

    // 按最近调用时间排序
    const sortedInstances = Array.from(instanceStats.entries()).sort((a, b) => b[1].lastCall - a[1].lastCall);

    // 生成过滤按钮
    const allColor = this.getInstanceColor('all');
    const allTextColor = this.getContrastColor(allColor);
    const isAllActive = this.selectedInstances.has('all');
    
    let buttonsHTML = `
      <button class="trtc-filter-btn ${isAllActive ? 'active' : ''}" data-instance="all" style="--instance-color: ${allColor}; ${isAllActive ? `background-color: ${allColor} !important; color: ${allTextColor} !important; border-color: ${allColor} !important;` : ''}">
        <div class="trtc-filter-btn-dot" style="background-color: ${allColor};"></div>
        All
      </button>
    `;

    buttonsHTML += sortedInstances.map(([instanceId], index) => {
      const color = this.getInstanceColor(instanceId);
      const shortName = this.getInstanceShortName(instanceId, index);
      const isActive = this.selectedInstances.has(instanceId);
      const textColor = this.getContrastColor(color);
      
      return `
        <button class="trtc-filter-btn ${isActive ? 'active' : ''}" data-instance="${instanceId}" style="--instance-color: ${color}; ${isActive ? `background-color: ${color} !important; color: ${textColor} !important; border-color: ${color} !important;` : ''}">
          <div class="trtc-filter-btn-dot" style="background-color: ${color};"></div>
          ${shortName}
        </button>
      `;
    }).join('');

    filterButtons.innerHTML = buttonsHTML;

    // 绑定点击事件
    this.bindFilterEvents();
  }

  private bindFilterEvents(): void {
    if (!this.container) return;

    const filterButtons = this.container.querySelectorAll('.trtc-filter-btn[data-instance]');
    console.log(`Binding events to ${filterButtons.length} filter buttons`);
    
    filterButtons.forEach((button, index) => {
      const instanceId = (button as HTMLElement).dataset.instance;
      console.log(`Binding button ${index}: ${instanceId}`);
      
      // 移除之前的事件监听器
      button.removeEventListener('click', this.handleFilterClick);
      // 添加新的事件监听器
      button.addEventListener('click', this.handleFilterClick);
    });
  }

  private handleFilterClick = (e: Event): void => {
    e.preventDefault();
    e.stopPropagation();
    
    // 确保获取到正确的按钮元素
    let button = e.target as HTMLElement;
    
    // 如果点击的是按钮内的元素，向上查找按钮
    if (!button.dataset.instance) {
      button = button.closest('.trtc-filter-btn[data-instance]') as HTMLElement;
    }
    
    if (!button || !button.dataset.instance) {
      console.warn('Filter button not found');
      return;
    }
    
    const instanceId = button.dataset.instance;
    console.log('Filter clicked:', instanceId, 'Current selection:', Array.from(this.selectedInstances));

    if (instanceId === 'all') {
      // 点击 All 按钮 - 总是选中所有
      this.selectedInstances.clear();
      this.selectedInstances.add('all');
    } else {
      // 点击具体实例按钮
      if (this.selectedInstances.has('all')) {
        // 如果当前选中了 All，则取消 All 并选中当前实例
        this.selectedInstances.clear();
        this.selectedInstances.add(instanceId);
      } else {
        // 切换当前实例的选中状态
        if (this.selectedInstances.has(instanceId)) {
          this.selectedInstances.delete(instanceId);
          // 如果没有任何选中，则选中 All
          if (this.selectedInstances.size === 0) {
            this.selectedInstances.add('all');
          }
        } else {
          this.selectedInstances.add(instanceId);
        }
      }
    }

    console.log('After filter click:', Array.from(this.selectedInstances));

    // 更新按钮状态
    this.updateFilterButtonStates();
    
    // 重新渲染时间线
    this.refreshTimeline();
  };

  private updateFilterButtonStates(): void {
    if (!this.container) return;

    const filterButtons = this.container.querySelectorAll('.trtc-filter-btn[data-instance]');
    
    filterButtons.forEach(button => {
      const instanceId = (button as HTMLElement).dataset.instance;
      if (instanceId) {
        const isActive = this.selectedInstances.has(instanceId);
        
        if (isActive) {
          button.classList.add('active');
          
          // 为所有按钮（包括 "all"）应用实例颜色
          const color = this.getInstanceColor(instanceId);
          const textColor = this.getContrastColor(color);
          (button as HTMLElement).style.setProperty('background-color', color, 'important');
          (button as HTMLElement).style.setProperty('color', textColor, 'important');
          (button as HTMLElement).style.setProperty('border-color', color, 'important');
        } else {
          button.classList.remove('active');
          
          // 移除自定义样式，恢复默认样式
          (button as HTMLElement).style.removeProperty('background-color');
          (button as HTMLElement).style.removeProperty('color');
          (button as HTMLElement).style.removeProperty('border-color');
        }
      }
    });
  }

  private refreshTimeline(): void {
    // 重新获取和过滤数据并更新时间线
    this.updateFunctionsData();
  }

  private filterFunctionsByInstance(functions: any[]): any[] {
    if (this.selectedInstances.has('all')) {
      return functions;
    }
    
    console.log('Filtering functions. Selected instances:', Array.from(this.selectedInstances));
    console.log('Available function instances:', functions.map(f => f.instanceId));
    
    const filtered = functions.filter(func => {
      const match = this.selectedInstances.has(func.instanceId);
      if (!match) {
        console.log(`Function ${func.name} from instance ${func.instanceId} filtered out`);
      }
      return match;
    });
    
    console.log(`Filtered ${filtered.length} functions from ${functions.length} total`);
    return filtered;
  }

  private updateTimeline(functions: any[]): void {
    if (!this.container) return;

    const timelineContainer = this.container.querySelector('#functionsTimeline') as HTMLElement;
    if (!timelineContainer) return;

    // 根据选中的实例过滤函数
    const filteredFunctions = this.filterFunctionsByInstance(functions);

    if (filteredFunctions.length === 0) {
      // 显示空状态
      const emptyText = this.selectedInstances.has('all') 
        ? '暂无函数调用数据'
        : '选中的实例暂无函数调用数据';
      const emptyHint = this.selectedInstances.has('all')
        ? '开始使用 TRTC SDK 后将显示调用时间线'
        : '尝试选择其他实例或选择 "All" 查看所有数据';
      
      timelineContainer.innerHTML = `
        <div class="trtc-timeline-empty">
          <div class="trtc-empty-icon">📊</div>
          <div class="trtc-empty-text">${emptyText}</div>
          <div class="trtc-empty-hint">${emptyHint}</div>
        </div>
      `;
    } else {
      // 创建所有事件的时间线数组（包括START和END事件）
      const allEvents: any[] = [];
      
      filteredFunctions.slice(0, 50).forEach(func => {
        try {
          const functionName = func.name || 'unknown';
          const instanceId = func.instanceId || 'unknown';
          const startTime = func.startTimeStamp || Date.now();
          const endTime = func.endTimeStamp || startTime;
          const duration = Math.max(0, endTime - startTime);
          const isSync = startTime === endTime;
          
          // 生成预览参数
          const { previewParams, hasParams } = this.formatFunctionParams(func.params);
          
          // 存储完整函数数据用于浮窗显示
          const funcData = JSON.stringify({
            name: functionName,
            params: func.params || [],
            duration,
            instanceId,
            startTimeStamp: func.startTimeStamp,
            endTimeStamp: func.endTimeStamp
          }).replace(/"/g, '&quot;');

          const instanceColor = this.getInstanceColor(instanceId);
          const instanceDisplayName = this.getInstanceDisplayName(instanceId);
          const instanceTextColor = this.getContrastColor(instanceColor);

          if (isSync) {
            // 同步函数只添加一个 SYNC 事件
            allEvents.push({
              timestamp: startTime,
              type: 'sync',
              functionName,
              instanceId,
              instanceColor,
              instanceDisplayName,
              instanceTextColor,
              duration,
              previewParams,
              hasParams,
              funcData
            });
          } else {
            // 异步函数添加 START 和 END 事件
            allEvents.push({
              timestamp: startTime,
              type: 'start',
              functionName,
              instanceId,
              instanceColor,
              instanceDisplayName,
              instanceTextColor,
              duration,
              previewParams,
              hasParams,
              funcData
            });

            allEvents.push({
              timestamp: endTime,
              type: 'end',
              functionName,
              instanceId,
              instanceColor,
              instanceDisplayName,
              instanceTextColor,
              duration,
              previewParams,
              hasParams,
              funcData
            });
          }
        } catch (error) {
          console.warn('Error processing function for timeline:', error, 'func:', func);
        }
      });

      // 按时间戳严格排序所有事件
      allEvents.sort((a, b) => a.timestamp - b.timestamp);

      // 格式化时间显示的函数
      const formatTime = (timestamp: number) => {
        const date = new Date(timestamp);
        const now = new Date();
        const isToday = date.toDateString() === now.toDateString();
        
        if (isToday) {
          const time = date.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
          });
          const ms = Math.floor(date.getMilliseconds() / 10).toString().padStart(2, '0');
          return `${time}.${ms}`;
        } else {
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          const time = date.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit'
          });
          return `${month}/${day} ${time}`;
        }
      };

      // 生成时间线HTML
      const timelineHTML = allEvents.map((event, index) => {
        const formattedTime = formatTime(event.timestamp);
        const isLast = index === allEvents.length - 1;

        if (event.type === 'start') {
          return `
            <div class="trtc-event-item trtc-event-primary">
              <div class="trtc-event-time">${formattedTime}</div>
              <div class="trtc-event-indicator">
                <div class="trtc-event-dot start"></div>
                ${!isLast ? '<div class="trtc-event-line"></div>' : ''}
              </div>
              <div class="trtc-event-content">
                <div class="trtc-event-header">
                  <span class="trtc-event-action start">START</span>
                  <span class="trtc-event-function">${event.functionName}</span>
                  <span class="trtc-instance-badge" style="background-color: ${event.instanceColor}; color: ${event.instanceTextColor};">
                    <span class="trtc-instance-dot"></span>
                    ${event.instanceDisplayName}
                  </span>
                  ${event.hasParams ? `<span class="trtc-param-detail-btn" data-func-data="${event.funcData}" title="查看详细参数">🔍</span>` : ''}
                </div>
                <div class="trtc-event-details">
                  <span class="trtc-event-params">参数: ${event.previewParams}</span>
                </div>
              </div>
            </div>
          `;
        } else if (event.type === 'sync') {
          return `
            <div class="trtc-event-item trtc-event-sync">
              <div class="trtc-event-time">${formattedTime}</div>
              <div class="trtc-event-indicator">
                <div class="trtc-event-dot sync"></div>
                ${!isLast ? '<div class="trtc-event-line"></div>' : ''}
              </div>
              <div class="trtc-event-content">
                <div class="trtc-event-header">
                  <span class="trtc-event-action sync">SYNC</span>
                  <span class="trtc-event-function">${event.functionName}</span>
                  <span class="trtc-instance-badge" style="background-color: ${event.instanceColor}; color: ${event.instanceTextColor};">
                    <span class="trtc-instance-dot"></span>
                    ${event.instanceDisplayName}
                  </span>
                  ${event.hasParams ? `<span class="trtc-param-detail-btn" data-func-data="${event.funcData}" title="查看详细参数">🔍</span>` : ''}
                </div>
                <div class="trtc-event-details">
                  <span class="trtc-event-params">参数: ${event.previewParams}</span> • 
                  <span class="trtc-event-sync-note">同步执行</span>
                </div>
              </div>
            </div>
          `;
        } else {
          return `
            <div class="trtc-event-item trtc-event-secondary">
              <div class="trtc-event-time">${formattedTime}</div>
              <div class="trtc-event-indicator">
                <div class="trtc-event-dot end"></div>
                ${!isLast ? '<div class="trtc-event-line"></div>' : ''}
              </div>
              <div class="trtc-event-content">
                <div class="trtc-event-header">
                  <span class="trtc-event-action end">END</span>
                  <span class="trtc-event-function">${event.functionName}</span>
                  <span class="trtc-instance-badge" style="background-color: ${event.instanceColor}; color: ${event.instanceTextColor}; opacity: 0.7;">
                    <span class="trtc-instance-dot"></span>
                    ${event.instanceDisplayName}
                  </span>
                  <span class="trtc-event-duration">${event.duration}ms</span>
                </div>
                <div class="trtc-event-details">
                  <span class="trtc-event-instance">执行完成</span>
                </div>
              </div>
            </div>
          `;
        }
      }).join('');

      timelineContainer.innerHTML = timelineHTML;
      
      // 绑定参数详情事件
      this.bindParamDetailEvents();
    }
  }

  private formatFunctionParams(params: any): { previewParams: string; hasParams: boolean } {
    // 检查 params 是否是有效的数组
    if (!params || !Array.isArray(params) || params.length === 0) {
      return { previewParams: '()', hasParams: false };
    }

    try {
      // 生成预览参数
      let previewParams = '';
      if (params.length <= 2) {
        // 少于等于2个参数，显示简化版本
        const formattedParams = params.map(param => this.formatParamValue(param, false)).join(', ');
        previewParams = `(${formattedParams})`;
        return { previewParams, hasParams: params.length > 0 };
      } else {
        // 超过2个参数，只显示前2个 + 省略号
        const formattedParams = params.slice(0, 2).map(param => this.formatParamValue(param, false));
        previewParams = `(${formattedParams.join(', ')}, +${params.length - 2})`;
        return { previewParams, hasParams: true };
      }
    } catch (error) {
      console.warn('Error formatting function params:', error, 'params:', params);
      return { previewParams: '([Error])', hasParams: false };
    }
  }

  private formatParamValue(param: any, isDetailMode: boolean = false): string {
    try {
      if (param === null) {
        return 'null';
      } else if (param === undefined) {
        return 'undefined';
      } else if (typeof param === 'string') {
        const maxLength = isDetailMode ? 100 : 15;
        return param.length > maxLength ? `"${param.substring(0, maxLength)}..."` : `"${param}"`;
      } else if (typeof param === 'number' || typeof param === 'boolean') {
        return String(param);
      } else if (typeof param === 'function') {
        return 'function()';
      } else if (Array.isArray(param)) {
        if (isDetailMode && param.length <= 5) {
          try {
            return JSON.stringify(param, null, 2);
          } catch {
            return `Array(${param.length})`;
          }
        }
        return `Array(${param.length})`;
      } else if (typeof param === 'object') {
        const keys = Object.keys(param);
        if (keys.length === 0) {
          return '{}';
        } else if (isDetailMode) {
          try {
            return JSON.stringify(param, null, 2);
          } catch {
            return `{${keys.join(', ')}}`;
          }
        } else if (keys.length <= 2) {
          try {
            const str = JSON.stringify(param);
            return str.length > 30 ? `{${keys.join(', ')}}` : str;
          } catch {
            return `{${keys.join(', ')}}`;
          }
        } else {
          return `{${keys.slice(0, 2).join(', ')}, +${keys.length - 2}}`;
        }
      } else {
        return String(param);
      }
    } catch (error) {
      return '[Error]';
    }
  }

  private bindParamDetailEvents(): void {
    if (!this.container) return;

    try {
      // 创建浮窗
      this.createParamModal();
      
      // 绑定详情按钮点击事件
      const detailButtons = this.container.querySelectorAll('.trtc-param-detail-btn[data-func-data]');
      detailButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          try {
            e.stopPropagation();
            const funcDataStr = (button as HTMLElement).dataset.funcData;
            if (!funcDataStr) return;

            const funcData = JSON.parse(funcDataStr.replace(/&quot;/g, '"'));
            this.showParamModal(funcData);
          } catch (error) {
            console.warn('Error handling param detail click:', error);
          }
        });
      });
    } catch (error) {
      console.warn('Error binding param detail events:', error);
    }
  }

  private createParamModal(): void {
    if (!this.container) return;
    
    // 检查是否已存在浮窗
    if (this.container.querySelector('.trtc-param-modal')) return;

    const modalHTML = `
      <div class="trtc-param-modal" id="trtcParamModal">
        <div class="trtc-param-modal-content">
          <div class="trtc-param-modal-header">
            <div class="trtc-param-modal-title">函数参数详情</div>
            <div class="trtc-param-modal-close" id="trtcParamModalClose">×</div>
          </div>
          <div class="trtc-param-modal-body" id="trtcParamModalBody">
            <!-- 动态内容 -->
          </div>
        </div>
      </div>
    `;

    this.container.insertAdjacentHTML('beforeend', modalHTML);

    // 绑定关闭事件
    const modal = this.container.querySelector('#trtcParamModal') as HTMLElement;
    const closeBtn = this.container.querySelector('#trtcParamModalClose') as HTMLElement;

    if (modal && closeBtn) {
      // 点击关闭按钮
      closeBtn.addEventListener('click', () => {
        modal.classList.remove('show');
      });

      // 点击遮罩层关闭
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.remove('show');
        }
      });

      // ESC键关闭
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
          modal.classList.remove('show');
        }
      });
    }
  }

  private showParamModal(funcData: any): void {
    if (!this.container) return;

    const modal = this.container.querySelector('#trtcParamModal') as HTMLElement;
    const modalBody = this.container.querySelector('#trtcParamModalBody') as HTMLElement;

    if (!modal || !modalBody) return;

    try {
      // 生成函数签名
      const signature = this.generateFunctionSignature(funcData);
      
      // 生成参数列表
      const paramsList = this.generateParamsList(funcData.params || []);

      // 生成执行信息
      const executionInfo = this.generateExecutionInfo(funcData);

      modalBody.innerHTML = `
        <div class="trtc-function-signature">${signature}</div>
        
        ${paramsList}
        
        ${executionInfo}
      `;

      modal.classList.add('show');
    } catch (error) {
      console.warn('Error showing param modal:', error);
      modalBody.innerHTML = '<div style="color: var(--trtc-text-secondary);">参数显示出错</div>';
      modal.classList.add('show');
    }
  }

  private generateFunctionSignature(funcData: any): string {
    const params = funcData.params || [];
    const paramTypes = params.map((param: any, index: number) => {
      const type = this.getParamType(param);
      return `param${index}: ${type}`;
    });
    
    return `${funcData.name}(${paramTypes.join(', ')})`;
  }

  private generateParamsList(params: any[]): string {
    if (!params || params.length === 0) {
      return `
        <div class="trtc-param-section">
          <div class="trtc-param-section-title">参数 (0)</div>
          <div style="color: var(--trtc-text-muted); text-align: center; padding: 20px;">此函数没有参数</div>
        </div>
      `;
    }

    const paramsHTML = params.map((param, index) => {
      const type = this.getParamType(param);
      const formattedValue = this.formatParamValue(param, true);
      
      return `
        <div class="trtc-param-item-modal">
          <div class="trtc-param-item-header">
            <span class="trtc-param-index-modal">[${index}]</span>
            <span class="trtc-param-type">${type}</span>
          </div>
          <div class="trtc-param-value-modal">${formattedValue}</div>
        </div>
      `;
    }).join('');

    return `
      <div class="trtc-param-section">
        <div class="trtc-param-section-title">参数 (${params.length})</div>
        <div class="trtc-param-list">
          ${paramsHTML}
        </div>
      </div>
    `;
  }

  private generateExecutionInfo(funcData: any): string {
    const startTimeObj = new Date(funcData.startTimeStamp || Date.now());
    const endTimeObj = new Date(funcData.endTimeStamp || Date.now());
    
    // 格式化时间，显示更精确的时分秒毫秒
    const formatTime = (date: Date) => {
      const time = date.toLocaleTimeString('zh-CN', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
      });
      const ms = date.getMilliseconds().toString().padStart(3, '0');
      return `${time}.${ms}`;
    };
    
    const startTime = formatTime(startTimeObj);
    const endTime = formatTime(endTimeObj);
    const dateStr = startTimeObj.toLocaleDateString('zh-CN');
    
    return `
      <div class="trtc-param-section">
        <div class="trtc-param-section-title">执行信息</div>
        
        <div class="trtc-param-item-modal">
          <div style="display: grid; grid-template-columns: 80px 1fr; gap: 8px; margin-bottom: 16px; color: var(--trtc-text-secondary);">
            <span>函数名:</span><span style="color: var(--trtc-text-primary);">${funcData.name}</span>
            <span>执行时长:</span><span style="color: var(--trtc-accent-color); font-weight: 600;">${funcData.duration}ms</span>
            <span>实例ID:</span><span style="color: var(--trtc-text-primary);">${funcData.instanceId}</span>
            <span>日期:</span><span style="color: var(--trtc-text-primary);">${dateStr}</span>
          </div>
        </div>

        <div class="trtc-param-item-modal">
          <div style="border-left: 3px solid var(--trtc-success-color); padding-left: 12px; margin-bottom: 12px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
              <span style="color: var(--trtc-success-color); font-size: 11px; font-weight: 600;">▶ 开始执行</span>
            </div>
            <div style="color: var(--trtc-text-primary); font-family: 'SF Mono', Monaco, monospace; font-size: 13px; font-weight: 500;">
              ${startTime}
            </div>
          </div>
          
          <div style="border-left: 3px solid var(--trtc-danger-color); padding-left: 12px;">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
              <span style="color: var(--trtc-danger-color); font-size: 11px; font-weight: 600;">◀ 执行完成</span>
            </div>
            <div style="color: var(--trtc-text-primary); font-family: 'SF Mono', Monaco, monospace; font-size: 13px; font-weight: 500;">
              ${endTime}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private getParamType(param: any): string {
    if (param === null) return 'null';
    if (param === undefined) return 'undefined';
    if (Array.isArray(param)) return `Array[${param.length}]`;
    if (typeof param === 'object') return 'Object';
    return typeof param;
  }

  private updateTheme(theme: string): void {
    if (!this.container) return;
    
    // 切换主题类名
    if (theme === 'light') {
      this.container.classList.add('light-theme');
    } else {
      this.container.classList.remove('light-theme');
    }
    
    console.log(`Theme updated to: ${theme}`);
  }

  private updateToggleState(toggle: HTMLInputElement): void {
    if (!toggle) return;
    
    const background = toggle.nextElementSibling as HTMLElement; // trtc-toggle-bg
    const slider = toggle.nextElementSibling?.nextElementSibling as HTMLElement; // trtc-toggle-slider
    
    if (slider && background) {
      if (toggle.checked) {
        slider.style.setProperty('transform', 'translateX(24px)', 'important');
        background.style.setProperty('background', '#4CAF50', 'important');
      } else {
        slider.style.setProperty('transform', 'translateX(0)', 'important');
        background.style.setProperty('background', 'var(--trtc-toggle-bg)', 'important');
      }
    }
  }

  private updateToolbarPosition(position: string): void {
    if (!this.container) return;
    
    const toolbar = this.container.querySelector('.trtc-toolbar') as HTMLElement;
    if (!toolbar) return;

    // 重置所有位置样式 - 使用 !important 确保覆盖
    toolbar.style.setProperty('top', '', 'important');
    toolbar.style.setProperty('bottom', '', 'important');
    toolbar.style.setProperty('left', '', 'important');
    toolbar.style.setProperty('right', '', 'important');
    toolbar.style.setProperty('transform', '', 'important');
    toolbar.style.setProperty('width', '', 'important');

    // 根据位置设置样式
    switch (position) {
      case 'bottom-left':
        toolbar.style.setProperty('bottom', '20px', 'important');
        toolbar.style.setProperty('left', '20px', 'important');
        toolbar.style.setProperty('transform', 'none', 'important');
        break;
      case 'bottom-right':
        // 使用 flex 容器右对齐的方式
        toolbar.style.setProperty('bottom', '20px', 'important');
        toolbar.style.setProperty('right', '20px', 'important');
        toolbar.style.setProperty('left', 'auto', 'important');
        toolbar.style.setProperty('transform', 'none', 'important');
        toolbar.style.setProperty('width', 'auto', 'important');
        break;
      case 'bottom-center':
      default:
        toolbar.style.setProperty('bottom', '20px', 'important');
        toolbar.style.setProperty('left', '50%', 'important');
        toolbar.style.setProperty('transform', 'translateX(-50%)', 'important');
        break;
    }
    
    // 同时更新面板的位置
    this.updatePanelPosition(position);
    
    console.log(`Toolbar position updated to: ${position}`);
  }

  private updatePanelPosition(toolbarPosition: string): void {
    if (!this.container) return;

    // 获取所有非全屏面板
    const normalPanels = this.container.querySelectorAll('.trtc-panel:not(.fullscreen)') as NodeListOf<HTMLElement>;
    
    // 只对非全屏面板应用智能布局
    if (normalPanels.length > 0) {
      // 智能计算面板位置和尺寸
      const panelLayout = this.calculateSmartPanelLayout(toolbarPosition);

      // 应用面板位置样式
      normalPanels.forEach(panel => {
        // 重置所有位置和尺寸样式
        this.resetPanelStyles(panel);

        // 确保面板交互性
        this.ensurePanelInteractivity(panel);

        // 应用新的布局
        Object.entries(panelLayout).forEach(([property, value]) => {
          if (value) {
            panel.style.setProperty(property, value, 'important');
          }
        });
      });

      console.log(`Smart panel layout applied for toolbar position: ${toolbarPosition}`, panelLayout);
    }

    // 确保全屏面板的样式不被覆盖
    const fullscreenPanels = this.container.querySelectorAll('.trtc-panel.fullscreen') as NodeListOf<HTMLElement>;
    fullscreenPanels.forEach(panel => {
      // 重新确保全屏样式
      panel.style.setProperty('top', '20px', 'important');
      panel.style.setProperty('bottom', '20px', 'important');
      panel.style.setProperty('left', '20px', 'important');
      panel.style.setProperty('right', '20px', 'important');
      panel.style.setProperty('width', 'auto', 'important');
      panel.style.setProperty('max-width', 'none', 'important');
      panel.style.setProperty('height', 'auto', 'important');
      panel.style.setProperty('transform', 'none', 'important');
      
      // 确保面板交互性
      this.ensurePanelInteractivity(panel);
    });
  }

  private calculateSmartPanelLayout(toolbarPosition: string): Record<string, string> {
    // 获取视口尺寸
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 工具栏尺寸估算（大约120px宽，40px高）
    const toolbarWidth = 120;
    const toolbarHeight = 40;
    
    // 面板理想尺寸
    const idealPanelWidth = 520;
    const panelPadding = 20; // 距离屏幕边缘的最小距离
    const toolbarGap = 20; // 距离工具栏的最小距离

    let layout: Record<string, string> = {};

    switch (toolbarPosition) {
      case 'bottom-left':
        // 工具栏在左下角，面板放在右侧或上方
        if (viewportWidth > idealPanelWidth + toolbarWidth + toolbarGap + panelPadding * 2) {
          // 屏幕够宽，面板放在工具栏右侧
          layout = {
            bottom: `${panelPadding}px`,
            left: `${toolbarWidth + toolbarGap + panelPadding}px`,
            width: `min(${idealPanelWidth}px, calc(100vw - ${toolbarWidth + toolbarGap + panelPadding * 2}px))`,
            maxHeight: `calc(100vh - ${panelPadding * 2}px)`,
            transform: 'none'
          };
        } else {
          // 屏幕较窄，面板放在工具栏上方
          layout = {
            bottom: `${toolbarHeight + toolbarGap + panelPadding}px`,
            left: `${panelPadding}px`,
            width: `min(${idealPanelWidth}px, calc(100vw - ${panelPadding * 2}px))`,
            maxHeight: `calc(100vh - ${toolbarHeight + toolbarGap + panelPadding * 2}px)`,
            transform: 'none'
          };
        }
        break;

      case 'bottom-right':
        // 工具栏在右下角，面板放在左侧或上方
        if (viewportWidth > idealPanelWidth + toolbarWidth + toolbarGap + panelPadding * 2) {
          // 屏幕够宽，面板放在工具栏左侧
          layout = {
            bottom: `${panelPadding}px`,
            right: `${toolbarWidth + toolbarGap + panelPadding}px`,
            width: `min(${idealPanelWidth}px, calc(100vw - ${toolbarWidth + toolbarGap + panelPadding * 2}px))`,
            maxHeight: `calc(100vh - ${panelPadding * 2}px)`,
            transform: 'none'
          };
        } else {
          // 屏幕较窄，面板放在工具栏上方
          layout = {
            bottom: `${toolbarHeight + toolbarGap + panelPadding}px`,
            right: `${panelPadding}px`,
            width: `min(${idealPanelWidth}px, calc(100vw - ${panelPadding * 2}px))`,
            maxHeight: `calc(100vh - ${toolbarHeight + toolbarGap + panelPadding * 2}px)`,
            transform: 'none'
          };
        }
        break;

      default: // 'bottom-center'
        // 工具栏在底部中央，面板居中显示在上方
        layout = {
          bottom: `${toolbarHeight + toolbarGap + panelPadding}px`,
          left: '50%',
          width: `min(${idealPanelWidth}px, calc(100vw - ${panelPadding * 2}px))`,
          maxHeight: `calc(100vh - ${toolbarHeight + toolbarGap + panelPadding * 2}px)`,
          transform: 'translateX(-50%)'
        };
        break;
    }

    return layout;
  }

  private resetPanelStyles(panel: HTMLElement): void {
    // 重置所有位置和尺寸样式
    const resetProperties = ['top', 'bottom', 'left', 'right', 'transform', 'width', 'max-width', 'max-height'];
    resetProperties.forEach(prop => {
      panel.style.setProperty(prop, '', 'important');
    });
  }

  private setupResponsiveLayout(): void {
    // 防抖处理窗口大小变化
    let resizeTimer: number;
    
    const handleResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = window.setTimeout(() => {
        // 重新计算布局
        const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
        this.updatePanelPosition(savedPlacement);
        console.log('Responsive layout updated on window resize');
      }, 150);
    };

    window.addEventListener('resize', handleResize);
    
    // 存储清理函数
    this.resizeCleanup = () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimer);
    };
  }

  private applyFullscreenModeToActivePanel(isFullscreen: boolean): void {
    if (!this.container || !this.activePanel) return;
    
    const activePanel = this.container.querySelector(`#${this.activePanel}`) as HTMLElement;
    if (!activePanel) return;
    
    // 检查面板是否当前可见
    const currentDisplay = activePanel.style.display;
    const isVisible = currentDisplay === 'block';
    
    if (isVisible) {
      if (isFullscreen) {
        activePanel.classList.add('fullscreen');
        // 强制应用全屏样式，清除可能的宽度限制
        activePanel.style.setProperty('top', '20px', 'important');
        activePanel.style.setProperty('bottom', '20px', 'important');
        activePanel.style.setProperty('left', '20px', 'important');
        activePanel.style.setProperty('right', '20px', 'important');
        activePanel.style.setProperty('width', 'auto', 'important');
        activePanel.style.setProperty('max-width', 'none', 'important');
        activePanel.style.setProperty('height', 'auto', 'important');
        activePanel.style.setProperty('transform', 'none', 'important');
        console.log(`Applied fullscreen mode to active panel: ${this.activePanel}`);
      } else {
        activePanel.classList.remove('fullscreen');
        // 重新应用普通模式的位置
        const savedPlacement = localStorage.getItem('trtc-devtools-placement') || 'bottom-center';
        this.updatePanelPosition(savedPlacement);
        console.log(`Removed fullscreen mode from active panel: ${this.activePanel}`);
      }
    }
  }

  // 清理方法 - 用于完全移除DevTools
  destroy(): void {
    // 停止定时器
    this.stopAutoRefresh();
    
    // 清理响应式布局监听器
    if (this.resizeCleanup) {
      this.resizeCleanup();
      this.resizeCleanup = undefined;
    }
    
    if (this.container) {
      this.container.remove();
      this.container = null;
    }
    
    const styles = document.getElementById('trtc-devtools-styles');
    if (styles) {
      styles.remove();
    }
    
    console.log('DevTools destroyed and cleaned up');
  }
}

// ==================== 导出的主函数 ====================

export function initDevtools(config?: DevToolsConfig): void {
  const devtools = new DevToolsManager(config);
  devtools.init();
}