import puppeteer from 'puppeteer';

async function testPage() {
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox']
  });

  try {
    const page = await browser.newPage();

    // Capture console logs
    page.on('console', msg => console.log('Browser Console:', msg.text()));
    page.on('pageerror', error => console.log('Browser Error:', error.message));

    await page.goto('http://localhost:8081');

    // Wait for network idle to ensure all resources are loaded
    await page.waitForNetworkIdle();

    // Wait a bit to capture any async logs
    await new Promise(resolve => setTimeout(resolve, 2000));

  } catch (error) {
    console.log('Test failed:', error);
  } finally {
    await browser.close();
  }
}

testPage(); 