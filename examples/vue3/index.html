<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TRTC Demo</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="/lib-generate-test-usersig.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .button-container {
      position: fixed;
      top: 16px;
      right: -5px;
      display: flex;
      align-items: center;
      z-index: 1000;
      transform: translateX(calc(100% - 24px));
      transition: transform 0.3s ease-in-out;
    }

    .button-container.show {
      transform: translateX(-20px);
    }

    .toggle-button {
      width: 16px;
      height: 24px;
      background-color: transparent;
      border: none;
      padding: 0;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      font-size: 14px;
      font-weight: bold;
    }

    .toggle-button:hover {
      color: #333;
    }

    .top-right-buttons {
      display: flex;
      gap: 8px;
    }

    .top-right-buttons button {
      background-color: #fff; 
      border: 1px solid #ccc;
      border-radius: 4px;  
      padding: 8px 12px;  
      cursor: pointer;   
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); 
      transition: background-color 0.2s, box-shadow 0.2s;
      position: relative;
    }

    .top-right-buttons button:hover {
      background-color: #f0f0f0;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
    }
    
    .tooltip {
      position: absolute;
      background-color: #333;
      color: #fff;
      text-align: center;
      padding: 10px;
      border-radius: 4px;
      z-index: 1001;
      font-size: 14px;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
      top: 125%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 5px;
    }
    
    .tooltip::after {
      content: '';
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -5px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent #333 transparent;
    }
    
    .top-right-buttons button:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }
  </style>
</head>

<body>
  <div id="TRTC" style="display: flex;">  
  </div>

  <div class="button-container" id="multipleInstances">
    <button id="toggleButton" class="toggle-button">&lt;</button>
    <div class="top-right-buttons">
      <button id="addButton"><i class="fas fa-plus"></i><span class="tooltip">添加实例</span></button>
      <button id="subtractButton"><i class="fas fa-minus"></i><span class="tooltip">删除实例</span></button>
      <button id="macroButton"><i class="fas fa-layer-group"></i><span class="tooltip">宏组合</span></button>
    </div>
  </div>

  <script type="module" src="/src/main.ts"></script>
  <video id="music_A" src="testA.mp4" loop controls style="display: none;"></video>
  <script>
    class StreamGenerator {
      constructor(options) {
        this.id = options.id;
        this.video_ = document.querySelector(this.id);
        this.mediaStream = this.video_.captureStream();
        // this.canvas_ = document.createElement('canvas');
        // this.canvas_.width = 640;
        // this.canvas_.height = 480;
        // this.canvasCtx_ = this.canvas_.getContext('2d');
      }

      generateVideoTrack() {
        return this.mediaStream.getVideoTracks()[0];
        // const stream = this.canvas_.captureStream();
        // this.render();
        // return stream.getVideoTracks()[0];
      }

      generateAudioTrack() {
        return this.mediaStream.getAudioTracks()[0];
      }

      render() {
        if (this.canDrawVideoToCanvas) {
          this.canvasCtx_.drawImage(this.video_, 0, 0, 640, 480);
        }
        requestAnimationFrame(this.render.bind(this));
      }

      get canDrawVideoToCanvas() {
        return this.video_.readyState === this.video_.HAVE_ENOUGH_DATA;
      }
    }
    const streamGenerator = new StreamGenerator({
      id: '#music_A'
    });

    const toggleButton = document.getElementById('toggleButton');
    const buttonContainer = document.querySelector('.button-container');
    let isExpanded = false;
    toggleButton?.addEventListener('click', () => {
      isExpanded = !isExpanded;
      if (isExpanded) {
        buttonContainer.classList.add('show');
        toggleButton.innerHTML = '&gt;';
      } else {
        buttonContainer.classList.remove('show');
        toggleButton.innerHTML = '&lt;';
      }
    });
  </script>
</body>

</html>