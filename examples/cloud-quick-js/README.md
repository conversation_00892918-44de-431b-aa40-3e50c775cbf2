This document describes how to run the TRTC web quick demo (Vanilla js).

English | [简体中文](./README.zh.md)

## Online Demo

We offer an [online web demo (Vanilla js)](https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/quick-demo-js/index.html) for you to experience TRTC calling capability.

Enter your application’s `SDKAppID` and `SDKS<PERSON>retKey` on the webpage to enter a room. For how to get the `SDKAppID` and `SDKSecretKey`, see [TRTC RTC Engine](https://console.trtc.io/engine).

When entered the room, you can use the share link to invite others to try the audio/video call feature with you.

## How to Run

1. Clone this repository

```bash
git clone https://github.com/LiteAVSDK/TRTC_Web.git
```

2. Enter this folder

```bash
cd ./TRTC_Web/quick-demo-js
```

3. Open the `index.html` file in the Demo root directory with Chrome browser to run the Demo.

4. Enter the `SDKAppID` and `SDKSecretKey`. For detailed directions, see [TRTC RTC Engine](https://console.trtc.io/engine).

#### Try the demo

- Input userId and roomId
- Click the "Enter Room" button to enter the room
- Click the "Start Local Audio/Video" button to capture microphone or camera
- Click the "Stop Local Audio/Video" button to stop capturing microphone or camera
- Click the "Start Share Screen" button to start screen sharing
- Click the "Stop Share Screen" button to stop screen sharing
