<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="https://web.sdk.qcloud.com/trtc/webrtc/assets/trtcio48.avif">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>

  <!-- Open Graph Meta Tags -->
  <meta property="og:url" content="https://web.sdk.qcloud.com/trtc/webrtc/v5/test/qer/og/cloud-quick-vue2/index.html">
  <meta property="og:type" content="website">
  <meta property="og:title" content="Quick Demo Vue2 | Tencent RTC">
  <meta property="og:description" content="This demo uses Vue2 to quickly test essential Tencent RTC features.">
  <meta property="og:image" content="https://web.sdk.qcloud.com/trtc/webrtc/assets/og-image-vue2.png">

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta property="twitter:domain" content="web.sdk.qcloud.com">
  <meta property="twitter:url" content="https://web.sdk.qcloud.com/trtc/webrtc/v5/test/qer/og/cloud-quick-vue2/index.html">
  <meta name="twitter:title" content="Quick Demo Vue2 | Tencent RTC">
  <meta name="twitter:description" content="This demo uses Vue2 to quickly test essential Tencent RTC features.">
  <meta name="twitter:image" content="https://web.sdk.qcloud.com/trtc/webrtc/assets/og-image-vue2.png">
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
  <script>
    var ua = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipad|phone/i.test(ua)) {
      var vConsole = new window.VConsole();
    }
  </script>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>