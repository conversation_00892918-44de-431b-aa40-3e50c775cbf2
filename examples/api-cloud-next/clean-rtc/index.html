<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script src="../dist/trtc.js"></script>
  <script src="../dist/virtual-background.iife.js"></script>
  <button onclick="preload()"> 预加载虚拟背景 </button> <br>
  <button onclick="start()"> 开启虚拟背景 </button> <br>
  <div id="local-player" style="width: 640px; height: 480px;"></div>
</body>

</html>

<script>
  // const trtc = TRTC.create();
  const trtc = TRTC.create({ plugins: [VirtualBackground] });

  async function preload() {
    trtc.use(VirtualBackground);
  }

  async function start() {
    await trtc.startLocalVideo({ view: document.querySelector("#local-player") });
    trtc.startPlugin('VirtualBackground', { sdkAppId: 1400188366, userId: 'u97249542', userSig: 'eJw1jV0LgjAYRv*K7Dpkm**cBl1IhBAighHU3WDLXsqYH5Mo*u*p2e15OM95k0NW*h1WZO0RWw6NkYnNjtd9wdJXgrkSlQt2Stji3tQuT7fnkzaPEjZk5c2qeVpszWgzIQSnlC680zdlLeppAUpZFAVhuGyDaacc9*n-pcd6-pBMSgBg0cJxjPV4wZ-gYskhFsDJ5wtwszNW' })
  }

</script>