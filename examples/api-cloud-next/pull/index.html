<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TRTC 拉流</title>
</head>

<body style="margin: 10px;">
  <hr>
  进房信息:
  <div style="display: flex; flex-direction: column;">
  </div>
  <input type="number" id="sdk-app-id" placeholder="sdkAppId">
  <input type="password" id="secret-key" placeholder="secretKey" >
  <input type="text" id="user-id" placeholder="userId">
  <input type="text" id="room-id" placeholder="roomId">
  <hr>
  <button id="enter"> 进房并自动拉流 </button>
  <button id="exit"> 退房 </button>
  <hr>
  当前房间的远程流:
  <div id="local_stream" style="min-height: 320px; max-width: 640px; width: 100%; border: 1px solid black;"></div>
</body>

</html>

<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script>
  var vConsole = new window.VConsole();
</script>

<script src="./lib-generate-test-usersig.min.js"></script>
<script src="./trtc.js"></script>

<script type="module">
  const sdkAppIDElement = document.getElementById('sdk-app-id');
  const secretKeyElement = document.getElementById('secret-key');
  const userIdElement = document.getElementById('user-id');
  const userSigElement = document.getElementById('user-sig');
  const roomIdElement = document.getElementById('room-id');
  const urlParam = new URLSearchParams(window.location.search);

  document.getElementById('enter').addEventListener('click', handleEnterRoom);
  document.getElementById('exit').addEventListener('click', handleExitRoom);

  const trtc = TRTC.create();
  window.trtc = trtc;
  initParams();
  if (urlParam.has('autoEnter')) {
    handleEnterRoom();
  }

  function initParams() {
    const sdkAppId = Number(urlParam.get('sdkAppId'));
    const secretKey = urlParam.get('secretKey');
    const userId = urlParam.get('userId');
    const roomId = Number(urlParam.get('roomId'));
    console.log('sdkAppId:', sdkAppId, 'userId:', userId, 'roomId:', roomId);

    sdkAppIDElement.value = sdkAppId || '';
    userIdElement.value = userId || 'user_' + Math.random().toString(36).substr(2, 6);
    roomIdElement.value = roomId || Math.floor(Math.random() * 1000000);
    secretKeyElement.value = secretKey || '';
  }

  async function handleEnterRoom() {
    try {
      const sdkAppId = Number(sdkAppIDElement.value);
      const secretKey = secretKeyElement.value;
      const userId = userIdElement.value;
      const roomId = Number(roomIdElement.value);
      // 下面一行是前端生成 userSig，如果是正式服务，需要转换为后端生成增强安全性
      const generator = new LibGenerateTestUserSig(sdkAppId, secretKey, 604800);
      const userSig = generator.genTestUserSig(userId);

      trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, addVideoView);
      trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, removeVideoView);
      await trtc.enterRoom({ sdkAppId, userId, userSig, roomId, autoReceiveVideo: true });
    } catch (e) {
      alert(e);
    }
  }

  async function handleExitRoom() {
    try {
      trtc.off('*');
      await trtc.exitRoom();
    } catch (e) {
      alert(e);
    }
  }

  function addVideoView({ userId, streamType }) {
    const div = document.createElement('div');
    const elementId = `${userId}_${streamType}`;
    div.id = elementId;
    div.style.width = '100%';
    document.getElementById('local_stream').appendChild(div);
    console.warn('startRemoteVideo', userId, streamType, elementId);
    trtc.startRemoteVideo({ userId, streamType, view: elementId });
  }

  async function removeVideoView({ userId, streamType }) {
    const elementId = `${userId}_${streamType}`;
    await trtc.stopRemoteVideo({ userId, streamType });
    const element = document.getElementById(elementId);
    if (element) {
      element.remove();
    }
  }
</script>