import { genTestUserSig } from '../debug/GenerateTestUserSig-es.js';

export const generateRandomNumber = () => window.crypto.getRandomValues(new Uint16Array(1))[0];
export const urlParam = new URLSearchParams(window.location.search);

export const sdkAppId = parseInt(urlParam.get('sdkAppId'), 10);
export const secretKey = urlParam.get('secretKey');

export const userId = urlParam.get('userId') || generateRandomNumber().toString();
export const roomId = parseInt(urlParam.get('roomId'), 10) || generateRandomNumber();

export const userSig = urlParam.get('userSig') || genTestUserSig({ userID: userId, SDKAppID: sdkAppId, SecretKey: secretKey })?.userSig;

export { genTestUserSig };

console.log('(params) sdkAppId:', sdkAppId, 'secretKey:', secretKey, 'userId:', userId, 'roomId:', roomId, 'userSig:', userSig);
