/* global TRTC Watermark*/

import * as common from './common.js';
// eslint-disable-next-line max-len
// import { Watermark } from '../../../../packages/api-cloud-next/dist/npm-package/plugins/video-effect/virtual-background/virtual-background.esm.js';

document.getElementById('video').addEventListener('click', openCamera);
document.getElementById('enter').addEventListener('click', enter);
// document.getElementById('open-local-video-view').addEventListener('click', openLocalVideoView);
// document.getElementById('close-local-video-view').addEventListener('click', closeLocalVideoView);
// document.getElementById('exit').addEventListener('click', exit);
document.getElementById('test').addEventListener('click', test);

document.getElementById('startPlugin').addEventListener('click', startPlugin);
document.getElementById('updatePlugin').addEventListener('click', updatePlugin);
document.getElementById('stopPlugin').addEventListener('click', stopPlugin);
document.getElementById('device-select').addEventListener('change', changeCamera);

const watermarkTypeEle = document.getElementById('watermark-type');
const cameraListEle = document.getElementById('device-select');
const imageUrlEle = document.getElementById('image-url');

let trtc = TRTC.create({ plugins: [Watermark] });

navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
  stream.getTracks().forEach(track => track.stop());
  TRTC.getCameraList().then(list => {
    console.warn(list);
    list.forEach(l => {
      const optionEle = document.createElement('option');
      optionEle.value = l.deviceId;
      optionEle.text = l.label;
      cameraListEle.appendChild(optionEle);
    });
  });
});

async function test() {
}

let isOpenCamera = false;

if (common.sdkAppId) document.getElementById('sdkAppId').value = common.sdkAppId;
if (common.secretKey) document.getElementById('secret-key').value = common.secretKey;

let presetProfile = {};

function changeCamera() {
  if (!isOpenCamera) return;
  trtc.updateLocalVideo({ option: { cameraId: cameraListEle.value } });
}

// function onError(e) {
//   const { extraCode } = e;
//   if (extraCode === 10000003 || extraCode === 10000006) {
//     // 降低分辨率帧率或者关闭插件
//     console.error('client', e, e.code);
//     console.error('client', JSON.stringify(e), e.code);
//   }
// }

function getAuthDataFromInput() {
  const sdkAppId = parseInt(document.getElementById('sdkAppId').value, 10);
  const secretKey = document.getElementById('secret-key').value;
  if (!sdkAppId || !secretKey) {
    console.error('请填写 sdkAppId 和 secretKey');
  }
  const { userSig } = common.genTestUserSig({ userID: common.userId, SDKAppID: sdkAppId, SecretKey: secretKey });
  return {
    sdkAppId,
    userId: common.userId,
    userSig,
  };
}

function getParams() {
  const params = {
    imageUrl: document.getElementById('image-url').value
  };
  const inputX = parseInt(document.getElementById('x').value, 10);
  const inputY = parseInt(document.getElementById('y').value, 10);
  if (inputX === 0 || !!inputX) {
    params.x = inputX;
  }
  if (inputY === 0 || !!inputY) {
    params.y = inputY;
  }
  const size = document.getElementById('watermark-type').value;
  console.warn(size);
  if (size === 'contain' || size === 'cover') {
    params.size = size;
  } else if (size === 'number') {
    params.size = parseFloat(document.getElementById('size-number').value);
  } else if (size === 'object') {
    params.size = {
      width: parseInt(document.getElementById('size-width').value, 10),
      height: parseInt(document.getElementById('size-height').value, 10),
    };
  }
  console.warn(params);
  return params;
}

async function startPlugin() {
  try {
    await trtc.startPlugin('Watermark', getParams());
    console.warn('Watermark success');
  } catch (e) {
    console.error('Watermark failed', e);
  }
}

async function updatePlugin() {
  try {
    await trtc.updatePlugin('Watermark', getParams());
  } catch (e) {
    console.error('Watermark updatePlugin failed', e);
  }
}

async function stopPlugin() {
  try {
    await trtc.stopPlugin('Watermark');
  } catch (e) {
    console.error('Watermark stopPlugin failed', e);
  }
}

async function openCamera() {
  if (!trtc) trtc = TRTC.create({ plugins: [Watermark] });
  await trtc.startLocalVideo({
    option: {
      mirror: false,
      profile: presetProfile,
      cameraId: cameraListEle.value
    },
  });
  isOpenCamera = true;
  output();

  openLocalVideoView();
}

async function enter() {
  if (!trtc) trtc = TRTC.create({ plugins: [Watermark] });
  await trtc.enterRoom({ roomId: common.roomId, ...getAuthDataFromInput() });
  await trtc.startLocalVideo({
    option: {
      mirror: false,
      profile: presetProfile,
      cameraId: cameraListEle.value
    },
  });
  isOpenCamera = true;
  output();

  openLocalVideoView();
}

function openLocalVideoView() {
  trtc.updateLocalVideo({ view: 'local_stream', option: { mirror: false } });
}

function closeLocalVideoView() {
  trtc.updateLocalVideo({ view: null });
}

// 设置分辨率相关

const profiles = {
  '480p15': { width: 640, height: 480, frameRate: 15, bitrate: 1000 },
  '480p30': { width: 640, height: 480, frameRate: 30, bitrate: 1000 },
  '720p15': { width: 1280, height: 720, frameRate: 15, bitrate: 2000 },
  '720p30': { width: 1280, height: 720, frameRate: 30, bitrate: 2000 },
  '1080p20': { width: 1920, height: 1080, frameRate: 20, bitrate: 5000 }
};

const radioButtons = document.querySelectorAll('input[name="profile"]');
radioButtons.forEach(button => {
  button.addEventListener('change', () => {
    console.warn(button);
    const selectedProfile = button.value;
    const profile = profiles[selectedProfile];
    setVideoProfile(profile);
  });
  button.removeAttribute('disabled');
});

async function setVideoProfile(profile) {
  console.log(profile);
  presetProfile = profile;
  if (!trtc.getVideoTrack()) return;
  await trtc.updateLocalVideo({
    option: {
      profile,
    },
  });
  output();
}

async function output() {
  const videoTrack = trtc.getVideoTrack();
  if (!videoTrack) return;
  const settings = videoTrack.getSettings();
  const log = document.createElement('span');
  log.innerHTML = `当前分辨率：${settings.width}x${settings.height}, fps: ${settings.frameRate}`;
  document.getElementById('log').appendChild(log);
}

document.getElementById('size-number-div').style.display = 'none';
document.getElementById('size-object-div').style.display = 'none';

document.getElementById('watermark-type').addEventListener('change', (e) => {
  const value = e.target.value;
  if (value === 'contain' || value === 'cover') {
    document.getElementById('size-number-div').style.display = 'none';
    document.getElementById('size-object-div').style.display = 'none';
  } else if (value === 'number') {
    document.getElementById('size-number-div').style.display = 'block';
    document.getElementById('size-object-div').style.display = 'none';
  } else if (value === 'object') {
    document.getElementById('size-number-div').style.display = 'none';
    document.getElementById('size-object-div').style.display = 'block';
  }
});

document.getElementById('mirror').addEventListener('change', () => {
  trtc._room.videoManager.mirror = document.getElementById('mirror').checked;
});

document.getElementById('webgl').addEventListener('change', () => {
  trtc._room.videoManager.renderMode = document.getElementById('webgl').checked ? 'webgl' : '2d';
});

function showError(message) {
  // Recalculate nextErrorTop
  let nextErrorTop = 10;
  const errorDivs = document.querySelectorAll('div');
  errorDivs.forEach((div) => {
    if (div.style.position === 'fixed' && div.style.right === '10px' && div.style.backgroundColor === 'red') {
      const bottom = div.offsetTop + div.offsetHeight;
      if (bottom + 10 > nextErrorTop) {
        nextErrorTop = bottom + 10;
      }
    }
  });

  const errorDiv = document.createElement('div');
  errorDiv.textContent = message;
  errorDiv.style.position = 'fixed';
  errorDiv.style.right = '10px';
  errorDiv.style.top = `${nextErrorTop}px`;
  errorDiv.style.backgroundColor = 'red';
  errorDiv.style.color = 'white';
  errorDiv.style.padding = '10px';
  errorDiv.style.borderRadius = '5px';
  errorDiv.style.zIndex = '1000';
  document.body.appendChild(errorDiv);

  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

const originalConsoleError = console.error;
console.error = function () {
  originalConsoleError.apply(console, arguments);
  showError(Array.prototype.join.call(arguments, ' '));
};
