"use strict";
var Watermark = (() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __publicField = (obj, key, value) => {
    __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
    return value;
  };

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    Watermark: () => Watermark,
    default: () => src_default
  });

  // src/validate-config.ts
  function startValidateRule(core) {
    return {
      name: "WatermarkOptions",
      type: "object" /* Object */,
      required: true,
      allowEmpty: false,
      properties: {
        imageUrl: {
          required: true,
          type: "string" /* String */
        },
        x: {
          required: false,
          type: "number" /* Number */
        },
        y: {
          required: false,
          type: "number" /* Number */
        },
        size: {
          required: false,
          type: ["string" /* String */, "object" /* Object */, "number" /* Number */]
        }
      },
      validate(params, key, fnName, className) {
        var _a;
        const { RtcError, ErrorCode, ErrorCodeDictionary } = core.errorModule;
        if (!params)
          return;
        const { imageUrl } = params;
        const urlWithoutParams = imageUrl.split("?")[0];
        const type = urlWithoutParams.split(".").pop();
        if (type === "jpg" || type === "jpeg") {
          core.log.warn("The image format is not recommended to be jpg/jpeg, because the format does not support transparency.");
        }
        if (!((_a = core.room.videoManager.cameraTrack) == null ? void 0 : _a.mediaTrack)) {
          throw new RtcError({
            code: ErrorCode.INVALID_OPERATION,
            extraCode: ErrorCodeDictionary.INVALID_OPERATION_NEED_VIDEO,
            fnName
          });
        }
        if (core.utils.isString(params.size)) {
          if (params.size !== "contain" && params.size !== "cover") {
            throw new RtcError({
              code: ErrorCode.INVALID_PARAMETER,
              extraCode: ErrorCodeDictionary.INVALID_PARAMETER_TYPE,
              message: `The size parameter must be 'contain' or 'cover'`,
              fnName
            });
          }
        }
        if (core.utils.isNumber(params.size)) {
          if (params.size <= 0 || params.size > 1) {
            throw new RtcError({
              code: ErrorCode.INVALID_PARAMETER,
              extraCode: ErrorCodeDictionary.INVALID_PARAMETER_RANGE,
              message: `The size parameter must be greater than 0`,
              fnName
            });
          }
        }
        if (core.utils.isObject(params.size)) {
          if (!params.size.width || !params.size.height) {
            throw new RtcError({
              code: ErrorCode.INVALID_PARAMETER,
              extraCode: ErrorCodeDictionary.INVALID_PARAMETER_TYPE,
              message: `The size parameter must be an object with width and height properties`,
              fnName
            });
          }
          if (params.size.width <= 0 || params.size.height <= 0) {
            throw new RtcError({
              code: ErrorCode.INVALID_PARAMETER,
              extraCode: ErrorCodeDictionary.INVALID_PARAMETER_RANGE,
              message: `The size parameter must be greater than 0`,
              fnName
            });
          }
        }
      }
    };
  }
  function stopValidateRule(core) {
    return {
      name: "StopWatermarkOptions",
      required: false
    };
  }

  // src/index.ts
  var wSeq = 0;
  var Watermark = class {
    constructor(core) {
      this.core = core;
      __publicField(this, "seq");
      __publicField(this, "_core");
      __publicField(this, "log");
      __publicField(this, "startResolve");
      __publicField(this, "startReject");
      wSeq = wSeq + 1;
      this.seq = wSeq;
      this._core = core;
      this.log = core.log.createChild({ id: `${this.getAlias()}${wSeq}` });
      this.log.info("created");
    }
    getName() {
      return "Watermark";
    }
    getAlias() {
      return "w";
    }
    getValidateRule(method) {
      switch (method) {
        case "start":
          return startValidateRule(this._core);
        case "update":
          return stopValidateRule(this._core);
        case "stop":
          return stopValidateRule(this._core);
      }
    }
    getGroup() {
      return "w";
    }
    async start(options) {
      return this.doStart(options);
    }
    async update(options) {
      await this.stop();
      return this.doStart(options);
    }
    async stop() {
      return this._core.room.videoManager.stopWatermark();
    }
    async doStart(options) {
      const { imageUrl, x = 0, y = 0, size = "contain" } = options;
      const { settings } = this._core.room.videoManager.cameraTrack;
      const imageElement = await this._core.room.videoManager.loadImage(imageUrl);
      const { width: cameraWidth, height: cameraHeight } = settings;
      const { width: imageWidth, height: imageHeight } = imageElement;
      console.warn(`size: ${size}, image size: ${imageWidth}x${imageHeight}, camera size: ${cameraWidth}x${cameraHeight}`);
      let newImageWidth = imageWidth, newImageHeight = imageHeight;
      if (this._core.utils.isObject(size)) {
        newImageWidth = (size == null ? void 0 : size.width) || newImageWidth;
        newImageHeight = (size == null ? void 0 : size.height) || newImageHeight;
      }
      if (this._core.utils.isNumber(size)) {
        newImageWidth = imageWidth * size;
        newImageHeight = imageHeight * size;
      }
      const imageRatio = imageWidth / imageHeight;
      const cameraRatio = cameraWidth / cameraHeight;
      const isImageMoreWide = imageRatio > cameraRatio;
      if (size === "contain") {
        if (isImageMoreWide) {
          newImageWidth = cameraWidth;
          newImageHeight = cameraWidth / imageRatio;
        } else {
          newImageWidth = cameraHeight * imageRatio;
          newImageHeight = cameraHeight;
        }
      }
      if (size === "cover") {
        if (isImageMoreWide) {
          newImageHeight = cameraHeight;
          newImageWidth = cameraHeight * imageRatio;
        } else {
          newImageWidth = cameraWidth;
          newImageHeight = cameraWidth / imageRatio;
        }
      }
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = Math.min(cameraWidth - x, imageWidth);
      canvas.height = Math.min(cameraHeight - y, imageHeight);
      ctx == null ? void 0 : ctx.drawImage(imageElement, 0, 0, newImageWidth, newImageHeight);
      const croppedImage = new Image();
      croppedImage.src = canvas.toDataURL("image/png");
      return this._core.room.videoManager.setWatermark({ x, y, width: Math.min(cameraWidth - x, imageWidth), height: Math.min(cameraHeight - y, imageHeight), imageUrl, imageElement: croppedImage });
    }
  };
  var src_default = Watermark;
  return __toCommonJS(src_exports);
})().default;
//# sourceMappingURL=watermark.iife.js.map
