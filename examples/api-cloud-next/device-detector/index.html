<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="">
    <script src="https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/npm-package/trtc.js"></script>
    <!-- <script  src="../dist/device-detector.iife.js" > -->
    </script>
    <script type="module">
        // import {TRTC} from '../../../packages/api-cloud-next/dist/npm-package/trtc.esm.js'
        import {DeviceDetector} from 'https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/npm-package/plugins/device-detector.esm.js'
        import {genTestUserSig} from 'https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/libs/GenerateTestUserSig-es.js'

        let trtc = TRTC.create({ plugins: [DeviceDetector] });
        let sdkAppId = 20010458;  // your sdkAppId from console
        let roomId = 1234; // number create by yourself, optional, range is 1-4294967295, default value is 8080
        let userId="user_uplink_test"  // string create by yourself
        let userSig=genTestUserSig({ sdkAppId, userId: userId, sdkSecretKey: "7b1a9d90be91d9c3e581e8d090ed56dd512a77738fee435f41ef407863ae3573"}).userSig  // userSig, better to request from the server
        
        let downlinkUserId="user_downlink_test"
        let downlinkUserSig=genTestUserSig({ sdkAppId, userId: downlinkUserId, sdkSecretKey: "7b1a9d90be91d9c3e581e8d090ed56dd512a77738fee435f41ef407863ae3573"}).userSig //传入的

        const result = await trtc.startPlugin('DeviceDetector',{ networkDetect: { sdkAppId,userId, userSig, downlinkUserId, downlinkUserSig , roomId}})
        console.log(result,"result")
    </script>

    
</head>
<body>
</body>

</html>