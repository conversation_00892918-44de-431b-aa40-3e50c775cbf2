<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="">
    <script src="../dist/trtc.js"></script>
    <script  src="../dist/device-detector.iife.js">
        // import {DetectorUI} from './device-detector.esm.js'
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vConsole/3.9.5/vconsole.min.js"
    integrity="sha512-b0WdPl7IPDFmlPypxqFa3nIP4aeE+9KEG9Ataxg9r79c+y8qJFUEH1dBJDdqDYUvBhvuNhK9aIsXQUw4lwfbtA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- Optional JavaScript -->
    <!-- jQuery first, then <PERSON>per.js, then Bootstrap JS -->
    <!-- <script src="/assets/js/jquery-3.2.1.slim.min.js"></script> -->
    <script>
    var ua = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipad|phone/i.test(ua)) {
       var vConsole = new window.VConsole();
    }
   </script>
    <style>
        body{
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        button {
         padding: 6px 16px;
         border-radius: 4px;
         border: 0;
         outline: none;
         background-color: transparent;
         user-select: none;
         font-size: 0.875rem;
         min-width: 64px;
         box-sizing: border-box;
         font-weight: 500;
         line-height: 1.75;
         transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
           box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,
           border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
       }
       .contained {
         /* 听得到 */
         cursor: pointer;
         color: #ffffff;
         background-color: #006eff;
         box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%), 0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);
       }
    </style>
    <script type="module">
        import {genTestUserSig} from './GenerateTestUserSig-es.js'
        console.log("正在测试111")
        let trtc = TRTC.create({ plugins: [DeviceDetector] });
        let trtc2 = TRTC.create({ plugins: [DeviceDetector] });
        let sdkAppId = 20010458;  // your sdkAppId from console
        let roomId = 1234; // number create by yourself, optional, range is 1-4294967295, default value is 8080
        let userId="user_uplink_test"  // string create by yourself
        let userSig=genTestUserSig({ sdkAppId, userId, sdkSecretKey: "7b1a9d90be91d9c3e581e8d090ed56dd512a77738fee435f41ef407863ae3573"}).userSig  // userSig, better to request from the server

        let downlinkUserId="user_downlink_test"
        let downlinkUserSig=genTestUserSig({ sdkAppId, userId: downlinkUserId, sdkSecretKey: "7b1a9d90be91d9c3e581e8d090ed56dd512a77738fee435f41ef407863ae3573"}).userSig //传入的
        console.log("再次测试",trtc,userId)
        async function startPluginCall() {
          const result = await trtc.startPlugin('DeviceDetector', { networkDetect: { sdkAppId: sdkAppId, userId, userSig, roomId: roomId,downlinkUserId,downlinkUserSig } });
          console.log(result, "result");
        }

        await startPluginCall();
        await trtc.enterRoom({ roomId: 8080, sdkAppId, userId, userSig });
        await trtc.startPlugin('DeviceDetector');
        await trtc2.startPlugin('DeviceDetector');
    </script>

    
</head>
<body>
    <!-- <button id="start" class="contained">detect again</button>

    <script>
        document.getElementById('start').addEventListener('click', async () => {
            location.reload();
        })
    </script> -->
</body>

</html>