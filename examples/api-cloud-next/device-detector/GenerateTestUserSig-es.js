/* eslint-disable */
import LibGenerateTestUserSig from './lib-generate-test-usersig-es.min.js';



/**
 * Generate a temporary UserSig. Please do not publish or submit the following code to your code repository.
 *
 * @param {{userID: string, SDKAppID: number, SecretKey: string, ExpireTime?: number}} options
 * @param {string} options.userID - userID
 * @param {string} options.SDKAppID：SDKAppID of the application registered by the developer in the console.
 * @param {string} options.SecretKey：SecretKey of the application registered by the developer in the console.
 * @param {number=} options.ExpireTime：Signature validity period, which should not be set too short，Unit: second，Default value：7 x 24 x 60 x 60 = 604800 = 7 days
 * 
 * @returns {{userSig: string, SDKAppID: number}} result
 * @returns {string} result.userSig：User Signature
 * @returns {number} result.SDKAppID：SDKAppID
 * 
 * @example
 * const { userSig } = genTestUserSig({ 
 *  userID: "Alice", 
 *  SDKAppID: 0, 
 *  <PERSON><PERSON><PERSON>: "YOUR_SECRETKEY" 
 * });
 */
export function genTestUserSig({ sdkAppId, userId, sdkSecretKey}) {
  if (!userId) {
    console.error("Missing userID parameter.");
    return;
  }
  if (!sdkAppId) {
    console.error("Missing SDKAppID parameter.");
    return;
  }
  if (!sdkSecretKey) {
    console.error("Missing SecretKey parameter.");
    return;
  }
  const SDKAPPID = sdkAppId;

  const EXPIRETIME = 604800;

  const SDKSECRETKEY = sdkSecretKey;
  // const EXPIRETIME = 604800;
  // const sdkAppID = SDKAppID || SDKAPPID;
  // const secretKey = SecretKey || SECRETKEY;
  // const expireTime = ExpireTime || EXPIRETIME;

  const generator = new LibGenerateTestUserSig(SDKAPPID, SDKSECRETKEY, EXPIRETIME);
  const userSig = generator.genTestUserSig(userId);

  return {
    userSig,
    SDKAppID: sdkAppId
  };
}

// export { genTestUserSig, SDKAPPID, SECRETKEY };
