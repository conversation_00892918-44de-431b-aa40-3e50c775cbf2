<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Device Detector</title>
    <link href="./style.css" rel="stylesheet" type="text/css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/libs/lib-generate-test-usersig.min.js" defer></script>
    <script
      src="https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/npm-package/trtc.js"
      defer
    ></script>
    <script src="https://web.sdk.qcloud.com/trtc/webrtc/deviceDetector/npm-package/plugins/device-detector.iife.js"></script>
    <script src="https://tam.cdn-go.cn/aegis-sdk/latest/aegis.min.js"></script>
    <script src="./utils.js" defer></script>
    <script src="./script.js" defer></script>
  </head>
  <body>
    <main>
      <div class="feature-container">
        <h1 style="color: #1c66e5">Device Detector</h1>
        <section>
          <h2>Option-1: startPlugin without option</h2>
          <div class="btn-list">
            <button id="start-btn" class="btn" onclick="startPlugin()">
              <span class="btn-text">Start Plugin</span>
            </button>
          </div>
        </section>
        <section>
          <h2>Option-2: startPlugin with option networkDetect</h2>
          <h3>Step-1: Fill in the blanks</h3>
          <span class="note"
            >Get SDKAppId and SDKSecretKey from
            <a
              target="_blank"
              href="https://console.trtc.io/?quickclaim=engine_trial"
              >TRTC Console</a
            >
          </span>
          <span class="warning"
            >Note: This demo is for demonstration purposes only. Before official
            launch, please migrate SDKSecretKey and the UserSig calculation code
            to your own backend server to avoid unauthorized traffic use caused
            by the key leakage.
            <a target="_blank" href="https://trtc.io/document/35166"
              >View Documents</a
            ></span
          >
          <div class="input-list">
            <div class="input-group">
              <label for="sdk-app-id">SDKAppId</label>
              <input
                id="sdk-app-id"
                type="number"
                class="form-control"
              />
            </div>
            <div class="input-group">
              <label for="sdk-secret-key">SDKSecretKey</label>
              <input
                id="sdk-secret-key"
                type="text"
                class="form-control"
              />
            </div>
          </div>
          <div class="input-list">
            <div class="input-group">
              <label for="uplink-user-id">userId</label>
              <input id="uplink-user-id" type="text" class="form-control" />
            </div>
          </div>
          <h3>Step-2: Start Plugin</h3>
          <div class="btn-list">
            <button id="start-net-btn" class="btn" onclick="startPluginNetwork()" disabled>
              <span class="btn-text">Start Plugin</span>
            </button>
          </div>
        </section>
        <section>
          <h3>Detect Result</h3>
          <textarea id="resultBox" readonly></textarea>
        </section>
      </div>
    </main>
  </body>
</html>
