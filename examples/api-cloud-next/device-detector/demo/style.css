* {
  margin: 0;
  padding: 0;
}

main {
  width: 60%;
  margin: 25px auto;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  background-color: white;
  padding: 20px;
}
body {
  background-image: url("https://web.sdk.qcloud.com/trtc/webrtc/v5/demo/samples/assets/background.png");
}
@media screen and (min-width: 769px) {
  main {
    border-radius: 32px;
    box-shadow: 0 8px 24px rgba(66, 110, 175, 0.06),
      0 6px 8px rgba(32, 77, 141, 0.08);
  }
}

h1 {
  text-align: center;
  font-family: "Oxygen";
  margin-bottom: 0.8rem;
}

h3 {
  font-family: "Oxygen";
  margin-bottom: 18px;
}

@media screen and (max-width: 768px) {
  h1 {
    font-size: 1.5rem;
  }
  h2 {
    font-size: 1rem;
  }
}

.note {
  color: #084298;
  font-family: "Oxygen";
}

.warning {
  display: block;
  color: red;
  font-family: "Oxygen";
  margin: 3px 0;
}

.feature-container {
  width: 80%;
}

section {
  padding: 1rem;
  background-image: linear-gradient(
    rgb(237, 242, 252),
    rgb(255, 255, 255) 66.15%
  );
  border-radius: 16px;
  margin-bottom: 1rem;
  box-shadow: 0 8px 24px rgba(66, 110, 175, 0.06),
    0 6px 8px rgba(32, 77, 141, 0.08);
}

section:not(:last-child) {
  overflow: auto;
}

#resultBox {
  width: 95%;
  height: 35vh;
  border-radius: 8px;
  padding: 1vw;
  font-family: "Oxygen";
  resize: none;  
  border: 1px solid #ced4da;
  overflow-y: auto;  
}
.option-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  margin: 12px 0;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}


.input-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 8px;
}

.input-group {
  flex-basis: 40%;
  display: flex;
}

.input-group label {
  width: 7rem;
  font-size: 0.85rem;
  font-family: "Oxygen";
  background-color: #f2f5fc;
  padding: 8px;
  border: 1px solid #ced4da;
  border-right: none;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.form-control {
  width: 13vw;
  min-width: 8rem;
  padding: 8px;
  border: 1px solid #ced4da;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  color: #4f586b;
}

.btn-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.btn {
  flex-shrink: 0;
  background-color: #0d6efd;
  padding: 0.5rem;
  border: 1px solid #0d6efd;
  border-radius: 8px;
  margin-bottom: 3px;
  cursor: pointer;
}

.btn:not(:last-child) {
  margin-right: 16px;
}

.btn:hover {
  background-color: #144fb6;
  border: 1px solid #144fb6;
  transition: all 0.2s ease-out;
}

.btn:disabled {
  background-color: rgb(182, 187, 198);
  border: 1px solid transparent;
  cursor: not-allowed;
}

.btn-text {
  font-size: 0.85rem;
  color: #ffffff;
}
