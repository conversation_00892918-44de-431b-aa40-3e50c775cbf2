// --------global variables----------
const trtc = TRTC.create({ plugins: [DeviceDetector] });
let sdkAppId;
let userId;
let userSig;
let roomId;

// --------functions----------
async function startPlugin() {
  reportSuccessEvent('startPlugin', '0');
  const result = await trtc.startPlugin('DeviceDetector');
  let resultText = '';
  if (result) {
    for (const key of Object.keys(result)) {
      resultText += `${key}: ${JSON.stringify(result[key], null, 2)}\n`;
    }
    reportSuccessEvent('startPlugin', '0');
  }
  document.querySelector('#resultBox').value = resultText;
}
async function startPluginNetwork() {
  reportSuccessEvent('startPluginNet', 1);
  ({ sdkAppId, userId, userSig } = await initParams());
  const result = await trtc.startPlugin('DeviceDetector', { networkDetect: { sdkAppId, userId, userSig } });;
  let resultText = '';
  if (result) {
    for (const key of Object.keys(result)) {
      resultText += `${key}: ${JSON.stringify(result[key], null, 2)}\n`;
    }
    reportSuccessEvent('startPluginNet', sdkAppId);
  }
  document.querySelector('#resultBox').value = resultText;
}

