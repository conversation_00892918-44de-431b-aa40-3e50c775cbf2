const enterBtn = document.getElementById('start-net-btn');
const trtc2 = TRTC.create({ plugins: [DeviceDetector] });

// initialization
checkEnvironment();
fillParamsFromUrl();
handleError();

// check whether the button is available
const inputs = ['sdk-app-id', 'sdk-secret-key', 'uplink-user-id'];
function handleEvents() {
  for (const inputId of inputs) {
    const input = document.getElementById(inputId);
    input.addEventListener('input', checkInputs);
  }
}
function checkInputs() {
  let allFilled = true;
  for (const inputId of inputs) {
    const input = document.getElementById(inputId);
    if (!input.value) {
    //   console.log('input.value', input);
      allFilled = false;
      break;
    }
  }
  if (allFilled) {
    // console.log('allFilled', allFilled, enterBtn);
    enterBtn.disabled = false;
  } else {
    enterBtn.disabled = true;
  }
}

handleEvents();


// check current environment is supported TRTC or not
function checkEnvironment() {
  TRTC.isSupported().then((checkResult) => {
    console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
    if (!checkResult.result) {
      alert('Your browser does not supported TRTC!');
      window.location.href = 'https://web.sdk.qcloud.com/trtc/webrtc/demo/detect/index.html';
    }
  });
}


function handleError() {
  window.onunhandledrejection = (message) => {
    alert(message.reason);
    console.error('ERROR: ', message);
  };
}

// extract params from url and fill them in the blanks
function fillParamsFromUrl() {
  document.getElementById('uplink-user-id').value = 'user_test';
  document.getElementById('sdk-app-id').value =  localStorage.getItem('sdkAppId');
  document.getElementById('sdk-secret-key').value =  localStorage.getItem('sdkSecretKey');
  console.log(document.getElementById('uplink-user-id').value, document.getElementById('sdk-app-id').value, document.getElementById('sdk-secret-key').value);
  if (document.getElementById('uplink-user-id').value && document.getElementById('sdk-app-id').value && document.getElementById('sdk-secret-key').value) {
    enterBtn.disabled = false;
  }
}


function switchButtonStatus(startBtnId, endBtnId, toStart) {
  document.getElementById(startBtnId).disabled = toStart;
  document.getElementById(endBtnId).disabled = !toStart;
}

// get params from input blanks
async function initParams() {
  const sdkAppId = parseInt(document.getElementById('sdk-app-id').value);
  const sdkSecretKey = document.getElementById('sdk-secret-key').value;
  const userId = document.getElementById('uplink-user-id').value;
  localStorage.setItem('sdkAppId', sdkAppId);
  localStorage.setItem('sdkSecretKey', sdkSecretKey);
  if (!(sdkAppId && sdkSecretKey  && userId)) {
    throw new Error('Please enter the correct SDKAppId, SDKSecretKey, userId');
  }
  console.log('initParams', { sdkAppId, sdkSecretKey, userId });
  const userSig = genTestUserSig({ sdkAppId, userId, sdkSecretKey });
  const roomId = 8080;
  await trtc.enterRoom({ roomId, sdkAppId, userId, userSig });
  await trtc.exitRoom();
  return { sdkAppId, userId, userSig };
}

function genTestUserSig({ sdkAppId, userId, sdkSecretKey }) {
  const EXPIRETIME = 604800;
  const generator = new LibGenerateTestUserSig(sdkAppId, sdkSecretKey, EXPIRETIME);
  const userSig = generator.genTestUserSig(userId);
  return  userSig;
}

// handle the logic of report
const isProd = ['https://web.sdk.qcloud.com', 'https://cdpn.io'].includes(location.origin);
const DEMONAME = document.querySelector('h1').innerHTML;
const AEGIS_ID = 'iHWefAYqcRynkNsxEn';
const aegis = new Aegis({
  id: AEGIS_ID,
  uin: '',
  reportApiSpeed: false,
  reportAssetSpeed: false
});

function reportSuccessEvent(name, sdkAppId) {
  if (!isProd) return;
  aegis.reportEvent({
    name,
    ext1: `${name}`,
    ext2: DEMONAME,
    ext3: sdkAppId,
  });
}
