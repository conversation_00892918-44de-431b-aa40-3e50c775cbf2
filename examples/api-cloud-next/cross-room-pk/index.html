<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>v5</title>
  <link href="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/css/fastbootstrap.min.css" rel="stylesheet"
    integrity="sha256-xLGBU65wCDv2/qEdq3ZYw2Qdiia/wxxeGepRyZmpQdY=" crossorigin="anonymous">
</head>

<body>
  <h2>跨房连麦</h2>
  <hr>
  填入校验信息：
  <input type="number" id="sdkAppId" placeholder="please input sdkAppId">
  <input type="text" id="secret-key" placeholder="please input secretKey">
  获取地址: <a href="https://console.cloud.tencent.com/trtc/app" target="_blank" rel="noopener noreferrer"> trtc 控制台 </a>
  <hr>
  <div>
    1. 主播进房并且推流
    <button id="enter" class="btn btn-default"> 进房 + 推流</button>
    <span name="roomId"></span> 
    <span name="userId"></span> 
  </div>
  <hr>
  <div>
  2. <span name="userId"></span> 作为观众进入另一个主播房间并订阅 <br>
    填入对方房间号: <input id="roomId2" type="text">
    填入对方主播 id: <input id="userId2" type="text">
    <br>
    <button id="subscribe" class="btn btn-default"> 进对方房 + 订阅 </button>
  </div>
  <hr>
  <div>
  3. 把对方主播的声音画面混流到自己的 cdn 流上 <br>
    <button id="mix" class="btn btn-default"> 混流并播放 </button>
    <br>
    cdn-播放地址：<span name="cdn-url"></span>
    在播放器中播放，如 <a href="https://ossrs.net/players/srs_player.html" target="_blank" rel="noopener noreferrer">srs_player online</a>
  </div>
  <hr>
  <div id="local-video" class="video-box"> local </div>
  <div id="remote-video" class="video-box"> remote </div>
</body>

</html>

<script src="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/js/fastbootstrap.min.js"
  integrity="sha256-+c+/OCMmtlZadi89VaV1yUOkk1T4BD2pwBFpY3OcrqI=" crossorigin="anonymous"></script>
<script type="module" src="./main.js"></script>
<script src="../dist/trtc.js"></script>
<!-- <script src="./cdn-streaming.iife.js"></script> -->
<script src="../../../plugins/cloud-cdn-streaming/dist/cdn-streaming.iife.js"></script>

<style>
  .video-box {
    width: 666px; min-height: 333px; border: 1px saddlebrown solid;
    margin: 10px 0;
  }  
</style>