/* global TRTC Beauty VirtualBackground CDNStreaming*/

import * as common from './common.js';

document.getElementById('enter').addEventListener('click', enter);
document.getElementById('subscribe').addEventListener('click', subscribe);
document.getElementById('mix').addEventListener('click', mix);

if (common.sdkAppId) document.getElementById('sdkAppId').value = common.sdkAppId;
if (common.secretKey) document.getElementById('secret-key').value = common.secretKey;

let trtc = TRTC.create({ plugins: [CDNStreaming] });

function getAuthDataFromInput() {
  const sdkAppId = parseInt(document.getElementById('sdkAppId').value, 10);
  const secretKey = document.getElementById('secret-key').value;
  if (!sdkAppId || !secretKey) {
    console.error('请填写 sdkAppId 和 secretKey');
  }
  const { userSig } = common.genTestUserSig({ userID: common.userId, SDKAppID: sdkAppId, SecretKey: secretKey });
  return {
    sdkAppId,
    userId: common.userId,
    userSig,
  };
}

async function enter() {
  await trtc.enterRoom({
    ...getAuthDataFromInput(),
    scene: TRTC.TYPE.SCENE_LIVE,
    roomId: common.roomId,
    role: TRTC.TYPE.ROLE_ANCHOR
  });
  trtc.startLocalVideo({ view: 'local-video' });
  trtc.startLocalAudio();

  updatePageInfo();
}

async function subscribe() {
  const trtc2 = TRTC.create(); // 作为观众
  trtc2.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, (event) => {
    if (event.userId === document.getElementById('userId2').value) {
      trtc2.startRemoteVideo({
        view: 'remote-video',
        userId: event.userId,
        streamType: event.streamType
      });
    }
  });
  await trtc2.enterRoom({
    ...getAuthDataFromInput(),
    scene: TRTC.TYPE.SCENE_LIVE,
    roomId: parseInt(document.getElementById('roomId2').value, 10),
    role: TRTC.TYPE.ROLE_AUDIENCE
  });
}

function updatePageInfo() {
  document.querySelectorAll('span[name="roomId"]').forEach((span) => {
    // eslint-disable-next-line no-param-reassign
    span.textContent = `房间: ${common.roomId}`;
  });
  document.querySelectorAll('span[name="userId"]').forEach((span) => {
    // eslint-disable-next-line no-param-reassign
    span.textContent = `主播: ${common.userId}`;
  });
}

async function mix() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 480,
      videoBitrate: 1500,
      videoFramerate: 15
    },
    mix: {
      audioMixUserList: [
        {
          userId: common.userId,
          roomId: common.roomId
        },
        {
          userId: document.getElementById('userId2').value,
          roomId: parseInt(document.getElementById('roomId2').value, 10)
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: common.userId,
            roomId: common.roomId
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: document.getElementById('userId2').value,
            roomId: parseInt(document.getElementById('roomId2').value, 10)
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
  const url = `https://3891.liveplay.myqcloud.com/live/${common.sdkAppId}_${common.roomId}_${common.userId}_main.flv`;
  document.querySelector('span[name="cdn-url"]').textContent = url;
}

// --------------------------------- Error ---------------------------------

function showError(message) {
  // Recalculate nextErrorTop
  let nextErrorTop = 10;
  const errorDivs = document.querySelectorAll('div');
  errorDivs.forEach((div) => {
    if (div.style.position === 'fixed' && div.style.right === '10px' && div.style.backgroundColor === 'red') {
      const bottom = div.offsetTop + div.offsetHeight;
      if (bottom + 10 > nextErrorTop) {
        nextErrorTop = bottom + 10;
      }
    }
  });

  const errorDiv = document.createElement('div');
  errorDiv.textContent = message;
  errorDiv.style.position = 'fixed';
  errorDiv.style.right = '10px';
  errorDiv.style.top = `${nextErrorTop}px`;
  errorDiv.style.backgroundColor = 'red';
  errorDiv.style.color = 'white';
  errorDiv.style.padding = '10px';
  errorDiv.style.borderRadius = '5px';
  errorDiv.style.zIndex = '1000';
  document.body.appendChild(errorDiv);

  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

const originalConsoleError = console.error;
console.error = function () {
  originalConsoleError.apply(console, arguments);
  showError(Array.prototype.join.call(arguments, ' '));
};
