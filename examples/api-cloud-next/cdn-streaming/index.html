<!doctype html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Material Design for Bootstrap fonts and icons -->
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons"> -->

  <!-- Material Design for Bootstrap CSS -->
  <link rel="stylesheet" href="../assets/css/bootstrap-material-design.min.css">
  <link rel="stylesheet" href="./css/common.css">

  <title>TRTC Web SDK Samples - 基础音视频通话</title>
</head>

<body>
  <nav class="navbar navbar-light fixed-top rtc-primary-bg">
    <h5>基础音视频通话 0. url 添加参数 "?secretKey=XXX" 1. 进房 2. 配置混流用户参数 3. 配置混流输出参数 4. 开始混流</h5>
  </nav>
  <form id="form">
    <div class="custom-container container" style="min-width: 1900px;">
      <div class="row">
        <div class="custom-row-container">
          <div class="row">
            <div class="col-ms">
              <div class="card custom-card">
                <p style="margin: 10px 0;">进入房间</p>
                <div class="form-group">
                  <label for="userId" class="bmd-label-floating">userID:</label>
                  <input type="text" class="form-control" name="userId" id="userId">
                </div>
                <div class="form-group bmd-form-group">
                  <label for="roomId" class="bmd-label-floating">roomID:</label>
                  <input type="text" class="form-control" name="roomId" id="roomId">
                </div>
                <div class="form-group bmd-form-group">
                  <button id="join" type="button" class="btn btn-raised btn-primary rtc-primary-bg">JOIN</button>
                  <button id="publish" type="button" class="btn btn-raised btn-primary rtc-primary-bg">PUBLISH</button>
                  <button id="join-publish" type="button" class="btn btn-raised btn-primary rtc-primary-bg">JOIN-WITH-PUBLISH</button>
                  <button id="leave" type="button" class="btn btn-raised btn-primary rtc-primary-bg">LEAVE</button>
                  <button id="start_sharing" type="button" class="btn btn-raised btn-primary rtc-primary-bg">START
                    SHARING</button>
                  <button id="stop_sharing" type="button" class="btn btn-raised btn-primary rtc-primary-bg">STOP
                    SHARING</button>
                </div>
              </div>
              <div class="card">
                <button class="btn btn-raised rtc-expand-btn" id="settings" data-toggle="collapse"
                  data-target="#setting-collapse" aria-expanded="false" aria-controls="collapse">
                  Settings
                </button>
                <div id="setting-collapse" class="collapse" aria-labelledby="setting-collapse">
                  <div class="card-body">
                    <div class="form-group">
                      <label for="cameraId" class="bmd-label-floating">CAMERA</label>
                      <select class="form-control" id="cameraId" name="cameraId">
                      </select>
                    </div>
                    <div class="form-group">
                      <label for="microphoneId" class="bmd-label-floating">MICROPHONE</label>
                      <select class="form-control" id="microphoneId" name="microphoneId">
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-ms" style="margin-left: 10px; width: 500px;">
              <div class="form-group bmd-form-group">
                <div class="form-group">
                  <input type="radio" name="param" value="1"> 参数 1
                  <input type="radio" name="param" value="2"> 参数 2
                  <input type="radio" name="param" value="3"> 混流
                  <input type="radio" name="param" value="4"> 手动混流
                  <input type="radio" name="param" value="5"> 手动混流补充
                  <input type="radio" name="param" value="6"> customCDN
                </div>
              </div>
              <div class="card custom-card" style="white-space: pre-wrap; height: 600px; overflow: auto;">
                <div id="params-show" style="white-space: pre-wrap; width: 500px;" contentEditable></div>
              </div>              <div class="form-group bmd-form-group">
                <button id="start-cdn-streaming" type="button" class="btn btn-raised btn-primary rtc-primary-bg">START
                  Plugin
                </button>
                <button id="update-cdn-streaming" type="button"
                  class="btn btn-raised btn-primary rtc-primary-bg">UPDATE
                  Plugin
                </button>
                <button id="stop-cdn-streaming" type="button" class="btn btn-raised btn-primary rtc-primary-bg">STOP
                  Plugin
                </button>
                <button id="show-param" type="button" class="btn btn-raised"> show param
                </button>
                <button id="play-cdn" type="button" class="btn btn-raised"> play CDN
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
  <div class="video-grid" id="video_grid">
    <div id="local_stream" class="video-view">
      <div id="local_video_info" class="video-info"></div>
    </div>
    <div id="share" class="video-view">
    </div>
  </div>
  <div style="text-align: center; margin: 30px; box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px; width: 100vw">
    <h3 style="text-align: center;">cdn观看</h3>
    <div class="row" style=" width: 100vw">
      <div class="col-md-4">
        <div style="text-align: center;min-width: 30%;"> main
          <div id="main-stream-video" style="width:100%; height:auto; display: flex;justify-content: center;"></div>
        </div>
      </div>
      <div class="col-md-4">
        <div style="text-align: center;min-width: 30%;"> aux
          <div id="aux-stream-video" style="width:100%; height:auto; display: flex;justify-content: center;"></div>
        </div>
      </div>
      <div class="col-md-4">
        <div style="text-align: center;min-width: 30%;"> mix
          <div id="mixed-stream-video" style="width:100%; height:auto; display: flex;justify-content: center;"></div>
        </div>
      </div>
    </div>
  </div>
  <div id="mix-container">
    <div class="mix-region">
      <button class="btn-mixMCU btn-join-mix btn btn-raised btn-primary rtc-primary-bg btn-sm">加入混流</button>
      <button class="btn-mixMCU btn-remove-mix btn btn-raised btn-primary rtc-primary-bg btn-sm">取消混流</button>
      <div class="mix-info">
        <div>
          <label style="margin: 0;">width:</label>
          <input class="form-control video_width" style="height: 30px;" type="text">
        </div>
        <div>
          <label style="margin: 0;">height:</label>
          <input class="form-control video_height" style="height: 30px;" type="text" name="userId">
        </div>
        <div>
          <label style="margin: 0;">locationX:</label>
          <input class="form-control video_locationX" style="height: 30px;" type="text" name="userId">
        </div>
        <div>
          <label style="margin: 0;">locationY:</label>
          <input class="form-control video_locationY" style="height: 30px;" type="text" name="userId">
        </div>
        <div>
          <label style="margin: 0;">zOrder:[1, 15]</label>
          <input class="form-control video_zOrder" style="height: 30px;" type="text" name="userId">
        </div>
      </div>
    </div>
  </div>

  <!-- Optional JavaScript -->
  <!-- jQuery first, then Popper.js, then Bootstrap JS -->
  <!-- <script src="/assets/js/jquery-3.2.1.slim.min.js"></script> -->
  <script src="../assets/js/jquery-3.2.1.min.js"></script>
  <script src="../assets/js/popper.js"></script>
  <script src="../assets/js/bootstrap-material-design.js"></script>
  <script>$(document).ready(function () { $('body').bootstrapMaterialDesign(); });</script>
  <script src="https://imgcache.qq.com/open/qcloud/video/vcplayer/TcPlayer-2.3.3.js" charset="utf-8"></script>

  <script src="../dist/trtc.js"></script>
  <!-- <script src="../dist/plugins/cdn-streaming/cdn-streaming.iife.js"></script> -->
  <!-- <script src="../../../plugins/cloud-cdn-streaming/dist/cdn-streaming.iife.js"></script> -->
  <script src="js/index.js" type="module"></script>
</body>

</html>