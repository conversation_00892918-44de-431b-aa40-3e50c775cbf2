/* global $ TRTC CDNStreaming hex_md5 TRTCMixTranscodePlugin*/
/* eslint-disable require-jsdoc */

import { CDNStreaming, PublishMode } from '../../../../plugins/dist/cdn-streaming/cdn-streaming.esm.js';

import { genTestUserSig } from './debug/GenerateTestUserSig-es.js';

const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
const accountType = 14418;

const isLocal = window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost';

const streamId = '3333';
function playDomain() {
  if (query('sdkAppId') === '**********' || query('sdkAppId') === '**********') {
    return 'http://qilongyu.top';
  }
  return 'https://3891.liveplay.myqcloud.com';
}
console.log(playDomain());
let givenUrl = '';
const getUrl = () => {
  const d = playDomain();
  if (givenUrl) return givenUrl;
  return `${d}/live/${streamId}.flv`;
};

class RtcClient {
  constructor(options) {
    this.sdkAppId_ = options.sdkAppId || '';
    this.userId_ = options.userId || 0;
    this.userSig_ = options.userSig || '';
    this.roomId_ = options.roomId || '';
    this.privateMapKey_ = options.privateMapKey;

    this.isJoined_ = false;
    this.isPublished_ = false;
    this.remoteStreams_ = [];
    this.checkSpeakingInterval_ = -1;

    // 混流相关参数
    this.mixInputParamList = [];

    // 播放
    this.mainVideoPlayer = null;
    this.mixedVideoPlayer = null;
    this.auxVideoPlayer = null;

    console.log(CDNStreaming, PublishMode);
    console.log(window.CDNStreaming);
    console.log(window.CDNStreaming?.TYPE.PublishMode);
    console.log(window.TRTC.TYPE);

    this.trtc = TRTC.create({ plugins: [CDNStreaming] });
    this.handleEvents();
  }

  updateParams(options) {
    if (typeof options.userId !== 'undefined' && typeof options.userSig !== 'undefined') {
      this.userId_ = options.userId;
      this.userSig_ = options.userSig;
    }
    if (typeof options.roomId !== 'undefined') {
      this.roomId_ = options.roomId;
    }
  }

  async enterRoom() {
    await this.trtc.enterRoom({
      sdkAppId: Number(this.sdkAppId_),
      userId: this.userId_,
      userSig: this.userSig_,
      roomId: Number(this.roomId_),
      privateMapKey: this.privateMapKey_,
    });
    this.isJoined_ = true;
  }

  async publish() {
    this.startLocalVideo();
    this.trtc.startLocalAudio();
    this.startScreenShare();
    await this.playCDN();
  }

  async enterRoomWithPublish() {
    await this.trtc.enterRoom({
      sdkAppId: Number(this.sdkAppId_),
      userId: this.userId_,
      userSig: this.userSig_,
      roomId: Number(this.roomId_),
      privateMapKey: this.privateMapKey_,
    });
    this.isJoined_ = true;
    this.publish();
  }

  async leave() {
    if (!this.isJoined_) {
      console.warn('leave() - please join() firstly');
      return;
    }
    await this.trtc.exitRoom();
    await this.trtc.stopLocalVideo();
    if (this.isScreenSharePublished_) await this.stopScreenShare();

    this.isJoined_ = false;

    $('#local_stream .mix-region').remove();
    this.mixInputParamList = [];
  }

  startLocalVideo() {
    this.trtc.startLocalVideo({
      view: 'local_stream',
      option: {
        cameraId: getCameraId(),
        mirror: false,
      },
    });
  }

  switchCamera() {
    this.trtc.updateLocalVideo({
      option: { cameraId: getCameraId() },
    });
  }

  switchMicrophone() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }

  updateAudioProfile() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }

  stopLocalVideo() {
    this.trtc.stopLocalVideo();
  }

  async startScreenShare() {
    await this.trtc.startScreenShare({ view: 'share' });
    this.isScreenSharePublished_ = true;
  }

  async stopScreenShare() {
    if (!this.isScreenSharePublished_) return;
    await this.trtc.stopScreenShare();
    this.isScreenSharePublished_ = false;
  }

  getCDNStreamingPluginConfig() {
    const mode = 'preset-layout';
    let mixUsers = this.mixInputParamList;
    if (mode === 'preset-layout') {
      mixUsers = [
        {
          width: 960,
          height: 720,
          locationX: 0,
          locationY: 0,
          pureAudio: false,
          userId: this.userId_,
          streamType: TRTC.TYPE.STREAM_TYPE_SUB,
          zOrder: 1
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 0,
          pureAudio: false,
          userId: this.userId_,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 2
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 240,
          pureAudio: false,
          userId: '$PLACE_HOLDER_REMOTE$',
          zOrder: 3
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 480,
          pureAudio: false,
          userId: '$PLACE_HOLDER_REMOTE$',
          zOrder: 4
        }
      ];
    }
    if (mode === 'manual') {
      mixUsers = [
        {
          width: 960,
          height: 720,
          locationX: 0,
          locationY: 0,
          pureAudio: false,
          userId: this.userId_,
          streamType: TRTC.TYPE.STREAM_TYPE_SUB,
          zOrder: 1
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 0,
          pureAudio: false,
          userId: this.userId_,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 240,
          pureAudio: false,
          userId: '$PLACE_HOLDER_REMOTE$',
          zOrder: 1
        },
        {
          width: 320,
          height: 240,
          locationX: 960,
          locationY: 480,
          pureAudio: false,
          userId: '$PLACE_HOLDER_REMOTE$',
          zOrder: 1
        }
      ];
    }

    const stream = {
      streamId: '3333',
      streamType: 'main',
      // appId: parseInt($('#appId').val()),
      // bizId: parseInt($('#bizId').val()),
      // url: $('#cdnUrl').val(),
    };

    const CDNSteamingConfig = {
      stream,
      mixing: {
        mode,
        streamId: '3333',
        videoWidth: 1280,
        videoHeight: 720,
        backgroundImage: '',
        videoFramerate: 15,
        videoBitrate: 1500,
        videoGOP: 2,
        audioSampleRate: 48000,
        audioBitrate: 64,
        audioChannels: 1,
        backgroundColor: 0x000000,
        mixUsers
      }
    };
    const CDNSteamingConfig2 = {
      streamId: '33333',
      streamType: 'main',
      mixConfig: {
        mode: 'preset-layout',
        videoWidth: 1280,
        videoHeight: 720,
        mixUsers
      }
    };
    console.warn('[CDNSteamingConfig]', CDNSteamingConfig2);
    return CDNSteamingConfig;
  }

  showParams(param) {
    console.warn('showParams', param);
    if (param) {
      $('#params-show').html(JSON.stringify(param, null, 4));
    } else {
      $('#params-show').html(JSON.stringify(this.getCDNStreamingPluginConfig(), null, 4));
    }
  }

  getConfigFromInputArea() {
    // if (!$('#params-show').html()) {
    //   alert('参数为空');
    //   throw new Error('Invalid parameter');
    // }
    let result = {};
    console.warn('current param', $('#params-show').html());
    if ($('#params-show').html()) {
      result = JSON.parse($('#params-show').html());
    }
    console.warn('current param', result);
    if (result?.target && result.target.url) {
      result.target.url = result.target.url.replaceAll('&amp;', '&');
    }
    return result;
  }

  async startCDNStreamingPlugin() {
    try {
      await this.trtc.startPlugin('CDNStreaming', this.getConfigFromInputArea());
      console.log('startCDNStreamingPlugin success');
    } catch (error) {
      console.error('startCDNStreamingPlugin fail', error);
    }
  }

  async updateCDNStreamingPlugin() {
    try {
      await this.trtc.updatePlugin('CDNStreaming', this.getConfigFromInputArea());
      // this.showParams();
      console.log('updatePlugin success');
    } catch (error) {
      console.error('updatePlugin fail', error);
    }
  }

  async stopCDNStreamingPlugin() {
    try {
      await this.trtc.stopPlugin('CDNStreaming', this.getConfigFromInputArea());
      console.log('stopCDNStreamingPlugin success');
    } catch (error) {
      console.error('stopCDNStreamingPlugin fail', error);
    }
  }

  async playCDN() {
    const auxStreamId = `${rtc.sdkAppId_}_${rtc.roomId_}_${rtc.userId_}_aux`;
    const mainStreamID = `${rtc.sdkAppId_}_${rtc.roomId_}_${rtc.userId_}_main`;
    const mainUrl = `${(playDomain())}/live/${mainStreamID}.flv`;
    const AuxUrl = `${playDomain()}/live/${auxStreamId}.flv`;
    await setTimeout(() => {
      this.playerStart(getUrl(), mainUrl, AuxUrl);
    }, 3000);
  }

  playerStart(url, mainUrl, auxUrl) {
    if (this.mixedVideoPlayer || this.auxVideoPlayer || this.mainVideoPlayer) {
      this.auxVideoPlayer.destroy();
      this.mainVideoPlayer.destroy();
      this.mixedVideoPlayer.destroy();
    }
    console.log('playCDN', mainUrl, auxUrl, url);
    // eslint-disable-next-line no-undef
    this.mixedVideoPlayer = new TcPlayer('mixed-stream-video', {
      flv: url, // 请替换成实际可用的播放地址
      h5_flv: true,
      autoplay: true, // iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
      width: '320', // 视频的显示宽度，请尽量使用视频分辨率宽度
      height: '240' // 视频的显示高度，请尽量使用视频分辨率高度
    });
    // eslint-disable-next-line no-undef
    this.auxVideoPlayer = new TcPlayer('aux-stream-video', {
      flv: auxUrl, // 请替换成实际可用的播放地址
      h5_flv: true,
      autoplay: true, // iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
      width: '320', // 视频的显示宽度，请尽量使用视频分辨率宽度
      height: '240' // 视频的显示高度，请尽量使用视频分辨率高度
    });
    // eslint-disable-next-line no-undef
    this.mainVideoPlayer = new TcPlayer('main-stream-video', {
      flv: mainUrl, // 请替换成实际可用的播放地址
      h5_flv: true,
      autoplay: true, // iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
      width: '320', // 视频的显示宽度，请尽量使用视频分辨率宽度
      height: '240' // 视频的显示高度，请尽量使用视频分辨率高度
    });
  }

  handleEvents() {
    this.trtc.on(TRTC.EVENT.KICKED_OUT, console.error);
    this.trtc.on('screen-share-stopped', () => console.warn('screen share stopped'));
    this.trtc.on('remote-audio-available', ({ userId }) => {
      console.warn(`remote-audio-available ${userId}`);
      // this.remotePublishedAudioUserIdSet.add(userId);
    });
    this.trtc.on('remote-audio-unavailable', (event) => {
      console.warn(`remote-audio-unavailable ${event.userId}`);
      // this.remotePublishedAudioUserIdSet.delete(event.userId);
    });
    this.trtc.on('remote-video-available', ({ userId, streamType }) => {
      const id = `${userId}_${streamType}`;
      addView(id);
      this.trtc.startRemoteVideo({ view: id, userId, streamType });
    });
    this.trtc.on('remote-video-unavailable', (event) => {
      console.warn(`remote-video-unavailable ${JSON.stringify(event)}`);
      const id = `${event.userId}_${event.streamType}`;

      removeView(id);
    });
    this.trtc.on('audio-volume', (event) => {
      event.result.forEach(({ userId, volume }) => {
        const container = document.querySelector(`#${userId}_main`);
        if (container) {
          let volumeDiv = container.querySelector('#volume-div');
          if (!volumeDiv) {
            volumeDiv = document.createElement('div');
            volumeDiv.style.position = 'absolute';
            container.appendChild(volumeDiv);
          }
          volumeDiv.id = 'volume-div';
          volumeDiv.innerText = `${userId}音量：${volume}`;
        }
      });
    });
    this.trtc.on('network-quality', console.debug);
    this.trtc.on('audio-play-state-changed', console.warn);
    this.trtc.on('video-play-state-changed', console.warn);
    this.trtc.on('error', (error) => {
      console.error('rtc error:', error);
    });
    this.trtc.on('connection-state-changed', console.warn);
    this.trtc.on('autoplay-failed', () => {
      const button = document.createElement('button');
      button.innerText = '自动播放失败，点击恢复播放';
      button.onclick = () => document.body.removeChild(button);
      document.body.appendChild(button);
    });
  }
}

function query(name) {
  const match = window.location.search.match(new RegExp(`(\\?|&)${name}=([^&]*)(&|$)`));
  return !match ? '' : decodeURIComponent(match[2]);
}

function getUserSigData() {
  return $.ajax({
    type: 'POST',
    url: fetchUrl,
    dataType: 'json',
    data: JSON.stringify({
      pwd: '********',
      appid: parseInt(sdkAppId),
      roomnum: parseInt(roomId),
      privMap: 255,
      identifier: userId,
      accounttype: accountType
    })
  });
};

async function login() {
  const userId = $('#userId').val();
  const sdkAppId = query('sdkAppId') || defaultSdkAppId;
  const roomId = $('#roomId').val();
  let userSig; let privateMapKey;
  if (isLocal) {
    userSig = genTestUserSig({ userID: userId, SDKAppID: Number(sdkAppId), SecretKey: new URLSearchParams(window.location.search).get('secretKey') }).userSig;
  } else {
    const data = await getUserSigData().userSig;
    userSig = data.userSig;
    privateMapKey = data.privMapEncrypt;
  }
  return {
    sdkAppId,
    userId,
    userSig,
    roomId,
    privateMapKey
  };
}

function addView(id) {
  if (!$(`#${id}`)[0]) {
    $('<div/>', {
      id,
      class: 'video-view'
    }).appendTo('#video_grid');
    $('#mix-container .mix-region')
      .clone(true)
      .appendTo(`#${id}`);
    $(`#${id} .btn-join-mix`).show();
  }
}

function removeView(id) {
  if ($(`#${id}`)[0]) {
    $(`#${id}`).remove();
  }
}

// initialize sdkAppId/userId/userSig stuffs
let rtc;
const roomId = query('roomId') || parseInt(Math.random() * 10000, 10);
const userId = query('userId') || `user_${parseInt(Math.random() * 100000000, 10)}`;
$('#roomId').val(roomId);
$('#userId').val(userId);
$('#streamId').val('333333');
(async function init() {
  rtc = new RtcClient(await login());
}());

// setup button event handlers

$('#userId').on('change', async (e) => {
  e.preventDefault();
  console.log('userId changed');
  const options = await login();
  delete options.roomId;
  rtc.updateParams(options);
});

$('#roomId').on('input', (e) => {
  e.preventDefault();
  console.log(`roomId changed ${e.target.value}`);
  const validateVal = e.target.value.replace(/[^\d]/g, '');
  $('#roomId').val(validateVal);
  rtc.updateParams({ roomId: validateVal });
});

$('#join').on('click', async (e) => {
  e.preventDefault();
  console.log('join');
  await rtc.enterRoom();
});

$('#publish').on('click', async (e) => {
  e.preventDefault();
  console.log('publish');
  await rtc.publish();
});

$('#join-publish').on('click', async (e) => {
  e.preventDefault();
  console.log('join-publish');
  await rtc.enterRoomWithPublish();
});

$('#leave').on('click', (e) => {
  e.preventDefault();
  console.log('leave');
  rtc.leave();
});

$('#start_sharing').on('click', (e) => {
  e.preventDefault();
  rtc.startScreenShare();
});

$('#stop_sharing').on('click', (e) => {
  e.preventDefault();
  rtc.stopScreenShare();
});

$('#start-cdn-streaming').on('click', (e) => {
  e.preventDefault();
  rtc.startCDNStreamingPlugin();
});

$('#update-cdn-streaming').on('click', (e) => {
  e.preventDefault();
  rtc.updateCDNStreamingPlugin();
});

$('#stop-cdn-streaming').on('click', (e) => {
  e.preventDefault();
  rtc.stopCDNStreamingPlugin();
});

$('#show-param').on('click', (e) => {
  e.preventDefault();
  rtc.showParams();
});

$('#play-cdn').on('click', (e) => {
  e.preventDefault();
  rtc.playCDN();
});

// UI
$('#settings').on('click', (e) => {
  e.preventDefault();
  $('#settings').toggleClass('btn-raised');
  $('#setting-collapse').collapse();
});

$('#cdn').on('click', (e) => {
  e.preventDefault();
  $('#cdn').toggleClass('btn-raised');
  $('#cdn-collapse').collapse();
});

$('#mix').on('click', (e) => {
  e.preventDefault();
  $('#mix').toggleClass('btn-raised');
  $('#mix-collapse').collapse();
});

// 加入混流
$('.btn-join-mix').on('click', (e) => {
  e.preventDefault();
  $(e.target)
    .siblings('.mix-info')
    .show();
  $(e.target)
    .siblings('.btn-remove-mix')
    .show();
  $(e.target).hide();
  const parentId = $(e.target)
    .parents('.video-view')
    .attr('id');
  const currentStream = rtc.remoteStreams_.find(stream => stream.getId() === parentId);
  const mixNewer = {
    id: parentId,
    userId: currentStream.getUserId(),
    width: currentStream
      .getMediaStream()
      .getVideoTracks()[0]
      .getSettings().width,
    height: currentStream
      .getMediaStream()
      .getVideoTracks()[0]
      .getSettings().height,
    locationX: 0,
    locationY: 0,
    pureAudio: false,
    zOrder: rtc.mixInputParamList.length + 1,
    streamType: currentStream.getType() // 传入远端流类型
  };
  rtc.mixInputParamList.push(mixNewer);
  $(`#${parentId} .video_width`).val(mixNewer.width);
  $(`#${parentId} .video_height`).val(mixNewer.height);
  $(`#${parentId} .video_locationX`).val(mixNewer.locationX);
  $(`#${parentId} .video_locationY`).val(mixNewer.locationY);
  $(`#${parentId} .video_zOrder`).val(mixNewer.zOrder);
});

// 移除混流
$('.btn-remove-mix').on('click', (e) => {
  const parentId = $(e.target)
    .parents('.video-view')
    .attr('id');
  if (parentId === 'local_stream') {
    return;
  }
  e.preventDefault();
  $(e.target)
    .siblings('.mix-info')
    .hide();
  $(e.target)
    .siblings('.btn-join-mix')
    .show();
  $(e.target).hide();
  rtc.mixInputParamList = rtc.mixInputParamList.filter(inputParam => inputParam.id !== parentId);
});

const videoElement = '.video_width, .video_height, .video_locationX, .video_locationY, .video_zOrder';
$(videoElement).on('blur', (e) => {
  const parentId = $(e.target)
    .parents('.video-view')
    .attr('id');
  console.log('parentId', parentId);
  const editInfo = rtc.mixInputParamList.find(inputParam => inputParam.id === parentId);
  if ($(e.target).hasClass('video_width')) {
    editInfo.width = Number(e.target.value);
  }
  if ($(e.target).hasClass('video_height')) {
    editInfo.height = Number(e.target.value);
  }
  if ($(e.target).hasClass('video_locationX')) {
    editInfo.locationX = Number(e.target.value);
  }
  if ($(e.target).hasClass('video_locationY')) {
    editInfo.locationY = Number(e.target.value);
  }
  if ($(e.target).hasClass('video_zOrder')) {
    editInfo.zOrder = Number(e.target.value);
  }
  console.log('editInfo', editInfo);
});

// populate camera options
TRTC.getCameraList().then((devices) => {
  devices.forEach((device) => {
    $('<option/>', {
      value: device.deviceId,
      text: device.label
    }).appendTo('#cameraId');
  });
});

// populate microphone options
TRTC.getMicrophoneList().then((devices) => {
  devices.forEach((device) => {
    $('<option/>', {
      value: device.deviceId,
      text: device.label
    }).appendTo('#microphoneId');
  });
});

function getCameraId() {
  const selector = document.getElementById('cameraId');
  const cameraId = selector[selector.selectedIndex].value;
  console.log(`selected cameraId: ${cameraId}`);
  return cameraId;
}

function getMicrophoneId() {
  const selector = document.getElementById('microphoneId');
  const microphoneId = selector[selector.selectedIndex].value;
  console.log(`selected microphoneId: ${microphoneId}`);
  return microphoneId;
}

$('#cameraId').on('change', () => {
  rtc.switchCamera();
});

console.warn('CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom', CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom);

const param = [
  {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom,
      robotUser: {
        userId: `push_back_${$('#userId').val()}`,
        roomId: parseInt($('#roomId').val(), 10) + 1,
      }
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 720,
      videoBitrate: 1500,
      videoFramerate: 15,
    },
    mix: {
      audioMixUserList: [
        {
          userId: $('#userId').val(),
          roomId: parseInt($('#roomId').val(), 10)
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
            roomId: parseInt($('#roomId').val(), 10)
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
            roomId: parseInt($('#roomId').val(), 10)
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_SUB,
          zOrder: 1
        },
      ]
    }
  },
  {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom,
      robotUser: {
        userId: `push_back_${$('#userId').val()}`,
        roomId: parseInt($('#roomId').val(), 10) + 1,
      }
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 720,
      videoBitrate: 1500,
      videoFramerate: 15,
    },
    mix: {
      audioMixUserList: [
        {
          userId: $('#userId').val(),
          roomId: parseInt($('#roomId').val(), 10)
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
            roomId: parseInt($('#roomId').val(), 10)
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        }
      ]
    }
  },
  // {
  //   target: {
  //     publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
  //     appId: **********,
  //     bizId: 146516,
  //     url: ''
  //   }
  // },
  {
    source: '空',
  },
  {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
      streamId: '3333',
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 720,
      videoBitrate: 1500,
      videoFramerate: 15,
    },
    mix: {
      audioMixUserList: [
        {
          userId: $('#userId').val(),
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_SUB,
          zOrder: 1
        },
      ]
    }
  },
  {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
    },
    mix: {
      audioMixUserList: [
        {
          userId: $('#userId').val(),
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: $('#userId').val(),
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  },
  {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
      appId: **********,
      bizId: 3891,
      url: ''
    }
  }
];

async function fetchGivenCDN() {
  try {
    const response = await $.ajax({
      url: 'https://lvb.qcloud.com/weapp/utils/get_test_pushurl',
      method: 'GET',
      dataType: 'json'
    });
    return response;
  } catch (error) {
    console.log('请求失败:', error);
  }
}

$('input[type="radio"][name="param"]').on('change', async () => {
  console.log(document.querySelector('input[name="param"]:checked'));
  const paramId = parseInt(document.querySelector('input[name="param"]:checked').value, 10);
  const p = param[paramId - 1];
  if (p.target.appId) { // 检查p对象是否有customCDN字段
    const cdn = await fetchGivenCDN();
    console.log(cdn);
    p.target.url = cdn.url_push; // 如果有，将其中的url字段赋值为'x'
    givenUrl = cdn.url_play_flv;
  } else {
    givenUrl = '';
  }
  rtc.showParams(p);
});
