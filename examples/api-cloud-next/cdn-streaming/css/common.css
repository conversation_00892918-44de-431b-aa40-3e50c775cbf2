html, body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background: #fafafa; }
  
.rtc-primary-bg {
  color: #fff;
  background-color: #1E88E5 !important;
  align-items: center; }

.custom-row-container {
  display: flex;
  margin: 0 15px; }

.navbar {
  display: flex;
  align-content: center;
  height: 54px; }

h5 {
  margin: 0; }

.card-body.rtc-expand-card {
  color: #fff !important;
  background-color: #9e9b9b !important; }

.btn.rtc-expand-btn {
  width: 398px;
  color: #fff !important;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: #6b6363 !important;
  display: flex;
  padding: 0 24px 0 24px;
  min-height: 48px;
  transition: min-height 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  text-decoration: none;
  margin: 0; }

.custom-container {
  margin: left;
  margin: 0 15px;
  margin-top: 88px;
  box-sizing: border-box; }

.card.custom-card {
  width: 398px;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px; }
  
.video-grid {
  margin-left: 25px;
  display: grid;
  grid-gap: 20px;
  grid-template-columns: repeat(2, auto);
  grid-template-rows: auto; }

.card {
  width: 398px; }

.radio-list {
  display: flex;
  margin: auto;
  margin-top: 10px; }

.radio-list > .radio {
  margin: 0 10px;
  width: 50px; }

#local_stream {
  position: relative; }

#local_video_info {
  position: absolute; }

.video-view,
#local_stream, #local_video_info {
  width: 360px;
  height: 240px; }

.video-view {
  position: relative;
}

.video-view .mix-region {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.btn-mixMCU {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
  display: none;
}

.mix-info {
  width: 120px;
  position: absolute;
  right: 0;
  top: 0;
  transform: translate(100%);
  background-color: #ebe8e8;
  display: none
}
