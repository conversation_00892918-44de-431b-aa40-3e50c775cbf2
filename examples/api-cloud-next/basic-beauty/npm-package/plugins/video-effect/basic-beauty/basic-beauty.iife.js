"use strict";var BasicBeauty=(()=>{var nA=Object.defineProperty;var cC=Object.getOwnPropertyDescriptor;var DC=Object.getOwnPropertyNames;var lC=Object.prototype.hasOwnProperty;var yC=(l,s,h)=>s in l?nA(l,s,{enumerable:!0,configurable:!0,writable:!0,value:h}):l[s]=h;var dC=(l,s)=>{for(var h in s)nA(l,h,{get:s[h],enumerable:!0})},uC=(l,s,h,e)=>{if(s&&typeof s=="object"||typeof s=="function")for(let b of DC(s))!lC.call(l,b)&&b!==h&&nA(l,b,{get:()=>s[b],enumerable:!(e=cC(s,b))||e.enumerable});return l};var wC=l=>uC(nA({},"__esModule",{value:!0}),l);var O=(l,s,h)=>(yC(l,typeof s!="symbol"?s+"":s,h),h);var RC={};dC(RC,{BasicBeauty:()=>DA,default:()=>NC});function HA(l){return{name:"BasicBeautyOptions",type:"object",required:!0,allowEmpty:!1,properties:{beauty:{required:!1,type:"number"},brightness:{required:!1,type:"number"},ruddy:{required:!1,type:"number"}},validate(s,h,e,b){let{RtcError:Z,ErrorCode:yA,ErrorCodeDictionary:dA}=l.errorModule;if(l.utils.isOverseaSdkAppId(s.sdkAppId))throw new Z({code:yA.INVALID_OPERATION,extraCode:dA.INVALID_OPERATION,message:"This feature is not yet available in your country or region. If you have any questions, you can go to the community for consultation: https://zhiliao.qq.com/s/cWSPGIIM62CC/c3TPGIIM62CQ"})}}}function yg(l){return{name:"StopBasicBeautyOptions",required:!1}}var GC={},hC=(()=>{var l=GC.url;return function(s={}){var h,e=Object.assign({},s),b,Z,yA=new Promise((g,A)=>{b=g,Z=A}),dA=!0,uA=!1,TA=Object.assign({},e),ug=[],wg="./this.program",hg=(g,A)=>{throw A},L="",Gg,Ng,wA;(dA||uA)&&(uA?L=self.location.href:typeof document!="undefined"&&document.currentScript&&(L=document.currentScript.src),l&&(L=l),L.startsWith("blob:")?L="":L=L.substr(0,L.replace(/[?#].*/,"").lastIndexOf("/")+1),Gg=g=>{var A=new XMLHttpRequest;return A.open("GET",g,!1),A.send(null),A.responseText},uA&&(wA=g=>{var A=new XMLHttpRequest;return A.open("GET",g,!1),A.responseType="arraybuffer",A.send(null),new Uint8Array(A.response)}),Ng=(g,A,I)=>{var C=new XMLHttpRequest;C.open("GET",g,!0),C.responseType="arraybuffer",C.onload=()=>{if(C.status==200||C.status==0&&C.response){A(C.response);return}I()},C.onerror=I,C.send(null)});var pC=e.print||console.log.bind(console),hA=e.printErr||console.error.bind(console);Object.assign(e,TA),TA=null,e.arguments&&(ug=e.arguments),e.thisProgram&&(wg=e.thisProgram),e.quit&&(hg=e.quit);var AA;e.wasmBinary&&(AA=e.wasmBinary);function Rg(g){for(var A=atob(g),I=new Uint8Array(A.length),C=0;C<A.length;++C)I[C]=A.charCodeAt(C);return I}function pg(g){if(Hg(g))return Rg(g.slice(jA.length))}var JA,KA=!1,mg,v,G,V,gA,j,y,OA,ZA;function bg(){var g=JA.buffer;e.HEAP8=v=new Int8Array(g),e.HEAP16=V=new Int16Array(g),e.HEAPU8=G=new Uint8Array(g),e.HEAPU16=gA=new Uint16Array(g),e.HEAP32=j=new Int32Array(g),e.HEAPU32=y=new Uint32Array(g),e.HEAPF32=OA=new Float32Array(g),e.HEAPF64=ZA=new Float64Array(g)}var PA=[],xA=[],VA=[],Mg=!1;function fg(){if(e.preRun)for(typeof e.preRun=="function"&&(e.preRun=[e.preRun]);e.preRun.length;)Lg(e.preRun.shift());RA(PA)}function Sg(){Mg=!0,RA(xA)}function Fg(){if(e.postRun)for(typeof e.postRun=="function"&&(e.postRun=[e.postRun]);e.postRun.length;)kg(e.postRun.shift());RA(VA)}function Lg(g){PA.unshift(g)}function Ug(g){xA.unshift(g)}function kg(g){VA.unshift(g)}var H=0,GA=null,X=null;function Yg(g){var A;H++,(A=e.monitorRunDependencies)==null||A.call(e,H)}function vg(g){var I;if(H--,(I=e.monitorRunDependencies)==null||I.call(e,H),H==0&&(GA!==null&&(clearInterval(GA),GA=null),X)){var A=X;X=null,A()}}function NA(g){var I;(I=e.onAbort)==null||I.call(e,g),g="Aborted("+g+")",hA(g),KA=!0,mg=1,g+=". Build with -sASSERTIONS for more info.";var A=new WebAssembly.RuntimeError(g);throw Z(A),A}var jA="data:application/octet-stream;base64,",Hg=g=>g.startsWith(jA);function Tg(){var g="data:application/octet-stream;base64,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";return g}var IA;function Jg(g){if(g==IA&&AA)return new Uint8Array(AA);var A=pg(g);if(A)return A;if(wA)return wA(g);throw"both async and sync fetching of the wasm failed"}function Kg(g){return Promise.resolve().then(()=>Jg(g))}function Og(g,A,I){return Kg(g).then(C=>WebAssembly.instantiate(C,A)).then(I,C=>{hA(`failed to asynchronously prepare wasm: ${C}`),NA(C)})}function Zg(g,A,I,C){return Og(A,I,C)}function Pg(){return{a:sC}}function xg(){var g=Pg();function A(C,B){return m=C.exports,JA=m.y,bg(),Ig=m.B,Ug(m.z),vg("wasm-instantiate"),m}Yg("wasm-instantiate");function I(C){A(C.instance)}if(e.instantiateWasm)try{return e.instantiateWasm(g,A)}catch(C){hA(`Module.instantiateWasm callback failed with error: ${C}`),Z(C)}return IA||(IA=Tg()),Zg(AA,IA,g,I).catch(Z),{}}var RA=g=>{for(;g.length>0;)g.shift()(e)},mC=e.noExitRuntime||!0;class Vg{constructor(A){this.excPtr=A,this.ptr=A-24}set_type(A){y[this.ptr+4>>2]=A}get_type(){return y[this.ptr+4>>2]}set_destructor(A){y[this.ptr+8>>2]=A}get_destructor(){return y[this.ptr+8>>2]}set_caught(A){A=A?1:0,v[this.ptr+12]=A}get_caught(){return v[this.ptr+12]!=0}set_rethrown(A){A=A?1:0,v[this.ptr+13]=A}get_rethrown(){return v[this.ptr+13]!=0}init(A,I){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(I)}set_adjusted_ptr(A){y[this.ptr+16>>2]=A}get_adjusted_ptr(){return y[this.ptr+16>>2]}get_exception_ptr(){var A=sg(this.get_type());if(A)return y[this.excPtr>>2];var I=this.get_adjusted_ptr();return I!==0?I:this.excPtr}}var XA=0,jg=0,Xg=(g,A,I)=>{var C=new Vg(g);throw C.init(A,I),XA=g,jg++,XA},Wg=()=>{NA("")},_g=(g,A,I,C,B)=>{},zg=()=>{for(var g=new Array(256),A=0;A<256;++A)g[A]=String.fromCharCode(A);WA=g},WA,N=g=>{for(var A="",I=g;G[I];)A+=WA[G[I++]];return A},P={},T={},CA={},x,a=g=>{throw new x(g)},_A,BA=g=>{throw new _A(g)},J=(g,A,I)=>{g.forEach(function(Q){CA[Q]=A});function C(Q){var i=I(Q);i.length!==g.length&&BA("Mismatched type converter count");for(var o=0;o<g.length;++o)M(g[o],i[o])}var B=new Array(A.length),E=[],r=0;A.forEach((Q,i)=>{T.hasOwnProperty(Q)?B[i]=T[Q]:(E.push(Q),P.hasOwnProperty(Q)||(P[Q]=[]),P[Q].push(()=>{B[i]=T[Q],++r,r===E.length&&C(B)}))}),E.length===0&&C(B)};function qg(g,A,I={}){var C=A.name;if(g||a(`type "${C}" must have a positive integer typeid pointer`),T.hasOwnProperty(g)){if(I.ignoreDuplicateRegistrations)return;a(`Cannot register type '${C}' twice`)}if(T[g]=A,delete CA[g],P.hasOwnProperty(g)){var B=P[g];delete P[g],B.forEach(E=>E())}}function M(g,A,I={}){if(!("argPackAdvance"in A))throw new TypeError("registerType registeredInstance requires argPackAdvance");return qg(g,A,I)}var U=8,$g=(g,A,I,C)=>{A=N(A),M(g,{name:A,fromWireType:function(B){return!!B},toWireType:function(B,E){return E?I:C},argPackAdvance:U,readValueFromPointer:function(B){return this.fromWireType(G[B])},destructorFunction:null})},AI=g=>({count:g.count,deleteScheduled:g.deleteScheduled,preservePointerOnDelete:g.preservePointerOnDelete,ptr:g.ptr,ptrType:g.ptrType,smartPtr:g.smartPtr,smartPtrType:g.smartPtrType}),pA=g=>{function A(I){return I.$$.ptrType.registeredClass.name}a(A(g)+" instance already deleted")},mA=!1,zA=g=>{},gI=g=>{g.smartPtr?g.smartPtrType.rawDestructor(g.smartPtr):g.ptrType.registeredClass.rawDestructor(g.ptr)},qA=g=>{g.count.value-=1;var A=g.count.value===0;A&&gI(g)},$A=(g,A,I)=>{if(A===I)return g;if(I.baseClass===void 0)return null;var C=$A(g,A,I.baseClass);return C===null?null:I.downcast(C)},Ag={},II=()=>Object.keys(z).length,CI=()=>{var g=[];for(var A in z)z.hasOwnProperty(A)&&g.push(z[A]);return g},W=[],bA=()=>{for(;W.length;){var g=W.pop();g.$$.deleteScheduled=!1,g.delete()}},_,BI=g=>{_=g,W.length&&_&&_(bA)},QI=()=>{e.getInheritedInstanceCount=II,e.getLiveInheritedInstances=CI,e.flushPendingDeletes=bA,e.setDelayFunction=BI},z={},eI=(g,A)=>{for(A===void 0&&a("ptr should not be undefined");g.baseClass;)A=g.upcast(A),g=g.baseClass;return A},EI=(g,A)=>(A=eI(g,A),z[A]),QA=(g,A)=>{(!A.ptrType||!A.ptr)&&BA("makeClassHandle requires ptr and ptrType");var I=!!A.smartPtrType,C=!!A.smartPtr;return I!==C&&BA("Both smartPtrType and smartPtr must be specified"),A.count={value:1},q(Object.create(g,{$$:{value:A,writable:!0}}))};function rI(g){var A=this.getPointee(g);if(!A)return this.destructor(g),null;var I=EI(this.registeredClass,A);if(I!==void 0){if(I.$$.count.value===0)return I.$$.ptr=A,I.$$.smartPtr=g,I.clone();var C=I.clone();return this.destructor(g),C}function B(){return this.isSmartPointer?QA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:A,smartPtrType:this,smartPtr:g}):QA(this.registeredClass.instancePrototype,{ptrType:this,ptr:g})}var E=this.registeredClass.getActualType(A),r=Ag[E];if(!r)return B.call(this);var Q;this.isConst?Q=r.constPointerType:Q=r.pointerType;var i=$A(A,this.registeredClass,Q.registeredClass);return i===null?B.call(this):this.isSmartPointer?QA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:i,smartPtrType:this,smartPtr:g}):QA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:i})}var q=g=>typeof FinalizationRegistry=="undefined"?(q=A=>A,g):(mA=new FinalizationRegistry(A=>{qA(A.$$)}),q=A=>{var I=A.$$,C=!!I.smartPtr;if(C){var B={$$:I};mA.register(A,B,A)}return A},zA=A=>mA.unregister(A),q(g)),iI=()=>{Object.assign(eA.prototype,{isAliasOf(g){if(!(this instanceof eA)||!(g instanceof eA))return!1;var A=this.$$.ptrType.registeredClass,I=this.$$.ptr;g.$$=g.$$;for(var C=g.$$.ptrType.registeredClass,B=g.$$.ptr;A.baseClass;)I=A.upcast(I),A=A.baseClass;for(;C.baseClass;)B=C.upcast(B),C=C.baseClass;return A===C&&I===B},clone(){if(this.$$.ptr||pA(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var g=q(Object.create(Object.getPrototypeOf(this),{$$:{value:AI(this.$$)}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete(){this.$$.ptr||pA(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&a("Object already scheduled for deletion"),zA(this),qA(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||pA(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&a("Object already scheduled for deletion"),W.push(this),W.length===1&&_&&_(bA),this.$$.deleteScheduled=!0,this}})};function eA(){}var EA=(g,A)=>Object.defineProperty(A,"name",{value:g}),gg=(g,A,I)=>{if(g[A].overloadTable===void 0){var C=g[A];g[A]=function(...B){return g[A].overloadTable.hasOwnProperty(B.length)||a(`Function '${I}' called with an invalid number of arguments (${B.length}) - expects one of (${g[A].overloadTable})!`),g[A].overloadTable[B.length].apply(this,B)},g[A].overloadTable=[],g[A].overloadTable[C.argCount]=C}},tI=(g,A,I)=>{e.hasOwnProperty(g)?((I===void 0||e[g].overloadTable!==void 0&&e[g].overloadTable[I]!==void 0)&&a(`Cannot register public name '${g}' twice`),gg(e,g,g),e.hasOwnProperty(I)&&a(`Cannot register multiple overloads of a function with the same number of arguments (${I})!`),e[g].overloadTable[I]=A):(e[g]=A,I!==void 0&&(e[g].numArguments=I))},oI=48,aI=57,sI=g=>{if(g===void 0)return"_unknown";g=g.replace(/[^a-zA-Z0-9_]/g,"$");var A=g.charCodeAt(0);return A>=oI&&A<=aI?`_${g}`:g};function nI(g,A,I,C,B,E,r,Q){this.name=g,this.constructor=A,this.instancePrototype=I,this.rawDestructor=C,this.baseClass=B,this.getActualType=E,this.upcast=r,this.downcast=Q,this.pureVirtualFunctions=[]}var rA=(g,A,I)=>{for(;A!==I;)A.upcast||a(`Expected null or instance of ${I.name}, got an instance of ${A.name}`),g=A.upcast(g),A=A.baseClass;return g};function cI(g,A){if(A===null)return this.isReference&&a(`null is not a valid ${this.name}`),0;A.$$||a(`Cannot pass "${FA(A)}" as a ${this.name}`),A.$$.ptr||a(`Cannot pass deleted object as a pointer of type ${this.name}`);var I=A.$$.ptrType.registeredClass,C=rA(A.$$.ptr,I,this.registeredClass);return C}function DI(g,A){var I;if(A===null)return this.isReference&&a(`null is not a valid ${this.name}`),this.isSmartPointer?(I=this.rawConstructor(),g!==null&&g.push(this.rawDestructor,I),I):0;(!A||!A.$$)&&a(`Cannot pass "${FA(A)}" as a ${this.name}`),A.$$.ptr||a(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&A.$$.ptrType.isConst&&a(`Cannot convert argument of type ${A.$$.smartPtrType?A.$$.smartPtrType.name:A.$$.ptrType.name} to parameter type ${this.name}`);var C=A.$$.ptrType.registeredClass;if(I=rA(A.$$.ptr,C,this.registeredClass),this.isSmartPointer)switch(A.$$.smartPtr===void 0&&a("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:A.$$.smartPtrType===this?I=A.$$.smartPtr:a(`Cannot convert argument of type ${A.$$.smartPtrType?A.$$.smartPtrType.name:A.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:I=A.$$.smartPtr;break;case 2:if(A.$$.smartPtrType===this)I=A.$$.smartPtr;else{var B=A.clone();I=this.rawShare(I,Y.toHandle(()=>B.delete())),g!==null&&g.push(this.rawDestructor,I)}break;default:a("Unsupporting sharing policy")}return I}function lI(g,A){if(A===null)return this.isReference&&a(`null is not a valid ${this.name}`),0;A.$$||a(`Cannot pass "${FA(A)}" as a ${this.name}`),A.$$.ptr||a(`Cannot pass deleted object as a pointer of type ${this.name}`),A.$$.ptrType.isConst&&a(`Cannot convert argument of type ${A.$$.ptrType.name} to parameter type ${this.name}`);var I=A.$$.ptrType.registeredClass,C=rA(A.$$.ptr,I,this.registeredClass);return C}function iA(g){return this.fromWireType(y[g>>2])}var yI=()=>{Object.assign(tA.prototype,{getPointee(g){return this.rawGetPointee&&(g=this.rawGetPointee(g)),g},destructor(g){var A;(A=this.rawDestructor)==null||A.call(this,g)},argPackAdvance:U,readValueFromPointer:iA,fromWireType:rI})};function tA(g,A,I,C,B,E,r,Q,i,o,t){this.name=g,this.registeredClass=A,this.isReference=I,this.isConst=C,this.isSmartPointer=B,this.pointeeType=E,this.sharingPolicy=r,this.rawGetPointee=Q,this.rawConstructor=i,this.rawShare=o,this.rawDestructor=t,!B&&A.baseClass===void 0?C?(this.toWireType=cI,this.destructorFunction=null):(this.toWireType=lI,this.destructorFunction=null):this.toWireType=DI}var dI=(g,A,I)=>{e.hasOwnProperty(g)||BA("Replacing nonexistent public symbol"),e[g].overloadTable!==void 0&&I!==void 0?e[g].overloadTable[I]=A:(e[g]=A,e[g].argCount=I)},uI=(g,A,I)=>{g=g.replace(/p/g,"i");var C=e["dynCall_"+g];return C(A,...I)},oA=[],Ig,Cg=g=>{var A=oA[g];return A||(g>=oA.length&&(oA.length=g+1),oA[g]=A=Ig.get(g)),A},wI=(g,A,I=[])=>{if(g.includes("j"))return uI(g,A,I);var C=Cg(A)(...I);return C},hI=(g,A)=>(...I)=>wI(g,A,I),k=(g,A)=>{g=N(g);function I(){return g.includes("j")?hI(g,A):Cg(A)}var C=I();return typeof C!="function"&&a(`unknown function pointer with signature ${g}: ${A}`),C},GI=(g,A)=>{var I=EA(A,function(C){this.name=A,this.message=C;var B=new Error(C).stack;B!==void 0&&(this.stack=this.toString()+`
`+B.replace(/^Error(:[^\n]*)?\n/,""))});return I.prototype=Object.create(g.prototype),I.prototype.constructor=I,I.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},I},Bg,Qg=g=>{var A=ag(g),I=N(A);return S(A),I},$=(g,A)=>{var I=[],C={};function B(E){if(!C[E]&&!T[E]){if(CA[E]){CA[E].forEach(B);return}I.push(E),C[E]=!0}}throw A.forEach(B),new Bg(`${g}: `+I.map(Qg).join([", "]))},NI=(g,A,I,C,B,E,r,Q,i,o,t,n,c)=>{t=N(t),E=k(B,E),Q&&(Q=k(r,Q)),o&&(o=k(i,o)),c=k(n,c);var D=sI(t);tI(D,function(){$(`Cannot construct ${t} due to unbound types`,[C])}),J([g,A,I],C?[C]:[],d=>{var cg,Dg;d=d[0];var p,R;C?(p=d.registeredClass,R=p.instancePrototype):R=eA.prototype;var u=EA(t,function(...vA){if(Object.getPrototypeOf(this)!==K)throw new x("Use 'new' to construct "+t);if(w.constructor_body===void 0)throw new x(t+" has no accessible constructor");var lg=w.constructor_body[vA.length];if(lg===void 0)throw new x(`Tried to invoke ctor of ${t} with invalid number of parameters (${vA.length}) - expected (${Object.keys(w.constructor_body).toString()}) parameters instead!`);return lg.apply(this,vA)}),K=Object.create(R,{constructor:{value:u}});u.prototype=K;var w=new nI(t,u,K,c,p,E,Q,o);w.baseClass&&((Dg=(cg=w.baseClass).__derivedClasses)!=null||(cg.__derivedClasses=[]),w.baseClass.__derivedClasses.push(w));var YA=new tA(t,w,!0,!1,!1),F=new tA(t+"*",w,!1,!1,!1),sA=new tA(t+" const*",w,!1,!0,!1);return Ag[g]={pointerType:F,constPointerType:sA},dI(D,u),[YA,F,sA]})},eg=(g,A)=>{for(var I=[],C=0;C<g;C++)I.push(y[A+C*4>>2]);return I},MA=g=>{for(;g.length;){var A=g.pop(),I=g.pop();I(A)}};function RI(g){for(var A=1;A<g.length;++A)if(g[A]!==null&&g[A].destructorFunction===void 0)return!0;return!1}function Eg(g,A,I,C,B,E){var r=A.length;r<2&&a("argTypes array size mismatch! Must at least get return value and 'this' types!");var Q=A[1]!==null&&I!==null,i=RI(A),o=A[0].name!=="void",t=r-2,n=new Array(t),c=[],D=[],d=function(...p){p.length!==t&&a(`function ${g} called with ${p.length} arguments, expected ${t}`),D.length=0;var R;c.length=Q?2:1,c[0]=B,Q&&(R=A[1].toWireType(D,this),c[1]=R);for(var u=0;u<t;++u)n[u]=A[u+2].toWireType(D,p[u]),c.push(n[u]);var K=C(...c);function w(YA){if(i)MA(D);else for(var F=Q?1:2;F<A.length;F++){var sA=F===1?R:n[F-2];A[F].destructorFunction!==null&&A[F].destructorFunction(sA)}if(o)return A[0].fromWireType(YA)}return w(K)};return EA(g,d)}var pI=(g,A,I,C,B,E)=>{var r=eg(A,I);B=k(C,B),J([],[g],Q=>{Q=Q[0];var i=`constructor ${Q.name}`;if(Q.registeredClass.constructor_body===void 0&&(Q.registeredClass.constructor_body=[]),Q.registeredClass.constructor_body[A-1]!==void 0)throw new x(`Cannot register multiple constructors with identical number of parameters (${A-1}) for class '${Q.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return Q.registeredClass.constructor_body[A-1]=()=>{$(`Cannot construct ${Q.name} due to unbound types`,r)},J([],r,o=>(o.splice(1,0,null),Q.registeredClass.constructor_body[A-1]=Eg(i,o,null,B,E),[])),[]})},mI=g=>{g=g.trim();let A=g.indexOf("(");return A!==-1?g.substr(0,A):g},bI=(g,A,I,C,B,E,r,Q,i)=>{var o=eg(I,C);A=N(A),A=mI(A),E=k(B,E),J([],[g],t=>{t=t[0];var n=`${t.name}.${A}`;A.startsWith("@@")&&(A=Symbol[A.substring(2)]),Q&&t.registeredClass.pureVirtualFunctions.push(A);function c(){$(`Cannot call ${n} due to unbound types`,o)}var D=t.registeredClass.instancePrototype,d=D[A];return d===void 0||d.overloadTable===void 0&&d.className!==t.name&&d.argCount===I-2?(c.argCount=I-2,c.className=t.name,D[A]=c):(gg(D,A,n),D[A].overloadTable[I-2]=c),J([],o,p=>{var R=Eg(n,p,t,E,r,i);return D[A].overloadTable===void 0?(R.argCount=I-2,D[A]=R):D[A].overloadTable[I-2]=R,[]}),[]})},rg=(g,A,I)=>(g instanceof Object||a(`${I} with invalid "this": ${g}`),g instanceof A.registeredClass.constructor||a(`${I} incompatible with "this" of type ${g.constructor.name}`),g.$$.ptr||a(`cannot call emscripten binding method ${I} on deleted object`),rA(g.$$.ptr,g.$$.ptrType.registeredClass,A.registeredClass)),MI=(g,A,I,C,B,E,r,Q,i,o)=>{A=N(A),B=k(C,B),J([],[g],t=>{t=t[0];var n=`${t.name}.${A}`,c={get(){$(`Cannot access ${n} due to unbound types`,[I,r])},enumerable:!0,configurable:!0};return i?c.set=()=>$(`Cannot access ${n} due to unbound types`,[I,r]):c.set=D=>a(n+" is a read-only property"),Object.defineProperty(t.registeredClass.instancePrototype,A,c),J([],i?[I,r]:[I],D=>{var d=D[0],p={get(){var u=rg(this,t,n+" getter");return d.fromWireType(B(E,u))},enumerable:!0};if(i){i=k(Q,i);var R=D[1];p.set=function(u){var K=rg(this,t,n+" setter"),w=[];i(o,K,R.toWireType(w,u)),MA(w)}}return Object.defineProperty(t.registeredClass.instancePrototype,A,p),[]}),[]})},fA=[],f=[],SA=g=>{g>9&&--f[g+1]===0&&(f[g]=void 0,fA.push(g))},fI=()=>f.length/2-5-fA.length,SI=()=>{f.push(0,1,void 0,1,null,1,!0,1,!1,1),e.count_emval_handles=fI},Y={toValue:g=>(g||a("Cannot use deleted val. handle = "+g),f[g]),toHandle:g=>{switch(g){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:{let A=fA.pop()||f.length;return f[A]=g,f[A+1]=1,A}}}},FI={name:"emscripten::val",fromWireType:g=>{var A=Y.toValue(g);return SA(g),A},toWireType:(g,A)=>Y.toHandle(A),argPackAdvance:U,readValueFromPointer:iA,destructorFunction:null},LI=g=>M(g,FI),FA=g=>{if(g===null)return"null";var A=typeof g;return A==="object"||A==="array"||A==="function"?g.toString():""+g},UI=(g,A)=>{switch(A){case 4:return function(I){return this.fromWireType(OA[I>>2])};case 8:return function(I){return this.fromWireType(ZA[I>>3])};default:throw new TypeError(`invalid float width (${A}): ${g}`)}},kI=(g,A,I)=>{A=N(A),M(g,{name:A,fromWireType:C=>C,toWireType:(C,B)=>B,argPackAdvance:U,readValueFromPointer:UI(A,I),destructorFunction:null})},YI=(g,A,I)=>{switch(A){case 1:return I?C=>v[C]:C=>G[C];case 2:return I?C=>V[C>>1]:C=>gA[C>>1];case 4:return I?C=>j[C>>2]:C=>y[C>>2];default:throw new TypeError(`invalid integer width (${A}): ${g}`)}},vI=(g,A,I,C,B)=>{A=N(A),B===-1&&(B=4294967295);var E=t=>t;if(C===0){var r=32-8*I;E=t=>t<<r>>>r}var Q=A.includes("unsigned"),i=(t,n)=>{},o;Q?o=function(t,n){return i(n,this.name),n>>>0}:o=function(t,n){return i(n,this.name),n},M(g,{name:A,fromWireType:E,toWireType:o,argPackAdvance:U,readValueFromPointer:YI(A,I,C!==0),destructorFunction:null})},HI=(g,A,I)=>{var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],B=C[A];function E(r){var Q=y[r>>2],i=y[r+4>>2];return new B(v.buffer,i,Q)}I=N(I),M(g,{name:I,fromWireType:E,argPackAdvance:U,readValueFromPointer:E},{ignoreDuplicateRegistrations:!0})},TI=(g,A,I,C)=>{if(!(C>0))return 0;for(var B=I,E=I+C-1,r=0;r<g.length;++r){var Q=g.charCodeAt(r);if(Q>=55296&&Q<=57343){var i=g.charCodeAt(++r);Q=65536+((Q&1023)<<10)|i&1023}if(Q<=127){if(I>=E)break;A[I++]=Q}else if(Q<=2047){if(I+1>=E)break;A[I++]=192|Q>>6,A[I++]=128|Q&63}else if(Q<=65535){if(I+2>=E)break;A[I++]=224|Q>>12,A[I++]=128|Q>>6&63,A[I++]=128|Q&63}else{if(I+3>=E)break;A[I++]=240|Q>>18,A[I++]=128|Q>>12&63,A[I++]=128|Q>>6&63,A[I++]=128|Q&63}}return A[I]=0,I-B},JI=(g,A,I)=>TI(g,G,A,I),KI=g=>{for(var A=0,I=0;I<g.length;++I){var C=g.charCodeAt(I);C<=127?A++:C<=2047?A+=2:C>=55296&&C<=57343?(A+=4,++I):A+=3}return A},ig=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):void 0,OI=(g,A,I)=>{for(var C=A+I,B=A;g[B]&&!(B>=C);)++B;if(B-A>16&&g.buffer&&ig)return ig.decode(g.subarray(A,B));for(var E="";A<B;){var r=g[A++];if(!(r&128)){E+=String.fromCharCode(r);continue}var Q=g[A++]&63;if((r&224)==192){E+=String.fromCharCode((r&31)<<6|Q);continue}var i=g[A++]&63;if((r&240)==224?r=(r&15)<<12|Q<<6|i:r=(r&7)<<18|Q<<12|i<<6|g[A++]&63,r<65536)E+=String.fromCharCode(r);else{var o=r-65536;E+=String.fromCharCode(55296|o>>10,56320|o&1023)}}return E},ZI=(g,A)=>g?OI(G,g,A):"",PI=(g,A)=>{A=N(A);var I=A==="std::string";M(g,{name:A,fromWireType(C){var B=y[C>>2],E=C+4,r;if(I)for(var Q=E,i=0;i<=B;++i){var o=E+i;if(i==B||G[o]==0){var t=o-Q,n=ZI(Q,t);r===void 0?r=n:(r+=String.fromCharCode(0),r+=n),Q=o+1}}else{for(var c=new Array(B),i=0;i<B;++i)c[i]=String.fromCharCode(G[E+i]);r=c.join("")}return S(C),r},toWireType(C,B){B instanceof ArrayBuffer&&(B=new Uint8Array(B));var E,r=typeof B=="string";r||B instanceof Uint8Array||B instanceof Uint8ClampedArray||B instanceof Int8Array||a("Cannot pass non-string to std::string"),I&&r?E=KI(B):E=B.length;var Q=kA(4+E+1),i=Q+4;if(y[Q>>2]=E,I&&r)JI(B,i,E+1);else if(r)for(var o=0;o<E;++o){var t=B.charCodeAt(o);t>255&&(S(i),a("String has UTF-16 code units that do not fit in 8 bits")),G[i+o]=t}else for(var o=0;o<E;++o)G[i+o]=B[o];return C!==null&&C.push(S,Q),Q},argPackAdvance:U,readValueFromPointer:iA,destructorFunction(C){S(C)}})},tg=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):void 0,xI=(g,A)=>{for(var I=g,C=I>>1,B=C+A/2;!(C>=B)&&gA[C];)++C;if(I=C<<1,I-g>32&&tg)return tg.decode(G.subarray(g,I));for(var E="",r=0;!(r>=A/2);++r){var Q=V[g+r*2>>1];if(Q==0)break;E+=String.fromCharCode(Q)}return E},VI=(g,A,I)=>{if(I!=null||(I=2147483647),I<2)return 0;I-=2;for(var C=A,B=I<g.length*2?I/2:g.length,E=0;E<B;++E){var r=g.charCodeAt(E);V[A>>1]=r,A+=2}return V[A>>1]=0,A-C},jI=g=>g.length*2,XI=(g,A)=>{for(var I=0,C="";!(I>=A/4);){var B=j[g+I*4>>2];if(B==0)break;if(++I,B>=65536){var E=B-65536;C+=String.fromCharCode(55296|E>>10,56320|E&1023)}else C+=String.fromCharCode(B)}return C},WI=(g,A,I)=>{if(I!=null||(I=2147483647),I<4)return 0;for(var C=A,B=C+I-4,E=0;E<g.length;++E){var r=g.charCodeAt(E);if(r>=55296&&r<=57343){var Q=g.charCodeAt(++E);r=65536+((r&1023)<<10)|Q&1023}if(j[A>>2]=r,A+=4,A+4>B)break}return j[A>>2]=0,A-C},_I=g=>{for(var A=0,I=0;I<g.length;++I){var C=g.charCodeAt(I);C>=55296&&C<=57343&&++I,A+=4}return A},zI=(g,A,I)=>{I=N(I);var C,B,E,r;A===2?(C=xI,B=VI,r=jI,E=Q=>gA[Q>>1]):A===4&&(C=XI,B=WI,r=_I,E=Q=>y[Q>>2]),M(g,{name:I,fromWireType:Q=>{for(var i=y[Q>>2],o,t=Q+4,n=0;n<=i;++n){var c=Q+4+n*A;if(n==i||E(c)==0){var D=c-t,d=C(t,D);o===void 0?o=d:(o+=String.fromCharCode(0),o+=d),t=c+A}}return S(Q),o},toWireType:(Q,i)=>{typeof i!="string"&&a(`Cannot pass non-string to C++ string type ${I}`);var o=r(i),t=kA(4+o+A);return y[t>>2]=o/A,B(i,t+4,o+A),Q!==null&&Q.push(S,t),t},argPackAdvance:U,readValueFromPointer:iA,destructorFunction(Q){S(Q)}})},qI=(g,A)=>{A=N(A),M(g,{isVoid:!0,name:A,argPackAdvance:0,fromWireType:()=>{},toWireType:(I,C)=>{}})},$I=(g,A,I)=>G.copyWithin(g,A,A+I),LA=(g,A)=>{var I=T[g];return I===void 0&&a(`${A} has unknown type ${Qg(g)}`),I},og=(g,A,I)=>{var C=[],B=g.toWireType(C,I);return C.length&&(y[A>>2]=Y.toHandle(C)),B},AC=(g,A,I)=>(g=Y.toValue(g),A=LA(A,"emval::as"),og(A,I,g)),gC={},IC=g=>{var A=gC[g];return A===void 0?N(g):A},UA=[],CC=(g,A,I,C,B)=>(g=UA[g],A=Y.toValue(A),I=IC(I),g(A,A[I],C,B)),BC=g=>{var A=UA.length;return UA.push(g),A},QC=(g,A)=>{for(var I=new Array(g),C=0;C<g;++C)I[C]=LA(y[A+C*4>>2],"parameter "+C);return I},eC=Reflect.construct,EC=(g,A,I)=>{var C=QC(g,A),B=C.shift();g--;var E=new Array(g),r=(i,o,t,n)=>{for(var c=0,D=0;D<g;++D)E[D]=C[D].readValueFromPointer(n+c),c+=C[D].argPackAdvance;var d=I===1?eC(o,E):o.apply(i,E);return og(B,t,d)},Q=`methodCaller<(${C.map(i=>i.name).join(", ")}) => ${B.name}>`;return BC(EA(Q,r))},rC=g=>{g>9&&(f[g+1]+=1)},iC=g=>{var A=Y.toValue(g);MA(A),SA(g)},tC=(g,A)=>{g=LA(g,"_emval_take_value");var I=g.readValueFromPointer(A);return Y.toHandle(I)},oC=g=>{NA("OOM")},aC=g=>{var A=G.length;g>>>=0,oC(g)};zg(),x=e.BindingError=class extends Error{constructor(A){super(A),this.name="BindingError"}},_A=e.InternalError=class extends Error{constructor(A){super(A),this.name="InternalError"}},iI(),QI(),yI(),Bg=e.UnboundTypeError=GI(Error,"UnboundTypeError"),SI();var sC={v:Xg,p:Wg,o:_g,t:$g,x:NI,w:pI,h:bI,k:MI,s:LI,n:kI,g:vI,a:HI,m:PI,l:zI,u:qI,r:$I,j:AC,e:CC,c:SA,f:EC,d:rC,b:iC,i:tC,q:aC},m=xg(),nC=()=>(nC=m.z)(),kA=g=>(kA=m.A)(g),ag=g=>(ag=m.C)(g),S=g=>(S=m.D)(g),sg=g=>(sg=m.E)(g),bC=e._vertexShaderSource=13280,aA;X=function g(){aA||ng(),aA||(X=g)};function ng(){if(H>0||(fg(),H>0))return;function g(){aA||(aA=!0,e.calledRun=!0,!KA&&(Sg(),b(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),Fg()))}e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),g()},1)):g()}if(e.preInit)for(typeof e.preInit=="function"&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return ng(),h=yA,h}})(),dg=hC;var cA=0,lA=class lA{constructor(s){this.core=s;O(this,"seq");O(this,"_core");O(this,"log");O(this,"beautyParams");cA=cA+1,this.seq=cA,this._core=s,this.log=s.log.createChild({id:`${this.getAlias()}${cA}`}),this.log.info("created")}getName(){return lA.Name}getAlias(){return"bb"}getValidateRule(s){switch(s){case"start":return HA(this._core);case"update":return HA(this._core);case"stop":return yg(this._core)}}getGroup(){return"w"}async start(s){return this._core.room.videoManager.Wasm||(this._core.room.videoManager.Wasm=await dg({print:console.warn})),this._core.room.videoManager.renderMode="webgl",this._core.room.videoManager.setBeautyParams({beauty:s.beauty||.5,brightness:s.brightness||.5,ruddy:s.ruddy||.5})}async update(s){return this._core.room.videoManager.setBeautyParams({beauty:s.beauty||.5,brightness:s.brightness||.5,ruddy:s.ruddy||.5})}async stop(){return this._core.room.videoManager.renderMode="auto",this._core.room.videoManager.stopBeauty()}destroy(){this._core.room.videoManager.renderMode="auto"}};O(lA,"Name","BasicBeauty");var DA=lA;var NC=DA;return wC(RC);})().default;
