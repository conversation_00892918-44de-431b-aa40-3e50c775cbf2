<!doctype html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Material Design for Bootstrap CSS -->
  <link rel="stylesheet" href="../assets/css/bootstrap-material-design.min.css">
  <link rel="stylesheet" href="./css/common.css">

  <title>基础美颜 | TRTC Web SDK Samples</title>
</head>

<body>
  <nav class="navbar navbar-light fixed-top rtc-primary-bg">
    <h5>基础美颜
      <!-- <label> Version </label>
      <select id="sdkVersionSelector" style="font-size: smaller; padding: 0 5px;">
        <option value="latest">latest</option>
        <option value="5.3.1">5.3.1</option>
      </select> -->
    </h5>
    <div>
    </div>
  </nav>
  <form id="form">
    <div class="container custom-container">
      <div class="row">
        <div class="custom-row-container">
          <div class="row">
            <div class="col-ms">
              <div class="card custom-card">
                <div class="form-group bmd-form-group">
                  <div>
                    <div>
                      美颜 <input type="range" id="beauty-beauty" name="beauty-beauty-local" min="0" max="1" step="0.1"
                        value="0.5">
                    </div>
                    <div>
                      明亮 <input type="range" id="beauty-brightness" name="beauty-brightness-local" min="0" max="1"
                        step="0.1" value="0.5">
                    </div>
                    <div>
                      红润 <input type="range" id="beauty-ruddy" name="beauty-ruddy-local" min="0" max="1" step="0.1"
                        value="0.5">
                    </div>
                  </div>
                </div>
                <div class="form-group bmd-form-group">
                  <button id="start-beauty" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">开启美颜</button>
                  <button id="update-beauty" type="button"
                    class="btn btn-raised btn-primary rtc-primary-bg">更新美颜</button>
                  <button id="stop-beauty" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭美颜</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  </form>
  <div class="video-grid" id="video_grid">
    <div id="main"></div>
    <div id="side">
    </div>
  </div>
  </div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/vConsole/3.9.5/vconsole.min.js"
    integrity="sha512-b0WdPl7IPDFmlPypxqFa3nIP4aeE+9KEG9Ataxg9r79c+y8qJFUEH1dBJDdqDYUvBhvuNhK9aIsXQUw4lwfbtA=="
    crossorigin="anonymous" referrerpolicy="no-referrer"></script>
  <!-- Optional JavaScript -->
  <!-- jQuery first, then Popper.js, then Bootstrap JS -->
  <!-- <script src="/assets/js/jquery-3.2.1.slim.min.js"></script> -->
  <script>
    var ua = navigator.userAgent.toLowerCase();
    if (/mobile|android|iphone|ipad|phone/i.test(ua)) {
      var vConsole = new window.VConsole();
    }
  </script>
  <script src="../assets/js/jquery-3.2.1.min.js"></script>
  <script src="../assets/js/popper.js"></script>
  <script src="../assets/js/bootstrap-material-design.js"></script>
  <script>$(document).ready(function () { $('body').bootstrapMaterialDesign(); });</script>
  <script src="./npm-package/trtc.js"></script>
  <script src="./npm-package/plugins/video-effect/basic-beauty/basic-beauty.iife.js"></script>
  <script src="js/index.js"></script>
</body>

</html>