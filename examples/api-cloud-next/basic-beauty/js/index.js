/* global $ TRTC BasicBeauty VirtualBackground Watermark*/

const trtc = TRTC.create({ plugins: [BasicBeauty] });

function getBeautyParams() {
  const beauty = document.getElementById('beauty-beauty').value;
  const brightness = document.getElementById('beauty-brightness').value;
  const ruddy = document.getElementById('beauty-ruddy').value;
  return {
    beauty: Number(beauty),
    brightness: Number(brightness),
    ruddy: Number(ruddy),
  };
}

$('#start-beauty').on('click', () => {
  trtc.startLocalVideo({ view: 'main' });
  trtc.startPlugin('BasicBeauty', {
    ...getBeautyParams()
  });
});

$('#update-beauty').on('click', () => {
  trtc.updatePlugin('BasicBeauty', {
    ...getBeautyParams()
  });
});

$('#stop-beauty').on('click', () => {
  trtc.stopPlugin('BasicBeauty');
});
