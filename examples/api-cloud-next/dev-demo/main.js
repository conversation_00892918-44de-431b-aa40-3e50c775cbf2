/* global TRTC Beauty VirtualBackground Watermark*/

import * as common from './trtc-auth.js';
import './js/device.js';
import { addRemoteView, addButtonListener, toggleCameraIcon, toggleMicrophoneIcon } from './js/ui.js';

// eslint-disable-next-line max-len
[enter, enter2, openCamera, openMicrophone, openMicrophone2, audioSource2, muteVideoImage, muteVideo, unmuteVideo, startWatermark, startVirtualBackground].forEach((fn) => {
  console.log(fn.name);
  addButtonListener(fn.name, fn);
});

// addButtonListener('enter', enter);
// addButtonListener('enter2', enter2);
// addButtonListener('openCamera', openCamera);
// addButtonListener('openMicrophone', openMic);
// addButtonListener('openMicrophone2', openMic2);
// addButtonListener('audioSource2', audioSource2);
// addButtonListener('muteVideoImage', muteVideoImage);
// addButtonListener('muteVideo', muteVideo);
// addButtonListener('unmuteVideo', unmuteVideo);
// addButtonListener('startWatermark', handleWatermark);
// addButtonListener('startVirtualBackground', handleVirtualBackground);

if (common.sdkAppId) document.getElementById('sdkAppId').value = common.sdkAppId;
if (common.secretKey) document.getElementById('secret-key').value = common.secretKey;
if (common.userId) document.getElementById('userId').value = common.userId;
if (common.roomId) document.getElementById('roomId').value = common.roomId;

const trtc = TRTC.create({ plugins: [Watermark, VirtualBackground] });
const trtc2 = TRTC.create(); // 作为观众
window.trtc = trtc;
window.trtc2 = trtc2;
trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, (event) => {
  addRemoteView(event.userId);
});

trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
  toggleMicrophoneIcon(event.userId, false);
});
trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, (event) => {
  toggleMicrophoneIcon(event.userId, true);
});
trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
  toggleCameraIcon(event.userId, false);
});
trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, (event) => {
  trtc.startRemoteVideo({ userId: event.userId, streamType: event.streamType, view: `remote-${event.userId}` });
  toggleCameraIcon(event.userId, true);
});


trtc2.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, (event) => {
  console.log('REMOTE_VIDEO_AVAILABLE', event);
  if (event.userId === common.userId) {
    trtc2.startRemoteVideo({
      view: 'local-camera2',
      userId: event.userId,
      streamType: event.streamType
    });
  }
});
trtc2.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
  toggleMicrophoneIcon('self', false);
});
trtc2.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, (event) => {
  toggleMicrophoneIcon('self', true);
});
trtc2.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
  toggleCameraIcon('self', false);
});
trtc2.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, (event) => {
  toggleCameraIcon('self', true);
});
async function enter() {
  console.warn('enter');
  await trtc.enterRoom({
    sdkAppId: common.sdkAppId,
    userId: common.userId,
    userSig: common.userSig,
    roomId: common.roomId,
  });
}
function enter2() {
  console.warn('enter2');
  trtc2.enterRoom({
    sdkAppId: common.sdkAppId,
    userId: `${common.userId}_2`,
    userSig: common.genTestUserSig({ userID: `${common.userId}_2`, SDKAppID: common.sdkAppId, SecretKey: common.secretKey, EXPIRETIME: 86400 }).userSig,
    roomId: common.roomId,
  });
}
async function openCamera() {
  console.warn('openCamera');
  await trtc.startLocalVideo({ view: 'local-camera' });
}

async function startWatermark() {
  await trtc.startPlugin('Watermark', {
    imageUrl: 'https://web.sdk.qcloud.com/trtc/webrtc/test/qer-test/watermark/tv2.png',
    size: 'contain'
  });
}

function startVirtualBackground() {
  trtc.startPlugin('VirtualBackground', {
    sdkAppId: common.sdkAppId,
    userId: common.userId,
    userSig: common.userSig
  });
}

async function openMicrophone() {
  await trtc.startLocalAudio();
}
async function openMicrophone2() {
  return trtc2.startLocalAudio();
}
async function audioSource2() {
  const audio = new Audio('/assets/music/bgm.mp3');
  await audio.play();
  const audioTrack = audio.captureStream().getAudioTracks()[0];
  return trtc2.startLocalAudio({ option: { audioTrack } });
}
async function muteVideoImage() {
  try {
    console.warn('start mute video image');
    await trtc.updateLocalVideo({ mute: 'https://fastly.picsum.photos/id/723/200/300.jpg?hmac=EtJwe3DxhZ1GDiNghxWaO92pvcPcjg02wJzc7Qj7Lr0' });
    console.warn('success mute video image');
  } catch (e) {
    console.error(e);
  }
}

async function muteVideo(mute) {
  await trtc.updateLocalVideo({ mute: true });
}

async function unmuteVideo(mute) {
  await trtc.updateLocalVideo({ mute: false });
}
