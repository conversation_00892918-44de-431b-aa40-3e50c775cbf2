input {
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #d5dae1;
  cursor: pointer;
  padding: 7px 16px;
  transition: all 0.1s ease-in-out;
  position: relative;
}

input:focus {
  border-color: #323232;
  outline: none;
}

input[type=number]::-webkit-outer-spin-button, 
input[type=number]::-webkit-inner-spin-button {
  width: 25px;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
}

div .q-label-input {
  display: flex;
  flex-direction: column;
  line-height: 1;
  font-weight: 500;
  font-size: 14px;
  width: 150px;
  margin-bottom: 10px;
  margin-right: 10px;
}

div .q-label-input label {
  margin-bottom: 5px;
}