.btn {
  border-radius: 5px;
  cursor: pointer;
  padding: 7px 16px;
  transition: all 0.1s ease-in-out;
  margin-right: 5px;
  position: relative;

  background-color: #fff;
  border: 1px solid #d5dae1;
}

.btn:active {
  transform: translateY(2px);
}

.btn:hover {
  bacground-color: #f4f4f5;
  opacity: .9;
}

.btn.primary {
  background-color: #18181b;
  color: #fff;
}

.btn.secondary {
  background-color: #f4f4f5;
  border: 1px solid #f4f4f5;
}
.btn.secondary:hover {
  opacity: .8;
}

.btn .icon {
  display: flex;
  align-items: center;
}

.btn .icon > i {
  margin-right: 5px;
}

i {
  font-size: 18px;
}