@import './css/index.css';

.video {
  width: 14rem;
  height: 8rem;
  aspect-ratio: 16/9;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
  border: 1px solid #d5dae1;
}

.video-box {
  margin-right: 10px;
}

.video-label {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.queue {
  width: 100px;
  height: 200px;
  border: 2px dashed #ccc;
  overflow: auto;
  padding: 10px;
}

.card, .info {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: -10px;
}

.card {
  margin-bottom: 10px;
  height: fit-content;
  aligin-items: center;
}

.buttons {
  display: flex;
  flex-direction: row;
}

.show-select {
  display: flex;
  flex-direction: row;
}

.checkbox-label {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.checkbox-label label {
  font-size: 14px;
}

.loading-indicator {
  font-size: 14px;
  animation: rotate 1s linear infinite;
  display: inline-block;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}