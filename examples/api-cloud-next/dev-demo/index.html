<!doctype html>
<html lang="en">

<head>
  <title>trtc dev demo</title>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" type="image/svg+xml" href="https://console.trtc.io/favicon.png">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/tabler-icons.min.css" />
  <link rel="stylesheet" href="style.css">
</head>

<body>
  <div class="flex flex-row h-screen">
    <div>
      <div class="info">
        <!-- <div class="card">
          <div class="q-label-input">
            <label> SDK Version </label>
            <select id="sdkVersionSelector">
              <option value="latest">latest</option>
              <option value="5.3.1">5.3.1</option>
            </select>
          </div>
        </div> -->
        <div class="card">
          <div class="q-label-input">
            <label> SDKAppID </label>
            <input type="number" id="sdkAppId" placeholder="please input sdkAppId">
          </div>
          <div class="q-label-input">
            <label> SDKSecretKey </label>
            <input type="text" id="secret-key" placeholder="secret-key">
          </div>
        </div>
        <div class="card">
          <div class="q-label-input">
            <label> userId </label>
            <input type="text" id="userId">
          </div>
          <div class="q-label-input">
            <label> roomId </label>
            <input type="number" id="roomId">
          </div>
        </div>
      </div>
      <div class="card">
        <button draggable="true" ondragstart="drag(event)" id="enter" class="btn primary"> 进房 </button>
        <button draggable="true" ondragstart="drag(event)" id="enter2" class="btn primary"> 进房2 </button>
      </div>
      <hr>
      <div>
        <div class="card">
          <select name="" id="microphone"> </select>
          <button draggable="true" ondragstart="drag(event)" id="openMicrophone" class="btn primary"> 打开麦克风 </button>
          <button draggable="true" ondragstart="drag(event)" id="updateMicrophone" class="btn secondary"> 更新麦克风 </button>
          <button draggable="true" ondragstart="drag(event)" id="closeMicrophone" class="btn secondary"> 关闭麦克风 </button>
          <button draggable="true" ondragstart="drag(event)" id="openMicrophone2" class="btn primary"> 打开麦克风2 </button>
          <button draggable="true" ondragstart="drag(event)" id="audioSource2" class="btn primary"> 自定义音频2 </button>
        </div>
        <hr>
        <div class="card">
          <select name="" id="camera"> </select>
          <select name="" id="camera-resolution"> </select>
          <button draggable="true" ondragstart="drag(event)" id="openCamera" class="btn primary"> 打开摄像头 </button>
          <button draggable="true" ondragstart="drag(event)" id="updateCamera" class="btn secondary"> 更新摄像头 </button>
          <button draggable="true" ondragstart="drag(event)" id="closeCamera" class="btn secondary"> 关闭摄像头 </button>
          <button draggable="true" ondragstart="drag(event)" id="muteVideoImage" class="btn"> mute 图片垫片 </button>
          <button draggable="true" ondragstart="drag(event)" id="muteVideo" class="btn"> 普通 mute </button>
          <button draggable="true" ondragstart="drag(event)" id="unmuteVideo" class="btn"> 取消 mute </button>
        </div>
        <div class="card">
          <div class="video-box"> 
            <div id="local-camera" class="video"></div>
            <label class="video-label"> 本地摄像头 </label>
          </div>
          <div class="video-box"> 
            <div id="local-camera2" class="video"></div>
            <label class="video-label"> 远端看自己 
              <span>
                <i class="ti ti-device-computer-camera" id="remote-video-self" hidden></i>
                <i class="ti ti-device-computer-camera-off" id="remote-video-self-off"></i>
                <i class="ti ti-microphone" id="remote-audio-self" hidden></i>
                <i class="ti ti-microphone-off" id="remote-audio-self-off"></i>
              </span>
            </label>
          </div>
          <div class="video-box"> 
            <div id="local-screen" class="video"></div>
            <label class="video-label"> 本地屏幕分享 </label>
          </div>
        </div>
      </div>
      <hr>
      <div>
        <div class="show-select">
          <div class="checkbox-label">
            <input type="checkbox" id="show-select-all" checked> 
            <label>显示全部</label>
          </div>
          <div class="checkbox-label">
            <input type="checkbox" id="show-select-all" checked> 
            <label>虚拟背景</label>
          </div>
          <div class="checkbox-label">
            <input type="checkbox" id="show-select-all" checked> 
            <label>水印</label>
          </div>
        </div>
        <div class="buttons">
          <div id="virtual-background-buttons">
            <button draggable="true" ondragstart="drag(event)" id="startVirtualBackground"> 打开虚拟背景 </button>
          </div>
          <div id="watermark-buttons">
            <button draggable="true" ondragstart="drag(event)" id="startWatermark"> 打开水印 </button>
          </div>
        </div>
      </div>
      <hr>
    </div>
    <div class="flex flex-col border-l border-gray-700 h-full w-[14rem] overflow-hidden">
      <div class="card" id="remote-video">
      </div>
    </div>

    <div class="container">
      <div class="queue" id="queue">
      </div>
      <button id="executeQueue">执行队列</button>
      <button id="cleanQueue">清空队列</button>
    </div>
  </div>
</body>

</html>

<script>
  let tempDragButtonElement;
  let buttonQueue = [];

  function drag(event) {
    tempDragButtonElement = event.target.cloneNode(true);
    event.dataTransfer.setData("text", event.target.id);
    console.log(event.target.click)
  }

  document.getElementById("queue").addEventListener("drop", function (event) {
    event.preventDefault();
    const originId = event.dataTransfer.getData("text");
    buttonQueue.push(originId);
    this.appendChild(tempDragButtonElement);
    tempDragButtonElement = null;
    localStorage.setItem("buttonQueue", JSON.stringify(buttonQueue));
  });

  document.getElementById("queue").addEventListener("dragover", function (event) {
    event.preventDefault();
  });

  document.getElementById("executeQueue").addEventListener("click", executeQueue);

  window.addEventListener("load", function () {
    let savedButtonQueue = localStorage.getItem("buttonQueue");
    buttonQueue = savedButtonQueue ? JSON.parse(savedButtonQueue) : [];
    freshQueue();
    executeQueue();
  });

  function freshQueue() {
    buttonQueue.forEach(function (originId) {
      const button = document.getElementById(originId);
      const buttonClone = button.cloneNode(true);
      buttonClone.id = 'queue-' + originId;
      document.getElementById("queue").appendChild(buttonClone);
    });
  }

  async function executeQueue() {
    for (const buttonId of buttonQueue) {
      const button = document.getElementById(buttonId);
      const buttonClone = document.getElementById('queue-' + buttonId);
      buttonClone.innerHTML = buttonClone.innerHTML + '...';
      await button.click();
      buttonClone.innerHTML = button.innerHTML;
    }
  }

  document.getElementById("cleanQueue").addEventListener("click", cleanQueue);

  function cleanQueue() {
    document.getElementById("queue").innerHTML = "";
    localStorage.removeItem("buttonQueue");
  }
</script>

<script type="module" src="./main.js"></script>

<script src="../dist/trtc.js"></script>
<!-- <script src="https://www.unpkg.com/trtc-sdk-v5@5.3.2/trtc.js"></script> -->

<script src="https://www.unpkg.com/trtc-sdk-v5@5.3.2/plugins/video-effect/virtual-background/virtual-background.iife.js"></script>
<script src="https://www.unpkg.com/trtc-sdk-v5@5.3.2/plugins/video-effect/watermark/watermark.iife.js"></script>

<!-- <script>
  function loadSdk(version) {
    var baseUrl = "https://www.unpkg.com/trtc-sdk-v5@" + version;
    var scripts = [
        "/trtc.js",
        "/plugins/video-effect/virtual-background/virtual-background.iife.js",
        "/plugins/video-effect/watermark/watermark.iife.js"
    ];

    scripts.forEach(function(scriptPath) {
        var script = document.createElement('script');
        script.src = baseUrl + scriptPath;
        document.head.appendChild(script);
    });
}

function loadSelectedSdk() {
    var selectedVersion = document.getElementById('sdkVersionSelector').value;
    loadSdk(selectedVersion);
}

document.getElementById('sdkVersionSelector').addEventListener('change', loadSelectedSdk);
</script> -->