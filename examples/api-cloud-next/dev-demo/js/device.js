/* global TRTC Beauty VirtualBackground Watermark*/

TRTC.getCameraList().then((data) => {
  data.forEach((item) => {
    const option = document.createElement('option');
    option.value = item.deviceId;
    option.innerHTML = item.label;
    document.getElementById('camera').appendChild(option);
  });
});

TRTC.getMicrophoneList().then((data) => {
  data.forEach((item) => {
    const option = document.createElement('option');
    option.value = item.deviceId;
    option.innerHTML = item.label;
    document.getElementById('microphone').appendChild(option);
  });
});
