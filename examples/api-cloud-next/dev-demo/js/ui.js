export function addButtonListener(buttonId, functionName) {
  const btn = document.getElementById(buttonId);
  btn.addEventListener('click', () => withLoadingIndicator(btn, functionName));
}

async function withLoadingIndicator(button, asyncFunction) {
  button.innerHTML += ' <i class="ti ti-loader-2 loading-indicator"></i>';
  try {
    await asyncFunction();
  } finally {
    const loadingIndicator = button.querySelector('.loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }
}

export function toggleMicrophoneIcon(userId, value) {
  if (value) {
    document.getElementById(`remote-audio-${userId}-off`).setAttribute('hidden', 'hidden');
    document.getElementById(`remote-audio-${userId}`).removeAttribute('hidden');
  } else {
    document.getElementById(`remote-audio-${userId}`).setAttribute('hidden', 'hidden');
    document.getElementById(`remote-audio-${userId}-off`).removeAttribute('hidden');
  }
}

export function toggleCameraIcon(userId, value) {
  if (value) {
    document.getElementById(`remote-video-${userId}-off`).setAttribute('hidden', 'hidden');
    document.getElementById(`remote-video-${userId}`).removeAttribute('hidden');
  } else {
    document.getElementById(`remote-video-${userId}`).setAttribute('hidden', 'hidden');
    document.getElementById(`remote-video-${userId}-off`).removeAttribute('hidden');
  }
}

export function addRemoteView(userId) {
  console.log('[ui] addRemoteVIew', userId);
  document.getElementById('remote-video').innerHTML += `
    <div class="video-box"> 
      <div id="remote-${userId}" class="video"></div>
      <label class="video-label"> ${userId}
        <span>
          <i class="ti ti-device-computer-camera" id="remote-video-${userId}" hidden></i>
          <i class="ti ti-device-computer-camera-off" id="remote-video-${userId}-off"></i>
          <i class="ti ti-microphone" id="remote-audio-${userId}" hidden></i>
          <i class="ti ti-microphone-off" id="remote-audio-${userId}-off"></i>
        </span>
      </label>
    </div>
  `;
}
