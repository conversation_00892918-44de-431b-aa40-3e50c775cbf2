import { genTestUserSig } from '../debug/GenerateTestUserSig-es.js';

export const generateRandomNumber = () => window.crypto.getRandomValues(new Uint16Array(1))[0];
export const generateRandomString = () => window.crypto.getRandomValues(new Uint16Array(1))[0].toString();
export const urlParam = new URLSearchParams(window.location.search);

export const sdkAppId = parseInt(urlParam.get('sdkAppId'), 10) || 1400704311;
export const secretKey = urlParam.get('secretKey');

export const userId = urlParam.get('userId') || `user_${generateRandomNumber()}`;
export const roomId = parseInt(urlParam.get('roomId'), 10) || new Date().getMinutes() * 10 + 88000;

async function getUserSigData({ sdkAppId, userId, roomId }) {
  try {
    const res = await fetch('https://service.trtc.qcloud.com/release/UserSigService', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pwd: '********',
        appid: sdkAppId,
        roomnum: roomId,
        privMap: 255,
        identifier: userId,
        accounttype: 14418
      })
    });
    const data = await res.json();
    document.getElementById('secret-key').value = 'use server';
    return data.data.userSig;
  } catch (e) {
    console.error('userSig server is not available', e);
    return null;
  }
};

export const userSig = urlParam.get('userSig')
                      || (await getUserSigData({ sdkAppId, userId, roomId }))
                      || genTestUserSig({ userID: userId, SDKAppID: sdkAppId, SecretKey: secretKey })?.userSig;

export { genTestUserSig };

console.log('(params) sdkAppId:', sdkAppId, 'secretKey:', secretKey, 'userId:', userId, 'roomId:', roomId, 'userSig:', userSig);

export const getRoomId = () => roomId;
