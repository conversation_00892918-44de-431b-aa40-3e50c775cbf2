<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>v5</title>
  <link href="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/css/fastbootstrap.min.css" rel="stylesheet"
    integrity="sha256-xLGBU65wCDv2/qEdq3ZYw2Qdiia/wxxeGepRyZmpQdY=" crossorigin="anonymous">
</head>

<body>
  <h2>cdn - 两个实例测试 </h2>
  <hr>
  填入校验信息：
  <input type="number" id="sdkAppId" placeholder="please input sdkAppId">
  <input type="text" id="secret-key" placeholder="please input secretKey">
  获取地址: <a href="https://console.cloud.tencent.com/trtc/app" target="_blank" rel="noopener noreferrer"> trtc 控制台 </a>
  <hr>
  <div>
    1. 主播进房并且推流
    <button id="enter" class="btn btn-default"> 进房 + 推流</button>
    <span name="roomId"></span>
    <span name="userId"></span>
  </div>
  <hr>
  <div>
    2. 主播转推摄像头到第三方 cdn <br>
    <button id="tencent" class="btn btn-default"> 转推到腾讯云 </button>
    <br>
    <button id="given" class="btn btn-default"> 转推到第三方 </button>
    <button id="update-given" class="btn btn-default"> 更新转推到第三方 </button> 第三方地址 <input type="text" id="given-url">
    <br>
    <div style="border: 1px solid black; padding: 10px; margin: 20px 0;">
      <button id="mix-start" class="btn btn-default"> 开启混流 </button>
      <button id="mix-to-room" class="btn btn-default"> 混流回推 </button>
      <button id="mix-to-room2" class="btn btn-default"> 混流回推2 </button>
      <button id="mix-to-room3" class="btn btn-default"> 混流回推3 </button>
      <div>
        测试动态去掉混流用户：
        <input type="text" id="mix-roomId" placeholder="请输入混流 roomId">
        <input type="text" id="mix-userId" placeholder="请输入混流 userId">
        <button id="mix-add" class="btn btn-default"> 更新混流混入该用户 </button>
        <button id="mix-remove" class="btn btn-default"> 更新混流去掉该用户 </button>
      </div>
    </div>
    <button id="stop-main" class="btn btn-default"> 停止转推 </button>
  </div>
  <hr>
  <div>
    3. <span name="userId"></span> 创建另一个 trtc 实例并且进房 <br>
    <button id="enter2" class="btn btn-default"> 进入另一个房间 </button>
  </div>
  <hr>
  <div id="local-video" class="video-box"> local </div>
  <div id="cdn-video" class="video-box" style="width: fit-content;"> cdn-video
    <button id="cdn" class="btn">刷新</button>
    url<input id="cdn-url" type="text" style="width: 600px">
  </div>
</body>

</html>

<script src="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/js/fastbootstrap.min.js"
  integrity="sha256-+c+/OCMmtlZadi89VaV1yUOkk1T4BD2pwBFpY3OcrqI=" crossorigin="anonymous"></script>
<script type="module" src="./main.js"></script>
<script src="../dist/trtc.js"></script>
<script src="../dist/plugins/cdn-streaming/cdn-streaming.iife.js"></script>
<script src="https://imgcache.qq.com/open/qcloud/video/vcplayer/TcPlayer-2.3.3.js" charset="utf-8"></script>

<style>
  .video-box {
    width: 333px;
    min-height: 222px;
    border: 1px saddlebrown solid;
    margin: 10px 0;
  }
</style>