/* global TRTC Beauty VirtualBackground CDNStreaming*/

import * as common from './common.js';

document.getElementById('enter').addEventListener('click', enter);
document.getElementById('enter2').addEventListener('click', enter2);
document.getElementById('tencent').addEventListener('click', tencent);
document.getElementById('given').addEventListener('click', given);
document.getElementById('update-given').addEventListener('click', updateGiven);
document.getElementById('mix-start').addEventListener('click', mix);
document.getElementById('mix-add').addEventListener('click', mixAdd);
document.getElementById('mix-remove').addEventListener('click', mixRemove);
document.getElementById('stop-main').addEventListener('click', stopMain);
document.getElementById('cdn').addEventListener('click', playCDN);
document.getElementById('mix-to-room').addEventListener('click', mixToRoom);
document.getElementById('mix-to-room2').addEventListener('click', mixToRoom2);
document.getElementById('mix-to-room3').addEventListener('click', mixToRoom3);
if (common.sdkAppId) document.getElementById('sdkAppId').value = common.sdkAppId;
if (common.secretKey) document.getElementById('secret-key').value = common.secretKey;

const trtc = TRTC.create({ plugins: [CDNStreaming] });

function getAuthDataFromInput(userId) {
  const sdkAppId = parseInt(document.getElementById('sdkAppId').value, 10);
  const secretKey = document.getElementById('secret-key').value;
  if (!sdkAppId || !secretKey) {
    console.error('请填写 sdkAppId 和 secretKey');
  }
  const currentUserId = userId || common.userId;
  const { userSig } = common.genTestUserSig({ userID: currentUserId, SDKAppID: sdkAppId, SecretKey: secretKey });
  return {
    sdkAppId,
    userId: currentUserId,
    userSig,
  };
}

async function enter() {
  await trtc.enterRoom({
    ...getAuthDataFromInput(),
    scene: TRTC.TYPE.SCENE_LIVE,
    roomId: common.roomId,
  });
  trtc.startLocalVideo({ view: 'local-video' });
  trtc.startLocalAudio();

  updatePageInfo();
}

async function enter2() {
  const trtc2 = TRTC.create();
  await trtc2.enterRoom({
    ...getAuthDataFromInput(common.generateRandomNumber().toString()),
    scene: TRTC.TYPE.SCENE_LIVE,
    roomId: common.generateRandomNumber(),
  });
  trtc2.startLocalVideo();
  trtc2.startLocalAudio();
}

function updatePageInfo() {
  document.querySelectorAll('span[name="roomId"]').forEach((span) => {
    // eslint-disable-next-line no-param-reassign
    span.textContent = `房间: ${common.roomId}`;
  });
  document.querySelectorAll('span[name="userId"]').forEach((span) => {
    // eslint-disable-next-line no-param-reassign
    span.textContent = `主播: ${common.userId}`;
  });
}

let cdnVideoPlayer = null;

async function stopMain() {
  await trtc.stopPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
    },
  });
}

async function tencent() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
    },
  });
  const playDomain = 'http://qilongyu.top/';
  // const playDomain = 'http://3891.liveplay.myqcloud.com/';
  const url = `${playDomain}live/${common.sdkAppId}_${common.roomId}_${common.userId}_main.flv`;
  document.getElementById('cdn-url').value = url;
  setTimeout(() => {
    playCDN();
  }, 3000);
}

async function given() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
      appId: common.sdkAppId,
      bizId: 146516,
      url: document.getElementById('given-url').value
    },
  });
  // const playDomain = 'http://qilongyu.top/';
  const playDomain = 'http://3891.liveplay.myqcloud.com/';
  const url = `${playDomain}live/${common.sdkAppId}_${common.roomId}_${common.userId}_main.flv`;
  document.getElementById('cdn-url').value = url;
  setTimeout(() => {
    playCDN();
  }, 3000);
}

async function updateGiven() {
  await trtc.updatePlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMainStreamToCDN,
      appId: common.sdkAppId,
      bizId: 146516,
      url: document.getElementById('given-url').value
    },
  });
}

async function mix() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 480,
      videoBitrate: 1500,
      videoFramerate: 15
    },
    mix: {
      audioMixUserList: [
        {
          userId: common.userId,
          // roomId: common.roomId
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: common.userId,
            // roomId: common.roomId
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
  // const playDomain = 'http://qilongyu.top/';
  const playDomain = 'http://3891.liveplay.myqcloud.com/';
  const url = `${playDomain}live/${common.sdkAppId}_${common.roomId}_${common.userId}_main.flv`;
  document.getElementById('cdn-url').value = url;
  setTimeout(() => {
    playCDN();
  }, 3000);
}

async function mixToRoom() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom,
      robotUser: {
        userId: `push_back_user_${Date.now()}`, // 建议动态生成
        roomId: 67374,
      }
    },
    encoding: {
      videoWidth: 864,
      videoHeight: 960,
      videoBitrate: 1500,
      videoFramerate: 30,
    },
    mix: {
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: 'u1',
            roomId: 67374
          },
          width: 864,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: 'u2',
            roomId: 67374
          },
          width: 864,
          height: 480,
          locationX: 0,
          locationY: 480,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
}

async function mixToRoom2() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom,
      robotUser: {
        userId: `push_back_user_${Date.now()}`, // 建议动态生成
        roomId: 67374,
      }
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 720,
      videoBitrate: 1500,
      videoFramerate: 30,
    },
    mix: {
      audioMixUserList: [
        {
          userId: 'user_123',
          roomId: 3333
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: 'u3',
            roomId: 67374
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: 'u4',
            roomId: 67374
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
}

async function mixToRoom3() {
  await trtc.startPlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToRoom,
      robotUser: {
        userId: `push_back_user_${Date.now()}`, // 建议动态生成
        roomId: 67374,
      }
    },
    encoding: {
      videoWidth: 1728,
      videoHeight: 1920,
      videoBitrate: 4000,
      videoFramerate: 30,
    },
    mix: {
      audioMixUserList: [
        {
          userId: 'u1',
          roomId: 67373
        }
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: 'u1',
            roomId: 67373
          },
          width: 1728,
          height: 960,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: 'u2',
            roomId: 67373
          },
          width: 1728,
          height: 960,
          locationX: 0,
          locationY: 960,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
}

async function mixAdd() {
  await trtc.updatePlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 480,
      videoBitrate: 1500,
      videoFramerate: 15
    },
    mix: {
      audioMixUserList: [
        {
          userId: common.userId,
          roomId: common.roomId
        },
        {
          userId: document.getElementById('mix-userId').value,
          roomId: Number(document.getElementById('mix-roomId').value),
        },
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: common.userId,
            roomId: common.roomId
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
        {
          fixedVideoUser: {
            userId: document.getElementById('mix-userId').value,
            roomId: Number(document.getElementById('mix-roomId').value),
          },
          width: 640,
          height: 480,
          locationX: 640,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 2
        },
      ]
    }
  });
}

async function mixRemove() {
  await trtc.updatePlugin('CDNStreaming', {
    target: {
      publishMode: CDNStreaming.TYPE.PublishMode.PublishMixStreamToCDN,
    },
    encoding: {
      videoWidth: 1280,
      videoHeight: 480,
      videoBitrate: 1500,
      videoFramerate: 15
    },
    mix: {
      audioMixUserList: [
        {
          userId: common.userId,
          roomId: common.roomId
        },
      ],
      videoLayoutList: [
        {
          fixedVideoUser: {
            userId: common.userId,
            roomId: common.roomId
          },
          width: 640,
          height: 480,
          locationX: 0,
          locationY: 0,
          fixedVideoStreamType: TRTC.TYPE.STREAM_TYPE_MAIN,
          zOrder: 1
        },
      ]
    }
  });
}

function playCDN() {
  if (cdnVideoPlayer) cdnVideoPlayer.destroy();
  const url = document.getElementById('cdn-url').value;
  cdnVideoPlayer = new TcPlayer('cdn-video', {
    flv: url, // 请替换成实际可用的播放地址
    h5_flv: true,
    autoplay: true, // iOS 下 safari 浏览器，以及大部分移动端浏览器是不开放视频自动播放这个能力的
    width: '320', // 视频的显示宽度，请尽量使用视频分辨率宽度
    height: '240' // 视频的显示高度，请尽量使用视频分辨率高度
  });
}

// --------------------------------- Error ---------------------------------

function showError(message) {
  // Recalculate nextErrorTop
  let nextErrorTop = 10;
  const errorDivs = document.querySelectorAll('div');
  errorDivs.forEach((div) => {
    if (div.style.position === 'fixed' && div.style.right === '10px' && div.style.backgroundColor === 'red') {
      const bottom = div.offsetTop + div.offsetHeight;
      if (bottom + 10 > nextErrorTop) {
        nextErrorTop = bottom + 10;
      }
    }
  });

  const errorDiv = document.createElement('div');
  errorDiv.textContent = message;
  errorDiv.style.position = 'fixed';
  errorDiv.style.right = '10px';
  errorDiv.style.top = `${nextErrorTop}px`;
  errorDiv.style.backgroundColor = 'red';
  errorDiv.style.color = 'white';
  errorDiv.style.padding = '10px';
  errorDiv.style.borderRadius = '5px';
  errorDiv.style.zIndex = '1000';
  document.body.appendChild(errorDiv);

  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

const originalConsoleError = console.error;
console.error = function () {
  originalConsoleError.apply(console, arguments);
  showError(Array.prototype.join.call(arguments, ' '));
};
