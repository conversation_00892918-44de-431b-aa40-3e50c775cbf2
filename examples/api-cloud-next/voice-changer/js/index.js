/* eslint-disable no-undef */
/* global $ TRTC Toastify */
/* eslint-disable require-jsdoc */
let trtcWeakRef;
let weakRefIntervalId = -1;
const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const defaultSdkAppId = Number(new URLSearchParams(location.search).get('sdkAppId') || **********); // unified sdkAppId for Demos on all platforms.
// const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
// 与 native 互通的 appid
// const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
const accountType = 14418;

window.onunhandledrejection = (event) => {
  ToastError(event.reason);
};

const autoReceiveAudio = new URLSearchParams(location.search).get('autoReceiveAudio');
if (autoReceiveAudio) {
  document.getElementById('auto-receive-audio').selectedIndex = +autoReceiveAudio;
}

const autoReceiveVideo = new URLSearchParams(location.search).get('autoReceiveVideo');
if (autoReceiveVideo) {
  document.getElementById('auto-receive-video').selectedIndex = +autoReceiveVideo;
}
const spc = new URLSearchParams(location.search).get('spc');
if (spc) {
  $('#enable-spc')[0].checked = (Boolean(+spc));
}
let recordPCM;

// preset before starting RTC
class Presetting {
  init() {
    // populate userId/roomId/privMap
    $('#userId').val(`u${parseInt(Math.random() * *********)}`);
    $('#userId').trigger('change'); // 触发 bootstrap 的 is-filled
    $('#roomId').val(new Date().getMinutes() * 10 + 66666);
    $('#roomId').trigger('change');
    const roomId = this.query('roomId');
    const userId = this.query('userId');
    if (roomId) {
      $('#roomId').val(roomId);
    }
    if (userId) {
      $('#userId').val(userId);
    }
    const audioProfile = this.query('audioProfile');
    if (audioProfile) {
      $('#audio-profile').val(audioProfile);
    }
    const AIDenoiser = this.query('AIDenoiser') || 'false';
    if (AIDenoiser === 'true') {
      $('#ai-denoiser').attr('checked', true);
    } else {
      $('#ai-denoiser').attr('checked', false);
    }
    const enableSEI = this.query('enableSEI');
    if (enableSEI === 'true') {
      $('#enable-sei').attr('checked', true);
    } else {
      $('#enable-sei').attr('checked', false);
    }

    if (this.query('forceTurn')) {
      document.getElementById('force-turn').checked = true;
    }
  }

  query(name) {
    const match = window.location.search.match(new RegExp(`(\\?|&)${name}=([^&]*)(&|$)`));
    return !match ? '' : decodeURIComponent(match[2]);
  }

  login(share, callback) {
    let userId = $('#userId')
      .val();
    if (share) {
      userId = `share_${parseInt(Math.random() * *********)}`;
    }
    const sdkAppId = this.query('sdkAppId') || defaultSdkAppId;
    const roomId = $('#roomId')
      .val();
    if (genTestUserSig && location.search.includes('secretKey')) {
      const { userSig } = genTestUserSig(userId);
      return callback({
        sdkAppId,
        userId,
        userSig,
        roomId,
        privateMapKey: '',
      });
    }
    $.ajax({
      type: 'POST',
      url: fetchUrl,
      dataType: 'json',
      data: JSON.stringify({
        pwd: '********',
        appid: parseInt(sdkAppId),
        roomnum: parseInt(roomId),
        privMap: 255,
        identifier: userId,
        accounttype: accountType,
      }),
      success(json) {
        if (json && json.errorCode === 0) {
          const { userSig } = json.data;
          const privateMapKey = json.data.privMapEncrypt;
          callback({
            sdkAppId,
            userId,
            userSig,
            roomId,
            privateMapKey,
          });
        } else {
          console.error(`got invalid json:${json}`);
        }
      },
      error(err) {
        console.error('failed to retreive userSig');
      },
    });
  }
}

async function getUserSigFromServer(sdkAppId, roomId, userId) {
  const response = await $.ajax({
    type: 'POST',
    url: fetchUrl,
    dataType: 'json',
    data: JSON.stringify({
      pwd: '********',
      appid: parseInt(sdkAppId),
      roomnum: parseInt(roomId),
      privMap: 255,
      identifier: userId,
      accounttype: 14418,
    })
  });
  if (!response.data) return undefined;
  return response.data.userSig;
}

const extraLocalViews = [];

TRTC.setLogLevel(1);

class RtcClient {
  constructor(options) {
    this.sdkAppId_ = Number(options.sdkAppId);
    this.userId_ = options.userId;
    this.userSig_ = options.userSig;
    this.roomId_ = options.roomId;
    this.strRoomId_ = options.strRoomId_ || new URLSearchParams(location.search).get('strRoomId');
    $('#strRoomId').val(this.strRoomId_);
    if (this.strRoomId_) {
      $('#strRoomId').trigger('change');
    }
    this.privateMapKey_ = options.privateMapKey;

    this.isJoined_ = false;
    this.isPublished_ = false;

    this.initTRTC();
  }

  updateParams(options) {
    console.log('updateParams', options);
    if (typeof options.userId !== 'undefined' && typeof options.userSig !== 'undefined') {
      this.userId_ = options.userId;
      this.userSig_ = options.userSig;
    }
    if (typeof options.roomId !== 'undefined') {
      this.roomId_ = options.roomId;
    }
    if (typeof options.strRoomId !== 'undefined') {
      this.strRoomId_ = options.strRoomId;
    }
  }

  initTRTC() {
    if (this.trtc) return;

    const pluginsList = [];
    // if (pluginMap.VirtualBackground?.loaded) pluginsList.push(VirtualBackground);
    // if (pluginMap.Watermark?.loaded) pluginsList.push(Watermark);
    // if (pluginMap.Beauty?.loaded) pluginsList.push(Beauty);
    // if (pluginMap.BasicBeauty?.loaded) pluginsList.push(BasicBeauty);
    // if (pluginMap.DeviceDetector?.loaded) pluginsList.push(DeviceDetector);
    // if (pluginMap.Debug?.loaded) pluginsList.push(Debug);
    // if (pluginMap.CrossRoom?.loaded) pluginsList.push(CrossRoom);
    // if (pluginMap.TRTCVideoDecoder?.loaded) pluginsList.push(TRTCVideoDecoder);
    if (pluginMap.VoiceChanger?.loaded) pluginsList.push(VoiceChanger);
    console.log('pluginsList', pluginsList);

    this.trtc = TRTC.create({
      iceTransportPolicy: 'all',
      enableSPC: $('#enable-spc')[0].checked,
      enableSEI: $('#enable-sei')[0].checked,
      plugins: pluginsList,
      assetsPath: './js/assets/',
    });

    // 如果是 dev 或 npm-latest, 通过 trtc._room._version 获取当前版本展示
    const version = $('#version').val();
    if (version === 'dev' || version === 'latest') {
      const selectedOption = $('#version option:selected');
      if (selectedOption) {
        selectedOption.text(`${version === 'latest' ? 'npm-latest' : version} (${this.trtc._room._version})`);
      }
    }

    clearInterval(weakRefIntervalId);
    trtcWeakRef = new WeakRef(this.trtc);

    this.trtc.enableAudioVolumeEvaluation(200);
    this.trtc.on('sei-message', (event) => {
      console.log(`received sei message from ${event.userId} ${event.streamType} ${(new TextDecoder()).decode(event.data)}`);

      const toastSEI = !new URLSearchParams(location.search).get('disableSEIToast');
      if (toastSEI) {
        Toast(`received sei message from ${event.userId} ${event.streamType} ${(new TextDecoder()).decode(event.data)}`);
      }
    });
    this.trtc.on('video-decode-downgrade-state-changed', (event) => {
      console.log(`video-decode-downgrade-state-changed ${event.userId} ${event.streamType} ${event.prevState} -> ${event.state}, reason: ${event.reason}`);
      Toast(`video-decode-downgrade-state-changed ${event.userId} ${event.prevState} -> ${event.state}, reason: ${event.reason}`);
      const span = document.getElementById(`${event.userId}_${event.streamType || 'main'}_downgrade-state`);
      console.warn('video-decode-downgrade-state-changed span', span);
      if (span) {
        let text = `${event.type}(${event.renderer})`;
        if (event.state === 'STARTING') {
          text += '...';
        }
        span.textContent = text;
      }
    });
    this.remoteUserIdSet = new Set();
    this.remoteUserIdSet.add('*');
    this.remotePublishedMainVideoUserIdSet = new Set();
    this.remotePublishedSubVideoUserIdSet = new Set();
    this.remotePublishedAudioUserIdSet = new Set();
    this.startedRemoteVideoUserIdSet = new Set();
    this.handleEvents();
  }

  join() {
    const enableAutoPlayDialog = new URLSearchParams(location.search).get('enableAutoPlayDialog');
    if (!this.trtc) {
      this.initTRTC();
    }
    const roomId = this.strRoomId_ ? { strRoomId: this.strRoomId_ } : { roomId: Number(this.roomId_) };
    if ($('#webcodecsDecode')[0].checked) {
      this.trtc.startPlugin('VideoDecoder', {
        type: 'webCodecs',
        config: {
          codec: 'avc'
        }
      });
    } else if ($('#ffmpegDecode')[0].checked) {
      this.trtc.startPlugin('VideoDecoder', {
        type: 'wasm',
        outputFormat: $('#yuvMode')[0].checked ? 'yuv' : 'videoFrame',
        config: {
          codec: 'avc'
        }
      });
    }
    this.trtc.enterRoom({
      sdkAppId: this.sdkAppId_,
      userId: new URLSearchParams(location.search).get('userId') || this.userId_,
      userSig: new URLSearchParams(location.search).get('userSig') || this.userSig_,
      ...roomId,
      privateMapKey: this.privateMapKey_,
      latencyLevel: getLatencyLevel(),
      proxy: getProxyServer(),
      // proxy: 'wss://gz-signaling.rtc.qq.com',
      // proxy: {
      //   websocketProxy: 'wss://gz-signaling.rtc.qq.com',
      //   scheduleProxy: 'schedule.cloud-rtc.com'
      // },
      role: getRole(),
      scene: getScene(),
      autoReceiveAudio: isAutoReceiveAudio(),
      autoReceiveVideo: isAutoReceiveVideo(),
      enableAutoPlayDialog: enableAutoPlayDialog ? Boolean(Number(enableAutoPlayDialog)) : true,
      enableSEI: $('#enable-sei')[0].checked,
      preferHW: $('#HWEncoder')[0].checked,
      latencyLevel: Number(new URLSearchParams(location.search).get('latencyLevel')) || void 0,
      useVp8: $('#vp8')[0].checked
    }).then(() => {
      Toast('enterRoom success');
      // eslint-disable-next-line max-len
      const { uint64_start_time, uint64_end_time, int32_schedule_cost, uint64_send_request_acc_ip_cmd_end_time, uint64_send_request_acc_ip_cmd_start_time, uint64_send_request_enter_room_cmd_end_time, uint64_send_request_enter_room_cmd_start_time } = this.trtc._room.keyPointManager._pathJoinRoom;
      // eslint-disable-next-line camelcase
      console.warn(`进房耗时: ${uint64_end_time - uint64_start_time}ms(${int32_schedule_cost} ${uint64_send_request_acc_ip_cmd_end_time - uint64_send_request_acc_ip_cmd_start_time} ${uint64_send_request_enter_room_cmd_end_time - uint64_send_request_enter_room_cmd_start_time})`);
      if (this.userId_ === 'test1') {
        const encoder = new TextEncoder();
        rtc.trtc.sendCustomMessage({
          cmdId: +document.getElementById('cmdId').value,
          data: encoder.encode('test msg 1').buffer
        });
        rtc.trtc.sendCustomMessage({
          cmdId: +document.getElementById('cmdId').value,
          data: encoder.encode('test msg 2').buffer
        });
      }
    })
      .catch((error) => {
        console.warn(error);
        ToastError(error);
      });
  }

  leave() {
    this.trtc.exitRoom().then(() => {
      Toast('exitRoom success');
    });;
  }

  destroy() {
    this.trtc.destroy();
    Toast('destroy success');
    this.trtc = null;
    // 测试垃圾回收
    weakRefIntervalId = setInterval(() => {
      if (trtcWeakRef.deref()) {
        console.error('trtc is not gc. Please go to Chrome devtool -> Memory Tab -> click "Collect rubbish" button.');
      } else {
        console.log('trtc is successfully garbage collected!');
        clearInterval(weakRefIntervalId);
      }
    }, 1000);
  }

  // 自定义采集
  // async startLocalVideo() {
  //   const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
  //   this.trtc.startLocalVideo({
  // view: 'local_stream', option: { videoSource: stream.getTracks()[0], mirror: false, fillMode: 'contain' } });
  // }

  startLocalVideo(cameraName) {
    // const div = addLocalStreamDiv();
    const div = addView('local_stream', TRTC.TYPE.STREAM_TYPE_MAIN, true);
    const width = +new URLSearchParams(location.search).get('width');
    const height = +new URLSearchParams(location.search).get('height');
    const frameRate = +new URLSearchParams(location.search).get('frameRate');
    const bitrate = +new URLSearchParams(location.search).get('bitrate');
    const mainContainer = document.querySelector('#main');
    const mainView = mainContainer.childNodes.length === 0 ? mainContainer : 'local_stream';
    const views = extraLocalViews.concat(mainView);
    const convertedViews = views.map((item) => {
      if (typeof item === 'string') {
        return document.getElementById(item);
      }
      return item;
    });
    console.warn(convertedViews);
    this.trtc.startLocalVideo({
      view: div,
      option: {
        cameraId: getCameraId(cameraName),
        profile: width ? { width, height, frameRate: frameRate || 15, bitrate: bitrate || 500 } : getVideoProfile(),
        mirror: false,
        small: $('#small')[0].checked ? '240p' : undefined,
        fillMode: 'contain'
      },
    }).catch((error) => {
      if (error.extraCode === 5302) {
        ToastError(error);
        if (error.handler) {
          setTimeout(error.handler, 1000);
        }
      }
      throw error;
    });;
  }

  switchCamera() {
    this.trtc.updateLocalVideo({
      option: { cameraId: getCameraId() },
    });
  }

  switchMicrophone() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }

  updateVideoProfile() {
    this.trtc.updateLocalVideo({
      option: { profile: getVideoProfile() },
    });
  }

  updateAudioProfile() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId(), profile: getAudioProfile() },
    });
  }
  publishSmall(small) {
    this.trtc.updateLocalVideo({ option: { small } }).catch(() => { });
  }
  subscribeSmall(userId) {
    if (userId === '*') {
      this.remotePublishedMainVideoUserIdSet.forEach((remoteUserId) => {
        this.trtc.updateRemoteVideo({
          userId: remoteUserId,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: true }
        });
      });
    } else {
      this.trtc.updateRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: true } });
    }
  }

  subscribeBig(userId) {
    if (userId === '*') {
      this.remotePublishedMainVideoUserIdSet.forEach((remoteUserId) => {
        this.trtc.updateRemoteVideo({
          userId: remoteUserId,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: false }
        });
      });
    } else {
      this.trtc.updateRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: false } });
    }
  }

  stopLocalVideo() {
    removeLocalStreamDiv();
    this.trtc.stopLocalVideo();
  }
  stopPreviewLocalVideo() {
    this.trtc.updateLocalVideo({ view: null, publish: true });
  }
  switchRole(role) {
    this.trtc.switchRole(role, { latencyLevel: getLatencyLevel() });
  }
  updateRemoteUserOptionTag() {
    document.querySelector('#remote-userId').innerHTML = null;
    this.remoteUserIdSet.forEach((remoteUserId) => {
      $('<option/>', {
        value: remoteUserId,
        text: remoteUserId,
      })
        .appendTo('#remote-userId');
    });
  }
  handleEvents() {
    this.trtc.on(TRTC.EVENT.TRACK, (event) => {
      console.warn('ontrack ', event);
    });
    this.trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, (event) => {
      console.warn('on custom message ', event);
      Toast(`receive custom msg from ${event.userId} cmdId: ${event.cmdId} seq: ${event.seq} data: ${new TextDecoder().decode(event.data)}`);
    });
    this.trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, ({ userId }) => {
      this.remoteUserIdSet.add(userId);
      this.updateRemoteUserOptionTag();
    });
    this.trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, ({ userId }) => {
      this.remoteUserIdSet.delete(userId);
      this.updateRemoteUserOptionTag();
    });
    this.trtc.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
      let text = `${event.mediaType} publish ${event.state}`;
      if (event.state === 'stopped') {
        text += ` reason: ${event.reason}`;
        if (event.reason === 'error') {
          text += ` error: ${event.error.code} ${event.error.extraCode} ${event.error.message}`;
          return ToastError(text);
        }
      }
      if (event.state === 'started') {
        setInterval(() => {
          if (!this.trtc || !this.trtc._room || !this.trtc._room._stats) return;
          const encoder = this.trtc._room._stats._prevEncoderImplementation;
          const HWSet = new Set(['ExternalEncoder', 'VideoToolbox']);
          const SWSet = new Set(['OpenH264', 'libvpx']);
          if (encoder) {
            document.querySelector('#encoderImplementation').innerHTML = `编码器：${encoder}`;
            if (HWSet.has(encoder)) {
              document.querySelector('#encoderImplementation').innerHTML += '(硬编)';
            } else if (SWSet.has(encoder)) {
              document.querySelector('#encoderImplementation').innerHTML += '(软编)';
            }
          }
        }, 2000);
      }
      Toast(text);
    });
    this.trtc.on(TRTC.EVENT.KICKED_OUT, console.error);
    this.trtc.on('screen-share-stopped', () => console.warn('screen share stopped'));
    this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, ({ userId }) => {
      console.warn(`remote-audio-available ${userId}`);
      this.remotePublishedAudioUserIdSet.add(userId);
      addView(userId, 'main');
      if (!isAutoReceiveAudio()) {
        // this.trtc.muteRemoteAudio(userId, false);
      }
    });
    this.trtc.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
      console.warn(event);
      updateDeviceList();
      Toast(`${event.type} ${event.action} ${event.device.label}`);

      if (event.action === 'add') {
        if (event.type === 'camera') {
          if (this.trtc.getVideoTrack()) {
            this.trtc.updateLocalVideo({ option: { cameraId: event.device.deviceId } });
          }
        } else if (event.type === 'microphone') {
          if (this.trtc.getAudioTrack()) {
            this.trtc.updateLocalAudio({ option: { microphoneId: event.device.deviceId } });
          }
        }
      }
    });
    this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
      console.warn(`remote-audio-unavailable ${event.userId}`);
      this.remotePublishedAudioUserIdSet.delete(event.userId);
      // this.trtc.muteRemoteAudio(event.userId, true);
    });
    this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
      console.warn(`remote-video-available ${userId} ${streamType}`);
      try {
        const div = addView(userId, streamType);
        // observer.observe(div);

        if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          this.remotePublishedMainVideoUserIdSet.add(userId);
        } else {
          this.remotePublishedSubVideoUserIdSet.add(userId);
        }
        if (isAutoReceiveVideo()) {
          this.startRemoteVideo(div, userId, streamType);
        }
      } catch (error) {
        // debugger;
        console.error(error);
      }
    });
    this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
      console.warn(`remote-video-unavailable ${JSON.stringify(event)}`);
      if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
        this.remotePublishedMainVideoUserIdSet.delete(event.userId);
      } else {
        this.remotePublishedSubVideoUserIdSet.delete(event.userId);
      }
      removeView(event.userId, event.streamType);
    });
    this.trtc.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
      event.result.forEach(({ userId, volume }) => {
        const container = userId === '' ? document.getElementById('local_stream') : document.getElementById(`${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
        if (container) {
          let volumeSpan = container.querySelector('#volume-span');
          if (!volumeSpan) {
            volumeSpan = document.createElement('span');
            volumeSpan.style.position = 'absolute';
            volumeSpan.style.top = '24px';
            volumeSpan.style.left = '0';
            volumeSpan.style.background = 'black';
            volumeSpan.style.color = '#fff';
            volumeSpan.style.zIndex = 999;
            container.appendChild(volumeSpan);
          }
          volumeSpan.id = 'volume-span';
          volumeSpan.innerText = `音量：${volume}`;
        }
      });
    });
    // this.trtc.on('network-quality', console.debug);
    this.trtc.on('audio-play-state-changed', console.warn);
    this.trtc.on('video-play-state-changed', (event) => {
      console.warn(event);
      if (event.userId !== '') {
        const id = `${event.userId}_${event.streamType}`;
        const view = document.getElementById(id);
        const loading = view.querySelector('.loading');
        loading.style.display = event.state === 'PLAYING' ? 'none' : 'block';
      }
    });
    this.trtc.on(TRTC.EVENT.ERROR, (error) => {
      console.error('rtc error:', error);
      ToastError(error);
    });
    this.trtc.on('connection-state-changed', console.warn);
    this.trtc.on('autoplay-failed', () => {
      // const button = document.createElement('button');
      // button.innerText = '自动播放失败，点击恢复播放';
      // button.onclick = () => document.body.removeChild(button);
      // document.body.appendChild(button);
    });
    let prevBytesSent = 0;
    let prevBytesReceived = 0;
    this.trtc.on(TRTC.EVENT.STATISTICS, (stat) => {
      const byteRateSent = (stat.bytesSent - prevBytesSent) / 2;
      const byteRateReceived = (stat.bytesReceived - prevBytesReceived) / 2;
      prevBytesSent = stat.bytesSent;
      prevBytesReceived = stat.bytesReceived;
      const statElement = document.querySelector('#debug-stat');
      let html = `rtt: ${stat.rtt} ms upLoss: ${stat.upLoss} downLoss: ${stat.downLoss} <br>
        ↑ ${byteRateSent / 1000} KB/s ${8 * byteRateSent / 1000} Kb/s <br>
        ↓ ${byteRateReceived / 1000} KB/s ${8 * byteRateReceived / 1000} Kb/s <br><br>
      `;
      if (stat.localStatistics.audio.bitrate) {
        html += `<li>local audio: ${stat.localStatistics.audio.bitrate} Kb/s audioLevel: ${stat.localStatistics.audio.audioLevel}</li>`;
      }
      if (stat.localStatistics.video.length > 0) {
        html += `${stat.localStatistics.video.map(item => `<li>local ${item.videoType} video: ${item.bitrate} Kb/s ${item.width}*${item.height} ${item.frameRate}fps</li>`).join('')}<br>`;
      }
      if (stat.remoteStatistics.length > 0) {
        stat.remoteStatistics.forEach((remoteStat) => {
          let tempHtml = `remote ${remoteStat.userId}:`;
          if (remoteStat.audio.bitrate > 0) {
            tempHtml += `<br><li>audio: ${remoteStat.audio.bitrate} Kb/s audioLevel: ${remoteStat.audio.audioLevel}</li>`;
          }
          if (remoteStat.video.length > 0) {
            remoteStat.video.forEach((videoItem) => {
              tempHtml += `<li>${videoItem.videoType} video: ${videoItem.bitrate} Kb/s ${videoItem.width}*${videoItem.height} ${videoItem.frameRate}fps</li>`;
            });
          }
          html += tempHtml;
        });
      }
      statElement.innerHTML = html;
    });
  }
  startLocalAudio() {
    // addLocalStreamDiv();
    this.trtc.startLocalAudio({
      publish: true,
      option: {
        microphoneId: getMicrophoneId(),
        captureVolume: Number($('#volume-local').val()),
        echoCancellation: document.querySelector('#aec').checked,
        noiseSuppression: document.querySelector('#ans').checked,
        autoGainControl: document.querySelector('#agc').checked,
        profile: getAudioProfile(),
      },
    }).then(async () => {
      if (document.querySelector('#ai-denoiser').checked) {
        await this.trtc.startPlugin('AIDenoiser', {
          sdkAppId: this.sdkAppId_,
          userId: new URLSearchParams(location.search).get('userId') || this.userId_,
          userSig: new URLSearchParams(location.search).get('userSig') || this.userSig_,
        });
      }
      if (document.querySelector('#bgm').checked) {
        this.trtc.startPlugin('AudioMixer', {
          id: 'bgm',
          url: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/assets/music/bgm.mp3',
          loop: true,
        });
      }
    })
      .catch((error) => {
        ToastError(error);
        if (error.handler) {
          setTimeout(error.handler, 1000);
        }
      });
  }

  stopLocalAudio() {
    rtc.trtc.stopPlugin('VoiceChanger');
    this.trtc.stopLocalAudio();
  }

  startRemoteVideo(view, userId, streamType) {
    if ($('#sub-num').val() !== '' && this.startedRemoteVideoUserIdSet.size >= +$('#sub-num').val()) {
      return;
    }
    this.startedRemoteVideoUserIdSet.add(`${userId}_${streamType}`);
    return this.trtc.startRemoteVideo({
      view,
      userId,
      streamType,
      option: {
        fillMode: 'contain',
        small: $('#auto_sub_small')[0].checked,
        receiveWhenViewVisible: false,
        viewRoot: document.getElementById('side')
      }
    })
      .catch((error) => {
        ToastError(error);
        this.startedRemoteVideoUserIdSet.delete(`${userId}_${streamType}`);
      });
  }

  stopRemoteVideo({ userId, streamType }) {
    return this.trtc.stopRemoteVideo({ userId, streamType }).then(() => {
      this.startedRemoteVideoUserIdSet.delete(`${userId}_${streamType}`);
    });
  }

  updateRemoveVideoOnMain(userId, streamType) {
    this.trtc.updateRemoteVideo({ view: document.querySelector('#main'), userId, streamType, option: { small: false, fillMode: 'contain', receiveWhenViewVisible: false } });
  }
  updateRemoveVideoOnSide(sideNode, userId, streamType) {
    this.trtc.updateRemoteVideo({ view: sideNode, userId, streamType, option: { small: true, fillMode: 'cover', receiveWhenViewVisible: true, viewRoot: document.getElementById('side') } });
  }
}

function getUserIdAndStreamTypeFromId(id) {
  return {
    userId: id.split('_').slice(0, -1)
      .join('_')
      .substr(1),
    streamType: id.split('_').pop()
  };
}

// // 只拉可视区的视频，不可视区不拉取。
// const observer = new IntersectionObserver((entries, observer) => {
//   entries.forEach((entry) => {
//     const { userId, streamType } = getUserIdAndStreamTypeFromId(entry.target.id);
//     if (entry.isIntersecting) {
//       if (isAutoReceiveVideo()) {
//         rtc.startRemoteVideo(entry.target, userId, streamType);
//       }
//     } else {
//       // 远端流正在在主窗口显示，不停止拉流
//       if (!entry.target.querySelector('video')) return;
//       rtc.stopRemoteVideo({ userId, streamType });
//     }
//   });
// }, { root: document.querySelector('#side'), });

const isMainScreen = {};

async function playOnMain(userId, streamType) {
  const main = document.querySelector('#main');
  if (main.querySelector('video') || main.querySelector('canvas')) {
    Toast('main already has video');
    return;
  }
  if (userId === 'local_stream') {
    rtc.trtc.updateLocalVideo({ view: main });
  } else if (userId === 'local_screen') {
    rtc.trtc.updateScreenShare({ view: main });
  } else {
    rtc.updateRemoveVideoOnMain(userId, streamType);
  }
}

function addView(userId, streamType, isLocal = false) {
  const id = `${userId}_${streamType}`;
  if (!document.getElementById(id)) {
    const div = document.createElement('div');
    div.id = id;
    div.className = 'flex-center';
    div.style.position = 'relative';
    div.style.minHeight = '100px';
    div.appendChild(createToolHeader(userId, streamType, isLocal));
    if (isLocal) {
      document.querySelector('#side').insertBefore(div, document.querySelector('#side').firstChild);
    } else {
      document.querySelector('#side').appendChild(div);
      const loading = document.createElement('div');
      loading.className = 'loading';

      div.appendChild(loading);
    }
    return div;
  }
  return document.getElementById(id);
}
function createAudioDowngradeTest(userId) {
  const userIdSpan = document.createElement('button');
  userIdSpan.innerText = '音频降级';
  userIdSpan.style.userSelect = 'none';
  userIdSpan.style.cursor = 'pointer';
  userIdSpan.onclick = (event) => {
    event.stopPropagation();
    rtc.trtc._room.remotePublishedUserMap.get(userId).remoteAudioTrack.emit('decode-failed');
  };
  return userIdSpan;
}

function createVideoDowngradeMock(userId, streamType) {
  const userIdSpan = document.createElement('button');
  userIdSpan.innerText = '视频降级';
  userIdSpan.style.userSelect = 'none';
  userIdSpan.style.cursor = 'pointer';
  userIdSpan.onclick = (event) => {
    event.stopPropagation();
    const track = streamType === 'main' ? rtc.trtc._room.remotePublishedUserMap.get(userId).remoteVideoTrack : rtc.trtc._room.remotePublishedUserMap.get(userId).remoteAuxiliaryTrack;
    console.warn('updatePlugin downgrade streamType', streamType, track);
    rtc.trtc.updatePlugin('TRTCVideoDecoder', { type: 'mock', track }).catch(() => {
      track.emit('decode-failed');
    });
  };
  return userIdSpan;
}

const isMobile = () => {
  const userAgent = (window.navigator && window.navigator.userAgent) || '';
  const isIOS = /iPad/i.test(userAgent) || /iPhone/i.test(userAgent);
  const isAndroid = /Android/i.test(userAgent);
  console.warn('isIOS', isIOS, 'isAndroid', isAndroid);
  return isIOS || isAndroid;
};

function createToolHeader(userId, streamType, isLocal = false) {
  console.warn('createToolHeader', userId, streamType, isLocal);
  const div = document.createElement('div');
  div.style.position = 'absolute';
  div.style.top = 0;
  div.style.left = 0;
  div.style.zIndex = 20;
  div.style.display = 'flex';
  div.style.flexDirection = 'row';
  div.style.alignItems = 'center';
  const name = `${userId}_${streamType}`;
  const mainScreenButton = document.createElement('button');
  if (isMainScreen[name] === undefined) {
    isMainScreen[name] = false;
  }
  mainScreenButton.innerText = isMainScreen[name] ? '退出大屏' : '全屏';
  mainScreenButton.onclick = () => {
    const theMainScreenUserId = Object.keys(isMainScreen).find(key => isMainScreen[key]);
    if (theMainScreenUserId && theMainScreenUserId !== name) {
      Toast('已经有其他人全屏了');
      return;
    }
    if (isMainScreen[name]) {
      if (userId === 'local_stream') {
        rtc.trtc.updateLocalVideo({ view: document.querySelector('#local_stream_main') });
      } else if (userId === 'local_screen') {
        rtc.trtc.updateScreenShare({ view: document.querySelector('#local_screen_sub') });
      } else {
        rtc.updateRemoveVideoOnSide(document.getElementById(name), userId, streamType);
      }
    } else {
      playOnMain(userId, streamType);
    }
    console.warn('!isMainScreen[name]', !isMainScreen[name]);
    isMainScreen[name] = !isMainScreen[name];
    console.warn('set isMainScreen[name]', name, isMainScreen[name]);
    mainScreenButton.innerText = isMainScreen[name] ? '退出大屏' : '全屏';
  };
  div.appendChild(createUserIdSpan(isLocal ? '我' : name));
  if (!isMobile()) div.appendChild(mainScreenButton);
  div.appendChild(createVideoDowngradeMock(userId, streamType));
  div.appendChild(createAudioDowngradeTest(userId));
  const span = document.createElement('span');
  span.id = `${userId}_${streamType}_downgrade-state`;
  span.textContent = 'hardware';
  span.style.background = 'black';
  div.appendChild(span);
  return div;
}

function createUserIdSpan(userId) {
  const userIdSpan = document.createElement('span');
  userIdSpan.innerText = userId;
  userIdSpan.style.background = 'black';
  userIdSpan.style.color = '#fff';
  userIdSpan.onclick = event => event.stopPropagation();
  return userIdSpan;
}

function addLocalStreamDiv() {
  if (document.querySelector('#local_stream')) return;
  const div = document.createElement('div');
  div.id = 'local_stream';
  div.style.position = 'relative';
  div.appendChild(createUserIdSpan('Me'));
  const main = document.querySelector('#main');
  const side = document.querySelector('#side');
  if (side.children.length > 0) {
    side.insertBefore(div, side.children[0]);
  } else {
    side.appendChild(div);
  }
  return div;
}

function removeLocalStreamDiv() {
  document.querySelector('#local_stream_main').remove();
}

function removeView(userId, streamType) {
  const id = `${userId}_${streamType}`;
  document.getElementById(id) && document.getElementById(id).remove();
}

let rtc = null;
const presetting = new Presetting();

// initialize sdkAppId/userId/userSig stuffs
(function login() {
  presetting.init();
  presetting.login(false, (options) => {
    rtc = new RtcClient(options);
    const params = new URLSearchParams(location.search);
    if (params.get('autoJoin') === '1') {
      rtc.join();
    };
    if (params.get('autoCamera')) {
      rtc.startLocalVideo(params.get('autoCamera'));
    }
    if (params.get('closeSetting') === '1') {
      document.querySelector('#close-setting').click();
    }
  });
}());

$('#trtc2').on('click', async () => {
  const trtc2 = TRTC.create();
  trtc2.use(VirtualBackground);
  const view = document.createElement('div');
  view.style.width = '500px';
  view.style.height = '500px';
  document.body.appendChild(view);
  await trtc2.startLocalAudio();
  await trtc2.startLocalVideo({ view });
  const authData = {
    sdkAppId: defaultSdkAppId,
    userId: 'qer',
    userSig: await getUserSigFromServer(defaultSdkAppId, $('#roomId').val(), 'qer')
  };
  trtc2.enterRoom({
    roomId: Number($('#roomId').val()),
    ...authData
  });
  trtc2.startPlugin('AIDenoiser', {
    assetsPath: './js',
    ...authData
  });
  trtc2.startPlugin('VirtualBackground', {
    ...authData
  });
});

$('#switch-bad-cam').on('click', () => {
  rtc.trtc.updateLocalVideo({ option: { cameraId: 'bad' } });
});

$('#userId')
  .on('change', (e) => {
    e.preventDefault();
    console.log('userId changed');
    presetting.login(false, (options) => {
      delete options.roomId;
      rtc.updateParams(options);
    });
  });
$('#roomId')
  .on('input', (e) => {
    e.preventDefault();
    console.log(`roomId changed ${e.target.value}`);
    const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#roomId')
      .val(validateVal);
    rtc.updateParams({ roomId: validateVal });
  });
$('#strRoomId')
  .on('input', (e) => {
    e.preventDefault();
    console.log(`strRoomId changed ${e.target.value}`);
    // const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#strRoomId')
      .val(e.target.value);
    rtc.updateParams({ strRoomId: e.target.value });
  });
$('#sub-num')
  .on('input', (e) => {
    e.preventDefault();
    const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#sub-num')
      .val(validateVal);
  });
$('#small')
  .on('change', () => {
    const v = $('#small')[0].checked;
    console.log('pub small', v);
    rtc.publishSmall(v);
  });
$('#ai-denoiser')
  .on('change', () => {
    const { checked } = $('#ai-denoiser')[0];
    if (checked && rtc.trtc.getAudioTrack()) {
      rtc.trtc.startPlugin('AIDenoiser', {
        assetsPath: './js',
        sdkAppId: rtc.sdkAppId_,
        userId: new URLSearchParams(location.search).get('userId') || rtc.userId_,
        userSig: new URLSearchParams(location.search).get('userSig') || rtc.userSig_,
      });
    } else {
      rtc.trtc.stopPlugin('AIDenoiser');
    }
  });
document.querySelector('#bgm').onchange = () => {
  if (document.querySelector('#bgm').checked && rtc.trtc.getAudioTrack()) {
    rtc.trtc.startPlugin('AudioMixer', {
      id: 'bgm',
      url: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/assets/music/bgm.mp3',
      loop: true
    });
  } else {
    rtc.trtc.stopPlugin('AudioMixer', { id: 'bgm' });
  }
};
$('#bgm')
  .on('change', () => {

  });
$('#sub_small')
  .on('click', (e) => {
    e.preventDefault();
    console.log('sub small');
    rtc.subscribeSmall(getRemoteUserId());
  });

$('#sub_big')
  .on('click', (e) => {
    e.preventDefault();
    console.log('sub small');
    rtc.subscribeBig(getRemoteUserId());
  });

$('#add-local-view')
  .on('click', (e) => {
    e.preventDefault();
    console.log('add-local-view');
    const d = addView(rtc.userId_, extraLocalViews.length + 1);
    extraLocalViews.push(d.id);
  });

$('#join')
  .on('click', (e) => {
    e.preventDefault();
    console.log('join');
    rtc.join();
  });

$('#start-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalVideo();
  });

$('#enable-spc').on('change', () => {
  if (rtc.trtc) {
    rtc.trtc._room.enableSPC = $('#enable-spc')[0].checked;
  }
});
$('#enable-sei').on('change', () => {
  if (rtc.trtc) {
    rtc.trtc._room.enableSEI = $('#enable-sei')[0].checked;
  }
});
$('#update-water-mark').on('click', () => {
  const imageUrls = [
    './mute.png',
    './tv2.png',
  ];
  const imageUrl = imageUrls[Math.floor(Math.random() * imageUrls.length)];
  const width = 100 + Math.floor((Math.random() - 0.5) * 100);
  rtc.trtc.updatePlugin('Watermark', {
    x: 100 + Math.floor(Math.random() * 100),
    y: 100 + Math.floor(Math.random() * 100),
    size: {
      width,
      height: width,
    },
    imageUrl
  });
});

$('#water-mark').on('change', () => {
  if ($('#water-mark')[0].checked) {
    // if (intervalId > 0 || !rtc.trtc.getVideoTrack()) {
    //   return;
    // }
    // startWaterMark({ x: 100, y: 10, imageUrl: './test.png' });
    rtc.trtc.startPlugin('Watermark', {
      x: 0,
      y: 0,
      imageUrl: './tv2.png',
    });
  } else {
    rtc.trtc.stopPlugin('Watermark');
  }
});

$('#install-vb').on('click', async () => {
  try {
    rtc.trtc.use(VirtualBackground);
    // rtc.trtc.use({ plugin: VirtualBackground });
    // rtc.trtc.use({ plugin: VirtualBackground, assetsPath: './js/assets/' });
    rtc.trtc.use(Watermark);
  } catch (e) {
    alert(e);
  }
});

let isStartedVirtualBackground = false;


$('input:radio[name="background"]').on('change', async function () {
  const v = $(this).val();
  async function setVirtualBackground(config) {
    try {
      if (!isStartedVirtualBackground) {
        await rtc.trtc.startPlugin('VirtualBackground', {
          ...config,
          sdkAppId: rtc.sdkAppId_,
          userId: rtc.userId_,
          userSig: rtc.userSig_,
        });
        isStartedVirtualBackground = true;
      } else {
        await rtc.trtc.updatePlugin('VirtualBackground', config);
      }
    } catch (e) {
      document.getElementById('normal-background').checked = true;
      alert(e);
    }
  }
  switch (v) {
    case 'blur':
      await setVirtualBackground({
        type: 'blur',
        blurLevel: parseInt(document.getElementById('vb-level-select').value, 10),
      });
      break;
    case 'normal':
      await rtc.trtc.stopPlugin('VirtualBackground');
      isStartedVirtualBackground = false;
      break;
    default:
      await setVirtualBackground({
        type: v,
        src: './vb.jpg'
      });
  }
});

$('#vb-level-select').on('change', function () {
  const v = parseInt($(this).val(), 10);
  console.log(v);
  if (document.getElementById('blur-background').checked) {
    rtc.trtc.updatePlugin('VirtualBackground', {
      type: 'blur',
      blurLevel: parseInt(document.getElementById('vb-level-select').value, 10),
    });
  }
});

$('#webcodecsDecode').on('change', function () {
  if (rtc.trtc?.room.isJoined && $(this)[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'webCodecs',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('#ffmpegDecode').on('change', function () {
  if (rtc.trtc?.room.isJoined && $(this)[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'wasm',
      renderer: $('#yuvMode')[0].checked ? 'webgl' : 'videoFrame',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('#yuvMode').on('change', () => {
  if (rtc.trtc?.room.isJoined && $('#ffmpegDecode')[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'wasm',
      renderer: $('#yuvMode')[0].checked ? 'webgl' : 'videoFrame',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('input:radio[name="mirror"]').on('change', function () {
  const v = $(this).val();
  let mirror = v;
  if (mirror === 'true') mirror = true;
  if (mirror === 'false') mirror = false;

  const mainContainer = document.querySelector('#main');
  const mainView = mainContainer.childNodes.length === 0 ? mainContainer : 'local_stream';
  const views = extraLocalViews.concat(mainView);
  const convertedViews = views.map((item) => {
    if (typeof item === 'string') {
      return document.getElementById(item);
    }
    return item;
  });
  rtc.trtc.updateLocalVideo({ view: convertedViews, option: { mirror } });
});

function getBeautyParams() {
  const beauty = document.getElementById('beauty-beauty').value;
  const brightness = document.getElementById('beauty-brightness').value;
  const ruddy = document.getElementById('beauty-ruddy').value;
  return {
    beauty: Number(beauty),
    brightness: Number(brightness),
    ruddy: Number(ruddy),
  };
}

$('#start-beauty').on('click', () => {
  rtc.trtc.startPlugin('BasicBeauty', {
    ...getBeautyParams()
  });
});

$('#update-beauty').on('click', () => {
  rtc.trtc.updatePlugin('BasicBeauty', {
    ...getBeautyParams()
  });
});

$('#stop-beauty').on('click', () => {
  rtc.trtc.stopPlugin('BasicBeauty');
});

$('#mirror').on('change', () => {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.mirror = ($('#mirror')[0].checked);
  }
});

$('#stop-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopLocalVideo();
  });
$('#stop-preview-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopPreviewLocalVideo();
  });
$('#start-local-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalAudio();
  });
$('#switch-microphone')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchMicrophone();
  });
$('#stop-local-audio')
  .on('click', (e) => {
    e.preventDefault();

    rtc.stopLocalAudio();
  });
$('#start-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    const div = addView('local_screen', TRTC.TYPE.STREAM_TYPE_SUB, true);
    rtc.trtc.startScreenShare({
      view: div,
      option: {
        systemAudio: $('#system-audio')[0].checked,
        // qosPreference: screenQos(),
        profile: { width: 1920, height: 1080, frameRate: 15, bitrate: 6000 },
        // captureElement: document.getElementById('form'),
        // preferDisplaySurface: 'tab'
      },
    }).catch((error) => {
      ToastError(error);
      if (error.handler) {
        setTimeout(error.handler, 1000);
      }
      throw error;
    });
  });
$('#stop-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.stopScreenShare();
  });
$('#start-remote-video')
  .on('click', (e) => {
    e.preventDefault();
    const remoteUserId = getRemoteUserId();
    if (remoteUserId === '*') {
      rtc.remotePublishedMainVideoUserIdSet.forEach((userId) => {
        const id = `${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_MAIN);
        rtc.startRemoteVideo(id, userId, TRTC.TYPE.STREAM_TYPE_MAIN);
      });
      rtc.remotePublishedSubVideoUserIdSet.forEach((userId) => {
        const id = `${userId}_${TRTC.TYPE.STREAM_TYPE_SUB}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_SUB);
        rtc.startRemoteVideo(id, userId, TRTC.TYPE.STREAM_TYPE_SUB);
      });
    } else {
      if (rtc.remotePublishedMainVideoUserIdSet.has(remoteUserId)) {
        const id = `${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`;
        addView(remoteUserId, TRTC.TYPE.STREAM_TYPE_MAIN);
        rtc.startRemoteVideo(id, remoteUserId, TRTC.TYPE.STREAM_TYPE_MAIN);
      }
      if (rtc.remotePublishedSubVideoUserIdSet.has(remoteUserId)) {
        const id = `${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_SUB}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_SUB);
        rtc.startRemoteVideo(id, remoteUserId, TRTC.TYPE.STREAM_TYPE_SUB);
      }
    }
  });
$('#stop-remote-video')
  .on('click', async (e) => {
    e.preventDefault();
    const remoteUserId = getRemoteUserId();
    if (remoteUserId === '*') {
      rtc.stopRemoteVideo({ userId: remoteUserId });
      rtc.remotePublishedMainVideoUserIdSet.forEach((userId) => {
        removeView(`${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
      });
      rtc.remotePublishedSubVideoUserIdSet.forEach((userId) => {
        removeView(`${userId}_${TRTC.TYPE.STREAM_TYPE_SUB}`);
      });
    } else {
      await rtc.stopRemoteVideo({ userId: remoteUserId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
      await rtc.stopRemoteVideo({ userId: remoteUserId, streamType: TRTC.TYPE.STREAM_TYPE_SUB });
      removeView(`${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
      removeView(`${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_SUB}`);
    }
  });
$('#mute-remote-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.muteRemoteAudio(getRemoteUserId(), true);
  });
$('#unmute-remote-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.muteRemoteAudio(getRemoteUserId(), false);
  });
$('#switch-camera')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchCamera();
  });
$('#update-video-profile')
  .on('click', (e) => {
    e.preventDefault();
    rtc.updateVideoProfile();
  });
$('#update-audio-profile')
  .on('click', (e) => {
    e.preventDefault();
    rtc.updateAudioProfile();
  });
$('#recordPCM')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.dumpAudio(10).then((download) => {
      $('#downloadPCM').removeClass('disabled');
      $('#downloadPCM')
        .one('click', (e) => {
          e.preventDefault();
          if (download) {
            download();
            $('#downloadPCM').addClass('disabled');
          }
        });
    });
  });
$('#gain0').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 0 }
  });
});
$('#gain50').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 50 }
  });
});
$('#gain100').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 100 }
  });
});
$('#gain300').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 300 }
  });
});
$('#gain500').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 500 }
  });
});

$('#beauty-ar').on('click', (e) => {
  rtc.trtc.startPlugin('Beauty', {
    sdkAppId: rtc.sdkAppId_,
    userId: rtc.userId_,
    userSig: rtc.userSig_,

    whiten: 0.5, // Whitening 0-1
    dermabrasion: 0.5, // Dermabrasion 0-1
    lift: 0.5, // Face Slimming 0-1
    shave: 0.5, // Jaw Shaving 0-1
    eye: 0.5, // Enlarged Eyes 0-1
    chin: 0.5, // Chin Adjustment 0-1

    effect: [{
      id: '7A62218064EF7959', // the effect id
      intensity: 0.7, // specify the strength of the effect
    },
    {
      id: 'D7C6418064B90F4C', // the effect id
      intensity: 0.7, // specify the strength of the effect
    }]
  });
});
$('#device-detector').html('开启检测组件');
$('#device-detector-network').html('开启检测组件(网络)');
$('#device-detector').on('click', async (e) => {
  const result = await rtc.trtc.startPlugin('DeviceDetector', {
    cameraDetect: {
      mirror: true
    }
  });
  console.log(result);
});
$('#device-detector-network').on('click', async (e) => {
  const sdkAppId = defaultSdkAppId;
  const userId = 'user_uplink_test';
  const userSig = await getUserSigFromServer(sdkAppId, 8080, userId);
  console.log('这是userSig', userSig);

  const downlinkUserId = 'user_downlink_test';
  const downlinkUserSig = await getUserSigFromServer(sdkAppId, 8080, downlinkUserId);
  const result = await rtc.trtc.startPlugin('DeviceDetector', { networkDetect: { sdkAppId, userId, userSig, downlinkUserId, downlinkUserSig } });
  console.log(result);
});

$('#switch-role-anchor').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-anchor');
  rtc.switchRole('anchor');
});

$('#switch-role-audience').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-audience');
  rtc.switchRole('audience');
});

$('#volume').on('input', (event) => {
  rtc.remotePublishedAudioUserIdSet.forEach((userId) => {
    rtc.trtc.setRemoteAudioVolume(userId, Number(event.target.value));
  });
});

$('#volume-local').on('input', (event) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: Number(event.target.value) }
  });
});

$('#volume-ear').on('input', (event) => {
  rtc.trtc.updateLocalAudio({
    option: { earMonitorVolume: Number(event.target.value) }
  });
});

$('#leave')
  .on('click', (e) => {
    e.preventDefault();
    console.log('leave');
    rtc.leave();
  });
$('#destroy')
  .on('click', (e) => {
    e.preventDefault();
    rtc.destroy();
  });

$('#mute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: true });
  });

$('#mute-video-image')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: './mute.png' });
  });

$('#unmute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: false });
  });
$('#mute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: true });
  });
$('#unmute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: false });
  });
$('#send-sei')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#sei').value);
    rtc.trtc.sendSEIMessage(buffer.buffer);
  });
$('#send-sei-aux')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#sei').value);
    rtc.trtc.sendSEIMessage(buffer.buffer, { toSubStream: true });
  });
$('#send-custom-msg')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#custom-msg').value);
    rtc.trtc.sendCustomMessage({
      cmdId: +document.getElementById('cmdId').value,
      data: buffer.buffer
    });
  });
$('#open-setting')
  .on('click', (e) => {
    e.preventDefault();
    const form = document.querySelector('#form');
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-setting');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭设置' : '打开设置';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#close-setting')
  .on('click', (e) => {
    e.preventDefault();
    const form = document.querySelector('#form');
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-setting');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭设置' : '打开设置';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#open-stat')
  .on('click', (e) => {
    e.preventDefault();
    const stat = document.querySelector('#debug-stat');
    stat.style.display = stat.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-stat');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭 Stat' : '打开 Stat';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#speaker')
  .on('change', (e) => {
    e.preventDefault();
    TRTC.setCurrentSpeaker(getSpeakerId());
  });

$('#settings')
  .on('click', (e) => {
    e.preventDefault();
    $('#settings')
      .toggleClass('btn-raised');
    $('#setting-collapse')
      .collapse();
  });

$('#video-frame')
  .on('click', (e) => {
    e.preventDefault();
    const frameURL = rtc.trtc.getVideoSnapshot({ userId: $('#video-frame-userId').val(), streamType: $('#video-frame-streamType').val() });
    if (frameURL) {
      const img = document.createElement('img');
      img.width = '640';
      img.height = '480';
      img.src = frameURL;
      document.body.appendChild(img);
    }
  });
$('#start-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const roomId = +$('#remote-roomId').val();
    const strRoomId = $('#remote-strRoomId').val();
    const userId = $('#cross-room-remote-userId').val() || undefined;
    rtc.trtc.startPlugin('CrossRoom', {
      roomId,
      strRoomId,
      userId,
    });
  });
$('#update-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const roomId = +$('#remote-roomId').val();
    const strRoomId = $('#remote-strRoomId').val();
    const userId = $('#cross-room-remote-userId').val() || undefined;
    rtc.trtc.updatePlugin('CrossRoom', {
      updateList: [{
        roomId,
        strRoomId,
        userId,
        muteAudio: $('#cross-room-muteAudio')[0].checked,
        muteVideo: $('#cross-room-muteVideo')[0].checked,
        muteSubStream: $('#cross-room-muteSubStream')[0].checked,
      }]
    });
  });
$('#stop-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const userId = $('#cross-room-remote-userId').val() || undefined;

    let option;
    if (!userId) {
      option = {
        roomId: +$('#remote-roomId').val(),
        strRoomId: $('#remote-strRoomId').val(),
      };
    }

    rtc.trtc.stopPlugin('CrossRoom', option);
  });

$('#start-voice-changer').on('click', (e) => {
  e.preventDefault();
  const voiceChangerType = document.querySelector('#voice-changer').value;
  rtc.trtc.startPlugin('VoiceChanger', {
    voiceType: Number(voiceChangerType),
    sdkAppId: rtc.sdkAppId_,
    userId: rtc.userId_,
    userSig: rtc.userSig_
  });
});
$('#update-voice-changer').on('click', (e) => {
  e.preventDefault();
  const voiceChangerType = document.querySelector('#voice-changer').value;
  rtc.trtc.updatePlugin('VoiceChanger', { voiceType: Number(voiceChangerType)  });
});

$('#stop-voice-changer').on('click', (e) => {
  e.preventDefault();
  rtc.trtc.stopPlugin('VoiceChanger');
});

TRTC.isSupported().then((result) => {
  const notSupported = Object.keys(result.detail).filter(key => !result.detail[key]);
  if (notSupported.length > 0) {
    const p = document.createElement('p');
    p.style.width = '300px';
    p.style.wordWrap = 'break-word';
    p.style.background = 'gray';

    p.innerText = `不支持如下能力：\n${notSupported.join('\n')}`;
    document.querySelector('#form').appendChild(p);
  }
});

async function updateDeviceList() {
  const microphoneList = await TRTC.getMicrophoneList();
  const cameraList = await TRTC.getCameraList();
  const speakerList = await TRTC.getSpeakerList();
  document.querySelector('#microphoneId').innerHTML = `
    ${microphoneList.filter(device => !device.label.includes('OPPO'))
    .map(device => `<option value="${device.deviceId}">${device.label}</option>`)}
  `;
  const selectedSpeakerIndex = speakerList.findIndex(item => item.deviceId === getSpeakerId());
  document.querySelector('#speaker').innerHTML = `
    ${speakerList
    .map(device => `<option value="${device.deviceId}">${device.label}</option>`)}
  `;
  if (selectedSpeakerIndex >= 0) {
    document.querySelector('#speaker').selectedIndex = selectedSpeakerIndex;
  }
  document.querySelector('#cameraId').innerHTML = `
    ${cameraList.filter(device => !device.label.includes('xxxxxxxxx'))
    .map(device => `<option value="${device.deviceId}" ${device.label.includes('FaceTime') ? 'selected' : ''}>${device.label}</option>`)}
  `;
}

updateDeviceList();

function getCameraId(cameraName) {
  const selector = document.getElementById('cameraId');
  let cameraId = selector[selector.selectedIndex].value;
  console.warn('getCameraId() cameraName', cameraName);
  if (cameraName) {
    const options = Array.from(selector.options);
    console.warn('getCameraId() cameraName', cameraName, 'options', options);
    const targetOption = options.find(option => option.text.toLowerCase().includes(cameraName.toLowerCase()));
    console.warn('targetOption', targetOption);
    if (targetOption) {
      console.warn(`auto find cameraId includes ${cameraName}: ${targetOption.value}`);
      cameraId = targetOption.value;
    }
  }
  console.log(`selected cameraId: ${cameraId}`);
  return cameraId;
}

function getRemoteUserId() {
  const selector = document.getElementById('remote-userId');
  const result = selector[selector.selectedIndex].value;
  return result;
}

function getMicrophoneId() {
  const selector = document.getElementById('microphoneId');
  const microphoneId = selector[selector.selectedIndex].value;
  console.log(`selected microphoneId: ${microphoneId}`);
  return microphoneId;
}
function getSpeakerId() {
  const selector = document.getElementById('speaker');
  if (!selector[selector.selectedIndex]) {
    return '';
  }
  const speakerId = selector[selector.selectedIndex].value;
  console.log(`selected speakerId: ${speakerId}`);
  return speakerId;
}

function getVideoProfile() {
  const selector = document.getElementById('video-profile');
  const profile = selector[selector.selectedIndex].value;
  console.log(`selected video profile: ${profile}`);
  if (profile === '480p30fps600kbps') {
    console.log('selected video profile: 480p30fps600kbps');
    return { width: 864, height: 480, frameRate: 30, bitrate: 600 };
  }
  if (profile === '480p30fps1500kbps') {
    console.log('selected video profile: 480p30fps1500kbps');
    return { width: 864, height: 480, frameRate: 30, bitrate: 1500 };
  }
  if (profile === '960p30fps1000kbps') {
    console.log('selected video profile: 960p30fps1000kbps');
    return { width: 1728, height: 960, frameRate: 30, bitrate: 1000 };
  }
  if (profile === '960p30fps2000kbps') {
    console.log('selected video profile: 960p30fps2000kbps');
    return { width: 1728, height: 960, frameRate: 30, bitrate: 2000 };
  }
  if (profile === '720p30') {
    console.log('selected video profile: 720p30');
    return { width: 1280, height: 720, frameRate: 30, bitrate: 1200 };
  }
  return profile;
}
function getAudioProfile() {
  const selector = document.getElementById('audio-profile');
  const profile = selector[selector.selectedIndex].value;
  console.log(`selected audio profile: ${profile}`);
  return profile;
}
function getProxyServer() {
  const selector = document.getElementById('proxy-server');
  const _proxy = selector[selector.selectedIndex].value;
  console.log(`selected proxy: ${_proxy}`);
  const proxy = {
    unifiedProxy: getUnifiedServer(),
  };
  if (document.getElementById('turn-server').value) {
    proxy.turnServer = {
      url: document.getElementById('turn-server').value,
      username: document.getElementById('turn-username').value,
      credential: document.getElementById('turn-credential').value,
      credentialType: 'password'
    };
    if (document.getElementById('force-turn').checked) {
      proxy.iceTransportPolicy = 'relay';
    }
  }
  if (_proxy.startsWith('ws')) {
    proxy.websocketProxy = _proxy;
  } else {
    proxy.webtransportProxy = _proxy;
  }
  return proxy;
}

function getUnifiedServer() {
  const input = $('#unified-proxy-input').val();
  const selector = document.getElementById('unified-proxy');
  const proxy = input || selector[selector.selectedIndex].value;
  console.log(`selected unified-proxy: ${proxy}`);
  return proxy;
}

function getRole() {
  const selector = document.getElementById('role');
  const role = selector[selector.selectedIndex].value;
  console.log(`selected role: ${role}`);
  return role;
}

function getLatencyLevel() {
  const selector = document.getElementById('latencyLevel');
  const latencyLevel = selector[selector.selectedIndex].value;
  console.log(`selected latencyLevel: ${latencyLevel}`);
  return Number(latencyLevel) || undefined;
}

function getScene() {
  const selector = document.getElementById('scene');
  const scene = selector[selector.selectedIndex].value;
  console.log(`selected scene: ${scene}`);
  return scene;
}

function isAutoReceiveAudio() {
  const selector = document.getElementById('auto-receive-audio');
  const result = selector[selector.selectedIndex].value;
  return Number(result) === 1;
}
function isAutoReceiveVideo() {
  const selector = document.getElementById('auto-receive-video');
  const result = selector[selector.selectedIndex].value;
  return Number(result) === 1;
}

function screenQos() {
  const selector = document.getElementById('screen-qos');
  const result = selector[selector.selectedIndex].value;
  return result;
}

function Toast(text) {
  Toastify({
    text,
    position: 'left',
    offset: {
      y: 40
    }
  }).showToast();
}

function ToastError(error = {}) {
  if (error && error.code === 5998) return;
  console.error(error);
  Toastify({ text: typeof error === 'string' ? error : `${error.functionName} failed, code: ${error.code} extraCode: ${error.extraCode} message: ${error.message}`, style: { background: 'red' } }).showToast();
}

// let sourceVideoTrack = null;
// let intervalId = -1;
// let video = null;

// 用于加载水印图片
// function loadImage(imageUrl) {
//   return new Promise((resolve) => {
//     const image = new Image();
//     // 开启跨域访问，避免出现加载非同源的图片资源时，开启水印生成的 videoTrack 是黑屏的问题。
//     image.crossOrigin = 'anonymous';
//     image.src = imageUrl;
//     image.onload = () => resolve(image);
//   });
// }

async function startWaterMark({ x, y, width, height, imageUrl }) {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.setWatermark({ x, y, width, height, imageUrl });
  }
  // if (intervalId > 0) return;
  // // 2. 创建 video 标签播放视频流
  // const video = document.createElement('video');
  // sourceVideoTrack = rtc.trtc.getVideoTrack();
  // const mediaStream = new MediaStream();
  // mediaStream.addTrack(sourceVideoTrack);
  // video.srcObject = mediaStream;
  // await video.play();

  // // 3. 加载水印图片
  // const image = await loadImage(imageUrl);

  // // 4. 创建 canvas 标签，并使用 setInterval 将视频和水印绘制到 canvas 画布中
  // const canvas = document.createElement('canvas');
  // const ctx = canvas.getContext('2d');
  // const settings = sourceVideoTrack.getSettings();
  // canvas.width = settings.width;
  // canvas.height = settings.height;

  // intervalId = setInterval(() => {
  //   // 将视频绘制到画布中
  //   ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  //   // 将水印图片绘制到画布中，可以控制水印的位置和大小
  //   ctx.drawImage(image, x, y, width || image.width, height || image.height);
  // }, Math.floor(1000 / settings.frameRate)); // 根据帧率计算每次绘制的时间间隔

  // // 5. 使用 canvas.captureStream 从画布中采集视频流，使用 updateLocalVideo 替换视频流
  // const canvasStream = canvas.captureStream();
  // await rtc.trtc.updateLocalVideo({ option: { videoTrack: canvasStream.getVideoTracks()[0] } });
}

// 关闭水印
async function stopWaterMark() {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.stopWatermark();
  }
  // if (intervalId > 0) {
  //   clearInterval(intervalId);
  //   intervalId = -1;
  //   await rtc.trtc.updateLocalVideo({ option: { videoTrack: sourceVideoTrack } });
  //   if (video) {
  //     video.srcObject = null;
  //     video = null;
  //   }
  // }
}

// test change
