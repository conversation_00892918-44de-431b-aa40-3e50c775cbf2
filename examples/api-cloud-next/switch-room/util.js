const DEFAULT_SDK_APP_ID = **********;
const USER_SIG_URL = 'https://service.trtc.qcloud.com/release/UserSigService';
// 获取 UserSig
async function getUserSig(userId, roomId) {
  const response = await fetch(USER_SIG_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      pwd: '********',
      appid: DEFAULT_SDK_APP_ID,
      roomnum: roomId,
      privMap: 255,
      identifier: userId,
      accounttype: 14418,
    }),
  });
  const json = await response.json();
  return json.data.userSig;
}
// 配置信息
const config = {
  sdkAppId: DEFAULT_SDK_APP_ID, // 替换为您的 sdkAppId
  rooms: [
    {
      roomId: 1001,
      anchors: [{ userId: 'anchor1' }],
    },
    {
      roomId: 1002,
      anchors: [{ userId: 'anchor2' }],
    },
    {
      roomId: 1003,
      anchors: [{ userId: 'anchor3' }, { userId: 'anchor4' }],
    },
  ],
  audience: {
    userId: 'audience1',
    userSig: null, // 将在初始化时设置
  },
};
