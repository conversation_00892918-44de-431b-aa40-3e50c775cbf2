<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>TRTC 主播端</title>
  <script src="./static/trtc.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      overflow: hidden;
      position: relative;
    }
    
    .container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: #333;
      color: white;
    }
    
    .header {
      padding: 20px;
      background-color: #222;
    }
    
    .title {
      font-size: 24px;
      margin-bottom: 10px;
    }
    
    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 20px;
    }
    
    button {
      padding: 12px 24px;
      border-radius: 4px;
      border: none;
      font-size: 18px;
      background-color: #007bff;
      color: white;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #0056b3;
    }
    
    button:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
    
    .preview-grid {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 10px;
      padding: 10px;
    }
    
    .preview-item {
      position: relative;
      background-color: #000;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .preview {
      width: 100%;
      height: 100%;
      background-color: #000;
    }
    
    .preview-label {
      position: absolute;
      top: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .status {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .log-container {
      position: absolute;
      bottom: 20px;
      left: 20px;
      right: 20px;
      max-height: 150px;
      overflow-y: auto;
      background-color: rgba(0, 0, 0, 0.7);
      color: #00ff00;
      font-family: monospace;
      padding: 10px;
      border-radius: 5px;
      z-index: 10;
    }
    
    .toggle-container {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    
    .toggle-btn {
      padding: 8px 16px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 class="title">TRTC 主播端</h1>
      <div class="controls">
        <button id="startAllBtn">一键开播所有直播间</button>
        <button id="stopAllBtn" disabled>停止所有直播</button>
        <div class="toggle-container">
          <button id="useRealCameraBtn" class="toggle-btn">使用真实摄像头</button>
          <button id="usePlaceholderBtn" class="toggle-btn">使用占位图片</button>
        </div>
      </div>
    </div>
    
    <div class="preview-grid">
      <div class="preview-item">
        <div id="preview-anchor1" class="preview"></div>
        <div class="preview-label">1001 - anchor1</div>
        <div class="status" id="status-anchor1">未开播</div>
      </div>
      <div class="preview-item">
        <div id="preview-anchor2" class="preview"></div>
        <div class="preview-label">1002 - anchor2</div>
        <div class="status" id="status-anchor2">未开播</div>
      </div>
      <div class="preview-item">
        <div id="preview-anchor3" class="preview"></div>
        <div class="preview-label">1003 - anchor3</div>
        <div class="status" id="status-anchor3">未开播</div>
      </div>
      <div class="preview-item">
        <div id="preview-anchor4" class="preview"></div>
        <div class="preview-label">1003 - anchor4</div>
        <div class="status" id="status-anchor4">未开播</div>
      </div>
    </div>
  </div>
  
  <div class="log-container" id="logContainer"></div>

</body>
<script src="./util.js"></script>
<script>
  // 占位图片 URL
  const placeholderImages = {
    'anchor1': './image/1.jpg',
    'anchor2': './image/2.jpg',
    'anchor3': './image/3.jpg',
    'anchor4': './image/4.jpg'
  };

  // 全局变量
  const trtcInstances = {};
  let isPublishing = false;
  let usePlaceholder = false;

  // DOM 元素
  const startAllBtn = document.getElementById('startAllBtn');
  const stopAllBtn = document.getElementById('stopAllBtn');
  const useRealCameraBtn = document.getElementById('useRealCameraBtn');
  const usePlaceholderBtn = document.getElementById('usePlaceholderBtn');

  // 初始化
  document.addEventListener('DOMContentLoaded', () => {
    setupEventListeners();
  });

  // 设置事件监听器
  function setupEventListeners() {
    // 开始所有直播按钮
    startAllBtn.addEventListener('click', startAllPublishing);

    // 停止所有直播按钮
    stopAllBtn.addEventListener('click', stopAllPublishing);

    // 使用真实摄像头按钮
    useRealCameraBtn.addEventListener('click', () => {
      usePlaceholder = false;
      updateVideoSource();
    });

    // 使用占位图片按钮
    usePlaceholderBtn.addEventListener('click', () => {
      usePlaceholder = true;
      updateVideoSource();
    });
  }

  // 更新视频源
  async function updateVideoSource() {
    if (!isPublishing) return;

    for (const userId in trtcInstances) {
      const trtc = trtcInstances[userId];
      if (!trtc) continue;

      try {
        if (usePlaceholder) {
          // 使用占位图片
          await trtc.updateLocalVideo({
            mute: placeholderImages[userId]
          });
          log(`[${userId}] 已切换到占位图片`);
        } else {
          // 使用真实摄像头
          await trtc.updateLocalVideo({
            mute: false
          });
          log(`[${userId}] 已切换到真实摄像头`);
        }
      } catch (error) {
        log(`[${userId}] 切换视频源失败: ${error.message}`);
      }
    }
  }

  // 开始所有推流
  async function startAllPublishing() {
    try {
      // 禁用开始按钮，防止重复点击
      startAllBtn.disabled = true;
      log('准备开始所有直播间的推流...');

      // 遍历所有房间和主播
      for (const room of config.rooms) {
        for (const anchor of room.anchors) {
          const userId = anchor.userId;
          const roomId = room.roomId;

          // 更新状态
          updateStatus(userId, '准备中...');

          try {
            // 获取 UserSig
            const userSig = await getUserSig(userId, roomId);

            // 创建 TRTC 实例
            const trtc = TRTC.create();
            trtcInstances[userId] = trtc;

            // 监听错误事件
            trtc.on(TRTC.EVENT.ERROR, error => {
              log(`[${userId}] 错误: ${error.message}, 代码: ${error.code}`);
            });

            // 进入房间
            await trtc.enterRoom({
              sdkAppId: config.sdkAppId,
              userId: userId,
              userSig: userSig,
              roomId: roomId,
              scene: TRTC.TYPE.SCENE_LIVE,
              role: TRTC.TYPE.ROLE_ANCHOR
            });

            log(`[${userId}] 成功进入房间 ${roomId}`);

            // 开启本地视频，根据设置决定是否使用占位图片
            await trtc.startLocalVideo({
              view: `preview-${userId}`,
              fillMode: 'contain',
              mute: usePlaceholder ? placeholderImages[userId] : false
            });

            // 开启本地音频
            await trtc.startLocalAudio();

            log(`[${userId}] 成功开启本地音视频`);
            updateStatus(userId, '直播中');
          } catch (error) {
            log(`[${userId}] 开播失败: ${error.message}`);
            updateStatus(userId, '开播失败');
          }
        }
      }

      // 更新状态
      isPublishing = true;

      // 启用停止按钮
      stopAllBtn.disabled = false;
    } catch (error) {
      log(`开播失败: ${error.message}`);
      startAllBtn.disabled = false;
    }
  }

  // 停止所有推流
  async function stopAllPublishing() {
    if (!isPublishing) return;

    try {
      stopAllBtn.disabled = true;
      log('准备停止所有直播间的推流...');

      // 遍历所有主播实例
      for (const userId in trtcInstances) {
        const trtc = trtcInstances[userId];
        if (!trtc) continue;

        try {
          // 停止本地视频
          await trtc.stopLocalVideo();

          // 停止本地音频
          await trtc.stopLocalAudio();

          // 退出房间
          await trtc.exitRoom();

          log(`[${userId}] 已停止直播`);
          updateStatus(userId, '未开播');
        } catch (error) {
          log(`[${userId}] 停止直播失败: ${error.message}`);
        }
      }

      // 清空实例
      for (const userId in trtcInstances) {
        trtcInstances[userId] = null;
      }

      // 更新状态
      isPublishing = false;

      // 启用开始按钮
      startAllBtn.disabled = false;
    } catch (error) {
      log(`停止直播失败: ${error.message}`);
      stopAllBtn.disabled = false;
    }
  }

  // 更新状态显示
  function updateStatus(userId, status) {
    const statusEl = document.getElementById(`status-${userId}`);
    if (statusEl) {
      statusEl.textContent = status;
    }
  }

  // 日志函数
  function log(message) {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;

    console.log(message);
  }
</script>
</html>
