<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>TRTC switchRoom API 测试</title>
  <script src="./static/trtc.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      height: 100%;
      width: 100%;
      overflow: hidden;
      position: relative;
      touch-action: none;
    }
    
    .live-container {
      position: absolute;
      width: 100%;
      height: 100%;
      /* transition: transform 0.3s ease-out; */
    }
    
    .live-room {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      background-color: #333;
    }
    
    .video-container {
      position: relative;
      width: 100%;
      height: 100%;
      background-color: #000;
    }
    
    .video-player {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    
    .dual-host-container {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: 100%;
    }
    
    .dual-host-video {
      width: 50%;
      height: 50%;
      object-fit: contain;
    }
    
    .room-info {
      position: absolute;
      top: 20px;
      left: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 10px;
      border-radius: 5px;
      z-index: 10;
    }
    
    .loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 20;
    }
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    
    .log-container {
      position: absolute;
      bottom: 20px;
      left: 20px;
      right: 20px;
      max-height: 150px;
      overflow-y: auto;
      background-color: rgba(0, 0, 0, 0.7);
      color: #00ff00;
      font-family: monospace;
      padding: 10px;
      border-radius: 5px;
      z-index: 10;
    }
    
    .mode-switch {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: rgba(0, 123, 255, 0.8);
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      font-size: 14px;
      z-index: 30;
      cursor: pointer;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
      transition: background-color 0.3s;
    }
    
    .mode-switch:hover {
      background-color: rgba(0, 86, 179, 0.9);
    }
    
    .mode-indicator {
      position: fixed;
      top: 70px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 30;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="live-container" id="liveContainer">
    <!-- 直播间1 -->
    <div class="live-room" id="room1">
      <div class="video-container">
        <div id="videoView1" class="video-player"></div>
        <div class="room-info">1001 (单主播)</div>
        <div class="loading" id="loading1">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </div>
    
    <!-- 直播间2 -->
    <div class="live-room" id="room2">
      <div class="video-container">
        <div id="videoView2" class="video-player"></div>
        <div class="room-info">1002 (单主播)</div>
        <div class="loading" id="loading2">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </div>
    
    <!-- 直播间3 -->
    <div class="live-room" id="room3">
      <div class="video-container">
        <div class="dual-host-container">
          <div id="videoView3Main" class="dual-host-video"></div>
          <div id="videoView3Sub" class="dual-host-video"></div>
        </div>
        <div class="room-info">1003 (双主播)</div>
        <div class="loading" id="loading3">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="log-container" id="logContainer"></div>
  
  <!-- 模式切换按钮 -->
  <div class="mode-indicator" id="modeIndicator">当前模式: switchRoom API</div>
  <div class="mode-switch" id="modeSwitch">切换模式</div>
</body>
<script src="./util.js"></script>
<script>
  // 全局变量
  let currentRoomIndex = 0;
  let startY = 0;
  let trtcInstance = null; // 单个 TRTC 实例
  let isTransitioning = false;
  let isFirstRoom = true; // 标记是否是第一次进入房间
  let useSwitchRoomAPI = true; // 默认使用 switchRoom API
  let roomSwitchStartTime = 0; // 记录房间切换开始时间
  let roomSwitchEndTime = 0; // 记录房间切换结束时间
  let pendingRoomIndex = -1; // 记录正在切换的房间索引

  // 初始化
  async function initApp() {
    try {
      // 获取 userSig
      config.audience.userSig = await getUserSig('audience1', 1001);

      setupRooms();
      setupTouchEvents();
      setupKeyboardEvents();
      setupModeSwitch();

      // 创建单个 TRTC 实例
      trtcInstance = TRTC.create();

      window.trtc = trtcInstance;

      // 设置事件监听
      setupEventListeners();

      // 进入第一个房间
      await enterFirstRoom();

      log(`TRTC SDK 已加载`);
    } catch (error) {
      log(`初始化失败: ${error.message}`);
    }
  }

  // 设置模式切换按钮
  function setupModeSwitch() {
    const modeSwitch = document.getElementById('modeSwitch');
    const modeIndicator = document.getElementById('modeIndicator');
    
    modeSwitch.addEventListener('click', () => {
      useSwitchRoomAPI = !useSwitchRoomAPI;
      modeIndicator.textContent = `当前模式: ${useSwitchRoomAPI ? 'switchRoom API' : 'exitRoom/enterRoom'}`;
      log(`已切换到 ${useSwitchRoomAPI ? 'switchRoom API' : 'exitRoom/enterRoom'} 模式`);
    });
  }

  // 设置事件监听器
  function setupEventListeners() {
    // 监听自动播放失败事件
    trtcInstance.on(TRTC.EVENT.AUTOPLAY_FAILED, event => {
      const { userId } = event;
      log(`直播间 ${currentRoomIndex + 1} 自动播放失败，用户: ${userId || '本地'}`);
      console.warn(`自动播放失败事件触发: 直播间 ${currentRoomIndex + 1}, 用户: ${userId || '本地'}`);
    });

    // 监听远端视频可用事件
    trtcInstance.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, async ({ userId, streamType }) => {
      try {
        if (currentRoomIndex === 2 && config.rooms[currentRoomIndex].anchors.length > 1) {
          // 直播间3（双主播）
          if (userId === config.rooms[currentRoomIndex].anchors[0].userId) {
            await trtcInstance.startRemoteVideo({
              userId,
              streamType,
              view: 'videoView3Main',
              fillMode: 'contain'
            });
          } else {
            await trtcInstance.startRemoteVideo({
              userId,
              streamType,
              view: 'videoView3Sub',
              fillMode: 'contain'
            });
          }
        } else {
          // 直播间1和2（单主播）
          await trtcInstance.startRemoteVideo({
            userId,
            streamType,
            view: `videoView${currentRoomIndex + 1}`,
            fillMode: 'contain'
          });
        }
      } catch (error) {
        log(`直播间 ${currentRoomIndex + 1} 播放远端视频失败: ${error.message}`);
      }
    });

    // 监听视频播放状态变化事件
    trtcInstance.on(TRTC.EVENT.VIDEO_PLAY_STATE_CHANGED, ({ userId, streamType, state, reason }) => {
      const loadingEl = document.getElementById(`loading${currentRoomIndex + 1}`);
      
      if (state === 'PLAYING' && reason === 'playing') {
        loadingEl.style.display = 'none';
        
        // 如果是正在切换的房间，计算视频渲染耗时
        if (pendingRoomIndex === currentRoomIndex && roomSwitchStartTime > 0) {
          const videoRenderTime = performance.now();
          const switchToRenderDuration = (videoRenderTime - roomSwitchStartTime).toFixed(2);
          
          log(`视频开始渲染，从滑动到渲染耗时: ${switchToRenderDuration}ms`);
          
          // 重置状态
          pendingRoomIndex = -1;
          roomSwitchStartTime = 0;
        }
      } else {
        loadingEl.style.display = 'flex';
      }
    });

    // 监听错误事件
    trtcInstance.on(TRTC.EVENT.ERROR, error => {
      log(`错误: ${error.message}, 代码: ${error.code}`);
    });
  }

  // 在 DOM 加载完成后初始化
  document.addEventListener('DOMContentLoaded', () => {
    initApp();
  });

  // 设置直播间位置
  function setupRooms() {
    const rooms = document.querySelectorAll('.live-room');
    rooms.forEach((room, index) => {
      room.style.top = `${index * 100}%`;
    });
  }

  // 设置触摸事件
  function setupTouchEvents() {
    const container = document.getElementById('liveContainer');
    const totalRooms = config.rooms.length;

    container.addEventListener('touchstart', (e) => {
      if (isTransitioning) return;
      startY = e.touches[0].clientY;
    });

    container.addEventListener('touchmove', (e) => {
      e.preventDefault();
    });

    container.addEventListener('touchend', async (e) => {
      if (isTransitioning) return;

      const endY = e.changedTouches[0].clientY;
      const diffY = endY - startY;

      if (Math.abs(diffY) > 50) {
        let newIndex;
        
        if (diffY > 0) {
          // 下滑 - 前一个房间或循环到最后一个
          newIndex = (currentRoomIndex - 1 + totalRooms) % totalRooms;
        } else {
          // 上滑 - 后一个房间或循环到第一个
          newIndex = (currentRoomIndex + 1) % totalRooms;
        }
        
        await switchRoom(newIndex);
      }
    });
  }

  // 切换直播间
  async function switchRoom(newIndex) {
    if (newIndex === currentRoomIndex) return;

    isTransitioning = true;
    pendingRoomIndex = newIndex; // 记录正在切换的房间索引

    // 更新位置
    const container = document.getElementById('liveContainer');
    container.style.transform = `translateY(${-newIndex * 100}%)`;

    // 显示加载动画
    const loadingEl = document.getElementById(`loading${newIndex + 1}`);
    loadingEl.style.display = 'flex';

    // 记录开始时间
    roomSwitchStartTime = performance.now();

    try {
      // 获取新房间信息
      const newRoomConfig = config.rooms[newIndex];

      if (useSwitchRoomAPI) {
        // 使用 switchRoom API 切换房间
        await trtcInstance.switchRoom({
          roomId: newRoomConfig.roomId,
          // autoSubscribeCount: 1 // 隐藏参数
        });
      } else {
        // 使用 exitRoom/enterRoom 方式切换房间
        await trtcInstance.exitRoom();
        
        await trtcInstance.enterRoom({
          sdkAppId: config.sdkAppId,
          userId: config.audience.userId,
          userSig: config.audience.userSig,
          roomId: newRoomConfig.roomId,
          scene: TRTC.TYPE.SCENE_LIVE,
          role: TRTC.TYPE.ROLE_AUDIENCE,
          autoReceiveVideo: true
        });

        window.enterRoomConfig = {
          sdkAppId: config.sdkAppId,
          userId: config.audience.userId,
          userSig: config.audience.userSig,
          scene: TRTC.TYPE.SCENE_LIVE,
          role: TRTC.TYPE.ROLE_AUDIENCE,
          autoReceiveVideo: true
        }
      }

      
    } catch (error) {
      log(`切换直播间失败: ${error.message}`);
      loadingEl.style.display = 'none';
      pendingRoomIndex = -1; // 重置状态
    } finally {
      isTransitioning = false;
      // 更新当前房间索引
      currentRoomIndex = newIndex;
    }

    // 延迟结束过渡状态
    setTimeout(() => {
      isTransitioning = false;
    }, 300);
  }

  // 进入第一个房间
  async function enterFirstRoom() {
    const roomIndex = 0;
    const roomConfig = config.rooms[roomIndex];
    const loadingEl = document.getElementById(`loading${roomIndex + 1}`);

    loadingEl.style.display = 'flex';

    try {
      // 从 URL 参数获取 enableAutoPlayDialog 设置
      const urlParams = new URLSearchParams(window.location.search);
      const enableAutoPlayDialog = urlParams.get('enableAutoPlayDialog') === 'true';

      // 进入房间
      await trtcInstance.enterRoom({
        sdkAppId: config.sdkAppId,
        userId: config.audience.userId,
        userSig: config.audience.userSig,
        roomId: roomConfig.roomId,
        scene: TRTC.TYPE.SCENE_LIVE,
        role: TRTC.TYPE.ROLE_AUDIENCE,
        autoReceiveVideo: true
      });

      // 更新当前房间索引
      currentRoomIndex = roomIndex;
      isFirstRoom = false;

      log(`成功进入直播间 ${roomIndex + 1}`);
    } catch (error) {
      log(`进入直播间 ${roomIndex + 1} 失败: ${error.message}`);
      loadingEl.style.display = 'none';
    }
  }

  // 日志函数
  function log(message) {
    const logContainer = document.getElementById('logContainer');
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;

    console.log(message);
  }

  // 设置键盘事件
  function setupKeyboardEvents() {
    document.addEventListener('keydown', async (e) => {
      if (isTransitioning) return;
      
      const totalRooms = config.rooms.length;
      let newIndex = currentRoomIndex;
      
      // 上方向键 - 向上滑动效果（下一个房间）
      if (e.key === 'ArrowUp') {
        newIndex = (currentRoomIndex + 1) % totalRooms;
        await switchRoom(newIndex);
      } 
      // 下方向键 - 向下滑动效果（上一个房间）
      else if (e.key === 'ArrowDown') {
        newIndex = (currentRoomIndex - 1 + totalRooms) % totalRooms;
        await switchRoom(newIndex);
      }
    });
  }
</script>
</html>
