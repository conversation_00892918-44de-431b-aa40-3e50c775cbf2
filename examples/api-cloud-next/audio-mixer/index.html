<!doctype html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- Material Design for Bootstrap fonts and icons -->
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Material+Icons"> -->

  <!-- Material Design for Bootstrap CSS -->
  <link rel="stylesheet" href="../assets/css/bootstrap-material-design.min.css">
  <link rel="stylesheet" href="./css/common.css">

  <title>TRTC Web SDK Samples - 基础音视频通话</title>
</head>

<body>
<nav class="navbar navbar-light fixed-top rtc-primary-bg">
  <h5>基础音视频通话</h5>
</nav>
<form id="form">
  <div class="container custom-container">
    <div class="row">
      <div class="custom-row-container">
        <div class="row">
          <div class="col-ms">
            <div class="card custom-card">
              <div class="form-group">
                <label for="userId" class="bmd-label-floating">userID:</label>
                <input type="text" class="form-control" name="userId" id="userId">
              </div>
              <div class="form-group bmd-form-group">
                <label for="roomId" class="bmd-label-floating">roomID:</label>
                <input type="text" class="form-control" name="roomId" id="roomId">
              </div>

              <div class="form-group bmd-form-group">
                <button id="join" type="button" class="btn btn-raised btn-primary rtc-primary-bg">进房</button>
                <button id="leave" type="button" class="btn btn-raised btn-primary rtc-primary-bg">退房</button>
                <br>
                <button id="start-local-video" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">打开摄像头</button>
                <button id="stop-local-video" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">关闭摄像头</button>
                <button id="switch-camera" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">切换摄像头</button>
                <button id="mute-video" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">muteVideo</button>
                <button id="unmute-video" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">unmuteVideo</button>
                <button id="update-video-profile" type="button" class="btn btn-raised btn-primary rtc-primary-bg">更新
                  video profile</button>
                <br>

                <button id="start-local-audio" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">开启麦克风</button>
                <button id="stop-local-audio" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">关闭麦克风</button>
                <button id="switch-microphone" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">切换麦克风</button>
                <button id="mute-audio" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">muteAudio</button>
                <button id="unmute-audio" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">unmuteAudio</button>
                <br>
                <div>
                  <label for="volume-local">调节本地音量</label>
                  <input type="range" id="volume-local" name="volume-local" min="0" max="100" value="100">
                </div>
                <button id="start-screen-share" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">开启屏幕分享</button>
                <button id="stop-screen-share" type="button"
                        class="btn btn-raised btn-primary rtc-primary-bg">关闭屏幕分享</button>
                <div>
                  <label for="volume">调节远端音量</label>
                  <input type="range" id="volume" name="volume" min="0" max="100" value="100">
                </div>
                <div>
                  <button id="start-audio-mixer-m" type="button" class="btn btn-raised btn-primary rtc-primary-bg">增加背景音乐 robot</button>
                  <button id="stop-audio-mixer-m" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭背景音乐 robot</button>
                  <button id="start-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">增加背景音乐 count</button>
                  <button id="stop-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭背景音乐 count</button>
                  <button id="start-wow" type="button" class="btn btn-raised btn-primary rtc-primary-bg">启动 wow</button>
                  <button id="start-wow2" type="button" class="btn btn-raised btn-primary rtc-primary-bg">启动 wow（循环）</button>
                  <button id="update-wow" type="button" class="btn btn-raised btn-primary rtc-primary-bg">更新 wow</button>
                  <button id="stop-wow" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭 wow</button>
                  <hr>
                  不停止插件，对音频进行操作：<br>
                  <button id="pause-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">pause</button>
                  <button id="resume-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">resume</button>
                  <button id="s-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">stop</button>
                  <button id="seek-audio-mixer" type="button" class="btn btn-raised btn-primary rtc-primary-bg">seek 5s</button>
                  <hr>
                  增加 track：<br>
                  <audio id="audioPlayer" src="../assets/music/count.mp3" controls loop></audio>
                  <button id="get-track" type="button" class="btn btn-raised btn-primary rtc-primary-bg"> 获取 track </button>
                  <button id="start-audio-track" type="button" class="btn btn-raised btn-primary rtc-primary-bg"> mix track start </button>
                  <button id="stop-audio-track" type="button" class="btn btn-raised btn-primary rtc-primary-bg"> mix track stop </button>
                  <hr>
                </div>
                <div>
                  <input id="enable-aec" type="checkbox" name="enable-aec">WebRTC AEC
                </div>
                <div>
                  <button id="start-ai-denoiser" type="button" class="btn btn-raised btn-primary rtc-primary-bg">开启降噪</button>
                  <button id="stop-ai-denoiser" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭降噪</button>
                </div>
                <div>
                  <button id="start-aec" type="button" class="btn btn-raised btn-primary rtc-primary-bg">开启自研AEC插件</button>
                  <button id="stop-aec" type="button" class="btn btn-raised btn-primary rtc-primary-bg">关闭自研AEC插件</button>
                </div>
                <div>
                  <button id="test-onended" type="button" class="btn btn-raised btn-primary rtc-primary-bg">测试 onEnded 事件</button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-ms">
            <div class="card">
              <button class="btn btn-raised rtc-expand-btn" id="settings" data-toggle="collapse"
                      data-target="#setting-collapse" aria-expanded="false" aria-controls="collapse">
                Settings
              </button>
              <div id="setting-collapse" class="collapse" aria-labelledby="setting-collapse">
                <div class="card-body">
                  <div class="form-group">
                    <label for="cameraId" class="bmd-label-floating">CAMERA</label>
                    <select class="form-control" id="cameraId" name="cameraId">
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="microphoneId" class="bmd-label-floating">MICROPHONE</label>
                    <select class="form-control" id="microphoneId" name="microphoneId">
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="video-profile" class="bmd-label-floating">video profile</label>
                    <select class="form-control" id="video-profile" name="video-profile">
                      <option value="240p">240p</option>
                      <option value="360p">360p</option>
                      <option value="480p">480p</option>
                      <option value="720p">720p</option>
                      <option value="1080p">1080p</option>
                      <option value="1440p" selected>1440p</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="scene" class="bmd-label-floating">scene</label>
                    <select class="form-control" id="scene" name="scene">
                      <option value="rtc" selected>rtc</option>
                      <option value="live">live</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="role" class="bmd-label-floating">role</label>
                    <select class="form-control" id="role" name="role">
                      <option value="anchor" selected>anchor</option>
                      <option value="audience">audience</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="receive-mode" class="bmd-label-floating">receive mode</label>
                    <select class="form-control" id="receive-mode" name="receive-mode">
                      <option value="0" selected>不自动拉流</option>
                      <option value="1">自动拉流</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="proxy-server" class="bmd-label-floating">proxy server</label>
                    <select class="form-control" id="proxy-server" name="proxy-server">
                      <option value="" selected>no proxy</option>
                      <option value="https://test11.rtc.qq.com:8080">test11_wt8080</option>
                      <option value="wss://test11.rtc.qq.com:8687">test11_ws8687</option>
                      <option value="wss://signaling.rtc.qcloud.com">signaling.rtc.qcloud.com</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
<div class="video-grid" id="video_grid">
  <div id="main"></div>
  <div id="side">
  </div>
</div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vConsole/3.9.5/vconsole.min.js"
        integrity="sha512-b0WdPl7IPDFmlPypxqFa3nIP4aeE+9KEG9Ataxg9r79c+y8qJFUEH1dBJDdqDYUvBhvuNhK9aIsXQUw4lwfbtA=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<!-- Optional JavaScript -->
<!-- jQuery first, then Popper.js, then Bootstrap JS -->
<!-- <script src="/assets/js/jquery-3.2.1.slim.min.js"></script> -->
<script>
  var ua = navigator.userAgent.toLowerCase();
  if (/mobile|android|iphone|ipad|phone/i.test(ua)) {
    var vConsole = new window.VConsole();
  }
</script>
<script src="../assets/js/jquery-3.2.1.min.js"></script>
<script src="../assets/js/popper.js"></script>
<script src="../assets/js/bootstrap-material-design.js"></script>
<script>$(document).ready(function () { $('body').bootstrapMaterialDesign(); });</script>
<script src="../dist/trtc.js"></script>
<script src="js/index.js"></script>
<timestamp>6/6/2024, 3:36:20 PM</timestamp>
</body>

</html>
