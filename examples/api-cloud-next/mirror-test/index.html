<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script src="./trtc.js"></script>
  <!-- <script src="../dist/trtc.js"></script> -->
  <!-- <script src="https://unpkg.com/trtc-sdk-v5@5.5.1/trtc.js"></script> -->
  <div id="userId"></div>
  <button onclick="enterRoom()"> 进房 </button> <br>
  <select name="mirror" id="mirror" onchange="handleChangeMirror()">
    <option value="true">true</option>
    <option value="false">false</option>
    <option value="view">view</option>
    <option value="publish">publish</option>
    <option value="both" selected>both</option>
  </select>
  <div id="player" style="width: 320px"></div>
  <div id="remote" style="width: 320px"></div>
  <canvas id="myCanvas" width="500" height="500"></canvas>
    <button onclick="transformCanvas()">Transform</button>

    <script>
        const canvas = document.getElementById('myCanvas');
        const ctx = canvas.getContext('2d');

        // Draw something on the canvas
        ctx.fillStyle = 'blue';
        ctx.fillRect(100, 100, 200, 200);
        ctx.fillStyle = 'red';
        ctx.beginPath();
        ctx.arc(250, 250, 100, 0, 2 * Math.PI);
        ctx.fill();

        function transformCanvas() {
            // Save the current context state
            ctx.save();

            // Apply transformations
            ctx.scale(-1, 1);
            ctx.translate(-canvas.width, 0);

            // Clear the canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Redraw the content
            ctx.fillStyle = 'blue';
            ctx.fillRect(100, 100, 200, 200);
            ctx.fillStyle = 'red';
            ctx.beginPath();
            ctx.arc(250, 250, 100, 0, 2 * Math.PI);
            ctx.fill();

            ctx.restore();
        }
    </script>
</body>

</html>

<script>
  const trtc = TRTC.create();
  trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
    trtc.startRemoteVideo({ userId, streamType, view: `remote` });
  });

  async function enterRoom() {
    try {
      const query = new URLSearchParams(location.search);
      // await trtc.enterRoom({
      //   sdkAppId: +query.get('sdkAppId'),
      //   userId: query.get('userId'),
      //   userSig: query.get('userSig'),
      //   roomId: +query.get('roomId'),
      // });
      // document.getElementById('userId').innerText = `${query.get('userId')} log in.`;
      // await trtc.startLocalAudio();
      await trtc.startLocalVideo({
        view: 'player',
        option: {
          useFrontCamera: true,
          mirror: 'both',
        },
      });
    } catch (error) {
      console.log(error);
    }
  }

  function handleChangeMirror() {;
    const mirror = document.getElementById('mirror').value;
    console.log('handleChangeMirror', mirror);
    if (mirror === 'true' || mirror === 'false') {
      trtc.updateLocalVideo({ option: { mirror: mirror === 'true' } });
      return;
    }
    trtc.updateLocalVideo({ option: { mirror } });
  }
</script>

<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script>
  // VConsole 会自动挂载到 `window.VConsole`
  var vConsole = new window.VConsole();
</script>