!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).TRTCVideoDecoder=t()}(this,(function(){"use strict";function e(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function t(e,t,r,n,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,o)}function r(e){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=e.apply(r,n);function s(e){t(a,o,i,s,c,"next",e)}function c(e){t(a,o,i,s,c,"throw",e)}s(void 0)}))}}function n(e,t,r){return t=u(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,l()?Reflect.construct(t,r||[],u(e).constructor):t.apply(e,r))}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=y(e))||t){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function c(e,t,r){return(t=v(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function f(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function d(){d=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var i=t&&t.prototype instanceof g?t:g,a=Object.create(i.prototype),s=new O(n||[]);return o(a,"_invoke",{value:A(e,r,s)}),a}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var h="suspendedStart",p="suspendedYield",v="executing",m="completed",y={};function g(){}function w(){}function b(){}var E={};u(E,a,(function(){return this}));var k=Object.getPrototypeOf,_=k&&k(k(F([])));_&&_!==r&&n.call(_,a)&&(E=_);var P=b.prototype=g.prototype=Object.create(E);function T(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function r(o,i,a,s){var c=l(e[o],e,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==typeof f&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return r("throw",e,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function A(t,r,n){var o=h;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var c=C(s,n);if(c){if(c===y)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=l(t,r,n);if("normal"===u.type){if(o=n.done?m:p,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function C(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,C(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=l(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function F(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return w.prototype=b,o(P,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:w,configurable:!0}),w.displayName=u(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,u(e,c,"GeneratorFunction")),e.prototype=Object.create(P),e},t.awrap=function(e){return{__await:e}},T(S.prototype),u(S.prototype,s,(function(){return this})),t.AsyncIterator=S,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},T(P),u(P,c,"Generator"),u(P,a,(function(){return this})),u(P,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=F,O.prototype={constructor:O,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return s.type="throw",s.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),x(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;x(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:F(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),y}},t}function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}function p(t){return function(t){if(Array.isArray(t))return e(t)}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof t?t:t+""}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function y(t,r){if(t){if("string"==typeof t)return e(t,r);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,r):void 0}}function g(e){var t="function"==typeof Map?new Map:void 0;return g=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(l())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&h(o,r.prototype),o}(e,arguments,u(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),h(r,e)},g(e)}var w,b,E=Object.create,k=Object.defineProperty,_=Object.defineProperties,P=Object.getOwnPropertyDescriptor,T=Object.getOwnPropertyDescriptors,S=Object.getOwnPropertyNames,A=Object.getOwnPropertySymbols,C=Object.getPrototypeOf,D=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,O=function(e,t,r){return t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},F=function(e,t){for(var r in t||(t={}))D.call(t,r)&&O(e,r,t[r]);if(A){var n,o=s(A(t));try{for(o.s();!(n=o.n()).done;){r=n.value;x.call(t,r)&&O(e,r,t[r])}}catch(e){o.e(e)}finally{o.f()}}return e},j=function(e,t){return _(e,T(t))},M=function(e,t,r,n){for(var o,i=P(t,r),a=e.length-1;a>=0;a--)(o=e[a])&&(i=o(t,r,i)||i);return i&&k(t,r,i),i},$=function(e,t,r){return O(e,"symbol"!==m(t)?t+"":t,r)},R=(w={"../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js":function(e,t){var r=Object.prototype.hasOwnProperty,n="~";function o(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||e,a),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new o:delete e._events[t]}function c(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),c.prototype.eventNames=function(){var e,t,o=[];if(0===this._eventsCount)return o;for(t in e=this._events)r.call(e,t)&&o.push(n?t.slice(1):t);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},c.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=new Array(i);o<i;o++)a[o]=r[o].fn;return a},c.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},c.prototype.emit=function(e,t,r,o,i,a){var s=n?n+e:e;if(!this._events[s])return!1;var c,u,f=this._events[s],l=arguments.length;if(f.fn){switch(f.once&&this.removeListener(e,f.fn,void 0,!0),l){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,t),!0;case 3:return f.fn.call(f.context,t,r),!0;case 4:return f.fn.call(f.context,t,r,o),!0;case 5:return f.fn.call(f.context,t,r,o,i),!0;case 6:return f.fn.call(f.context,t,r,o,i,a),!0}for(u=1,c=new Array(l-1);u<l;u++)c[u-1]=arguments[u];f.fn.apply(f.context,c)}else{var d,h=f.length;for(u=0;u<h;u++)switch(f[u].once&&this.removeListener(e,f[u].fn,void 0,!0),l){case 1:f[u].fn.call(f[u].context);break;case 2:f[u].fn.call(f[u].context,t);break;case 3:f[u].fn.call(f[u].context,t,r);break;case 4:f[u].fn.call(f[u].context,t,r,o);break;default:if(!c)for(d=1,c=new Array(l-1);d<l;d++)c[d-1]=arguments[d];f[u].fn.apply(f[u].context,c)}}return!0},c.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,r,o){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return s(this,i),this;var a=this._events[i];if(a.fn)a.fn!==t||o&&!a.once||r&&a.context!==r||s(this,i);else{for(var c=0,u=[],f=a.length;c<f;c++)(a[c].fn!==t||o&&!a[c].once||r&&a[c].context!==r)&&u.push(a[c]);u.length?this._events[i]=1===u.length?u[0]:u:s(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&s(this,t)):(this._events=new o,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=n,c.EventEmitter=c,void 0!==t&&(t.exports=c)}},function(){return b||(0,w[S(w)[0]])((b={exports:{}}).exports,b),b.exports}),L=function(e,t,r){return r=null!=e?E(C(e)):{},function(e,t,r,n){if(t&&"object"===m(t)||"function"==typeof t){var o,i=s(S(t));try{var a=function(){var i=o.value;D.call(e,i)||i===r||k(e,i,{get:function(){return t[i]},enumerable:!(n=P(t,i))||n.enumerable})};for(i.s();!(o=i.n()).done;)a()}catch(e){i.e(e)}finally{i.f()}}return e}(k(r,"default",{value:e,enumerable:!0}),e)}(R()),N=Symbol("instance"),U=Symbol("cacheResult"),z=function(){return a((function e(t,r,n){o(this,e),this.oldState=t,this.newState=r,this.action=n,this.aborted=!1}),[{key:"abort",value:function(e){this.aborted=!0,q.call(e,this.oldState,new Error("action '".concat(this.action,"' aborted")))}},{key:"toString",value:function(){return"".concat(this.action,"ing")}}])}(),I=function(e){function t(e,r,i){var a;return o(this,t),(a=n(this,t,[r])).state=e,a.message=r,a.cause=i,a}return f(t,e),a(t)}(g(Error));var B=new Map;function W(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(n,o,i){var a=r.action||o;if(!r.context){var s=B.get(n)||[];B.has(n)||B.set(n,s),s.push({from:e,to:t,action:a})}var c=i.value;i.value=function(){for(var n,o=this,i=this,s=arguments.length,u=new Array(s),f=0;f<s;f++)u[f]=arguments[f];r.context&&(i=X.get("function"==typeof r.context?(n=r.context).call.apply(n,[this].concat(u)):r.context));if(i.state===t)return r.sync?i[U]:Promise.resolve(i[U]);i.state instanceof z&&i.state.action==r.abortAction&&i.state.abort(i);var l=null;Array.isArray(e)?0==e.length?i.state instanceof z&&i.state.abort(i):"string"==typeof i.state&&e.includes(i.state)||(l=new I(i._state,"".concat(i.name," ").concat(a," to ").concat(t," failed: current state ").concat(i._state," not from ").concat(e.join("|")))):e!==i.state&&(l=new I(i._state,"".concat(i.name," ").concat(a," to ").concat(t," failed: current state ").concat(i._state," not from ").concat(e)));var d=function(e){if(r.fail&&r.fail.call(o,e),r.sync){if(r.ignoreError)return e;throw e}return r.ignoreError?Promise.resolve(e):Promise.reject(e)};if(l)return d(l);var h=i.state,p=new z(h,t,a);q.call(i,p);var v,y=function(e){var n;return i[U]=e,p.aborted||(q.call(i,t),null===(n=r.success)||void 0===n||n.call(o,i[U])),e},g=function(e){return q.call(i,h,e),d(e)};try{var w=c.apply(this,u);return"object"===m(v=w)&&v&&"then"in v?w.then(y).catch(g):r.sync?y(w):Promise.resolve(y(w))}catch(r){return g(new I(i._state,"".concat(i.name," ").concat(a," from ").concat(e," to ").concat(t," failed: ").concat(r),r instanceof Error?r:new Error(String(r))))}}}}var H,V,G=(H="undefined"!=typeof window&&window.__AFSM__,V="undefined"!=typeof importScripts,H?function(e,t){window.dispatchEvent(new CustomEvent(e,{detail:t}))}:V?function(e,t){postMessage({type:e,payload:t})}:function(){});function q(e,t){var r=this._state;this._state=e;var n=e.toString();e&&this.emit(n,r),this.emit(X.STATECHANGED,e,r,t),this.updateDevTools({value:e,old:r,err:t instanceof Error?t.message:String(t)})}var X=function(e){function t(e,r,i){var a;o(this,t),(a=n(this,t)).name=e,a.groupName=r,a._state=t.INIT,e||(e=Date.now().toString(36)),i?Object.setPrototypeOf(a,i):i=Object.getPrototypeOf(a),r||(a.groupName=a.constructor.name);var s=i[N];return s?a.name=s.name+"-"+s.count++:i[N]={name:a.name,count:0},a.updateDevTools({diagram:a.stateDiagram}),a}return f(t,e),a(t,[{key:"stateDiagram",get:function(){var e=Object.getPrototypeOf(this),t=B.get(e)||[],r=new Set,n=[],o=[],i=new Set,a=Object.getPrototypeOf(e);B.has(a)&&(a.stateDiagram.forEach((function(e){return r.add(e)})),a.allStates.forEach((function(e){return i.add(e)}))),t.forEach((function(e){var t=e.from,r=e.to,i=e.action;"string"==typeof t?n.push({from:t,to:r,action:i}):t.length?t.forEach((function(e){n.push({from:e,to:r,action:i})})):o.push({to:r,action:i})})),n.forEach((function(e){var t=e.from,n=e.to,o=e.action;i.add(t),i.add(n),i.add(o+"ing"),r.add("".concat(t," --\x3e ").concat(o,"ing : ").concat(o)),r.add("".concat(o,"ing --\x3e ").concat(n," : ").concat(o," 🟢")),r.add("".concat(o,"ing --\x3e ").concat(t," : ").concat(o," 🔴"))})),o.forEach((function(e){var t=e.to,n=e.action;r.add("".concat(n,"ing --\x3e ").concat(t," : ").concat(n," 🟢")),i.forEach((function(e){e!==t&&r.add("".concat(e," --\x3e ").concat(n,"ing : ").concat(n))}))}));var s=p(r);return Object.defineProperties(e,{stateDiagram:{value:s},allStates:{value:i}}),s}},{key:"updateDevTools",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};G(t.UPDATEAFSM,Object.assign({name:this.name,group:this.groupName},e))}},{key:"state",get:function(){return this._state},set:function(e){q.call(this,e)}}],[{key:"get",value:function(e){var r;return"string"==typeof e?(r=t.instances.get(e))||t.instances.set(e,r=new t(e,void 0,Object.create(t.prototype))):(r=t.instances2.get(e))||t.instances2.set(e,r=new t(e.constructor.name,void 0,Object.create(t.prototype))),r}},{key:"getState",value:function(e){var r;return null===(r=t.get(e))||void 0===r?void 0:r.state}}])}(L.default);X.STATECHANGED="stateChanged",X.UPDATEAFSM="updateAFSM",X.INIT="[*]",X.ON="on",X.OFF="off",X.instances=new Map,X.instances2=new WeakMap;var K=function(e){function t(){var e;return o(this,t),e=n(this,t,arguments),$(e,"decoder"),$(e,"config"),e}return f(t,e),a(t,[{key:"initialize",value:(i=r(d().mark((function e(){var t=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.decoder=new VideoDecoder({output:function(e){t.emit("videoFrame",e)},error:function(e){t.close(),t.emit("error",e)}});case 1:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"configure",value:function(e){this.config=e,this.decoder.configure(j(F({},e),{codec:this.getCodec(e)}))}},{key:"getCodec",value:function(e){switch(e.codec){case"hevc":return"hvc1.1.6.L0.12.34.56.78.9A.BC";case"av1":return"av01.0.05M.08";case"avc":return"avc1.420028";default:return e.codec}}},{key:"decode",value:function(e){"configured"===this.decoder.state&&this.decoder.decode(new EncodedVideoChunk(e))}},{key:"flush",value:function(){this.decoder.flush()}},{key:"reset",value:function(){this.decoder.reset()}},{key:"close",value:function(){"closed"!==this.decoder.state&&this.decoder.close()}}]);var i}(X);function J(){var e;self.onmessage=function(t){if("init"===t.data.type){var r=t.data,n=r.canvas,o=r.wasmScript,i=r.wasmBinary,a=null==n?void 0:n.getContext("2d"),s=0,c=0,u={wasmBinary:i,postRun:function(){e=new u.VideoDecoder({videoInfo:function(e,t){s=e,c=t,console.log("video info",e,t)},yuvData:function(e,t){var r=s*c,o=r>>2,i=u.HEAPU32[e>>2],f=u.HEAPU32[1+(e>>2)],l=u.HEAPU32[2+(e>>2)],d=u.HEAPU8.subarray(i,i+r),h=u.HEAPU8.subarray(f,f+o),p=u.HEAPU8.subarray(l,l+o),v=new Uint8Array(r+o+o);v.set(d),v.set(h,r),v.set(p,r+o);var m=new VideoFrame(v,{codedWidth:s,codedHeight:c,format:"I420",timestamp:t});n?(null==a||a.drawImage(m,0,0,n.width,n.height),null==a||a.commit()):self.postMessage({type:"yuvData",videoFrame:m},[m])}}),self.postMessage({type:"ready"})}};Function("var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;return "+o)()(u)}else if("decode"===t.data.type){var f=t.data.packet;null==e||e.decode(f.data,"key"==f.type,f.timestamp)}else if("setCodec"===t.data.type){var l=t.data,d=l.codec,h=l.format,p=l.description;null==e||e.setCodec(d,h,null!=p?p:"")}}}M([W([X.INIT,"closed"],"initialized")],K.prototype,"initialize"),M([W("initialized","configured",{sync:!0})],K.prototype,"configure"),M([function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e,r,n){var o=n.value,i=r;n.value=function(){if(!t.includes(this.state.toString()))throw new I(this.state,"".concat(this.name," ").concat(i," failed: current state ").concat(this.state," not in ").concat(t));for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return o.apply(this,r)}}}("configured")],K.prototype,"decode"),M([W([],X.INIT,{sync:!0})],K.prototype,"reset"),M([W([],"closed",{ignoreError:!0,sync:!0})],K.prototype,"close");var Y=function(e){function t(e,r){var i,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],s=arguments.length>3?arguments[3]:void 0,c=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return o(this,t),(i=n(this,t)).createModule=e,i.wasmBinary=r,i.workerMode=a,i.canvas=s,i.yuvMode=c,$(i,"worker"),$(i,"decoder"),$(i,"config"),$(i,"module",{}),$(i,"width",0),$(i,"height",0),i}return f(t,e),a(t,[{key:"initialize",value:(i=r(d().mark((function e(t){var r,n,o,i,a,s=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.workerMode){e.next=10;break}return n=new RegExp("\\{(.+)\\}","s").exec(J.toString())[1],this.worker=new Worker(URL.createObjectURL(new Blob([n],{type:"text/javascript"}))),o=null==(r=this.canvas)?void 0:r.transferControlToOffscreen(),e.next=6,this.wasmBinary;case 6:return i=e.sent,console.warn("worker mode",i),this.worker.postMessage({type:"init",canvas:o,wasmScript:this.createModule.toString(),wasmBinary:i},o?[o,i]:[i]),e.abrupt("return",new Promise((function(e){s.worker.onmessage=function(t){if("ready"===t.data.type)delete s.wasmBinary,e(),console.warn("worker mode initialize success");else if("yuvData"===t.data.type){var r=t.data.videoFrame;s.emit("videoFrame",r)}}})));case 10:if(a=this.module,!this.wasmBinary){e.next=15;break}return e.next=14,this.wasmBinary;case 14:a.wasmBinary=e.sent;case 15:return a.print=function(e){return console.log(e)},a.printErr=function(e){return console.log("[JS] ERROR: ".concat(e))},a.onAbort=function(){return console.log("[JS] FATAL: WASM ABORTED")},e.abrupt("return",new Promise((function(e){a.postRun=function(t){s.decoder=new s.module.VideoDecoder(s),console.log("video soft decoder initialize success"),e()},t&&Object.assign(a,t),s.createModule(a)})));case 19:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"configure",value:function(e){var t,r,n;this.config=e;var o=this.config.codec.startsWith("avc")?"avc":"hevc",i=this.config.description?"avc"==o?"avcc":"hvcc":"annexb";null==(r=this.decoder)||r.setCodec(o,i,null!=(t=this.config.description)?t:""),null==(n=this.worker)||n.postMessage({type:"setCodec",codec:o,format:i,description:this.config.description})}},{key:"decode",value:function(e){var t,r;null==(t=this.decoder)||t.decode(e.data,"key"==e.type,e.timestamp),"configured"===this.state&&(null==(r=this.worker)||r.postMessage({type:"decode",packet:e}))}},{key:"flush",value:function(){}},{key:"reset",value:function(){this.config=void 0,this.decoder&&this.decoder.clear()}},{key:"close",value:function(){this.removeAllListeners(),this.decoder&&(this.decoder.clear(),this.decoder.delete())}},{key:"videoInfo",value:function(e,t){this.width=e,this.height=t;var r={width:e,height:t};this.emit("videoCodecInfo",r)}},{key:"yuvData",value:function(e,t){if(this.module){var r=this.width*this.height,n=r>>2,o=this.module.HEAPU32[e>>2],i=this.module.HEAPU32[1+(e>>2)],a=this.module.HEAPU32[2+(e>>2)],s=this.module.HEAPU8.subarray(o,o+r),c=this.module.HEAPU8.subarray(i,i+n),u=this.module.HEAPU8.subarray(a,a+n);if(this.yuvMode)this.emit("videoFrame",{y:s,u:c,v:u,timestamp:t});else{var f=new Uint8Array(r+n+n);f.set(s),f.set(c,r),f.set(u,r+n),this.emit("videoFrame",new VideoFrame(f,{codedWidth:this.width,codedHeight:this.height,format:"I420",timestamp:t}))}}}},{key:"errorInfo",value:function(e){var t={errMsg:e};this.emit("error",t)}}]);var i}(X);M([W([X.INIT,"closed"],"initialized")],Y.prototype,"initialize"),M([W("initialized","configured",{sync:!0})],Y.prototype,"configure"),M([W([],X.INIT,{sync:!0})],Y.prototype,"reset"),M([W([],"closed",{sync:!0})],Y.prototype,"close");var Z,Q=(Z="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(){var e,t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=r;i.ready=new Promise((function(r,n){e=r,t=n}));var s=Object.assign({},i),u="./this.program",l="object"==("undefined"==typeof window?"undefined":m(window)),d="function"==typeof importScripts;"object"==("undefined"==typeof process?"undefined":m(process))&&"object"==m(process.versions)&&process.versions.node;var h,p,v,y="";(l||d)&&(d?y=self.location.href:"undefined"!=typeof document&&document.currentScript&&(y=document.currentScript.src),Z&&(y=Z),y=0!==y.indexOf("blob:")?y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1):"",h=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(v=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),p=function(e,t,r){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)});var w,b,E,k=i.print||console.log.bind(console),_=i.printErr||console.error.bind(console);Object.assign(i,s),s=null,i.arguments&&i.arguments,i.thisProgram&&(u=i.thisProgram),i.quit&&i.quit,i.wasmBinary&&(w=i.wasmBinary),i.noExitRuntime,"object"!=("undefined"==typeof WebAssembly?"undefined":m(WebAssembly))&&q("no native wasm support detected");var P,T,S,A,C,D,x,O,F,j=!1,M=[],$=[],R=[];function L(e){M.unshift(e)}function N(e){R.unshift(e)}var U,z,I,B,W=0,H=null;function V(e){W++,i.monitorRunDependencies&&i.monitorRunDependencies(W)}function G(e){if(W--,i.monitorRunDependencies&&i.monitorRunDependencies(W),0==W&&H){var t=H;H=null,t()}}function q(e){i.onAbort&&i.onAbort(e),_(e="Aborted("+e+")"),j=!0,e+=". Build with -sASSERTIONS for more info.";var r=new WebAssembly.RuntimeError(e);throw t(r),r}function X(e){return e.startsWith("data:application/octet-stream;base64,")}function K(e){if(e==U&&w)return new Uint8Array(w);if(v)return v(e);throw"both async and sync fetching of the wasm failed"}function J(e,t,r){return function(e){return w||!l&&!d||"function"!=typeof fetch?Promise.resolve().then((function(){return K(e)})):fetch(e,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+e+"'";return t.arrayBuffer()})).catch((function(){return K(e)}))}(e).then((function(e){return WebAssembly.instantiate(e,t)})).then((function(e){return e})).then(r,(function(e){_("failed to asynchronously prepare wasm: "+e),q(e)}))}X(U="videodec.wasm")||(z=U,U=i.locateFile?i.locateFile(z,y):y+z);var Y=function(e){for(;e.length>0;)e.shift()(i)};function Q(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){D[this.ptr+4>>2]=e},this.get_type=function(){return D[this.ptr+4>>2]},this.set_destructor=function(e){D[this.ptr+8>>2]=e},this.get_destructor=function(){return D[this.ptr+8>>2]},this.set_caught=function(e){e=e?1:0,P[this.ptr+12|0]=e},this.get_caught=function(){return 0!=P[this.ptr+12|0]},this.set_rethrown=function(e){e=e?1:0,P[this.ptr+13|0]=e},this.get_rethrown=function(){return 0!=P[this.ptr+13|0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t)},this.set_adjusted_ptr=function(e){D[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return D[this.ptr+16>>2]},this.get_exception_ptr=function(){if(Qt(this.get_type()))return D[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}var ee={isAbs:function(e){return"/"===e.charAt(0)},splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,t){for(var r=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:function(e){var t=ee.isAbs(e),r="/"===e.substr(-1);return(e=ee.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||t||(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname:function(e){var t=ee.splitPath(e),r=t[0],n=t[1];return r||n?(n&&(n=n.substr(0,n.length-1)),r+n):"."},basename:function(e){if("/"===e)return"/";var t=(e=(e=ee.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments);return ee.normalize(e.join("/"))},join2:function(e,t){return ee.normalize(e+"/"+t)}},te=function(e){return(te=function(){if("object"==("undefined"==typeof crypto?"undefined":m(crypto))&&"function"==typeof crypto.getRandomValues)return function(e){return crypto.getRandomValues(e)};q("initRandomDevice")}())(e)},re={resolve:function(){for(var e="",t=!1,r=arguments.length-1;r>=-1&&!t;r--){var n=r>=0?arguments[r]:ve.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t=ee.isAbs(n)}return(t?"/":"")+(e=ee.normalizeArray(e.split("/").filter((function(e){return!!e})),!t).join("/"))||"."},relative:function(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;r>=0&&""===e[r];r--);return t>r?[]:e.slice(t,r-t+1)}e=re.resolve(e).substr(1),t=re.resolve(t).substr(1);for(var n=r(e.split("/")),o=r(t.split("/")),i=Math.min(n.length,o.length),a=i,s=0;s<i;s++)if(n[s]!==o[s]){a=s;break}var c=[];for(s=a;s<n.length;s++)c.push("..");return(c=c.concat(o.slice(a))).join("/")}},ne="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,oe=function(e,t,r){for(var n=t+r,o=t;e[o]&&!(o>=n);)++o;if(o-t>16&&e.buffer&&ne)return ne.decode(e.subarray(t,o));for(var i="";t<o;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var c=63&e[t++];if((a=224==(240&a)?(15&a)<<12|s<<6|c:(7&a)<<18|s<<12|c<<6|63&e[t++])<65536)i+=String.fromCharCode(a);else{var u=a-65536;i+=String.fromCharCode(55296|u>>10,56320|1023&u)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i},ie=[],ae=function(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:n>=55296&&n<=57343?(t+=4,++r):t+=3}return t},se=function(e,t,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(r>=i)break;t[r++]=s}else if(s<=2047){if(r+1>=i)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=i)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}return t[r]=0,r-o};function ce(e,t,r){var n=ae(e)+1,o=new Array(n),i=se(e,o,0,o.length);return o.length=i,o}var ue={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){ue.ttys[e]={input:[],output:[],ops:t},ve.registerDevice(e,ue.stream_ops)},stream_ops:{open:function(e){var t=ue.ttys[e.node.rdev];if(!t)throw new ve.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.fsync(e.tty)},fsync:function(e){e.tty.ops.fsync(e.tty)},read:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.get_char)throw new ve.ErrnoError(60);for(var i=0,a=0;a<n;a++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new ve.ErrnoError(29)}if(void 0===s&&0===i)throw new ve.ErrnoError(6);if(null==s)break;i++,t[r+a]=s}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.put_char)throw new ve.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,t[r+i])}catch(e){throw new ve.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){return function(){if(!ie.length){var e=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(e=window.prompt("Input: "))&&(e+="\n"):"function"==typeof readline&&null!==(e=readline())&&(e+="\n"),!e)return null;ie=ce(e)}return ie.shift()}()},put_char:function(e,t){null===t||10===t?(k(oe(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(k(oe(e.output,0)),e.output=[])},ioctl_tcgets:function(e){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets:function(e,t,r){return 0},ioctl_tiocgwinsz:function(e){return[24,80]}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(_(oe(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},fsync:function(e){e.output&&e.output.length>0&&(_(oe(e.output,0)),e.output=[])}}},fe=function(e){q()},le={ops_table:null,mount:function(e){return le.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(ve.isBlkdev(r)||ve.isFIFO(r))throw new ve.ErrnoError(63);le.ops_table||(le.ops_table={dir:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,lookup:le.node_ops.lookup,mknod:le.node_ops.mknod,rename:le.node_ops.rename,unlink:le.node_ops.unlink,rmdir:le.node_ops.rmdir,readdir:le.node_ops.readdir,symlink:le.node_ops.symlink},stream:{llseek:le.stream_ops.llseek}},file:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:{llseek:le.stream_ops.llseek,read:le.stream_ops.read,write:le.stream_ops.write,allocate:le.stream_ops.allocate,mmap:le.stream_ops.mmap,msync:le.stream_ops.msync}},link:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr,readlink:le.node_ops.readlink},stream:{}},chrdev:{node:{getattr:le.node_ops.getattr,setattr:le.node_ops.setattr},stream:ve.chrdev_stream_ops}});var o=ve.createNode(e,t,r,n);return ve.isDir(o.mode)?(o.node_ops=le.ops_table.dir.node,o.stream_ops=le.ops_table.dir.stream,o.contents={}):ve.isFile(o.mode)?(o.node_ops=le.ops_table.file.node,o.stream_ops=le.ops_table.file.stream,o.usedBytes=0,o.contents=null):ve.isLink(o.mode)?(o.node_ops=le.ops_table.link.node,o.stream_ops=le.ops_table.link.stream):ve.isChrdev(o.mode)&&(o.node_ops=le.ops_table.chrdev.node,o.stream_ops=le.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[t]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;if(!(r>=t)){t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256));var n=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,t){if(e.usedBytes!=t)if(0==t)e.contents=null,e.usedBytes=0;else{var r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr:function(e){var t={};return t.dev=ve.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,ve.isDir(e.mode)?t.size=4096:ve.isFile(e.mode)?t.size=e.usedBytes:ve.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&le.resizeFileStorage(e,t.size)},lookup:function(e,t){throw ve.genericErrors[44]},mknod:function(e,t,r,n){return le.createNode(e,t,r,n)},rename:function(e,t,r){if(ve.isDir(e.mode)){var n;try{n=ve.lookupNode(t,r)}catch(e){}if(n)for(var o in n.contents)throw new ve.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){var r=ve.lookupNode(e,t);for(var n in r.contents)throw new ve.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t=[".",".."];for(var r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,t,r){var n=le.createNode(e,t,41471,0);return n.link=r,n},readlink:function(e){if(!ve.isLink(e.mode))throw new ve.ErrnoError(28);return e.link}},stream_ops:{read:function(e,t,r,n,o){var i=e.node.contents;if(o>=e.node.usedBytes)return 0;var a=Math.min(e.node.usedBytes-o,n);if(a>8&&i.subarray)t.set(i.subarray(o,o+a),r);else for(var s=0;s<a;s++)t[r+s]=i[o+s];return a},write:function(e,t,r,n,o,i){if(!n)return 0;var a=e.node;if(a.timestamp=Date.now(),t.subarray&&(!a.contents||a.contents.subarray)){if(i)return a.contents=t.subarray(r,r+n),a.usedBytes=n,n;if(0===a.usedBytes&&0===o)return a.contents=t.slice(r,r+n),a.usedBytes=n,n;if(o+n<=a.usedBytes)return a.contents.set(t.subarray(r,r+n),o),n}if(le.expandFileStorage(a,o+n),a.contents.subarray&&t.subarray)a.contents.set(t.subarray(r,r+n),o);else for(var s=0;s<n;s++)a.contents[o+s]=t[r+s];return a.usedBytes=Math.max(a.usedBytes,o+n),n},llseek:function(e,t,r){var n=t;if(1===r?n+=e.position:2===r&&ve.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new ve.ErrnoError(28);return n},allocate:function(e,t,r){le.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,o){if(!ve.isFile(e.node.mode))throw new ve.ErrnoError(43);var i,a,s=e.node.contents;if(2&o||s.buffer!==P.buffer){if((r>0||r+t<s.length)&&(s=s.subarray?s.subarray(r,r+t):Array.prototype.slice.call(s,r,r+t)),a=!0,!(i=fe()))throw new ve.ErrnoError(48);P.set(s,i)}else a=!1,i=s.byteOffset;return{ptr:i,allocated:a}},msync:function(e,t,r,n,o){return le.stream_ops.write(e,t,0,n,r,!1),0}}},de=function(e,t,r,n){var o="al ".concat(e);p(e,(function(r){var n,i;n=r,i='Loading data file "'.concat(e,'" failed (no arrayBuffer).'),n||q(i),t(new Uint8Array(r)),o&&G()}),(function(t){if(!r)throw'Loading data file "'.concat(e,'" failed.');r()})),o&&V()},he=i.preloadPlugins||[];function pe(e,t){var r=0;return e&&(r|=365),t&&(r|=146),r}var ve={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(e=re.resolve(e)))return{path:"",node:null};if((t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count>8)throw new ve.ErrnoError(32);for(var r=e.split("/").filter((function(e){return!!e})),n=ve.root,o="/",i=0;i<r.length;i++){var a=i===r.length-1;if(a&&t.parent)break;if(n=ve.lookupNode(n,r[i]),o=ee.join2(o,r[i]),ve.isMountpoint(n)&&(!a||a&&t.follow_mount)&&(n=n.mounted.root),!a||t.follow)for(var s=0;ve.isLink(n.mode);){var c=ve.readlink(o);if(o=re.resolve(ee.dirname(o),c),n=ve.lookupPath(o,{recurse_count:t.recurse_count+1}).node,s++>40)throw new ve.ErrnoError(32)}}return{path:o,node:n}},getPath:function(e){for(var t;;){if(ve.isRoot(e)){var r=e.mount.mountpoint;return t?"/"!==r[r.length-1]?"".concat(r,"/").concat(t):r+t:r}t=t?"".concat(e.name,"/").concat(t):e.name,e=e.parent}},hashName:function(e,t){for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%ve.nameTable.length},hashAddNode:function(e){var t=ve.hashName(e.parent.id,e.name);e.name_next=ve.nameTable[t],ve.nameTable[t]=e},hashRemoveNode:function(e){var t=ve.hashName(e.parent.id,e.name);if(ve.nameTable[t]===e)ve.nameTable[t]=e.name_next;else for(var r=ve.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:function(e,t){var r=ve.mayLookup(e);if(r)throw new ve.ErrnoError(r,e);for(var n=ve.hashName(e.id,t),o=ve.nameTable[n];o;o=o.name_next){var i=o.name;if(o.parent.id===e.id&&i===t)return o}return ve.lookup(e,t)},createNode:function(e,t,r,n){var o=new ve.FSNode(e,t,r,n);return ve.hashAddNode(o),o},destroyNode:function(e){ve.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return!(49152&~e)},flagsToPermissionString:function(e){var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:function(e,t){return ve.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2},mayLookup:function(e){var t=ve.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate:function(e,t){try{return ve.lookupNode(e,t),20}catch(e){}return ve.nodePermissions(e,"wx")},mayDelete:function(e,t,r){var n;try{n=ve.lookupNode(e,t)}catch(e){return e.errno}var o=ve.nodePermissions(e,"wx");if(o)return o;if(r){if(!ve.isDir(n.mode))return 54;if(ve.isRoot(n)||ve.getPath(n)===ve.cwd())return 10}else if(ve.isDir(n.mode))return 31;return 0},mayOpen:function(e,t){return e?ve.isLink(e.mode)?32:ve.isDir(e.mode)&&("r"!==ve.flagsToPermissionString(t)||512&t)?31:ve.nodePermissions(e,ve.flagsToPermissionString(t)):44},MAX_OPEN_FDS:4096,nextfd:function(){for(var e=0;e<=ve.MAX_OPEN_FDS;e++)if(!ve.streams[e])return e;throw new ve.ErrnoError(33)},getStreamChecked:function(e){var t=ve.getStream(e);if(!t)throw new ve.ErrnoError(8);return t},getStream:function(e){return ve.streams[e]},createStream:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1;return ve.FSStream||(ve.FSStream=function(){this.shared={}},ve.FSStream.prototype={},Object.defineProperties(ve.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return!!(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new ve.FSStream,e),-1==t&&(t=ve.nextfd()),e.fd=t,ve.streams[t]=e,e},closeStream:function(e){ve.streams[e]=null},chrdev_stream_ops:{open:function(e){var t=ve.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new ve.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,t){return e<<8|t},registerDevice:function(e,t){ve.devices[e]={stream_ops:t}},getDevice:function(e){return ve.devices[e]},getMounts:function(e){for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:function(e,t){"function"==typeof e&&(t=e,e=!1),ve.syncFSRequests++,ve.syncFSRequests>1&&_("warning: ".concat(ve.syncFSRequests," FS.syncfs operations in flight at once, probably just doing extra work"));var r=ve.getMounts(ve.root.mount),n=0;function o(e){return ve.syncFSRequests--,t(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,o(e));++n>=r.length&&o(null)}r.forEach((function(t){if(!t.type.syncfs)return i(null);t.type.syncfs(t,e,i)}))},mount:function(e,t,r){var n,o="/"===r,i=!r;if(o&&ve.root)throw new ve.ErrnoError(10);if(!o&&!i){var a=ve.lookupPath(r,{follow_mount:!1});if(r=a.path,n=a.node,ve.isMountpoint(n))throw new ve.ErrnoError(10);if(!ve.isDir(n.mode))throw new ve.ErrnoError(54)}var s={type:e,opts:t,mountpoint:r,mounts:[]},c=e.mount(s);return c.mount=s,s.root=c,o?ve.root=c:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),c},unmount:function(e){var t=ve.lookupPath(e,{follow_mount:!1});if(!ve.isMountpoint(t.node))throw new ve.ErrnoError(28);var r=t.node,n=r.mounted,o=ve.getMounts(n);Object.keys(ve.nameTable).forEach((function(e){for(var t=ve.nameTable[e];t;){var r=t.name_next;o.includes(t.mount)&&ve.destroyNode(t),t=r}})),r.mounted=null;var i=r.mount.mounts.indexOf(n);r.mount.mounts.splice(i,1)},lookup:function(e,t){return e.node_ops.lookup(e,t)},mknod:function(e,t,r){var n=ve.lookupPath(e,{parent:!0}).node,o=ee.basename(e);if(!o||"."===o||".."===o)throw new ve.ErrnoError(28);var i=ve.mayCreate(n,o);if(i)throw new ve.ErrnoError(i);if(!n.node_ops.mknod)throw new ve.ErrnoError(63);return n.node_ops.mknod(n,o,t,r)},create:function(e,t){return t=void 0!==t?t:438,t&=4095,t|=32768,ve.mknod(e,t,0)},mkdir:function(e,t){return t=void 0!==t?t:511,t&=1023,t|=16384,ve.mknod(e,t,0)},mkdirTree:function(e,t){for(var r=e.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{ve.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,t,r){return void 0===r&&(r=t,t=438),t|=8192,ve.mknod(e,t,r)},symlink:function(e,t){if(!re.resolve(e))throw new ve.ErrnoError(44);var r=ve.lookupPath(t,{parent:!0}).node;if(!r)throw new ve.ErrnoError(44);var n=ee.basename(t),o=ve.mayCreate(r,n);if(o)throw new ve.ErrnoError(o);if(!r.node_ops.symlink)throw new ve.ErrnoError(63);return r.node_ops.symlink(r,n,e)},rename:function(e,t){var r,n,o=ee.dirname(e),i=ee.dirname(t),a=ee.basename(e),s=ee.basename(t);if(r=ve.lookupPath(e,{parent:!0}).node,n=ve.lookupPath(t,{parent:!0}).node,!r||!n)throw new ve.ErrnoError(44);if(r.mount!==n.mount)throw new ve.ErrnoError(75);var c,u=ve.lookupNode(r,a),f=re.relative(e,i);if("."!==f.charAt(0))throw new ve.ErrnoError(28);if("."!==(f=re.relative(t,o)).charAt(0))throw new ve.ErrnoError(55);try{c=ve.lookupNode(n,s)}catch(e){}if(u!==c){var l=ve.isDir(u.mode),d=ve.mayDelete(r,a,l);if(d)throw new ve.ErrnoError(d);if(d=c?ve.mayDelete(n,s,l):ve.mayCreate(n,s))throw new ve.ErrnoError(d);if(!r.node_ops.rename)throw new ve.ErrnoError(63);if(ve.isMountpoint(u)||c&&ve.isMountpoint(c))throw new ve.ErrnoError(10);if(n!==r&&(d=ve.nodePermissions(r,"w")))throw new ve.ErrnoError(d);ve.hashRemoveNode(u);try{r.node_ops.rename(u,n,s)}catch(e){throw e}finally{ve.hashAddNode(u)}}},rmdir:function(e){var t=ve.lookupPath(e,{parent:!0}).node,r=ee.basename(e),n=ve.lookupNode(t,r),o=ve.mayDelete(t,r,!0);if(o)throw new ve.ErrnoError(o);if(!t.node_ops.rmdir)throw new ve.ErrnoError(63);if(ve.isMountpoint(n))throw new ve.ErrnoError(10);t.node_ops.rmdir(t,r),ve.destroyNode(n)},readdir:function(e){var t=ve.lookupPath(e,{follow:!0}).node;if(!t.node_ops.readdir)throw new ve.ErrnoError(54);return t.node_ops.readdir(t)},unlink:function(e){var t=ve.lookupPath(e,{parent:!0}).node;if(!t)throw new ve.ErrnoError(44);var r=ee.basename(e),n=ve.lookupNode(t,r),o=ve.mayDelete(t,r,!1);if(o)throw new ve.ErrnoError(o);if(!t.node_ops.unlink)throw new ve.ErrnoError(63);if(ve.isMountpoint(n))throw new ve.ErrnoError(10);t.node_ops.unlink(t,r),ve.destroyNode(n)},readlink:function(e){var t=ve.lookupPath(e).node;if(!t)throw new ve.ErrnoError(44);if(!t.node_ops.readlink)throw new ve.ErrnoError(28);return re.resolve(ve.getPath(t.parent),t.node_ops.readlink(t))},stat:function(e,t){var r=ve.lookupPath(e,{follow:!t}).node;if(!r)throw new ve.ErrnoError(44);if(!r.node_ops.getattr)throw new ve.ErrnoError(63);return r.node_ops.getattr(r)},lstat:function(e){return ve.stat(e,!0)},chmod:function(e,t,r){var n;if(!(n="string"==typeof e?ve.lookupPath(e,{follow:!r}).node:e).node_ops.setattr)throw new ve.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&t|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,t){ve.chmod(e,t,!0)},fchmod:function(e,t){var r=ve.getStreamChecked(e);ve.chmod(r.node,t)},chown:function(e,t,r,n){var o;if(!(o="string"==typeof e?ve.lookupPath(e,{follow:!n}).node:e).node_ops.setattr)throw new ve.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(e,t,r){ve.chown(e,t,r,!0)},fchown:function(e,t,r){var n=ve.getStreamChecked(e);ve.chown(n.node,t,r)},truncate:function(e,t){if(t<0)throw new ve.ErrnoError(28);var r;if(!(r="string"==typeof e?ve.lookupPath(e,{follow:!0}).node:e).node_ops.setattr)throw new ve.ErrnoError(63);if(ve.isDir(r.mode))throw new ve.ErrnoError(31);if(!ve.isFile(r.mode))throw new ve.ErrnoError(28);var n=ve.nodePermissions(r,"w");if(n)throw new ve.ErrnoError(n);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate:function(e,t){var r=ve.getStreamChecked(e);if(!(2097155&r.flags))throw new ve.ErrnoError(28);ve.truncate(r.node,t)},utime:function(e,t,r){var n=ve.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(t,r)})},open:function(e,t,r){if(""===e)throw new ve.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?function(e){var t={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[e];if(void 0===t)throw new Error("Unknown file open mode: ".concat(e));return t}(t):t)?4095&r|32768:0,"object"==m(e))n=e;else{e=ee.normalize(e);try{n=ve.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var o=!1;if(64&t)if(n){if(128&t)throw new ve.ErrnoError(20)}else n=ve.mknod(e,r,0),o=!0;if(!n)throw new ve.ErrnoError(44);if(ve.isChrdev(n.mode)&&(t&=-513),65536&t&&!ve.isDir(n.mode))throw new ve.ErrnoError(54);if(!o){var a=ve.mayOpen(n,t);if(a)throw new ve.ErrnoError(a)}512&t&&!o&&ve.truncate(n,0),t&=-131713;var s=ve.createStream({node:n,path:ve.getPath(n),flags:t,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return s.stream_ops.open&&s.stream_ops.open(s),!i.logReadFiles||1&t||(ve.readFiles||(ve.readFiles={}),e in ve.readFiles||(ve.readFiles[e]=1)),s},close:function(e){if(ve.isClosed(e))throw new ve.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{ve.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,t,r){if(ve.isClosed(e))throw new ve.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new ve.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new ve.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:function(e,t,r,n,o){if(n<0||o<0)throw new ve.ErrnoError(28);if(ve.isClosed(e))throw new ve.ErrnoError(8);if(1==(2097155&e.flags))throw new ve.ErrnoError(8);if(ve.isDir(e.node.mode))throw new ve.ErrnoError(31);if(!e.stream_ops.read)throw new ve.ErrnoError(28);var i=void 0!==o;if(i){if(!e.seekable)throw new ve.ErrnoError(70)}else o=e.position;var a=e.stream_ops.read(e,t,r,n,o);return i||(e.position+=a),a},write:function(e,t,r,n,o,i){if(n<0||o<0)throw new ve.ErrnoError(28);if(ve.isClosed(e))throw new ve.ErrnoError(8);if(!(2097155&e.flags))throw new ve.ErrnoError(8);if(ve.isDir(e.node.mode))throw new ve.ErrnoError(31);if(!e.stream_ops.write)throw new ve.ErrnoError(28);e.seekable&&1024&e.flags&&ve.llseek(e,0,2);var a=void 0!==o;if(a){if(!e.seekable)throw new ve.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,t,r,n,o,i);return a||(e.position+=s),s},allocate:function(e,t,r){if(ve.isClosed(e))throw new ve.ErrnoError(8);if(t<0||r<=0)throw new ve.ErrnoError(28);if(!(2097155&e.flags))throw new ve.ErrnoError(8);if(!ve.isFile(e.node.mode)&&!ve.isDir(e.node.mode))throw new ve.ErrnoError(43);if(!e.stream_ops.allocate)throw new ve.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:function(e,t,r,n,o){if(2&n&&!(2&o)&&2!=(2097155&e.flags))throw new ve.ErrnoError(2);if(1==(2097155&e.flags))throw new ve.ErrnoError(2);if(!e.stream_ops.mmap)throw new ve.ErrnoError(43);return e.stream_ops.mmap(e,t,r,n,o)},msync:function(e,t,r,n,o){return e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,o):0},munmap:function(e){return 0},ioctl:function(e,t,r){if(!e.stream_ops.ioctl)throw new ve.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'.concat(r.encoding,'"'));var n=ve.open(e,r.flags),o=ve.stat(e).size,i=new Uint8Array(o);return ve.read(n,i,0,o,0),"utf8"===r.encoding?t=oe(i,0):"binary"===r.encoding&&(t=i),ve.close(n),t},writeFile:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};r.flags=r.flags||577;var n=ve.open(e,r.flags,r.mode);if("string"==typeof t){var o=new Uint8Array(ae(t)+1),i=se(t,o,0,o.length);ve.write(n,o,0,i,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");ve.write(n,t,0,t.byteLength,void 0,r.canOwn)}ve.close(n)},cwd:function(){return ve.currentPath},chdir:function(e){var t=ve.lookupPath(e,{follow:!0});if(null===t.node)throw new ve.ErrnoError(44);if(!ve.isDir(t.node.mode))throw new ve.ErrnoError(54);var r=ve.nodePermissions(t.node,"x");if(r)throw new ve.ErrnoError(r);ve.currentPath=t.path},createDefaultDirectories:function(){ve.mkdir("/tmp"),ve.mkdir("/home"),ve.mkdir("/home/<USER>")},createDefaultDevices:function(){ve.mkdir("/dev"),ve.registerDevice(ve.makedev(1,3),{read:function(){return 0},write:function(e,t,r,n,o){return n}}),ve.mkdev("/dev/null",ve.makedev(1,3)),ue.register(ve.makedev(5,0),ue.default_tty_ops),ue.register(ve.makedev(6,0),ue.default_tty1_ops),ve.mkdev("/dev/tty",ve.makedev(5,0)),ve.mkdev("/dev/tty1",ve.makedev(6,0));var e=new Uint8Array(1024),t=0,r=function(){return 0===t&&(t=te(e).byteLength),e[--t]};ve.createDevice("/dev","random",r),ve.createDevice("/dev","urandom",r),ve.mkdir("/dev/shm"),ve.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){ve.mkdir("/proc");var e=ve.mkdir("/proc/self");ve.mkdir("/proc/self/fd"),ve.mount({mount:function(){var t=ve.createNode(e,"fd",16895,73);return t.node_ops={lookup:function(e,t){var r=+t,n=ve.getStreamChecked(r),o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},t}},{},"/proc/self/fd")},createStandardStreams:function(){i.stdin?ve.createDevice("/dev","stdin",i.stdin):ve.symlink("/dev/tty","/dev/stdin"),i.stdout?ve.createDevice("/dev","stdout",null,i.stdout):ve.symlink("/dev/tty","/dev/stdout"),i.stderr?ve.createDevice("/dev","stderr",null,i.stderr):ve.symlink("/dev/tty1","/dev/stderr"),ve.open("/dev/stdin",0),ve.open("/dev/stdout",1),ve.open("/dev/stderr",1)},ensureErrnoError:function(){ve.ErrnoError||(ve.ErrnoError=function(e,t){this.name="ErrnoError",this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},ve.ErrnoError.prototype=new Error,ve.ErrnoError.prototype.constructor=ve.ErrnoError,[44].forEach((function(e){ve.genericErrors[e]=new ve.ErrnoError(e),ve.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){ve.ensureErrnoError(),ve.nameTable=new Array(4096),ve.mount(le,{},"/"),ve.createDefaultDirectories(),ve.createDefaultDevices(),ve.createSpecialDirectories(),ve.filesystems={MEMFS:le}},init:function(e,t,r){ve.init.initialized=!0,ve.ensureErrnoError(),i.stdin=e||i.stdin,i.stdout=t||i.stdout,i.stderr=r||i.stderr,ve.createStandardStreams()},quit:function(){ve.init.initialized=!1;for(var e=0;e<ve.streams.length;e++){var t=ve.streams[e];t&&ve.close(t)}},findObject:function(e,t){var r=ve.analyzePath(e,t);return r.exists?r.object:null},analyzePath:function(e,t){try{e=(n=ve.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=ve.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=ee.basename(e),n=ve.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:function(e,t,r,n){e="string"==typeof e?e:ve.getPath(e);for(var o=t.split("/").reverse();o.length;){var i=o.pop();if(i){var a=ee.join2(e,i);try{ve.mkdir(a)}catch(e){}e=a}}return a},createFile:function(e,t,r,n,o){var i=ee.join2("string"==typeof e?e:ve.getPath(e),t),a=pe(n,o);return ve.create(i,a)},createDataFile:function(e,t,r,n,o,i){var a=t;e&&(e="string"==typeof e?e:ve.getPath(e),a=t?ee.join2(e,t):e);var s=pe(n,o),c=ve.create(a,s);if(r){if("string"==typeof r){for(var u=new Array(r.length),f=0,l=r.length;f<l;++f)u[f]=r.charCodeAt(f);r=u}ve.chmod(c,146|s);var d=ve.open(c,577);ve.write(d,r,0,r.length,0,i),ve.close(d),ve.chmod(c,s)}return c},createDevice:function(e,t,r,n){var o=ee.join2("string"==typeof e?e:ve.getPath(e),t),i=pe(!!r,!!n);ve.createDevice.major||(ve.createDevice.major=64);var a=ve.makedev(ve.createDevice.major++,0);return ve.registerDevice(a,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,t,n,o,i){for(var a=0,s=0;s<o;s++){var c;try{c=r()}catch(e){throw new ve.ErrnoError(29)}if(void 0===c&&0===a)throw new ve.ErrnoError(6);if(null==c)break;a++,t[n+s]=c}return a&&(e.node.timestamp=Date.now()),a},write:function(e,t,r,o,i){for(var a=0;a<o;a++)try{n(t[r+a])}catch(e){throw new ve.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),a}}),ve.mkdev(o,i,a)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!h)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=ce(h(e.url)),e.usedBytes=e.contents.length}catch(e){throw new ve.ErrnoError(29)}},createLazyFile:function(e,t,r,n,o){function i(){this.lengthKnown=!1,this.chunks=[]}if(i.prototype.get=function(e){if(!(e>this.length-1||e<0)){var t=e%this.chunkSize,r=e/this.chunkSize|0;return this.getter(r)[t]}},i.prototype.setDataGetter=function(e){this.getter=e},i.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",r,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+r+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),o=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,i=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,a=1048576;o||(a=n);var s=this;s.setDataGetter((function(e){var t=e*a,o=(e+1)*a-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=function(e,t){if(e>t)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(t>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",r,!1),n!==a&&o.setRequestHeader("Range","bytes="+e+"-"+t),o.responseType="arraybuffer",o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+r+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):ce(o.responseText||"")}(t,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!i&&n||(a=n=1,n=this.getter(0).length,a=n,k("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=a,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!d)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new i;Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:a}}else s={isDevice:!1,url:r};var c=ve.createFile(e,t,s,n,o);s.contents?c.contents=s.contents:s.url&&(c.contents=null,c.url=s.url),Object.defineProperties(c,{usedBytes:{get:function(){return this.contents.length}}});var u={};function f(e,t,r,n,o){var i=e.node.contents;if(o>=i.length)return 0;var a=Math.min(i.length-o,n);if(i.slice)for(var s=0;s<a;s++)t[r+s]=i[o+s];else for(s=0;s<a;s++)t[r+s]=i.get(o+s);return a}return Object.keys(c.stream_ops).forEach((function(e){var t=c.stream_ops[e];u[e]=function(){return ve.forceLoadFile(c),t.apply(null,arguments)}})),u.read=function(e,t,r,n,o){return ve.forceLoadFile(c),f(e,t,r,n,o)},u.mmap=function(e,t,r,n,o){ve.forceLoadFile(c);var i=fe();if(!i)throw new ve.ErrnoError(48);return f(e,P,i,t,r),{ptr:i,allocated:!0}},c.stream_ops=u,c}},me=function(e,t){return e?oe(T,e,t):""},ye={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(ee.isAbs(t))return t;var n;if(n=-100===e?ve.cwd():ye.getStreamFromFD(e).path,0==t.length){if(!r)throw new ve.ErrnoError(44);return n}return ee.join2(n,t)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&ee.normalize(t)!==ee.normalize(ve.getPath(e.node)))return-54;throw e}C[r>>2]=n.dev,C[r+4>>2]=n.mode,D[r+8>>2]=n.nlink,C[r+12>>2]=n.uid,C[r+16>>2]=n.gid,C[r+20>>2]=n.rdev,B=[n.size>>>0,(I=n.size,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[r+24>>2]=B[0],C[r+28>>2]=B[1],C[r+32>>2]=4096,C[r+36>>2]=n.blocks;var o=n.atime.getTime(),i=n.mtime.getTime(),a=n.ctime.getTime();return B=[Math.floor(o/1e3)>>>0,(I=Math.floor(o/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[r+40>>2]=B[0],C[r+44>>2]=B[1],D[r+48>>2]=o%1e3*1e3,B=[Math.floor(i/1e3)>>>0,(I=Math.floor(i/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[r+56>>2]=B[0],C[r+60>>2]=B[1],D[r+64>>2]=i%1e3*1e3,B=[Math.floor(a/1e3)>>>0,(I=Math.floor(a/1e3),+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[r+72>>2]=B[0],C[r+76>>2]=B[1],D[r+80>>2]=a%1e3*1e3,B=[n.ino>>>0,(I=n.ino,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[r+88>>2]=B[0],C[r+92>>2]=B[1],0},doMsync:function(e,t,r,n,o){if(!ve.isFile(t.node.mode))throw new ve.ErrnoError(43);if(2&n)return 0;var i=T.slice(e,e+r);ve.msync(t,i,o,r,n)},varargs:void 0,get:function(){return ye.varargs+=4,C[ye.varargs-4>>2]},getStr:function(e){return me(e)},getStreamFromFD:function(e){return ve.getStreamChecked(e)}};function ge(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: ".concat(e))}}var we=void 0;function be(e){for(var t="",r=e;T[r];)t+=we[T[r++]];return t}var Ee={},ke={},_e={},Pe=void 0;function Te(e){throw new Pe(e)}var Se=void 0;function Ae(e){throw new Se(e)}function Ce(e,t,r){function n(t){var n=r(t);n.length!==e.length&&Ae("Mismatched type converter count");for(var o=0;o<e.length;++o)De(e[o],n[o])}e.forEach((function(e){_e[e]=t}));var o=new Array(t.length),i=[],a=0;t.forEach((function(e,t){ke.hasOwnProperty(e)?o[t]=ke[e]:(i.push(e),Ee.hasOwnProperty(e)||(Ee[e]=[]),Ee[e].push((function(){o[t]=ke[e],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function De(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=t.name;if(e||Te('type "'.concat(n,'" must have a positive integer typeid pointer')),ke.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;Te("Cannot register type '".concat(n,"' twice"))}if(ke[e]=t,delete _e[e],Ee.hasOwnProperty(e)){var o=Ee[e];delete Ee[e],o.forEach((function(e){return e()}))}}(e,t,r)}function xe(e){if(!(this instanceof Ze))return!1;if(!(e instanceof Ze))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o}function Oe(e){Te(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Fe=!1;function je(e){}function Me(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function $e(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=$e(e,t,r.baseClass);return null===n?null:r.downcast(n)}var Re={};function Le(){return Object.keys(We).length}function Ne(){var e=[];for(var t in We)We.hasOwnProperty(t)&&e.push(We[t]);return e}var Ue=[];function ze(){for(;Ue.length;){var e=Ue.pop();e.$$.deleteScheduled=!1,e.delete()}}var Ie=void 0;function Be(e){Ie=e,Ue.length&&Ie&&Ie(ze)}var We={};function He(e,t){return t=function(e,t){for(void 0===t&&Te("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),We[t]}function Ve(e,t){return t.ptrType&&t.ptr||Ae("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&Ae("Both smartPtrType and smartPtr must be specified"),t.count={value:1},qe(Object.create(e,{$$:{value:t}}))}function Ge(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=He(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?Ve(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):Ve(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(t),s=Re[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var c=$e(t,this.registeredClass,i.registeredClass);return null===c?o.call(this):this.isSmartPointer?Ve(i.registeredClass.instancePrototype,{ptrType:i,ptr:c,smartPtrType:this,smartPtr:e}):Ve(i.registeredClass.instancePrototype,{ptrType:i,ptr:c})}var qe=function(e){return"undefined"==typeof FinalizationRegistry?(qe=function(e){return e},e):(Fe=new FinalizationRegistry((function(e){Me(e.$$)})),je=function(e){return Fe.unregister(e)},(qe=function(e){var t=e.$$;if(t.smartPtr){var r={$$:t};Fe.register(e,r,e)}return e})(e))};function Xe(){if(this.$$.ptr||Oe(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=qe(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t}function Ke(){this.$$.ptr||Oe(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Te("Object already scheduled for deletion"),je(this),Me(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Je(){return!this.$$.ptr}function Ye(){return this.$$.ptr||Oe(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&Te("Object already scheduled for deletion"),Ue.push(this),1===Ue.length&&Ie&&Ie(ze),this.$$.deleteScheduled=!0,this}function Ze(){}function Qe(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=48&&t<=57?"_".concat(e):e}function et(e,t){return c({},e=Qe(e),(function(){return t.apply(this,arguments)}))[e]}function tt(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||Te("Function '".concat(r,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(e[t].overloadTable,")!")),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function rt(e,t,r,n,o,i,a,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function nt(e,t,r){for(;t!==r;)t.upcast||Te("Expected null or instance of ".concat(r.name,", got an instance of ").concat(t.name)),e=t.upcast(e),t=t.baseClass;return e}function ot(e,t){if(null===t)return this.isReference&&Te("null is not a valid ".concat(this.name)),0;t.$$||Te('Cannot pass "'.concat(Ct(t),'" as a ').concat(this.name)),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type ".concat(this.name));var r=t.$$.ptrType.registeredClass;return nt(t.$$.ptr,r,this.registeredClass)}function it(e,t){var r;if(null===t)return this.isReference&&Te("null is not a valid ".concat(this.name)),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||Te('Cannot pass "'.concat(Ct(t),'" as a ').concat(this.name)),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&t.$$.ptrType.isConst&&Te("Cannot convert argument of type ".concat(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name," to parameter type ").concat(this.name));var n=t.$$.ptrType.registeredClass;if(r=nt(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&Te("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:Te("Cannot convert argument of type ".concat(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var o=t.clone();r=this.rawShare(r,At.toHandle((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:Te("Unsupporting sharing policy")}return r}function at(e,t){if(null===t)return this.isReference&&Te("null is not a valid ".concat(this.name)),0;t.$$||Te('Cannot pass "'.concat(Ct(t),'" as a ').concat(this.name)),t.$$.ptr||Te("Cannot pass deleted object as a pointer of type ".concat(this.name)),t.$$.ptrType.isConst&&Te("Cannot convert argument of type ".concat(t.$$.ptrType.name," to parameter type ").concat(this.name));var r=t.$$.ptrType.registeredClass;return nt(t.$$.ptr,r,this.registeredClass)}function st(e){return this.fromWireType(C[e>>2])}function ct(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function ut(e){this.rawDestructor&&this.rawDestructor(e)}function ft(e){null!==e&&e.delete()}function lt(e,t,r,n,o,i,a,s,c,u,f){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=u,this.rawDestructor=f,o||void 0!==t.baseClass?this.toWireType=it:n?(this.toWireType=ot,this.destructorFunction=null):(this.toWireType=at,this.destructorFunction=null)}var dt=[],ht=function(e){var t=dt[e];return t||(e>=dt.length&&(dt.length=e+1),dt[e]=t=F.get(e)),t},pt=function(e,t,r){return e.includes("j")?function(e,t,r){var n=i["dynCall_"+e];return r&&r.length?n.apply(null,[t].concat(r)):n.call(null,t)}(e,t,r):ht(t).apply(null,r)};function vt(e,t){var r,n,o,i=(e=be(e)).includes("j")?(r=e,n=t,o=[],function(){return o.length=0,Object.assign(o,arguments),pt(r,n,o)}):ht(t);return"function"!=typeof i&&Te("unknown function pointer with signature ".concat(e,": ").concat(t)),i}var mt=void 0;function yt(e){var t=Yt(e),r=be(t);return Kt(t),r}function gt(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||ke[t]||(_e[t]?_e[t].forEach(e):(r.push(t),n[t]=!0))})),new mt("".concat(e,": ")+r.map(yt).join([", "]))}function wt(e,t){for(var r=[],n=0;n<e;n++)r.push(D[t+4*n>>2]);return r}function bt(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function Et(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type ".concat(m(e)," which is not a function"));var r=et(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,o=e.apply(n,t);return o instanceof Object?o:n}function kt(e,t,r,n,o,i){var a=t.length;a<2&&Te("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var s=null!==t[1]&&null!==r,c=!1,u=1;u<t.length;++u)if(null!==t[u]&&void 0===t[u].destructorFunction){c=!0;break}var f="void"!==t[0].name,l="",d="";for(u=0;u<a-2;++u)l+=(0!==u?", ":"")+"arg"+u,d+=(0!==u?", ":"")+"arg"+u+"Wired";var h="\n        return function ".concat(Qe(e),"(").concat(l,") {\n        if (arguments.length !== ").concat(a-2,") {\n          throwBindingError('function ").concat(e," called with ").concat(arguments.length," arguments, expected ").concat(a-2," args!');\n        }");c&&(h+="var destructors = [];\n");var p=c?"destructors":"null",v=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[Te,n,o,bt,t[0],t[1]];for(s&&(h+="var thisWired = classParam.toWireType("+p+", this);\n"),u=0;u<a-2;++u)h+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+t[u+2].name+"\n",v.push("argType"+u),m.push(t[u+2]);if(s&&(d="thisWired"+(d.length>0?", ":"")+d),h+=(f||i?"var rv = ":"")+"invoker(fn"+(d.length>0?", ":"")+d+");\n",c)h+="runDestructors(destructors);\n";else for(u=s?1:2;u<t.length;++u){var y=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==t[u].destructorFunction&&(h+=y+"_dtor("+y+"); // "+t[u].name+"\n",v.push(y+"_dtor"),m.push(t[u].destructorFunction))}return f&&(h+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),h+="}\n",v.push(h),Et(Function,v).apply(null,m)}function _t(){this.allocated=[void 0],this.freelist=[]}var Pt=new _t;function Tt(e){e>=Pt.reserved&&0==--Pt.get(e).refcount&&Pt.free(e)}function St(){for(var e=0,t=Pt.reserved;t<Pt.allocated.length;++t)void 0!==Pt.allocated[t]&&++e;return e}var At={toValue:function(e){return e||Te("Cannot use deleted val. handle = "+e),Pt.get(e).value},toHandle:function(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return Pt.allocate({refcount:1,value:e})}}};function Ct(e){if(null===e)return"null";var t=m(e);return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Dt(e,t){switch(t){case 2:return function(e){return this.fromWireType(x[e>>2])};case 3:return function(e){return this.fromWireType(O[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function xt(e,t,r){switch(t){case 0:return r?function(e){return P[e]}:function(e){return T[e]};case 1:return r?function(e){return S[e>>1]}:function(e){return A[e>>1]};case 2:return r?function(e){return C[e>>2]}:function(e){return D[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var Ot,Ft,jt,Mt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,$t=function(e,t){for(var r=e,n=r>>1,o=n+t/2;!(n>=o)&&A[n];)++n;if((r=n<<1)-e>32&&Mt)return Mt.decode(T.subarray(e,r));for(var i="",a=0;!(a>=t/2);++a){var s=S[e+2*a>>1];if(0==s)break;i+=String.fromCharCode(s)}return i},Rt=function(e,t,r){if(void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);S[t>>1]=a,t+=2}return S[t>>1]=0,t-n},Lt=function(e){return 2*e.length},Nt=function(e,t){for(var r=0,n="";!(r>=t/4);){var o=C[e+4*r>>2];if(0==o)break;if(++r,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n},Ut=function(e,t,r){if(void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,o=n+r-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i)),C[t>>2]=a,(t+=4)+4>o)break}return C[t>>2]=0,t-n},zt=function(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t},It={},Bt=[],Wt=[],Ht={},Vt=function(){if(!Vt.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==("undefined"==typeof navigator?"undefined":m(navigator))&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var t in Ht)void 0===Ht[t]?delete e[t]:e[t]=Ht[t];var r=[];for(var t in e)r.push("".concat(t,"=").concat(e[t]));Vt.strings=r}return Vt.strings},Gt=function(e,t,r,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=ve.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n};Object.defineProperties(Gt.prototype,{read:{get:function(){return!(365&~this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return!(146&~this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return ve.isDir(this.mode)}},isDevice:{get:function(){return ve.isChrdev(this.mode)}}}),ve.FSNode=Gt,ve.createPreloadedFile=function(e,t,r,n,o,i,a,s,c,u){var f=t?re.resolve(ee.join2(e,t)):e;function l(r){function l(r){u&&u(),s||ve.createDataFile(e,t,r,n,o,c),i&&i(),G()}(function(e,t,r,n){"undefined"!=typeof Browser&&Browser.init();var o=!1;return he.forEach((function(i){o||i.canHandle(t)&&(i.handle(e,t,r,n),o=!0)})),o})(r,f,l,(function(){a&&a(),G()}))||l(r)}V(),"string"==typeof r?de(r,(function(e){return l(e)}),a):l(r)},ve.staticInit(),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);we=e}(),Pe=i.BindingError=function(e){function t(e){var r;return o(this,t),(r=n(this,t,[e])).name="BindingError",r}return f(t,e),a(t)}(g(Error)),Se=i.InternalError=function(e){function t(e){var r;return o(this,t),(r=n(this,t,[e])).name="InternalError",r}return f(t,e),a(t)}(g(Error)),Ze.prototype.isAliasOf=xe,Ze.prototype.clone=Xe,Ze.prototype.delete=Ke,Ze.prototype.isDeleted=Je,Ze.prototype.deleteLater=Ye,i.getInheritedInstanceCount=Le,i.getLiveInheritedInstances=Ne,i.flushPendingDeletes=ze,i.setDelayFunction=Be,lt.prototype.getPointee=ct,lt.prototype.destructor=ut,lt.prototype.argPackAdvance=8,lt.prototype.readValueFromPointer=st,lt.prototype.deleteObject=ft,lt.prototype.fromWireType=Ge,mt=i.UnboundTypeError=(Ot=Error,(jt=et(Ft="UnboundTypeError",(function(e){this.name=Ft,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(Ot.prototype),jt.prototype.constructor=jt,jt.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},jt),Object.assign(_t.prototype,{get:function(e){return this.allocated[e]},has:function(e){return void 0!==this.allocated[e]},allocate:function(e){var t=this.freelist.pop()||this.allocated.length;return this.allocated[t]=e,t},free:function(e){this.allocated[e]=void 0,this.freelist.push(e)}}),Pt.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),Pt.reserved=Pt.allocated.length,i.count_emval_handles=St;var qt={p:function(e,t,r){throw new Q(e).init(t,r),e},C:function(e,t,r){ye.varargs=r;try{var n=ye.getStreamFromFD(e);switch(t){case 0:return(o=ye.get())<0?-28:ve.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=ye.get();return n.flags|=o,0;case 5:return o=ye.get(),S[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return i=28,C[Jt()>>2]=i,-1}}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return-e.errno}var i},w:function(e,t,r,n){ye.varargs=n;try{t=ye.getStr(t),t=ye.calculateAt(e,t);var o=n?ye.get():0;return ve.open(t,r,o).fd}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return-e.errno}},t:function(e,t,r,n,o){},n:function(e,t,r,n,o){var i=ge(r);De(e,{name:t=be(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=P;else if(2===r)n=S;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=C}return this.fromWireType(n[e>>i])},destructorFunction:null})},r:function(e,t,r,n,o,a,s,c,u,f,l,d,h){l=be(l),a=vt(o,a),c&&(c=vt(s,c)),f&&(f=vt(u,f)),h=vt(d,h);var p=Qe(l);!function(e,t,r){i.hasOwnProperty(e)?(Te("Cannot register public name '".concat(e,"' twice")),tt(i,e,e),i.hasOwnProperty(r)&&Te("Cannot register multiple overloads of a function with the same number of arguments (".concat(r,")!")),i[e].overloadTable[r]=t):i[e]=t}(p,(function(){gt("Cannot construct ".concat(l," due to unbound types"),[n])})),Ce([e,t,r],n?[n]:[],(function(t){var r,o;t=t[0],o=n?(r=t.registeredClass).instancePrototype:Ze.prototype;var s=et(p,(function(){if(Object.getPrototypeOf(this)!==u)throw new Pe("Use 'new' to construct "+l);if(void 0===d.constructor_body)throw new Pe(l+" has no accessible constructor");var e=d.constructor_body[arguments.length];if(void 0===e)throw new Pe("Tried to invoke ctor of ".concat(l," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(d.constructor_body).toString(),") parameters instead!"));return e.apply(this,arguments)})),u=Object.create(o,{constructor:{value:s}});s.prototype=u;var d=new rt(l,s,u,h,r,a,c,f);d.baseClass&&(void 0===d.baseClass.__derivedClasses&&(d.baseClass.__derivedClasses=[]),d.baseClass.__derivedClasses.push(d));var v=new lt(l,d,!0,!1,!1),m=new lt(l+"*",d,!1,!1,!1),y=new lt(l+" const*",d,!1,!0,!1);return Re[e]={pointerType:m,constPointerType:y},function(e,t,r){i.hasOwnProperty(e)||Ae("Replacing nonexistant public symbol"),void 0!==i[e].overloadTable&&void 0!==r||(i[e]=t,i[e].argCount=r)}(p,s),[v,m,y]}))},q:function(e,t,r,n,o,i){var a=wt(t,r);o=vt(n,o),Ce([],[e],(function(e){e=e[0];var r="constructor ".concat(e.name);if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Pe("Cannot register multiple constructors with identical number of parameters (".concat(t-1,") for class '").concat(e.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return e.registeredClass.constructor_body[t-1]=function(){gt("Cannot construct ".concat(e.name," due to unbound types"),a)},Ce([],a,(function(n){return n.splice(1,0,null),e.registeredClass.constructor_body[t-1]=kt(r,n,null,o,i),[]})),[]}))},d:function(e,t,r,n,o,i,a,s,c){var u=wt(r,n);t=be(t),i=vt(o,i),Ce([],[e],(function(e){e=e[0];var n="".concat(e.name,".").concat(t);function o(){gt("Cannot call ".concat(n," due to unbound types"),u)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(t);var f=e.registeredClass.instancePrototype,l=f[t];return void 0===l||void 0===l.overloadTable&&l.className!==e.name&&l.argCount===r-2?(o.argCount=r-2,o.className=e.name,f[t]=o):(tt(f,t,n),f[t].overloadTable[r-2]=o),Ce([],u,(function(o){var s=kt(n,o,e,i,a,c);return void 0===f[t].overloadTable?(s.argCount=r-2,f[t]=s):f[t].overloadTable[r-2]=s,[]})),[]}))},D:function(e,t){De(e,{name:t=be(t),fromWireType:function(e){var t=At.toValue(e);return Tt(e),t},toWireType:function(e,t){return At.toHandle(t)},argPackAdvance:8,readValueFromPointer:st,destructorFunction:null})},k:function(e,t,r){var n=ge(r);De(e,{name:t=be(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:Dt(t,n),destructorFunction:null})},c:function(e,t,r,n,o){t=be(t);var i=ge(r),a=function(e){return e};if(0===n){var s=32-8*r;a=function(e){return e<<s>>>s}}var c=t.includes("unsigned");De(e,{name:t,fromWireType:a,toWireType:c?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:xt(t,i,0!==n),destructorFunction:null})},b:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=D,r=t[e>>=2],o=t[e+1];return new n(t.buffer,o,r)}De(e,{name:r=be(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},j:function(e,t){var r="std::string"===(t=be(t));De(e,{name:t,fromWireType:function(e){var t,n=D[e>>2],o=e+4;if(r)for(var i=o,a=0;a<=n;++a){var s=o+a;if(a==n||0==T[s]){var c=me(i,s-i);void 0===t?t=c:(t+=String.fromCharCode(0),t+=c),i=s+1}}else{var u=new Array(n);for(a=0;a<n;++a)u[a]=String.fromCharCode(T[o+a]);t=u.join("")}return Kt(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var o="string"==typeof t;o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||Te("Cannot pass non-string to std::string"),n=r&&o?ae(t):t.length;var i=Xt(4+n+1),a=i+4;if(D[i>>2]=n,r&&o)se(t,T,a,n+1);else if(o)for(var s=0;s<n;++s){var c=t.charCodeAt(s);c>255&&(Kt(a),Te("String has UTF-16 code units that do not fit in 8 bits")),T[a+s]=c}else for(s=0;s<n;++s)T[a+s]=t[s];return null!==e&&e.push(Kt,i),i},argPackAdvance:8,readValueFromPointer:st,destructorFunction:function(e){Kt(e)}})},f:function(e,t,r){var n,o,i,a,s;r=be(r),2===t?(n=$t,o=Rt,a=Lt,i=function(){return A},s=1):4===t&&(n=Nt,o=Ut,a=zt,i=function(){return D},s=2),De(e,{name:r,fromWireType:function(e){for(var r,o=D[e>>2],a=i(),c=e+4,u=0;u<=o;++u){var f=e+4+u*t;if(u==o||0==a[f>>s]){var l=n(c,f-c);void 0===r?r=l:(r+=String.fromCharCode(0),r+=l),c=f+t}}return Kt(e),r},toWireType:function(e,n){"string"!=typeof n&&Te("Cannot pass non-string to C++ string type ".concat(r));var i=a(n),c=Xt(4+i+t);return D[c>>2]=i>>s,o(n,c+4,i+t),null!==e&&e.push(Kt,c),c},argPackAdvance:8,readValueFromPointer:st,destructorFunction:function(e){Kt(e)}})},o:function(e,t){De(e,{isVoid:!0,name:t=be(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},g:function(e,t,r,n){var o,i;(e=Bt[e])(t=At.toValue(t),r=void 0===(i=It[o=r])?be(o):i,null,n)},m:Tt,l:function(e,t){var r=function(e,t){for(var r,n,o,i=new Array(e),a=0;a<e;++a)i[a]=(r=D[t+4*a>>2],n="parameter "+a,o=void 0,void 0===(o=ke[r])&&Te(n+" has unknown type "+yt(r)),o);return i}(e,t),n=r[0],o=n.name+"_$"+r.slice(1).map((function(e){return e.name})).join("_")+"$",i=Wt[o];if(void 0!==i)return i;for(var a=["retType"],s=[n],c="",u=0;u<e-1;++u)c+=(0!==u?", ":"")+"arg"+u,a.push("argType"+u),s.push(r[1+u]);var f="return function "+Qe("methodCaller_"+o)+"(handle, name, destructors, args) {\n",l=0;for(u=0;u<e-1;++u)f+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(l?"+"+l:"")+");\n",l+=r[u+1].argPackAdvance;for(f+="    var rv = handle[name]("+c+");\n",u=0;u<e-1;++u)r[u+1].deleteObject&&(f+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(f+="    return retType.toWireType(destructors, rv);\n"),f+="};\n",a.push(f);var d,h,p=Et(Function,a).apply(null,s);return d=p,h=Bt.length,Bt.push(d),i=h,Wt[o]=i,i},a:function(){q("")},e:function(){return Date.now()},v:function(){return T.length},A:function(e,t,r){return T.copyWithin(e,t,t+r)},u:function(e){T.length,q("OOM")},y:function(e,t){var r=0;return Vt().forEach((function(n,o){var i=t+r;D[e+4*o>>2]=i,function(e,t){for(var r=0;r<e.length;++r)P[0|t++]=e.charCodeAt(r);P[0|t]=0}(n,i),r+=n.length+1})),0},z:function(e,t){var r=Vt();D[e>>2]=r.length;var n=0;return r.forEach((function(e){n+=e.length+1})),D[t>>2]=n,0},i:function(e){try{var t=ye.getStreamFromFD(e);return ve.close(t),0}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return e.errno}},x:function(e,t){try{var r=ye.getStreamFromFD(e),n=r.tty?2:ve.isDir(r.mode)?3:ve.isLink(r.mode)?7:4;return P[0|t]=n,S[t+2>>1]=0,B=[0,(I=0,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[t+8>>2]=B[0],C[t+12>>2]=B[1],B=[0,(I=0,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[t+16>>2]=B[0],C[t+20>>2]=B[1],0}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return e.errno}},B:function(e,t,r,n){try{var o=function(e,t,r,n){for(var o=0,i=0;i<r;i++){var a=D[t>>2],s=D[t+4>>2];t+=8;var c=ve.read(e,P,a,s,n);if(c<0)return-1;if(o+=c,c<s)break}return o}(ye.getStreamFromFD(e),t,r);return D[n>>2]=o,0}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return e.errno}},s:function(e,t,r,n,o){var i,a,s=(a=r)+2097152>>>0<4194305-!!(i=t)?(i>>>0)+4294967296*a:NaN;try{if(isNaN(s))return 61;var c=ye.getStreamFromFD(e);return ve.llseek(c,s,n),B=[c.position>>>0,(I=c.position,+Math.abs(I)>=1?I>0?+Math.floor(I/4294967296)>>>0:~~+Math.ceil((I-+(~~I>>>0))/4294967296)>>>0:0)],C[o>>2]=B[0],C[o+4>>2]=B[1],c.getdents&&0===s&&0===n&&(c.getdents=null),0}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return e.errno}},h:function(e,t,r,n){try{var o=function(e,t,r,n){for(var o=0,i=0;i<r;i++){var a=D[t>>2],s=D[t+4>>2];t+=8;var c=ve.write(e,P,a,s,n);if(c<0)return-1;o+=c}return o}(ye.getStreamFromFD(e),t,r);return D[n>>2]=o,0}catch(e){if(void 0===ve||"ErrnoError"!==e.name)throw e;return e.errno}}};!function(){var e,r,n,o,a={a:qt};function s(e,t){var r,n=e.exports;return b=(E=n).E,r=b.buffer,i.HEAP8=P=new Int8Array(r),i.HEAP16=S=new Int16Array(r),i.HEAP32=C=new Int32Array(r),i.HEAPU8=T=new Uint8Array(r),i.HEAPU16=A=new Uint16Array(r),i.HEAPU32=D=new Uint32Array(r),i.HEAPF32=x=new Float32Array(r),i.HEAPF64=O=new Float64Array(r),F=E.H,function(e){$.unshift(e)}(E.F),G(),n}if(V(),i.instantiateWasm)try{return i.instantiateWasm(a,s)}catch(e){_("Module.instantiateWasm callback failed with error: "+e),t(e)}(e=w,r=U,n=a,o=function(e){s(e.instance)},e||"function"!=typeof WebAssembly.instantiateStreaming||X(r)||"function"!=typeof fetch?J(r,n,o):fetch(r,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,n).then(o,(function(e){return _("wasm streaming compile failed: "+e),_("falling back to ArrayBuffer instantiation"),J(r,n,o)}))}))).catch(t)}();var Xt=function(e){return(Xt=E.G)(e)},Kt=function(e){return(Kt=E.I)(e)},Jt=function(){return(Jt=E.J)()},Yt=function(e){return(Yt=E.K)(e)};i.__embind_initialize_bindings=function(){return(i.__embind_initialize_bindings=E.L)()};var Zt,Qt=function(e){return(Qt=E.M)(e)};function er(){function t(){Zt||(Zt=!0,i.calledRun=!0,j||(i.noFSInit||ve.init.initialized||ve.init(),ve.ignorePermissions=!1,Y($),e(i),i.onRuntimeInitialized&&i.onRuntimeInitialized(),function(){if(i.postRun)for("function"==typeof i.postRun&&(i.postRun=[i.postRun]);i.postRun.length;)N(i.postRun.shift());Y(R)}()))}W>0||(function(){if(i.preRun)for("function"==typeof i.preRun&&(i.preRun=[i.preRun]);i.preRun.length;)L(i.preRun.shift());Y(M)}(),W>0||(i.setStatus?(i.setStatus("Running..."),setTimeout((function(){setTimeout((function(){i.setStatus("")}),1),t()}),1)):t()))}if(i.dynCall_jiji=function(e,t,r,n,o){return(i.dynCall_jiji=E.N)(e,t,r,n,o)},i._ff_h264_cabac_tables=67061,H=function e(){Zt||er(),Zt||(H=e)},i.preInit)for("function"==typeof i.preInit&&(i.preInit=[i.preInit]);i.preInit.length>0;)i.preInit.pop()();return er(),r.ready}),ee=Q,te=function(e){function t(e){return o(this,t),n(this,t,[ee,(null==e?void 0:e.wasmPath)?fetch(null==e?void 0:e.wasmPath).then((function(e){return e.arrayBuffer()})):void 0,null==e?void 0:e.workerMode,null==e?void 0:e.canvas,null==e?void 0:e.yuvMode])}return f(t,e),a(t)}(Y),re=(function(){var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0}(),function(e,t,r,n){return new(r||(r=Promise))((function(t,o){function i(e){try{s(n.next(e))}catch(e){o(e)}}function a(e){try{s(n.throw(e))}catch(e){o(e)}}function s(e){var n;e.done?t(e.value):(n=e.value,n instanceof r?n:new r((function(e){e(n)}))).then(i,a)}s((n=n.apply(e,[])).next())}))}),ne=Symbol(32),oe=Symbol(16),ie=Symbol(8),ae=function(){return a((function e(t){o(this,e),this.g=t,this.consumed=0,t&&(this.need=t.next().value)}),[{key:"setG",value:function(e){this.g=e,this.demand(e.next().value,!0)}},{key:"consume",value:function(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}},{key:"demand",value:function(e,t){return t&&this.consume(),this.need=e,this.flush()}},{key:"read",value:function(e){return re(this,0,void 0,d().mark((function t(){var r=this;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.lastReadPromise){t.next=3;break}return t.next=3,this.lastReadPromise;case 3:return t.abrupt("return",this.lastReadPromise=new Promise((function(t,n){var o;r.reject=n,r.resolve=function(e){delete r.lastReadPromise,delete r.resolve,delete r.need,t(e)},r.demand(e,!0)||null===(o=r.pull)||void 0===o||o.call(r,e)})));case 4:case"end":return t.stop()}}),t,this)})))}},{key:"readU32",value:function(){return this.read(ne)}},{key:"readU16",value:function(){return this.read(oe)}},{key:"readU8",value:function(){return this.read(ie)}},{key:"close",value:function(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null===(e=this.reject)||void 0===e||e.call(this,new Error("EOF")),delete this.lastReadPromise}},{key:"flush",value:function(){if(this.buffer&&this.need){var e=null,t=this.buffer.subarray(this.consumed),r=0,n=function(e){return t.length<(r=e)};if("number"==typeof this.need){if(n(this.need))return;e=t.subarray(0,r)}else if(this.need===ne){if(n(4))return;e=t[0]<<24|t[1]<<16|t[2]<<8|t[3]}else if(this.need===oe){if(n(2))return;e=t[0]<<8|t[1]}else if(this.need===ie){if(n(1))return;e=t[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(n(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(t.subarray(0,r)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(n(this.need.byteLength))return;new Uint8Array(this.need).set(t.subarray(0,r)),e=this.need}return this.consumed+=r,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}}},{key:"write",value:function(e){var t=this;if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise((function(e){return t.pull=e}));this.flush()}},{key:"writeU32",value:function(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}},{key:"writeU16",value:function(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}},{key:"writeU8",value:function(e){this.malloc(1)[0]=e,this.flush()}},{key:"malloc",value:function(e){if(this.buffer){var t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{var n=new Uint8Array(r);n.set(this.buffer),this.buffer=n}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}])}();function se(){return performance&&performance.now?Math.floor(performance.now()):Date.now()}ae.U32=ne,ae.U16=oe,ae.U8=ie;var ce=function(){function e(t){o(this,e),this.core=t,$(this,"contextMap",new Map),$(this,"decodeProcessorMap",new WeakMap),this.addKVReportBeforeExitRoom=this.addKVReportBeforeExitRoom.bind(this),this.createDecoder=this.createDecoder.bind(this),this.core.innerEmitter.on("51",this.addKVReportBeforeExitRoom)}return a(e,[{key:"getAlias",value:function(){return"videoDecoder"}},{key:"getGroup",value:function(e){return(null==e?void 0:e.track)?"".concat(e.track.userId).concat(e.track.streamType):"*"}},{key:"getName",value:function(){return e.Name}},{key:"getValidateRule",value:function(e){return{type:this.core.enums.BASIC_TYPE.Object}}},{key:"createDecoder",value:function(e,t){switch(e){case"wasm":return new te(j(F({},t),{wasmPath:"".concat(this.core.assetsPath||".","/videodec.wasm")}));case"webCodecs":return new K;case"mse":throw new Error("mse decoder not supported yet");default:throw new Error("Unsupported decoder type")}}},{key:"start",value:function(e){var t=this;this.decodeProcessorMap.set(e.track,this.decode(e)),this.core.room.videoManager.addDecodeProcessor({processor:function(e){var r,n=e.frame,o=e.track;return(null==(r=t.decodeProcessorMap.get(o))?void 0:r({frame:n,track:o}))||n},type:3})}},{key:"decode",value:function(e){var t=this,r=this.core.rx,n=r.pipe,o=r.take,i=r.subscribe;return function(r){var a=r.frame,s=r.track;if(s!==e.track||"empty"===a.type)return a;if(t.contextMap.has(s))return t.contextMap.get(s).decode(a);var c=t.core.room.videoManager.createDecodeContext(j(F({},e),{createDecoder:t.createDecoder}));return n(c.trackDoneOB,o(1),i((function(){t.core.clearStarted(t,t.getGroup(e)),t.stop({track:s})}))),t.contextMap.set(s,c),c.decode(a)}}},{key:"stop",value:function(e){var t=this;if(null==e?void 0:e.track){var r=this.contextMap.get(e.track);r&&(r.close("stop"),this.report(r,e.track),this.contextMap.delete(e.track))}else this.contextMap.forEach((function(e,r){e.close("stop"),t.contextMap.delete(r)}));0===this.contextMap.size&&this.core.room.videoManager.removeDecodeProcessor({type:3})}},{key:"report",value:function(e,t){if(!e.isReported){e.isReported=!0;var r=se()-e.startPerformanceTime,n=this.getRemoteTrackFreezeDuration(t,e),o=n.renderFreezeTotal,i=n.dataFreezeTotal,a="report health data: ".concat(e.track.userId," ").concat(e.track.streamType," ").concat(e.type,"+").concat(e.renderer," goodType：").concat(e.goodType," ").concat(e.failedReason?"failedReason: ".concat(e.failedReason):"");r&&(this.core.kvStatManager.addNumber({key:514850,value:Math.floor(o/r*100),split:1,max:100}),a+=" RENDER_FREEZE_RATE: ".concat(Math.floor(o/r*100),"%")),r&&(this.core.kvStatManager.addNumber({key:514851,value:Math.floor(i/r*100),split:1,max:100}),a+=" DATA_FREEZE_RATE: ".concat(Math.floor(i/r*100),"%")),e.inputFrameCount&&(this.core.kvStatManager.addNumber({key:514852,value:Math.floor(e.decodedFrameCount/e.inputFrameCount*100),split:1,max:100}),a+=" VIDEO_CONSUME_RENDER_RATE: ".concat(Math.floor(e.decodedFrameCount/e.inputFrameCount*100),"%")),this.core.log.info(a)}}},{key:"addKVReportBeforeExitRoom",value:function(e){var t=this;e.room===this.core.room&&this.contextMap.forEach((function(e,r){t.report(e,r)}))}},{key:"update",value:function(e){var t=this.contextMap.get(e.track);t&&("mock"!==e.type?(t.close("update"),this.contextMap.set(e.track,this.core.room.videoManager.createDecodeContext(j(F({},e),{createDecoder:this.createDecoder})))):t.mock(this.core.enums.DECODE_FAILED_ERROR_CODE.TEST))}},{key:"getRemoteTrackFreezeDuration",value:function(e,t){var r=this.core.room.badCaseDetector.getRenderFreezeMap().get("".concat(e.userId,"_").concat(e.streamType)),n=this.core.room.badCaseDetector.getDataFreezeMap().get("".concat(e.userId,"_").concat(e.streamType)),o=0;r&&r.freezeTimeline.forEach((function(e){var r=e.startTime,n=e.endTime;r>t.startPerformanceTime&&(o+=0===n?se()-r:n-r)}));var i=0;return n&&n.durationItemList.forEach((function(e){e.startTime>t.startPerformanceTime&&(i+=e.getDuration())})),{renderFreezeTotal:o,dataFreezeTotal:i}}},{key:"destroy",value:function(){this.core.innerEmitter.off("51",this.addKVReportBeforeExitRoom)}}])}();return $(ce,"Name","TRTCVideoDecoder"),ce}));
