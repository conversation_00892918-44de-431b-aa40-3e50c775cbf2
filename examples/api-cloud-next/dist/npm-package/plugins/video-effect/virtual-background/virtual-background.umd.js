!function(A,g){"object"==typeof exports&&"undefined"!=typeof module?module.exports=g():"function"==typeof define&&define.amd?define(g):(A="undefined"!=typeof globalThis?globalThis:A||self).VirtualBackground=g()}(this,(function(){"use strict";function A(A,g){(null==g||g>A.length)&&(g=A.length);for(var I=0,t=Array(g);I<g;I++)t[I]=A[I];return t}function g(A,g,I,t,C,B,r){try{var Q=A[B](r),e=Q.value}catch(A){return void I(A)}Q.done?g(e):Promise.resolve(e).then(t,C)}function I(A){return function(){var I=this,t=arguments;return new Promise((function(C,B){var r=A.apply(I,t);function Q(A){g(r,C,B,Q,e,"next",A)}function e(A){g(r,<PERSON>,<PERSON>,Q,e,"throw",A)}Q(void 0)}))}}function t(A,g,I){return g=Q(g),function(A,g){if(g&&("object"==typeof g||"function"==typeof g))return g;if(void 0!==g)throw new TypeError("Derived constructors may only return object or undefined");return function(A){if(void 0===A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A}(A)}(A,n()?Reflect.construct(g,I||[],Q(A).constructor):g.apply(A,I))}function C(A,g){if(!(A instanceof g))throw new TypeError("Cannot call a class as a function")}function B(A,g){for(var I=0;I<g.length;I++){var t=g[I];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(A,a(t.key),t)}}function r(A,g,I){return g&&B(A.prototype,g),I&&B(A,I),Object.defineProperty(A,"prototype",{writable:!1}),A}function Q(A){return Q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(A){return A.__proto__||Object.getPrototypeOf(A)},Q(A)}function e(A,g){if("function"!=typeof g&&null!==g)throw new TypeError("Super expression must either be null or a function");A.prototype=Object.create(g&&g.prototype,{constructor:{value:A,writable:!0,configurable:!0}}),Object.defineProperty(A,"prototype",{writable:!1}),g&&E(A,g)}function n(){try{var A=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(A){}return(n=function(){return!!A})()}function o(){o=function(){return g};var A,g={},I=Object.prototype,t=I.hasOwnProperty,C=Object.defineProperty||function(A,g,I){A[g]=I.value},B="function"==typeof Symbol?Symbol:{},r=B.iterator||"@@iterator",Q=B.asyncIterator||"@@asyncIterator",e=B.toStringTag||"@@toStringTag";function n(A,g,I){return Object.defineProperty(A,g,{value:I,enumerable:!0,configurable:!0,writable:!0}),A[g]}try{n({},"")}catch(A){n=function(A,g,I){return A[g]=I}}function E(A,g,I,t){var B=g&&g.prototype instanceof y?g:y,r=Object.create(B.prototype),Q=new b(t||[]);return C(r,"_invoke",{value:M(A,I,Q)}),r}function i(A,g,I){try{return{type:"normal",arg:A.call(g,I)}}catch(A){return{type:"throw",arg:A}}}g.wrap=E;var a="suspendedStart",s="suspendedYield",c="executing",u="completed",h={};function y(){}function l(){}function f(){}var D={};n(D,r,(function(){return this}));var w=Object.getPrototypeOf,d=w&&w(w(v([])));d&&d!==I&&t.call(d,r)&&(D=d);var G=f.prototype=y.prototype=Object.create(D);function N(A){["next","throw","return"].forEach((function(g){n(A,g,(function(A){return this._invoke(g,A)}))}))}function p(A,g){function I(C,B,r,Q){var e=i(A[C],A,B);if("throw"!==e.type){var n=e.arg,o=n.value;return o&&"object"==typeof o&&t.call(o,"__await")?g.resolve(o.__await).then((function(A){I("next",A,r,Q)}),(function(A){I("throw",A,r,Q)})):g.resolve(o).then((function(A){n.value=A,r(n)}),(function(A){return I("throw",A,r,Q)}))}Q(e.arg)}var B;C(this,"_invoke",{value:function(A,t){function C(){return new g((function(g,C){I(A,t,g,C)}))}return B=B?B.then(C,C):C()}})}function M(g,I,t){var C=a;return function(B,r){if(C===c)throw Error("Generator is already running");if(C===u){if("throw"===B)throw r;return{value:A,done:!0}}for(t.method=B,t.arg=r;;){var Q=t.delegate;if(Q){var e=F(Q,t);if(e){if(e===h)continue;return e}}if("next"===t.method)t.sent=t._sent=t.arg;else if("throw"===t.method){if(C===a)throw C=u,t.arg;t.dispatchException(t.arg)}else"return"===t.method&&t.abrupt("return",t.arg);C=c;var n=i(g,I,t);if("normal"===n.type){if(C=t.done?u:s,n.arg===h)continue;return{value:n.arg,done:t.done}}"throw"===n.type&&(C=u,t.method="throw",t.arg=n.arg)}}}function F(g,I){var t=I.method,C=g.iterator[t];if(C===A)return I.delegate=null,"throw"===t&&g.iterator.return&&(I.method="return",I.arg=A,F(g,I),"throw"===I.method)||"return"!==t&&(I.method="throw",I.arg=new TypeError("The iterator does not provide a '"+t+"' method")),h;var B=i(C,g.iterator,I.arg);if("throw"===B.type)return I.method="throw",I.arg=B.arg,I.delegate=null,h;var r=B.arg;return r?r.done?(I[g.resultName]=r.value,I.next=g.nextLoc,"return"!==I.method&&(I.method="next",I.arg=A),I.delegate=null,h):r:(I.method="throw",I.arg=new TypeError("iterator result is not an object"),I.delegate=null,h)}function R(A){var g={tryLoc:A[0]};1 in A&&(g.catchLoc=A[1]),2 in A&&(g.finallyLoc=A[2],g.afterLoc=A[3]),this.tryEntries.push(g)}function Y(A){var g=A.completion||{};g.type="normal",delete g.arg,A.completion=g}function b(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(R,this),this.reset(!0)}function v(g){if(g||""===g){var I=g[r];if(I)return I.call(g);if("function"==typeof g.next)return g;if(!isNaN(g.length)){var C=-1,B=function I(){for(;++C<g.length;)if(t.call(g,C))return I.value=g[C],I.done=!1,I;return I.value=A,I.done=!0,I};return B.next=B}}throw new TypeError(typeof g+" is not iterable")}return l.prototype=f,C(G,"constructor",{value:f,configurable:!0}),C(f,"constructor",{value:l,configurable:!0}),l.displayName=n(f,e,"GeneratorFunction"),g.isGeneratorFunction=function(A){var g="function"==typeof A&&A.constructor;return!!g&&(g===l||"GeneratorFunction"===(g.displayName||g.name))},g.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,f):(A.__proto__=f,n(A,e,"GeneratorFunction")),A.prototype=Object.create(G),A},g.awrap=function(A){return{__await:A}},N(p.prototype),n(p.prototype,Q,(function(){return this})),g.AsyncIterator=p,g.async=function(A,I,t,C,B){void 0===B&&(B=Promise);var r=new p(E(A,I,t,C),B);return g.isGeneratorFunction(I)?r:r.next().then((function(A){return A.done?A.value:r.next()}))},N(G),n(G,e,"Generator"),n(G,r,(function(){return this})),n(G,"toString",(function(){return"[object Generator]"})),g.keys=function(A){var g=Object(A),I=[];for(var t in g)I.push(t);return I.reverse(),function A(){for(;I.length;){var t=I.pop();if(t in g)return A.value=t,A.done=!1,A}return A.done=!0,A}},g.values=v,b.prototype={constructor:b,reset:function(g){if(this.prev=0,this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(Y),!g)for(var I in this)"t"===I.charAt(0)&&t.call(this,I)&&!isNaN(+I.slice(1))&&(this[I]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0].completion;if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(g){if(this.done)throw g;var I=this;function C(t,C){return Q.type="throw",Q.arg=g,I.next=t,C&&(I.method="next",I.arg=A),!!C}for(var B=this.tryEntries.length-1;B>=0;--B){var r=this.tryEntries[B],Q=r.completion;if("root"===r.tryLoc)return C("end");if(r.tryLoc<=this.prev){var e=t.call(r,"catchLoc"),n=t.call(r,"finallyLoc");if(e&&n){if(this.prev<r.catchLoc)return C(r.catchLoc,!0);if(this.prev<r.finallyLoc)return C(r.finallyLoc)}else if(e){if(this.prev<r.catchLoc)return C(r.catchLoc,!0)}else{if(!n)throw Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return C(r.finallyLoc)}}}},abrupt:function(A,g){for(var I=this.tryEntries.length-1;I>=0;--I){var C=this.tryEntries[I];if(C.tryLoc<=this.prev&&t.call(C,"finallyLoc")&&this.prev<C.finallyLoc){var B=C;break}}B&&("break"===A||"continue"===A)&&B.tryLoc<=g&&g<=B.finallyLoc&&(B=null);var r=B?B.completion:{};return r.type=A,r.arg=g,B?(this.method="next",this.next=B.finallyLoc,h):this.complete(r)},complete:function(A,g){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&g&&(this.next=g),h},finish:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.finallyLoc===A)return this.complete(I.completion,I.afterLoc),Y(I),h}},catch:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.tryLoc===A){var t=I.completion;if("throw"===t.type){var C=t.arg;Y(I)}return C}}throw Error("illegal catch attempt")},delegateYield:function(g,I,t){return this.delegate={iterator:v(g),resultName:I,nextLoc:t},"next"===this.method&&(this.arg=A),h}},g}function E(A,g){return E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(A,g){return A.__proto__=g,A},E(A,g)}function i(A,g){return function(A){if(Array.isArray(A))return A}(A)||function(A,g){var I=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=I){var t,C,B,r,Q=[],e=!0,n=!1;try{if(B=(I=I.call(A)).next,0===g);else for(;!(e=(t=B.call(I)).done)&&(Q.push(t.value),Q.length!==g);e=!0);}catch(A){n=!0,C=A}finally{try{if(!e&&null!=I.return&&(r=I.return(),Object(r)!==r))return}finally{if(n)throw C}}return Q}}(A,g)||c(A,g)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(A){var g=function(A,g){if("object"!=typeof A||!A)return A;var I=A[Symbol.toPrimitive];if(void 0!==I){var t=I.call(A,g);if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A,"string");return"symbol"==typeof g?g:g+""}function s(A){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},s(A)}function c(g,I){if(g){if("string"==typeof g)return A(g,I);var t={}.toString.call(g).slice(8,-1);return"Object"===t&&g.constructor&&(t=g.constructor.name),"Map"===t||"Set"===t?Array.from(g):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?A(g,I):void 0}}function u(A){var g="function"==typeof Map?new Map:void 0;return u=function(A){if(null===A||!function(A){try{return-1!==Function.toString.call(A).indexOf("[native code]")}catch(g){return"function"==typeof A}}(A))return A;if("function"!=typeof A)throw new TypeError("Super expression must either be null or a function");if(void 0!==g){if(g.has(A))return g.get(A);g.set(A,I)}function I(){return function(A,g,I){if(n())return Reflect.construct.apply(null,arguments);var t=[null];t.push.apply(t,g);var C=new(A.bind.apply(A,t));return I&&E(C,I.prototype),C}(A,arguments,Q(this).constructor)}return I.prototype=Object.create(A.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),E(I,A)},u(A)}var h=Object.defineProperty,y=Object.defineProperties,l=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertySymbols,D=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,d=function(A,g,I){return g in A?h(A,g,{enumerable:!0,configurable:!0,writable:!0,value:I}):A[g]=I},G=function(A,g){for(var I in g||(g={}))D.call(g,I)&&d(A,I,g[I]);if(f){var t,C=function(A,g){var I="undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(!I){if(Array.isArray(A)||(I=c(A))||g){I&&(A=I);var t=0,C=function(){};return{s:C,n:function(){return t>=A.length?{done:!0}:{done:!1,value:A[t++]}},e:function(A){throw A},f:C}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var B,r=!0,Q=!1;return{s:function(){I=I.call(A)},n:function(){var A=I.next();return r=A.done,A},e:function(A){Q=!0,B=A},f:function(){try{r||null==I.return||I.return()}finally{if(Q)throw B}}}}(f(g));try{for(C.s();!(t=C.n()).done;){I=t.value;w.call(g,I)&&d(A,I,g[I])}}catch(A){C.e(A)}finally{C.f()}}return A},N=function(A,g,I){return d(A,"symbol"!==s(g)?g+"":g,I)};function p(A){return M.apply(this,arguments)}function M(){return(M=I(o().mark((function A(g){var I,t,C,B,r,Q,e,n;return o().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return I=g.sdkAppId,t=g.userId,C=g.userSig,B=g.core,Q=Math.round((new Date).getTime()/1e3),A.prev=2,A.next=5,B.schedule.getAbilityConfig(I,B.schedule.ScheduleRequestType.TRTC_AUTO_CONF,{sdkAppId:I,userId:t,userSig:C,timestamp:Q});case 5:if(e=A.sent,B.log.info("virtual background ability response: ".concat(JSON.stringify(e))),n=e.data,!(null==(r=null==n?void 0:n.trtcAutoConf)?void 0:r.web_ar)){A.next=10;break}return A.abrupt("return",{auth:!0,timestamp:Q});case 10:return A.abrupt("return",{auth:!1});case 13:return A.prev=13,A.t0=A.catch(2),B.log.error("virtual background fetch error",A.t0),A.abrupt("return",{auth:!1});case 17:case"end":return A.stop()}}),A,null,[[2,13]])})))).apply(this,arguments)}var F={sdkAppId:{required:!0,type:"number"},userId:{required:!0,type:"string"},userSig:{required:!0,type:"string"}};var R,Y=(R="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(){var A,g,I=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=I;B.ready=new Promise((function(I,t){A=I,g=t}));var Q=Object.assign({},B),n="";"undefined"!=typeof document&&document.currentScript&&(n=document.currentScript.src),R&&(n=R),n=0!==n.indexOf("blob:")?n.substr(0,n.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var o,E,i=B.print||console.log.bind(console),a=B.printErr||console.error.bind(console);function c(A){if(m(A))return function(A){for(var g=atob(A),I=new Uint8Array(g.length),t=0;t<g.length;++t)I[t]=g.charCodeAt(t);return I}(A.slice(L.length))}Object.assign(B,Q),Q=null,B.arguments&&B.arguments,B.thisProgram&&B.thisProgram,B.quit&&B.quit,B.wasmBinary&&(o=B.wasmBinary),"object"!=("undefined"==typeof WebAssembly?"undefined":s(WebAssembly))&&v("no native wasm support detected");var h,y,l,f,D,w,d,G,N=!1,p=[],M=[],F=[],Y=0,b=null;function v(A){B.onAbort&&B.onAbort(A),a(A="Aborted("+A+")"),N=!0,A+=". Build with -sASSERTIONS for more info.";var I=new WebAssembly.RuntimeError(A);throw g(I),I}var k,S,L="data:application/octet-stream;base64,",m=function(A){return A.startsWith(L)};function H(A){return Promise.resolve().then((function(){return function(A){if(A==k&&o)return new Uint8Array(o);var g=c(A);if(g)return g;throw"both async and sync fetching of the wasm failed"}(A)}))}function U(A,g,I,t){return function(A,g,I){return H(A).then((function(A){return WebAssembly.instantiate(A,g)})).then((function(A){return A})).then(I,(function(A){a("failed to asynchronously prepare wasm: ".concat(A)),v(A)}))}(g,I,t)}m(k="data:application/octet-stream;base64,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")||(S=k,k=B.locateFile?B.locateFile(S,n):n+S);var K=function(A){for(;A.length>0;)A.shift()(B)};function J(A){this.excPtr=A,this.ptr=A-24,this.set_type=function(A){w[this.ptr+4>>2]=A},this.get_type=function(){return w[this.ptr+4>>2]},this.set_destructor=function(A){w[this.ptr+8>>2]=A},this.get_destructor=function(){return w[this.ptr+8>>2]},this.set_caught=function(A){A=A?1:0,h[this.ptr+12|0]=A},this.get_caught=function(){return 0!=h[this.ptr+12|0]},this.set_rethrown=function(A){A=A?1:0,h[this.ptr+13|0]=A},this.get_rethrown=function(){return 0!=h[this.ptr+13|0]},this.init=function(A,g){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(g)},this.set_adjusted_ptr=function(A){w[this.ptr+16>>2]=A},this.get_adjusted_ptr=function(){return w[this.ptr+16>>2]},this.get_exception_ptr=function(){if(ug(this.get_type()))return w[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}B.noExitRuntime;var Z,T,j,x=function(A){for(var g="",I=A;y[I];)g+=Z[y[I++]];return g},P={},W={},O={},V=function(A){throw new T(A)},X=function(A){throw new j(A)},z=function(A,g,I){function t(g){var t=I(g);t.length!==A.length&&X("Mismatched type converter count");for(var C=0;C<A.length;++C)q(A[C],t[C])}A.forEach((function(A){O[A]=g}));var C=new Array(g.length),B=[],r=0;g.forEach((function(A,g){W.hasOwnProperty(A)?C[g]=W[A]:(B.push(A),P.hasOwnProperty(A)||(P[A]=[]),P[A].push((function(){C[g]=W[A],++r===B.length&&t(C)})))})),0===B.length&&t(C)};function q(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=g.name;if(A||V('type "'.concat(t,'" must have a positive integer typeid pointer')),W.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;V("Cannot register type '".concat(t,"' twice"))}if(W[A]=g,delete O[A],P.hasOwnProperty(A)){var C=P[A];delete P[A],C.forEach((function(A){return A()}))}}(A,g,I)}var $,_=function(A){V(A.$$.ptrType.registeredClass.name+" instance already deleted")},AA=!1,gA=function(A){},IA=function(A){A.count.value-=1,0===A.count.value&&function(A){A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)}(A)},tA=function(A,g,I){if(g===I)return A;if(void 0===I.baseClass)return null;var t=tA(A,g,I.baseClass);return null===t?null:I.downcast(t)},CA={},BA=function(){return Object.keys(oA).length},rA=function(){var A=[];for(var g in oA)oA.hasOwnProperty(g)&&A.push(oA[g]);return A},QA=[],eA=function(){for(;QA.length;){var A=QA.pop();A.$$.deleteScheduled=!1,A.delete()}},nA=function(A){$=A,QA.length&&$&&$(eA)},oA={},EA=function(A,g){return g=function(A,g){for(void 0===g&&V("ptr should not be undefined");A.baseClass;)g=A.upcast(g),A=A.baseClass;return g}(A,g),oA[g]},iA=function(A,g){return g.ptrType&&g.ptr||X("makeClassHandle requires ptr and ptrType"),!!g.smartPtrType!=!!g.smartPtr&&X("Both smartPtrType and smartPtr must be specified"),g.count={value:1},sA(Object.create(A,{$$:{value:g}}))};function aA(A){var g=this.getPointee(A);if(!g)return this.destructor(A),null;var I=EA(this.registeredClass,g);if(void 0!==I){if(0===I.$$.count.value)return I.$$.ptr=g,I.$$.smartPtr=A,I.clone();var t=I.clone();return this.destructor(A),t}function C(){return this.isSmartPointer?iA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:g,smartPtrType:this,smartPtr:A}):iA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var B,r=this.registeredClass.getActualType(g),Q=CA[r];if(!Q)return C.call(this);B=this.isConst?Q.constPointerType:Q.pointerType;var e=tA(g,this.registeredClass,B.registeredClass);return null===e?C.call(this):this.isSmartPointer?iA(B.registeredClass.instancePrototype,{ptrType:B,ptr:e,smartPtrType:this,smartPtr:A}):iA(B.registeredClass.instancePrototype,{ptrType:B,ptr:e})}var sA=function(A){return"undefined"==typeof FinalizationRegistry?(sA=function(A){return A},A):(AA=new FinalizationRegistry((function(A){IA(A.$$)})),gA=function(A){return AA.unregister(A)},(sA=function(A){var g=A.$$;if(g.smartPtr){var I={$$:g};AA.register(A,I,A)}return A})(A))};function cA(){}var uA=function(A,g){return Object.defineProperty(g,"name",{value:A})},hA=function(A,g,I){if(void 0===A[g].overloadTable){var t=A[g];A[g]=function(){return A[g].overloadTable.hasOwnProperty(arguments.length)||V("Function '".concat(I,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(A[g].overloadTable,")!")),A[g].overloadTable[arguments.length].apply(this,arguments)},A[g].overloadTable=[],A[g].overloadTable[t.argCount]=t}};function yA(A,g,I,t,C,B,r,Q){this.name=A,this.constructor=g,this.instancePrototype=I,this.rawDestructor=t,this.baseClass=C,this.getActualType=B,this.upcast=r,this.downcast=Q,this.pureVirtualFunctions=[]}var lA=function(A,g,I){for(;g!==I;)g.upcast||V("Expected null or instance of ".concat(I.name,", got an instance of ").concat(g.name)),A=g.upcast(A),g=g.baseClass;return A};function fA(A,g){if(null===g)return this.isReference&&V("null is not a valid ".concat(this.name)),0;g.$$||V('Cannot pass "'.concat(WA(g),'" as a ').concat(this.name)),g.$$.ptr||V("Cannot pass deleted object as a pointer of type ".concat(this.name));var I=g.$$.ptrType.registeredClass;return lA(g.$$.ptr,I,this.registeredClass)}function DA(A,g){var I;if(null===g)return this.isReference&&V("null is not a valid ".concat(this.name)),this.isSmartPointer?(I=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,I),I):0;g.$$||V('Cannot pass "'.concat(WA(g),'" as a ').concat(this.name)),g.$$.ptr||V("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&g.$$.ptrType.isConst&&V("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));var t=g.$$.ptrType.registeredClass;if(I=lA(g.$$.ptr,t,this.registeredClass),this.isSmartPointer)switch(void 0===g.$$.smartPtr&&V("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:g.$$.smartPtrType===this?I=g.$$.smartPtr:V("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:I=g.$$.smartPtr;break;case 2:if(g.$$.smartPtrType===this)I=g.$$.smartPtr;else{var C=g.clone();I=this.rawShare(I,ZA.toHandle((function(){return C.delete()}))),null!==A&&A.push(this.rawDestructor,I)}break;default:V("Unsupporting sharing policy")}return I}function wA(A,g){if(null===g)return this.isReference&&V("null is not a valid ".concat(this.name)),0;g.$$||V('Cannot pass "'.concat(WA(g),'" as a ').concat(this.name)),g.$$.ptr||V("Cannot pass deleted object as a pointer of type ".concat(this.name)),g.$$.ptrType.isConst&&V("Cannot convert argument of type ".concat(g.$$.ptrType.name," to parameter type ").concat(this.name));var I=g.$$.ptrType.registeredClass;return lA(g.$$.ptr,I,this.registeredClass)}function dA(A){return this.fromWireType(w[A>>2])}function GA(A,g,I,t,C,B,r,Q,e,n,o){this.name=A,this.registeredClass=g,this.isReference=I,this.isConst=t,this.isSmartPointer=C,this.pointeeType=B,this.sharingPolicy=r,this.rawGetPointee=Q,this.rawConstructor=e,this.rawShare=n,this.rawDestructor=o,C||void 0!==g.baseClass?this.toWireType=DA:t?(this.toWireType=fA,this.destructorFunction=null):(this.toWireType=wA,this.destructorFunction=null)}var NA,pA,MA=[],FA=function(A){var g=MA[A];return g||(A>=MA.length&&(MA.length=A+1),MA[A]=g=NA.get(A)),g},RA=function(A,g,I){return A.includes("j")?function(A,g,I){var t=B["dynCall_"+A];return I&&I.length?t.apply(null,[g].concat(I)):t.call(null,g)}(A,g,I):FA(g).apply(null,I)},YA=function(A,g){var I,t,C,B=(A=x(A)).includes("j")?(I=A,t=g,C=[],function(){return C.length=0,Object.assign(C,arguments),RA(I,t,C)}):FA(g);return"function"!=typeof B&&V("unknown function pointer with signature ".concat(A,": ").concat(g)),B},bA=function(A){var g=sg(A),I=x(g);return cg(g),I},vA=function(A,g){var I=[],t={};throw g.forEach((function A(g){t[g]||W[g]||(O[g]?O[g].forEach(A):(I.push(g),t[g]=!0))})),new pA("".concat(A,": ")+I.map(bA).join([", "]))},kA=function(A,g){for(var I=[],t=0;t<A;t++)I.push(w[g+4*t>>2]);return I},SA=function(A){for(;A.length;){var g=A.pop();A.pop()(g)}};function LA(A,g,I,t,C,B){var r=g.length;r<2&&V("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var Q=null!==g[1]&&null!==I,e=!1,n=1;n<g.length;++n)if(null!==g[n]&&void 0===g[n].destructorFunction){e=!0;break}var o="void"!==g[0].name,E=r-2,i=new Array(E),a=[],s=[];return uA(A,(function(){var I;arguments.length!==E&&V("function ".concat(A," called with ").concat(arguments.length," arguments, expected ").concat(E)),s.length=0,a.length=Q?2:1,a[0]=C,Q&&(I=g[1].toWireType(s,this),a[1]=I);for(var B=0;B<E;++B)i[B]=g[B+2].toWireType(s,arguments[B]),a.push(i[B]);return function(A){if(e)SA(s);else for(var t=Q?1:2;t<g.length;t++){var C=1===t?I:i[t-2];null!==g[t].destructorFunction&&g[t].destructorFunction(C)}if(o)return g[0].fromWireType(A)}(t.apply(null,a))}))}var mA=function(A,g,I){return A instanceof Object||V("".concat(I,' with invalid "this": ').concat(A)),A instanceof g.registeredClass.constructor||V("".concat(I,' incompatible with "this" of type ').concat(A.constructor.name)),A.$$.ptr||V("cannot call emscripten binding method ".concat(I," on deleted object")),lA(A.$$.ptr,A.$$.ptrType.registeredClass,g.registeredClass)};function HA(){this.allocated=[void 0],this.freelist=[]}var UA=new HA,KA=function(A){A>=UA.reserved&&0==--UA.get(A).refcount&&UA.free(A)},JA=function(){for(var A=0,g=UA.reserved;g<UA.allocated.length;++g)void 0!==UA.allocated[g]&&++A;return A},ZA={toValue:function(A){return A||V("Cannot use deleted val. handle = "+A),UA.get(A).value},toHandle:function(A){switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return UA.allocate({refcount:1,value:A})}}};function TA(A){return this.fromWireType(D[A>>2])}var jA,xA,PA,WA=function(A){if(null===A)return"null";var g=s(A);return"object"===g||"array"===g||"function"===g?A.toString():""+A},OA=function(A,g){switch(g){case 4:return function(A){return this.fromWireType(d[A>>2])};case 8:return function(A){return this.fromWireType(G[A>>3])};default:throw new TypeError("invalid float width (".concat(g,"): ").concat(A))}},VA=function(A,g,I){switch(g){case 1:return I?function(A){return h[0|A]}:function(A){return y[0|A]};case 2:return I?function(A){return l[A>>1]}:function(A){return f[A>>1]};case 4:return I?function(A){return D[A>>2]}:function(A){return w[A>>2]};default:throw new TypeError("invalid integer width (".concat(g,"): ").concat(A))}},XA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,zA=function(A,g,I){for(var t=g+I,C=g;A[C]&&!(C>=t);)++C;if(C-g>16&&A.buffer&&XA)return XA.decode(A.subarray(g,C));for(var B="";g<C;){var r=A[g++];if(128&r){var Q=63&A[g++];if(192!=(224&r)){var e=63&A[g++];if((r=224==(240&r)?(15&r)<<12|Q<<6|e:(7&r)<<18|Q<<12|e<<6|63&A[g++])<65536)B+=String.fromCharCode(r);else{var n=r-65536;B+=String.fromCharCode(55296|n>>10,56320|1023&n)}}else B+=String.fromCharCode((31&r)<<6|Q)}else B+=String.fromCharCode(r)}return B},qA="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,$A=function(A,g){for(var I=A,t=I>>1,C=t+g/2;!(t>=C)&&f[t];)++t;if((I=t<<1)-A>32&&qA)return qA.decode(y.subarray(A,I));for(var B="",r=0;!(r>=g/2);++r){var Q=l[A+2*r>>1];if(0==Q)break;B+=String.fromCharCode(Q)}return B},_A=function(A,g,I){if(void 0===I&&(I=2147483647),I<2)return 0;for(var t=g,C=(I-=2)<2*A.length?I/2:A.length,B=0;B<C;++B){var r=A.charCodeAt(B);l[g>>1]=r,g+=2}return l[g>>1]=0,g-t},Ag=function(A){return 2*A.length},gg=function(A,g){for(var I=0,t="";!(I>=g/4);){var C=D[A+4*I>>2];if(0==C)break;if(++I,C>=65536){var B=C-65536;t+=String.fromCharCode(55296|B>>10,56320|1023&B)}else t+=String.fromCharCode(C)}return t},Ig=function(A,g,I){if(void 0===I&&(I=2147483647),I<4)return 0;for(var t=g,C=t+I-4,B=0;B<A.length;++B){var r=A.charCodeAt(B);if(r>=55296&&r<=57343&&(r=65536+((1023&r)<<10)|1023&A.charCodeAt(++B)),D[g>>2]=r,(g+=4)+4>C)break}return D[g>>2]=0,g-t},tg=function(A){for(var g=0,I=0;I<A.length;++I){var t=A.charCodeAt(I);t>=55296&&t<=57343&&++I,g+=4}return g},Cg=function(A,g){var I=W[A];return void 0===I&&V(g+" has unknown type "+bA(A)),I},Bg=function(A,g,I){var t=[],C=A.toWireType(t,I);return t.length&&(w[g>>2]=ZA.toHandle(t)),C},rg={},Qg=[],eg=Reflect.construct,ng=[null,[],[]];!function(){for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);Z=A}(),T=B.BindingError=function(A){function g(A){var I;return C(this,g),(I=t(this,g,[A])).name="BindingError",I}return e(g,A),r(g)}(u(Error)),j=B.InternalError=function(A){function g(A){var I;return C(this,g),(I=t(this,g,[A])).name="InternalError",I}return e(g,A),r(g)}(u(Error)),Object.assign(cA.prototype,{isAliasOf:function(A){if(!(this instanceof cA))return!1;if(!(A instanceof cA))return!1;var g=this.$$.ptrType.registeredClass,I=this.$$.ptr;A.$$=A.$$;for(var t=A.$$.ptrType.registeredClass,C=A.$$.ptr;g.baseClass;)I=g.upcast(I),g=g.baseClass;for(;t.baseClass;)C=t.upcast(C),t=t.baseClass;return g===t&&I===C},clone:function(){if(this.$$.ptr||_(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,g=sA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete:function(){this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&V("Object already scheduled for deletion"),gA(this),IA(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted:function(){return!this.$$.ptr},deleteLater:function(){return this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&V("Object already scheduled for deletion"),QA.push(this),1===QA.length&&$&&$(eA),this.$$.deleteScheduled=!0,this}}),B.getInheritedInstanceCount=BA,B.getLiveInheritedInstances=rA,B.flushPendingDeletes=eA,B.setDelayFunction=nA,Object.assign(GA.prototype,{getPointee:function(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor:function(A){this.rawDestructor&&this.rawDestructor(A)},argPackAdvance:8,readValueFromPointer:dA,deleteObject:function(A){null!==A&&A.delete()},fromWireType:aA}),pA=B.UnboundTypeError=(jA=Error,(PA=uA(xA="UnboundTypeError",(function(A){this.name=xA,this.message=A;var g=new Error(A).stack;void 0!==g&&(this.stack=this.toString()+"\n"+g.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(jA.prototype),PA.prototype.constructor=PA,PA.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},PA),Object.assign(HA.prototype,{get:function(A){return this.allocated[A]},has:function(A){return void 0!==this.allocated[A]},allocate:function(A){var g=this.freelist.pop()||this.allocated.length;return this.allocated[g]=A,g},free:function(A){this.allocated[A]=void 0,this.freelist.push(A)}}),UA.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),UA.reserved=UA.allocated.length,B.count_emval_handles=JA;var og,Eg={w:function(A,g,I){throw new J(A).init(g,I),A},q:function(A,g,I,t,C){},u:function(A,g,I,t){q(A,{name:g=x(g),fromWireType:function(A){return!!A},toWireType:function(A,g){return g?I:t},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(y[A])},destructorFunction:null})},y:function(A,g,I,t,C,r,Q,e,n,o,E,i,a){E=x(E),r=YA(C,r),e&&(e=YA(Q,e)),o&&(o=YA(n,o)),a=YA(i,a);var s=function(A){if(void 0===A)return"_unknown";var g=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return g>=48&&g<=57?"_".concat(A):A}(E);!function(A,g,I){B.hasOwnProperty(A)?(V("Cannot register public name '".concat(A,"' twice")),hA(B,A,A),B.hasOwnProperty(I)&&V("Cannot register multiple overloads of a function with the same number of arguments (".concat(I,")!")),B[A].overloadTable[I]=g):B[A]=g}(s,(function(){vA("Cannot construct ".concat(E," due to unbound types"),[t])})),z([A,g,I],t?[t]:[],(function(g){var I,C;g=g[0],C=t?(I=g.registeredClass).instancePrototype:cA.prototype;var Q=uA(E,(function(){if(Object.getPrototypeOf(this)!==n)throw new T("Use 'new' to construct "+E);if(void 0===i.constructor_body)throw new T(E+" has no accessible constructor");var A=i.constructor_body[arguments.length];if(void 0===A)throw new T("Tried to invoke ctor of ".concat(E," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(i.constructor_body).toString(),") parameters instead!"));return A.apply(this,arguments)})),n=Object.create(C,{constructor:{value:Q}});Q.prototype=n;var i=new yA(E,Q,n,a,I,r,e,o);i.baseClass&&(void 0===i.baseClass.__derivedClasses&&(i.baseClass.__derivedClasses=[]),i.baseClass.__derivedClasses.push(i));var c=new GA(E,i,!0,!1,!1),u=new GA(E+"*",i,!1,!1,!1),h=new GA(E+" const*",i,!1,!0,!1);return CA[A]={pointerType:u,constPointerType:h},function(A,g,I){B.hasOwnProperty(A)||X("Replacing nonexistant public symbol"),void 0!==B[A].overloadTable&&void 0!==I||(B[A]=g,B[A].argCount=I)}(s,Q),[c,u,h]}))},x:function(A,g,I,t,C,B){var r=kA(g,I);C=YA(t,C),z([],[A],(function(A){A=A[0];var I="constructor ".concat(A.name);if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[g-1])throw new T("Cannot register multiple constructors with identical number of parameters (".concat(g-1,") for class '").concat(A.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return A.registeredClass.constructor_body[g-1]=function(){vA("Cannot construct ".concat(A.name," due to unbound types"),r)},z([],r,(function(t){return t.splice(1,0,null),A.registeredClass.constructor_body[g-1]=LA(I,t,null,C,B),[]})),[]}))},i:function(A,g,I,t,C,B,r,Q,e){var n,o,E=kA(I,t);g=x(g),o=(n=(n=g).trim()).indexOf("("),g=-1!==o?n.substr(0,o):n,B=YA(C,B),z([],[A],(function(A){A=A[0];var t="".concat(A.name,".").concat(g);function C(){vA("Cannot call ".concat(t," due to unbound types"),E)}g.startsWith("@@")&&(g=Symbol[g.substring(2)]),Q&&A.registeredClass.pureVirtualFunctions.push(g);var e=A.registeredClass.instancePrototype,n=e[g];return void 0===n||void 0===n.overloadTable&&n.className!==A.name&&n.argCount===I-2?(C.argCount=I-2,C.className=A.name,e[g]=C):(hA(e,g,t),e[g].overloadTable[I-2]=C),z([],E,(function(C){var Q=LA(t,C,A,B,r);return void 0===e[g].overloadTable?(Q.argCount=I-2,e[g]=Q):e[g].overloadTable[I-2]=Q,[]})),[]}))},k:function(A,g,I,t,C,B,r,Q,e,n){g=x(g),C=YA(t,C),z([],[A],(function(A){A=A[0];var t="".concat(A.name,".").concat(g),o={get:function(){vA("Cannot access ".concat(t," due to unbound types"),[I,r])},enumerable:!0,configurable:!0};return o.set=e?function(){return vA("Cannot access ".concat(t," due to unbound types"),[I,r])}:function(A){return V(t+" is a read-only property")},Object.defineProperty(A.registeredClass.instancePrototype,g,o),z([],e?[I,r]:[I],(function(I){var r=I[0],o={get:function(){var g=mA(this,A,t+" getter");return r.fromWireType(C(B,g))},enumerable:!0};if(e){e=YA(Q,e);var E=I[1];o.set=function(g){var I=mA(this,A,t+" setter"),C=[];e(n,I,E.toWireType(C,g)),SA(C)}}return Object.defineProperty(A.registeredClass.instancePrototype,g,o),[]})),[]}))},t:function(A,g){q(A,{name:g=x(g),fromWireType:function(A){var g=ZA.toValue(A);return KA(A),g},toWireType:function(A,g){return ZA.toHandle(g)},argPackAdvance:8,readValueFromPointer:TA,destructorFunction:null})},p:function(A,g,I){q(A,{name:g=x(g),fromWireType:function(A){return A},toWireType:function(A,g){return g},argPackAdvance:8,readValueFromPointer:OA(g,I),destructorFunction:null})},g:function(A,g,I,t,C){g=x(g);var B=function(A){return A};if(0===t){var r=32-8*I;B=function(A){return A<<r>>>r}}var Q=g.includes("unsigned");q(A,{name:g,fromWireType:B,toWireType:Q?function(A,g){return this.name,g>>>0}:function(A,g){return this.name,g},argPackAdvance:8,readValueFromPointer:VA(g,I,0!==t),destructorFunction:null})},a:function(A,g,I){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][g];function C(A){var g=w[A>>2],I=w[A+4>>2];return new t(h.buffer,I,g)}q(A,{name:I=x(I),fromWireType:C,argPackAdvance:8,readValueFromPointer:C},{ignoreDuplicateRegistrations:!0})},o:function(A,g){var I="std::string"===(g=x(g));q(A,{name:g,fromWireType:function(A){var g,t,C,B=w[A>>2],r=A+4;if(I)for(var Q=r,e=0;e<=B;++e){var n=r+e;if(e==B||0==y[n]){var o=(C=n-Q,(t=Q)?zA(y,t,C):"");void 0===g?g=o:(g+=String.fromCharCode(0),g+=o),Q=n+1}}else{var E=new Array(B);for(e=0;e<B;++e)E[e]=String.fromCharCode(y[r+e]);g=E.join("")}return cg(A),g},toWireType:function(A,g){var t;g instanceof ArrayBuffer&&(g=new Uint8Array(g));var C="string"==typeof g;C||g instanceof Uint8Array||g instanceof Uint8ClampedArray||g instanceof Int8Array||V("Cannot pass non-string to std::string"),t=I&&C?function(A){for(var g=0,I=0;I<A.length;++I){var t=A.charCodeAt(I);t<=127?g++:t<=2047?g+=2:t>=55296&&t<=57343?(g+=4,++I):g+=3}return g}(g):g.length;var B=ag(4+t+1),r=B+4;if(w[B>>2]=t,I&&C)!function(A,g,I,t){if(!(t>0))return 0;for(var C=I+t-1,B=0;B<A.length;++B){var r=A.charCodeAt(B);if(r>=55296&&r<=57343&&(r=65536+((1023&r)<<10)|1023&A.charCodeAt(++B)),r<=127){if(I>=C)break;g[I++]=r}else if(r<=2047){if(I+1>=C)break;g[I++]=192|r>>6,g[I++]=128|63&r}else if(r<=65535){if(I+2>=C)break;g[I++]=224|r>>12,g[I++]=128|r>>6&63,g[I++]=128|63&r}else{if(I+3>=C)break;g[I++]=240|r>>18,g[I++]=128|r>>12&63,g[I++]=128|r>>6&63,g[I++]=128|63&r}}g[I]=0}(g,y,r,t+1);else if(C)for(var Q=0;Q<t;++Q){var e=g.charCodeAt(Q);e>255&&(cg(r),V("String has UTF-16 code units that do not fit in 8 bits")),y[r+Q]=e}else for(Q=0;Q<t;++Q)y[r+Q]=g[Q];return null!==A&&A.push(cg,B),B},argPackAdvance:8,readValueFromPointer:dA,destructorFunction:function(A){cg(A)}})},l:function(A,g,I){var t,C,B,r,Q;I=x(I),2===g?(t=$A,C=_A,r=Ag,B=function(){return f},Q=1):4===g&&(t=gg,C=Ig,r=tg,B=function(){return w},Q=2),q(A,{name:I,fromWireType:function(A){for(var I,C=w[A>>2],r=B(),e=A+4,n=0;n<=C;++n){var o=A+4+n*g;if(n==C||0==r[o>>Q]){var E=t(e,o-e);void 0===I?I=E:(I+=String.fromCharCode(0),I+=E),e=o+g}}return cg(A),I},toWireType:function(A,t){"string"!=typeof t&&V("Cannot pass non-string to C++ string type ".concat(I));var B=r(t),e=ag(4+B+g);return w[e>>2]=B>>Q,C(t,e+4,B+g),null!==A&&A.push(cg,e),e},argPackAdvance:8,readValueFromPointer:TA,destructorFunction:function(A){cg(A)}})},v:function(A,g){q(A,{isVoid:!0,name:g=x(g),argPackAdvance:0,fromWireType:function(){},toWireType:function(A,g){}})},j:function(A,g,I){return A=ZA.toValue(A),g=Cg(g,"emval::as"),Bg(g,I,A)},e:function(A,g,I,t,C){var B,r;return(A=Qg[A])(g=ZA.toValue(g),g[I=void 0===(r=rg[B=I])?x(B):r],t,C)},d:KA,f:function(A,g,I){var t=function(A,g){for(var I=new Array(A),t=0;t<A;++t)I[t]=Cg(w[g+4*t>>2],"parameter "+t);return I}(A,g),C=t.shift();A--;var B,r,Q=new Array(A),e="methodCaller<(".concat(t.map((function(A){return A.name})).join(", "),") => ").concat(C.name,">");return B=uA(e,(function(g,B,r,e){for(var n=0,o=0;o<A;++o)Q[o]=t[o].readValueFromPointer(e+n),n+=t[o].argPackAdvance;var E=1===I?eg(B,Q):B.apply(g,Q);for(o=0;o<A;++o)t[o].deleteObject&&t[o].deleteObject(Q[o]);return Bg(C,r,E)})),r=Qg.length,Qg.push(B),r},c:function(A){A>4&&(UA.get(A).refcount+=1)},b:function(A){var g=ZA.toValue(A);SA(g),KA(A)},h:function(A,g){var I=(A=Cg(A,"_emval_take_value")).readValueFromPointer(g);return ZA.toHandle(I)},m:function(){v("")},s:function(A,g,I){return y.copyWithin(A,g,g+I)},r:function(A){y.length,v("OOM")},n:function(A,g,I,t){for(var C,B,r,Q=0,e=0;e<I;e++){var n=w[g>>2],o=w[g+4>>2];g+=8;for(var E=0;E<o;E++)C=A,B=y[n+E],r=void 0,r=ng[C],0===B||10===B?((1===C?i:a)(zA(r,0)),r.length=0):r.push(B);Q+=o}return w[t>>2]=Q,0}},ig=function(){var A={a:Eg};function I(A,g){var I,t;return ig=A.exports,E=ig.z,I=E.buffer,B.HEAP8=h=new Int8Array(I),B.HEAP16=l=new Int16Array(I),B.HEAPU8=y=new Uint8Array(I),B.HEAPU16=f=new Uint16Array(I),B.HEAP32=D=new Int32Array(I),B.HEAPU32=w=new Uint32Array(I),B.HEAPF32=d=new Float32Array(I),B.HEAPF64=G=new Float64Array(I),NA=ig.C,t=ig.A,M.unshift(t),function(){if(Y--,B.monitorRunDependencies&&B.monitorRunDependencies(Y),0==Y&&b){var A=b;b=null,A()}}(),ig}if(Y++,B.monitorRunDependencies&&B.monitorRunDependencies(Y),B.instantiateWasm)try{return B.instantiateWasm(A,I)}catch(A){a("Module.instantiateWasm callback failed with error: ".concat(A)),g(A)}return U(0,k,A,(function(A){I(A.instance)})).catch(g),{}}(),ag=function(A){return(ag=ig.B)(A)},sg=function(A){return(sg=ig.D)(A)},cg=function(A){return(cg=ig.E)(A)},ug=function(A){return(ug=ig.F)(A)};function hg(){function g(){og||(og=!0,B.calledRun=!0,N||(K(M),A(B),B.onRuntimeInitialized&&B.onRuntimeInitialized(),function(){if(B.postRun)for("function"==typeof B.postRun&&(B.postRun=[B.postRun]);B.postRun.length;)A=B.postRun.shift(),F.unshift(A);var A;K(F)}()))}Y>0||(function(){if(B.preRun)for("function"==typeof B.preRun&&(B.preRun=[B.preRun]);B.preRun.length;)A=B.preRun.shift(),p.unshift(A);var A;K(p)}(),Y>0||(B.setStatus?(B.setStatus("Running..."),setTimeout((function(){setTimeout((function(){B.setStatus("")}),1),g()}),1)):g()))}if(B.dynCall_jiji=function(A,g,I,t,C){return(B.dynCall_jiji=ig.G)(A,g,I,t,C)},B._vertexShaderSource=10624,b=function A(){og||hg(),og||(b=A)},B.preInit)for("function"==typeof B.preInit&&(B.preInit=[B.preInit]);B.preInit.length>0;)B.preInit.pop()();return hg(),I.ready}),b=Y,v="undefined"==typeof navigator?"":navigator.userAgent,k=function(A){return new RegExp(A,"i").test(v)},S=function(A){if(k(A)){var g=new RegExp("".concat(A,"\\/([\\d.]+)")),I=v.match(g);if(I&&I[1])return I[1]}return""},L=function(A){if(k(A)){var g=new RegExp("".concat(A,"\\/(\\d+)")),I=v.match(g);if(I&&I[1])return parseFloat(I[1])}return NaN},m=/AppleWebKit\/([\d.]+)/i.exec(v);m&&parseFloat(m[1]);var H=k("iPad"),U="undefined"!=typeof navigator&&navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&k("Macintosh"),K=k("iPhone")&&!H,J=k("iPod"),Z=K||H||J||U,T=k("Android");!function(){if(T){var A=v.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(A){var g=A[1]&&parseFloat(A[1]),I=A[2]&&parseFloat(A[2]);if(g&&I)return parseFloat("".concat(A[1],".").concat(A[2]));if(g)return g}}}();T&&k("webkit");var j=k("Firefox"),x=S("Firefox");L("Firefox");var P=k("Edge"),W=S("Edge"),O=k("Edg"),V=S("Edg");L("Edg");var X=k("SogouMobileBrowser"),z=S("SogouMobileBrowser"),q=k("MetaSr\\s"),$=S("MetaSr\\s"),_=k("TBS"),AA=S("TBS"),gA=k("XWEB"),IA=S("XWEB");k("MSIE\\s8\\.0");var tA=k("MSIE\\/\\d+");!function(){if(tA){var A=/MSIE\s(\d+)\.\d/.exec(v),g=A&&parseFloat(A[1]);return!g&&/Trident\/7.0/i.test(v)&&/rv:11.0/.test(v)&&(g=11),g}}();var CA=k("(micromessenger|webbrowser)"),BA=S("MicroMessenger"),rA=!_&&k("MQQBrowser")&&k("COVC"),QA=!_&&k("MQQBrowser")&&!k("COVC"),eA=QA||rA?S("MQQBrowser"):"",nA=!_&&k(" QQBrowser"),oA=S(" QQBrowser"),EA=!_&&k("QQBrowserLite"),iA=S("QQBrowserLite"),aA=!_&&k("MQBHD"),sA=S("MQBHD");k("Windows"),!Z&&k("MAC OS X"),!T&&k("Linux"),k("CrOS"),k("MicroMessenger"),k("UCBrowser"),k("Electron");var cA=k("MiuiBrowser"),uA=S("MiuiBrowser"),hA=k("HuaweiBrowser");k("Huawei")||k("HUAWEI"),k("Honor")||k("HONOR");var yA=S("HuaweiBrowser"),lA=k("SamsungBrowser"),fA=S("SamsungBrowser"),DA=k("HeyTapBrowser"),wA=S("HeyTapBrowser"),dA=k("VivoBrowser"),GA=S("VivoBrowser");k("OpenHarmony"),S("OpenHarmony");var NA=k("Chrome"),pA=!P&&!q&&!X&&!_&&!gA&&!O&&!nA&&!cA&&!hA&&!lA&&!DA&&!dA&&NA;k("HeadlessChrome");var MA=L("Chrome"),FA=S("Chrome"),RA=!NA&&!QA&&!rA&&!EA&&!aA&&k("Safari"),YA=S("Version"),bA=function(){if(U)return YA;if(Z){var A=v.match(/OS (\d+)_(\d+)/i);if(A&&A[1]){var g=A[1];return A[2]&&(g+=".".concat(A[2])),g}}return""}();Number(bA.split(".")[0]),Number(bA.split(".")[0]),k("CriOS"),function(){var A=new Map([[j,["Firefox",x]],[O,["Edg",V]],[pA,["Chrome",FA]],[RA,["Safari",YA]],[_,["TBS",AA]],[gA,["XWEB",IA]],[CA&&K,["WeChat",BA]],[nA,["QQ(Win)",oA]],[QA,["QQ(Mobile)",eA]],[rA,["QQ(Mobile X5)",eA]],[EA,["QQ(Mac)",iA]],[aA,["QQ(iPad)",sA]],[cA,["MI",uA]],[hA,["HW",yA]],[lA,["Samsung",fA]],[DA,["OPPO",wA]],[dA,["VIVO",GA]],[P,["EDGE",W]],[X,["SogouMobile",z]],[q,["Sogou",$]]]),g="unknown",I="unknown";if(A.has(!0)){var t=i(A.get(!0),2);g=t[0],I=t[1]}}();var vA=1e-6,kA="undefined"!=typeof Float32Array?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var A=0,g=arguments.length;g--;)A+=arguments[g]*arguments[g];return Math.sqrt(A)});var SA={};function LA(){var A=new kA(16);return kA!=Float32Array&&(A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=0,A[12]=0,A[13]=0,A[14]=0),A[0]=1,A[5]=1,A[10]=1,A[15]=1,A}function mA(A){var g=new kA(16);return g[0]=A[0],g[1]=A[1],g[2]=A[2],g[3]=A[3],g[4]=A[4],g[5]=A[5],g[6]=A[6],g[7]=A[7],g[8]=A[8],g[9]=A[9],g[10]=A[10],g[11]=A[11],g[12]=A[12],g[13]=A[13],g[14]=A[14],g[15]=A[15],g}function HA(A,g){return A[0]=g[0],A[1]=g[1],A[2]=g[2],A[3]=g[3],A[4]=g[4],A[5]=g[5],A[6]=g[6],A[7]=g[7],A[8]=g[8],A[9]=g[9],A[10]=g[10],A[11]=g[11],A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15],A}function UA(A,g,I,t,C,B,r,Q,e,n,o,E,i,a,s,c){var u=new kA(16);return u[0]=A,u[1]=g,u[2]=I,u[3]=t,u[4]=C,u[5]=B,u[6]=r,u[7]=Q,u[8]=e,u[9]=n,u[10]=o,u[11]=E,u[12]=i,u[13]=a,u[14]=s,u[15]=c,u}function KA(A,g,I,t,C,B,r,Q,e,n,o,E,i,a,s,c,u){return A[0]=g,A[1]=I,A[2]=t,A[3]=C,A[4]=B,A[5]=r,A[6]=Q,A[7]=e,A[8]=n,A[9]=o,A[10]=E,A[11]=i,A[12]=a,A[13]=s,A[14]=c,A[15]=u,A}function JA(A){return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function ZA(A,g){if(A===g){var I=g[1],t=g[2],C=g[3],B=g[6],r=g[7],Q=g[11];A[1]=g[4],A[2]=g[8],A[3]=g[12],A[4]=I,A[6]=g[9],A[7]=g[13],A[8]=t,A[9]=B,A[11]=g[14],A[12]=C,A[13]=r,A[14]=Q}else A[0]=g[0],A[1]=g[4],A[2]=g[8],A[3]=g[12],A[4]=g[1],A[5]=g[5],A[6]=g[9],A[7]=g[13],A[8]=g[2],A[9]=g[6],A[10]=g[10],A[11]=g[14],A[12]=g[3],A[13]=g[7],A[14]=g[11],A[15]=g[15];return A}function TA(A,g){var I=g[0],t=g[1],C=g[2],B=g[3],r=g[4],Q=g[5],e=g[6],n=g[7],o=g[8],E=g[9],i=g[10],a=g[11],s=g[12],c=g[13],u=g[14],h=g[15],y=I*Q-t*r,l=I*e-C*r,f=I*n-B*r,D=t*e-C*Q,w=t*n-B*Q,d=C*n-B*e,G=o*c-E*s,N=o*u-i*s,p=o*h-a*s,M=E*u-i*c,F=E*h-a*c,R=i*h-a*u,Y=y*R-l*F+f*M+D*p-w*N+d*G;return Y?(Y=1/Y,A[0]=(Q*R-e*F+n*M)*Y,A[1]=(C*F-t*R-B*M)*Y,A[2]=(c*d-u*w+h*D)*Y,A[3]=(i*w-E*d-a*D)*Y,A[4]=(e*p-r*R-n*N)*Y,A[5]=(I*R-C*p+B*N)*Y,A[6]=(u*f-s*d-h*l)*Y,A[7]=(o*d-i*f+a*l)*Y,A[8]=(r*F-Q*p+n*G)*Y,A[9]=(t*p-I*F-B*G)*Y,A[10]=(s*w-c*f+h*y)*Y,A[11]=(E*f-o*w-a*y)*Y,A[12]=(Q*N-r*M-e*G)*Y,A[13]=(I*M-t*N+C*G)*Y,A[14]=(c*l-s*D-u*y)*Y,A[15]=(o*D-E*l+i*y)*Y,A):null}function jA(A,g){var I=g[0],t=g[1],C=g[2],B=g[3],r=g[4],Q=g[5],e=g[6],n=g[7],o=g[8],E=g[9],i=g[10],a=g[11],s=g[12],c=g[13],u=g[14],h=g[15];return A[0]=Q*(i*h-a*u)-E*(e*h-n*u)+c*(e*a-n*i),A[1]=-(t*(i*h-a*u)-E*(C*h-B*u)+c*(C*a-B*i)),A[2]=t*(e*h-n*u)-Q*(C*h-B*u)+c*(C*n-B*e),A[3]=-(t*(e*a-n*i)-Q*(C*a-B*i)+E*(C*n-B*e)),A[4]=-(r*(i*h-a*u)-o*(e*h-n*u)+s*(e*a-n*i)),A[5]=I*(i*h-a*u)-o*(C*h-B*u)+s*(C*a-B*i),A[6]=-(I*(e*h-n*u)-r*(C*h-B*u)+s*(C*n-B*e)),A[7]=I*(e*a-n*i)-r*(C*a-B*i)+o*(C*n-B*e),A[8]=r*(E*h-a*c)-o*(Q*h-n*c)+s*(Q*a-n*E),A[9]=-(I*(E*h-a*c)-o*(t*h-B*c)+s*(t*a-B*E)),A[10]=I*(Q*h-n*c)-r*(t*h-B*c)+s*(t*n-B*Q),A[11]=-(I*(Q*a-n*E)-r*(t*a-B*E)+o*(t*n-B*Q)),A[12]=-(r*(E*u-i*c)-o*(Q*u-e*c)+s*(Q*i-e*E)),A[13]=I*(E*u-i*c)-o*(t*u-C*c)+s*(t*i-C*E),A[14]=-(I*(Q*u-e*c)-r*(t*u-C*c)+s*(t*e-C*Q)),A[15]=I*(Q*i-e*E)-r*(t*i-C*E)+o*(t*e-C*Q),A}function xA(A){var g=A[0],I=A[1],t=A[2],C=A[3],B=A[4],r=A[5],Q=A[6],e=A[7],n=A[8],o=A[9],E=A[10],i=A[11],a=A[12],s=A[13],c=A[14],u=A[15];return(g*r-I*B)*(E*u-i*c)-(g*Q-t*B)*(o*u-i*s)+(g*e-C*B)*(o*c-E*s)+(I*Q-t*r)*(n*u-i*a)-(I*e-C*r)*(n*c-E*a)+(t*e-C*Q)*(n*s-o*a)}function PA(A,g,I){var t=g[0],C=g[1],B=g[2],r=g[3],Q=g[4],e=g[5],n=g[6],o=g[7],E=g[8],i=g[9],a=g[10],s=g[11],c=g[12],u=g[13],h=g[14],y=g[15],l=I[0],f=I[1],D=I[2],w=I[3];return A[0]=l*t+f*Q+D*E+w*c,A[1]=l*C+f*e+D*i+w*u,A[2]=l*B+f*n+D*a+w*h,A[3]=l*r+f*o+D*s+w*y,l=I[4],f=I[5],D=I[6],w=I[7],A[4]=l*t+f*Q+D*E+w*c,A[5]=l*C+f*e+D*i+w*u,A[6]=l*B+f*n+D*a+w*h,A[7]=l*r+f*o+D*s+w*y,l=I[8],f=I[9],D=I[10],w=I[11],A[8]=l*t+f*Q+D*E+w*c,A[9]=l*C+f*e+D*i+w*u,A[10]=l*B+f*n+D*a+w*h,A[11]=l*r+f*o+D*s+w*y,l=I[12],f=I[13],D=I[14],w=I[15],A[12]=l*t+f*Q+D*E+w*c,A[13]=l*C+f*e+D*i+w*u,A[14]=l*B+f*n+D*a+w*h,A[15]=l*r+f*o+D*s+w*y,A}function WA(A,g,I){var t,C,B,r,Q,e,n,o,E,i,a,s,c=I[0],u=I[1],h=I[2];return g===A?(A[12]=g[0]*c+g[4]*u+g[8]*h+g[12],A[13]=g[1]*c+g[5]*u+g[9]*h+g[13],A[14]=g[2]*c+g[6]*u+g[10]*h+g[14],A[15]=g[3]*c+g[7]*u+g[11]*h+g[15]):(t=g[0],C=g[1],B=g[2],r=g[3],Q=g[4],e=g[5],n=g[6],o=g[7],E=g[8],i=g[9],a=g[10],s=g[11],A[0]=t,A[1]=C,A[2]=B,A[3]=r,A[4]=Q,A[5]=e,A[6]=n,A[7]=o,A[8]=E,A[9]=i,A[10]=a,A[11]=s,A[12]=t*c+Q*u+E*h+g[12],A[13]=C*c+e*u+i*h+g[13],A[14]=B*c+n*u+a*h+g[14],A[15]=r*c+o*u+s*h+g[15]),A}function OA(A,g,I){var t=I[0],C=I[1],B=I[2];return A[0]=g[0]*t,A[1]=g[1]*t,A[2]=g[2]*t,A[3]=g[3]*t,A[4]=g[4]*C,A[5]=g[5]*C,A[6]=g[6]*C,A[7]=g[7]*C,A[8]=g[8]*B,A[9]=g[9]*B,A[10]=g[10]*B,A[11]=g[11]*B,A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15],A}function VA(A,g,I,t){var C,B,r,Q,e,n,o,E,i,a,s,c,u,h,y,l,f,D,w,d,G,N,p,M,F=t[0],R=t[1],Y=t[2],b=Math.hypot(F,R,Y);return b<vA?null:(F*=b=1/b,R*=b,Y*=b,C=Math.sin(I),r=1-(B=Math.cos(I)),Q=g[0],e=g[1],n=g[2],o=g[3],E=g[4],i=g[5],a=g[6],s=g[7],c=g[8],u=g[9],h=g[10],y=g[11],l=F*F*r+B,f=R*F*r+Y*C,D=Y*F*r-R*C,w=F*R*r-Y*C,d=R*R*r+B,G=Y*R*r+F*C,N=F*Y*r+R*C,p=R*Y*r-F*C,M=Y*Y*r+B,A[0]=Q*l+E*f+c*D,A[1]=e*l+i*f+u*D,A[2]=n*l+a*f+h*D,A[3]=o*l+s*f+y*D,A[4]=Q*w+E*d+c*G,A[5]=e*w+i*d+u*G,A[6]=n*w+a*d+h*G,A[7]=o*w+s*d+y*G,A[8]=Q*N+E*p+c*M,A[9]=e*N+i*p+u*M,A[10]=n*N+a*p+h*M,A[11]=o*N+s*p+y*M,g!==A&&(A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15]),A)}function XA(A,g,I){var t=Math.sin(I),C=Math.cos(I),B=g[4],r=g[5],Q=g[6],e=g[7],n=g[8],o=g[9],E=g[10],i=g[11];return g!==A&&(A[0]=g[0],A[1]=g[1],A[2]=g[2],A[3]=g[3],A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15]),A[4]=B*C+n*t,A[5]=r*C+o*t,A[6]=Q*C+E*t,A[7]=e*C+i*t,A[8]=n*C-B*t,A[9]=o*C-r*t,A[10]=E*C-Q*t,A[11]=i*C-e*t,A}function zA(A,g,I){var t=Math.sin(I),C=Math.cos(I),B=g[0],r=g[1],Q=g[2],e=g[3],n=g[8],o=g[9],E=g[10],i=g[11];return g!==A&&(A[4]=g[4],A[5]=g[5],A[6]=g[6],A[7]=g[7],A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15]),A[0]=B*C-n*t,A[1]=r*C-o*t,A[2]=Q*C-E*t,A[3]=e*C-i*t,A[8]=B*t+n*C,A[9]=r*t+o*C,A[10]=Q*t+E*C,A[11]=e*t+i*C,A}function qA(A,g,I){var t=Math.sin(I),C=Math.cos(I),B=g[0],r=g[1],Q=g[2],e=g[3],n=g[4],o=g[5],E=g[6],i=g[7];return g!==A&&(A[8]=g[8],A[9]=g[9],A[10]=g[10],A[11]=g[11],A[12]=g[12],A[13]=g[13],A[14]=g[14],A[15]=g[15]),A[0]=B*C+n*t,A[1]=r*C+o*t,A[2]=Q*C+E*t,A[3]=e*C+i*t,A[4]=n*C-B*t,A[5]=o*C-r*t,A[6]=E*C-Q*t,A[7]=i*C-e*t,A}function $A(A,g){return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=g[0],A[13]=g[1],A[14]=g[2],A[15]=1,A}function _A(A,g){return A[0]=g[0],A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=g[1],A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=g[2],A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function Ag(A,g,I){var t,C,B,r=I[0],Q=I[1],e=I[2],n=Math.hypot(r,Q,e);return n<vA?null:(r*=n=1/n,Q*=n,e*=n,t=Math.sin(g),B=1-(C=Math.cos(g)),A[0]=r*r*B+C,A[1]=Q*r*B+e*t,A[2]=e*r*B-Q*t,A[3]=0,A[4]=r*Q*B-e*t,A[5]=Q*Q*B+C,A[6]=e*Q*B+r*t,A[7]=0,A[8]=r*e*B+Q*t,A[9]=Q*e*B-r*t,A[10]=e*e*B+C,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A)}function gg(A,g){var I=Math.sin(g),t=Math.cos(g);return A[0]=1,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=t,A[6]=I,A[7]=0,A[8]=0,A[9]=-I,A[10]=t,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function Ig(A,g){var I=Math.sin(g),t=Math.cos(g);return A[0]=t,A[1]=0,A[2]=-I,A[3]=0,A[4]=0,A[5]=1,A[6]=0,A[7]=0,A[8]=I,A[9]=0,A[10]=t,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function tg(A,g){var I=Math.sin(g),t=Math.cos(g);return A[0]=t,A[1]=I,A[2]=0,A[3]=0,A[4]=-I,A[5]=t,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=1,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function Cg(A,g,I){var t=g[0],C=g[1],B=g[2],r=g[3],Q=t+t,e=C+C,n=B+B,o=t*Q,E=t*e,i=t*n,a=C*e,s=C*n,c=B*n,u=r*Q,h=r*e,y=r*n;return A[0]=1-(a+c),A[1]=E+y,A[2]=i-h,A[3]=0,A[4]=E-y,A[5]=1-(o+c),A[6]=s+u,A[7]=0,A[8]=i+h,A[9]=s-u,A[10]=1-(o+a),A[11]=0,A[12]=I[0],A[13]=I[1],A[14]=I[2],A[15]=1,A}function Bg(A,g){var I=new kA(3),t=-g[0],C=-g[1],B=-g[2],r=g[3],Q=g[4],e=g[5],n=g[6],o=g[7],E=t*t+C*C+B*B+r*r;return E>0?(I[0]=2*(Q*r+o*t+e*B-n*C)/E,I[1]=2*(e*r+o*C+n*t-Q*B)/E,I[2]=2*(n*r+o*B+Q*C-e*t)/E):(I[0]=2*(Q*r+o*t+e*B-n*C),I[1]=2*(e*r+o*C+n*t-Q*B),I[2]=2*(n*r+o*B+Q*C-e*t)),Cg(A,g,I),A}function rg(A,g){return A[0]=g[12],A[1]=g[13],A[2]=g[14],A}function Qg(A,g){var I=g[0],t=g[1],C=g[2],B=g[4],r=g[5],Q=g[6],e=g[8],n=g[9],o=g[10];return A[0]=Math.hypot(I,t,C),A[1]=Math.hypot(B,r,Q),A[2]=Math.hypot(e,n,o),A}function eg(A,g){var I=new kA(3);Qg(I,g);var t=1/I[0],C=1/I[1],B=1/I[2],r=g[0]*t,Q=g[1]*C,e=g[2]*B,n=g[4]*t,o=g[5]*C,E=g[6]*B,i=g[8]*t,a=g[9]*C,s=g[10]*B,c=r+o+s,u=0;return c>0?(u=2*Math.sqrt(c+1),A[3]=.25*u,A[0]=(E-a)/u,A[1]=(i-e)/u,A[2]=(Q-n)/u):r>o&&r>s?(u=2*Math.sqrt(1+r-o-s),A[3]=(E-a)/u,A[0]=.25*u,A[1]=(Q+n)/u,A[2]=(i+e)/u):o>s?(u=2*Math.sqrt(1+o-r-s),A[3]=(i-e)/u,A[0]=(Q+n)/u,A[1]=.25*u,A[2]=(E+a)/u):(u=2*Math.sqrt(1+s-r-o),A[3]=(Q-n)/u,A[0]=(i+e)/u,A[1]=(E+a)/u,A[2]=.25*u),A}function ng(A,g,I,t){var C=g[0],B=g[1],r=g[2],Q=g[3],e=C+C,n=B+B,o=r+r,E=C*e,i=C*n,a=C*o,s=B*n,c=B*o,u=r*o,h=Q*e,y=Q*n,l=Q*o,f=t[0],D=t[1],w=t[2];return A[0]=(1-(s+u))*f,A[1]=(i+l)*f,A[2]=(a-y)*f,A[3]=0,A[4]=(i-l)*D,A[5]=(1-(E+u))*D,A[6]=(c+h)*D,A[7]=0,A[8]=(a+y)*w,A[9]=(c-h)*w,A[10]=(1-(E+s))*w,A[11]=0,A[12]=I[0],A[13]=I[1],A[14]=I[2],A[15]=1,A}function og(A,g,I,t,C){var B=g[0],r=g[1],Q=g[2],e=g[3],n=B+B,o=r+r,E=Q+Q,i=B*n,a=B*o,s=B*E,c=r*o,u=r*E,h=Q*E,y=e*n,l=e*o,f=e*E,D=t[0],w=t[1],d=t[2],G=C[0],N=C[1],p=C[2],M=(1-(c+h))*D,F=(a+f)*D,R=(s-l)*D,Y=(a-f)*w,b=(1-(i+h))*w,v=(u+y)*w,k=(s+l)*d,S=(u-y)*d,L=(1-(i+c))*d;return A[0]=M,A[1]=F,A[2]=R,A[3]=0,A[4]=Y,A[5]=b,A[6]=v,A[7]=0,A[8]=k,A[9]=S,A[10]=L,A[11]=0,A[12]=I[0]+G-(M*G+Y*N+k*p),A[13]=I[1]+N-(F*G+b*N+S*p),A[14]=I[2]+p-(R*G+v*N+L*p),A[15]=1,A}function Eg(A,g){var I=g[0],t=g[1],C=g[2],B=g[3],r=I+I,Q=t+t,e=C+C,n=I*r,o=t*r,E=t*Q,i=C*r,a=C*Q,s=C*e,c=B*r,u=B*Q,h=B*e;return A[0]=1-E-s,A[1]=o+h,A[2]=i-u,A[3]=0,A[4]=o-h,A[5]=1-n-s,A[6]=a+c,A[7]=0,A[8]=i+u,A[9]=a-c,A[10]=1-n-E,A[11]=0,A[12]=0,A[13]=0,A[14]=0,A[15]=1,A}function ig(A,g,I,t,C,B,r){var Q=1/(I-g),e=1/(C-t),n=1/(B-r);return A[0]=2*B*Q,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=2*B*e,A[6]=0,A[7]=0,A[8]=(I+g)*Q,A[9]=(C+t)*e,A[10]=(r+B)*n,A[11]=-1,A[12]=0,A[13]=0,A[14]=r*B*2*n,A[15]=0,A}function ag(A,g,I,t,C){var B,r=1/Math.tan(g/2);return A[0]=r/I,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=r,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=-1,A[12]=0,A[13]=0,A[15]=0,null!=C&&C!==1/0?(B=1/(t-C),A[10]=(C+t)*B,A[14]=2*C*t*B):(A[10]=-1,A[14]=-2*t),A}!function(A,g){for(var I in g)h(A,I,{get:g[I],enumerable:!0})}(SA,{add:function(){return Gg},adjoint:function(){return jA},clone:function(){return mA},copy:function(){return HA},create:function(){return LA},determinant:function(){return xA},equals:function(){return Rg},exactEquals:function(){return Fg},frob:function(){return dg},fromQuat:function(){return Eg},fromQuat2:function(){return Bg},fromRotation:function(){return Ag},fromRotationTranslation:function(){return Cg},fromRotationTranslationScale:function(){return ng},fromRotationTranslationScaleOrigin:function(){return og},fromScaling:function(){return _A},fromTranslation:function(){return $A},fromValues:function(){return UA},fromXRotation:function(){return gg},fromYRotation:function(){return Ig},fromZRotation:function(){return tg},frustum:function(){return ig},getRotation:function(){return eg},getScaling:function(){return Qg},getTranslation:function(){return rg},identity:function(){return JA},invert:function(){return TA},lookAt:function(){return fg},mul:function(){return Yg},multiply:function(){return PA},multiplyScalar:function(){return pg},multiplyScalarAndAdd:function(){return Mg},ortho:function(){return yg},orthoNO:function(){return hg},orthoZO:function(){return lg},perspective:function(){return sg},perspectiveFromFieldOfView:function(){return ug},perspectiveNO:function(){return ag},perspectiveZO:function(){return cg},rotate:function(){return VA},rotateX:function(){return XA},rotateY:function(){return zA},rotateZ:function(){return qA},scale:function(){return OA},set:function(){return KA},str:function(){return wg},sub:function(){return bg},subtract:function(){return Ng},targetTo:function(){return Dg},translate:function(){return WA},transpose:function(){return ZA}});var sg=ag;function cg(A,g,I,t,C){var B,r=1/Math.tan(g/2);return A[0]=r/I,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=r,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[11]=-1,A[12]=0,A[13]=0,A[15]=0,null!=C&&C!==1/0?(B=1/(t-C),A[10]=C*B,A[14]=C*t*B):(A[10]=-1,A[14]=-t),A}function ug(A,g,I,t){var C=Math.tan(g.upDegrees*Math.PI/180),B=Math.tan(g.downDegrees*Math.PI/180),r=Math.tan(g.leftDegrees*Math.PI/180),Q=Math.tan(g.rightDegrees*Math.PI/180),e=2/(r+Q),n=2/(C+B);return A[0]=e,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=n,A[6]=0,A[7]=0,A[8]=-(r-Q)*e*.5,A[9]=(C-B)*n*.5,A[10]=t/(I-t),A[11]=-1,A[12]=0,A[13]=0,A[14]=t*I/(I-t),A[15]=0,A}function hg(A,g,I,t,C,B,r){var Q=1/(g-I),e=1/(t-C),n=1/(B-r);return A[0]=-2*Q,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=-2*e,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=2*n,A[11]=0,A[12]=(g+I)*Q,A[13]=(C+t)*e,A[14]=(r+B)*n,A[15]=1,A}var yg=hg;function lg(A,g,I,t,C,B,r){var Q=1/(g-I),e=1/(t-C),n=1/(B-r);return A[0]=-2*Q,A[1]=0,A[2]=0,A[3]=0,A[4]=0,A[5]=-2*e,A[6]=0,A[7]=0,A[8]=0,A[9]=0,A[10]=n,A[11]=0,A[12]=(g+I)*Q,A[13]=(C+t)*e,A[14]=B*n,A[15]=1,A}function fg(A,g,I,t){var C,B,r,Q,e,n,o,E,i,a,s=g[0],c=g[1],u=g[2],h=t[0],y=t[1],l=t[2],f=I[0],D=I[1],w=I[2];return Math.abs(s-f)<vA&&Math.abs(c-D)<vA&&Math.abs(u-w)<vA?JA(A):(o=s-f,E=c-D,i=u-w,C=y*(i*=a=1/Math.hypot(o,E,i))-l*(E*=a),B=l*(o*=a)-h*i,r=h*E-y*o,(a=Math.hypot(C,B,r))?(C*=a=1/a,B*=a,r*=a):(C=0,B=0,r=0),Q=E*r-i*B,e=i*C-o*r,n=o*B-E*C,(a=Math.hypot(Q,e,n))?(Q*=a=1/a,e*=a,n*=a):(Q=0,e=0,n=0),A[0]=C,A[1]=Q,A[2]=o,A[3]=0,A[4]=B,A[5]=e,A[6]=E,A[7]=0,A[8]=r,A[9]=n,A[10]=i,A[11]=0,A[12]=-(C*s+B*c+r*u),A[13]=-(Q*s+e*c+n*u),A[14]=-(o*s+E*c+i*u),A[15]=1,A)}function Dg(A,g,I,t){var C=g[0],B=g[1],r=g[2],Q=t[0],e=t[1],n=t[2],o=C-I[0],E=B-I[1],i=r-I[2],a=o*o+E*E+i*i;a>0&&(o*=a=1/Math.sqrt(a),E*=a,i*=a);var s=e*i-n*E,c=n*o-Q*i,u=Q*E-e*o;return(a=s*s+c*c+u*u)>0&&(s*=a=1/Math.sqrt(a),c*=a,u*=a),A[0]=s,A[1]=c,A[2]=u,A[3]=0,A[4]=E*u-i*c,A[5]=i*s-o*u,A[6]=o*c-E*s,A[7]=0,A[8]=o,A[9]=E,A[10]=i,A[11]=0,A[12]=C,A[13]=B,A[14]=r,A[15]=1,A}function wg(A){return"mat4("+A[0]+", "+A[1]+", "+A[2]+", "+A[3]+", "+A[4]+", "+A[5]+", "+A[6]+", "+A[7]+", "+A[8]+", "+A[9]+", "+A[10]+", "+A[11]+", "+A[12]+", "+A[13]+", "+A[14]+", "+A[15]+")"}function dg(A){return Math.hypot(A[0],A[1],A[2],A[3],A[4],A[5],A[6],A[7],A[8],A[9],A[10],A[11],A[12],A[13],A[14],A[15])}function Gg(A,g,I){return A[0]=g[0]+I[0],A[1]=g[1]+I[1],A[2]=g[2]+I[2],A[3]=g[3]+I[3],A[4]=g[4]+I[4],A[5]=g[5]+I[5],A[6]=g[6]+I[6],A[7]=g[7]+I[7],A[8]=g[8]+I[8],A[9]=g[9]+I[9],A[10]=g[10]+I[10],A[11]=g[11]+I[11],A[12]=g[12]+I[12],A[13]=g[13]+I[13],A[14]=g[14]+I[14],A[15]=g[15]+I[15],A}function Ng(A,g,I){return A[0]=g[0]-I[0],A[1]=g[1]-I[1],A[2]=g[2]-I[2],A[3]=g[3]-I[3],A[4]=g[4]-I[4],A[5]=g[5]-I[5],A[6]=g[6]-I[6],A[7]=g[7]-I[7],A[8]=g[8]-I[8],A[9]=g[9]-I[9],A[10]=g[10]-I[10],A[11]=g[11]-I[11],A[12]=g[12]-I[12],A[13]=g[13]-I[13],A[14]=g[14]-I[14],A[15]=g[15]-I[15],A}function pg(A,g,I){return A[0]=g[0]*I,A[1]=g[1]*I,A[2]=g[2]*I,A[3]=g[3]*I,A[4]=g[4]*I,A[5]=g[5]*I,A[6]=g[6]*I,A[7]=g[7]*I,A[8]=g[8]*I,A[9]=g[9]*I,A[10]=g[10]*I,A[11]=g[11]*I,A[12]=g[12]*I,A[13]=g[13]*I,A[14]=g[14]*I,A[15]=g[15]*I,A}function Mg(A,g,I,t){return A[0]=g[0]+I[0]*t,A[1]=g[1]+I[1]*t,A[2]=g[2]+I[2]*t,A[3]=g[3]+I[3]*t,A[4]=g[4]+I[4]*t,A[5]=g[5]+I[5]*t,A[6]=g[6]+I[6]*t,A[7]=g[7]+I[7]*t,A[8]=g[8]+I[8]*t,A[9]=g[9]+I[9]*t,A[10]=g[10]+I[10]*t,A[11]=g[11]+I[11]*t,A[12]=g[12]+I[12]*t,A[13]=g[13]+I[13]*t,A[14]=g[14]+I[14]*t,A[15]=g[15]+I[15]*t,A}function Fg(A,g){return A[0]===g[0]&&A[1]===g[1]&&A[2]===g[2]&&A[3]===g[3]&&A[4]===g[4]&&A[5]===g[5]&&A[6]===g[6]&&A[7]===g[7]&&A[8]===g[8]&&A[9]===g[9]&&A[10]===g[10]&&A[11]===g[11]&&A[12]===g[12]&&A[13]===g[13]&&A[14]===g[14]&&A[15]===g[15]}function Rg(A,g){var I=A[0],t=A[1],C=A[2],B=A[3],r=A[4],Q=A[5],e=A[6],n=A[7],o=A[8],E=A[9],i=A[10],a=A[11],s=A[12],c=A[13],u=A[14],h=A[15],y=g[0],l=g[1],f=g[2],D=g[3],w=g[4],d=g[5],G=g[6],N=g[7],p=g[8],M=g[9],F=g[10],R=g[11],Y=g[12],b=g[13],v=g[14],k=g[15];return Math.abs(I-y)<=vA*Math.max(1,Math.abs(I),Math.abs(y))&&Math.abs(t-l)<=vA*Math.max(1,Math.abs(t),Math.abs(l))&&Math.abs(C-f)<=vA*Math.max(1,Math.abs(C),Math.abs(f))&&Math.abs(B-D)<=vA*Math.max(1,Math.abs(B),Math.abs(D))&&Math.abs(r-w)<=vA*Math.max(1,Math.abs(r),Math.abs(w))&&Math.abs(Q-d)<=vA*Math.max(1,Math.abs(Q),Math.abs(d))&&Math.abs(e-G)<=vA*Math.max(1,Math.abs(e),Math.abs(G))&&Math.abs(n-N)<=vA*Math.max(1,Math.abs(n),Math.abs(N))&&Math.abs(o-p)<=vA*Math.max(1,Math.abs(o),Math.abs(p))&&Math.abs(E-M)<=vA*Math.max(1,Math.abs(E),Math.abs(M))&&Math.abs(i-F)<=vA*Math.max(1,Math.abs(i),Math.abs(F))&&Math.abs(a-R)<=vA*Math.max(1,Math.abs(a),Math.abs(R))&&Math.abs(s-Y)<=vA*Math.max(1,Math.abs(s),Math.abs(Y))&&Math.abs(c-b)<=vA*Math.max(1,Math.abs(c),Math.abs(b))&&Math.abs(u-v)<=vA*Math.max(1,Math.abs(u),Math.abs(v))&&Math.abs(h-k)<=vA*Math.max(1,Math.abs(h),Math.abs(k))}var Yg=PA,bg=Ng,vg=570703,kg=0,Sg=function(){function A(g){C(this,A),this.core=g,N(this,"seq"),N(this,"_core"),N(this,"log"),N(this,"preLoadPromise"),N(this,"startResolve"),N(this,"startReject"),N(this,"mediaPipeSolutions"),N(this,"assetsPath"),N(this,"currentType"),N(this,"onAbort"),N(this,"isAborted",!1),kg+=1,this.seq=kg,this._core=g,this.log=g.log.createChild({id:"".concat(this.getAlias()).concat(kg)}),this.log.info("created"),g.assetsPath&&(this.preLoadPromise=this.preload(g.assetsPath))}return r(A,[{key:"preload",value:(Q=I(o().mark((function A(g){var I,t,C,B,r=this;return o().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(A.prev=0,this._core.room.videoManager.Wasm){A.next=5;break}return A.next=4,b();case 4:this._core.room.videoManager.Wasm=A.sent;case 5:return I=function(A){var g;r.core.kvStatManager.addEnum({key:vg,value:r.getKVTypeValue(!1,r.isAborted,"ABORT_IN_INFERENCE")}),r.isAborted=!0,r.log.error("mediaPipeSolutions abort",A),r.core.clearStarted(r,r.getGroup()),r.stop(),null==(g=r.onAbort)||g.call(r,A)},this._core.room.videoManager.initVirtualBackground(I,SA),A.next=9,this._core.initVisionTaskRegistry(g);case 9:A.next=15;break;case 11:throw A.prev=11,A.t0=A.catch(0),t=this._core.errorModule,C=t.RtcError,B=t.ErrorCode,new C({code:B.INVALID_OPERATION,message:"VirtualBackground preload error, please redeploy the assets of the npm package. detail: ".concat(A.t0)});case 15:case"end":return A.stop()}}),A,this,[[0,11]])}))),function(A){return Q.apply(this,arguments)})},{key:"getName",value:function(){return A.Name}},{key:"getAlias",value:function(){return"vb"}},{key:"getValidateRule",value:function(A){switch(A){case"start":return g=this._core,{name:"VirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:(I=G({},F),t={type:{required:!1,type:"string",values:["image","blur"]},src:{required:!1,type:"string"},blurLevel:{required:!1,type:"number",min:1,max:10},onAbort:{required:!1}},y(I,l(t))),validate:function(A,I,t,C){var B,r=g.errorModule,Q=r.RtcError,e=r.ErrorCode,n=r.ErrorCodeDictionary;if(A){var o=A.type,E=A.src,i=A.onAbort;if("image"===o&&!E)throw new Q({code:e.INVALID_PARAMETER,extraCode:n.INVALID_PARAMETER_REQUIRED,fnName:t,messageParams:{key:"src"}});if(i&&!g.utils.isFunction(i))throw new Q({code:e.INVALID_PARAMETER,extraCode:n.INVALID_PARAMETER_TYPE,fnName:t,messageParams:{key:"onAbort",value:s(i),rule:{type:"Function"}}});if(!(null==(B=g.room.videoManager.cameraTrack)?void 0:B.mediaTrack))throw new Q({code:e.INVALID_OPERATION,extraCode:n.INVALID_OPERATION_NEED_VIDEO,fnName:t})}}};case"update":return function(A){return{name:"UpdateVirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:{type:{required:!0,type:"string",values:["image","blur"]},src:{required:!1,type:"string"},blurLevel:{required:!1,type:"number",min:1,max:10}},validate:function(g,I,t,C){if(g){var B=A.errorModule,r=B.RtcError,Q=B.ErrorCode,e=B.ErrorCodeDictionary,n=g.type,o=g.src;if("image"===n&&!o)throw new r({code:Q.INVALID_PARAMETER,extraCode:e.INVALID_PARAMETER_REQUIRED,fnName:t,messageParams:{key:"src"}})}}}}(this._core);case"stop":return this._core,{name:"StopVirtualBackgroundOptions",required:!1}}var g,I,t}},{key:"getGroup",value:function(){return"vb"}},{key:"getKVTypeValue",value:function(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],g=arguments.length>1&&void 0!==arguments[1]&&arguments[1],I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"NONE",t=0;switch(this.currentType){case"blur":t|=0;break;case"image":t|=1;break;case"green":t|=2}switch(A&&(t|=256),g&&(t|=512),I){case"ABORT_IN_INFERENCE":t|=4096;break;case"ABORT_IN_VIDEO_MANAGER":t|=8192;break;case"OTHER":t|=61440}return t}},{key:"start",value:(B=I(o().mark((function A(g){var I,t,C,B,r,Q,e,n,E,i,a,s,c,u=this;return o().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return I=g.type,t=void 0===I?"blur":I,C=g.src,B=g.blurLevel,r=void 0===B?3:B,Q=g.onAbort,this.currentType=t,this.onAbort=Q,A.next=5,p({sdkAppId:g.sdkAppId,userId:g.userId,userSig:g.userSig,core:this._core});case 5:if(e=A.sent,n=e.auth,E=this._core.errorModule,i=E.RtcError,a=E.ErrorCodeDictionary,s=E.ErrorCode,n){A.next=11;break}throw c=this._core.utils.isOverseaSdkAppId(g.sdkAppId)?"https://trtc.io/document/56025":"https://cloud.tencent.com/document/product/647/85386",new i({code:a.NEED_TO_BUY,messageParams:{value:"Virtual Background",url:c}});case 11:if(this.preLoadPromise){A.next=15;break}if(this._core.assetsPath){A.next=14;break}throw new i({code:s.INVALID_PARAMETER,message:"you need to deploy the assets of the npm package and set assetsPath param in TRTC.create()"});case 14:this.preLoadPromise=this.preload(this._core.assetsPath);case 15:return A.next=17,this.preLoadPromise;case 17:return A.abrupt("return",this.core.room.videoManager.setVirtualBackground({type:t,imageUrl:C,blurLevel:r,enableFaceCentering:g.enableFaceCentering,onAbort:function(A){var g;u.core.kvStatManager.addEnum({key:vg,value:u.getKVTypeValue(!0,u.isAborted,"ABORT_IN_VIDEO_MANAGER")}),u.isAborted=!0,u.core.clearStarted(u,u.getGroup()),u.stop(),delete u.preLoadPromise,null==(g=u.onAbort)||g.call(u,A)}}).then((function(){u.core.kvStatManager.addEnum({key:vg,value:u.getKVTypeValue(!1,u.isAborted,"NONE")})})).catch((function(){u.core.kvStatManager.addEnum({key:vg,value:u.getKVTypeValue(!0,u.isAborted,"OTHER")})})));case 18:case"end":return A.stop()}}),A,this)}))),function(A){return B.apply(this,arguments)})},{key:"update",value:(t=I(o().mark((function A(g){var I,t,C=this;return o().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return I=g.type,t=g.src,I!==this.currentType&&(this.currentType=I),A.abrupt("return",this.core.room.videoManager.setVirtualBackground({type:I,imageUrl:t,blurLevel:g.blurLevel,enableFaceCentering:g.enableFaceCentering}).then((function(){C.core.kvStatManager.addEnum({key:vg,value:C.getKVTypeValue(!1,!1,"NONE")})})).catch((function(){C.core.kvStatManager.addEnum({key:vg,value:C.getKVTypeValue(!0,!1,"OTHER")})})));case 3:case"end":return A.stop()}}),A,this)}))),function(A){return t.apply(this,arguments)})},{key:"stop",value:(g=I(o().mark((function A(){return o().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return A.abrupt("return",this.core.room.videoManager.setVirtualBackground());case 1:case"end":return A.stop()}}),A,this)}))),function(){return g.apply(this,arguments)})}],[{key:"isSupported",value:function(){if(MA<90)return!1;var A=document.createElement("canvas").getContext("webgl2");return!!(A&&A instanceof WebGL2RenderingContext)}}]);var g,t,B,Q}();return N(Sg,"Name","VirtualBackground"),Sg}));
