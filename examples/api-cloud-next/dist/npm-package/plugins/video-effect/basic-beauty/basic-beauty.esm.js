var __defProp=Object.defineProperty,__defNormalProp=(A,g,I)=>g in A?__defProp(A,g,{enumerable:!0,configurable:!0,writable:!0,value:I}):A[g]=I,__publicField=(A,g,I)=>__defNormalProp(A,"symbol"!=typeof g?g+"":g,I);function startValidateRule(A){return{name:"BasicBeautyOptions",type:"object",required:!0,allowEmpty:!1,properties:{beauty:{required:!1,type:"number"},brightness:{required:!1,type:"number"},ruddy:{required:!1,type:"number"}},validate(g,I,C,B){const{RtcError:Q,ErrorCode:E,ErrorCodeDictionary:i}=A.errorModule;if(A.utils.isOverseaSdkAppId(g.sdkAppId))throw new Q({code:E.INVALID_OPERATION,extraCode:i.INVALID_OPERATION,message:"This feature is not yet available in your country or region. If you have any questions, you can go to the community for consultation: https://zhiliao.qq.com/s/cWSPGIIM62CC/c3TPGIIM62CQ"})}}}function stopValidateRule(A){return{name:"StopBasicBeautyOptions",required:!1}}var Module=(()=>{var A="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(g={}){var I,C,B=g;B.ready=new Promise(((A,g)=>{I=A,C=g}));var Q,E=Object.assign({},B),i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src),A&&(i=A),i=0!==i.indexOf("blob:")?i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var e,t,o=B.print||console.log.bind(console),r=B.printErr||console.error.bind(console);function s(A){if(p(A))return function(A){for(var g=atob(A),I=new Uint8Array(g.length),C=0;C<g.length;++C)I[C]=g.charCodeAt(C);return I}(A.slice(L.length))}Object.assign(B,E),E=null,B.arguments&&B.arguments,B.thisProgram&&B.thisProgram,B.quit&&B.quit,B.wasmBinary&&(e=B.wasmBinary),"object"!=typeof WebAssembly&&R("no native wasm support detected");var a,n,D,c,y,h,G,w,N=!1;var l=[],d=[],F=[];var u=0,Y=null,M=null;function R(A){B.onAbort&&B.onAbort(A),r(A="Aborted("+A+")"),N=!0,A+=". Build with -sASSERTIONS for more info.";var g=new WebAssembly.RuntimeError(A);throw C(g),g}var k,S,L="data:application/octet-stream;base64,",p=A=>A.startsWith(L);function H(A){return Promise.resolve().then((()=>function(A){if(A==k&&e)return new Uint8Array(e);var g=s(A);if(g)return g;if(Q)return Q(A);throw"both async and sync fetching of the wasm failed"}(A)))}function f(A,g,I,C){return function(A,g,I){return H(A).then((A=>WebAssembly.instantiate(A,g))).then((A=>A)).then(I,(A=>{r(`failed to asynchronously prepare wasm: ${A}`),R(A)}))}(g,I,C)}p(k="data:application/octet-stream;base64,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")||(S=k,k=B.locateFile?B.locateFile(S,i):i+S);var U=A=>{for(;A.length>0;)A.shift()(B)};B.noExitRuntime;function K(A){this.excPtr=A,this.ptr=A-24,this.set_type=function(A){h[this.ptr+4>>2]=A},this.get_type=function(){return h[this.ptr+4>>2]},this.set_destructor=function(A){h[this.ptr+8>>2]=A},this.get_destructor=function(){return h[this.ptr+8>>2]},this.set_caught=function(A){A=A?1:0,a[this.ptr+12|0]=A},this.get_caught=function(){return 0!=a[this.ptr+12|0]},this.set_rethrown=function(A){A=A?1:0,a[this.ptr+13|0]=A},this.get_rethrown=function(){return 0!=a[this.ptr+13|0]},this.init=function(A,g){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(g)},this.set_adjusted_ptr=function(A){h[this.ptr+16>>2]=A},this.get_adjusted_ptr=function(){return h[this.ptr+16>>2]},this.get_exception_ptr=function(){if(ag(this.get_type()))return h[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}var J,b,m,Z=A=>{for(var g="",I=A;n[I];)g+=J[n[I++]];return g},v={},j={},W={},T=A=>{throw new b(A)},P=A=>{throw new m(A)},x=(A,g,I)=>{function C(g){var C=I(g);C.length!==A.length&&P("Mismatched type converter count");for(var B=0;B<A.length;++B)X(A[B],C[B])}A.forEach((function(A){W[A]=g}));var B=new Array(g.length),Q=[],E=0;g.forEach(((A,g)=>{j.hasOwnProperty(A)?B[g]=j[A]:(Q.push(A),v.hasOwnProperty(A)||(v[A]=[]),v[A].push((()=>{B[g]=j[A],++E===Q.length&&C(B)})))})),0===Q.length&&C(B)};function X(A,g,I={}){if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,g,I={}){var C=g.name;if(A||T(`type "${C}" must have a positive integer typeid pointer`),j.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;T(`Cannot register type '${C}' twice`)}if(j[A]=g,delete W[A],v.hasOwnProperty(A)){var B=v[A];delete v[A],B.forEach((A=>A()))}}(A,g,I)}var z,V=A=>{T(A.$$.ptrType.registeredClass.name+" instance already deleted")},q=!1,O=A=>{},$=A=>{A.count.value-=1,0===A.count.value&&(A=>{A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)})(A)},_=(A,g,I)=>{if(g===I)return A;if(void 0===I.baseClass)return null;var C=_(A,g,I.baseClass);return null===C?null:I.downcast(C)},AA={},gA=()=>Object.keys(EA).length,IA=()=>{var A=[];for(var g in EA)EA.hasOwnProperty(g)&&A.push(EA[g]);return A},CA=[],BA=()=>{for(;CA.length;){var A=CA.pop();A.$$.deleteScheduled=!1,A.delete()}},QA=A=>{z=A,CA.length&&z&&z(BA)},EA={},iA=(A,g)=>(g=((A,g)=>{for(void 0===g&&T("ptr should not be undefined");A.baseClass;)g=A.upcast(g),A=A.baseClass;return g})(A,g),EA[g]),eA=(A,g)=>(g.ptrType&&g.ptr||P("makeClassHandle requires ptr and ptrType"),!!g.smartPtrType!==!!g.smartPtr&&P("Both smartPtrType and smartPtr must be specified"),g.count={value:1},oA(Object.create(A,{$$:{value:g}})));function tA(A){var g=this.getPointee(A);if(!g)return this.destructor(A),null;var I=iA(this.registeredClass,g);if(void 0!==I){if(0===I.$$.count.value)return I.$$.ptr=g,I.$$.smartPtr=A,I.clone();var C=I.clone();return this.destructor(A),C}function B(){return this.isSmartPointer?eA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:g,smartPtrType:this,smartPtr:A}):eA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var Q,E=this.registeredClass.getActualType(g),i=AA[E];if(!i)return B.call(this);Q=this.isConst?i.constPointerType:i.pointerType;var e=_(g,this.registeredClass,Q.registeredClass);return null===e?B.call(this):this.isSmartPointer?eA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e,smartPtrType:this,smartPtr:A}):eA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e})}var oA=A=>"undefined"==typeof FinalizationRegistry?(oA=A=>A,A):(q=new FinalizationRegistry((A=>{$(A.$$)})),O=A=>q.unregister(A),(oA=A=>{var g=A.$$;if(!!g.smartPtr){var I={$$:g};q.register(A,I,A)}return A})(A));function rA(){}var sA=(A,g)=>Object.defineProperty(g,"name",{value:A}),aA=(A,g,I)=>{if(void 0===A[g].overloadTable){var C=A[g];A[g]=function(){return A[g].overloadTable.hasOwnProperty(arguments.length)||T(`Function '${I}' called with an invalid number of arguments (${arguments.length}) - expects one of (${A[g].overloadTable})!`),A[g].overloadTable[arguments.length].apply(this,arguments)},A[g].overloadTable=[],A[g].overloadTable[C.argCount]=C}};function nA(A,g,I,C,B,Q,E,i){this.name=A,this.constructor=g,this.instancePrototype=I,this.rawDestructor=C,this.baseClass=B,this.getActualType=Q,this.upcast=E,this.downcast=i,this.pureVirtualFunctions=[]}var DA=(A,g,I)=>{for(;g!==I;)g.upcast||T(`Expected null or instance of ${I.name}, got an instance of ${g.name}`),A=g.upcast(A),g=g.baseClass;return A};function cA(A,g){if(null===g)return this.isReference&&T(`null is not a valid ${this.name}`),0;g.$$||T(`Cannot pass "${jA(g)}" as a ${this.name}`),g.$$.ptr||T(`Cannot pass deleted object as a pointer of type ${this.name}`);var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function yA(A,g){var I;if(null===g)return this.isReference&&T(`null is not a valid ${this.name}`),this.isSmartPointer?(I=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,I),I):0;g.$$||T(`Cannot pass "${jA(g)}" as a ${this.name}`),g.$$.ptr||T(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&g.$$.ptrType.isConst&&T(`Cannot convert argument of type ${g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name} to parameter type ${this.name}`);var C=g.$$.ptrType.registeredClass;if(I=DA(g.$$.ptr,C,this.registeredClass),this.isSmartPointer)switch(void 0===g.$$.smartPtr&&T("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:g.$$.smartPtrType===this?I=g.$$.smartPtr:T(`Cannot convert argument of type ${g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:I=g.$$.smartPtr;break;case 2:if(g.$$.smartPtrType===this)I=g.$$.smartPtr;else{var B=g.clone();I=this.rawShare(I,JA.toHandle((()=>B.delete()))),null!==A&&A.push(this.rawDestructor,I)}break;default:T("Unsupporting sharing policy")}return I}function hA(A,g){if(null===g)return this.isReference&&T(`null is not a valid ${this.name}`),0;g.$$||T(`Cannot pass "${jA(g)}" as a ${this.name}`),g.$$.ptr||T(`Cannot pass deleted object as a pointer of type ${this.name}`),g.$$.ptrType.isConst&&T(`Cannot convert argument of type ${g.$$.ptrType.name} to parameter type ${this.name}`);var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function GA(A){return this.fromWireType(h[A>>2])}function wA(A,g,I,C,B,Q,E,i,e,t,o){this.name=A,this.registeredClass=g,this.isReference=I,this.isConst=C,this.isSmartPointer=B,this.pointeeType=Q,this.sharingPolicy=E,this.rawGetPointee=i,this.rawConstructor=e,this.rawShare=t,this.rawDestructor=o,B||void 0!==g.baseClass?this.toWireType=yA:C?(this.toWireType=cA,this.destructorFunction=null):(this.toWireType=hA,this.destructorFunction=null)}var NA,lA,dA=[],FA=A=>{var g=dA[A];return g||(A>=dA.length&&(dA.length=A+1),dA[A]=g=NA.get(A)),g},uA=(A,g,I)=>A.includes("j")?((A,g,I)=>{var C=B["dynCall_"+A];return I&&I.length?C.apply(null,[g].concat(I)):C.call(null,g)})(A,g,I):FA(g).apply(null,I),YA=(A,g)=>{var I,C,B,Q=(A=Z(A)).includes("j")?(I=A,C=g,B=[],function(){return B.length=0,Object.assign(B,arguments),uA(I,C,B)}):FA(g);return"function"!=typeof Q&&T(`unknown function pointer with signature ${A}: ${g}`),Q},MA=A=>{var g=rg(A),I=Z(g);return sg(g),I},RA=(A,g)=>{var I=[],C={};throw g.forEach((function A(g){C[g]||j[g]||(W[g]?W[g].forEach(A):(I.push(g),C[g]=!0))})),new lA(`${A}: `+I.map(MA).join([", "]))},kA=(A,g)=>{for(var I=[],C=0;C<A;C++)I.push(h[g+4*C>>2]);return I},SA=A=>{for(;A.length;){var g=A.pop();A.pop()(g)}};function LA(A,g,I,C,B,Q){var E=g.length;E<2&&T("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==g[1]&&null!==I,e=!1,t=1;t<g.length;++t)if(null!==g[t]&&void 0===g[t].destructorFunction){e=!0;break}var o="void"!==g[0].name,r=E-2,s=new Array(r),a=[],n=[];return sA(A,(function(){var I;arguments.length!==r&&T(`function ${A} called with ${arguments.length} arguments, expected ${r}`),n.length=0,a.length=i?2:1,a[0]=B,i&&(I=g[1].toWireType(n,this),a[1]=I);for(var Q=0;Q<r;++Q)s[Q]=g[Q+2].toWireType(n,arguments[Q]),a.push(s[Q]);return function(A){if(e)SA(n);else for(var C=i?1:2;C<g.length;C++){var B=1===C?I:s[C-2];null!==g[C].destructorFunction&&g[C].destructorFunction(B)}if(o)return g[0].fromWireType(A)}(C.apply(null,a))}))}var pA=(A,g,I)=>(A instanceof Object||T(`${I} with invalid "this": ${A}`),A instanceof g.registeredClass.constructor||T(`${I} incompatible with "this" of type ${A.constructor.name}`),A.$$.ptr||T(`cannot call emscripten binding method ${I} on deleted object`),DA(A.$$.ptr,A.$$.ptrType.registeredClass,g.registeredClass));function HA(){this.allocated=[void 0],this.freelist=[]}var fA=new HA,UA=A=>{A>=fA.reserved&&0==--fA.get(A).refcount&&fA.free(A)},KA=()=>{for(var A=0,g=fA.reserved;g<fA.allocated.length;++g)void 0!==fA.allocated[g]&&++A;return A},JA={toValue:A=>(A||T("Cannot use deleted val. handle = "+A),fA.get(A).value),toHandle:A=>{switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return fA.allocate({refcount:1,value:A})}}};function bA(A){return this.fromWireType(y[A>>2])}var mA,ZA,vA,jA=A=>{if(null===A)return"null";var g=typeof A;return"object"===g||"array"===g||"function"===g?A.toString():""+A},WA=(A,g)=>{switch(g){case 4:return function(A){return this.fromWireType(G[A>>2])};case 8:return function(A){return this.fromWireType(w[A>>3])};default:throw new TypeError(`invalid float width (${g}): ${A}`)}},TA=(A,g,I)=>{switch(g){case 1:return I?A=>a[A|0]:A=>n[A|0];case 2:return I?A=>D[A>>1]:A=>c[A>>1];case 4:return I?A=>y[A>>2]:A=>h[A>>2];default:throw new TypeError(`invalid integer width (${g}): ${A}`)}},PA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,xA=(A,g,I)=>{for(var C=g+I,B=g;A[B]&&!(B>=C);)++B;if(B-g>16&&A.buffer&&PA)return PA.decode(A.subarray(g,B));for(var Q="";g<B;){var E=A[g++];if(128&E){var i=63&A[g++];if(192!=(224&E)){var e=63&A[g++];if((E=224==(240&E)?(15&E)<<12|i<<6|e:(7&E)<<18|i<<12|e<<6|63&A[g++])<65536)Q+=String.fromCharCode(E);else{var t=E-65536;Q+=String.fromCharCode(55296|t>>10,56320|1023&t)}}else Q+=String.fromCharCode((31&E)<<6|i)}else Q+=String.fromCharCode(E)}return Q},XA=(A,g)=>A?xA(n,A,g):"",zA="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,VA=(A,g)=>{for(var I=A,C=I>>1,B=C+g/2;!(C>=B)&&c[C];)++C;if((I=C<<1)-A>32&&zA)return zA.decode(n.subarray(A,I));for(var Q="",E=0;!(E>=g/2);++E){var i=D[A+2*E>>1];if(0==i)break;Q+=String.fromCharCode(i)}return Q},qA=(A,g,I)=>{if(void 0===I&&(I=2147483647),I<2)return 0;for(var C=g,B=(I-=2)<2*A.length?I/2:A.length,Q=0;Q<B;++Q){var E=A.charCodeAt(Q);D[g>>1]=E,g+=2}return D[g>>1]=0,g-C},OA=A=>2*A.length,$A=(A,g)=>{for(var I=0,C="";!(I>=g/4);){var B=y[A+4*I>>2];if(0==B)break;if(++I,B>=65536){var Q=B-65536;C+=String.fromCharCode(55296|Q>>10,56320|1023&Q)}else C+=String.fromCharCode(B)}return C},_A=(A,g,I)=>{if(void 0===I&&(I=2147483647),I<4)return 0;for(var C=g,B=C+I-4,Q=0;Q<A.length;++Q){var E=A.charCodeAt(Q);if(E>=55296&&E<=57343)E=65536+((1023&E)<<10)|1023&A.charCodeAt(++Q);if(y[g>>2]=E,(g+=4)+4>B)break}return y[g>>2]=0,g-C},Ag=A=>{for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C>=55296&&C<=57343&&++I,g+=4}return g},gg=(A,g)=>{var I=j[A];return void 0===I&&T(g+" has unknown type "+MA(A)),I},Ig=(A,g,I)=>{var C=[],B=A.toWireType(C,I);return C.length&&(h[g>>2]=JA.toHandle(C)),B},Cg={},Bg=[],Qg=Reflect.construct,Eg=[null,[],[]];(()=>{for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);J=A})(),b=B.BindingError=class extends Error{constructor(A){super(A),this.name="BindingError"}},m=B.InternalError=class extends Error{constructor(A){super(A),this.name="InternalError"}},Object.assign(rA.prototype,{isAliasOf(A){if(!(this instanceof rA))return!1;if(!(A instanceof rA))return!1;var g=this.$$.ptrType.registeredClass,I=this.$$.ptr;A.$$=A.$$;for(var C=A.$$.ptrType.registeredClass,B=A.$$.ptr;g.baseClass;)I=g.upcast(I),g=g.baseClass;for(;C.baseClass;)B=C.upcast(B),C=C.baseClass;return g===C&&I===B},clone(){if(this.$$.ptr||V(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,g=oA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete(){this.$$.ptr||V(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&T("Object already scheduled for deletion"),O(this),$(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||V(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&T("Object already scheduled for deletion"),CA.push(this),1===CA.length&&z&&z(BA),this.$$.deleteScheduled=!0,this}}),B.getInheritedInstanceCount=gA,B.getLiveInheritedInstances=IA,B.flushPendingDeletes=BA,B.setDelayFunction=QA,Object.assign(wA.prototype,{getPointee(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor(A){this.rawDestructor&&this.rawDestructor(A)},argPackAdvance:8,readValueFromPointer:GA,deleteObject(A){null!==A&&A.delete()},fromWireType:tA}),lA=B.UnboundTypeError=(mA=Error,(vA=sA(ZA="UnboundTypeError",(function(A){this.name=ZA,this.message=A;var g=new Error(A).stack;void 0!==g&&(this.stack=this.toString()+"\n"+g.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(mA.prototype),vA.prototype.constructor=vA,vA.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},vA),Object.assign(HA.prototype,{get(A){return this.allocated[A]},has(A){return void 0!==this.allocated[A]},allocate(A){var g=this.freelist.pop()||this.allocated.length;return this.allocated[g]=A,g},free(A){this.allocated[A]=void 0,this.freelist.push(A)}}),fA.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),fA.reserved=fA.allocated.length,B.count_emval_handles=KA;var ig,eg={w:(A,g,I)=>{throw new K(A).init(g,I),A},q:(A,g,I,C,B)=>{},u:(A,g,I,C)=>{X(A,{name:g=Z(g),fromWireType:function(A){return!!A},toWireType:function(A,g){return g?I:C},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(n[A])},destructorFunction:null})},y:(A,g,I,C,Q,E,i,e,t,o,r,s,a)=>{r=Z(r),E=YA(Q,E),e&&(e=YA(i,e)),o&&(o=YA(t,o)),a=YA(s,a);var n=(A=>{if(void 0===A)return"_unknown";var g=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return g>=48&&g<=57?`_${A}`:A})(r);((A,g,I)=>{B.hasOwnProperty(A)?((void 0===I||void 0!==B[A].overloadTable&&void 0!==B[A].overloadTable[I])&&T(`Cannot register public name '${A}' twice`),aA(B,A,A),B.hasOwnProperty(I)&&T(`Cannot register multiple overloads of a function with the same number of arguments (${I})!`),B[A].overloadTable[I]=g):(B[A]=g,void 0!==I&&(B[A].numArguments=I))})(n,(function(){RA(`Cannot construct ${r} due to unbound types`,[C])})),x([A,g,I],C?[C]:[],(function(g){var I,Q;g=g[0],Q=C?(I=g.registeredClass).instancePrototype:rA.prototype;var i=sA(r,(function(){if(Object.getPrototypeOf(this)!==t)throw new b("Use 'new' to construct "+r);if(void 0===s.constructor_body)throw new b(r+" has no accessible constructor");var A=s.constructor_body[arguments.length];if(void 0===A)throw new b(`Tried to invoke ctor of ${r} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(s.constructor_body).toString()}) parameters instead!`);return A.apply(this,arguments)})),t=Object.create(Q,{constructor:{value:i}});i.prototype=t;var s=new nA(r,i,t,a,I,E,e,o);s.baseClass&&(void 0===s.baseClass.__derivedClasses&&(s.baseClass.__derivedClasses=[]),s.baseClass.__derivedClasses.push(s));var D=new wA(r,s,!0,!1,!1),c=new wA(r+"*",s,!1,!1,!1),y=new wA(r+" const*",s,!1,!0,!1);return AA[A]={pointerType:c,constPointerType:y},((A,g,I)=>{B.hasOwnProperty(A)||P("Replacing nonexistant public symbol"),void 0!==B[A].overloadTable&&void 0!==I?B[A].overloadTable[I]=g:(B[A]=g,B[A].argCount=I)})(n,i),[D,c,y]}))},x:(A,g,I,C,B,Q)=>{var E=kA(g,I);B=YA(C,B),x([],[A],(function(A){var I=`constructor ${(A=A[0]).name}`;if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[g-1])throw new b(`Cannot register multiple constructors with identical number of parameters (${g-1}) for class '${A.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return A.registeredClass.constructor_body[g-1]=()=>{RA(`Cannot construct ${A.name} due to unbound types`,E)},x([],E,(C=>(C.splice(1,0,null),A.registeredClass.constructor_body[g-1]=LA(I,C,null,B,Q),[]))),[]}))},i:(A,g,I,C,B,Q,E,i,e)=>{var t=kA(I,C);g=(A=>{const g=(A=A.trim()).indexOf("(");return-1!==g?A.substr(0,g):A})(g=Z(g)),Q=YA(B,Q),x([],[A],(function(A){var C=`${(A=A[0]).name}.${g}`;function B(){RA(`Cannot call ${C} due to unbound types`,t)}g.startsWith("@@")&&(g=Symbol[g.substring(2)]),i&&A.registeredClass.pureVirtualFunctions.push(g);var e=A.registeredClass.instancePrototype,o=e[g];return void 0===o||void 0===o.overloadTable&&o.className!==A.name&&o.argCount===I-2?(B.argCount=I-2,B.className=A.name,e[g]=B):(aA(e,g,C),e[g].overloadTable[I-2]=B),x([],t,(function(B){var i=LA(C,B,A,Q,E);return void 0===e[g].overloadTable?(i.argCount=I-2,e[g]=i):e[g].overloadTable[I-2]=i,[]})),[]}))},k:(A,g,I,C,B,Q,E,i,e,t)=>{g=Z(g),B=YA(C,B),x([],[A],(function(A){var C=`${(A=A[0]).name}.${g}`,o={get(){RA(`Cannot access ${C} due to unbound types`,[I,E])},enumerable:!0,configurable:!0};return o.set=e?()=>RA(`Cannot access ${C} due to unbound types`,[I,E]):A=>T(C+" is a read-only property"),Object.defineProperty(A.registeredClass.instancePrototype,g,o),x([],e?[I,E]:[I],(function(I){var E=I[0],o={get(){var g=pA(this,A,C+" getter");return E.fromWireType(B(Q,g))},enumerable:!0};if(e){e=YA(i,e);var r=I[1];o.set=function(g){var I=pA(this,A,C+" setter"),B=[];e(t,I,r.toWireType(B,g)),SA(B)}}return Object.defineProperty(A.registeredClass.instancePrototype,g,o),[]})),[]}))},t:(A,g)=>{X(A,{name:g=Z(g),fromWireType:A=>{var g=JA.toValue(A);return UA(A),g},toWireType:(A,g)=>JA.toHandle(g),argPackAdvance:8,readValueFromPointer:bA,destructorFunction:null})},p:(A,g,I)=>{X(A,{name:g=Z(g),fromWireType:A=>A,toWireType:(A,g)=>g,argPackAdvance:8,readValueFromPointer:WA(g,I),destructorFunction:null})},g:(A,g,I,C,B)=>{g=Z(g),-1===B&&(B=4294967295);var Q=A=>A;if(0===C){var E=32-8*I;Q=A=>A<<E>>>E}var i=g.includes("unsigned");X(A,{name:g,fromWireType:Q,toWireType:i?function(A,g){return this.name,g>>>0}:function(A,g){return this.name,g},argPackAdvance:8,readValueFromPointer:TA(g,I,0!==C),destructorFunction:null})},a:(A,g,I)=>{var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][g];function B(A){var g=h[A>>2],I=h[A+4>>2];return new C(a.buffer,I,g)}X(A,{name:I=Z(I),fromWireType:B,argPackAdvance:8,readValueFromPointer:B},{ignoreDuplicateRegistrations:!0})},o:(A,g)=>{var I="std::string"===(g=Z(g));X(A,{name:g,fromWireType(A){var g,C=h[A>>2],B=A+4;if(I)for(var Q=B,E=0;E<=C;++E){var i=B+E;if(E==C||0==n[i]){var e=XA(Q,i-Q);void 0===g?g=e:(g+=String.fromCharCode(0),g+=e),Q=i+1}}else{var t=new Array(C);for(E=0;E<C;++E)t[E]=String.fromCharCode(n[B+E]);g=t.join("")}return sg(A),g},toWireType(A,g){var C;g instanceof ArrayBuffer&&(g=new Uint8Array(g));var B="string"==typeof g;B||g instanceof Uint8Array||g instanceof Uint8ClampedArray||g instanceof Int8Array||T("Cannot pass non-string to std::string"),C=I&&B?(A=>{for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C<=127?g++:C<=2047?g+=2:C>=55296&&C<=57343?(g+=4,++I):g+=3}return g})(g):g.length;var Q=og(4+C+1),E=Q+4;if(h[Q>>2]=C,I&&B)((A,g,I,C)=>{if(!(C>0))return 0;for(var B=I,Q=I+C-1,E=0;E<A.length;++E){var i=A.charCodeAt(E);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&A.charCodeAt(++E)),i<=127){if(I>=Q)break;g[I++]=i}else if(i<=2047){if(I+1>=Q)break;g[I++]=192|i>>6,g[I++]=128|63&i}else if(i<=65535){if(I+2>=Q)break;g[I++]=224|i>>12,g[I++]=128|i>>6&63,g[I++]=128|63&i}else{if(I+3>=Q)break;g[I++]=240|i>>18,g[I++]=128|i>>12&63,g[I++]=128|i>>6&63,g[I++]=128|63&i}}g[I]=0})(g,n,E,C+1);else if(B)for(var i=0;i<C;++i){var e=g.charCodeAt(i);e>255&&(sg(E),T("String has UTF-16 code units that do not fit in 8 bits")),n[E+i]=e}else for(i=0;i<C;++i)n[E+i]=g[i];return null!==A&&A.push(sg,Q),Q},argPackAdvance:8,readValueFromPointer:GA,destructorFunction(A){sg(A)}})},l:(A,g,I)=>{var C,B,Q,E,i;I=Z(I),2===g?(C=VA,B=qA,E=OA,Q=()=>c,i=1):4===g&&(C=$A,B=_A,E=Ag,Q=()=>h,i=2),X(A,{name:I,fromWireType:A=>{for(var I,B=h[A>>2],E=Q(),e=A+4,t=0;t<=B;++t){var o=A+4+t*g;if(t==B||0==E[o>>i]){var r=C(e,o-e);void 0===I?I=r:(I+=String.fromCharCode(0),I+=r),e=o+g}}return sg(A),I},toWireType:(A,C)=>{"string"!=typeof C&&T(`Cannot pass non-string to C++ string type ${I}`);var Q=E(C),e=og(4+Q+g);return h[e>>2]=Q>>i,B(C,e+4,Q+g),null!==A&&A.push(sg,e),e},argPackAdvance:8,readValueFromPointer:bA,destructorFunction(A){sg(A)}})},v:(A,g)=>{X(A,{isVoid:!0,name:g=Z(g),argPackAdvance:0,fromWireType:()=>{},toWireType:(A,g)=>{}})},j:(A,g,I)=>(A=JA.toValue(A),g=gg(g,"emval::as"),Ig(g,I,A)),e:(A,g,I,C,B)=>{var Q,E;return(A=Bg[A])(g=JA.toValue(g),g[I=void 0===(E=Cg[Q=I])?Z(Q):E],C,B)},d:UA,f:(A,g,I)=>{var C=((A,g)=>{for(var I=new Array(A),C=0;C<A;++C)I[C]=gg(h[g+4*C>>2],"parameter "+C);return I})(A,g),B=C.shift();A--;var Q,E,i=new Array(A),e=`methodCaller<(${C.map((A=>A.name)).join(", ")}) => ${B.name}>`;return Q=sA(e,((g,Q,E,e)=>{for(var t=0,o=0;o<A;++o)i[o]=C[o].readValueFromPointer(e+t),t+=C[o].argPackAdvance;var r=1===I?Qg(Q,i):Q.apply(g,i);for(o=0;o<A;++o)C[o].deleteObject&&C[o].deleteObject(i[o]);return Ig(B,E,r)})),E=Bg.length,Bg.push(Q),E},c:A=>{A>4&&(fA.get(A).refcount+=1)},b:A=>{var g=JA.toValue(A);SA(g),UA(A)},h:(A,g)=>{var I=(A=gg(A,"_emval_take_value")).readValueFromPointer(g);return JA.toHandle(I)},m:()=>{R("")},s:(A,g,I)=>n.copyWithin(A,g,g+I),r:A=>{n.length;R("OOM")},n:(A,g,I,C)=>{for(var B,Q,E,i=0,e=0;e<I;e++){var t=h[g>>2],s=h[g+4>>2];g+=8;for(var a=0;a<s;a++)B=A,Q=n[t+a],E=void 0,E=Eg[B],0===Q||10===Q?((1===B?o:r)(xA(E,0)),E.length=0):E.push(Q);i+=s}return h[C>>2]=i,0}},tg=function(){var A={a:eg};function g(A,g){var I,C;return tg=A.exports,t=tg.z,I=t.buffer,B.HEAP8=a=new Int8Array(I),B.HEAP16=D=new Int16Array(I),B.HEAPU8=n=new Uint8Array(I),B.HEAPU16=c=new Uint16Array(I),B.HEAP32=y=new Int32Array(I),B.HEAPU32=h=new Uint32Array(I),B.HEAPF32=G=new Float32Array(I),B.HEAPF64=w=new Float64Array(I),NA=tg.C,C=tg.A,d.unshift(C),function(){if(u--,B.monitorRunDependencies&&B.monitorRunDependencies(u),0==u&&(null!==Y&&(clearInterval(Y),Y=null),M)){var A=M;M=null,A()}}(),tg}if(u++,B.monitorRunDependencies&&B.monitorRunDependencies(u),B.instantiateWasm)try{return B.instantiateWasm(A,g)}catch(A){r(`Module.instantiateWasm callback failed with error: ${A}`),C(A)}return f(0,k,A,(function(A){g(A.instance)})).catch(C),{}}(),og=A=>(og=tg.B)(A),rg=A=>(rg=tg.D)(A),sg=A=>(sg=tg.E)(A),ag=A=>(ag=tg.F)(A);B.dynCall_jiji=(A,g,I,C,Q)=>(B.dynCall_jiji=tg.G)(A,g,I,C,Q),B._vertexShaderSource=10624;function ng(){function A(){ig||(ig=!0,B.calledRun=!0,N||(U(d),I(B),B.onRuntimeInitialized&&B.onRuntimeInitialized(),function(){if(B.postRun)for("function"==typeof B.postRun&&(B.postRun=[B.postRun]);B.postRun.length;)A=B.postRun.shift(),F.unshift(A);var A;U(F)}()))}u>0||(!function(){if(B.preRun)for("function"==typeof B.preRun&&(B.preRun=[B.preRun]);B.preRun.length;)A=B.preRun.shift(),l.unshift(A);var A;U(l)}(),u>0||(B.setStatus?(B.setStatus("Running..."),setTimeout((function(){setTimeout((function(){B.setStatus("")}),1),A()}),1)):A()))}if(M=function A(){ig||ng(),ig||(M=A)},B.preInit)for("function"==typeof B.preInit&&(B.preInit=[B.preInit]);B.preInit.length>0;)B.preInit.pop()();return ng(),g.ready}})(),allin1_default=Module,bbSeq=0,_BasicBeauty=class A{constructor(A){this.core=A,__publicField(this,"seq"),__publicField(this,"_core"),__publicField(this,"log"),__publicField(this,"beautyParams"),bbSeq+=1,this.seq=bbSeq,this._core=A,this.log=A.log.createChild({id:`${this.getAlias()}${bbSeq}`}),this.log.info("created")}getName(){return A.Name}getAlias(){return"bb"}getValidateRule(A){switch(A){case"start":case"update":return startValidateRule(this._core);case"stop":return stopValidateRule(this._core)}}getGroup(){return"bb"}async start(A){this._core.room.videoManager.Wasm||(this._core.room.videoManager.Wasm=await allin1_default()),this._core.room.videoManager.renderMode="webgl";const g=this._core.utils.isUndefined(A.beauty)?.5:A.beauty,I=this._core.utils.isUndefined(A.brightness)?.5:A.brightness,C=this._core.utils.isUndefined(A.ruddy)?.5:A.ruddy;return this._core.room.videoManager.setBeautyParams({beauty:g,brightness:I,ruddy:C})}async update(A){const g=this._core.utils.isUndefined(A.beauty)?.5:A.beauty,I=this._core.utils.isUndefined(A.brightness)?.5:A.brightness,C=this._core.utils.isUndefined(A.ruddy)?.5:A.ruddy;return this._core.room.videoManager.setBeautyParams({beauty:g,brightness:I,ruddy:C})}async stop(){return this._core.room.videoManager.renderMode="auto",this._core.room.videoManager.stopBeauty()}destroy(){this._core.room.videoManager.renderMode="auto"}};__publicField(_BasicBeauty,"Name","BasicBeauty");var BasicBeauty=_BasicBeauty,index_default=BasicBeauty;export{index_default as default};export{BasicBeauty};