!function(A,g){"object"==typeof exports&&"undefined"!=typeof module?module.exports=g():"function"==typeof define&&define.amd?define(g):(A="undefined"!=typeof globalThis?globalThis:A||self).BasicBeauty=g()}(this,(function(){"use strict";function A(A,g,I,C,B,Q,t){try{var E=A[Q](t),e=E.value}catch(A){return void I(A)}E.done?g(e):Promise.resolve(e).then(C,B)}function g(g){return function(){var I=this,C=arguments;return new Promise((function(B,Q){var t=g.apply(I,C);function E(g){A(t,B,Q,E,e,"next",g)}function e(g){A(t,B,Q,E,e,"throw",g)}E(void 0)}))}}function I(A,g,I){return g=Q(g),function(A,g){if(g&&("object"==typeof g||"function"==typeof g))return g;if(void 0!==g)throw new TypeError("Derived constructors may only return object or undefined");return function(A){if(void 0===A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A}(A)}(A,E()?Reflect.construct(g,I||[],Q(A).constructor):g.apply(A,I))}function C(A,g){if(!(A instanceof g))throw new TypeError("Cannot call a class as a function")}function B(A,g,I){return g&&function(A,g){for(var I=0;I<g.length;I++){var C=g[I];C.enumerable=C.enumerable||!1,C.configurable=!0,"value"in C&&(C.writable=!0),Object.defineProperty(A,o(C.key),C)}}(A.prototype,g),Object.defineProperty(A,"prototype",{writable:!1}),A}function Q(A){return Q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(A){return A.__proto__||Object.getPrototypeOf(A)},Q(A)}function t(A,g){if("function"!=typeof g&&null!==g)throw new TypeError("Super expression must either be null or a function");A.prototype=Object.create(g&&g.prototype,{constructor:{value:A,writable:!0,configurable:!0}}),Object.defineProperty(A,"prototype",{writable:!1}),g&&i(A,g)}function E(){try{var A=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(A){}return(E=function(){return!!A})()}function e(){e=function(){return g};var A,g={},I=Object.prototype,C=I.hasOwnProperty,B=Object.defineProperty||function(A,g,I){A[g]=I.value},Q="function"==typeof Symbol?Symbol:{},t=Q.iterator||"@@iterator",E=Q.asyncIterator||"@@asyncIterator",i=Q.toStringTag||"@@toStringTag";function o(A,g,I){return Object.defineProperty(A,g,{value:I,enumerable:!0,configurable:!0,writable:!0}),A[g]}try{o({},"")}catch(A){o=function(A,g,I){return A[g]=I}}function r(A,g,I,C){var Q=g&&g.prototype instanceof h?g:h,t=Object.create(Q.prototype),E=new k(C||[]);return B(t,"_invoke",{value:p(A,I,E)}),t}function n(A,g,I){try{return{type:"normal",arg:A.call(g,I)}}catch(A){return{type:"throw",arg:A}}}g.wrap=r;var a="suspendedStart",s="suspendedYield",c="executing",u="completed",y={};function h(){}function D(){}function w(){}var l={};o(l,t,(function(){return this}));var f=Object.getPrototypeOf,G=f&&f(f(L([])));G&&G!==I&&C.call(G,t)&&(l=G);var N=w.prototype=h.prototype=Object.create(l);function d(A){["next","throw","return"].forEach((function(g){o(A,g,(function(A){return this._invoke(g,A)}))}))}function F(A,g){function I(B,Q,t,E){var e=n(A[B],A,Q);if("throw"!==e.type){var i=e.arg,o=i.value;return o&&"object"==typeof o&&C.call(o,"__await")?g.resolve(o.__await).then((function(A){I("next",A,t,E)}),(function(A){I("throw",A,t,E)})):g.resolve(o).then((function(A){i.value=A,t(i)}),(function(A){return I("throw",A,t,E)}))}E(e.arg)}var Q;B(this,"_invoke",{value:function(A,C){function B(){return new g((function(g,B){I(A,C,g,B)}))}return Q=Q?Q.then(B,B):B()}})}function p(g,I,C){var B=a;return function(Q,t){if(B===c)throw Error("Generator is already running");if(B===u){if("throw"===Q)throw t;return{value:A,done:!0}}for(C.method=Q,C.arg=t;;){var E=C.delegate;if(E){var e=Y(E,C);if(e){if(e===y)continue;return e}}if("next"===C.method)C.sent=C._sent=C.arg;else if("throw"===C.method){if(B===a)throw B=u,C.arg;C.dispatchException(C.arg)}else"return"===C.method&&C.abrupt("return",C.arg);B=c;var i=n(g,I,C);if("normal"===i.type){if(B=C.done?u:s,i.arg===y)continue;return{value:i.arg,done:C.done}}"throw"===i.type&&(B=u,C.method="throw",C.arg=i.arg)}}}function Y(g,I){var C=I.method,B=g.iterator[C];if(B===A)return I.delegate=null,"throw"===C&&g.iterator.return&&(I.method="return",I.arg=A,Y(g,I),"throw"===I.method)||"return"!==C&&(I.method="throw",I.arg=new TypeError("The iterator does not provide a '"+C+"' method")),y;var Q=n(B,g.iterator,I.arg);if("throw"===Q.type)return I.method="throw",I.arg=Q.arg,I.delegate=null,y;var t=Q.arg;return t?t.done?(I[g.resultName]=t.value,I.next=g.nextLoc,"return"!==I.method&&(I.method="next",I.arg=A),I.delegate=null,y):t:(I.method="throw",I.arg=new TypeError("iterator result is not an object"),I.delegate=null,y)}function M(A){var g={tryLoc:A[0]};1 in A&&(g.catchLoc=A[1]),2 in A&&(g.finallyLoc=A[2],g.afterLoc=A[3]),this.tryEntries.push(g)}function R(A){var g=A.completion||{};g.type="normal",delete g.arg,A.completion=g}function k(A){this.tryEntries=[{tryLoc:"root"}],A.forEach(M,this),this.reset(!0)}function L(g){if(g||""===g){var I=g[t];if(I)return I.call(g);if("function"==typeof g.next)return g;if(!isNaN(g.length)){var B=-1,Q=function I(){for(;++B<g.length;)if(C.call(g,B))return I.value=g[B],I.done=!1,I;return I.value=A,I.done=!0,I};return Q.next=Q}}throw new TypeError(typeof g+" is not iterable")}return D.prototype=w,B(N,"constructor",{value:w,configurable:!0}),B(w,"constructor",{value:D,configurable:!0}),D.displayName=o(w,i,"GeneratorFunction"),g.isGeneratorFunction=function(A){var g="function"==typeof A&&A.constructor;return!!g&&(g===D||"GeneratorFunction"===(g.displayName||g.name))},g.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,w):(A.__proto__=w,o(A,i,"GeneratorFunction")),A.prototype=Object.create(N),A},g.awrap=function(A){return{__await:A}},d(F.prototype),o(F.prototype,E,(function(){return this})),g.AsyncIterator=F,g.async=function(A,I,C,B,Q){void 0===Q&&(Q=Promise);var t=new F(r(A,I,C,B),Q);return g.isGeneratorFunction(I)?t:t.next().then((function(A){return A.done?A.value:t.next()}))},d(N),o(N,i,"Generator"),o(N,t,(function(){return this})),o(N,"toString",(function(){return"[object Generator]"})),g.keys=function(A){var g=Object(A),I=[];for(var C in g)I.push(C);return I.reverse(),function A(){for(;I.length;){var C=I.pop();if(C in g)return A.value=C,A.done=!1,A}return A.done=!0,A}},g.values=L,k.prototype={constructor:k,reset:function(g){if(this.prev=0,this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(R),!g)for(var I in this)"t"===I.charAt(0)&&C.call(this,I)&&!isNaN(+I.slice(1))&&(this[I]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0].completion;if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(g){if(this.done)throw g;var I=this;function B(C,B){return E.type="throw",E.arg=g,I.next=C,B&&(I.method="next",I.arg=A),!!B}for(var Q=this.tryEntries.length-1;Q>=0;--Q){var t=this.tryEntries[Q],E=t.completion;if("root"===t.tryLoc)return B("end");if(t.tryLoc<=this.prev){var e=C.call(t,"catchLoc"),i=C.call(t,"finallyLoc");if(e&&i){if(this.prev<t.catchLoc)return B(t.catchLoc,!0);if(this.prev<t.finallyLoc)return B(t.finallyLoc)}else if(e){if(this.prev<t.catchLoc)return B(t.catchLoc,!0)}else{if(!i)throw Error("try statement without catch or finally");if(this.prev<t.finallyLoc)return B(t.finallyLoc)}}}},abrupt:function(A,g){for(var I=this.tryEntries.length-1;I>=0;--I){var B=this.tryEntries[I];if(B.tryLoc<=this.prev&&C.call(B,"finallyLoc")&&this.prev<B.finallyLoc){var Q=B;break}}Q&&("break"===A||"continue"===A)&&Q.tryLoc<=g&&g<=Q.finallyLoc&&(Q=null);var t=Q?Q.completion:{};return t.type=A,t.arg=g,Q?(this.method="next",this.next=Q.finallyLoc,y):this.complete(t)},complete:function(A,g){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&g&&(this.next=g),y},finish:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.finallyLoc===A)return this.complete(I.completion,I.afterLoc),R(I),y}},catch:function(A){for(var g=this.tryEntries.length-1;g>=0;--g){var I=this.tryEntries[g];if(I.tryLoc===A){var C=I.completion;if("throw"===C.type){var B=C.arg;R(I)}return B}}throw Error("illegal catch attempt")},delegateYield:function(g,I,C){return this.delegate={iterator:L(g),resultName:I,nextLoc:C},"next"===this.method&&(this.arg=A),y}},g}function i(A,g){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(A,g){return A.__proto__=g,A},i(A,g)}function o(A){var g=function(A,g){if("object"!=typeof A||!A)return A;var I=A[Symbol.toPrimitive];if(void 0!==I){var C=I.call(A,g);if("object"!=typeof C)return C;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(A)}(A,"string");return"symbol"==typeof g?g:g+""}function r(A){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},r(A)}function n(A){var g="function"==typeof Map?new Map:void 0;return n=function(A){if(null===A||!function(A){try{return-1!==Function.toString.call(A).indexOf("[native code]")}catch(g){return"function"==typeof A}}(A))return A;if("function"!=typeof A)throw new TypeError("Super expression must either be null or a function");if(void 0!==g){if(g.has(A))return g.get(A);g.set(A,I)}function I(){return function(A,g,I){if(E())return Reflect.construct.apply(null,arguments);var C=[null];C.push.apply(C,g);var B=new(A.bind.apply(A,C));return I&&i(B,I.prototype),B}(A,arguments,Q(this).constructor)}return I.prototype=Object.create(A.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),i(I,A)},n(A)}var a=Object.defineProperty,s=function(A,g,I){return function(A,g,I){return g in A?a(A,g,{enumerable:!0,configurable:!0,writable:!0,value:I}):A[g]=I}(A,"symbol"!==r(g)?g+"":g,I)};function c(A){return{name:"BasicBeautyOptions",type:"object",required:!0,allowEmpty:!1,properties:{beauty:{required:!1,type:"number"},brightness:{required:!1,type:"number"},ruddy:{required:!1,type:"number"}},validate:function(g,I,C,B){var Q=A.errorModule,t=Q.RtcError,E=Q.ErrorCode,e=Q.ErrorCodeDictionary;if(A.utils.isOverseaSdkAppId(g.sdkAppId))throw new t({code:E.INVALID_OPERATION,extraCode:e.INVALID_OPERATION,message:"This feature is not yet available in your country or region. If you have any questions, you can go to the community for consultation: https://zhiliao.qq.com/s/cWSPGIIM62CC/c3TPGIIM62CQ"})}}}var u,y=(u="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(){var A,g,Q=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},E=Q;E.ready=new Promise((function(I,C){A=I,g=C}));var e=Object.assign({},E),i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src),u&&(i=u),i=0!==i.indexOf("blob:")?i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var o,a,s=E.print||console.log.bind(console),c=E.printErr||console.error.bind(console);function y(A){if(U(A))return function(A){for(var g=atob(A),I=new Uint8Array(g.length),C=0;C<g.length;++C)I[C]=g.charCodeAt(C);return I}(A.slice(b.length))}Object.assign(E,e),e=null,E.arguments&&E.arguments,E.thisProgram&&E.thisProgram,E.quit&&E.quit,E.wasmBinary&&(o=E.wasmBinary),"object"!=("undefined"==typeof WebAssembly?"undefined":r(WebAssembly))&&L("no native wasm support detected");var h,D,w,l,f,G,N,d,F=!1,p=[],Y=[],M=[],R=0,k=null;function L(A){E.onAbort&&E.onAbort(A),c(A="Aborted("+A+")"),F=!0,A+=". Build with -sASSERTIONS for more info.";var I=new WebAssembly.RuntimeError(A);throw g(I),I}var S,H,b="data:application/octet-stream;base64,",U=function(A){return A.startsWith(b)};function K(A){return Promise.resolve().then((function(){return function(A){if(A==S&&o)return new Uint8Array(o);var g=y(A);if(g)return g;throw"both async and sync fetching of the wasm failed"}(A)}))}function v(A,g,I,C){return function(A,g,I){return K(A).then((function(A){return WebAssembly.instantiate(A,g)})).then((function(A){return A})).then(I,(function(A){c("failed to asynchronously prepare wasm: ".concat(A)),L(A)}))}(g,I,C)}U(S="data:application/octet-stream;base64,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")||(H=S,S=E.locateFile?E.locateFile(H,i):i+H);var J=function(A){for(;A.length>0;)A.shift()(E)};function m(A){this.excPtr=A,this.ptr=A-24,this.set_type=function(A){G[this.ptr+4>>2]=A},this.get_type=function(){return G[this.ptr+4>>2]},this.set_destructor=function(A){G[this.ptr+8>>2]=A},this.get_destructor=function(){return G[this.ptr+8>>2]},this.set_caught=function(A){A=A?1:0,h[this.ptr+12|0]=A},this.get_caught=function(){return 0!=h[this.ptr+12|0]},this.set_rethrown=function(A){A=A?1:0,h[this.ptr+13|0]=A},this.get_rethrown=function(){return 0!=h[this.ptr+13|0]},this.init=function(A,g){this.set_adjusted_ptr(0),this.set_type(A),this.set_destructor(g)},this.set_adjusted_ptr=function(A){G[this.ptr+16>>2]=A},this.get_adjusted_ptr=function(){return G[this.ptr+16>>2]},this.get_exception_ptr=function(){if(ug(this.get_type()))return G[this.excPtr>>2];var A=this.get_adjusted_ptr();return 0!==A?A:this.excPtr}}E.noExitRuntime;var Z,j,T,W=function(A){for(var g="",I=A;D[I];)g+=Z[D[I++]];return g},x={},P={},X={},z=function(A){throw new j(A)},O=function(A){throw new T(A)},V=function(A,g,I){function C(g){var C=I(g);C.length!==A.length&&O("Mismatched type converter count");for(var B=0;B<A.length;++B)q(A[B],C[B])}A.forEach((function(A){X[A]=g}));var B=new Array(g.length),Q=[],t=0;g.forEach((function(A,g){P.hasOwnProperty(A)?B[g]=P[A]:(Q.push(A),x.hasOwnProperty(A)||(x[A]=[]),x[A].push((function(){B[g]=P[A],++t===Q.length&&C(B)})))})),0===Q.length&&C(B)};function q(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return function(A,g){var I=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},C=g.name;if(A||z('type "'.concat(C,'" must have a positive integer typeid pointer')),P.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;z("Cannot register type '".concat(C,"' twice"))}if(P[A]=g,delete X[A],x.hasOwnProperty(A)){var B=x[A];delete x[A],B.forEach((function(A){return A()}))}}(A,g,I)}var $,_=function(A){z(A.$$.ptrType.registeredClass.name+" instance already deleted")},AA=!1,gA=function(A){},IA=function(A){A.count.value-=1,0===A.count.value&&function(A){A.smartPtr?A.smartPtrType.rawDestructor(A.smartPtr):A.ptrType.registeredClass.rawDestructor(A.ptr)}(A)},CA=function(A,g,I){if(g===I)return A;if(void 0===I.baseClass)return null;var C=CA(A,g,I.baseClass);return null===C?null:I.downcast(C)},BA={},QA=function(){return Object.keys(oA).length},tA=function(){var A=[];for(var g in oA)oA.hasOwnProperty(g)&&A.push(oA[g]);return A},EA=[],eA=function(){for(;EA.length;){var A=EA.pop();A.$$.deleteScheduled=!1,A.delete()}},iA=function(A){$=A,EA.length&&$&&$(eA)},oA={},rA=function(A,g){return g=function(A,g){for(void 0===g&&z("ptr should not be undefined");A.baseClass;)g=A.upcast(g),A=A.baseClass;return g}(A,g),oA[g]},nA=function(A,g){return g.ptrType&&g.ptr||O("makeClassHandle requires ptr and ptrType"),!!g.smartPtrType!=!!g.smartPtr&&O("Both smartPtrType and smartPtr must be specified"),g.count={value:1},sA(Object.create(A,{$$:{value:g}}))};function aA(A){var g=this.getPointee(A);if(!g)return this.destructor(A),null;var I=rA(this.registeredClass,g);if(void 0!==I){if(0===I.$$.count.value)return I.$$.ptr=g,I.$$.smartPtr=A,I.clone();var C=I.clone();return this.destructor(A),C}function B(){return this.isSmartPointer?nA(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:g,smartPtrType:this,smartPtr:A}):nA(this.registeredClass.instancePrototype,{ptrType:this,ptr:A})}var Q,t=this.registeredClass.getActualType(g),E=BA[t];if(!E)return B.call(this);Q=this.isConst?E.constPointerType:E.pointerType;var e=CA(g,this.registeredClass,Q.registeredClass);return null===e?B.call(this):this.isSmartPointer?nA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e,smartPtrType:this,smartPtr:A}):nA(Q.registeredClass.instancePrototype,{ptrType:Q,ptr:e})}var sA=function(A){return"undefined"==typeof FinalizationRegistry?(sA=function(A){return A},A):(AA=new FinalizationRegistry((function(A){IA(A.$$)})),gA=function(A){return AA.unregister(A)},(sA=function(A){var g=A.$$;if(g.smartPtr){var I={$$:g};AA.register(A,I,A)}return A})(A))};function cA(){}var uA=function(A,g){return Object.defineProperty(g,"name",{value:A})},yA=function(A,g,I){if(void 0===A[g].overloadTable){var C=A[g];A[g]=function(){return A[g].overloadTable.hasOwnProperty(arguments.length)||z("Function '".concat(I,"' called with an invalid number of arguments (").concat(arguments.length,") - expects one of (").concat(A[g].overloadTable,")!")),A[g].overloadTable[arguments.length].apply(this,arguments)},A[g].overloadTable=[],A[g].overloadTable[C.argCount]=C}};function hA(A,g,I,C,B,Q,t,E){this.name=A,this.constructor=g,this.instancePrototype=I,this.rawDestructor=C,this.baseClass=B,this.getActualType=Q,this.upcast=t,this.downcast=E,this.pureVirtualFunctions=[]}var DA=function(A,g,I){for(;g!==I;)g.upcast||z("Expected null or instance of ".concat(I.name,", got an instance of ").concat(g.name)),A=g.upcast(A),g=g.baseClass;return A};function wA(A,g){if(null===g)return this.isReference&&z("null is not a valid ".concat(this.name)),0;g.$$||z('Cannot pass "'.concat(PA(g),'" as a ').concat(this.name)),g.$$.ptr||z("Cannot pass deleted object as a pointer of type ".concat(this.name));var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function lA(A,g){var I;if(null===g)return this.isReference&&z("null is not a valid ".concat(this.name)),this.isSmartPointer?(I=this.rawConstructor(),null!==A&&A.push(this.rawDestructor,I),I):0;g.$$||z('Cannot pass "'.concat(PA(g),'" as a ').concat(this.name)),g.$$.ptr||z("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&g.$$.ptrType.isConst&&z("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));var C=g.$$.ptrType.registeredClass;if(I=DA(g.$$.ptr,C,this.registeredClass),this.isSmartPointer)switch(void 0===g.$$.smartPtr&&z("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:g.$$.smartPtrType===this?I=g.$$.smartPtr:z("Cannot convert argument of type ".concat(g.$$.smartPtrType?g.$$.smartPtrType.name:g.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:I=g.$$.smartPtr;break;case 2:if(g.$$.smartPtrType===this)I=g.$$.smartPtr;else{var B=g.clone();I=this.rawShare(I,ZA.toHandle((function(){return B.delete()}))),null!==A&&A.push(this.rawDestructor,I)}break;default:z("Unsupporting sharing policy")}return I}function fA(A,g){if(null===g)return this.isReference&&z("null is not a valid ".concat(this.name)),0;g.$$||z('Cannot pass "'.concat(PA(g),'" as a ').concat(this.name)),g.$$.ptr||z("Cannot pass deleted object as a pointer of type ".concat(this.name)),g.$$.ptrType.isConst&&z("Cannot convert argument of type ".concat(g.$$.ptrType.name," to parameter type ").concat(this.name));var I=g.$$.ptrType.registeredClass;return DA(g.$$.ptr,I,this.registeredClass)}function GA(A){return this.fromWireType(G[A>>2])}function NA(A,g,I,C,B,Q,t,E,e,i,o){this.name=A,this.registeredClass=g,this.isReference=I,this.isConst=C,this.isSmartPointer=B,this.pointeeType=Q,this.sharingPolicy=t,this.rawGetPointee=E,this.rawConstructor=e,this.rawShare=i,this.rawDestructor=o,B||void 0!==g.baseClass?this.toWireType=lA:C?(this.toWireType=wA,this.destructorFunction=null):(this.toWireType=fA,this.destructorFunction=null)}var dA,FA,pA=[],YA=function(A){var g=pA[A];return g||(A>=pA.length&&(pA.length=A+1),pA[A]=g=dA.get(A)),g},MA=function(A,g,I){return A.includes("j")?function(A,g,I){var C=E["dynCall_"+A];return I&&I.length?C.apply(null,[g].concat(I)):C.call(null,g)}(A,g,I):YA(g).apply(null,I)},RA=function(A,g){var I,C,B,Q=(A=W(A)).includes("j")?(I=A,C=g,B=[],function(){return B.length=0,Object.assign(B,arguments),MA(I,C,B)}):YA(g);return"function"!=typeof Q&&z("unknown function pointer with signature ".concat(A,": ").concat(g)),Q},kA=function(A){var g=sg(A),I=W(g);return cg(g),I},LA=function(A,g){var I=[],C={};throw g.forEach((function A(g){C[g]||P[g]||(X[g]?X[g].forEach(A):(I.push(g),C[g]=!0))})),new FA("".concat(A,": ")+I.map(kA).join([", "]))},SA=function(A,g){for(var I=[],C=0;C<A;C++)I.push(G[g+4*C>>2]);return I},HA=function(A){for(;A.length;){var g=A.pop();A.pop()(g)}};function bA(A,g,I,C,B,Q){var t=g.length;t<2&&z("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var E=null!==g[1]&&null!==I,e=!1,i=1;i<g.length;++i)if(null!==g[i]&&void 0===g[i].destructorFunction){e=!0;break}var o="void"!==g[0].name,r=t-2,n=new Array(r),a=[],s=[];return uA(A,(function(){var I;arguments.length!==r&&z("function ".concat(A," called with ").concat(arguments.length," arguments, expected ").concat(r)),s.length=0,a.length=E?2:1,a[0]=B,E&&(I=g[1].toWireType(s,this),a[1]=I);for(var Q=0;Q<r;++Q)n[Q]=g[Q+2].toWireType(s,arguments[Q]),a.push(n[Q]);return function(A){if(e)HA(s);else for(var C=E?1:2;C<g.length;C++){var B=1===C?I:n[C-2];null!==g[C].destructorFunction&&g[C].destructorFunction(B)}if(o)return g[0].fromWireType(A)}(C.apply(null,a))}))}var UA=function(A,g,I){return A instanceof Object||z("".concat(I,' with invalid "this": ').concat(A)),A instanceof g.registeredClass.constructor||z("".concat(I,' incompatible with "this" of type ').concat(A.constructor.name)),A.$$.ptr||z("cannot call emscripten binding method ".concat(I," on deleted object")),DA(A.$$.ptr,A.$$.ptrType.registeredClass,g.registeredClass)};function KA(){this.allocated=[void 0],this.freelist=[]}var vA=new KA,JA=function(A){A>=vA.reserved&&0==--vA.get(A).refcount&&vA.free(A)},mA=function(){for(var A=0,g=vA.reserved;g<vA.allocated.length;++g)void 0!==vA.allocated[g]&&++A;return A},ZA={toValue:function(A){return A||z("Cannot use deleted val. handle = "+A),vA.get(A).value},toHandle:function(A){switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return vA.allocate({refcount:1,value:A})}}};function jA(A){return this.fromWireType(f[A>>2])}var TA,WA,xA,PA=function(A){if(null===A)return"null";var g=r(A);return"object"===g||"array"===g||"function"===g?A.toString():""+A},XA=function(A,g){switch(g){case 4:return function(A){return this.fromWireType(N[A>>2])};case 8:return function(A){return this.fromWireType(d[A>>3])};default:throw new TypeError("invalid float width (".concat(g,"): ").concat(A))}},zA=function(A,g,I){switch(g){case 1:return I?function(A){return h[0|A]}:function(A){return D[0|A]};case 2:return I?function(A){return w[A>>1]}:function(A){return l[A>>1]};case 4:return I?function(A){return f[A>>2]}:function(A){return G[A>>2]};default:throw new TypeError("invalid integer width (".concat(g,"): ").concat(A))}},OA="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,VA=function(A,g,I){for(var C=g+I,B=g;A[B]&&!(B>=C);)++B;if(B-g>16&&A.buffer&&OA)return OA.decode(A.subarray(g,B));for(var Q="";g<B;){var t=A[g++];if(128&t){var E=63&A[g++];if(192!=(224&t)){var e=63&A[g++];if((t=224==(240&t)?(15&t)<<12|E<<6|e:(7&t)<<18|E<<12|e<<6|63&A[g++])<65536)Q+=String.fromCharCode(t);else{var i=t-65536;Q+=String.fromCharCode(55296|i>>10,56320|1023&i)}}else Q+=String.fromCharCode((31&t)<<6|E)}else Q+=String.fromCharCode(t)}return Q},qA="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,$A=function(A,g){for(var I=A,C=I>>1,B=C+g/2;!(C>=B)&&l[C];)++C;if((I=C<<1)-A>32&&qA)return qA.decode(D.subarray(A,I));for(var Q="",t=0;!(t>=g/2);++t){var E=w[A+2*t>>1];if(0==E)break;Q+=String.fromCharCode(E)}return Q},_A=function(A,g,I){if(void 0===I&&(I=2147483647),I<2)return 0;for(var C=g,B=(I-=2)<2*A.length?I/2:A.length,Q=0;Q<B;++Q){var t=A.charCodeAt(Q);w[g>>1]=t,g+=2}return w[g>>1]=0,g-C},Ag=function(A){return 2*A.length},gg=function(A,g){for(var I=0,C="";!(I>=g/4);){var B=f[A+4*I>>2];if(0==B)break;if(++I,B>=65536){var Q=B-65536;C+=String.fromCharCode(55296|Q>>10,56320|1023&Q)}else C+=String.fromCharCode(B)}return C},Ig=function(A,g,I){if(void 0===I&&(I=2147483647),I<4)return 0;for(var C=g,B=C+I-4,Q=0;Q<A.length;++Q){var t=A.charCodeAt(Q);if(t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&A.charCodeAt(++Q)),f[g>>2]=t,(g+=4)+4>B)break}return f[g>>2]=0,g-C},Cg=function(A){for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C>=55296&&C<=57343&&++I,g+=4}return g},Bg=function(A,g){var I=P[A];return void 0===I&&z(g+" has unknown type "+kA(A)),I},Qg=function(A,g,I){var C=[],B=A.toWireType(C,I);return C.length&&(G[g>>2]=ZA.toHandle(C)),B},tg={},Eg=[],eg=Reflect.construct,ig=[null,[],[]];!function(){for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);Z=A}(),j=E.BindingError=function(A){function g(A){var B;return C(this,g),(B=I(this,g,[A])).name="BindingError",B}return t(g,A),B(g)}(n(Error)),T=E.InternalError=function(A){function g(A){var B;return C(this,g),(B=I(this,g,[A])).name="InternalError",B}return t(g,A),B(g)}(n(Error)),Object.assign(cA.prototype,{isAliasOf:function(A){if(!(this instanceof cA))return!1;if(!(A instanceof cA))return!1;var g=this.$$.ptrType.registeredClass,I=this.$$.ptr;A.$$=A.$$;for(var C=A.$$.ptrType.registeredClass,B=A.$$.ptr;g.baseClass;)I=g.upcast(I),g=g.baseClass;for(;C.baseClass;)B=C.upcast(B),C=C.baseClass;return g===C&&I===B},clone:function(){if(this.$$.ptr||_(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var A,g=sA(Object.create(Object.getPrototypeOf(this),{$$:{value:(A=this.$$,{count:A.count,deleteScheduled:A.deleteScheduled,preservePointerOnDelete:A.preservePointerOnDelete,ptr:A.ptr,ptrType:A.ptrType,smartPtr:A.smartPtr,smartPtrType:A.smartPtrType})}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g},delete:function(){this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&z("Object already scheduled for deletion"),gA(this),IA(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted:function(){return!this.$$.ptr},deleteLater:function(){return this.$$.ptr||_(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&z("Object already scheduled for deletion"),EA.push(this),1===EA.length&&$&&$(eA),this.$$.deleteScheduled=!0,this}}),E.getInheritedInstanceCount=QA,E.getLiveInheritedInstances=tA,E.flushPendingDeletes=eA,E.setDelayFunction=iA,Object.assign(NA.prototype,{getPointee:function(A){return this.rawGetPointee&&(A=this.rawGetPointee(A)),A},destructor:function(A){this.rawDestructor&&this.rawDestructor(A)},argPackAdvance:8,readValueFromPointer:GA,deleteObject:function(A){null!==A&&A.delete()},fromWireType:aA}),FA=E.UnboundTypeError=(TA=Error,(xA=uA(WA="UnboundTypeError",(function(A){this.name=WA,this.message=A;var g=new Error(A).stack;void 0!==g&&(this.stack=this.toString()+"\n"+g.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(TA.prototype),xA.prototype.constructor=xA,xA.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},xA),Object.assign(KA.prototype,{get:function(A){return this.allocated[A]},has:function(A){return void 0!==this.allocated[A]},allocate:function(A){var g=this.freelist.pop()||this.allocated.length;return this.allocated[g]=A,g},free:function(A){this.allocated[A]=void 0,this.freelist.push(A)}}),vA.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),vA.reserved=vA.allocated.length,E.count_emval_handles=mA;var og,rg={w:function(A,g,I){throw new m(A).init(g,I),A},q:function(A,g,I,C,B){},u:function(A,g,I,C){q(A,{name:g=W(g),fromWireType:function(A){return!!A},toWireType:function(A,g){return g?I:C},argPackAdvance:8,readValueFromPointer:function(A){return this.fromWireType(D[A])},destructorFunction:null})},y:function(A,g,I,C,B,Q,t,e,i,o,r,n,a){r=W(r),Q=RA(B,Q),e&&(e=RA(t,e)),o&&(o=RA(i,o)),a=RA(n,a);var s=function(A){if(void 0===A)return"_unknown";var g=(A=A.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return g>=48&&g<=57?"_".concat(A):A}(r);!function(A,g,I){E.hasOwnProperty(A)?(z("Cannot register public name '".concat(A,"' twice")),yA(E,A,A),E.hasOwnProperty(I)&&z("Cannot register multiple overloads of a function with the same number of arguments (".concat(I,")!")),E[A].overloadTable[I]=g):E[A]=g}(s,(function(){LA("Cannot construct ".concat(r," due to unbound types"),[C])})),V([A,g,I],C?[C]:[],(function(g){var I,B;g=g[0],B=C?(I=g.registeredClass).instancePrototype:cA.prototype;var t=uA(r,(function(){if(Object.getPrototypeOf(this)!==i)throw new j("Use 'new' to construct "+r);if(void 0===n.constructor_body)throw new j(r+" has no accessible constructor");var A=n.constructor_body[arguments.length];if(void 0===A)throw new j("Tried to invoke ctor of ".concat(r," with invalid number of parameters (").concat(arguments.length,") - expected (").concat(Object.keys(n.constructor_body).toString(),") parameters instead!"));return A.apply(this,arguments)})),i=Object.create(B,{constructor:{value:t}});t.prototype=i;var n=new hA(r,t,i,a,I,Q,e,o);n.baseClass&&(void 0===n.baseClass.__derivedClasses&&(n.baseClass.__derivedClasses=[]),n.baseClass.__derivedClasses.push(n));var c=new NA(r,n,!0,!1,!1),u=new NA(r+"*",n,!1,!1,!1),y=new NA(r+" const*",n,!1,!0,!1);return BA[A]={pointerType:u,constPointerType:y},function(A,g,I){E.hasOwnProperty(A)||O("Replacing nonexistant public symbol"),void 0!==E[A].overloadTable&&void 0!==I||(E[A]=g,E[A].argCount=I)}(s,t),[c,u,y]}))},x:function(A,g,I,C,B,Q){var t=SA(g,I);B=RA(C,B),V([],[A],(function(A){A=A[0];var I="constructor ".concat(A.name);if(void 0===A.registeredClass.constructor_body&&(A.registeredClass.constructor_body=[]),void 0!==A.registeredClass.constructor_body[g-1])throw new j("Cannot register multiple constructors with identical number of parameters (".concat(g-1,") for class '").concat(A.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return A.registeredClass.constructor_body[g-1]=function(){LA("Cannot construct ".concat(A.name," due to unbound types"),t)},V([],t,(function(C){return C.splice(1,0,null),A.registeredClass.constructor_body[g-1]=bA(I,C,null,B,Q),[]})),[]}))},i:function(A,g,I,C,B,Q,t,E,e){var i,o,r=SA(I,C);g=W(g),o=(i=(i=g).trim()).indexOf("("),g=-1!==o?i.substr(0,o):i,Q=RA(B,Q),V([],[A],(function(A){A=A[0];var C="".concat(A.name,".").concat(g);function B(){LA("Cannot call ".concat(C," due to unbound types"),r)}g.startsWith("@@")&&(g=Symbol[g.substring(2)]),E&&A.registeredClass.pureVirtualFunctions.push(g);var e=A.registeredClass.instancePrototype,i=e[g];return void 0===i||void 0===i.overloadTable&&i.className!==A.name&&i.argCount===I-2?(B.argCount=I-2,B.className=A.name,e[g]=B):(yA(e,g,C),e[g].overloadTable[I-2]=B),V([],r,(function(B){var E=bA(C,B,A,Q,t);return void 0===e[g].overloadTable?(E.argCount=I-2,e[g]=E):e[g].overloadTable[I-2]=E,[]})),[]}))},k:function(A,g,I,C,B,Q,t,E,e,i){g=W(g),B=RA(C,B),V([],[A],(function(A){A=A[0];var C="".concat(A.name,".").concat(g),o={get:function(){LA("Cannot access ".concat(C," due to unbound types"),[I,t])},enumerable:!0,configurable:!0};return o.set=e?function(){return LA("Cannot access ".concat(C," due to unbound types"),[I,t])}:function(A){return z(C+" is a read-only property")},Object.defineProperty(A.registeredClass.instancePrototype,g,o),V([],e?[I,t]:[I],(function(I){var t=I[0],o={get:function(){var g=UA(this,A,C+" getter");return t.fromWireType(B(Q,g))},enumerable:!0};if(e){e=RA(E,e);var r=I[1];o.set=function(g){var I=UA(this,A,C+" setter"),B=[];e(i,I,r.toWireType(B,g)),HA(B)}}return Object.defineProperty(A.registeredClass.instancePrototype,g,o),[]})),[]}))},t:function(A,g){q(A,{name:g=W(g),fromWireType:function(A){var g=ZA.toValue(A);return JA(A),g},toWireType:function(A,g){return ZA.toHandle(g)},argPackAdvance:8,readValueFromPointer:jA,destructorFunction:null})},p:function(A,g,I){q(A,{name:g=W(g),fromWireType:function(A){return A},toWireType:function(A,g){return g},argPackAdvance:8,readValueFromPointer:XA(g,I),destructorFunction:null})},g:function(A,g,I,C,B){g=W(g);var Q=function(A){return A};if(0===C){var t=32-8*I;Q=function(A){return A<<t>>>t}}var E=g.includes("unsigned");q(A,{name:g,fromWireType:Q,toWireType:E?function(A,g){return this.name,g>>>0}:function(A,g){return this.name,g},argPackAdvance:8,readValueFromPointer:zA(g,I,0!==C),destructorFunction:null})},a:function(A,g,I){var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][g];function B(A){var g=G[A>>2],I=G[A+4>>2];return new C(h.buffer,I,g)}q(A,{name:I=W(I),fromWireType:B,argPackAdvance:8,readValueFromPointer:B},{ignoreDuplicateRegistrations:!0})},o:function(A,g){var I="std::string"===(g=W(g));q(A,{name:g,fromWireType:function(A){var g,C,B,Q=G[A>>2],t=A+4;if(I)for(var E=t,e=0;e<=Q;++e){var i=t+e;if(e==Q||0==D[i]){var o=(B=i-E,(C=E)?VA(D,C,B):"");void 0===g?g=o:(g+=String.fromCharCode(0),g+=o),E=i+1}}else{var r=new Array(Q);for(e=0;e<Q;++e)r[e]=String.fromCharCode(D[t+e]);g=r.join("")}return cg(A),g},toWireType:function(A,g){var C;g instanceof ArrayBuffer&&(g=new Uint8Array(g));var B="string"==typeof g;B||g instanceof Uint8Array||g instanceof Uint8ClampedArray||g instanceof Int8Array||z("Cannot pass non-string to std::string"),C=I&&B?function(A){for(var g=0,I=0;I<A.length;++I){var C=A.charCodeAt(I);C<=127?g++:C<=2047?g+=2:C>=55296&&C<=57343?(g+=4,++I):g+=3}return g}(g):g.length;var Q=ag(4+C+1),t=Q+4;if(G[Q>>2]=C,I&&B)!function(A,g,I,C){if(!(C>0))return 0;for(var B=I+C-1,Q=0;Q<A.length;++Q){var t=A.charCodeAt(Q);if(t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&A.charCodeAt(++Q)),t<=127){if(I>=B)break;g[I++]=t}else if(t<=2047){if(I+1>=B)break;g[I++]=192|t>>6,g[I++]=128|63&t}else if(t<=65535){if(I+2>=B)break;g[I++]=224|t>>12,g[I++]=128|t>>6&63,g[I++]=128|63&t}else{if(I+3>=B)break;g[I++]=240|t>>18,g[I++]=128|t>>12&63,g[I++]=128|t>>6&63,g[I++]=128|63&t}}g[I]=0}(g,D,t,C+1);else if(B)for(var E=0;E<C;++E){var e=g.charCodeAt(E);e>255&&(cg(t),z("String has UTF-16 code units that do not fit in 8 bits")),D[t+E]=e}else for(E=0;E<C;++E)D[t+E]=g[E];return null!==A&&A.push(cg,Q),Q},argPackAdvance:8,readValueFromPointer:GA,destructorFunction:function(A){cg(A)}})},l:function(A,g,I){var C,B,Q,t,E;I=W(I),2===g?(C=$A,B=_A,t=Ag,Q=function(){return l},E=1):4===g&&(C=gg,B=Ig,t=Cg,Q=function(){return G},E=2),q(A,{name:I,fromWireType:function(A){for(var I,B=G[A>>2],t=Q(),e=A+4,i=0;i<=B;++i){var o=A+4+i*g;if(i==B||0==t[o>>E]){var r=C(e,o-e);void 0===I?I=r:(I+=String.fromCharCode(0),I+=r),e=o+g}}return cg(A),I},toWireType:function(A,C){"string"!=typeof C&&z("Cannot pass non-string to C++ string type ".concat(I));var Q=t(C),e=ag(4+Q+g);return G[e>>2]=Q>>E,B(C,e+4,Q+g),null!==A&&A.push(cg,e),e},argPackAdvance:8,readValueFromPointer:jA,destructorFunction:function(A){cg(A)}})},v:function(A,g){q(A,{isVoid:!0,name:g=W(g),argPackAdvance:0,fromWireType:function(){},toWireType:function(A,g){}})},j:function(A,g,I){return A=ZA.toValue(A),g=Bg(g,"emval::as"),Qg(g,I,A)},e:function(A,g,I,C,B){var Q,t;return(A=Eg[A])(g=ZA.toValue(g),g[I=void 0===(t=tg[Q=I])?W(Q):t],C,B)},d:JA,f:function(A,g,I){var C=function(A,g){for(var I=new Array(A),C=0;C<A;++C)I[C]=Bg(G[g+4*C>>2],"parameter "+C);return I}(A,g),B=C.shift();A--;var Q,t,E=new Array(A),e="methodCaller<(".concat(C.map((function(A){return A.name})).join(", "),") => ").concat(B.name,">");return Q=uA(e,(function(g,Q,t,e){for(var i=0,o=0;o<A;++o)E[o]=C[o].readValueFromPointer(e+i),i+=C[o].argPackAdvance;var r=1===I?eg(Q,E):Q.apply(g,E);for(o=0;o<A;++o)C[o].deleteObject&&C[o].deleteObject(E[o]);return Qg(B,t,r)})),t=Eg.length,Eg.push(Q),t},c:function(A){A>4&&(vA.get(A).refcount+=1)},b:function(A){var g=ZA.toValue(A);HA(g),JA(A)},h:function(A,g){var I=(A=Bg(A,"_emval_take_value")).readValueFromPointer(g);return ZA.toHandle(I)},m:function(){L("")},s:function(A,g,I){return D.copyWithin(A,g,g+I)},r:function(A){D.length,L("OOM")},n:function(A,g,I,C){for(var B,Q,t,E=0,e=0;e<I;e++){var i=G[g>>2],o=G[g+4>>2];g+=8;for(var r=0;r<o;r++)B=A,Q=D[i+r],t=void 0,t=ig[B],0===Q||10===Q?((1===B?s:c)(VA(t,0)),t.length=0):t.push(Q);E+=o}return G[C>>2]=E,0}},ng=function(){var A={a:rg};function I(A,g){var I,C;return ng=A.exports,a=ng.z,I=a.buffer,E.HEAP8=h=new Int8Array(I),E.HEAP16=w=new Int16Array(I),E.HEAPU8=D=new Uint8Array(I),E.HEAPU16=l=new Uint16Array(I),E.HEAP32=f=new Int32Array(I),E.HEAPU32=G=new Uint32Array(I),E.HEAPF32=N=new Float32Array(I),E.HEAPF64=d=new Float64Array(I),dA=ng.C,C=ng.A,Y.unshift(C),function(){if(R--,E.monitorRunDependencies&&E.monitorRunDependencies(R),0==R&&k){var A=k;k=null,A()}}(),ng}if(R++,E.monitorRunDependencies&&E.monitorRunDependencies(R),E.instantiateWasm)try{return E.instantiateWasm(A,I)}catch(A){c("Module.instantiateWasm callback failed with error: ".concat(A)),g(A)}return v(0,S,A,(function(A){I(A.instance)})).catch(g),{}}(),ag=function(A){return(ag=ng.B)(A)},sg=function(A){return(sg=ng.D)(A)},cg=function(A){return(cg=ng.E)(A)},ug=function(A){return(ug=ng.F)(A)};function yg(){function g(){og||(og=!0,E.calledRun=!0,F||(J(Y),A(E),E.onRuntimeInitialized&&E.onRuntimeInitialized(),function(){if(E.postRun)for("function"==typeof E.postRun&&(E.postRun=[E.postRun]);E.postRun.length;)A=E.postRun.shift(),M.unshift(A);var A;J(M)}()))}R>0||(function(){if(E.preRun)for("function"==typeof E.preRun&&(E.preRun=[E.preRun]);E.preRun.length;)A=E.preRun.shift(),p.unshift(A);var A;J(p)}(),R>0||(E.setStatus?(E.setStatus("Running..."),setTimeout((function(){setTimeout((function(){E.setStatus("")}),1),g()}),1)):g()))}if(E.dynCall_jiji=function(A,g,I,C,B){return(E.dynCall_jiji=ng.G)(A,g,I,C,B)},E._vertexShaderSource=10624,k=function A(){og||yg(),og||(k=A)},E.preInit)for("function"==typeof E.preInit&&(E.preInit=[E.preInit]);E.preInit.length>0;)E.preInit.pop()();return yg(),Q.ready}),h=y,D=0,w=function(){function A(g){C(this,A),this.core=g,s(this,"seq"),s(this,"_core"),s(this,"log"),s(this,"beautyParams"),D+=1,this.seq=D,this._core=g,this.log=g.log.createChild({id:"".concat(this.getAlias()).concat(D)}),this.log.info("created")}return B(A,[{key:"getName",value:function(){return A.Name}},{key:"getAlias",value:function(){return"bb"}},{key:"getValidateRule",value:function(A){switch(A){case"start":case"update":return c(this._core);case"stop":return this._core,{name:"StopBasicBeautyOptions",required:!1}}}},{key:"getGroup",value:function(){return"bb"}},{key:"start",value:(t=g(e().mark((function A(g){var I,C,B;return e().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(this._core.room.videoManager.Wasm){A.next=4;break}return A.next=3,h();case 3:this._core.room.videoManager.Wasm=A.sent;case 4:return this._core.room.videoManager.renderMode="webgl",I=this._core.utils.isUndefined(g.beauty)?.5:g.beauty,C=this._core.utils.isUndefined(g.brightness)?.5:g.brightness,B=this._core.utils.isUndefined(g.ruddy)?.5:g.ruddy,A.abrupt("return",this._core.room.videoManager.setBeautyParams({beauty:I,brightness:C,ruddy:B}));case 9:case"end":return A.stop()}}),A,this)}))),function(A){return t.apply(this,arguments)})},{key:"update",value:(Q=g(e().mark((function A(g){var I,C,B;return e().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return I=this._core.utils.isUndefined(g.beauty)?.5:g.beauty,C=this._core.utils.isUndefined(g.brightness)?.5:g.brightness,B=this._core.utils.isUndefined(g.ruddy)?.5:g.ruddy,A.abrupt("return",this._core.room.videoManager.setBeautyParams({beauty:I,brightness:C,ruddy:B}));case 4:case"end":return A.stop()}}),A,this)}))),function(A){return Q.apply(this,arguments)})},{key:"stop",value:(I=g(e().mark((function A(){return e().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:return this._core.room.videoManager.renderMode="auto",A.abrupt("return",this._core.room.videoManager.stopBeauty());case 2:case"end":return A.stop()}}),A,this)}))),function(){return I.apply(this,arguments)})},{key:"destroy",value:function(){this._core.room.videoManager.renderMode="auto"}}]);var I,Q,t}();return s(w,"Name","BasicBeauty"),w}));
