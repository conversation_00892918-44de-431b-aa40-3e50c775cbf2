!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(e="undefined"!=typeof globalThis?globalThis:e||self).VideoMixer=r()}(this,(function(){"use strict";function e(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=Array(r);t<r;t++)n[t]=e[t];return n}function r(e,r,t,n,i,o,a){try{var c=e[o](a),s=c.value}catch(e){return void t(e)}c.done?r(s):Promise.resolve(s).then(n,i)}function t(e){return function(){var t=this,n=arguments;return new Promise((function(i,o){var a=e.apply(t,n);function c(e){r(a,i,o,c,s,"next",e)}function s(e){r(a,i,o,c,s,"throw",e)}c(void 0)}))}}function n(e,r,t){return r&&function(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,c(n.key),n)}}(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=u(e))||r){t&&(e=t);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(c)throw o}}}}function o(){o=function(){return r};var e,r={},t=Object.prototype,n=t.hasOwnProperty,i=Object.defineProperty||function(e,r,t){e[r]=t.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(e,r,t){return Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}),e[r]}try{l({},"")}catch(e){l=function(e,r,t){return e[r]=t}}function f(e,r,t,n){var o=r&&r.prototype instanceof x?r:x,a=Object.create(o.prototype),c=new L(n||[]);return i(a,"_invoke",{value:M(e,t,c)}),a}function p(e,r,t){try{return{type:"normal",arg:e.call(r,t)}}catch(e){return{type:"throw",arg:e}}}r.wrap=f;var d="suspendedStart",h="suspendedYield",v="executing",y="completed",m={};function x(){}function g(){}function b(){}var w={};l(w,c,(function(){return this}));var k=Object.getPrototypeOf,S=k&&k(k(P([])));S&&S!==t&&n.call(S,c)&&(w=S);var E=b.prototype=x.prototype=Object.create(w);function T(e){["next","throw","return"].forEach((function(r){l(e,r,(function(e){return this._invoke(r,e)}))}))}function V(e,r){function t(i,o,a,c){var s=p(e[i],e,o);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&n.call(l,"__await")?r.resolve(l.__await).then((function(e){t("next",e,a,c)}),(function(e){t("throw",e,a,c)})):r.resolve(l).then((function(e){u.value=e,a(u)}),(function(e){return t("throw",e,a,c)}))}c(s.arg)}var o;i(this,"_invoke",{value:function(e,n){function i(){return new r((function(r,i){t(e,n,r,i)}))}return o=o?o.then(i,i):i()}})}function M(r,t,n){var i=d;return function(o,a){if(i===v)throw Error("Generator is already running");if(i===y){if("throw"===o)throw a;return{value:e,done:!0}}for(n.method=o,n.arg=a;;){var c=n.delegate;if(c){var s=C(c,n);if(s){if(s===m)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===d)throw i=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=v;var u=p(r,t,n);if("normal"===u.type){if(i=n.done?y:h,u.arg===m)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=y,n.method="throw",n.arg=u.arg)}}}function C(r,t){var n=t.method,i=r.iterator[n];if(i===e)return t.delegate=null,"throw"===n&&r.iterator.return&&(t.method="return",t.arg=e,C(r,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var o=p(i,r.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,m;var a=o.arg;return a?a.done?(t[r.resultName]=a.value,t.next=r.nextLoc,"return"!==t.method&&(t.method="next",t.arg=e),t.delegate=null,m):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function _(e){var r={tryLoc:e[0]};1 in e&&(r.catchLoc=e[1]),2 in e&&(r.finallyLoc=e[2],r.afterLoc=e[3]),this.tryEntries.push(r)}function O(e){var r=e.completion||{};r.type="normal",delete r.arg,e.completion=r}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function P(r){if(r||""===r){var t=r[c];if(t)return t.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var i=-1,o=function t(){for(;++i<r.length;)if(n.call(r,i))return t.value=r[i],t.done=!1,t;return t.value=e,t.done=!0,t};return o.next=o}}throw new TypeError(typeof r+" is not iterable")}return g.prototype=b,i(E,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:g,configurable:!0}),g.displayName=l(b,u,"GeneratorFunction"),r.isGeneratorFunction=function(e){var r="function"==typeof e&&e.constructor;return!!r&&(r===g||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,u,"GeneratorFunction")),e.prototype=Object.create(E),e},r.awrap=function(e){return{__await:e}},T(V.prototype),l(V.prototype,s,(function(){return this})),r.AsyncIterator=V,r.async=function(e,t,n,i,o){void 0===o&&(o=Promise);var a=new V(f(e,t,n,i),o);return r.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},T(E),l(E,u,"Generator"),l(E,c,(function(){return this})),l(E,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var r=Object(e),t=[];for(var n in r)t.push(n);return t.reverse(),function e(){for(;t.length;){var n=t.pop();if(n in r)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=P,L.prototype={constructor:L,reset:function(r){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(O),!r)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var t=this;function i(n,i){return c.type="throw",c.arg=r,t.next=n,i&&(t.method="next",t.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],c=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var s=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(s&&u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,r){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=r&&r<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=r,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(a)},complete:function(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),m},finish:function(e){for(var r=this.tryEntries.length-1;r>=0;--r){var t=this.tryEntries[r];if(t.finallyLoc===e)return this.complete(t.completion,t.afterLoc),O(t),m}},catch:function(e){for(var r=this.tryEntries.length-1;r>=0;--r){var t=this.tryEntries[r];if(t.tryLoc===e){var n=t.completion;if("throw"===n.type){var i=n.arg;O(t)}return i}}throw Error("illegal catch attempt")},delegateYield:function(r,t,n){return this.delegate={iterator:P(r),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=e),m}},r}function a(r){return function(r){if(Array.isArray(r))return e(r)}(r)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||u(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==typeof r?r:r+""}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function u(r,t){if(r){if("string"==typeof r)return e(r,t);var n={}.toString.call(r).slice(8,-1);return"Object"===n&&r.constructor&&(n=r.constructor.name),"Map"===n||"Set"===n?Array.from(r):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(r,t):void 0}}var l=Object.defineProperty,f=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,h=function(e,r,t){return r in e?l(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t},v=function(e,r){for(var t in r||(r={}))p.call(r,t)&&h(e,t,r[t]);if(f){var n,o=i(f(r));try{for(o.s();!(n=o.n()).done;){t=n.value;d.call(r,t)&&h(e,t,r[t])}}catch(e){o.e(e)}finally{o.f()}}return e},y=function(e,r,t){return h(e,"symbol"!==s(r)?r+"":r,t)},m={x:{required:!0,type:"number"},y:{required:!0,type:"number"},width:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},height:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},zIndex:{required:!0,type:"number"},fillMode:{required:!1,type:"string"},mirror:{required:!1,type:"boolean"},rotation:{required:!1,type:"number"},hidden:{required:!1,type:"boolean"}},x=function(e){return{type:"object",required:arguments.length>1&&void 0!==arguments[1]&&arguments[1],properties:{canvasColor:{required:!1,type:["string",CanvasGradient,CanvasPattern]},width:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},height:{required:!0,type:"number",notLessThanZero:!0,min:1,max:3840},frameRate:{required:!1,type:"number",notLessThanZero:!0,min:1,max:60}},validate:function(r,t,n){var i=e.errorModule,o=i.RtcError,a=i.ErrorCode;if(i.ErrorCodeDictionary,r){var c=r.width,s=r.height;if(c&&s&&c*s>8294400)throw new o({code:a.INVALID_PARAMETER,message:"The mix resolution cannot be set higher than 3840 * 2160."})}}}},g=function(e){return{required:!1,type:["string",HTMLElement,null],validate:function(r,t,n){var i=e.errorModule,o=i.RtcError,a=i.ErrorCode,c=i.ErrorCodeDictionary;if(e.utils.isString(r)&&!document.getElementById(r))throw new o({code:a.INVALID_PARAMETER,extraCode:c.INVALID_ELEMENT_ID,fnName:n,messageParams:{key:t}})}}},b=function(e){return{type:"object",required:!(arguments.length>1&&void 0!==arguments[1])||arguments[1],properties:v({},m),validate:function(r,t,n){var i=e.errorModule,o=i.RtcError,a=i.ErrorCode,c=i.ErrorCodeDictionary;if(r){if(r.fillMode&&!["contain","cover","fill"].includes(r.fillMode))throw new o({code:a.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_TYPE,message:"The fillMode parameter must be 'contain', 'cover' or 'fill'",fnName:n});if(r.rotation&&![0,90,180,270].includes(r.rotation))throw new o({code:a.INVALID_PARAMETER,extraCode:c.INVALID_PARAMETER_TYPE,message:"The rotation parameter must be 0, 90, 180 or 270",fnName:n})}}}},w=function(e){return{type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},cameraId:{required:!1,type:"string"},videoTrack:{required:!1,instanceof:MediaStreamTrack},profile:{required:!1,type:["string","object"],properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},layout:v({},b(e))}}}},k=function(e){return{type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},profile:{required:!1,type:["string","object"],properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},captureElement:{required:!1,type:HTMLElement},preferDisplaySurface:{required:!1,type:"string"},layout:v({},b(e))},validate:function(r,t,n){var i=e.errorModule,o=i.RtcError,a=i.ErrorCode,c=i.ErrorCodeDictionary;if(!e.rtcDectection.isScreenCaptureApiAvailable())throw new o({code:a.ENV_NOT_SUPPORTED,fnName:n,extraCode:c.NOT_SUPPORTED_SCREEN_SHARE})}}}},S=function(e){return{type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},content:{required:!0,type:"string"},font:{required:!1,type:"string"},color:{required:!1,type:["string",CanvasGradient,CanvasPattern]},layout:v({},b(e))}}}},E=function(e){return{type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},url:{required:!0,type:"string"},layout:v({},b(e))}}}},T=function(e){return{type:"array",required:!1,arrayItem:{type:"object",properties:{id:{required:!0,type:"string"},url:{required:!0,type:"string"},layout:v({},b(e))}}}};var V=0,M=function(){function e(r){!function(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}(this,e),this.core=r,y(this,"seq"),y(this,"log"),y(this,"localMixVideoTrack",null),y(this,"_mixVideoConfig"),y(this,"onScreenShareStop"),V+=1,this.seq=V,this.log=r.log.createChild({id:"".concat(this.getAlias()).concat(V)}),this.log.info("created")}return n(e,[{key:"getName",value:function(){return e.Name}},{key:"getAlias",value:function(){return"vmix"}},{key:"getValidateRule",value:function(e){switch(e){case"start":return r=this.core,{name:"VideoMixerOptions",type:"object",required:!0,allowEmpty:!1,properties:{view:v({},g(r)),canvasInfo:v({},x(r,!0)),camera:v({},w(r)),screen:v({},k(r)),text:v({},S(r)),image:v({},E(r)),video:v({},T(r))},validate:function(e,t,n,i){var o=r.errorModule,a=o.RtcError,c=o.ErrorCode,u=o.ErrorCodeDictionary;if(r.environment.isMobile())throw new a({code:c.ENV_NOT_SUPPORTED,message:"VideoMixer is not supported on mobile devices currently"});var l=e.onScreenShareStop;if(l&&!r.utils.isFunction(l))throw new a({code:c.INVALID_PARAMETER,extraCode:u.INVALID_PARAMETER_TYPE,fnName:n,messageParams:{key:"onScreenShareStop",value:s(l),rule:{type:"Function"}}})}};case"update":return function(e){return{name:"VideoMixerOptions",type:"object",required:!1,allowEmpty:!1,properties:{view:v({},g(e)),canvasInfo:v({},x(e)),camera:v({},w(e)),screen:v({},k(e)),text:v({},S(e)),image:v({},E(e)),video:v({},T(e))}}}(this.core);case"stop":return this.core,{name:"StopVideoMixerOptions",required:!1}}var r}},{key:"getGroup",value:function(){return"vmix"}},{key:"start",value:(C=t(o().mark((function e(r){var t,n,i,a;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.localMixVideoTrack||(this.localMixVideoTrack=new this.core.LocalMixVideoTrack(this.core.room.videoManager)),this._mixVideoConfig={canvasInfo:{width:1920,height:1080}},r=this.core.utils.deepCloneBasic(r),n=(t=r).view,i=t.onScreenShareStop,e.next=6,this.parseMixOptions(r);case 6:return a=e.sent,i&&(this.onScreenShareStop=i,this._mixVideoConfig.onScreenShareStop=i),this._updatePreview({view:n,track:this.localMixVideoTrack}),this.core.utils.isUndefined(n)||(this._mixVideoConfig.view=n),e.next=12,this.localMixVideoTrack.startMix();case 12:return e.abrupt("return",{track:this.localMixVideoTrack._outputTrack,result:a});case 13:case"end":return e.stop()}}),e,this)}))),function(e){return C.apply(this,arguments)})},{key:"update",value:(M=t(o().mark((function e(r){var t,n,i,a,c;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.core.errorModule,n=t.RtcError,i=t.ErrorCode,this.localMixVideoTrack){e.next=3;break}throw new n({code:i.INVALID_OPERATION,message:"mixTrack doesn't initialize!"});case 3:return r=this.core.utils.deepCloneBasic(r),a=r.view,e.next=7,this.parseMixOptions(r);case 7:return c=e.sent,e.next=10,this._updatePreview({view:a,track:this.localMixVideoTrack,prevConfig:this._mixVideoConfig});case 10:return this.core.utils.isUndefined(a)||(this._mixVideoConfig.view=a),e.abrupt("return",{track:this.localMixVideoTrack._outputTrack,result:c});case 12:case"end":return e.stop()}}),e,this)}))),function(e){return M.apply(this,arguments)})},{key:"parseMixOptions",value:(b=t(o().mark((function e(r){var t,n,i,c,s,u,l,f,p,d,h,y,m,x,g,b,w,k,S,E,T,V,M,C,_,O,L,P,I,A,q,N,j;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t={successOptions:{},failedDetails:[]},n=this.core.errorModule,i=n.RtcError,c=n.ErrorCode,n.ErrorCodeDictionary,this.localMixVideoTrack&&this._mixVideoConfig){e.next=4;break}return e.abrupt("return",t);case 4:if(s=[],u=v({},r),l=r.canvasInfo,f=r.camera,p=r.screen,d=r.text,h=r.image,y=r.video,m=0,x=0,l&&(g=l.canvasColor,b=l.width,w=l.height,k=l.frameRate,g&&this.localMixVideoTrack.setMixBackground(g),k&&this.localMixVideoTrack.setFps(k),this.localMixVideoTrack.resizeMixCanvas(b,w),this._mixVideoConfig.canvasInfo=l),!f){e.next=20;break}return m++,e.next=14,this.parseCameraOptions(this.localMixVideoTrack,f,this._mixVideoConfig.camera);case 14:S=e.sent,E=S.finalOptions,T=S.errors,this._mixVideoConfig.camera=E,u.camera=E,T.length>0&&(s.push.apply(s,a(T)),T.length===f.length&&x++);case 20:if(!p){e.next=30;break}return m++,e.next=24,this.parseScreenOptions(this.localMixVideoTrack,p,this._mixVideoConfig.screen);case 24:V=e.sent,M=V.finalOptions,C=V.errors,this._mixVideoConfig.screen=M,u.screen=M,C.length>0&&(s.push.apply(s,a(C)),C.length===p.length&&x++);case 30:if(!d){e.next=40;break}return m++,e.next=34,this.parseTextOptions(this.localMixVideoTrack,d,this._mixVideoConfig.text);case 34:_=e.sent,O=_.finalOptions,L=_.errors,this._mixVideoConfig.text=O,u.text=O,L.length>0&&(s.push.apply(s,a(L)),L.length===d.length&&x++);case 40:if(!h){e.next=50;break}return m++,e.next=44,this.parseImageOptions(this.localMixVideoTrack,h,this._mixVideoConfig.image);case 44:P=e.sent,I=P.finalOptions,A=P.errors,this._mixVideoConfig.image=I,u.image=I,A.length>0&&(s.push.apply(s,a(A)),A.length===h.length&&x++);case 50:if(!y){e.next=60;break}return m++,e.next=54,this.parseVideoOptions(this.localMixVideoTrack,y,this._mixVideoConfig.video);case 54:q=e.sent,N=q.finalOptions,j=q.errors,this._mixVideoConfig.video=N,u.video=N,j.length>0&&(s.push.apply(s,a(j)),j.length===y.length&&x++);case 60:if(!(x>0&&x===m)){e.next=62;break}throw new i({code:c.INVALID_PARAMETER,message:"all sources mix failed",data:{failedDetails:s}});case 62:return t={successOptions:u,failedDetails:s},e.abrupt("return",t);case 64:case"end":return e.stop()}}),e,this)}))),function(e){return b.apply(this,arguments)})},{key:"parseCameraOptions",value:(m=t(o().mark((function e(r,t){var n,a,c,s,u,l,f,p,d,h,v,y,m,x,g,b,w,k,S,E,T,V=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=V.length>2&&void 0!==V[2]?V[2]:[],s=new Set(t.map((function(e){return e.id}))),u=n.filter((function(e){return!s.has(e.id)})).map((function(e){return e.id})),l=i(u);try{for(l.s();!(f=l.n()).done;)p=f.value,r.removeCameraSource(p)}catch(e){l.e(e)}finally{l.f()}d=new Map(n.map((function(e){return[e.id,e]}))),h=[],v=[],y=i(t),e.prev=9,y.s();case 11:if((m=y.n()).done){e.next=45;break}if(x=m.value,g=x.id,b=x.layout,w=x.profile,e.prev=14,!r.inputLocalVideoTracks.has(g)){e.next=25;break}return k=null==(a=r.inputLocalVideoTracks.get(g))?void 0:a.mediaTrack,e.next=19,this.updateCameraProfile(x);case 19:S=null==(c=r.inputLocalVideoTracks.get(g))?void 0:c.mediaTrack,E=void 0,w&&(E=this.core.utils.isString(w)?this.core.constants.videoProfileMap[w]:w),S!==k?r.updateCameraSource(g,b,S,E):r.updateCameraSource(g,b,null,E),e.next=36;break;case 25:return e.next=27,this.captureCamera(x);case 27:T=e.sent,e.prev=28,r.addCameraSource(g,T,b),e.next=36;break;case 32:throw e.prev=32,e.t0=e.catch(28),T.close(),e.t0;case 36:h.push(x),e.next=43;break;case 39:e.prev=39,e.t1=e.catch(14),v.push({id:g,error:e.t1}),d.has(g)&&h.push(d.get(g));case 43:e.next=11;break;case 45:e.next=50;break;case 47:e.prev=47,e.t2=e.catch(9),y.e(e.t2);case 50:return e.prev=50,y.f(),e.finish(50);case 53:return e.abrupt("return",{finalOptions:h,errors:v});case 54:case"end":return e.stop()}}),e,this,[[9,47,50,53],[14,39],[28,32]])}))),function(e,r){return m.apply(this,arguments)})},{key:"parseScreenOptions",value:(h=t(o().mark((function e(r,t){var n,a,c,s,u,l,f,p,d,h,v,y,m,x,g,b=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=b.length>2&&void 0!==b[2]?b[2]:[],a=new Set(t.map((function(e){return e.id}))),c=n.filter((function(e){return!a.has(e.id)})).map((function(e){return e.id})),s=i(c);try{for(s.s();!(u=s.n()).done;)l=u.value,r.removeScreenSource(l)}catch(e){s.e(e)}finally{s.f()}f=new Map(n.map((function(e){return[e.id,e]}))),p=[],d=[],h=i(t),e.prev=9,h.s();case 11:if((v=h.n()).done){e.next=39;break}if(y=v.value,m=y.id,x=y.layout,e.prev=14,!r.inputLocalScreenTracks.has(m)){e.next=19;break}r.updateScreenSource(m,x),e.next=30;break;case 19:return e.next=21,this.captureScreen(y);case 21:g=e.sent,e.prev=22,r.addScreenSource(m,g,x),e.next=30;break;case 26:throw e.prev=26,e.t0=e.catch(22),g.close(),e.t0;case 30:p.push(y),e.next=37;break;case 33:e.prev=33,e.t1=e.catch(14),d.push({id:m,error:e.t1}),f.has(m)&&p.push(f.get(m));case 37:e.next=11;break;case 39:e.next=44;break;case 41:e.prev=41,e.t2=e.catch(9),h.e(e.t2);case 44:return e.prev=44,h.f(),e.finish(44);case 47:return e.abrupt("return",{finalOptions:p,errors:d});case 48:case"end":return e.stop()}}),e,this,[[9,41,44,47],[14,33],[22,26]])}))),function(e,r){return h.apply(this,arguments)})},{key:"parseTextOptions",value:(d=t(o().mark((function e(r,t){var n,a,c,s,u,l,f,p,d,h,v,y,m=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=m.length>2&&void 0!==m[2]?m[2]:[],a=new Set(t.map((function(e){return e.id}))),c=n.filter((function(e){return!a.has(e.id)})).map((function(e){return e.id})),s=i(c);try{for(s.s();!(u=s.n()).done;)l=u.value,r.removeTextSource(l)}catch(e){s.e(e)}finally{s.f()}f=new Map(n.map((function(e){return[e.id,e]}))),p=[],d=[],h=i(t),e.prev=9,y=o().mark((function e(){var t,i;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=v.value,i=t.id;try{n.some((function(e){return e.id===i}))?r.updateTextSource(t):r.addTextSource(t),p.push(t)}catch(e){d.push({id:i,error:e}),f.has(i)&&p.push(f.get(i))}case 3:case"end":return e.stop()}}),e)})),h.s();case 12:if((v=h.n()).done){e.next=16;break}return e.delegateYield(y(),"t0",14);case 14:e.next=12;break;case 16:e.next=21;break;case 18:e.prev=18,e.t1=e.catch(9),h.e(e.t1);case 21:return e.prev=21,h.f(),e.finish(21);case 24:return e.abrupt("return",{finalOptions:p,errors:d});case 25:case"end":return e.stop()}}),e,null,[[9,18,21,24]])}))),function(e,r){return d.apply(this,arguments)})},{key:"parseImageOptions",value:(p=t(o().mark((function e(r,t){var n,a,c,s,u,l,f,p,d,h,v,y,m=this,x=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=x.length>2&&void 0!==x[2]?x[2]:[],a=new Set(t.map((function(e){return e.id}))),c=n.filter((function(e){return!a.has(e.id)})).map((function(e){return e.id})),s=i(c);try{for(s.s();!(u=s.n()).done;)l=u.value,r.removeImageSource(l)}catch(e){s.e(e)}finally{s.f()}f=new Map(n.map((function(e){return[e.id,e]}))),p=[],d=[],h=i(t),e.prev=9,y=o().mark((function e(){var t,i,a,c,s,u,l;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=v.value,i=t.id,a=t.url,c=t.layout,e.prev=2,!(s=n.find((function(e){return e.id===i})))){e.next=12;break}if(s.url===a){e.next=9;break}return e.next=8,m.core.utils.loadImage(a);case 8:u=e.sent;case 9:r.updateImageSource(i,c,u),e.next=16;break;case 12:return e.next=14,m.core.utils.loadImage(a);case 14:l=e.sent,r.addImageSource(i,l,c);case 16:p.push(t),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(2),d.push({id:i,error:e.t0}),f.has(i)&&p.push(f.get(i));case 23:case"end":return e.stop()}}),e,null,[[2,19]])})),h.s();case 12:if((v=h.n()).done){e.next=16;break}return e.delegateYield(y(),"t0",14);case 14:e.next=12;break;case 16:e.next=21;break;case 18:e.prev=18,e.t1=e.catch(9),h.e(e.t1);case 21:return e.prev=21,h.f(),e.finish(21);case 24:return e.abrupt("return",{finalOptions:p,errors:d});case 25:case"end":return e.stop()}}),e,null,[[9,18,21,24]])}))),function(e,r){return p.apply(this,arguments)})},{key:"parseVideoOptions",value:(f=t(o().mark((function e(r,t){var n,a,c,s,u,l,f,p,d,h,v,y,m=this,x=arguments;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=x.length>2&&void 0!==x[2]?x[2]:[],a=new Set(t.map((function(e){return e.id}))),c=n.filter((function(e){return!a.has(e.id)})).map((function(e){return e.id})),s=i(c);try{for(s.s();!(u=s.n()).done;)l=u.value,r.removeVideoSource(l)}catch(e){s.e(e)}finally{s.f()}f=new Map(n.map((function(e){return[e.id,e]}))),p=[],d=[],h=i(t),e.prev=9,y=o().mark((function e(){var t,i,a,c,s,u,l;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=v.value,i=t.id,a=t.url,c=t.layout,e.prev=2,!(s=n.find((function(e){return e.id===i})))){e.next=12;break}if(s.url===a){e.next=9;break}return e.next=8,m.core.utils.loadVideo(a);case 8:u=e.sent;case 9:r.updateVideoSource(i,c,u),e.next=16;break;case 12:return e.next=14,m.core.utils.loadVideo(a);case 14:l=e.sent,r.addVideoSource(i,l,c);case 16:p.push(t),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(2),d.push({id:i,error:e.t0}),f.has(i)&&p.push(f.get(i));case 23:case"end":return e.stop()}}),e,null,[[2,19]])})),h.s();case 12:if((v=h.n()).done){e.next=16;break}return e.delegateYield(y(),"t0",14);case 14:e.next=12;break;case 16:e.next=21;break;case 18:e.prev=18,e.t1=e.catch(9),h.e(e.t1);case 21:return e.prev=21,h.f(),e.finish(21);case 24:return e.abrupt("return",{finalOptions:p,errors:d});case 25:case"end":return e.stop()}}),e,null,[[9,18,21,24]])}))),function(e,r){return f.apply(this,arguments)})},{key:"captureCamera",value:(l=t(o().mark((function e(r){var t,n,i,a,c,s;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.id,n=r.cameraId,i=r.videoTrack,a=r.profile,(c=new this.core.LocalVideoTrack).log.id+="-".concat(t),s={},n?s.deviceId=n:this.core.utils.isUndefined(i)||(s.customSource=i),this.core.utils.isUndefined(a)||(this.core.utils.isString(a)?this.core.constants.videoProfileMap[a]&&c.setProfile(this.core.constants.videoProfileMap[a]):c.setProfile(a)),e.next=8,c.capture(s);case 8:return e.abrupt("return",c);case 9:case"end":return e.stop()}}),e,this)}))),function(e){return l.apply(this,arguments)})},{key:"updateCameraProfile",value:(u=t(o().mark((function e(r){var t,n,i,a,c,s;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=r.id,i=r.cameraId,a=r.videoTrack,c=r.profile,s=null==(t=this.localMixVideoTrack)?void 0:t.inputLocalVideoTracks.get(n)){e.next=4;break}return e.abrupt("return");case 4:if(!i){e.next=9;break}return e.next=7,s.switchDevice(i);case 7:e.next=12;break;case 9:if(this.core.utils.isUndefined(a)){e.next=12;break}return e.next=12,s.setInputMediaStreamTrack(a);case 12:if(this.core.utils.isUndefined(c)){e.next=17;break}if(this.core.utils.isString(c)?this.core.constants.videoProfileMap[c]&&s.setProfile(this.core.constants.videoProfileMap[c]):s.setProfile(c),i&&s.isNeedToSwitchDevice(i)){e.next=17;break}return e.next=17,s.applyProfile();case 17:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"captureScreen",value:(c=t(o().mark((function e(r){var t,n,i,a,c,s,u=this;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.id,n=r.profile,i=r.captureElement,a=r.preferDisplaySurface,(c=new this.core.LocalScreenTrack).log.id+="-".concat(t),s={},this.core.utils.isUndefined(n)||(this.core.utils.isString(n)?this.core.constants.screenProfileMap[n]&&c.setProfile(this.core.constants.screenProfileMap[n]):c.setProfile(n)),i&&(s.captureElement=i),a&&(s.preferDisplaySurface=a),e.next=9,c.capture(s);case 9:return c.mediaTrack.addEventListener(this.core.constants.NAME.ENDED,(function(){var e,r,n;null==(e=u.localMixVideoTrack)||e.removeScreenSource(t),(null==(r=u._mixVideoConfig)?void 0:r.screen)&&(u._mixVideoConfig.screen=u._mixVideoConfig.screen.filter((function(e){return e.id!==t}))),null==(n=u.onScreenShareStop)||n.call(u,t)})),e.abrupt("return",c);case 11:case"end":return e.stop()}}),e,this)}))),function(e){return c.apply(this,arguments)})},{key:"_updatePreview",value:(r=t(o().mark((function e(r){var t,n,i,a,c;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=r.view,n=r.track,i=r.prevConfig,!(this.core.utils.isUndefined(t)&&i&&i.view)){e.next=6;break}if(!((a=this.core.utils.getViewListFromView(i.view)).length>0)){e.next=6;break}return e.next=6,n.play(a);case 6:if(this.core.utils.isUndefined(t)){e.next=14;break}if(!((c=this.core.utils.getViewListFromView(t)).length>0)){e.next=13;break}return e.next=11,n.play(c);case 11:e.next=14;break;case 13:n.stop();case 14:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"stop",value:function(){var e;null==(e=this.localMixVideoTrack)||e.close(),this.localMixVideoTrack=null,delete this.onScreenShareStop,delete this._mixVideoConfig}}]);var r,c,u,l,f,p,d,h,m,b,M,C}();return y(M,"Name","VideoMixer"),M}));
