!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).CrossRoom=e()}(this,(function(){"use strict";function t(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function e(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function r(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function u(t){e(a,o,i,u,c,"next",t)}function c(t){e(a,o,i,u,c,"throw",t)}u(void 0)}))}}function n(t,e,r){return e&&function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,a(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}function o(){o=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var o=e&&e.prototype instanceof g?e:g,a=Object.create(o.prototype),u=new P(n||[]);return i(a,"_invoke",{value:O(t,r,u)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",y="executing",m="completed",v={};function g(){}function b(){}function w(){}var x={};l(x,u,(function(){return this}));var S=Object.getPrototypeOf,E=S&&S(S(T([])));E&&E!==r&&n.call(E,u)&&(x=E);var R=w.prototype=g.prototype=Object.create(x);function I(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function r(o,i,a,u){var c=d(t[o],t,i);if("throw"!==c.type){var s=c.arg,l=s.value;return l&&"object"==typeof l&&n.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(l).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(t,n){function i(){return new e((function(e,o){r(t,n,e,o)}))}return o=o?o.then(i,i):i()}})}function O(e,r,n){var o=p;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=L(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var s=d(e,r,n);if("normal"===s.type){if(o=n.done?m:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=m,n.method="throw",n.arg=s.arg)}}}function L(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,L(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=d(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function _(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(_,this),this.reset(!0)}function T(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return b.prototype=w,i(R,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=l(w,s,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,l(t,s,"GeneratorFunction")),t.prototype=Object.create(R),t},e.awrap=function(t){return{__await:t}},I(k.prototype),l(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new k(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},I(R),l(R,s,"Generator"),l(R,u,(function(){return this})),l(R,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=T,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(j),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),j(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;j(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:T(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function i(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||c(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e);if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function c(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}var s=Object.defineProperty,l=Object.defineProperties,f=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyDescriptors,p=Object.getOwnPropertySymbols,h=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable,m=function(t,e,r){return e in t?s(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r},v=function(t,e){for(var r in e||(e={}))h.call(e,r)&&m(t,r,e[r]);if(p){var n,o=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=c(t))||e){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(p(e));try{for(o.s();!(n=o.n()).done;){r=n.value;y.call(e,r)&&m(t,r,e[r])}}catch(t){o.e(t)}finally{o.f()}}return t},g=function(t,e){return l(t,d(e))},b=function(t,e,r,n){for(var o,i=f(e,r),a=t.length-1;a>=0;a--)(o=t[a])&&(i=o(e,r,i)||i);return i&&s(e,r,i),i},w=function(t,e,r){return m(t,"symbol"!==u(e)?e+"":e,r)},x={roomId:{type:"number"},strRoomId:{type:"string"}},S={name:"option",required:!0,properties:g(v({},x),{userId:{type:"string"}})},E={name:"option",required:!0,properties:{updateList:{type:"array",required:!0,arrayItem:{required:!0,type:"object",properties:g(v({},S.properties),{userId:{required:!1,type:"string"},muteAudio:{type:"boolean"},muteVideo:{type:"boolean"},muteSubStream:{type:"boolean"}})}}}},R={name:"option",properties:v({},x)},I=function(t){return"function"==typeof t};var k=function(t){var e=t.retryFunction,n=t.settings,i=t.onError,a=t.onRetrying,u=t.onRetryFailed,c=t.onRetrySuccess,s=t.context;return function(){for(var t=this,l=arguments.length,f=new Array(l),d=0;d<l;d++)f[d]=arguments[d];var p=n.retries,h=void 0===p?5:p,y=n.timeout,m=void 0===y?1e3:y,v=0,g=-1,b=0,w=function(){var n=r(o().mark((function r(n,l){var d,p,y,x;return o().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return d=s||t,r.prev=1,r.next=4,e.apply(d,f);case 4:p=r.sent,v>0&&c&&c.call(t,v),v=0,n(p),r.next=15;break;case 10:r.prev=10,r.t0=r.catch(1),y=function(){clearTimeout(g),v=0,b=2,l(r.t0)},x=function(){2!==b&&v<(I(h)?h():h)?(v++,b=1,I(a)&&a.call(t,v,y),g=window.setTimeout((function(){g=-1,w(n,l)}),I(m)?m(v):m)):(y(),I(u)&&u.call(t,r.t0))},I(i)?i.call(t,{error:r.t0,retry:x,reject:l,retryFuncArgs:f,retriedCount:v}):x();case 15:case"end":return r.stop()}}),r,null,[[1,10]])})));return function(t,e){return n.apply(this,arguments)}}();return new Promise(w)}},O=new WeakMap;function L(t){var e=t.settings,r=void 0===e?{retries:5,timeout:2e3}:e,n=t.onError,o=t.onRetrying,i=t.onRetryFailed;return function(t,e,a){var u=k({retryFunction:a.value,settings:r,onError:function(r){var o,i=r.error,a=r.retry,u=r.reject,c=r.retryFuncArgs;n?n.call(this,i,(function(){var r;(null==(r=O.get(t))?void 0:r.has(e))?a():u(i)}),u,c):(null==(o=O.get(t))?void 0:o.has(e))?a():u(i)},onRetrying:function(r,n){var i;I(o)&&o.call(this,r,n),(null==(i=O.get(t))?void 0:i.has(e))&&(O.get(t).get(e).stopRetry=n)},onRetryFailed:i});return a.value=function(){for(var r=O.get(t),n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return r?r.set(e,{args:o}):O.set(t,new Map([[e,{args:o}]])),u.apply(this,o).finally((function(){var r;return null==(r=O.get(t))?void 0:r.delete(e)}))},a}}var _=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.core=e,w(this,"disableRandomCall",!0),w(this,"connectedRoomIdSet",new Set),w(this,"updateSeq",0),w(this,"_log"),this._log=this.core.log.createChild({id:"".concat(this.getAlias())})}return n(t,[{key:"getName",value:function(){return t.Name}},{key:"getAlias",value:function(){return"crs-r"}},{key:"getGroup",value:function(t){var e,r=(null==t?void 0:t.userId)||(null==(e=null==t?void 0:t.updateList)?void 0:e[0].userId)||"";return r||(t?t.updateList?String(t.updateList[0].roomId)||t.updateList[0].strRoomId||"":String(t.roomId)||t.strRoomId||"":"*")}},{key:"getValidateRule",value:function(t){switch(t){case"start":return S;case"update":return E;case"stop":return R}}},{key:"start",value:(c=r(o().mark((function t(e){var r,n,i,a,u,c,s,l;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.roomId,n=e.strRoomId,i=e.userId,a=this.core.errorModule,u=a.RtcError,c=a.ErrorCode,this.core.room.sendSignalMessage){t.next=4;break}throw new u({code:c.ENV_NOT_SUPPORTED});case 4:return s=r||n,t.next=7,this.core.room.sendSignalMessage({command:"connect_other_room",responseCommand:String(8209),data:{roomId:s,userId:i,localRoomId:i?void 0:this.core.room.roomId},retries:3});case 7:if(0===(l=t.sent).data.code){t.next=10;break}throw new u({code:c.SERVER_ERROR,extraCode:l.data.code,message:l.data.message});case 10:i||this.connectedRoomIdSet.add(s);case 11:case"end":return t.stop()}}),t,this)}))),function(t){return c.apply(this,arguments)})},{key:"update",value:(u=r(o().mark((function t(e){var r,n,i,a,u,c,s;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.updateList,i=this.core.errorModule,a=i.RtcError,u=i.ErrorCode,this.core.room.sendSignalMessage){t.next=4;break}throw new a({code:u.ENV_NOT_SUPPORTED});case 4:return c=r.find((function(t){return t.userId}))?0:1,t.next=7,this.core.room.sendSignalMessage({command:"update_other_room_forward_mode",responseCommand:String(8213),data:{seq:++this.updateSeq,operationType:c,updateList:r.map((function(t){var e=t.roomId,r=t.strRoomId;return{roomId:e||r,userId:t.userId,muteAudio:t.muteAudio,muteVideo:t.muteVideo,muteSubStream:t.muteSubStream}}))},retries:3});case 7:if(!(s=t.sent).data.data.expectSeq){t.next=11;break}return this.updateSeq=s.data.data.expectSeq,t.abrupt("return",this.update({updateList:r}));case 11:if(0===s.data.code){t.next=15;break}throw new a({code:u.SERVER_ERROR,extraCode:s.data.code,message:s.data.message});case 15:if(!((null==(n=s.data.data.errorList)?void 0:n.length)>0)){t.next=17;break}throw new a({code:u.UNKNOWN_ERROR,message:s.data.data.errorList[0].message});case 17:case"end":return t.stop()}}),t,this)}))),function(t){return u.apply(this,arguments)})},{key:"stop",value:(a=r(o().mark((function t(){var e,r,n,a,u,c,s,l=arguments;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=(e=l.length>0&&void 0!==l[0]?l[0]:{}).roomId,n=e.strRoomId,!(a=r||n)){t.next=7;break}return t.next=5,this.doStop(a);case 5:t.next=20;break;case 7:if(!(this.connectedRoomIdSet.size>0)){t.next=18;break}u=0,c=i(this.connectedRoomIdSet.values());case 9:if(!(u<c.length)){t.next=16;break}return s=c[u],t.next=13,this.doStop(s);case 13:u++,t.next=9;break;case 16:t.next=20;break;case 18:return t.next=20,this.doStop();case 20:case"end":return t.stop()}}),t,this)}))),function(){return a.apply(this,arguments)})},{key:"doStop",value:(e=r(o().mark((function t(e){var r,n,i,a;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=this.core.errorModule,n=r.RtcError,i=r.ErrorCode,this.core.room.sendSignalMessage){t.next=3;break}throw new n({code:i.ENV_NOT_SUPPORTED});case 3:return t.next=5,this.core.room.sendSignalMessage({command:"disconnect_other_room",responseCommand:String(8211),data:{roomId:e,localRoomId:this.core.room.roomId},retries:3});case 5:if(0===(a=t.sent).data.code){t.next=8;break}throw new n({code:i.SERVER_ERROR,extraCode:a.data.code,message:a.data.message});case 8:this.connectedRoomIdSet.delete(e);case 9:case"end":return t.stop()}}),t,this)}))),function(t){return e.apply(this,arguments)})},{key:"destroy",value:function(){}}]);var e,a,u,c}();return w(_,"Name","CrossRoom"),b([L({settings:{retries:3,timeout:1e3},onRetrying:function(t){this._log.warn("retry start: ".concat(t))}})],_.prototype,"start"),b([L({settings:{retries:3,timeout:1e3},onRetrying:function(t){this._log.warn("retry update: ".concat(t))}})],_.prototype,"update"),_}));
