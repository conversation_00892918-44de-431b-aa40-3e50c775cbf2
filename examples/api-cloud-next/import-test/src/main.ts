import TRTC from 'trtc-sdk-v5';
import { BasicBeauty } from 'trtc-sdk-v5/plugins/video-effect/basic-beauty';

const trtc = TRTC.create({ plugins: [BasicBeauty] });

trtc.startLocalVideo({ view: 'local' }).then(() => {
  trtc.startPlugin('BasicBeauty', {
    beauty: 0.9
  });
});

trtc.startRemoteVideo({
  view: 'remote',
  userId: '2',
  streamType: TRTC.TYPE.STREAM_TYPE_MAIN,
  option: {
    canvasRender: true,
    poster: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/rtc/vb.jpg'
  }
})

console.log(TRTC.EVENT.AUDIO_FRAME)