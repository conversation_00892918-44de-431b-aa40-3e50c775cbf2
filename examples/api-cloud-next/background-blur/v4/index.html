<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>v4</title>
  </head>
  <body>
    <input type="radio" name="profile" value="480p15" disabled> 480p15
    <input type="radio" name="profile" value="480p30" disabled> 480p30
    <input type="radio" name="profile" value="720p15" disabled> 720p15
    <input type="radio" name="profile" value="720p30" disabled> 720p30
    <div id="log">  </div>
    <hr>
    <button id="video"> v4进房并打开摄像头 </button>
    <button id="open-local-video-view"> 开启本地预览 </button>
    <button id="close-local-video-view"> 关闭本地预览 </button>
    <button id="exit"> exit </button>
    <div id="local_stream" style="width: 426px; height: 240px"></div>
  </body>
</html>
<!-- 
<script src="../../../../packages/sdk/dist/npm-package/trtc.js"></script>
<script src="../../../../plugins/rtc-beauty-plugin/dist/npm-package/rtc-beauty-plugin.js"></script> -->

<script src="./trtc.js"></script>
<script src="./rtc-beauty-plugin.js"></script>
<script type="module" src="./main.js"></script>