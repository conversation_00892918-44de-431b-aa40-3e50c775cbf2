!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("trtc-js-sdk")):"function"==typeof define&&define.amd?define(["trtc-js-sdk"],t):(e=e||self).RTCBeautyPlugin=t(e.TRTC)}(this,(function(e){function t(e,t,r,n,o,i,a){try{var u=e[i](a),s=u.value}catch(l){return void r(l)}u.done?t(s):Promise.resolve(s).then(n,o)}function r(e){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=e.apply(r,n);function u(e){t(a,o,i,u,s,"next",e)}function s(e){t(a,o,i,u,s,"throw",e)}u(void 0)}))}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e;var a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function u(e,t){return e(t={exports:{}},t.exports),t.exports}u((function(e){var t=function(e){var t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function u(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(w){u=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),a=new P(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return C()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===c)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var s=l(e,t,r);if("normal"===s.type){if(n=r.done?"completed":"suspendedYield",s.arg===c)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(n="completed",r.method="throw",r.arg=s.arg)}}}(e,r,a),i}function l(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(w){return{type:"throw",arg:w}}}e.wrap=s;var c={};function f(){}function h(){}function v(){}var d={};u(d,o,(function(){return this}));var g=Object.getPrototypeOf,x=g&&g(g(E([])));x&&x!==t&&r.call(x,o)&&(d=x);var m=v.prototype=f.prototype=Object.create(d);function p(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function y(e,t){var n;this._invoke=function(o,i){function a(){return new t((function(n,a){!function n(o,i,a,u){var s=l(e[o],e,i);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"==typeof f&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):t.resolve(f).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,u)}))}u(s.arg)}(o,i,n,a)}))}return n=n?n.then(a,a):a()}}function _(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,_(e,t),"throw"===t.method))return c;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return c}var n=l(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,c;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,c):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,c)}function b(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(b,this),this.reset(!0)}function E(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:C}}function C(){return{value:void 0,done:!0}}return h.prototype=v,u(m,"constructor",v),u(v,"constructor",h),h.displayName=u(v,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,u(e,a,"GeneratorFunction")),e.prototype=Object.create(m),e},e.awrap=function(e){return{__await:e}},p(y.prototype),u(y.prototype,i,(function(){return this})),e.AsyncIterator=y,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new y(s(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},p(m),u(m,a,"Generator"),u(m,o,(function(){return this})),u(m,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=E,P.prototype={constructor:P,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(u&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,c):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),c},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),c}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:E(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),c}},e}(e.exports);try{regeneratorRuntime=t}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=t:Function("r","regeneratorRuntime = r")(t)}}));var s,l,c=function(e){return e&&e.Math==Math&&e},f=c("object"==typeof globalThis&&globalThis)||c("object"==typeof window&&window)||c("object"==typeof self&&self)||c("object"==typeof a&&a)||function(){return this}()||Function("return this")(),h=function(e){try{return!!e()}catch(t){return!0}},v=!h((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d={}.propertyIsEnumerable,g=Object.getOwnPropertyDescriptor,x={f:g&&!d.call({1:2},1)?function(e){var t=g(this,e);return!!t&&t.enumerable}:d},m=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},p={}.toString,y=function(e){return p.call(e).slice(8,-1)},_="".split,b=h((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==y(e)?_.call(e,""):Object(e)}:Object,T=function(e){if(null==e)throw TypeError("Can't call method on "+e);return e},P=function(e){return b(T(e))},E=function(e){return"function"==typeof e},C=function(e){return"object"==typeof e?null!==e:E(e)},w=function(e){return E(e)?e:void 0},S=function(e,t){return arguments.length<2?w(f[e]):f[e]&&f[e][t]},A=S("navigator","userAgent")||"",k=f.process,B=f.Deno,R=k&&k.versions||B&&B.version,L=R&&R.v8;L?l=(s=L.split("."))[0]<4?1:s[0]+s[1]:A&&(!(s=A.match(/Edge\/(\d+)/))||s[1]>=74)&&(s=A.match(/Chrome\/(\d+)/))&&(l=s[1]);var O=l&&+l,I=!!Object.getOwnPropertySymbols&&!h((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&O&&O<41})),j=I&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,U=j?function(e){return"symbol"==typeof e}:function(e){var t=S("Symbol");return E(t)&&Object(e)instanceof t},D=function(e){try{return String(e)}catch(t){return"Object"}},M=function(e){if(E(e))return e;throw TypeError(D(e)+" is not a function")},F=function(e,t){var r=e[t];return null==r?void 0:M(r)},V=function(e,t){try{Object.defineProperty(f,e,{value:t,configurable:!0,writable:!0})}catch(r){f[e]=t}return t},N=f["__core-js_shared__"]||V("__core-js_shared__",{}),G=u((function(e){(e.exports=function(e,t){return N[e]||(N[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.18.2",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),z=function(e){return Object(T(e))},X={}.hasOwnProperty,Y=Object.hasOwn||function(e,t){return X.call(z(e),t)},W=0,Z=Math.random(),H=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++W+Z).toString(36)},$=G("wks"),K=f.Symbol,q=j?K:K&&K.withoutSetter||H,J=function(e){return Y($,e)&&(I||"string"==typeof $[e])||(I&&Y(K,e)?$[e]=K[e]:$[e]=q("Symbol."+e)),$[e]},Q=J("toPrimitive"),ee=function(e,t){if(!C(e)||U(e))return e;var r,n=F(e,Q);if(n){if(void 0===t&&(t="default"),r=n.call(e,t),!C(r)||U(r))return r;throw TypeError("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var r,n;if("string"===t&&E(r=e.toString)&&!C(n=r.call(e)))return n;if(E(r=e.valueOf)&&!C(n=r.call(e)))return n;if("string"!==t&&E(r=e.toString)&&!C(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}(e,t)},te=function(e){var t=ee(e,"string");return U(t)?t:String(t)},re=f.document,ne=C(re)&&C(re.createElement),oe=function(e){return ne?re.createElement(e):{}},ie=!v&&!h((function(){return 7!=Object.defineProperty(oe("div"),"a",{get:function(){return 7}}).a})),ae=Object.getOwnPropertyDescriptor,ue={f:v?ae:function(e,t){if(e=P(e),t=te(t),ie)try{return ae(e,t)}catch(r){}if(Y(e,t))return m(!x.f.call(e,t),e[t])}},se=function(e){if(C(e))return e;throw TypeError(String(e)+" is not an object")},le=Object.defineProperty,ce={f:v?le:function(e,t,r){if(se(e),t=te(t),se(r),ie)try{return le(e,t,r)}catch(n){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},fe=v?function(e,t,r){return ce.f(e,t,m(1,r))}:function(e,t,r){return e[t]=r,e},he=Function.toString;E(N.inspectSource)||(N.inspectSource=function(e){return he.call(e)});var ve,de,ge,xe=N.inspectSource,me=f.WeakMap,pe=E(me)&&/native code/.test(xe(me)),ye=G("keys"),_e=function(e){return ye[e]||(ye[e]=H(e))},be={},Te=f.WeakMap;if(pe||N.state){var Pe=N.state||(N.state=new Te),Ee=Pe.get,Ce=Pe.has,we=Pe.set;ve=function(e,t){if(Ce.call(Pe,e))throw new TypeError("Object already initialized");return t.facade=e,we.call(Pe,e,t),t},de=function(e){return Ee.call(Pe,e)||{}},ge=function(e){return Ce.call(Pe,e)}}else{var Se=_e("state");be[Se]=!0,ve=function(e,t){if(Y(e,Se))throw new TypeError("Object already initialized");return t.facade=e,fe(e,Se,t),t},de=function(e){return Y(e,Se)?e[Se]:{}},ge=function(e){return Y(e,Se)}}var Ae={set:ve,get:de,has:ge,enforce:function(e){return ge(e)?de(e):ve(e,{})},getterFor:function(e){return function(t){var r;if(!C(t)||(r=de(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}}},ke=Function.prototype,Be=v&&Object.getOwnPropertyDescriptor,Re=Y(ke,"name"),Le={EXISTS:Re,PROPER:Re&&"something"===function(){}.name,CONFIGURABLE:Re&&(!v||v&&Be(ke,"name").configurable)},Oe=u((function(e){var t=Le.CONFIGURABLE,r=Ae.get,n=Ae.enforce,o=String(String).split("String");(e.exports=function(e,r,i,a){var u,s=!!a&&!!a.unsafe,l=!!a&&!!a.enumerable,c=!!a&&!!a.noTargetGet,h=a&&void 0!==a.name?a.name:r;E(i)&&("Symbol("===String(h).slice(0,7)&&(h="["+String(h).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Y(i,"name")||t&&i.name!==h)&&fe(i,"name",h),(u=n(i)).source||(u.source=o.join("string"==typeof h?h:""))),e!==f?(s?!c&&e[r]&&(l=!0):delete e[r],l?e[r]=i:fe(e,r,i)):l?e[r]=i:V(r,i)})(Function.prototype,"toString",(function(){return E(this)&&r(this).source||xe(this)}))})),Ie=Math.ceil,je=Math.floor,Ue=function(e){var t=+e;return t!=t||0===t?0:(t>0?je:Ie)(t)},De=Math.max,Me=Math.min,Fe=function(e,t){var r=Ue(e);return r<0?De(r+t,0):Me(r,t)},Ve=Math.min,Ne=function(e){return e>0?Ve(Ue(e),9007199254740991):0},Ge=function(e){return Ne(e.length)},ze=function(e){return function(t,r,n){var o,i=P(t),a=Ge(i),u=Fe(n,a);if(e&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((e||u in i)&&i[u]===r)return e||u||0;return!e&&-1}},Xe={includes:ze(!0),indexOf:ze(!1)},Ye=Xe.indexOf,We=function(e,t){var r,n=P(e),o=0,i=[];for(r in n)!Y(be,r)&&Y(n,r)&&i.push(r);for(;t.length>o;)Y(n,r=t[o++])&&(~Ye(i,r)||i.push(r));return i},Ze=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],He=Ze.concat("length","prototype"),$e={f:Object.getOwnPropertyNames||function(e){return We(e,He)}},Ke={f:Object.getOwnPropertySymbols},qe=S("Reflect","ownKeys")||function(e){var t=$e.f(se(e)),r=Ke.f;return r?t.concat(r(e)):t},Je=function(e,t){for(var r=qe(t),n=ce.f,o=ue.f,i=0;i<r.length;i++){var a=r[i];Y(e,a)||n(e,a,o(t,a))}},Qe=/#|\.prototype\./,et=function(e,t){var r=rt[tt(e)];return r==ot||r!=nt&&(E(t)?h(t):!!t)},tt=et.normalize=function(e){return String(e).replace(Qe,".").toLowerCase()},rt=et.data={},nt=et.NATIVE="N",ot=et.POLYFILL="P",it=et,at=ue.f,ut=function(e,t){var r,n,o,i,a,u=e.target,s=e.global,l=e.stat;if(r=s?f:l?f[u]||V(u,{}):(f[u]||{}).prototype)for(n in t){if(i=t[n],o=e.noTargetGet?(a=at(r,n))&&a.value:r[n],!it(s?n:u+(l?".":"#")+n,e.forced)&&void 0!==o){if(typeof i==typeof o)continue;Je(i,o)}(e.sham||o&&o.sham)&&fe(i,"sham",!0),Oe(r,n,i,e)}},st=function(e,t,r){if(M(e),void 0===t)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,o){return e.call(t,r,n,o)}}return function(){return e.apply(t,arguments)}},lt=Array.isArray||function(e){return"Array"==y(e)},ct={};ct[J("toStringTag")]="z";var ft,ht="[object z]"===String(ct),vt=J("toStringTag"),dt="Arguments"==y(function(){return arguments}()),gt=ht?y:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(r){}}(t=Object(e),vt))?r:dt?y(t):"Object"==(n=y(t))&&E(t.callee)?"Arguments":n},xt=[],mt=S("Reflect","construct"),pt=/^\s*(?:class|function)\b/,yt=pt.exec,_t=!pt.exec((function(){})),bt=function(e){if(!E(e))return!1;try{return mt(Object,xt,e),!0}catch(t){return!1}},Tt=!mt||h((function(){var e;return bt(bt.call)||!bt(Object)||!bt((function(){e=!0}))||e}))?function(e){if(!E(e))return!1;switch(gt(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return _t||!!yt.call(pt,xe(e))}:bt,Pt=J("species"),Et=function(e,t){return new(function(e){var t;return lt(e)&&(t=e.constructor,(Tt(t)&&(t===Array||lt(t.prototype))||C(t)&&null===(t=t[Pt]))&&(t=void 0)),void 0===t?Array:t}(e))(0===t?0:t)},Ct=[].push,wt=function(e){var t=1==e,r=2==e,n=3==e,o=4==e,i=6==e,a=7==e,u=5==e||i;return function(s,l,c,f){for(var h,v,d=z(s),g=b(d),x=st(l,c,3),m=Ge(g),p=0,y=f||Et,_=t?y(s,m):r||a?y(s,0):void 0;m>p;p++)if((u||p in g)&&(v=x(h=g[p],p,d),e))if(t)_[p]=v;else if(v)switch(e){case 3:return!0;case 5:return h;case 6:return p;case 2:Ct.call(_,h)}else switch(e){case 4:return!1;case 7:Ct.call(_,h)}return i?-1:n||o?o:_}},St={forEach:wt(0),map:wt(1),filter:wt(2),some:wt(3),every:wt(4),find:wt(5),findIndex:wt(6),filterReject:wt(7)},At=J("species"),kt=St.filter,Bt=(ft="filter",O>=51||!h((function(){var e=[];return(e.constructor={})[At]=function(){return{foo:1}},1!==e[ft](Boolean).foo})));ut({target:"Array",proto:!0,forced:!Bt},{filter:function(e){return kt(this,e,arguments.length>1?arguments[1]:void 0)}});var Rt=Date.prototype,Lt=Rt.toString,Ot=Rt.getTime;"Invalid Date"!=String(new Date(NaN))&&Oe(Rt,"toString",(function(){var e=Ot.call(this);return e==e?Lt.call(this):"Invalid Date"}));var It=[].slice,jt={},Ut=function(e,t,r){if(!(t in jt)){for(var n=[],o=0;o<t;o++)n[o]="a["+o+"]";jt[t]=Function("C,a","return new C("+n.join(",")+")")}return jt[t](e,r)},Dt=Function.bind||function(e){var t=M(this),r=It.call(arguments,1),n=function(){var o=r.concat(It.call(arguments));return this instanceof n?Ut(t,o.length,o):t.apply(e,o)};return C(t.prototype)&&(n.prototype=t.prototype),n};ut({target:"Function",proto:!0},{bind:Dt});var Mt=[].slice,Ft=/MSIE .\./.test(A),Vt=function(e){return function(t,r){var n=arguments.length>2,o=n?Mt.call(arguments,2):void 0;return e(n?function(){(E(t)?t:Function(t)).apply(this,o)}:t,r)}};ut({global:!0,bind:!0,forced:Ft},{setTimeout:Vt(f.setTimeout),setInterval:Vt(f.setInterval)});var Nt,Gt=Object.keys||function(e){return We(e,Ze)},zt=v?Object.defineProperties:function(e,t){se(e);for(var r,n=Gt(t),o=n.length,i=0;o>i;)ce.f(e,r=n[i++],t[r]);return e},Xt=S("document","documentElement"),Yt=_e("IE_PROTO"),Wt=function(){},Zt=function(e){return"<script>"+e+"<\/script>"},Ht=function(e){e.write(Zt("")),e.close();var t=e.parentWindow.Object;return e=null,t},$t=function(){try{Nt=new ActiveXObject("htmlfile")}catch(n){}var e,t;$t="undefined"!=typeof document?document.domain&&Nt?Ht(Nt):((t=oe("iframe")).style.display="none",Xt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Zt("document.F=Object")),e.close(),e.F):Ht(Nt);for(var r=Ze.length;r--;)delete $t.prototype[Ze[r]];return $t()};be[Yt]=!0;var Kt=Object.create||function(e,t){var r;return null!==e?(Wt.prototype=se(e),r=new Wt,Wt.prototype=null,r[Yt]=e):r=$t(),void 0===t?r:zt(r,t)},qt=J("unscopables"),Jt=Array.prototype;null==Jt[qt]&&ce.f(Jt,qt,{configurable:!0,value:Kt(null)});var Qt,er,tr,rr=function(e){Jt[qt][e]=!0},nr={},or=!h((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ir=_e("IE_PROTO"),ar=Object.prototype,ur=or?Object.getPrototypeOf:function(e){var t=z(e);if(Y(t,ir))return t[ir];var r=t.constructor;return E(r)&&t instanceof r?r.prototype:t instanceof Object?ar:null},sr=J("iterator"),lr=!1;[].keys&&("next"in(tr=[].keys())?(er=ur(ur(tr)))!==Object.prototype&&(Qt=er):lr=!0),(null==Qt||h((function(){var e={};return Qt[sr].call(e)!==e})))&&(Qt={}),E(Qt[sr])||Oe(Qt,sr,(function(){return this}));var cr={IteratorPrototype:Qt,BUGGY_SAFARI_ITERATORS:lr},fr=ce.f,hr=J("toStringTag"),vr=function(e,t,r){e&&!Y(e=r?e:e.prototype,hr)&&fr(e,hr,{configurable:!0,value:t})},dr=cr.IteratorPrototype,gr=function(){return this},xr=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{(e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(r,[]),t=r instanceof Array}catch(n){}return function(r,n){return se(r),function(e){if("object"==typeof e||E(e))return e;throw TypeError("Can't set "+String(e)+" as a prototype")}(n),t?e.call(r,n):r.__proto__=n,r}}():void 0),mr=Le.PROPER,pr=Le.CONFIGURABLE,yr=cr.IteratorPrototype,_r=cr.BUGGY_SAFARI_ITERATORS,br=J("iterator"),Tr=function(){return this},Pr=function(e,t,r,n,o,i,a){!function(e,t,r){var n=t+" Iterator";e.prototype=Kt(dr,{next:m(1,r)}),vr(e,n,!1),nr[n]=gr}(r,t,n);var u,s,l,c=function(e){if(e===o&&g)return g;if(!_r&&e in v)return v[e];switch(e){case"keys":case"values":case"entries":return function(){return new r(this,e)}}return function(){return new r(this)}},f=t+" Iterator",h=!1,v=e.prototype,d=v[br]||v["@@iterator"]||o&&v[o],g=!_r&&d||c(o),x="Array"==t&&v.entries||d;if(x&&(u=ur(x.call(new e)))!==Object.prototype&&u.next&&(ur(u)!==yr&&(xr?xr(u,yr):E(u[br])||Oe(u,br,Tr)),vr(u,f,!0)),mr&&"values"==o&&d&&"values"!==d.name&&(pr?fe(v,"name","values"):(h=!0,g=function(){return d.call(this)})),o)if(s={values:c("values"),keys:i?g:c("keys"),entries:c("entries")},a)for(l in s)(_r||h||!(l in v))&&Oe(v,l,s[l]);else ut({target:t,proto:!0,forced:_r||h},s);return v[br]!==g&&Oe(v,br,g,{name:o}),nr[t]=g,s},Er=Ae.set,Cr=Ae.getterFor("Array Iterator"),wr=Pr(Array,"Array",(function(e,t){Er(this,{type:"Array Iterator",target:P(e),index:0,kind:t})}),(function(){var e=Cr(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}}),"values");nr.Arguments=nr.Array,rr("keys"),rr("values"),rr("entries");var Sr=$e.f,Ar={}.toString,kr="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Br={f:function(e){return kr&&"[object Window]"==Ar.call(e)?function(e){try{return Sr(e)}catch(t){return kr.slice()}}(e):Sr(P(e))}},Rr=!h((function(){return Object.isExtensible(Object.preventExtensions({}))})),Lr=u((function(e){var t=ce.f,r=!1,n=H("meta"),o=0,i=Object.isExtensible||function(){return!0},a=function(e){t(e,n,{value:{objectID:"O"+o++,weakData:{}}})},u=e.exports={enable:function(){u.enable=function(){},r=!0;var e=$e.f,t=[].splice,o={};o[n]=1,e(o).length&&($e.f=function(r){for(var o=e(r),i=0,a=o.length;i<a;i++)if(o[i]===n){t.call(o,i,1);break}return o},ut({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Br.f}))},fastKey:function(e,t){if(!C(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!Y(e,n)){if(!i(e))return"F";if(!t)return"E";a(e)}return e[n].objectID},getWeakData:function(e,t){if(!Y(e,n)){if(!i(e))return!0;if(!t)return!1;a(e)}return e[n].weakData},onFreeze:function(e){return Rr&&r&&i(e)&&!Y(e,n)&&a(e),e}};be[n]=!0})),Or=(Lr.enable,Lr.fastKey,Lr.getWeakData,Lr.onFreeze,J("iterator")),Ir=Array.prototype,jr=function(e){return void 0!==e&&(nr.Array===e||Ir[Or]===e)},Ur=J("iterator"),Dr=function(e){if(null!=e)return F(e,Ur)||F(e,"@@iterator")||nr[gt(e)]},Mr=function(e,t){var r=arguments.length<2?Dr(e):t;if(M(r))return se(r.call(e));throw TypeError(String(e)+" is not iterable")},Fr=function(e,t,r){var n,o;se(e);try{if(!(n=F(e,"return"))){if("throw"===t)throw r;return r}n=n.call(e)}catch(i){o=!0,n=i}if("throw"===t)throw r;if(o)throw n;return se(n),r},Vr=function(e,t){this.stopped=e,this.result=t},Nr=function(e,t,r){var n,o,i,a,u,s,l,c=r&&r.that,f=!(!r||!r.AS_ENTRIES),h=!(!r||!r.IS_ITERATOR),v=!(!r||!r.INTERRUPTED),d=st(t,c,1+f+v),g=function(e){return n&&Fr(n,"normal",e),new Vr(!0,e)},x=function(e){return f?(se(e),v?d(e[0],e[1],g):d(e[0],e[1])):v?d(e,g):d(e)};if(h)n=e;else{if(!(o=Dr(e)))throw TypeError(String(e)+" is not iterable");if(jr(o)){for(i=0,a=Ge(e);a>i;i++)if((u=x(e[i]))&&u instanceof Vr)return u;return new Vr(!1)}n=Mr(e,o)}for(s=n.next;!(l=s.call(n)).done;){try{u=x(l.value)}catch(m){Fr(n,"throw",m)}if("object"==typeof u&&u&&u instanceof Vr)return u}return new Vr(!1)},Gr=function(e,t,r){if(e instanceof t)return e;throw TypeError("Incorrect "+(r?r+" ":"")+"invocation")},zr=J("iterator"),Xr=!1;try{var Yr=0,Wr={next:function(){return{done:!!Yr++}},return:function(){Xr=!0}};Wr[zr]=function(){return this},Array.from(Wr,(function(){throw 2}))}catch(nl){}var Zr=function(e,t){if(!t&&!Xr)return!1;var r=!1;try{var n={};n[zr]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(nl){}return r},Hr=function(e,t,r){var n,o;return xr&&E(n=t.constructor)&&n!==r&&C(o=n.prototype)&&o!==r.prototype&&xr(e,o),e},$r=function(e,t,r){for(var n in t)Oe(e,n,t[n],r);return e},Kr=J("species"),qr=function(e){var t=S(e),r=ce.f;v&&t&&!t[Kr]&&r(t,Kr,{configurable:!0,get:function(){return this}})},Jr=ce.f,Qr=Lr.fastKey,en=Ae.set,tn=Ae.getterFor,rn=(function(e,t,r){var n=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),i=n?"set":"add",a=f[e],u=a&&a.prototype,s=a,l={},c=function(e){var t=u[e];Oe(u,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(o&&!C(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return o&&!C(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(o&&!C(e))&&t.call(this,0===e?0:e)}:function(e,r){return t.call(this,0===e?0:e,r),this})};if(it(e,!E(a)||!(o||u.forEach&&!h((function(){(new a).entries().next()})))))s=r.getConstructor(t,e,n,i),Lr.enable();else if(it(e,!0)){var v=new s,d=v[i](o?{}:-0,1)!=v,g=h((function(){v.has(1)})),x=Zr((function(e){new a(e)})),m=!o&&h((function(){for(var e=new a,t=5;t--;)e[i](t,t);return!e.has(-0)}));x||((s=t((function(t,r){Gr(t,s,e);var o=Hr(new a,t,s);return null!=r&&Nr(r,o[i],{that:o,AS_ENTRIES:n}),o}))).prototype=u,u.constructor=s),(g||m)&&(c("delete"),c("has"),n&&c("get")),(m||d)&&c(i),o&&u.clear&&delete u.clear}l[e]=s,ut({global:!0,forced:s!=a},l),vr(s,e),o||r.setStrong(s,e,n)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,r,n){var o=e((function(e,i){Gr(e,o,t),en(e,{type:t,index:Kt(null),first:void 0,last:void 0,size:0}),v||(e.size=0),null!=i&&Nr(i,e[n],{that:e,AS_ENTRIES:r})})),i=tn(t),a=function(e,t,r){var n,o,a=i(e),s=u(e,t);return s?s.value=r:(a.last=s={index:o=Qr(t,!0),key:t,value:r,previous:n=a.last,next:void 0,removed:!1},a.first||(a.first=s),n&&(n.next=s),v?a.size++:e.size++,"F"!==o&&(a.index[o]=s)),e},u=function(e,t){var r,n=i(e),o=Qr(t);if("F"!==o)return n.index[o];for(r=n.first;r;r=r.next)if(r.key==t)return r};return $r(o.prototype,{clear:function(){for(var e=i(this),t=e.index,r=e.first;r;)r.removed=!0,r.previous&&(r.previous=r.previous.next=void 0),delete t[r.index],r=r.next;e.first=e.last=void 0,v?e.size=0:this.size=0},delete:function(e){var t=i(this),r=u(this,e);if(r){var n=r.next,o=r.previous;delete t.index[r.index],r.removed=!0,o&&(o.next=n),n&&(n.previous=o),t.first==r&&(t.first=n),t.last==r&&(t.last=o),v?t.size--:this.size--}return!!r},forEach:function(e){for(var t,r=i(this),n=st(e,arguments.length>1?arguments[1]:void 0,3);t=t?t.next:r.first;)for(n(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!u(this,e)}}),$r(o.prototype,r?{get:function(e){var t=u(this,e);return t&&t.value},set:function(e,t){return a(this,0===e?0:e,t)}}:{add:function(e){return a(this,e=0===e?0:e,e)}}),v&&Jr(o.prototype,"size",{get:function(){return i(this).size}}),o},setStrong:function(e,t,r){var n=t+" Iterator",o=tn(t),i=tn(n);Pr(e,t,(function(e,t){en(this,{type:n,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,r=e.last;r&&r.removed;)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?"keys"==t?{value:r.key,done:!1}:"values"==t?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),qr(t)}}),ht?{}.toString:function(){return"[object "+gt(this)+"]"});ht||Oe(Object.prototype,"toString",rn,{unsafe:!0});var nn=function(e){if("Symbol"===gt(e))throw TypeError("Cannot convert a Symbol value to a string");return String(e)},on=function(e){return function(t,r){var n,o,i=nn(T(t)),a=Ue(r),u=i.length;return a<0||a>=u?e?"":void 0:(n=i.charCodeAt(a))<55296||n>56319||a+1===u||(o=i.charCodeAt(a+1))<56320||o>57343?e?i.charAt(a):n:e?i.slice(a,a+2):o-56320+(n-55296<<10)+65536}},an={codeAt:on(!1),charAt:on(!0)},un=an.charAt,sn=Ae.set,ln=Ae.getterFor("String Iterator");Pr(String,"String",(function(e){sn(this,{type:"String Iterator",string:nn(e),index:0})}),(function(){var e,t=ln(this),r=t.string,n=t.index;return n>=r.length?{value:void 0,done:!0}:(e=un(r,n),t.index+=e.length,{value:e,done:!1})}));var cn={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},fn=oe("span").classList,hn=fn&&fn.constructor&&fn.constructor.prototype,vn=hn===Object.prototype?void 0:hn,dn=J("iterator"),gn=J("toStringTag"),xn=wr.values,mn=function(e,t){if(e){if(e[dn]!==xn)try{fe(e,dn,xn)}catch(nl){e[dn]=xn}if(e[gn]||fe(e,gn,t),cn[t])for(var r in wr)if(e[r]!==wr[r])try{fe(e,r,wr[r])}catch(nl){e[r]=wr[r]}}};for(var pn in cn)mn(f[pn]&&f[pn].prototype,pn);mn(vn,"DOMTokenList");var yn="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView,_n=function(e){if(void 0===e)return 0;var t=Ue(e),r=Ne(t);if(t!==r)throw RangeError("Wrong length or index");return r},bn=Math.abs,Tn=Math.pow,Pn=Math.floor,En=Math.log,Cn=Math.LN2,wn=function(e,t,r){var n,o,i,a=new Array(r),u=8*r-t-1,s=(1<<u)-1,l=s>>1,c=23===t?Tn(2,-24)-Tn(2,-77):0,f=e<0||0===e&&1/e<0?1:0,h=0;for((e=bn(e))!=e||Infinity===e?(o=e!=e?1:0,n=s):(n=Pn(En(e)/Cn),e*(i=Tn(2,-n))<1&&(n--,i*=2),(e+=n+l>=1?c/i:c*Tn(2,1-l))*i>=2&&(n++,i/=2),n+l>=s?(o=0,n=s):n+l>=1?(o=(e*i-1)*Tn(2,t),n+=l):(o=e*Tn(2,l-1)*Tn(2,t),n=0));t>=8;a[h++]=255&o,o/=256,t-=8);for(n=n<<t|o,u+=t;u>0;a[h++]=255&n,n/=256,u-=8);return a[--h]|=128*f,a},Sn=function(e,t){var r,n=e.length,o=8*n-t-1,i=(1<<o)-1,a=i>>1,u=o-7,s=n-1,l=e[s--],c=127&l;for(l>>=7;u>0;c=256*c+e[s],s--,u-=8);for(r=c&(1<<-u)-1,c>>=-u,u+=t;u>0;r=256*r+e[s],s--,u-=8);if(0===c)c=1-a;else{if(c===i)return r?NaN:l?-Infinity:Infinity;r+=Tn(2,t),c-=a}return(l?-1:1)*r*Tn(2,c-t)},An=function(e){for(var t=z(this),r=Ge(t),n=arguments.length,o=Fe(n>1?arguments[1]:void 0,r),i=n>2?arguments[2]:void 0,a=void 0===i?r:Fe(i,r);a>o;)t[o++]=e;return t},kn=$e.f,Bn=ce.f,Rn=Le.PROPER,Ln=Le.CONFIGURABLE,On=Ae.get,In=Ae.set,jn=f.ArrayBuffer,Un=jn,Dn=f.DataView,Mn=Dn&&Dn.prototype,Fn=Object.prototype,Vn=f.RangeError,Nn=wn,Gn=Sn,zn=function(e){return[255&e]},Xn=function(e){return[255&e,e>>8&255]},Yn=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},Wn=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},Zn=function(e){return Nn(e,23,4)},Hn=function(e){return Nn(e,52,8)},$n=function(e,t){Bn(e.prototype,t,{get:function(){return On(this)[t]}})},Kn=function(e,t,r,n){var o=_n(r),i=On(e);if(o+t>i.byteLength)throw Vn("Wrong index");var a=On(i.buffer).bytes,u=o+i.byteOffset,s=a.slice(u,u+t);return n?s:s.reverse()},qn=function(e,t,r,n,o,i){var a=_n(r),u=On(e);if(a+t>u.byteLength)throw Vn("Wrong index");for(var s=On(u.buffer).bytes,l=a+u.byteOffset,c=n(+o),f=0;f<t;f++)s[l+f]=c[i?f:t-f-1]};if(yn){var Jn=Rn&&"ArrayBuffer"!==jn.name;if(h((function(){jn(1)}))&&h((function(){new jn(-1)}))&&!h((function(){return new jn,new jn(1.5),new jn(NaN),Jn&&!Ln})))Jn&&Ln&&fe(jn,"name","ArrayBuffer");else{for(var Qn,eo=(Un=function(e){return Gr(this,Un),new jn(_n(e))}).prototype=jn.prototype,to=kn(jn),ro=0;to.length>ro;)(Qn=to[ro++])in Un||fe(Un,Qn,jn[Qn]);eo.constructor=Un}xr&&ur(Mn)!==Fn&&xr(Mn,Fn);var no=new Dn(new Un(2)),oo=Mn.setInt8;no.setInt8(0,2147483648),no.setInt8(1,2147483649),!no.getInt8(0)&&no.getInt8(1)||$r(Mn,{setInt8:function(e,t){oo.call(this,e,t<<24>>24)},setUint8:function(e,t){oo.call(this,e,t<<24>>24)}},{unsafe:!0})}else Un=function(e){Gr(this,Un,"ArrayBuffer");var t=_n(e);In(this,{bytes:An.call(new Array(t),0),byteLength:t}),v||(this.byteLength=t)},Dn=function(e,t,r){Gr(this,Dn,"DataView"),Gr(e,Un,"DataView");var n=On(e).byteLength,o=Ue(t);if(o<0||o>n)throw Vn("Wrong offset");if(o+(r=void 0===r?n-o:Ne(r))>n)throw Vn("Wrong length");In(this,{buffer:e,byteLength:r,byteOffset:o}),v||(this.buffer=e,this.byteLength=r,this.byteOffset=o)},v&&($n(Un,"byteLength"),$n(Dn,"buffer"),$n(Dn,"byteLength"),$n(Dn,"byteOffset")),$r(Dn.prototype,{getInt8:function(e){return Kn(this,1,e)[0]<<24>>24},getUint8:function(e){return Kn(this,1,e)[0]},getInt16:function(e){var t=Kn(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=Kn(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return Wn(Kn(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return Wn(Kn(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return Gn(Kn(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return Gn(Kn(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){qn(this,1,e,zn,t)},setUint8:function(e,t){qn(this,1,e,zn,t)},setInt16:function(e,t){qn(this,2,e,Xn,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){qn(this,2,e,Xn,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){qn(this,4,e,Yn,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){qn(this,4,e,Yn,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){qn(this,4,e,Zn,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){qn(this,8,e,Hn,t,arguments.length>2?arguments[2]:void 0)}});vr(Un,"ArrayBuffer"),vr(Dn,"DataView");var io={ArrayBuffer:Un,DataView:Dn},ao=function(e){if(Tt(e))return e;throw TypeError(D(e)+" is not a constructor")},uo=J("species"),so=function(e,t){var r,n=se(e).constructor;return void 0===n||null==(r=se(n)[uo])?t:ao(r)},lo=io.ArrayBuffer,co=io.DataView,fo=lo.prototype.slice,ho=h((function(){return!new lo(2).slice(1,void 0).byteLength}));ut({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:ho},{slice:function(e,t){if(void 0!==fo&&void 0===t)return fo.call(se(this),e);for(var r=se(this).byteLength,n=Fe(e,r),o=Fe(void 0===t?r:t,r),i=new(so(this,lo))(Ne(o-n)),a=new co(this),u=new co(i),s=0;n<o;)u.setUint8(s++,a.getUint8(n++));return i}});var vo,go,xo,mo=ce.f,po=f.Int8Array,yo=po&&po.prototype,_o=f.Uint8ClampedArray,bo=_o&&_o.prototype,To=po&&ur(po),Po=yo&&ur(yo),Eo=Object.prototype,Co=Eo.isPrototypeOf,wo=J("toStringTag"),So=H("TYPED_ARRAY_TAG"),Ao=H("TYPED_ARRAY_CONSTRUCTOR"),ko=yn&&!!xr&&"Opera"!==gt(f.opera),Bo=!1,Ro={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},Lo={BigInt64Array:8,BigUint64Array:8},Oo=function(e){if(!C(e))return!1;var t=gt(e);return Y(Ro,t)||Y(Lo,t)};for(vo in Ro)(xo=(go=f[vo])&&go.prototype)?fe(xo,Ao,go):ko=!1;for(vo in Lo)(xo=(go=f[vo])&&go.prototype)&&fe(xo,Ao,go);if((!ko||!E(To)||To===Function.prototype)&&(To=function(){throw TypeError("Incorrect invocation")},ko))for(vo in Ro)f[vo]&&xr(f[vo],To);if((!ko||!Po||Po===Eo)&&(Po=To.prototype,ko))for(vo in Ro)f[vo]&&xr(f[vo].prototype,Po);if(ko&&ur(bo)!==Po&&xr(bo,Po),v&&!Y(Po,wo))for(vo in Bo=!0,mo(Po,wo,{get:function(){return C(this)?this[So]:void 0}}),Ro)f[vo]&&fe(f[vo],So,vo);var Io={NATIVE_ARRAY_BUFFER_VIEWS:ko,TYPED_ARRAY_CONSTRUCTOR:Ao,TYPED_ARRAY_TAG:Bo&&So,aTypedArray:function(e){if(Oo(e))return e;throw TypeError("Target is not a typed array")},aTypedArrayConstructor:function(e){if(E(e)&&(!xr||Co.call(To,e)))return e;throw TypeError(D(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,r){if(v){if(r)for(var n in Ro){var o=f[n];if(o&&Y(o.prototype,e))try{delete o.prototype[e]}catch(nl){}}Po[e]&&!r||Oe(Po,e,r?t:ko&&yo[e]||t)}},exportTypedArrayStaticMethod:function(e,t,r){var n,o;if(v){if(xr){if(r)for(n in Ro)if((o=f[n])&&Y(o,e))try{delete o[e]}catch(nl){}if(To[e]&&!r)return;try{return Oe(To,e,r?t:ko&&To[e]||t)}catch(nl){}}for(n in Ro)!(o=f[n])||o[e]&&!r||Oe(o,e,t)}},isView:function(e){if(!C(e))return!1;var t=gt(e);return"DataView"===t||Y(Ro,t)||Y(Lo,t)},isTypedArray:Oo,TypedArray:To,TypedArrayPrototype:Po},jo=Io.NATIVE_ARRAY_BUFFER_VIEWS,Uo=f.ArrayBuffer,Do=f.Int8Array,Mo=!jo||!h((function(){Do(1)}))||!h((function(){new Do(-1)}))||!Zr((function(e){new Do,new Do(null),new Do(1.5),new Do(e)}),!0)||h((function(){return 1!==new Do(new Uo(2),1,void 0).length})),Fo=Math.floor,Vo=Number.isInteger||function(e){return!C(e)&&isFinite(e)&&Fo(e)===e},No=function(e,t){var r=function(e){var t=Ue(e);if(t<0)throw RangeError("The argument can't be less than 0");return t}(e);if(r%t)throw RangeError("Wrong offset");return r},Go=Io.aTypedArrayConstructor,zo=function(e){var t,r,n,o,i,a,u=ao(this),s=z(e),l=arguments.length,c=l>1?arguments[1]:void 0,f=void 0!==c,h=Dr(s);if(h&&!jr(h))for(a=(i=Mr(s,h)).next,s=[];!(o=a.call(i)).done;)s.push(o.value);for(f&&l>2&&(c=st(c,arguments[2],2)),r=Ge(s),n=new(Go(u))(r),t=0;r>t;t++)n[t]=f?c(s[t],t):s[t];return n};u((function(e){var t=$e.f,r=St.forEach,n=Ae.get,o=Ae.set,i=ce.f,a=ue.f,u=Math.round,s=f.RangeError,l=io.ArrayBuffer,c=io.DataView,h=Io.NATIVE_ARRAY_BUFFER_VIEWS,d=Io.TYPED_ARRAY_CONSTRUCTOR,g=Io.TYPED_ARRAY_TAG,x=Io.TypedArray,p=Io.TypedArrayPrototype,y=Io.aTypedArrayConstructor,_=Io.isTypedArray,b=function(e,t){for(var r=0,n=t.length,o=new(y(e))(n);n>r;)o[r]=t[r++];return o},T=function(e,t){i(e,t,{get:function(){return n(this)[t]}})},P=function(e){var t;return e instanceof l||"ArrayBuffer"==(t=gt(e))||"SharedArrayBuffer"==t},E=function(e,t){return _(e)&&!U(t)&&t in e&&Vo(+t)&&t>=0},w=function(e,t){return t=te(t),E(e,t)?m(2,e[t]):a(e,t)},S=function(e,t,r){return t=te(t),!(E(e,t)&&C(r)&&Y(r,"value"))||Y(r,"get")||Y(r,"set")||r.configurable||Y(r,"writable")&&!r.writable||Y(r,"enumerable")&&!r.enumerable?i(e,t,r):(e[t]=r.value,e)};v?(h||(ue.f=w,ce.f=S,T(p,"buffer"),T(p,"byteOffset"),T(p,"byteLength"),T(p,"length")),ut({target:"Object",stat:!0,forced:!h},{getOwnPropertyDescriptor:w,defineProperty:S}),e.exports=function(e,a,v){var m=e.match(/\d+$/)[0]/8,y=e+(v?"Clamped":"")+"Array",T="get"+e,E="set"+e,w=f[y],S=w,A=S&&S.prototype,k={},B=function(e,t){i(e,t,{get:function(){return function(e,t){var r=n(e);return r.view[T](t*m+r.byteOffset,!0)}(this,t)},set:function(e){return function(e,t,r){var o=n(e);v&&(r=(r=u(r))<0?0:r>255?255:255&r),o.view[E](t*m+o.byteOffset,r,!0)}(this,t,e)},enumerable:!0})};h?Mo&&(S=a((function(e,t,r,n){return Gr(e,S,y),Hr(C(t)?P(t)?void 0!==n?new w(t,No(r,m),n):void 0!==r?new w(t,No(r,m)):new w(t):_(t)?b(S,t):zo.call(S,t):new w(_n(t)),e,S)})),xr&&xr(S,x),r(t(w),(function(e){e in S||fe(S,e,w[e])})),S.prototype=A):(S=a((function(e,t,r,n){Gr(e,S,y);var i,a,u,f=0,h=0;if(C(t)){if(!P(t))return _(t)?b(S,t):zo.call(S,t);i=t,h=No(r,m);var v=t.byteLength;if(void 0===n){if(v%m)throw s("Wrong length");if((a=v-h)<0)throw s("Wrong length")}else if((a=Ne(n)*m)+h>v)throw s("Wrong length");u=a/m}else u=_n(t),i=new l(a=u*m);for(o(e,{buffer:i,byteOffset:h,byteLength:a,length:u,view:new c(i)});f<u;)B(e,f++)})),xr&&xr(S,x),A=S.prototype=Kt(p)),A.constructor!==S&&fe(A,"constructor",S),fe(A,d,S),g&&fe(A,g,y),k[y]=S,ut({global:!0,forced:S!=w,sham:!h},k),"BYTES_PER_ELEMENT"in S||fe(S,"BYTES_PER_ELEMENT",m),"BYTES_PER_ELEMENT"in A||fe(A,"BYTES_PER_ELEMENT",m),qr(y)}):e.exports=function(){}}))("Float32",(function(e){return function(t,r,n){return e(this,t,r,n)}}));var Xo=Math.min,Yo=[].copyWithin||function(e,t){var r=z(this),n=Ge(r),o=Fe(e,n),i=Fe(t,n),a=arguments.length>2?arguments[2]:void 0,u=Xo((void 0===a?n:Fe(a,n))-i,n-o),s=1;for(i<o&&o<i+u&&(s=-1,i+=u-1,o+=u-1);u-- >0;)i in r?r[o]=r[i]:delete r[o],o+=s,i+=s;return r},Wo=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("copyWithin",(function(e,t){return Yo.call(Wo(this),e,t,arguments.length>2?arguments[2]:void 0)}));var Zo=St.every,Ho=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("every",(function(e){return Zo(Ho(this),e,arguments.length>1?arguments[1]:void 0)}));var $o=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("fill",(function(e){return An.apply($o(this),arguments)}));var Ko=Io.TYPED_ARRAY_CONSTRUCTOR,qo=Io.aTypedArrayConstructor,Jo=function(e){return qo(so(e,e[Ko]))},Qo=function(e,t){return function(e,t){for(var r=0,n=t.length,o=new e(n);n>r;)o[r]=t[r++];return o}(Jo(e),t)},ei=St.filter,ti=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("filter",(function(e){var t=ei(ti(this),e,arguments.length>1?arguments[1]:void 0);return Qo(this,t)}));var ri=St.find,ni=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("find",(function(e){return ri(ni(this),e,arguments.length>1?arguments[1]:void 0)}));var oi=St.findIndex,ii=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("findIndex",(function(e){return oi(ii(this),e,arguments.length>1?arguments[1]:void 0)}));var ai=St.forEach,ui=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("forEach",(function(e){ai(ui(this),e,arguments.length>1?arguments[1]:void 0)}));var si=Xe.includes,li=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("includes",(function(e){return si(li(this),e,arguments.length>1?arguments[1]:void 0)}));var ci=Xe.indexOf,fi=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("indexOf",(function(e){return ci(fi(this),e,arguments.length>1?arguments[1]:void 0)}));var hi=Le.PROPER,vi=J("iterator"),di=f.Uint8Array,gi=wr.values,xi=wr.keys,mi=wr.entries,pi=Io.aTypedArray,yi=Io.exportTypedArrayMethod,_i=di&&di.prototype[vi],bi=!!_i&&"values"===_i.name,Ti=function(){return gi.call(pi(this))};yi("entries",(function(){return mi.call(pi(this))})),yi("keys",(function(){return xi.call(pi(this))})),yi("values",Ti,hi&&!bi),yi(vi,Ti,hi&&!bi);var Pi=Io.aTypedArray,Ei=[].join;(0,Io.exportTypedArrayMethod)("join",(function(e){return Ei.apply(Pi(this),arguments)}));var Ci=function(e,t){var r=[][e];return!!r&&h((function(){r.call(null,t||function(){throw 1},1)}))},wi=Math.min,Si=[].lastIndexOf,Ai=!!Si&&1/[1].lastIndexOf(1,-0)<0,ki=Ci("lastIndexOf"),Bi=Ai||!ki?function(e){if(Ai)return Si.apply(this,arguments)||0;var t=P(this),r=Ge(t),n=r-1;for(arguments.length>1&&(n=wi(n,Ue(arguments[1]))),n<0&&(n=r+n);n>=0;n--)if(n in t&&t[n]===e)return n||0;return-1}:Si,Ri=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("lastIndexOf",(function(e){return Bi.apply(Ri(this),arguments)}));var Li=St.map,Oi=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("map",(function(e){return Li(Oi(this),e,arguments.length>1?arguments[1]:void 0,(function(e,t){return new(Jo(e))(t)}))}));var Ii=function(e){return function(t,r,n,o){M(r);var i=z(t),a=b(i),u=Ge(i),s=e?u-1:0,l=e?-1:1;if(n<2)for(;;){if(s in a){o=a[s],s+=l;break}if(s+=l,e?s<0:u<=s)throw TypeError("Reduce of empty array with no initial value")}for(;e?s>=0:u>s;s+=l)s in a&&(o=r(o,a[s],s,i));return o}},ji={left:Ii(!1),right:Ii(!0)},Ui=ji.left,Di=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("reduce",(function(e){return Ui(Di(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var Mi=ji.right,Fi=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("reduceRight",(function(e){return Mi(Fi(this),e,arguments.length,arguments.length>1?arguments[1]:void 0)}));var Vi=Io.aTypedArray,Ni=Io.exportTypedArrayMethod,Gi=Math.floor;Ni("reverse",(function(){for(var e,t=Vi(this).length,r=Gi(t/2),n=0;n<r;)e=this[n],this[n++]=this[--t],this[t]=e;return this}));var zi=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("set",(function(e){zi(this);var t=No(arguments.length>1?arguments[1]:void 0,1),r=this.length,n=z(e),o=Ge(n),i=0;if(o+t>r)throw RangeError("Wrong length");for(;i<o;)this[t+i]=n[i++]}),h((function(){new Int8Array(1).set({})})));var Xi=Io.aTypedArray,Yi=[].slice;(0,Io.exportTypedArrayMethod)("slice",(function(e,t){for(var r=Yi.call(Xi(this),e,t),n=Jo(this),o=0,i=r.length,a=new n(i);i>o;)a[o]=r[o++];return a}),h((function(){new Int8Array(1).slice()})));var Wi=St.some,Zi=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("some",(function(e){return Wi(Zi(this),e,arguments.length>1?arguments[1]:void 0)}));var Hi=Math.floor,$i=function(e,t){var r=e.length,n=Hi(r/2);return r<8?Ki(e,t):qi($i(e.slice(0,n),t),$i(e.slice(n),t),t)},Ki=function(e,t){for(var r,n,o=e.length,i=1;i<o;){for(n=i,r=e[i];n&&t(e[n-1],r)>0;)e[n]=e[--n];n!==i++&&(e[n]=r)}return e},qi=function(e,t,r){for(var n=e.length,o=t.length,i=0,a=0,u=[];i<n||a<o;)i<n&&a<o?u.push(r(e[i],t[a])<=0?e[i++]:t[a++]):u.push(i<n?e[i++]:t[a++]);return u},Ji=$i,Qi=A.match(/firefox\/(\d+)/i),ea=!!Qi&&+Qi[1],ta=/MSIE|Trident/.test(A),ra=A.match(/AppleWebKit\/(\d+)\./),na=!!ra&&+ra[1],oa=Io.aTypedArray,ia=Io.exportTypedArrayMethod,aa=f.Uint16Array,ua=aa&&aa.prototype.sort,sa=!!ua&&!h((function(){var e=new aa(2);e.sort(null),e.sort({})})),la=!!ua&&!h((function(){if(O)return O<74;if(ea)return ea<67;if(ta)return!0;if(na)return na<602;var e,t,r=new aa(516),n=Array(516);for(e=0;e<516;e++)t=e%4,r[e]=515-e,n[e]=e-2*t+3;for(r.sort((function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(r[e]!==n[e])return!0}));ia("sort",(function(e){if(void 0!==e&&M(e),la)return ua.call(this,e);oa(this);var t,r=Ge(this),n=Array(r);for(t=0;t<r;t++)n[t]=this[t];for(n=Ji(this,function(e){return function(t,r){return void 0!==e?+e(t,r)||0:r!=r?-1:t!=t?1:0===t&&0===r?1/t>0&&1/r<0?1:-1:t>r}}(e)),t=0;t<r;t++)this[t]=n[t];return this}),!la||sa);var ca=Io.aTypedArray;(0,Io.exportTypedArrayMethod)("subarray",(function(e,t){var r=ca(this),n=r.length,o=Fe(e,n);return new(Jo(r))(r.buffer,r.byteOffset+o*r.BYTES_PER_ELEMENT,Ne((void 0===t?n:Fe(t,n))-o))}));var fa=f.Int8Array,ha=Io.aTypedArray,va=Io.exportTypedArrayMethod,da=[].toLocaleString,ga=[].slice,xa=!!fa&&h((function(){da.call(new fa(1))}));va("toLocaleString",(function(){return da.apply(xa?ga.call(ha(this)):ha(this),arguments)}),h((function(){return[1,2].toLocaleString()!=new fa([1,2]).toLocaleString()}))||!h((function(){fa.prototype.toLocaleString.call([1,2])})));var ma=Io.exportTypedArrayMethod,pa=f.Uint8Array,ya=pa&&pa.prototype||{},_a=[].toString,ba=[].join;h((function(){_a.call({})}))&&(_a=function(){return ba.call(this)});var Ta=ya.toString!=_a;ma("toString",_a,Ta);var Pa=function(){var e=se(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t},Ea=f.RegExp,Ca={UNSUPPORTED_Y:h((function(){var e=Ea("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),BROKEN_CARET:h((function(){var e=Ea("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},wa=f.RegExp,Sa=h((function(){var e=wa(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),Aa=f.RegExp,ka=h((function(){var e=Aa("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),Ba=Ae.get,Ra=RegExp.prototype.exec,La=G("native-string-replace",String.prototype.replace),Oa=Ra,Ia=function(){var e=/a/,t=/b*/g;return Ra.call(e,"a"),Ra.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),ja=Ca.UNSUPPORTED_Y||Ca.BROKEN_CARET,Ua=void 0!==/()??/.exec("")[1];(Ia||Ua||ja||Sa||ka)&&(Oa=function(e){var t,r,n,o,i,a,u,s=this,l=Ba(s),c=nn(e),f=l.raw;if(f)return f.lastIndex=s.lastIndex,t=Oa.call(f,c),s.lastIndex=f.lastIndex,t;var h=l.groups,v=ja&&s.sticky,d=Pa.call(s),g=s.source,x=0,m=c;if(v&&(-1===(d=d.replace("y","")).indexOf("g")&&(d+="g"),m=c.slice(s.lastIndex),s.lastIndex>0&&(!s.multiline||s.multiline&&"\n"!==c.charAt(s.lastIndex-1))&&(g="(?: "+g+")",m=" "+m,x++),r=new RegExp("^(?:"+g+")",d)),Ua&&(r=new RegExp("^"+g+"$(?!\\s)",d)),Ia&&(n=s.lastIndex),o=Ra.call(v?r:s,m),v?o?(o.input=o.input.slice(x),o[0]=o[0].slice(x),o.index=s.lastIndex,s.lastIndex+=o[0].length):s.lastIndex=0:Ia&&o&&(s.lastIndex=s.global?o.index+o[0].length:n),Ua&&o&&o.length>1&&La.call(o[0],r,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&h)for(o.groups=a=Kt(null),i=0;i<h.length;i++)a[(u=h[i])[0]]=o[u[1]];return o});var Da=Oa;ut({target:"RegExp",proto:!0,forced:/./.exec!==Da},{exec:Da});var Ma=J("species"),Fa=RegExp.prototype,Va=an.charAt,Na=function(e,t,r){return t+(r?Va(e,t).length:1)},Ga=Math.floor,za="".replace,Xa=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ya=/\$([$&'`]|\d{1,2})/g,Wa=function(e,t,r,n,o,i){var a=r+e.length,u=n.length,s=Ya;return void 0!==o&&(o=z(o),s=Xa),za.call(i,s,(function(i,s){var l;switch(s.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(a);case"<":l=o[s.slice(1,-1)];break;default:var c=+s;if(0===c)return i;if(c>u){var f=Ga(c/10);return 0===f?i:f<=u?void 0===n[f-1]?s.charAt(1):n[f-1]+s.charAt(1):i}l=n[c-1]}return void 0===l?"":l}))},Za=function(e,t){var r=e.exec;if(E(r)){var n=r.call(e,t);return null!==n&&se(n),n}if("RegExp"===y(e))return Da.call(e,t);throw TypeError("RegExp#exec called on incompatible receiver")},Ha=J("replace"),$a=Math.max,Ka=Math.min,qa="$0"==="a".replace(/./,"$0"),Ja=!!/./[Ha]&&""===/./[Ha]("a","$0");!function(e,t,r,n){var o=J(e),i=!h((function(){var t={};return t[o]=function(){return 7},7!=""[e](t)})),a=i&&!h((function(){var t=!1,r=/a/;return"split"===e&&((r={}).constructor={},r.constructor[Ma]=function(){return r},r.flags="",r[o]=/./[o]),r.exec=function(){return t=!0,null},r[o](""),!t}));if(!i||!a||r){var u=/./[o],s=t(o,""[e],(function(e,t,r,n,o){var a=t.exec;return a===Da||a===Fa.exec?i&&!o?{done:!0,value:u.call(t,r,n)}:{done:!0,value:e.call(r,t,n)}:{done:!1}}));Oe(String.prototype,e,s[0]),Oe(Fa,o,s[1])}n&&fe(Fa[o],"sham",!0)}("replace",(function(e,t,r){var n=Ja?"$":"$0";return[function(e,r){var n=T(this),o=null==e?void 0:F(e,Ha);return o?o.call(e,n,r):t.call(nn(n),e,r)},function(e,o){var i=se(this),a=nn(e);if("string"==typeof o&&-1===o.indexOf(n)&&-1===o.indexOf("$<")){var u=r(t,i,a,o);if(u.done)return u.value}var s=E(o);s||(o=nn(o));var l=i.global;if(l){var c=i.unicode;i.lastIndex=0}for(var f=[];;){var h=Za(i,a);if(null===h)break;if(f.push(h),!l)break;""===nn(h[0])&&(i.lastIndex=Na(a,Ne(i.lastIndex),c))}for(var v,d="",g=0,x=0;x<f.length;x++){h=f[x];for(var m=nn(h[0]),p=$a(Ka(Ue(h.index),a.length),0),y=[],_=1;_<h.length;_++)y.push(void 0===(v=h[_])?v:String(v));var b=h.groups;if(s){var T=[m].concat(y,p,a);void 0!==b&&T.push(b);var P=nn(o.apply(void 0,T))}else P=Wa(m,a,p,y,b,o);p>=g&&(d+=a.slice(g,p)+P,g=p+m.length)}return d+a.slice(g)}]}),!!h((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!qa||Ja);var Qa=Le.PROPER,eu=RegExp.prototype,tu=eu.toString,ru=h((function(){return"/a/b"!=tu.call({source:"a",flags:"b"})})),nu=Qa&&"toString"!=tu.name;(ru||nu)&&Oe(RegExp.prototype,"toString",(function(){var e=se(this),t=nn(e.source),r=e.flags;return"/"+t+"/"+nn(void 0===r&&e instanceof RegExp&&!("flags"in eu)?Pa.call(e):r)}),{unsafe:!0});var ou=J("match"),iu=ce.f,au=$e.f,uu=Ae.enforce,su=J("match"),lu=f.RegExp,cu=lu.prototype,fu=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,hu=/a/g,vu=/a/g,du=new lu(hu)!==hu,gu=Ca.UNSUPPORTED_Y,xu=v&&(!du||gu||Sa||ka||h((function(){return vu[su]=!1,lu(hu)!=hu||lu(vu)==vu||"/a/i"!=lu(hu,"i")})));if(it("RegExp",xu)){for(var mu=function(e,t){var r,n,o,i,a,u,s,l,c=this instanceof mu,f=C(r=e)&&(void 0!==(n=r[ou])?!!n:"RegExp"==y(r)),h=void 0===t,v=[],d=e;if(!c&&f&&h&&e.constructor===mu)return e;if((f||e instanceof mu)&&(e=e.source,h&&(t="flags"in d?d.flags:Pa.call(d))),e=void 0===e?"":nn(e),t=void 0===t?"":nn(t),d=e,Sa&&"dotAll"in hu&&(i=!!t&&t.indexOf("s")>-1)&&(t=t.replace(/s/g,"")),o=t,gu&&"sticky"in hu&&(a=!!t&&t.indexOf("y")>-1)&&(t=t.replace(/y/g,"")),ka&&(e=(u=function(e){for(var t,r=e.length,n=0,o="",i=[],a={},u=!1,s=!1,l=0,c="";n<=r;n++){if("\\"===(t=e.charAt(n)))t+=e.charAt(++n);else if("]"===t)u=!1;else if(!u)switch(!0){case"["===t:u=!0;break;case"("===t:fu.test(e.slice(n+1))&&(n+=2,s=!0),o+=t,l++;continue;case">"===t&&s:if(""===c||Y(a,c))throw new SyntaxError("Invalid capture group name");a[c]=!0,i.push([c,l]),s=!1,c="";continue}s?c+=t:o+=t}return[o,i]}(e))[0],v=u[1]),s=Hr(lu(e,t),c?this:cu,mu),(i||a||v.length)&&(l=uu(s),i&&(l.dotAll=!0,l.raw=mu(function(e){for(var t,r=e.length,n=0,o="",i=!1;n<=r;n++)"\\"!==(t=e.charAt(n))?i||"."!==t?("["===t?i=!0:"]"===t&&(i=!1),o+=t):o+="[\\s\\S]":o+=t+e.charAt(++n);return o}(e),o)),a&&(l.sticky=!0),v.length&&(l.groups=v)),e!==d)try{fe(s,"source",""===d?"(?:)":d)}catch(nl){}return s},pu=function(e){e in mu||iu(mu,e,{configurable:!0,get:function(){return lu[e]},set:function(t){lu[e]=t}})},yu=au(lu),_u=0;yu.length>_u;)pu(yu[_u++]);cu.constructor=mu,mu.prototype=cu,Oe(f,"RegExp",mu)}qr("RegExp");var bu=[].join,Tu=b!=Object,Pu=Ci("join",",");ut({target:"Array",proto:!0,forced:Tu||!Pu},{join:function(e){return bu.call(P(this),void 0===e?",":e)}});var Eu=St.forEach,Cu=Ci("forEach")?[].forEach:function(e){return Eu(this,e,arguments.length>1?arguments[1]:void 0)};ut({target:"Array",proto:!0,forced:[].forEach!=Cu},{forEach:Cu});var wu=function(e){if(e&&e.forEach!==Cu)try{fe(e,"forEach",Cu)}catch(nl){e.forEach=Cu}};for(var Su in cn)cn[Su]&&wu(f[Su]&&f[Su].prototype);function Au(e,t,r){var n=function(e,t,r){var n=new RegExp("\\b".concat(t," \\w+ (\\w+)"),"ig");e.replace(n,(function(e,t){return r[t]=0,e}))},o=function(e,t,r){var n=e.createShader(r);return e.shaderSource(n,t),e.compileShader(n),e.getShaderParameter(n,e.COMPILE_STATUS)?n:(console.log(e.getShaderInfoLog(n)),null)};this.uniform={},this.attribute={};var i=o(e,t,e.VERTEX_SHADER),a=o(e,r,e.FRAGMENT_SHADER);for(var u in this.id=e.createProgram(),e.attachShader(this.id,i),e.attachShader(this.id,a),e.linkProgram(this.id),e.getProgramParameter(this.id,e.LINK_STATUS)||console.log(e.getProgramInfoLog(this.id)),e.useProgram(this.id),n(t,"attribute",this.attribute),this.attribute)this.attribute[u]=e.getAttribLocation(this.id,u);for(var s in n(t,"uniform",this.uniform),n(r,"uniform",this.uniform),this.uniform)this.uniform[s]=e.getUniformLocation(this.id,s)}function ku(e,t,r,n){for(var o=[],i=0;i<t.length;++i)o.push(Ru(e,t[i],e[Bu[i]]));return function(e,t,r,n){var o=e.createProgram();t.forEach((function(t){e.attachShader(o,t)})),r&&r.forEach((function(t,r){e.bindAttribLocation(o,n?n[r]:r,t)}));if(e.linkProgram(o),!e.getProgramParameter(o,e.LINK_STATUS))return e.deleteProgram(o),null;return o}(e,o,r,n)}wu(vn);var Bu=["VERTEX_SHADER","FRAGMENT_SHADER"];function Ru(e,t,r){var n=e.createShader(r);return e.shaderSource(n,t),e.compileShader(n),e.getShaderParameter(n,e.COMPILE_STATUS)?n:(e.deleteShader(n),null)}var Lu,Ou,Iu,ju,Uu=function(){function e(t){n(this,e),this.canvas=t.canvas,this.video=t.video,this.width=t.width||640,this.height=t.height||480,this.vertexBuffer=null,this.currentProgram=null,this.applied=!1,this.beautyParams={beauty:.5,brightness:.5,ruddy:.5},this.gl=this.createGL(t.canvas),this.sourceTexture=this.gl.createTexture()}return i(e,[{key:"setRect",value:function(e,t){this.width=e,this.height=t}},{key:"apply",value:function(e){if(!this.vertexBuffer){var t=new Float32Array([-1,-1,0,1,1,-1,1,1,-1,1,0,0,-1,1,0,0,1,-1,1,1,1,1,1,0]);this.vertexBuffer=this.gl.createBuffer(),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.vertexBuffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,t,this.gl.STATIC_DRAW),this.gl.pixelStorei(this.gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0)}this.gl.viewport(0,0,this.width,this.height),this.gl.bindTexture(this.gl.TEXTURE_2D,this.sourceTexture),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_S,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_T,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MIN_FILTER,this.gl.NEAREST),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MAG_FILTER,this.gl.NEAREST),this.applied?this.gl.texSubImage2D(this.gl.TEXTURE_2D,0,0,0,this.gl.RGB,this.gl.UNSIGNED_BYTE,e):(this.gl.texImage2D(this.gl.TEXTURE_2D,0,this.gl.RGB,this.gl.RGB,this.gl.UNSIGNED_BYTE,e),this.applied=!0);var r=this.beautyParams,n=r.beauty,o=r.brightness,i=r.ruddy,a=2/this.width,u=2/this.height,s=this.compileBeautyShader();this.gl.uniform2f(s.uniform.singleStepOffset,a,u);var l=new Float32Array([1-.8*n,1-.6*n,.1+.45*i,.1+.45*i]);this.gl.uniform4fv(s.uniform.params,l),this.gl.uniform1f(s.uniform.brightness,.12*o);this.gl.bindTexture(this.gl.TEXTURE_2D,this.sourceTexture),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,null),this.gl.uniform1f(this.currentProgram.uniform.flipY,1),this.gl.drawArrays(this.gl.TRIANGLES,0,6)}},{key:"compileBeautyShader",value:function(){if(this.currentProgram)return this.currentProgram;this.currentProgram=new Au(this.gl,["precision highp float;","attribute vec2 pos;","attribute vec2 uv;","varying vec2 vUv;","uniform float flipY;","void main(void) {","vUv = uv;","gl_Position = vec4(pos.x, pos.y*flipY, 0.0, 1.);","}"].join("\n"),["precision highp float;","uniform vec2 singleStepOffset;","uniform sampler2D texture;","uniform vec4 params;","uniform float brightness;","varying vec2 vUv;","const highp vec3 W = vec3(0.299,0.587,0.114);","const mat3 saturateMatrix = mat3(1.1102,-0.0598,-0.061,-0.0774,1.0826,-0.1186,-0.0228,-0.0228,1.1772);","vec2 blurCoordinates[24];","float hardLight(float color){","if(color <= 0.5){","color = color * color * 2.0;","} else {","color = 1.0 - ((1.0 - color)*(1.0 - color) * 2.0);","}","return color;","}","void main(){","vec3 centralColor = texture2D(texture, vUv).rgb;","blurCoordinates[0] = vUv.xy + singleStepOffset * vec2(0.0, -10.0);","blurCoordinates[1] = vUv.xy + singleStepOffset * vec2(0.0, 10.0);","blurCoordinates[2] = vUv.xy + singleStepOffset * vec2(-10.0, 0.0);","blurCoordinates[3] = vUv.xy + singleStepOffset * vec2(10.0, 0.0);","blurCoordinates[4] = vUv.xy + singleStepOffset * vec2(5.0, -8.0);","blurCoordinates[5] = vUv.xy + singleStepOffset * vec2(5.0, 8.0);","blurCoordinates[6] = vUv.xy + singleStepOffset * vec2(-5.0, 8.0);","blurCoordinates[7] = vUv.xy + singleStepOffset * vec2(-5.0, -8.0);","blurCoordinates[8] = vUv.xy + singleStepOffset * vec2(8.0, -5.0);","blurCoordinates[9] = vUv.xy + singleStepOffset * vec2(8.0, 5.0);","blurCoordinates[10] = vUv.xy + singleStepOffset * vec2(-8.0, 5.0);","blurCoordinates[11] = vUv.xy + singleStepOffset * vec2(-8.0, -5.0);","blurCoordinates[12] = vUv.xy + singleStepOffset * vec2(0.0, -6.0);","blurCoordinates[13] = vUv.xy + singleStepOffset * vec2(0.0, 6.0);","blurCoordinates[14] = vUv.xy + singleStepOffset * vec2(6.0, 0.0);","blurCoordinates[15] = vUv.xy + singleStepOffset * vec2(-6.0, 0.0);","blurCoordinates[16] = vUv.xy + singleStepOffset * vec2(-4.0, -4.0);","blurCoordinates[17] = vUv.xy + singleStepOffset * vec2(-4.0, 4.0);","blurCoordinates[18] = vUv.xy + singleStepOffset * vec2(4.0, -4.0);","blurCoordinates[19] = vUv.xy + singleStepOffset * vec2(4.0, 4.0);","blurCoordinates[20] = vUv.xy + singleStepOffset * vec2(-2.0, -2.0);","blurCoordinates[21] = vUv.xy + singleStepOffset * vec2(-2.0, 2.0);","blurCoordinates[22] = vUv.xy + singleStepOffset * vec2(2.0, -2.0);","blurCoordinates[23] = vUv.xy + singleStepOffset * vec2(2.0, 2.0);","float sampleColor = centralColor.g * 22.0;","sampleColor += texture2D(texture, blurCoordinates[0]).g;","sampleColor += texture2D(texture, blurCoordinates[1]).g;","sampleColor += texture2D(texture, blurCoordinates[2]).g;","sampleColor += texture2D(texture, blurCoordinates[3]).g;","sampleColor += texture2D(texture, blurCoordinates[4]).g;","sampleColor += texture2D(texture, blurCoordinates[5]).g;","sampleColor += texture2D(texture, blurCoordinates[6]).g;","sampleColor += texture2D(texture, blurCoordinates[7]).g;","sampleColor += texture2D(texture, blurCoordinates[8]).g;","sampleColor += texture2D(texture, blurCoordinates[9]).g;","sampleColor += texture2D(texture, blurCoordinates[10]).g;","sampleColor += texture2D(texture, blurCoordinates[11]).g;","sampleColor += texture2D(texture, blurCoordinates[12]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[13]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[14]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[15]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[16]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[17]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[18]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[19]).g * 2.0;","sampleColor += texture2D(texture, blurCoordinates[20]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[21]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[22]).g * 3.0;","sampleColor += texture2D(texture, blurCoordinates[23]).g * 3.0;","sampleColor = sampleColor / 62.0;","float highPass = centralColor.g - sampleColor + 0.5;","for(int i = 0; i < 5;i++){","highPass = hardLight(highPass);","}","float luminance = dot(centralColor, W);","float alpha = pow(luminance, params.r);","vec3 smoothColor = centralColor + (centralColor-vec3(highPass))*alpha*0.1;","smoothColor.r = clamp(pow(smoothColor.r, params.g),0.0,1.0);","smoothColor.g = clamp(pow(smoothColor.g, params.g),0.0,1.0);","smoothColor.b = clamp(pow(smoothColor.b, params.g),0.0,1.0);","vec3 screen = vec3(1.0) - (vec3(1.0)-smoothColor) * (vec3(1.0)-centralColor);","vec3 lighten = max(smoothColor, centralColor);","vec3 softLight = 2.0 * centralColor*smoothColor + centralColor*centralColor - 2.0 * centralColor*centralColor * smoothColor;","gl_FragColor = vec4(mix(centralColor, screen, alpha), 1.0);","gl_FragColor.rgb = mix(gl_FragColor.rgb, lighten, alpha);","gl_FragColor.rgb = mix(gl_FragColor.rgb, softLight, params.b);","vec3 satColor = gl_FragColor.rgb * saturateMatrix;","gl_FragColor.rgb = mix(gl_FragColor.rgb, satColor, params.a);","gl_FragColor.rgb = vec3(gl_FragColor.rgb + vec3(brightness));","}"].join("\n"));var e=Float32Array.BYTES_PER_ELEMENT,t=4*e;return this.gl.enableVertexAttribArray(this.currentProgram.attribute.pos),this.gl.vertexAttribPointer(this.currentProgram.attribute.pos,2,this.gl.FLOAT,!1,t,0),this.gl.enableVertexAttribArray(this.currentProgram.attribute.uv),this.gl.vertexAttribPointer(this.currentProgram.attribute.uv,2,this.gl.FLOAT,!1,t,2*e),this.currentProgram}},{key:"createGL",value:function(e){var t=e.getContext("webgl2");if(t||e.getContext("experimental-webgl",{preserveDrawingBuffer:!0}),!t)throw"Couldn't get WebGL context";return t}},{key:"setBeautyParams",value:function(e){this.beautyParams=e}},{key:"reset",value:function(){this.applied=!1}}]),e}(),Du=function(){function e(t){n(this,e),this.canvas=t.canvas,this.video=t.video,this.width=t.width||640,this.height=t.height||480,this.gl=this.createGL(t.canvas),this.vao=this.gl.createVertexArray(),this.positionBuffer=this.gl.createBuffer(),this.sourceTexture=this.gl.createTexture(),this.program=null,this.blurParams={resolutionLocation:null,imageLocation0:null,imageLocation1:null},this.virtualParams={resolutionLocation:null,imageLocation0:null,imageLocation1:null,imageLocation2:null,imageOldLocation0:null,imageOldLocation1:null,imageOldLocation2:null,imageOldLocation3:null,imageOldLocation4:null,imageOldLocation5:null},this.virtualLocation={postProcessTypeLocation:null,sigmaLocation:null,bSigmaLocation:null,mSizeLocation:null,overlayTypeLocation:null,hardThresholdLocation:null,sigmoidClipLocation:null},this.maskBuffer=[],this.frameBuffer=[],this.virtualOption={postProcess:2,sigma:100,bSigma:10,kSize:19,overlayType:0,hardThreshold:.5,sigmoidClip:1},this.type=0,this.frameRate=0,this.rafId=null,this.timeoutId=null,this.img=null,this.isRender=!1}var t;return i(e,[{key:"initVirtualParams",value:function(e){var t=e.img,r=e.type;this.type="blur"===r?0:1,this.setBackground(t),0===this.type?this.initBackgroundBlurProgram():1===this.type&&this.initVirtualBackgroundProgram()}},{key:"setBackground",value:function(e){this.img=e}},{key:"setRect",value:function(e,t){this.width=e,this.height=t}},{key:"createGL",value:function(e){var t=e.getContext("webgl2");if(t||e.getContext("experimental-webgl",{preserveDrawingBuffer:!0}),!t)throw"Couldn't get WebGL context";return t}},{key:"setRectangle",value:function(e,t,r,n,o){var i=t,a=t+n,u=r,s=r+o;e.bufferData(e.ARRAY_BUFFER,new Float32Array([i,u,a,u,i,s,i,s,a,u,a,s]),e.STATIC_DRAW)}},{key:"startVirtual",value:function(e){this.frameRate=e;var t=window.selfieSegmentation;t.setOptions({selfieMode:!1,modelSelection:0,effect:"background"}),0===this.type?t.onResults(this.onBlurResult.bind(this)):t.onResults(this.onVirtualResults.bind(this)),this.startTime=(new Date).getTime(),this.isRender=!0,this.installEvents()}},{key:"render",value:(t=r(regeneratorRuntime.mark((function e(){var t,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isRender){e.next=2;break}return e.abrupt("return");case 2:if(!((t=(new Date).getTime())-this.startTime>1e3/this.frameRate)){e.next=8;break}if(this.video.readyState!==this.video.HAVE_ENOUGH_DATA){e.next=7;break}return e.next=7,window.selfieSegmentation.send({image:this.video});case 7:this.startTime=t;case 8:document.hidden?(this.timeoutId&&clearTimeout(this.timeoutId),this.rafId&&cancelAnimationFrame(this.rafId),this.timeoutId=setTimeout((function(){r.render()}),1e3/this.frameRate)):(this.timeoutId&&clearTimeout(this.timeoutId),this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=requestAnimationFrame(this.render.bind(this)));case 9:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"installEvents",value:function(){window.addEventListener("visibilitychange",this.render.bind(this))}},{key:"uninstallEvents",value:function(){window.addEventListener("visibilitychange",this.render.bind(this))}},{key:"destroy",value:function(){this.isRender=!1,cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.uninstallEvents()}},{key:"initBackgroundBlurProgram",value:function(){var e=this.gl,t={vertexShaderSource:"#version 300 es\n    \n    // an attribute is an input (in) to a vertex shader.\n    // It will receive data from a buffer\n    in vec2 position;\n    in vec2 texCoord;\n    \n    // Used to pass in the resolution of the canvas\n    uniform vec2 resolution;\n    \n    // Used to pass the texture coordinates to the fragment shader\n    out vec2 v_texCoord;\n    \n    // all shaders have a main function\n    void main() {\n    \n      // convert the position from pixels to 0.0 to 1.0\n      vec2 zeroToOne = position / resolution;\n    \n      // convert from 0->1 to 0->2\n      vec2 zeroToTwo = zeroToOne * 2.0;\n    \n      // convert from 0->2 to -1->+1 (clipspace)\n      vec2 clipSpace = zeroToTwo - 1.0;\n    \n      gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);\n    \n      // pass the texCoord to the fragment shader\n      // The GPU will interpolate this value between points.\n      v_texCoord = texCoord;\n    }\n    ",fragmentShaderSource:'#version 300 es\n    \n    // fragment shaders don\'t have a default precision so we need\n    // to pick one. highp is a good default. It means "high precision"\n    precision highp float;\n    \n    // our texture\n    uniform sampler2D u_image0;\n    uniform sampler2D u_image1;\n    \n    // the texCoords passed in from the vertex shader.\n    in vec2 v_texCoord;\n    \n    // we need to declare an output for the fragment shader\n    out vec4 outColor;\n    \n    void main() {\n      vec2 onePixel = vec2(1) / vec2(textureSize(u_image0, 0));\n    \n      vec4 color0 = texture(u_image0, v_texCoord);\n      vec4 color1 = texture(u_image1, v_texCoord);\n      \n      vec4 gaussianBlur = vec4(0.0);\n    \n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -12.0 * onePixel.y)) * 0.004937634762006754;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -10.0 * onePixel.y)) * 0.00572726182364418;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -8.0 * onePixel.y)) * 0.006427731733273422;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -6.0 * onePixel.y)) * 0.006979930414848231;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -4.0 * onePixel.y)) * 0.007333766846250579;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, -2.0 * onePixel.y)) * 0.0074556543695930904;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, 0.0 * onePixel.y)) * 0.007333766846250585;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, 2.0 * onePixel.y)) * 0.0069799304148482205;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, 4.0 * onePixel.y)) * 0.006427731733273439;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, 6.0 * onePixel.y)) * 0.0057272618236441745;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-12.0 * onePixel.x, 8.0 * onePixel.y)) * 0.004937634762006749;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -12.0 * onePixel.y)) * 0.00572726182364418;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -10.0 * onePixel.y)) * 0.006643166126617444;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -8.0 * onePixel.y)) * 0.007455655256615504;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -6.0 * onePixel.y)) * 0.008096161608439047;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -4.0 * onePixel.y)) * 0.008506583598534066;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, -2.0 * onePixel.y)) * 0.008647963387211346;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, 0.0 * onePixel.y)) * 0.008506583598534071;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, 2.0 * onePixel.y)) * 0.008096161608439035;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, 4.0 * onePixel.y)) * 0.007455655256615524;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, 6.0 * onePixel.y)) * 0.006643166126617437;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-10.0 * onePixel.x, 8.0 * onePixel.y)) * 0.005727261823644173;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -12.0 * onePixel.y)) * 0.006427731733273422;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -10.0 * onePixel.y)) * 0.007455655256615504;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -8.0 * onePixel.y)) * 0.008367515465671788;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -6.0 * onePixel.y)) * 0.009086358628382269;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -4.0 * onePixel.y)) * 0.009546977075905662;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, -2.0 * onePixel.y)) * 0.009705648249340199;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, 0.0 * onePixel.y)) * 0.00954697707590567;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, 2.0 * onePixel.y)) * 0.009086358628382255;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, 4.0 * onePixel.y)) * 0.00836751546567181;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, 6.0 * onePixel.y)) * 0.007455655256615497;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-8.0 * onePixel.x, 8.0 * onePixel.y)) * 0.006427731733273414;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -12.0 * onePixel.y)) * 0.006979930414848231;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -10.0 * onePixel.y)) * 0.008096161608439047;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -8.0 * onePixel.y)) * 0.009086358628382269;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -6.0 * onePixel.y)) * 0.009866956740300258;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -4.0 * onePixel.y)) * 0.010367146363159857;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, -2.0 * onePixel.y)) * 0.010539448785751998;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, 0.0 * onePixel.y)) * 0.010367146363159866;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, 2.0 * onePixel.y)) * 0.009866956740300243;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, 4.0 * onePixel.y)) * 0.009086358628382293;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, 6.0 * onePixel.y)) * 0.00809616160843904;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-6.0 * onePixel.x, 8.0 * onePixel.y)) * 0.006979930414848224;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -12.0 * onePixel.y)) * 0.007333766846250579;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -10.0 * onePixel.y)) * 0.008506583598534066;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -8.0 * onePixel.y)) * 0.009546977075905662;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -6.0 * onePixel.y)) * 0.010367146363159857;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -4.0 * onePixel.y)) * 0.010892692300575347;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, -2.0 * onePixel.y)) * 0.01107372931946121;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, 0.0 * onePixel.y)) * 0.010892692300575355;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, 2.0 * onePixel.y)) * 0.01036714636315984;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, 4.0 * onePixel.y)) * 0.009546977075905686;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, 6.0 * onePixel.y)) * 0.008506583598534057;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-4.0 * onePixel.x, 8.0 * onePixel.y)) * 0.007333766846250571;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -12.0 * onePixel.y)) * 0.0074556543695930904;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -10.0 * onePixel.y)) * 0.008647963387211346;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -8.0 * onePixel.y)) * 0.009705648249340199;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -6.0 * onePixel.y)) * 0.010539448785751998;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -4.0 * onePixel.y)) * 0.01107372931946121;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, -2.0 * onePixel.y)) * 0.011257775181459748;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, 0.0 * onePixel.y)) * 0.011073729319461219;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, 2.0 * onePixel.y)) * 0.010539448785751979;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, 4.0 * onePixel.y)) * 0.009705648249340225;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, 6.0 * onePixel.y)) * 0.008647963387211337;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(-2.0 * onePixel.x, 8.0 * onePixel.y)) * 0.007455654369593082;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -12.0 * onePixel.y)) * 0.007333766846250585;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -10.0 * onePixel.y)) * 0.008506583598534071;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -8.0 * onePixel.y)) * 0.00954697707590567;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -6.0 * onePixel.y)) * 0.010367146363159866;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -4.0 * onePixel.y)) * 0.010892692300575355;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, -2.0 * onePixel.y)) * 0.011073729319461219;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, 0.0 * onePixel.y)) * 0.010892692300575364;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, 2.0 * onePixel.y)) * 0.01036714636315985;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, 4.0 * onePixel.y)) * 0.009546977075905695;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, 6.0 * onePixel.y)) * 0.008506583598534064;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(0.0 * onePixel.x, 8.0 * onePixel.y)) * 0.007333766846250576;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -12.0 * onePixel.y)) * 0.0069799304148482205;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -10.0 * onePixel.y)) * 0.008096161608439035;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -8.0 * onePixel.y)) * 0.009086358628382255;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -6.0 * onePixel.y)) * 0.009866956740300243;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -4.0 * onePixel.y)) * 0.01036714636315984;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, -2.0 * onePixel.y)) * 0.010539448785751979;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, 0.0 * onePixel.y)) * 0.01036714636315985;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, 2.0 * onePixel.y)) * 0.009866956740300227;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, 4.0 * onePixel.y)) * 0.00908635862838228;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, 6.0 * onePixel.y)) * 0.008096161608439026;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(2.0 * onePixel.x, 8.0 * onePixel.y)) * 0.006979930414848213;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -12.0 * onePixel.y)) * 0.006427731733273439;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -10.0 * onePixel.y)) * 0.007455655256615524;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -8.0 * onePixel.y)) * 0.00836751546567181;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -6.0 * onePixel.y)) * 0.009086358628382293;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -4.0 * onePixel.y)) * 0.009546977075905686;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, -2.0 * onePixel.y)) * 0.009705648249340225;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, 0.0 * onePixel.y)) * 0.009546977075905695;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, 2.0 * onePixel.y)) * 0.00908635862838228;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, 4.0 * onePixel.y)) * 0.008367515465671832;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, 6.0 * onePixel.y)) * 0.007455655256615516;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(4.0 * onePixel.x, 8.0 * onePixel.y)) * 0.006427731733273432;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -12.0 * onePixel.y)) * 0.0057272618236441745;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -10.0 * onePixel.y)) * 0.006643166126617437;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -8.0 * onePixel.y)) * 0.007455655256615497;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -6.0 * onePixel.y)) * 0.00809616160843904;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -4.0 * onePixel.y)) * 0.008506583598534057;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, -2.0 * onePixel.y)) * 0.008647963387211337;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, 0.0 * onePixel.y)) * 0.008506583598534064;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, 2.0 * onePixel.y)) * 0.008096161608439026;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, 4.0 * onePixel.y)) * 0.007455655256615516;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, 6.0 * onePixel.y)) * 0.00664316612661743;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(6.0 * onePixel.x, 8.0 * onePixel.y)) * 0.005727261823644167;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -12.0 * onePixel.y)) * 0.004937634762006749;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -10.0 * onePixel.y)) * 0.005727261823644173;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -8.0 * onePixel.y)) * 0.006427731733273414;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -6.0 * onePixel.y)) * 0.006979930414848224;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -4.0 * onePixel.y)) * 0.007333766846250571;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, -2.0 * onePixel.y)) * 0.007455654369593082;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, 0.0 * onePixel.y)) * 0.007333766846250576;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, 2.0 * onePixel.y)) * 0.006979930414848213;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, 4.0 * onePixel.y)) * 0.006427731733273432;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, 6.0 * onePixel.y)) * 0.005727261823644167;\n      gaussianBlur += texture(u_image1, v_texCoord+ vec2(8.0 * onePixel.x, 8.0 * onePixel.y)) * 0.004937634762006742;\n    \n      outColor = (1.0 - color0.r) * gaussianBlur + color0.r * color1;\n    }\n    '},r=t.vertexShaderSource,n=t.fragmentShaderSource;this.program=ku(e,[r,n]);var o=e.getAttribLocation(this.program,"position"),i=e.getAttribLocation(this.program,"texCoord");this.blurParams.resolutionLocation=e.getUniformLocation(this.program,"resolution"),this.blurParams.imageLocation0=e.getUniformLocation(this.program,"u_image0"),this.blurParams.imageLocation1=e.getUniformLocation(this.program,"u_image1"),e.bindVertexArray(this.vao),e.bindBuffer(e.ARRAY_BUFFER,this.positionBuffer),e.enableVertexAttribArray(o),e.vertexAttribPointer(o,2,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,e.createBuffer()),e.bufferData(e.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),e.STATIC_DRAW),e.enableVertexAttribArray(i),e.vertexAttribPointer(i,2,e.FLOAT,!1,0,0)}},{key:"onBlurResult",value:function(e){for(var t=e.segmentationMask,r=e.image,n=this.gl,o=[],i=[t,r],a=0;a<2;++a){var u=n.createTexture();n.bindTexture(n.TEXTURE_2D,u),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,i[a]),o.push(u)}n.bindBuffer(n.ARRAY_BUFFER,this.positionBuffer),this.setRectangle(n,0,0,this.width,this.height),n.viewport(0,0,this.width,this.height),n.clearColor(0,0,0,0),n.clear(n.COLOR_BUFFER_BIT|n.DEPTH_BUFFER_BIT),n.useProgram(this.program),n.bindVertexArray(this.vao);var s=this.blurParams,l=s.resolutionLocation,c=s.imageLocation0,f=s.imageLocation1;n.uniform2f(l,this.width,this.height),n.uniform1i(c,0),n.uniform1i(f,1),n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,o[0]),n.activeTexture(n.TEXTURE1),n.bindTexture(n.TEXTURE_2D,o[1]),n.drawArrays(n.TRIANGLES,0,6)}},{key:"initVirtualBackgroundProgram",value:function(){var e=this.gl,t={vertexShaderSource:"#version 300 es\n    \n    // an attribute is an input (in) to a vertex shader.\n    // It will receive data from a buffer\n    in vec2 position;\n    in vec2 texCoord;\n    \n    // Used to pass in the resolution of the canvas\n    uniform vec2 resolution;\n    \n    // Used to pass the texture coordinates to the fragment shader\n    out vec2 v_texCoord;\n    \n    // all shaders have a main function\n    void main() {\n    \n      // convert the position from pixels to 0.0 to 1.0\n      vec2 zeroToOne = position / resolution;\n    \n      // convert from 0->1 to 0->2\n      vec2 zeroToTwo = zeroToOne * 2.0;\n    \n      // convert from 0->2 to -1->+1 (clipspace)\n      vec2 clipSpace = zeroToTwo - 1.0;\n    \n      gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);\n    \n      // pass the texCoord to the fragment shader\n      // The GPU will interpolate this value between points.\n      v_texCoord = texCoord;\n    }\n    ",fragmentShaderSource:'#version 300 es\n    \n    // fragment shaders don\'t have a default precision so we need\n    // to pick one. highp is a good default. It means "high precision"\n    precision highp float;\n    \n    // Some hyperParameter:\n    uniform int PostProcessType;\n    uniform int MSIZE;\n    uniform float SIGMA;\n    uniform float BSIGMA;\n    uniform int overlayType;\n    uniform float hardThreshold;\n    uniform int sigmoidClip;\n    \n    // our texture\n    uniform sampler2D u_image0;\n    uniform sampler2D u_image1;\n    uniform sampler2D u_image2;\n    \n    // time-wise texture\n    uniform sampler2D u_image_old0;\n    uniform sampler2D u_image_old1;\n    uniform sampler2D u_image_old2;\n    uniform sampler2D u_image_old3;\n    uniform sampler2D u_image_old4;\n    uniform sampler2D u_image_old5;\n    \n    // the texCoords passed in from the vertex shader.\n    in vec2 v_texCoord;\n    \n    // we need to declare an output for the fragment shader\n    out vec4 outColor;\n    \n    float normpdf(in float x, in float sigma)\n    {\n      return 0.39894*exp(-0.5*x*x/(sigma*sigma))/sigma;\n    }\n    \n    float normpdf3(in vec3 v, in float sigma)\n    {\n    return 0.39894*exp(-0.5*dot(v,v)/(sigma*sigma))/sigma;\n    }\n    \n    void main() {\n      vec2 onePixel = vec2(1) / vec2(textureSize(u_image0, 0));\n    \n      vec4 color0 = texture(u_image0, v_texCoord);                  // mask. r & a are same.\n      vec4 color1 = texture(u_image1, v_texCoord);                  // video image.\n      vec4 color2 = texture(u_image2, v_texCoord);                  // background image.\n      vec4 color_old0 = texture(u_image_old0, v_texCoord);          // old mask(3 frames old)\n      vec4 color_old1 = texture(u_image_old1, v_texCoord);          // old mask(2 frames old)\n      vec4 color_old2 = texture(u_image_old2, v_texCoord);          // old mask(1 frames old)\n      vec4 color_old3 = texture(u_image_old3, v_texCoord);          // old video image(3 frames old)\n      vec4 color_old4 = texture(u_image_old4, v_texCoord);          // old video image(2 frames old)\n      vec4 color_old5 = texture(u_image_old5, v_texCoord);          // old video image(1 frames old)\n      \n      float maskValue = color0.a;\n      if (PostProcessType == 0) {\n        // Do nothing to the mask. Passthrough.\n      }\n      else if (PostProcessType == 1) {\n          // JBF\n          vec4 c = texture(u_image1, v_texCoord);\n          int kSize = (MSIZE-1)/2;\n          float kernel[40];\n          float bfinal_colour = 0.0;\n          float bZ = 0.0;\n          //create the 1-D kernel\n          for (int j = 0; j <= kSize; ++j) {\n            kernel[kSize+j] = kernel[kSize-j] = normpdf(float(j), SIGMA);\n          }\n        \n          vec3 cc;\n          float gfactor;\n          float bfactor;\n          float bZnorm = 1.0/normpdf(0.0, BSIGMA);\n          \n          for (int i=-kSize; i <= kSize; ++i)\n            {\n              for (int j=-kSize; j <= kSize; ++j)\n              {\n                cc = texture(u_image1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                \n                // compute both the gaussian smoothed and bilateral\n                gfactor = kernel[kSize+j]*kernel[kSize+i];\n                bfactor = normpdf3(cc-c.rgb, BSIGMA)*bZnorm*gfactor;\n                bZ += bfactor;\n                \n                bfinal_colour += bfactor * texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n              }\n            }\n          \n          maskValue = bfinal_colour / bZ;\n      }\n      else if (PostProcessType == 2) {\n          // Joint Bilateral filter. + avg time domain filter. Pixel image is time-domain filtered, ref image is not.\n          // vec4 c = texture(u_image1, v_texCoord);\n          // vec4 c = texture(u_image1, v_texCoord) * 0.25 + texture(u_image_old3, v_texCoord) * 0.25 +\n          //          texture(u_image_old4, v_texCoord) * 0.25 + texture(u_image_old5, v_texCoord) * 0.25;\n          // vec4 c = sqrt(\n          // texture(u_image1, v_texCoord) * texture(u_image1, v_texCoord) * 0.25 +\n          // texture(u_image1, v_texCoord) * texture(u_image_old3, v_texCoord) * 0.25 +\n          // texture(u_image1, v_texCoord) * texture(u_image_old4, v_texCoord) * 0.25 +\n          // texture(u_image1, v_texCoord) * texture(u_image_old5, v_texCoord) * 0.25\n          // );\n          vec4 c = texture(u_image1, v_texCoord) * 0.5 + texture(u_image_old4, v_texCoord) * 0.5;\n          \n          int kSize = (MSIZE-1)/2;\n          float kernel[40];\n          float bfinal_colour = 0.0;\n          float bZ = 0.0;\n          //create the 1-D kernel\n          for (int j = 0; j <= kSize; ++j) {\n            kernel[kSize+j] = kernel[kSize-j] = normpdf(float(j), SIGMA);\n          }\n        \n          vec3 cc;\n          float gfactor;\n          float bfactor;\n          float bZnorm = 1.0/normpdf(0.0, BSIGMA);\n          \n          for (int i=-kSize; i <= kSize; ++i)\n            {\n              for (int j=-kSize; j <= kSize; ++j)\n              {\n                vec3 p0 = texture(u_image1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                // vec3 p1 = texture(u_image_old3, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                // vec3 p2 = texture(u_image_old4, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p3 = texture(u_image_old5, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                cc = p0 * 0.5 + p3 * 0.5;\n                \n                // compute both the gaussian smoothed and bilateral\n                gfactor = kernel[kSize+j]*kernel[kSize+i];\n                bfactor = normpdf3(cc-c.rgb, BSIGMA)*bZnorm*gfactor;\n                bZ += bfactor;\n        \n                // bfinal_colour += bfactor * texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp0 = texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp1 = texture(u_image_old0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp2 = texture(u_image_old1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp3 = texture(u_image_old2, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                \n                bfinal_colour += bfactor  * ( tmp0 * 0.5 + tmp3 * 0.5 ); // smooth filter on latest 2 frames.\n              }\n            }\n          \n          maskValue = bfinal_colour / bZ;\n      }\n      else if (PostProcessType == 3) {\n          // Joint Bilateral filter. + avg time domain filter. Pixel image is time-domain filtered, ref image is not.\n          vec4 c = texture(u_image1, v_texCoord) * 0.33 + texture(u_image_old5, v_texCoord) * 0.33 + texture(u_image_old4, v_texCoord) * 0.33;\n          \n          int kSize = (MSIZE-1)/2;\n          float kernel[40];\n          float bfinal_colour = 0.0;\n          float bZ = 0.0;\n          //create the 1-D kernel\n          for (int j = 0; j <= kSize; ++j) {\n            kernel[kSize+j] = kernel[kSize-j] = normpdf(float(j), SIGMA);\n          }\n        \n          vec3 cc;\n          float gfactor;\n          float bfactor;\n          float bZnorm = 1.0/normpdf(0.0, BSIGMA);\n          \n          for (int i=-kSize; i <= kSize; ++i)\n            {\n              for (int j=-kSize; j <= kSize; ++j)\n              {\n                vec3 p0 = texture(u_image1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p2 = texture(u_image_old4, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p3 = texture(u_image_old5, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                cc = p0 * 0.33 + p3 * 0.33 + p2 * 0.33;\n                \n                // compute both the gaussian smoothed and bilateral\n                gfactor = kernel[kSize+j]*kernel[kSize+i];\n                bfactor = normpdf3(cc-c.rgb, BSIGMA)*bZnorm*gfactor;\n                bZ += bfactor;\n        \n                // bfinal_colour += bfactor * texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp0 = texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp1 = texture(u_image_old0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp2 = texture(u_image_old1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp3 = texture(u_image_old2, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                \n                bfinal_colour += bfactor  * ( tmp0 * 0.33 + tmp3 * 0.33 + tmp2 * 0.33 ); // smooth filter on latest 3 frames.\n              }\n            }\n          \n          maskValue = bfinal_colour / bZ;\n      }\n      else if (PostProcessType == 4) {\n          vec4 c = texture(u_image1, v_texCoord) * 0.25 + texture(u_image_old5, v_texCoord) * 0.25 +\n                   texture(u_image_old4, v_texCoord) * 0.25 + texture(u_image_old3, v_texCoord) * 0.25;\n          \n          int kSize = (MSIZE-1)/2;\n          float kernel[40];\n          float bfinal_colour = 0.0;\n          float bZ = 0.0;\n          //create the 1-D kernel\n          for (int j = 0; j <= kSize; ++j) {\n            kernel[kSize+j] = kernel[kSize-j] = normpdf(float(j), SIGMA);\n          }\n        \n          vec3 cc;\n          float gfactor;\n          float bfactor;\n          float bZnorm = 1.0/normpdf(0.0, BSIGMA);\n          \n          for (int i=-kSize; i <= kSize; ++i)\n            {\n              for (int j=-kSize; j <= kSize; ++j)\n              {\n                vec3 p0 = texture(u_image1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p1 = texture(u_image_old3, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p2 = texture(u_image_old4, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                vec3 p3 = texture(u_image_old5, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).rgb;\n                cc = p0 * 0.25 + p1 * 0.25 + p2 * 0.25 + p3 * 0.25;\n                \n                // compute both the gaussian smoothed and bilateral\n                gfactor = kernel[kSize+j]*kernel[kSize+i];\n                bfactor = normpdf3(cc-c.rgb, BSIGMA)*bZnorm*gfactor;\n                bZ += bfactor;\n    \n                float tmp0 = texture(u_image0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp1 = texture(u_image_old0, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp2 = texture(u_image_old1, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                float tmp3 = texture(u_image_old2, v_texCoord+ vec2(float(i) * onePixel.x, float(j) * onePixel.y)).a;\n                \n                bfinal_colour += bfactor  * ( tmp0 * 0.25 + tmp3 * 0.25 + tmp1 * 0.25 + tmp2 * 0.25 ); // smooth filter on latest 4 frames.\n              }\n            }\n          \n          maskValue = bfinal_colour / bZ;\n      }\n      \n      if (sigmoidClip == 1) {\n        maskValue = 1.0 / (1.0 + exp(-10.0 * (maskValue - 0.5)));\n      }\n      \n      if (overlayType != 0) {\n        maskValue = maskValue > hardThreshold ? 1.0 : 0.0;\n      }\n      \n      outColor.rgb = (maskValue * color1 + (1.0 - maskValue) * color2).rgb;\n      outColor.a = 1.0;\n    }\n    '},r=t.vertexShaderSource,n=t.fragmentShaderSource;this.program=ku(e,[r,n]);var o=e.getAttribLocation(this.program,"position"),i=e.getAttribLocation(this.program,"texCoord"),a=this.program;this.virtualParams={resolutionLocation:e.getUniformLocation(a,"resolution"),imageLocation0:e.getUniformLocation(a,"u_image0"),imageLocation1:e.getUniformLocation(a,"u_image1"),imageLocation2:e.getUniformLocation(a,"u_image2"),imageOldLocation0:e.getUniformLocation(a,"u_image_old0"),imageOldLocation1:e.getUniformLocation(a,"u_image_old1"),imageOldLocation2:e.getUniformLocation(a,"u_image_old2"),imageOldLocation3:e.getUniformLocation(a,"u_image_old3"),imageOldLocation4:e.getUniformLocation(a,"u_image_old4"),imageOldLocation5:e.getUniformLocation(a,"u_image_old5")},this.virtualLocation={postProcessTypeLocation:e.getUniformLocation(a,"PostProcessType"),sigmaLocation:e.getUniformLocation(a,"SIGMA"),bSigmaLocation:e.getUniformLocation(a,"BSIGMA"),mSizeLocation:e.getUniformLocation(a,"MSIZE"),overlayTypeLocation:e.getUniformLocation(a,"overlayType"),hardThresholdLocation:e.getUniformLocation(a,"hardThreshold"),sigmoidClipLocation:e.getUniformLocation(a,"sigmoidClip")},e.bindVertexArray(this.vao),e.bindBuffer(e.ARRAY_BUFFER,this.positionBuffer),e.enableVertexAttribArray(o),e.vertexAttribPointer(o,2,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,e.createBuffer()),e.bufferData(e.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),e.STATIC_DRAW),e.enableVertexAttribArray(i),e.vertexAttribPointer(i,2,e.FLOAT,!1,0,0)}},{key:"onVirtualResults",value:function(e){var t=e.segmentationMask,r=e.image,n=this.gl;this.maskBuffer.length<3&&this.maskBuffer.push(t,t,t),this.frameBuffer.length<3&&this.frameBuffer.push(r,r,r);for(var o=[],i=[t,r,this.img,this.maskBuffer[0],this.maskBuffer[1],this.maskBuffer[2],this.frameBuffer[0],this.frameBuffer[1],this.frameBuffer[2]],a=0;a<i.length;++a)this.texture=n.createTexture(),n.bindTexture(n.TEXTURE_2D,this.texture),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.NEAREST),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.NEAREST),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,i[a]),o.push(this.texture);n.bindBuffer(n.ARRAY_BUFFER,this.positionBuffer),this.setRectangle(n,0,0,this.width,this.height),n.viewport(0,0,this.width,this.height),n.clearColor(0,0,0,0),n.clear(n.COLOR_BUFFER_BIT|n.DEPTH_BUFFER_BIT),n.useProgram(this.program),n.bindVertexArray(this.vao);var u=this.virtualParams,s=u.resolutionLocation,l=u.imageLocation0,c=u.imageLocation1,f=u.imageLocation2,h=u.imageOldLocation0,v=u.imageOldLocation1,d=u.imageOldLocation2,g=u.imageOldLocation3,x=u.imageOldLocation4,m=u.imageOldLocation5;n.uniform2f(s,this.width,this.height);var p=this.virtualOption,y=p.postProcess,_=p.kSize,b=p.sigma,T=p.bSigma,P=p.overlayType,E=p.hardThreshold,C=p.sigmoidClip,w=this.virtualLocation,S=w.postProcessTypeLocation,A=w.mSizeLocation,k=w.sigmaLocation,B=w.bSigmaLocation,R=w.overlayTypeLocation,L=w.hardThresholdLocation,O=w.sigmoidClipLocation;n.uniform1i(S,y),n.uniform1i(A,_),n.uniform1f(k,b),n.uniform1f(B,T),n.uniform1i(R,P),n.uniform1f(L,E),n.uniform1i(O,C),n.uniform1i(l,0),n.uniform1i(c,1),n.uniform1i(f,2),n.uniform1i(h,3),n.uniform1i(v,4),n.uniform1i(d,5),n.uniform1i(g,6),n.uniform1i(x,7),n.uniform1i(m,8),n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,o[0]),n.activeTexture(n.TEXTURE1),n.bindTexture(n.TEXTURE_2D,o[1]),n.activeTexture(n.TEXTURE2),n.bindTexture(n.TEXTURE_2D,o[2]),n.activeTexture(n.TEXTURE3),n.bindTexture(n.TEXTURE_2D,o[3]),n.activeTexture(n.TEXTURE4),n.bindTexture(n.TEXTURE_2D,o[4]),n.activeTexture(n.TEXTURE5),n.bindTexture(n.TEXTURE_2D,o[5]),n.activeTexture(n.TEXTURE6),n.bindTexture(n.TEXTURE_2D,o[6]),n.activeTexture(n.TEXTURE7),n.bindTexture(n.TEXTURE_2D,o[7]),n.activeTexture(n.TEXTURE8),n.bindTexture(n.TEXTURE_2D,o[8]),n.drawArrays(n.TRIANGLES,0,6),this.maskBuffer[0]=this.maskBuffer[1],this.maskBuffer[1]=this.maskBuffer[2],this.maskBuffer[2]=t,this.frameBuffer[0]=this.frameBuffer[1],this.frameBuffer[1]=this.frameBuffer[2],this.frameBuffer[2]=r}}]),e}(),Mu=f.Promise,Fu=/(?:ipad|iphone|ipod).*applewebkit/i.test(A),Vu="process"==y(f.process),Nu=f.setImmediate,Gu=f.clearImmediate,zu=f.process,Xu=f.MessageChannel,Yu=f.Dispatch,Wu=0,Zu={};try{Lu=f.location}catch(nl){}var Hu=function(e){if(Zu.hasOwnProperty(e)){var t=Zu[e];delete Zu[e],t()}},$u=function(e){return function(){Hu(e)}},Ku=function(e){Hu(e.data)},qu=function(e){f.postMessage(String(e),Lu.protocol+"//"+Lu.host)};Nu&&Gu||(Nu=function(e){for(var t=[],r=arguments.length,n=1;r>n;)t.push(arguments[n++]);return Zu[++Wu]=function(){(E(e)?e:Function(e)).apply(void 0,t)},Ou(Wu),Wu},Gu=function(e){delete Zu[e]},Vu?Ou=function(e){zu.nextTick($u(e))}:Yu&&Yu.now?Ou=function(e){Yu.now($u(e))}:Xu&&!Fu?(ju=(Iu=new Xu).port2,Iu.port1.onmessage=Ku,Ou=st(ju.postMessage,ju,1)):f.addEventListener&&E(f.postMessage)&&!f.importScripts&&Lu&&"file:"!==Lu.protocol&&!h(qu)?(Ou=qu,f.addEventListener("message",Ku,!1)):Ou="onreadystatechange"in oe("script")?function(e){Xt.appendChild(oe("script")).onreadystatechange=function(){Xt.removeChild(this),Hu(e)}}:function(e){setTimeout($u(e),0)});var Ju,Qu,es,ts,rs,ns,os,is,as={set:Nu,clear:Gu},us=/ipad|iphone|ipod/i.test(A)&&void 0!==f.Pebble,ss=/web0s(?!.*chrome)/i.test(A),ls=ue.f,cs=as.set,fs=f.MutationObserver||f.WebKitMutationObserver,hs=f.document,vs=f.process,ds=f.Promise,gs=ls(f,"queueMicrotask"),xs=gs&&gs.value;xs||(Ju=function(){var e,t;for(Vu&&(e=vs.domain)&&e.exit();Qu;){t=Qu.fn,Qu=Qu.next;try{t()}catch(nl){throw Qu?ts():es=void 0,nl}}es=void 0,e&&e.enter()},Fu||Vu||ss||!fs||!hs?!us&&ds&&ds.resolve?((os=ds.resolve(void 0)).constructor=ds,is=os.then,ts=function(){is.call(os,Ju)}):ts=Vu?function(){vs.nextTick(Ju)}:function(){cs.call(f,Ju)}:(rs=!0,ns=hs.createTextNode(""),new fs(Ju).observe(ns,{characterData:!0}),ts=function(){ns.data=rs=!rs}));var ms,ps,ys,_s,bs=xs||function(e){var t={fn:e,next:void 0};es&&(es.next=t),Qu||(Qu=t,ts()),es=t},Ts=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=M(t),this.reject=M(r)},Ps={f:function(e){return new Ts(e)}},Es=function(e){try{return{error:!1,value:e()}}catch(nl){return{error:!0,value:nl}}},Cs="object"==typeof window,ws=as.set,Ss=J("species"),As="Promise",ks=Ae.get,Bs=Ae.set,Rs=Ae.getterFor(As),Ls=Mu&&Mu.prototype,Os=Mu,Is=Ls,js=f.TypeError,Us=f.document,Ds=f.process,Ms=Ps.f,Fs=Ms,Vs=!!(Us&&Us.createEvent&&f.dispatchEvent),Ns=E(f.PromiseRejectionEvent),Gs=!1,zs=it(As,(function(){var e=xe(Os),t=e!==String(Os);if(!t&&66===O)return!0;if(O>=51&&/native code/.test(e))return!1;var r=new Os((function(e){e(1)})),n=function(e){e((function(){}),(function(){}))};return(r.constructor={})[Ss]=n,!(Gs=r.then((function(){}))instanceof n)||!t&&Cs&&!Ns})),Xs=zs||!Zr((function(e){Os.all(e).catch((function(){}))})),Ys=function(e){var t;return!(!C(e)||!E(t=e.then))&&t},Ws=function(e,t){if(!e.notified){e.notified=!0;var r=e.reactions;bs((function(){for(var n=e.value,o=1==e.state,i=0;r.length>i;){var a,u,s,l=r[i++],c=o?l.ok:l.fail,f=l.resolve,h=l.reject,v=l.domain;try{c?(o||(2===e.rejection&&Ks(e),e.rejection=1),!0===c?a=n:(v&&v.enter(),a=c(n),v&&(v.exit(),s=!0)),a===l.promise?h(js("Promise-chain cycle")):(u=Ys(a))?u.call(a,f,h):f(a)):h(n)}catch(nl){v&&!s&&v.exit(),h(nl)}}e.reactions=[],e.notified=!1,t&&!e.rejection&&Hs(e)}))}},Zs=function(e,t,r){var n,o;Vs?((n=Us.createEvent("Event")).promise=t,n.reason=r,n.initEvent(e,!1,!0),f.dispatchEvent(n)):n={promise:t,reason:r},!Ns&&(o=f["on"+e])?o(n):"unhandledrejection"===e&&function(e,t){var r=f.console;r&&r.error&&(1===arguments.length?r.error(e):r.error(e,t))}("Unhandled promise rejection",r)},Hs=function(e){ws.call(f,(function(){var t,r=e.facade,n=e.value;if($s(e)&&(t=Es((function(){Vu?Ds.emit("unhandledRejection",n,r):Zs("unhandledrejection",r,n)})),e.rejection=Vu||$s(e)?2:1,t.error))throw t.value}))},$s=function(e){return 1!==e.rejection&&!e.parent},Ks=function(e){ws.call(f,(function(){var t=e.facade;Vu?Ds.emit("rejectionHandled",t):Zs("rejectionhandled",t,e.value)}))},qs=function(e,t,r){return function(n){e(t,n,r)}},Js=function(e,t,r){e.done||(e.done=!0,r&&(e=r),e.value=t,e.state=2,Ws(e,!0))},Qs=function(e,t,r){if(!e.done){e.done=!0,r&&(e=r);try{if(e.facade===t)throw js("Promise can't be resolved itself");var n=Ys(t);n?bs((function(){var r={done:!1};try{n.call(t,qs(Qs,r,e),qs(Js,r,e))}catch(nl){Js(r,nl,e)}})):(e.value=t,e.state=1,Ws(e,!1))}catch(nl){Js({done:!1},nl,e)}}};if(zs&&(Is=(Os=function(e){Gr(this,Os,As),M(e),ms.call(this);var t=ks(this);try{e(qs(Qs,t),qs(Js,t))}catch(nl){Js(t,nl)}}).prototype,(ms=function(e){Bs(this,{type:As,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=$r(Is,{then:function(e,t){var r=Rs(this),n=Ms(so(this,Os));return n.ok=!E(e)||e,n.fail=E(t)&&t,n.domain=Vu?Ds.domain:void 0,r.parent=!0,r.reactions.push(n),0!=r.state&&Ws(r,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),ps=function(){var e=new ms,t=ks(e);this.promise=e,this.resolve=qs(Qs,t),this.reject=qs(Js,t)},Ps.f=Ms=function(e){return e===Os||e===ys?new ps(e):Fs(e)},E(Mu)&&Ls!==Object.prototype)){_s=Ls.then,Gs||(Oe(Ls,"then",(function(e,t){var r=this;return new Os((function(e,t){_s.call(r,e,t)})).then(e,t)}),{unsafe:!0}),Oe(Ls,"catch",Is.catch,{unsafe:!0}));try{delete Ls.constructor}catch(nl){}xr&&xr(Ls,Is)}ut({global:!0,wrap:!0,forced:zs},{Promise:Os}),vr(Os,As,!1),qr(As),ys=S(As),ut({target:As,stat:!0,forced:zs},{reject:function(e){var t=Ms(this);return t.reject.call(void 0,e),t.promise}}),ut({target:As,stat:!0,forced:zs},{resolve:function(e){return function(e,t){if(se(e),C(t)&&t.constructor===e)return t;var r=Ps.f(e);return(0,r.resolve)(t),r.promise}(this,e)}}),ut({target:As,stat:!0,forced:Xs},{all:function(e){var t=this,r=Ms(t),n=r.resolve,o=r.reject,i=Es((function(){var r=M(t.resolve),i=[],a=0,u=1;Nr(e,(function(e){var s=a++,l=!1;i.push(void 0),u++,r.call(t,e).then((function(e){l||(l=!0,i[s]=e,--u||n(i))}),o)})),--u||n(i)}));return i.error&&o(i.value),r.promise},race:function(e){var t=this,r=Ms(t),n=r.reject,o=Es((function(){var o=M(t.resolve);Nr(e,(function(e){o.call(t,e).then(r.resolve,n)}))}));return o.error&&n(o.value),r.promise}});var el=function(){function e(){n(this,e),this.selfieSegmentation=null}var t,o,a;return i(e,[{key:"init",value:(a=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.loadScript();case 2:return e.next=4,this.initSelfie();case 4:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"loadResources",value:(o=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,window.selfieSegmentation.initialize();case 2:case"end":return e.stop()}}),e)}))),function(){return o.apply(this,arguments)})},{key:"initSelfie",value:(t=r(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,new window.SelfieSegmentation({locateFile:function(e){return"https://web.sdk.qcloud.com/trtc/webrtc/test/iris-test/selfie_segmentation/".concat(e)}});case 2:this.selfieSegmentation=e.sent,window.selfieSegmentation=this.selfieSegmentation;case 4:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"loadScript",value:function(){return new Promise((function(e,t){var r=document.createElement("script");r.type="text/javascript",r.crossOrigin="anonymous",r.onload=function(){return e()},r.onerror=function(){return t("load selfie_segmentation error")},r.src="https://web.sdk.qcloud.com/trtc/webrtc/test/iris-test/selfie_segmentation/selfie_segmentation.js",document.getElementsByTagName("head")[0].appendChild(r)}))}}]),e}(),tl=function(e){return"number"==typeof e},rl=function(){function t(){n(this,t),this.videoBeauty=document.createElement("video"),this.videoBeauty.loop=!0,this.videoBeauty.autoplay=!0,this.videoVirtual=document.createElement("video"),this.videoVirtual.loop=!0,this.videoVirtual.autoplay=!0,this.canvasBeauty=document.createElement("canvas"),this.filter=new Uu({canvas:this.canvasBeauty,video:this.videoBeauty}),this.canvasVirtual=document.createElement("canvas"),this.virtual=new Du({canvas:this.canvasVirtual,video:this.videoVirtual}),this.beautyParams={beauty:.5,brightness:.5,ruddy:.5},this.timeoutId=null,this.rafId=null,this.startTime=null,this.originTrack=null,this.beautyTrack=null,this.resultTrack=null,this.localStream=null,this.frameRate=null,this.disableStatus=!1}var o,a,u,s,l;return i(t,[{key:"generateBeautyStream",value:function(e){var t=e.getVideoTrack();if(!t)throw new Error("Your localStream does not contain video track.");var r=this.generateBeautyTrack(t);return e.replaceTrack(r),this.localStream=e,e.setBeautyStatus&&e.setBeautyStatus(!0),e}},{key:"generateBeautyTrack",value:function(e){var t=this;this.reset();var r=e.getSettings();this.frameRate=r.frameRate,this.filter.setRect(r.width,r.height),this.setRect(r.width,r.height);var n=new MediaStream;n.addTrack(e),this.videoBeauty.srcObject=n,this.videoBeauty.play();var o=this.canvasBeauty.captureStream(this.frameRate||15).getVideoTracks()[0];return this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=requestAnimationFrame((function(){t.startTime=(new Date).getTime(),t.render()})),this.installEvents(),this.originTrack=e,this.beautyTrack=o,this.setTrackParam({type:"beauty",originTrack:e,resultTrack:o,param:this.beautyParams}),o}},{key:"loadResources",value:(l=r(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=new el,e.next=3,t.init();case 3:return e.next=5,t.loadResources();case 5:case"end":return e.stop()}}),e)}))),function(){return l.apply(this,arguments)})},{key:"setBackground",value:function(e){if(!(e instanceof HTMLImageElement))throw new Error("you should use an HTMLImageElement to set background");this.virtual.setBackground(e)}},{key:"generateStream",value:(s=r(regeneratorRuntime.mark((function e(t){var r,n,o,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.localStream,n=t.type,o=t.img,i=r.getVideoTrack()){e.next=4;break}throw new Error("Your localStream does not contain video track.");case 4:return this.localStream=r,e.next=7,this.generateMixedTrack({videoTrack:i,type:n,img:o});case 7:return a=e.sent,r.replaceTrack(a),r.setBeautyStatus&&r.setBeautyStatus(!0),e.abrupt("return",r);case 11:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"generateMixedTrack",value:(u=r(regeneratorRuntime.mark((function e(t){var r,n,o,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.videoTrack,n=t.type,o=t.img,window.selfieSegmentation){e.next=3;break}throw new Error("you should call loadResources() first");case 3:return this.virtual.initVirtualParams({type:n,img:o}),e.next=6,this.generateBeautyTrack(r);case 6:return i=e.sent,e.next=9,this.generateVirtualTrack({videoTrack:i,type:n,img:o});case 9:return a=e.sent,this.beautyTrack=i,this.resultTrack=a,this.setTrackParam({type:"mixed",originTrack:this.originTrack,resultTrack:this.resultTrack,param:{type:n,img:o}}),e.abrupt("return",this.resultTrack);case 14:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"generateVirtualStream",value:(a=r(regeneratorRuntime.mark((function e(t){var r,n,o,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.localStream,n=t.type,o=t.img,i=r.getVideoTrack()){e.next=4;break}throw new Error("Your localStream does not contain video track.");case 4:return this.localStream=r,e.next=7,this.generateVirtualTrack({videoTrack:i,type:n,img:o});case 7:return a=e.sent,r.replaceTrack(a),r.setBeautyStatus&&r.setBeautyStatus(!0),e.abrupt("return",r);case 11:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"generateVirtualTrack",value:(o=r(regeneratorRuntime.mark((function e(t){var r,n,o,i,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.videoTrack,n=t.type,o=t.img,window.selfieSegmentation){e.next=3;break}throw new Error("you should call loadResources() first");case 3:return this.virtual.initVirtualParams({type:n,img:o}),i=r.getSettings(),this.frameRate=i.frameRate,this.virtual.setRect(i.width,i.height),this.setRect(i.width,i.height),(a=new MediaStream).addTrack(r),this.videoVirtual.srcObject=a,e.next=13,this.videoVirtual.play();case 13:return this.virtual.startVirtual(this.frameRate),e.next=16,this.virtual.render();case 16:return this.originTrack=r,this.resultTrack=this.canvasVirtual.captureStream(this.frameRate||15).getVideoTracks()[0],this.setTrackParam({type:"virtual",originTrack:this.originTrack,resultTrack:this.resultTrack,param:{type:n,img:o}}),e.abrupt("return",this.resultTrack);case 20:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})},{key:"deleteSource",value:function(t){if(e){var r=e.beautyTrackMap.get(t);r&&("mixed"===r.type&&this.deleteSource(r.originTrack.id),r.originTrack&&(r.originTrack.stop(),r.originTrack=null),r.beautyTrack&&(r.beautyTrack.stop(),r.beautyTrack=null),r.resultTrack&&(r.resultTrack.stop(),r.resultTrack=null),e.beautyTrackMap.delete(t))}}},{key:"draw",value:function(){this.videoBeauty&&this.videoBeauty.readyState===this.videoBeauty.HAVE_ENOUGH_DATA&&this.filter.apply(this.videoBeauty)}},{key:"render",value:function(){var e=this,t=(new Date).getTime();t-this.startTime>1e3/this.frameRate&&(this.draw(),this.startTime=t),document.hidden?(clearTimeout(this.timeoutId),this.timeoutId=setTimeout((function(){e.render()}),1e3/this.frameRate)):(this.timeoutId&&clearTimeout(this.timeoutId),this.rafId&&cancelAnimationFrame(this.rafId),this.rafId=requestAnimationFrame(this.render.bind(this)))}},{key:"setBeautyParam",value:function(e){var t=e.beauty,r=e.brightness,n=e.ruddy;tl(t)&&(this.beautyParams.beauty=t),tl(r)&&(this.beautyParams.brightness=r),tl(n)&&(this.beautyParams.ruddy=n),this.filter.setBeautyParams(this.beautyParams),this.getClose()&&!this.disableStatus&&this.disable(),!this.getClose()&&this.disableStatus&&this.enable()}},{key:"setRect",value:function(e,t){var r=e||640,n=t||480;this.videoBeauty.height=n,this.videoBeauty.width=r,this.canvasBeauty.height=n,this.canvasBeauty.width=r,this.videoVirtual.height=n,this.videoVirtual.width=r,this.canvasVirtual.height=n,this.canvasVirtual.width=r}},{key:"reset",value:function(){cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.videoBeauty.pause(),this.filter.reset(),this.beautyTrack&&this.beautyTrack.stop(),this.videoVirtual.pause(),this.resultTrack&&this.resultTrack.stop(),this.originTrack&&this.originTrack.stop()}},{key:"destroy",value:function(){cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.canvasBeauty&&(this.canvasBeauty.width=0,this.canvasBeauty.height=0,this.canvasBeauty.remove(),this.canvasBeauty=null),this.videoBeauty&&(this.videoBeauty.pause(),this.videoBeauty.removeAttribute("srcObject"),this.videoBeauty.removeAttribute("src"),this.videoBeauty.load(),this.videoBeauty.width=0,this.videoBeauty.height=0,this.videoBeauty.remove(),this.videoBeauty=null),this.beautyTrack&&(this.deleteSource(this.beautyTrack.id),this.beautyTrack.stop(),this.beautyTrack=null),this.originTrack&&(this.originTrack.stop(),this.originTrack=null),this.resultTrack&&(this.deleteSource(this.resultTrack.id),this.resultTrack.stop(),this.resultTrack=null),this.canvasVirtual&&(this.canvasVirtual.width=0,this.canvasVirtual.height=0,this.canvasVirtual.remove(),this.canvasVirtual=null),this.videoVirtual&&(this.videoVirtual.pause(),this.videoVirtual.removeAttribute("srcObject"),this.videoVirtual.removeAttribute("src"),this.videoVirtual.load(),this.videoVirtual.width=0,this.videoVirtual.height=0,this.videoVirtual.remove(),this.videoVirtual=null),this.virtual.destroy(),this.uninstallEvents()}},{key:"generateVideoTrackFromCanvasCapture",value:function(e){return this.canvasVirtual.captureStream(e).getVideoTracks()[0]}},{key:"setTrackParam",value:function(t){var r=t.type,n=t.originTrack,o=t.resultTrack,i=t.param;e&&(e.beautyTrackMap||(e.beautyTrackMap=new Map),e.beautyTrackMap.set(o.id,{type:r,originTrack:n,resultTrack:o,param:i,pluginInstance:this}))}},{key:"disable",value:function(){this.localStream&&this.originTrack&&(this.localStream.replaceTrack(this.originTrack),cancelAnimationFrame(this.rafId),clearTimeout(this.timeoutId),this.disableStatus=!0)}},{key:"enable",value:function(){this.localStream&&this.beautyTrack&&(this.localStream.replaceTrack(this.beautyTrack),this.render(),this.disableStatus=!1)}},{key:"installEvents",value:function(){document.addEventListener("visibilitychange",this.render.bind(this))}},{key:"uninstallEvents",value:function(){document.removeEventListener("visibilitychange",this.render.bind(this))}},{key:"getClose",value:function(){return 0===this.beautyParams.beauty&&0===this.beautyParams.brightness&&0===this.beautyParams.ruddy}}]),t}();return e&&(e.getRTCBeautyPlugin=function(){return new rl}),rl}));
