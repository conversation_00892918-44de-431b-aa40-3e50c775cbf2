/* global RTCBeautyPlugin TRTC */

import { sdkAppId, userId, roomId, userSig } from '../common.js';

document.getElementById('video').addEventListener('click', openCamera);
document.getElementById('open-local-video-view').addEventListener('click', openLocalVideoView);
document.getElementById('close-local-video-view').addEventListener('click', closeLocalVideoView);
document.getElementById('exit').addEventListener('click', exit);

let client = TRTC.createClient({ mode: 'rtc', sdkAppId, userId, userSig });
let localStream = TRTC.createStream({ userId, audio: false, video: true });
let beautyPlugin = new RTCBeautyPlugin();
await beautyPlugin.loadResources();

async function init() {
  client = TRTC.createClient({ mode: 'rtc', sdkAppId, userId, userSig });
  localStream = TRTC.createStream({ userId, audio: false, video: true });
  beautyPlugin = new RTCBeautyPlugin();
  await beautyPlugin.loadResources();
}

async function openCamera() {
  if (!client) {
    await init();
  }
  await client.join({ roomId });
  await localStream.initialize();
  console.log(RTCBeautyPlugin, beautyPlugin);
  const virtualStream = await beautyPlugin.generateVirtualStream({
    localStream,
    type: 'blur'
  });
  await client.publish(virtualStream);
  output();

  openLocalVideoView();
}

function openLocalVideoView() {
  localStream.play('local_stream');
}

function closeLocalVideoView() {
  localStream.stop();
}

async function exit() {
  await localStream.close();
  await client.leave();
  await client.destroy();
  await beautyPlugin.destroy();
  client = null;
}

// 设置分辨率相关

const profiles = {
  '480p15': { width: 640, height: 480, frameRate: 15, bitrate: 1000 },
  '480p30': { width: 640, height: 480, frameRate: 30, bitrate: 1000 },
  '720p15': { width: 1280, height: 720, frameRate: 15, bitrate: 2000 },
  '720p30': { width: 1280, height: 720, frameRate: 30, bitrate: 2000 }
};

const radioButtons = document.querySelectorAll('input[name="profile"]');
radioButtons.forEach(button => {
  button.addEventListener('change', () => {
    console.warn(button);
    const selectedProfile = button.value;
    const profile = profiles[selectedProfile];
    setVideoProfile(profile);
  });
  button.removeAttribute('disabled');
});

async function setVideoProfile(profile) {
  console.log(profile);
  localStream.setVideoProfile(profile);
  output();
}

async function output() {
  const videoTrack = localStream.getVideoTrack();
  if (!videoTrack) return;
  const settings = videoTrack.getSettings();
  const log = document.createElement('span');
  log.innerHTML = `实际分辨率：${settings.width}x${settings.height}, fps: ${settings.frameRate}`;
  document.getElementById('log').appendChild(log);
}
