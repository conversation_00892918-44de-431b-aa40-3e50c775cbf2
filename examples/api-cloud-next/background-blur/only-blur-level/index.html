<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>v5</title>
    <link href="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/css/fastbootstrap.min.css" rel="stylesheet" integrity="sha256-xLGBU65wCDv2/qEdq3ZYw2Qdiia/wxxeGepRyZmpQdY=" crossorigin="anonymous">
  </head>
  <body>
    <input type="radio" name="profile" value="480p15" disabled> 480p15
    <input type="radio" name="profile" value="480p30" disabled> 480p30
    <input type="radio" name="profile" value="720p15" disabled> 720p15
    <input type="radio" name="profile" value="720p30" disabled> 720p30
    <input type="radio" name="profile" value="1080p20" disabled> 1080p20
    <div id="log">  </div>
    <hr>
    <button id="video" class="btn btn-default"> v5进房并打开摄像头 </button>
    <div class="input-group flex-nowrap">
      <span class="input-group-text">选择设备</span>
      <select class="form-select" size="2" id="device-select">
      </select>
    </div>
    <!-- <button id="open-local-video-view" class="btn btn-default" disabled> 开启本地预览 </button>
    <button id="close-local-video-view" class="btn btn-default" disabled> 关闭本地预览 </button>
    <button id="exit" class="btn btn-default" disabled> 退房 </button> -->
    <hr>
    <div class="btn-group" role="group" aria-label="Basic example">
      <button id="startPlugin" class="btn btn-default"> 开始 </button>
      <button id="stopPlugin" class="btn btn-default"> 停止 </button>
    </div>
    <div class="input-group flex-nowrap">
      <span class="input-group-text">虚拟背景样式</span>
      <select class="form-select" size="2" id="background-type">
        <option value="blur" selected> blur 模糊 </option>
        <option value="image">image 图片（需填入图片 url）</option>
      </select>
    </div>
    <div class="input-group flex-nowrap" id="image-area">
      <span class="input-group-text" id="addon-wrapping">图片 url</span>
      <input type="text" class="form-control" id="image-url" value="https://picsum.photos/seed/picsum/200/300" placeholder="https://..." />
    </div>
    <div class="input-group flex-nowrap" id="blur-level-area">
      <span class="input-group-text" id="addon-wrapping">虚化等级（1-10）</span>
      <input type="range" id="blur-level" value="3" min="1" max="10" step="1" />
      <span id="blur-level-value">3</span>
    </div>
    <hr>
    <svg id="loading" style="display: none;" xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path stroke-dasharray="16" stroke-dashoffset="16" d="M12 3c4.97 0 9 4.03 9 9"><animate fill="freeze" attributeName="stroke-dashoffset" dur="0.3s" values="16;0"/><animateTransform attributeName="transform" dur="1.5s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path><path stroke-dasharray="64" stroke-dashoffset="64" stroke-opacity=".3" d="M12 3c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9c0 -4.97 4.03 -9 9 -9Z"><animate fill="freeze" attributeName="stroke-dashoffset" dur="1.2s" values="64;0"/></path></g></svg>
    <div id="local_stream" style="width: 666px"></div>
  </body>
</html>

<script src="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/js/fastbootstrap.min.js" integrity="sha256-+c+/OCMmtlZadi89VaV1yUOkk1T4BD2pwBFpY3OcrqI=" crossorigin="anonymous"></script>
<script type="module" src="./main.js"></script>
<script src="./trtc.js"></script>
<script src="./virtual-background.iife.js"></script>
