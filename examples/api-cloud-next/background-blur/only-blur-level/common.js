import { genTestUserSig } from './debug/GenerateTestUserSig-es.js';

export const generateRandomNumber = () => window.crypto.getRandomValues(new Uint16Array(1))[0];
export const urlParam = new URLSearchParams(window.location.search);

export const secretKey = urlParam.get('secretKey');
export const userId = urlParam.get('userId') || generateRandomNumber().toString();
export const roomId = parseInt(urlParam.get('roomId'), 10) || generateRandomNumber();

export { genTestUserSig };

let sdkAppId = parseInt(urlParam.get('sdkAppId'), 10);
let userSig;
let getUserSigPromise;

if (!sdkAppId || !secretKey) {
  sdkAppId = 1400704311;
  getUserSigPromise = getUserSigFromServer(sdkAppId, userId, roomId);
} else {
  userSig = urlParam.get('userSig') || genTestUserSig({ userID: userId, SDKAppID: sdkAppId, SecretKey: secretKey })?.userSig;
}

export { sdkAppId, userSig, getUserSigPromise };
console.log('(params) sdkAppId:', sdkAppId, 'secretKey:', secretKey, 'userId:', userId, 'roomId:', roomId, 'userSig:', userSig);

async function getUserSigFromServer(sdkAppId, userId, roomId) {
  try {
    const response = await fetch('https://service.trtc.qcloud.com/release/UserSigService', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        pwd: '********',
        appid: parseInt(sdkAppId),
        roomnum: parseInt(roomId),
        privMap: 255,
        identifier: userId,
        accounttype: 14418,
      }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
    const json = await response.json();

    if (json && json.errorCode === 0) {
      console.log('getUserSigFromServer success');
      userSig = json.data.userSig;
    } else {
      console.error(`got invalid json:${JSON.stringify(json)}`);
    }
  } catch (error) {
    console.error('Failed to retrieve userSig:', error);
  }
}
