/* global TRTC VirtualBackground*/

import { sdkAppId, userId, roomId, userSig, getUserSigPromise } from './common.js';

document.getElementById('video').addEventListener('click', openCamera);

document.getElementById('startPlugin').addEventListener('click', startPlugin);
// document.getElementById('updatePlugin').addEventListener('click', updatePlugin);
document.getElementById('stopPlugin').addEventListener('click', stopPlugin);
document.getElementById('device-select').addEventListener('change', changeCamera);

const backgroundTypeEle = document.getElementById('background-type');
const cameraListEle = document.getElementById('device-select');
const imageUrlEle = document.getElementById('image-url');
const blurLevelEle = document.getElementById('blur-level');
const loadingEle = document.getElementById('loading');

let trtc = TRTC.create({ plugins: [VirtualBackground] });

TRTC.getCameraList().then(list => {
  console.warn(list);
  list.forEach(l => {
    const optionEle = document.createElement('option');
    optionEle.value = l.deviceId;
    optionEle.text = l.label;
    cameraListEle.appendChild(optionEle);
  });
});

let presetProfile = {};

function changeCamera() {
  trtc.updateLocalVideo({ option: { cameraId: cameraListEle.value } });
}

// function onError(e) {
//   const { extraCode } = e;
//   if (extraCode === 10000003 || extraCode === 10000006) {
//     // 降低分辨率帧率或者关闭插件
//     console.error('client', e, e.code);
//     console.error('client', JSON.stringify(e), e.code);
//   }
// }

async function onError(event) {
  const { code } = event;
  if (code === 10000003 || code === 10000006) {
    // 降低分辨率帧率或者关闭插件
    await trtc.stopPlugin('VirtualBackground');
  }
}

async function startPlugin() {
  console.warn(backgroundTypeEle.value, imageUrlEle.value);
  try {
    loadingEle.style.display = 'block';
    await trtc.startPlugin('VirtualBackground', {
      sdkAppId,
      userId,
      userSig,
      type: backgroundTypeEle.value,
      src: imageUrlEle.value,
      onError,
    });
    console.warn('VirtualBackground success');
  } catch (e) {
    console.error('VirtualBackground failed', e);
  } finally {
    loadingEle.style.display = 'none';
  }
}

async function updatePlugin() {
  loadingEle.style.display = 'block';
  try {
    trtc.updatePlugin('VirtualBackground', {
      type: backgroundTypeEle.value,
      src: imageUrlEle.value,
      blurLevel: Number(blurLevelEle.value),
    });
  } catch (e) {
    console.error('updatePlugin failed', e);
  } finally {
    loadingEle.style.display = 'none';
  }
}

async function stopPlugin() {
  loadingEle.style.display = 'block';
  await trtc.stopPlugin('VirtualBackground');
  loadingEle.style.display = 'none';
}

async function openCamera() {
  if (!trtc) trtc = TRTC.create({ plugins: [VirtualBackground] });
  await getUserSigPromise;
  await trtc.enterRoom({ roomId, sdkAppId, userId, userSig });
  await trtc.startLocalVideo({
    option: {
      profile: presetProfile,
      cameraId: cameraListEle.value
    },
  });
  output();

  openLocalVideoView();
}

function openLocalVideoView() {
  trtc.updateLocalVideo({ view: 'local_stream' });
}

// 设置分辨率相关

const profiles = {
  '480p15': { width: 640, height: 480, frameRate: 15, bitrate: 1000 },
  '480p30': { width: 640, height: 480, frameRate: 30, bitrate: 1000 },
  '720p15': { width: 1280, height: 720, frameRate: 15, bitrate: 2000 },
  '720p30': { width: 1280, height: 720, frameRate: 30, bitrate: 2000 },
  '1080p20': { width: 1920, height: 1080, frameRate: 20, bitrate: 5000 }
};

const radioButtons = document.querySelectorAll('input[name="profile"]');
radioButtons.forEach(button => {
  button.addEventListener('change', () => {
    console.warn(button);
    const selectedProfile = button.value;
    const profile = profiles[selectedProfile];
    setVideoProfile(profile);
  });
  button.removeAttribute('disabled');
});

async function setVideoProfile(profile) {
  console.log(profile);
  presetProfile = profile;
  if (!trtc.getVideoTrack()) return;
  await trtc.updateLocalVideo({
    option: {
      profile,
    },
  });
  output();
}

async function output() {
  const videoTrack = trtc.getVideoTrack();
  if (!videoTrack) return;
  const settings = videoTrack.getSettings();
  const log = document.createElement('span');
  log.innerHTML = `当前分辨率：${settings.width}x${settings.height}, fps: ${settings.frameRate}`;
  document.getElementById('log').innerHTML = '';
  document.getElementById('log').appendChild(log);
}

backgroundTypeEle.addEventListener('change', () => {
  updatePlugin();
});
imageUrlEle.addEventListener('change', () => {
  updatePlugin();
});
blurLevelEle.addEventListener('change', () => {
  updatePlugin();
  document.getElementById('blur-level-value').innerHTML = blurLevelEle.value;
});
