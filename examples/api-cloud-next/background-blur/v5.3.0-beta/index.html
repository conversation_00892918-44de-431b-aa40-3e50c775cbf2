<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>v5</title>
    <link href="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/css/fastbootstrap.min.css" rel="stylesheet" integrity="sha256-xLGBU65wCDv2/qEdq3ZYw2Qdiia/wxxeGepRyZmpQdY=" crossorigin="anonymous">
  </head>
  <body>
    <h2>虚拟背景测试（体验版、旗舰版）5.3.0-beta</h2>
    <hr>
    填入校验信息：
    <input type="number" id="sdkAppId" placeholder="please input sdkAppId">
    <input type="text" id="secret-key" placeholder="please input secretKey">
    获取地址: <a href="https://console.cloud.tencent.com/trtc/app" target="_blank" rel="noopener noreferrer"> trtc 控制台 </a>
    <hr>
    1. 选择摄像头和分辨率
    <input type="radio" name="profile" value="480p15" disabled> 480p15
    <input type="radio" name="profile" value="480p30" disabled> 480p30
    <input type="radio" name="profile" value="720p15" disabled> 720p15
    <input type="radio" name="profile" value="720p30" disabled> 720p30
    <input type="radio" name="profile" value="1080p20" disabled> 1080p20
    <div id="log">  </div>
    <div class="input-group flex-nowrap">
      <select class="form-select" size="4" id="device-select">
      </select>
    </div>
    <hr>
    2. TRTC 进房并且打开摄像头
    <button id="video" class="btn btn-default"> v5进房并打开摄像头 </button>
    <button id="test" class="btn btn-default" style="opacity: 0;"> test </button>
    <!-- <button id="open-local-video-view" class="btn btn-default" disabled> 开启本地预览 </button>
    <button id="close-local-video-view" class="btn btn-default" disabled> 关闭本地预览 </button>
    <button id="exit" class="btn btn-default" disabled> 退房 </button> -->
    <hr>
    <div class="input-group flex-nowrap">
      3. 选择虚拟背景样式
      <div style="width: 500px">
        <select class="form-select" size="2" id="background-type">
          <option value="blur" selected> blur 模糊 </option>
          <option value="image">image 图片（需填入图片 url）</option>
        </select>
        <input type="text" class="form-control" id="image-url" value="https://picsum.photos/seed/picsum/200/300" placeholder="https://..." />
      </div>
    </div>
    <hr>
    <div class="btn-group" role="group" aria-label="Basic example">
      4. 
      <button id="startPlugin" class="btn btn-default"> 开启 </button>
      <button id="updatePlugin" class="btn btn-default"> 更新 </button>
      <button id="stopPlugin" class="btn btn-default"> 停止 </button>
    </div>
    <div style="opacity: 0;">
      其他测试：
      <input type="checkbox" name="water-mark" id="water-mark"> 水印
      <input type="checkbox" name="mirror" id="mirror">  编码翻转
      <input type="checkbox" name="webgl" id="webgl">  webgl
    </div>
    <div id="local_stream" style="width: 666px"></div>
  </body>
</html>

<script src="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/js/fastbootstrap.min.js" integrity="sha256-+c+/OCMmtlZadi89VaV1yUOkk1T4BD2pwBFpY3OcrqI=" crossorigin="anonymous"></script>
<script type="module" src="./main.js"></script>
<script type="module" src="../common.js"></script>
<!-- <script src="./trtc.js"></script> -->
<!-- <script src="./virtual-background.iife.js"></script> -->
<script src="../../dist/trtc.js"></script>
<script src="../../dist/virtual-background.iife.js"></script>
<!-- <script src="./../../../../plugins/cloud-video-effect/virtual-background/dist/virtual-background.iife.js"></script> -->
