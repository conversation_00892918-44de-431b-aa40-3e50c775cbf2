"use strict";
var VirtualBackground = (() => {
  var __defProp = Object.defineProperty;
  var __defProps = Object.defineProperties;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
  var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
    get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
  }) : x)(function(x) {
    if (typeof require !== "undefined")
      return require.apply(this, arguments);
    throw Error('Dynamic require of "' + x + '" is not supported');
  });
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __publicField = (obj, key, value) => {
    __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
    return value;
  };

  // src/index.ts
  var src_exports = {};
  __export(src_exports, {
    VirtualBackground: () => VirtualBackground,
    default: () => src_default
  });

  // src/utils.ts
  var isOverseaSdkAppId = (sdkAppId) => {
    sdkAppId = Number(sdkAppId);
    return sdkAppId > 0 && sdkAppId < 14e8;
  };
  function getScheduleDomain(sdkAppId) {
    let domain;
    if (isOverseaSdkAppId(sdkAppId)) {
      domain = "intl-schedule.rtc.qq.com";
    } else {
      domain = "schedule.rtc.qq.com";
    }
    return domain;
  }
  async function authEffect({
    sdkAppId,
    userId,
    userSig
  }) {
    const domain = getScheduleDomain(sdkAppId);
    const timestamp = Math.round((/* @__PURE__ */ new Date()).getTime() / 1e3);
    const url = `https://${domain}/api/v1/trtcAutoConf?sdkAppId=${sdkAppId}&userId=${userId}&userSig=${userSig}&timestamp=${timestamp}`;
    const res = await fetch(url);
    const { data } = await res.json();
    if (!data.arAblitySign) {
      return {
        auth: false
      };
    }
    return {
      auth: true,
      sign: data.arAblitySign.toUpperCase(),
      // 注意：此处需要转大写
      timestamp
    };
  }

  // src/validate-config.ts
  var UserRule = {
    sdkAppId: {
      required: true,
      type: "number" /* Number */
    },
    userId: {
      required: true,
      type: "string" /* String */
    },
    userSig: {
      required: true,
      type: "string" /* String */
    }
  };
  function startValidateRule(core) {
    return {
      name: "VirtualBackgroundOptions",
      type: "object" /* Object */,
      required: true,
      allowEmpty: false,
      properties: __spreadProps(__spreadValues({}, UserRule), {
        type: {
          required: false,
          type: "string" /* String */,
          values: ["image", "blur"]
        },
        src: {
          required: false,
          type: "string" /* String */
        },
        onError: {
          required: false
        }
      }),
      validate(background, key, fnName, className) {
        var _a;
        const { RtcError, ErrorCode, ErrorCodeDictionary } = core.errorModule;
        if (!background)
          return;
        const { type, src, onError } = background;
        if (type === "image" && !src) {
          throw new RtcError({
            code: ErrorCode.INVALID_PARAMETER,
            extraCode: ErrorCodeDictionary.INVALID_PARAMETER_REQUIRED,
            fnName,
            messageParams: {
              key: "src"
            }
          });
        }
        if (onError && !core.utils.isFunction(onError)) {
          throw new RtcError({
            code: ErrorCode.INVALID_PARAMETER,
            extraCode: ErrorCodeDictionary.INVALID_PARAMETER_TYPE,
            fnName,
            messageParams: {
              key: "onError",
              value: typeof onError,
              rule: {
                type: "Function"
              }
            }
          });
        }
        if (!((_a = core.room.videoManager.cameraTrack) == null ? void 0 : _a.mediaTrack)) {
          throw new RtcError({
            code: ErrorCode.INVALID_OPERATION,
            extraCode: ErrorCodeDictionary.INVALID_OPERATION_NEED_VIDEO,
            fnName
          });
        }
        if (core.utils.env.CHROME_MAJOR_VERSION < 90) {
          throw new RtcError({
            code: ErrorCode.ENV_NOT_SUPPORTED,
            extraCode: ErrorCodeDictionary.NOT_SUPPORTED_CHROME_VERSION,
            fnName
          });
        }
      }
    };
  }
  function updateValidateRule(core) {
    return {
      name: "UpdateVirtualBackgroundOptions",
      type: "object" /* Object */,
      required: true,
      allowEmpty: false,
      properties: {
        type: {
          required: true,
          type: "string" /* String */,
          values: ["image", "blur"]
        },
        src: {
          required: false,
          type: "string" /* String */
        }
      },
      validate(background, key, fnName, className) {
        if (!background)
          return;
        const { RtcError, ErrorCode, ErrorCodeDictionary } = core.errorModule;
        const { type, src } = background;
        if (type === "image" && !src) {
          throw new RtcError({
            code: ErrorCode.INVALID_PARAMETER,
            extraCode: ErrorCodeDictionary.INVALID_PARAMETER_REQUIRED,
            fnName,
            messageParams: {
              key: "src"
            }
          });
        }
      }
    };
  }
  function stopValidateRule(core) {
    return {
      name: "StopVirtualBackgroundOptions",
      required: false
    };
  }

  // src/selfie_segmentation_solution_simd_wasm_bin.js
  var createMediapipeSolutionsWasm = function() {
    let _scriptDir = typeof document !== "undefined" && document.currentScript ? document.currentScript.src : void 0;
    if (typeof __filename !== "undefined")
      _scriptDir = _scriptDir || __filename;
    return function(createMediapipeSolutionsWasm2) {
      createMediapipeSolutionsWasm2 = createMediapipeSolutionsWasm2 || {};
      const Module = typeof createMediapipeSolutionsWasm2 !== "undefined" ? createMediapipeSolutionsWasm2 : {};
      let readyPromiseResolve;
      let readyPromiseReject;
      Module.ready = new Promise((resolve, reject) => {
        readyPromiseResolve = resolve;
        readyPromiseReject = reject;
      });
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_main")) {
        Object.defineProperty(Module.ready, "_main", { configurable: true, get() {
          abort("You are getting _main on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_main", { configurable: true, set() {
          abort("You are setting _main on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_stack_get_end")) {
        Object.defineProperty(Module.ready, "_emscripten_stack_get_end", { configurable: true, get() {
          abort("You are getting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_stack_get_end", { configurable: true, set() {
          abort("You are setting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_stack_get_free")) {
        Object.defineProperty(Module.ready, "_emscripten_stack_get_free", { configurable: true, get() {
          abort("You are getting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_stack_get_free", { configurable: true, set() {
          abort("You are setting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_stack_init")) {
        Object.defineProperty(Module.ready, "_emscripten_stack_init", { configurable: true, get() {
          abort("You are getting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_stack_init", { configurable: true, set() {
          abort("You are setting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_stackSave")) {
        Object.defineProperty(Module.ready, "_stackSave", { configurable: true, get() {
          abort("You are getting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_stackSave", { configurable: true, set() {
          abort("You are setting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_stackRestore")) {
        Object.defineProperty(Module.ready, "_stackRestore", { configurable: true, get() {
          abort("You are getting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_stackRestore", { configurable: true, set() {
          abort("You are setting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_stackAlloc")) {
        Object.defineProperty(Module.ready, "_stackAlloc", { configurable: true, get() {
          abort("You are getting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_stackAlloc", { configurable: true, set() {
          abort("You are setting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___wasm_call_ctors")) {
        Object.defineProperty(Module.ready, "___wasm_call_ctors", { configurable: true, get() {
          abort("You are getting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___wasm_call_ctors", { configurable: true, set() {
          abort("You are setting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_fflush")) {
        Object.defineProperty(Module.ready, "_fflush", { configurable: true, get() {
          abort("You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_fflush", { configurable: true, set() {
          abort("You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___errno_location")) {
        Object.defineProperty(Module.ready, "___errno_location", { configurable: true, get() {
          abort("You are getting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___errno_location", { configurable: true, set() {
          abort("You are setting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_malloc")) {
        Object.defineProperty(Module.ready, "_malloc", { configurable: true, get() {
          abort("You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_malloc", { configurable: true, set() {
          abort("You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_free")) {
        Object.defineProperty(Module.ready, "_free", { configurable: true, get() {
          abort("You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_free", { configurable: true, set() {
          abort("You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_setThrew")) {
        Object.defineProperty(Module.ready, "_setThrew", { configurable: true, get() {
          abort("You are getting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_setThrew", { configurable: true, set() {
          abort("You are setting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___cxa_is_pointer_type")) {
        Object.defineProperty(Module.ready, "___cxa_is_pointer_type", { configurable: true, get() {
          abort("You are getting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___cxa_is_pointer_type", { configurable: true, set() {
          abort("You are setting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___cxa_can_catch")) {
        Object.defineProperty(Module.ready, "___cxa_can_catch", { configurable: true, get() {
          abort("You are getting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___cxa_can_catch", { configurable: true, set() {
          abort("You are setting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_saveSetjmp")) {
        Object.defineProperty(Module.ready, "_saveSetjmp", { configurable: true, get() {
          abort("You are getting _saveSetjmp on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_saveSetjmp", { configurable: true, set() {
          abort("You are setting _saveSetjmp on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_memalign")) {
        Object.defineProperty(Module.ready, "_memalign", { configurable: true, get() {
          abort("You are getting _memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_memalign", { configurable: true, set() {
          abort("You are setting _memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_main_thread_process_queued_calls")) {
        Object.defineProperty(Module.ready, "_emscripten_main_thread_process_queued_calls", { configurable: true, get() {
          abort("You are getting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_main_thread_process_queued_calls", { configurable: true, set() {
          abort("You are setting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_webgl_make_context_current")) {
        Object.defineProperty(Module.ready, "_emscripten_webgl_make_context_current", { configurable: true, get() {
          abort("You are getting _emscripten_webgl_make_context_current on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_webgl_make_context_current", { configurable: true, set() {
          abort("You are setting _emscripten_webgl_make_context_current on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_webgl_get_current_context")) {
        Object.defineProperty(Module.ready, "_emscripten_webgl_get_current_context", { configurable: true, get() {
          abort("You are getting _emscripten_webgl_get_current_context on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_webgl_get_current_context", { configurable: true, set() {
          abort("You are setting _emscripten_webgl_get_current_context on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "__get_tzname")) {
        Object.defineProperty(Module.ready, "__get_tzname", { configurable: true, get() {
          abort("You are getting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "__get_tzname", { configurable: true, set() {
          abort("You are setting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "__get_daylight")) {
        Object.defineProperty(Module.ready, "__get_daylight", { configurable: true, get() {
          abort("You are getting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "__get_daylight", { configurable: true, set() {
          abort("You are setting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "__get_timezone")) {
        Object.defineProperty(Module.ready, "__get_timezone", { configurable: true, get() {
          abort("You are getting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "__get_timezone", { configurable: true, set() {
          abort("You are setting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_builtin_malloc")) {
        Object.defineProperty(Module.ready, "_emscripten_builtin_malloc", { configurable: true, get() {
          abort("You are getting _emscripten_builtin_malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_builtin_malloc", { configurable: true, set() {
          abort("You are setting _emscripten_builtin_malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_builtin_free")) {
        Object.defineProperty(Module.ready, "_emscripten_builtin_free", { configurable: true, get() {
          abort("You are getting _emscripten_builtin_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_builtin_free", { configurable: true, set() {
          abort("You are setting _emscripten_builtin_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "_emscripten_builtin_memalign")) {
        Object.defineProperty(Module.ready, "_emscripten_builtin_memalign", { configurable: true, get() {
          abort("You are getting _emscripten_builtin_memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "_emscripten_builtin_memalign", { configurable: true, set() {
          abort("You are setting _emscripten_builtin_memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___getTypeName")) {
        Object.defineProperty(Module.ready, "___getTypeName", { configurable: true, get() {
          abort("You are getting ___getTypeName on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___getTypeName", { configurable: true, set() {
          abort("You are setting ___getTypeName on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "___embind_register_native_and_builtin_types")) {
        Object.defineProperty(Module.ready, "___embind_register_native_and_builtin_types", { configurable: true, get() {
          abort("You are getting ___embind_register_native_and_builtin_types on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "___embind_register_native_and_builtin_types", { configurable: true, set() {
          abort("You are setting ___embind_register_native_and_builtin_types on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module.ready, "onRuntimeInitialized")) {
        Object.defineProperty(Module.ready, "onRuntimeInitialized", { configurable: true, get() {
          abort("You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
        Object.defineProperty(Module.ready, "onRuntimeInitialized", { configurable: true, set() {
          abort("You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js");
        } });
      }
      let moduleOverrides = {};
      let key;
      for (key in Module) {
        if (Module.hasOwnProperty(key)) {
          moduleOverrides[key] = Module[key];
        }
      }
      let arguments_ = [];
      let thisProgram = "./this.program";
      let quit_ = function(status, toThrow) {
        throw toThrow;
      };
      const ENVIRONMENT_IS_WEB = typeof window === "object";
      const ENVIRONMENT_IS_WORKER = typeof importScripts === "function";
      const ENVIRONMENT_IS_NODE = typeof process === "object" && typeof process.versions === "object" && typeof process.versions.node === "string";
      const ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;
      if (Module.ENVIRONMENT) {
        throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");
      }
      let scriptDirectory = "";
      function locateFile(path) {
        if (Module.locateFile) {
          return Module.locateFile(path, scriptDirectory);
        }
        return scriptDirectory + path;
      }
      let read_;
      let readAsync;
      let readBinary;
      let setWindowTitle;
      let nodeFS;
      let nodePath;
      if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
        if (ENVIRONMENT_IS_WORKER) {
          scriptDirectory = self.location.href;
        } else if (typeof document !== "undefined" && document.currentScript) {
          scriptDirectory = document.currentScript.src;
        }
        if (_scriptDir) {
          scriptDirectory = _scriptDir;
        }
        if (scriptDirectory.indexOf("blob:") !== 0) {
          scriptDirectory = scriptDirectory.substr(0, scriptDirectory.lastIndexOf("/") + 1);
        } else {
          scriptDirectory = "";
        }
        if (!(typeof window === "object" || typeof importScripts === "function"))
          throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");
        {
          read_ = function(url) {
            const xhr = new XMLHttpRequest();
            xhr.open("GET", url, false);
            xhr.send(null);
            return xhr.responseText;
          };
          if (ENVIRONMENT_IS_WORKER) {
            readBinary = function(url) {
              const xhr = new XMLHttpRequest();
              xhr.open("GET", url, false);
              xhr.responseType = "arraybuffer";
              xhr.send(null);
              return new Uint8Array(xhr.response);
            };
          }
          readAsync = function(url, onload, onerror) {
            const xhr = new XMLHttpRequest();
            xhr.open("GET", url, true);
            xhr.responseType = "arraybuffer";
            xhr.onload = function() {
              if (xhr.status == 200 || xhr.status == 0 && xhr.response) {
                onload(xhr.response);
                return;
              }
              onerror();
            };
            xhr.onerror = onerror;
            xhr.send(null);
          };
        }
        setWindowTitle = function(title) {
          document.title = title;
        };
      } else {
        throw new Error("environment detection error");
      }
      let out = Module.print || console.log.bind(console);
      let err = Module.printErr || console.warn.bind(console);
      for (key in moduleOverrides) {
        if (moduleOverrides.hasOwnProperty(key)) {
          Module[key] = moduleOverrides[key];
        }
      }
      moduleOverrides = null;
      if (Module.arguments)
        arguments_ = Module.arguments;
      if (!Object.getOwnPropertyDescriptor(Module, "arguments")) {
        Object.defineProperty(Module, "arguments", { configurable: true, get() {
          abort("Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (Module.thisProgram)
        thisProgram = Module.thisProgram;
      if (!Object.getOwnPropertyDescriptor(Module, "thisProgram")) {
        Object.defineProperty(Module, "thisProgram", { configurable: true, get() {
          abort("Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (Module.quit)
        quit_ = Module.quit;
      if (!Object.getOwnPropertyDescriptor(Module, "quit")) {
        Object.defineProperty(Module, "quit", { configurable: true, get() {
          abort("Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      assert(typeof Module.memoryInitializerPrefixURL === "undefined", "Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead");
      assert(typeof Module.pthreadMainPrefixURL === "undefined", "Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead");
      assert(typeof Module.cdInitializerPrefixURL === "undefined", "Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead");
      assert(typeof Module.filePackagePrefixURL === "undefined", "Module.filePackagePrefixURL option was removed, use Module.locateFile instead");
      assert(typeof Module.read === "undefined", "Module.read option was removed (modify read_ in JS)");
      assert(typeof Module.readAsync === "undefined", "Module.readAsync option was removed (modify readAsync in JS)");
      assert(typeof Module.readBinary === "undefined", "Module.readBinary option was removed (modify readBinary in JS)");
      assert(typeof Module.setWindowTitle === "undefined", "Module.setWindowTitle option was removed (modify setWindowTitle in JS)");
      assert(typeof Module.TOTAL_MEMORY === "undefined", "Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY");
      if (!Object.getOwnPropertyDescriptor(Module, "read")) {
        Object.defineProperty(Module, "read", { configurable: true, get() {
          abort("Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module, "readAsync")) {
        Object.defineProperty(Module, "readAsync", { configurable: true, get() {
          abort("Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module, "readBinary")) {
        Object.defineProperty(Module, "readBinary", { configurable: true, get() {
          abort("Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (!Object.getOwnPropertyDescriptor(Module, "setWindowTitle")) {
        Object.defineProperty(Module, "setWindowTitle", { configurable: true, get() {
          abort("Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      assert(!ENVIRONMENT_IS_SHELL, "shell environment detected but not enabled at build time.  Add 'shell' to `-s ENVIRONMENT` to enable.");
      function warnOnce(text) {
        if (!warnOnce.shown)
          warnOnce.shown = {};
        if (!warnOnce.shown[text]) {
          warnOnce.shown[text] = 1;
          err(text);
        }
      }
      function convertJsFunctionToWasm(func, sig) {
        if (typeof WebAssembly.Function === "function") {
          const typeNames = { i: "i32", j: "i64", f: "f32", d: "f64" };
          const type = { parameters: [], results: sig[0] == "v" ? [] : [typeNames[sig[0]]] };
          for (var i2 = 1; i2 < sig.length; ++i2) {
            type.parameters.push(typeNames[sig[i2]]);
          }
          return new WebAssembly.Function(type, func);
        }
        let typeSection = [1, 0, 1, 96];
        const sigRet = sig.slice(0, 1);
        const sigParam = sig.slice(1);
        const typeCodes = { i: 127, j: 126, f: 125, d: 124 };
        typeSection.push(sigParam.length);
        for (var i2 = 0; i2 < sigParam.length; ++i2) {
          typeSection.push(typeCodes[sigParam[i2]]);
        }
        if (sigRet == "v") {
          typeSection.push(0);
        } else {
          typeSection = typeSection.concat([1, typeCodes[sigRet]]);
        }
        typeSection[1] = typeSection.length - 2;
        const bytes = new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0].concat(typeSection, [2, 7, 1, 1, 101, 1, 102, 0, 0, 7, 5, 1, 1, 102, 0, 0]));
        const module = new WebAssembly.Module(bytes);
        const instance = new WebAssembly.Instance(module, { e: { f: func } });
        const wrappedFunc = instance.exports.f;
        return wrappedFunc;
      }
      const freeTableIndexes = [];
      let functionsInTableMap;
      function getEmptyTableSlot() {
        if (freeTableIndexes.length) {
          return freeTableIndexes.pop();
        }
        try {
          wasmTable.grow(1);
        } catch (err2) {
          if (!(err2 instanceof RangeError)) {
            throw err2;
          }
          throw "Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.";
        }
        return wasmTable.length - 1;
      }
      function addFunctionWasm(func, sig) {
        if (!functionsInTableMap) {
          functionsInTableMap = /* @__PURE__ */ new WeakMap();
          for (let i2 = 0; i2 < wasmTable.length; i2++) {
            const item = wasmTable.get(i2);
            if (item) {
              functionsInTableMap.set(item, i2);
            }
          }
        }
        if (functionsInTableMap.has(func)) {
          return functionsInTableMap.get(func);
        }
        const ret = getEmptyTableSlot();
        try {
          wasmTable.set(ret, func);
        } catch (err2) {
          if (!(err2 instanceof TypeError)) {
            throw err2;
          }
          assert(typeof sig !== "undefined", `Missing signature argument to addFunction: ${func}`);
          const wrapped = convertJsFunctionToWasm(func, sig);
          wasmTable.set(ret, wrapped);
        }
        functionsInTableMap.set(func, ret);
        return ret;
      }
      let tempRet0 = 0;
      const setTempRet0 = function(value) {
        tempRet0 = value;
      };
      const getTempRet0 = function() {
        return tempRet0;
      };
      let wasmBinary;
      if (Module.wasmBinary)
        wasmBinary = Module.wasmBinary;
      if (!Object.getOwnPropertyDescriptor(Module, "wasmBinary")) {
        Object.defineProperty(Module, "wasmBinary", { configurable: true, get() {
          abort("Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      const noExitRuntime = Module.noExitRuntime || true;
      if (!Object.getOwnPropertyDescriptor(Module, "noExitRuntime")) {
        Object.defineProperty(Module, "noExitRuntime", { configurable: true, get() {
          abort("Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      if (typeof WebAssembly !== "object") {
        abort("no native wasm support detected");
      }
      let wasmMemory;
      let ABORT = false;
      let EXITSTATUS;
      function assert(condition, text) {
        if (!condition) {
          abort(`Assertion failed: ${text}`);
        }
      }
      function getCFunc(ident) {
        const func = Module[`_${ident}`];
        assert(func, `Cannot call unknown function ${ident}, make sure it is exported`);
        return func;
      }
      function ccall(ident, returnType, argTypes, args, opts) {
        const toC = { string(str) {
          let ret2 = 0;
          if (str !== null && str !== void 0 && str !== 0) {
            const len = (str.length << 2) + 1;
            ret2 = stackAlloc(len);
            stringToUTF8(str, ret2, len);
          }
          return ret2;
        }, array(arr) {
          const ret2 = stackAlloc(arr.length);
          writeArrayToMemory(arr, ret2);
          return ret2;
        } };
        function convertReturnValue(ret2) {
          if (returnType === "string")
            return UTF8ToString(ret2);
          if (returnType === "boolean")
            return Boolean(ret2);
          return ret2;
        }
        const func = getCFunc(ident);
        const cArgs = [];
        let stack = 0;
        assert(returnType !== "array", 'Return type should not be "array".');
        if (args) {
          for (let i2 = 0; i2 < args.length; i2++) {
            const converter = toC[argTypes[i2]];
            if (converter) {
              if (stack === 0)
                stack = stackSave();
              cArgs[i2] = converter(args[i2]);
            } else {
              cArgs[i2] = args[i2];
            }
          }
        }
        let ret = func.apply(null, cArgs);
        function onDone(ret2) {
          if (stack !== 0)
            stackRestore(stack);
          return convertReturnValue(ret2);
        }
        ret = onDone(ret);
        return ret;
      }
      const ALLOC_STACK = 1;
      const UTF8Decoder = typeof TextDecoder !== "undefined" ? new TextDecoder("utf8") : void 0;
      function UTF8ArrayToString(heap, idx, maxBytesToRead) {
        const endIdx = idx + maxBytesToRead;
        let endPtr = idx;
        while (heap[endPtr] && !(endPtr >= endIdx))
          ++endPtr;
        if (endPtr - idx > 16 && heap.subarray && UTF8Decoder) {
          return UTF8Decoder.decode(heap.subarray(idx, endPtr));
        }
        let str = "";
        while (idx < endPtr) {
          let u0 = heap[idx++];
          if (!(u0 & 128)) {
            str += String.fromCharCode(u0);
            continue;
          }
          const u1 = heap[idx++] & 63;
          if ((u0 & 224) == 192) {
            str += String.fromCharCode((u0 & 31) << 6 | u1);
            continue;
          }
          const u2 = heap[idx++] & 63;
          if ((u0 & 240) == 224) {
            u0 = (u0 & 15) << 12 | u1 << 6 | u2;
          } else {
            if ((u0 & 248) != 240)
              warnOnce(`Invalid UTF-8 leading byte 0x${u0.toString(16)} encountered when deserializing a UTF-8 string in wasm memory to a JS string!`);
            u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heap[idx++] & 63;
          }
          if (u0 < 65536) {
            str += String.fromCharCode(u0);
          } else {
            const ch = u0 - 65536;
            str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
          }
        }
        return str;
      }
      function UTF8ToString(ptr, maxBytesToRead) {
        return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "";
      }
      function stringToUTF8Array(str, heap, outIdx, maxBytesToWrite) {
        if (!(maxBytesToWrite > 0))
          return 0;
        const startIdx = outIdx;
        const endIdx = outIdx + maxBytesToWrite - 1;
        for (let i2 = 0; i2 < str.length; ++i2) {
          let u = str.charCodeAt(i2);
          if (u >= 55296 && u <= 57343) {
            const u1 = str.charCodeAt(++i2);
            u = 65536 + ((u & 1023) << 10) | u1 & 1023;
          }
          if (u <= 127) {
            if (outIdx >= endIdx)
              break;
            heap[outIdx++] = u;
          } else if (u <= 2047) {
            if (outIdx + 1 >= endIdx)
              break;
            heap[outIdx++] = 192 | u >> 6;
            heap[outIdx++] = 128 | u & 63;
          } else if (u <= 65535) {
            if (outIdx + 2 >= endIdx)
              break;
            heap[outIdx++] = 224 | u >> 12;
            heap[outIdx++] = 128 | u >> 6 & 63;
            heap[outIdx++] = 128 | u & 63;
          } else {
            if (outIdx + 3 >= endIdx)
              break;
            if (u >= 2097152)
              warnOnce(`Invalid Unicode code point 0x${u.toString(16)} encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x1FFFFF).`);
            heap[outIdx++] = 240 | u >> 18;
            heap[outIdx++] = 128 | u >> 12 & 63;
            heap[outIdx++] = 128 | u >> 6 & 63;
            heap[outIdx++] = 128 | u & 63;
          }
        }
        heap[outIdx] = 0;
        return outIdx - startIdx;
      }
      function stringToUTF8(str, outPtr, maxBytesToWrite) {
        assert(typeof maxBytesToWrite === "number", "stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");
        return stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite);
      }
      function lengthBytesUTF8(str) {
        let len = 0;
        for (let i2 = 0; i2 < str.length; ++i2) {
          let u = str.charCodeAt(i2);
          if (u >= 55296 && u <= 57343)
            u = 65536 + ((u & 1023) << 10) | str.charCodeAt(++i2) & 1023;
          if (u <= 127)
            ++len;
          else if (u <= 2047)
            len += 2;
          else if (u <= 65535)
            len += 3;
          else
            len += 4;
        }
        return len;
      }
      const UTF16Decoder = typeof TextDecoder !== "undefined" ? new TextDecoder("utf-16le") : void 0;
      function UTF16ToString(ptr, maxBytesToRead) {
        assert(ptr % 2 == 0, "Pointer passed to UTF16ToString must be aligned to two bytes!");
        let endPtr = ptr;
        let idx = endPtr >> 1;
        const maxIdx = idx + maxBytesToRead / 2;
        while (!(idx >= maxIdx) && HEAPU16[idx])
          ++idx;
        endPtr = idx << 1;
        if (endPtr - ptr > 32 && UTF16Decoder) {
          return UTF16Decoder.decode(HEAPU8.subarray(ptr, endPtr));
        }
        let str = "";
        for (let i2 = 0; !(i2 >= maxBytesToRead / 2); ++i2) {
          const codeUnit = HEAP16[ptr + i2 * 2 >> 1];
          if (codeUnit == 0)
            break;
          str += String.fromCharCode(codeUnit);
        }
        return str;
      }
      function stringToUTF16(str, outPtr, maxBytesToWrite) {
        assert(outPtr % 2 == 0, "Pointer passed to stringToUTF16 must be aligned to two bytes!");
        assert(typeof maxBytesToWrite === "number", "stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");
        if (maxBytesToWrite === void 0) {
          maxBytesToWrite = 2147483647;
        }
        if (maxBytesToWrite < 2)
          return 0;
        maxBytesToWrite -= 2;
        const startPtr = outPtr;
        const numCharsToWrite = maxBytesToWrite < str.length * 2 ? maxBytesToWrite / 2 : str.length;
        for (let i2 = 0; i2 < numCharsToWrite; ++i2) {
          const codeUnit = str.charCodeAt(i2);
          HEAP16[outPtr >> 1] = codeUnit;
          outPtr += 2;
        }
        HEAP16[outPtr >> 1] = 0;
        return outPtr - startPtr;
      }
      function lengthBytesUTF16(str) {
        return str.length * 2;
      }
      function UTF32ToString(ptr, maxBytesToRead) {
        assert(ptr % 4 == 0, "Pointer passed to UTF32ToString must be aligned to four bytes!");
        let i2 = 0;
        let str = "";
        while (!(i2 >= maxBytesToRead / 4)) {
          const utf32 = HEAP32[ptr + i2 * 4 >> 2];
          if (utf32 == 0)
            break;
          ++i2;
          if (utf32 >= 65536) {
            const ch = utf32 - 65536;
            str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
          } else {
            str += String.fromCharCode(utf32);
          }
        }
        return str;
      }
      function stringToUTF32(str, outPtr, maxBytesToWrite) {
        assert(outPtr % 4 == 0, "Pointer passed to stringToUTF32 must be aligned to four bytes!");
        assert(typeof maxBytesToWrite === "number", "stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");
        if (maxBytesToWrite === void 0) {
          maxBytesToWrite = 2147483647;
        }
        if (maxBytesToWrite < 4)
          return 0;
        const startPtr = outPtr;
        const endPtr = startPtr + maxBytesToWrite - 4;
        for (let i2 = 0; i2 < str.length; ++i2) {
          let codeUnit = str.charCodeAt(i2);
          if (codeUnit >= 55296 && codeUnit <= 57343) {
            const trailSurrogate = str.charCodeAt(++i2);
            codeUnit = 65536 + ((codeUnit & 1023) << 10) | trailSurrogate & 1023;
          }
          HEAP32[outPtr >> 2] = codeUnit;
          outPtr += 4;
          if (outPtr + 4 > endPtr)
            break;
        }
        HEAP32[outPtr >> 2] = 0;
        return outPtr - startPtr;
      }
      function lengthBytesUTF32(str) {
        let len = 0;
        for (let i2 = 0; i2 < str.length; ++i2) {
          const codeUnit = str.charCodeAt(i2);
          if (codeUnit >= 55296 && codeUnit <= 57343)
            ++i2;
          len += 4;
        }
        return len;
      }
      function allocateUTF8(str) {
        const size = lengthBytesUTF8(str) + 1;
        const ret = _malloc(size);
        if (ret)
          stringToUTF8Array(str, HEAP8, ret, size);
        return ret;
      }
      function writeArrayToMemory(array, buffer2) {
        assert(array.length >= 0, "writeArrayToMemory array must have a length (should be an array or typed array)");
        HEAP8.set(array, buffer2);
      }
      function writeAsciiToMemory(str, buffer2, dontAddNull) {
        for (let i2 = 0; i2 < str.length; ++i2) {
          assert(str.charCodeAt(i2) === str.charCodeAt(i2) & 255);
          HEAP8[buffer2++ >> 0] = str.charCodeAt(i2);
        }
        if (!dontAddNull)
          HEAP8[buffer2 >> 0] = 0;
      }
      function alignUp(x, multiple) {
        if (x % multiple > 0) {
          x += multiple - x % multiple;
        }
        return x;
      }
      let buffer;
      let HEAP8;
      let HEAPU8;
      let HEAP16;
      let HEAPU16;
      let HEAP32;
      let HEAPU32;
      let HEAPF32;
      let HEAPF64;
      function updateGlobalBufferAndViews(buf) {
        buffer = buf;
        Module.HEAP8 = HEAP8 = new Int8Array(buf);
        Module.HEAP16 = HEAP16 = new Int16Array(buf);
        Module.HEAP32 = HEAP32 = new Int32Array(buf);
        Module.HEAPU8 = HEAPU8 = new Uint8Array(buf);
        Module.HEAPU16 = HEAPU16 = new Uint16Array(buf);
        Module.HEAPU32 = HEAPU32 = new Uint32Array(buf);
        Module.HEAPF32 = HEAPF32 = new Float32Array(buf);
        Module.HEAPF64 = HEAPF64 = new Float64Array(buf);
      }
      const TOTAL_STACK = 5242880;
      if (Module.TOTAL_STACK)
        assert(TOTAL_STACK === Module.TOTAL_STACK, "the stack size can no longer be determined at runtime");
      const INITIAL_MEMORY = Module.INITIAL_MEMORY || 16777216;
      if (!Object.getOwnPropertyDescriptor(Module, "INITIAL_MEMORY")) {
        Object.defineProperty(Module, "INITIAL_MEMORY", { configurable: true, get() {
          abort("Module.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)");
        } });
      }
      assert(INITIAL_MEMORY >= TOTAL_STACK, `INITIAL_MEMORY should be larger than TOTAL_STACK, was ${INITIAL_MEMORY}! (TOTAL_STACK=${TOTAL_STACK})`);
      assert(typeof Int32Array !== "undefined" && typeof Float64Array !== "undefined" && Int32Array.prototype.subarray !== void 0 && Int32Array.prototype.set !== void 0, "JS engine does not provide full typed array support");
      assert(!Module.wasmMemory, "Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally");
      assert(INITIAL_MEMORY == 16777216, "Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically");
      let wasmTable;
      function writeStackCookie() {
        const max = _emscripten_stack_get_end();
        assert((max & 3) == 0);
        HEAPU32[(max >> 2) + 1] = 34821223;
        HEAPU32[(max >> 2) + 2] = 2310721022;
        HEAP32[0] = 1668509029;
      }
      function checkStackCookie() {
        if (ABORT)
          return;
        const max = _emscripten_stack_get_end();
        const cookie1 = HEAPU32[(max >> 2) + 1];
        const cookie2 = HEAPU32[(max >> 2) + 2];
        if (cookie1 != 34821223 || cookie2 != 2310721022) {
          abort(`Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x${cookie2.toString(16)} ${cookie1.toString(16)}`);
        }
        if (HEAP32[0] !== 1668509029)
          abort("Runtime error: The application has corrupted its heap memory area (address zero)!");
      }
      (function() {
        const h16 = new Int16Array(1);
        const h8 = new Int8Array(h16.buffer);
        h16[0] = 25459;
        if (h8[0] !== 115 || h8[1] !== 99)
          throw "Runtime error: expected the system to be little-endian! (Run with -s SUPPORT_BIG_ENDIAN=1 to bypass)";
      })();
      const __ATPRERUN__ = [];
      const __ATINIT__ = [];
      const __ATEXIT__ = [];
      const __ATPOSTRUN__ = [];
      let runtimeInitialized = false;
      let runtimeExited = false;
      const runtimeKeepaliveCounter = 0;
      function keepRuntimeAlive() {
        return noExitRuntime || runtimeKeepaliveCounter > 0;
      }
      function preRun() {
        if (Module.preRun) {
          if (typeof Module.preRun === "function")
            Module.preRun = [Module.preRun];
          while (Module.preRun.length) {
            addOnPreRun(Module.preRun.shift());
          }
        }
        callRuntimeCallbacks(__ATPRERUN__);
      }
      function initRuntime() {
        checkStackCookie();
        assert(!runtimeInitialized);
        runtimeInitialized = true;
        if (!Module.noFSInit && !FS.init.initialized)
          FS.init();
        FS.ignorePermissions = false;
        TTY.init();
        callRuntimeCallbacks(__ATINIT__);
      }
      function exitRuntime() {
        checkStackCookie();
        runtimeExited = true;
      }
      function postRun() {
        checkStackCookie();
        if (Module.postRun) {
          if (typeof Module.postRun === "function")
            Module.postRun = [Module.postRun];
          while (Module.postRun.length) {
            addOnPostRun(Module.postRun.shift());
          }
        }
        callRuntimeCallbacks(__ATPOSTRUN__);
      }
      function addOnPreRun(cb) {
        __ATPRERUN__.unshift(cb);
      }
      function addOnInit(cb) {
        __ATINIT__.unshift(cb);
      }
      function addOnPostRun(cb) {
        __ATPOSTRUN__.unshift(cb);
      }
      assert(Math.imul, "This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");
      assert(Math.fround, "This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");
      assert(Math.clz32, "This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");
      assert(Math.trunc, "This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");
      let runDependencies = 0;
      let runDependencyWatcher = null;
      let dependenciesFulfilled = null;
      const runDependencyTracking = {};
      function getUniqueRunDependency(id) {
        const orig = id;
        while (1) {
          if (!runDependencyTracking[id])
            return id;
          id = orig + Math.random();
        }
      }
      function addRunDependency(id) {
        runDependencies++;
        if (Module.monitorRunDependencies) {
          Module.monitorRunDependencies(runDependencies);
        }
        if (id) {
          assert(!runDependencyTracking[id]);
          runDependencyTracking[id] = 1;
          if (runDependencyWatcher === null && typeof setInterval !== "undefined") {
            runDependencyWatcher = setInterval(() => {
              if (ABORT) {
                clearInterval(runDependencyWatcher);
                runDependencyWatcher = null;
                return;
              }
              let shown = false;
              for (const dep in runDependencyTracking) {
                if (!shown) {
                  shown = true;
                  err("still waiting on run dependencies:");
                }
                err(`dependency: ${dep}`);
              }
              if (shown) {
                err("(end of list)");
              }
            }, 1e4);
          }
        } else {
          err("warning: run dependency added without ID");
        }
      }
      function removeRunDependency(id) {
        runDependencies--;
        if (Module.monitorRunDependencies) {
          Module.monitorRunDependencies(runDependencies);
        }
        if (id) {
          assert(runDependencyTracking[id]);
          delete runDependencyTracking[id];
        } else {
          err("warning: run dependency removed without ID");
        }
        if (runDependencies == 0) {
          if (runDependencyWatcher !== null) {
            clearInterval(runDependencyWatcher);
            runDependencyWatcher = null;
          }
          if (dependenciesFulfilled) {
            const callback = dependenciesFulfilled;
            dependenciesFulfilled = null;
            callback();
          }
        }
      }
      Module.preloadedImages = {};
      Module.preloadedAudios = {};
      function abort(what) {
        if (Module.onAbort) {
          Module.onAbort(what);
        }
        what += "";
        err(what);
        ABORT = true;
        EXITSTATUS = 1;
        const output = `abort(${what}) at ${stackTrace()}`;
        what = output;
        const e = new WebAssembly.RuntimeError(what);
        readyPromiseReject(e);
        throw e;
      }
      const dataURIPrefix = "data:application/octet-stream;base64,";
      function isDataURI(filename) {
        return filename.startsWith(dataURIPrefix);
      }
      function isFileURI(filename) {
        return filename.startsWith("file://");
      }
      function createExportWrapper(name, fixedasm) {
        return function() {
          const displayName = name;
          let asm2 = fixedasm;
          if (!fixedasm) {
            asm2 = Module.asm;
          }
          assert(runtimeInitialized, `native function \`${displayName}\` called before runtime initialization`);
          assert(!runtimeExited, `native function \`${displayName}\` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)`);
          if (!asm2[name]) {
            assert(asm2[name], `exported native function \`${displayName}\` not found`);
          }
          return asm2[name].apply(null, arguments);
        };
      }
      let wasmBinaryFile;
      wasmBinaryFile = "selfie_segmentation_solution_simd_wasm_bin.wasm";
      if (!isDataURI(wasmBinaryFile)) {
        wasmBinaryFile = locateFile(wasmBinaryFile);
      }
      function getBinary(file) {
        try {
          if (file == wasmBinaryFile && wasmBinary) {
            return new Uint8Array(wasmBinary);
          }
          if (readBinary) {
            return readBinary(file);
          }
          throw "both async and sync fetching of the wasm failed";
        } catch (err2) {
          abort(err2);
        }
      }
      function getBinaryPromise() {
        if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {
          if (typeof fetch === "function" && !isFileURI(wasmBinaryFile)) {
            return fetch(wasmBinaryFile, { credentials: "same-origin" }).then((response) => {
              if (!response.ok) {
                throw `failed to load wasm binary file at '${wasmBinaryFile}'`;
              }
              return response.arrayBuffer();
            }).catch(() => getBinary(wasmBinaryFile));
          }
          if (readAsync) {
            return new Promise((resolve, reject) => {
              readAsync(wasmBinaryFile, (response) => {
                resolve(new Uint8Array(response));
              }, reject);
            });
          }
        }
        return Promise.resolve().then(() => getBinary(wasmBinaryFile));
      }
      function createWasm() {
        const info = { env: asmLibraryArg, wasi_snapshot_preview1: asmLibraryArg };
        function receiveInstance(instance, module) {
          const { exports } = instance;
          Module.asm = exports;
          wasmMemory = Module.asm.memory;
          assert(wasmMemory, "memory not found in wasm exports");
          updateGlobalBufferAndViews(wasmMemory.buffer);
          wasmTable = Module.asm.__indirect_function_table;
          assert(wasmTable, "table not found in wasm exports");
          addOnInit(Module.asm.__wasm_call_ctors);
          removeRunDependency("wasm-instantiate");
        }
        addRunDependency("wasm-instantiate");
        let trueModule = Module;
        function receiveInstantiationResult(result) {
          assert(Module === trueModule, "the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?");
          trueModule = null;
          receiveInstance(result.instance);
        }
        function instantiateArrayBuffer(receiver) {
          return getBinaryPromise().then((binary) => {
            const result = WebAssembly.instantiate(binary, info);
            return result;
          }).then(receiver, (reason) => {
            err(`failed to asynchronously prepare wasm: ${reason}`);
            if (isFileURI(wasmBinaryFile)) {
              err(`warning: Loading from a file URI (${wasmBinaryFile}) is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing`);
            }
            abort(reason);
          });
        }
        function instantiateAsync() {
          if (!wasmBinary && typeof WebAssembly.instantiateStreaming === "function" && !isDataURI(wasmBinaryFile) && !isFileURI(wasmBinaryFile) && typeof fetch === "function") {
            return fetch(wasmBinaryFile, { credentials: "same-origin" }).then((response) => {
              const result = WebAssembly.instantiateStreaming(response, info);
              return result.then(receiveInstantiationResult, (reason) => {
                err(`wasm streaming compile failed: ${reason}`);
                err("falling back to ArrayBuffer instantiation");
                return instantiateArrayBuffer(receiveInstantiationResult);
              });
            });
          }
          return instantiateArrayBuffer(receiveInstantiationResult);
        }
        if (Module.instantiateWasm) {
          try {
            const exports = Module.instantiateWasm(info, receiveInstance);
            return exports;
          } catch (e) {
            err(`Module.instantiateWasm callback failed with error: ${e}`);
            return false;
          }
        }
        instantiateAsync().catch(readyPromiseReject);
        return {};
      }
      let tempDouble;
      let tempI64;
      const ASM_CONSTS = { 460656() {
        let init_once = true;
        if (init_once) {
          const cachedFindCanvasEventTarget = findCanvasEventTarget;
          if (typeof cachedFindCanvasEventTarget !== "function") {
            if (typeof console !== "undefined") {
              console.error('Expected Emscripten global function "findCanvasEventTarget" not found. WebGL context creation may fail.');
            }
            return;
          }
          findCanvasEventTarget = function(target) {
            if (target == 0) {
              if (Module && Module.canvas) {
                return Module.canvas;
              }
              if (Module && Module.canvasCssSelector) {
                return cachedFindCanvasEventTarget(Module.canvasCssSelector);
              }
              if (typeof console !== "undefined") {
                console.warn("Module properties canvas and canvasCssSelector not found during WebGL context creation.");
              }
            }
            return cachedFindCanvasEventTarget(target);
          };
          init_once = false;
        }
      }, 461441() {
        return typeof wasmOffsetConverter !== "undefined";
      } };
      function HaveOffsetConverter() {
        return typeof wasmOffsetConverter !== "undefined";
      }
      function _emscripten_set_main_loop_timing(mode, value) {
        Browser.mainLoop.timingMode = mode;
        Browser.mainLoop.timingValue = value;
        if (!Browser.mainLoop.func) {
          err("emscripten_set_main_loop_timing: Cannot set timing mode for main loop since a main loop does not exist! Call emscripten_set_main_loop first to set one up.");
          return 1;
        }
        if (!Browser.mainLoop.running) {
          Browser.mainLoop.running = true;
        }
        if (mode == 0) {
          Browser.mainLoop.scheduler = function Browser_mainLoop_scheduler_setTimeout() {
            const timeUntilNextTick = Math.max(0, Browser.mainLoop.tickStartTime + value - _emscripten_get_now()) | 0;
            setTimeout(Browser.mainLoop.runner, timeUntilNextTick);
          };
          Browser.mainLoop.method = "timeout";
        } else if (mode == 1) {
          Browser.mainLoop.scheduler = function Browser_mainLoop_scheduler_rAF() {
            Browser.requestAnimationFrame(Browser.mainLoop.runner);
          };
          Browser.mainLoop.method = "rAF";
        } else if (mode == 2) {
          if (typeof setImmediate === "undefined") {
            const setImmediates = [];
            const emscriptenMainLoopMessageId = "setimmediate";
            const Browser_setImmediate_messageHandler = function(event) {
              if (event.data === emscriptenMainLoopMessageId || event.data.target === emscriptenMainLoopMessageId) {
                event.stopPropagation();
                setImmediates.shift()();
              }
            };
            addEventListener("message", Browser_setImmediate_messageHandler, true);
            setImmediate = function Browser_emulated_setImmediate(func) {
              setImmediates.push(func);
              if (ENVIRONMENT_IS_WORKER) {
                if (Module.setImmediates === void 0)
                  Module.setImmediates = [];
                Module.setImmediates.push(func);
                postMessage({ target: emscriptenMainLoopMessageId });
              } else
                postMessage(emscriptenMainLoopMessageId, "*");
            };
          }
          Browser.mainLoop.scheduler = function Browser_mainLoop_scheduler_setImmediate() {
            setImmediate(Browser.mainLoop.runner);
          };
          Browser.mainLoop.method = "immediate";
        }
        return 0;
      }
      let _emscripten_get_now;
      if (ENVIRONMENT_IS_NODE) {
        _emscripten_get_now = function() {
          const t = process.hrtime();
          return t[0] * 1e3 + t[1] / 1e6;
        };
      } else
        _emscripten_get_now = function() {
          return performance.now();
        };
      function _emscripten_webgl_do_commit_frame() {
        if (!GL.currentContext || !GL.currentContext.GLctx) {
          return -3;
        }
        if (GL.currentContext.defaultFbo) {
          GL.blitOffscreenFramebuffer(GL.currentContext);
          return 0;
        }
        if (!GL.currentContext.attributes.explicitSwapControl) {
          return -3;
        }
        return 0;
      }
      function _exit(status) {
        exit(status);
      }
      function maybeExit() {
        if (!keepRuntimeAlive()) {
          try {
            _exit(EXITSTATUS);
          } catch (e) {
            if (e instanceof ExitStatus) {
              return;
            }
            throw e;
          }
        }
      }
      function setMainLoop(browserIterationFunc, fps, simulateInfiniteLoop, arg, noSetTiming) {
        assert(!Browser.mainLoop.func, "emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");
        Browser.mainLoop.func = browserIterationFunc;
        Browser.mainLoop.arg = arg;
        const thisMainLoopId = Browser.mainLoop.currentlyRunningMainloop;
        function checkIsRunning() {
          if (thisMainLoopId < Browser.mainLoop.currentlyRunningMainloop) {
            maybeExit();
            return false;
          }
          return true;
        }
        Browser.mainLoop.running = false;
        Browser.mainLoop.runner = function Browser_mainLoop_runner() {
          if (ABORT)
            return;
          if (Browser.mainLoop.queue.length > 0) {
            const start = Date.now();
            const blocker = Browser.mainLoop.queue.shift();
            blocker.func(blocker.arg);
            if (Browser.mainLoop.remainingBlockers) {
              const remaining = Browser.mainLoop.remainingBlockers;
              let next = remaining % 1 == 0 ? remaining - 1 : Math.floor(remaining);
              if (blocker.counted) {
                Browser.mainLoop.remainingBlockers = next;
              } else {
                next = next + 0.5;
                Browser.mainLoop.remainingBlockers = (8 * remaining + next) / 9;
              }
            }
            out(`main loop blocker "${blocker.name}" took ${Date.now() - start} ms`);
            Browser.mainLoop.updateStatus();
            if (!checkIsRunning())
              return;
            setTimeout(Browser.mainLoop.runner, 0);
            return;
          }
          if (!checkIsRunning())
            return;
          Browser.mainLoop.currentFrameNumber = Browser.mainLoop.currentFrameNumber + 1 | 0;
          if (Browser.mainLoop.timingMode == 1 && Browser.mainLoop.timingValue > 1 && Browser.mainLoop.currentFrameNumber % Browser.mainLoop.timingValue != 0) {
            Browser.mainLoop.scheduler();
            return;
          }
          if (Browser.mainLoop.timingMode == 0) {
            Browser.mainLoop.tickStartTime = _emscripten_get_now();
          }
          GL.newRenderingFrameStarted();
          if (typeof GL !== "undefined" && GL.currentContext && !GL.currentContextIsProxied && !GL.currentContext.attributes.explicitSwapControl && GL.currentContext.GLctx.commit) {
            GL.currentContext.GLctx.commit();
          }
          if (Browser.mainLoop.method === "timeout" && Module.ctx) {
            warnOnce("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!");
            Browser.mainLoop.method = "";
          }
          Browser.mainLoop.runIter(browserIterationFunc);
          checkStackCookie();
          if (!checkIsRunning())
            return;
          if (typeof SDL === "object" && SDL.audio && SDL.audio.queueNewAudioData)
            SDL.audio.queueNewAudioData();
          Browser.mainLoop.scheduler();
        };
        if (!noSetTiming) {
          if (fps && fps > 0)
            _emscripten_set_main_loop_timing(0, 1e3 / fps);
          else
            _emscripten_set_main_loop_timing(1, 1);
          Browser.mainLoop.scheduler();
        }
        if (simulateInfiniteLoop) {
          throw "unwind";
        }
      }
      function callUserCallback(func, synchronous) {
        if (ABORT) {
          err("user callback triggered after application aborted.  Ignoring.");
          return;
        }
        if (synchronous) {
          func();
          return;
        }
        try {
          func();
        } catch (e) {
          if (e instanceof ExitStatus) {
            return;
          }
          if (e !== "unwind") {
            if (e && typeof e === "object" && e.stack)
              err(`exception thrown: ${[e, e.stack]}`);
            throw e;
          }
        }
      }
      function safeSetTimeout(func, timeout) {
        return setTimeout(() => {
          callUserCallback(func);
        }, timeout);
      }
      var Browser = { mainLoop: { running: false, scheduler: null, method: "", currentlyRunningMainloop: 0, func: null, arg: 0, timingMode: 0, timingValue: 0, currentFrameNumber: 0, queue: [], pause() {
        Browser.mainLoop.scheduler = null;
        Browser.mainLoop.currentlyRunningMainloop++;
      }, resume() {
        Browser.mainLoop.currentlyRunningMainloop++;
        const { timingMode } = Browser.mainLoop;
        const { timingValue } = Browser.mainLoop;
        const { func } = Browser.mainLoop;
        Browser.mainLoop.func = null;
        setMainLoop(func, 0, false, Browser.mainLoop.arg, true);
        _emscripten_set_main_loop_timing(timingMode, timingValue);
        Browser.mainLoop.scheduler();
      }, updateStatus() {
        if (Module.setStatus) {
          const message = Module.statusMessage || "Please wait...";
          const remaining = Browser.mainLoop.remainingBlockers;
          const expected = Browser.mainLoop.expectedBlockers;
          if (remaining) {
            if (remaining < expected) {
              Module.setStatus(`${message} (${expected - remaining}/${expected})`);
            } else {
              Module.setStatus(message);
            }
          } else {
            Module.setStatus("");
          }
        }
      }, runIter(func) {
        if (ABORT)
          return;
        if (Module.preMainLoop) {
          const preRet = Module.preMainLoop();
          if (preRet === false) {
            return;
          }
        }
        callUserCallback(func);
        if (Module.postMainLoop)
          Module.postMainLoop();
      } }, isFullscreen: false, pointerLock: false, moduleContextCreatedCallbacks: [], workers: [], init() {
        if (!Module.preloadPlugins)
          Module.preloadPlugins = [];
        if (Browser.initted)
          return;
        Browser.initted = true;
        try {
          new Blob();
          Browser.hasBlobConstructor = true;
        } catch (e) {
          Browser.hasBlobConstructor = false;
          out("warning: no blob constructor, cannot create blobs with mimetypes");
        }
        Browser.BlobBuilder = typeof MozBlobBuilder !== "undefined" ? MozBlobBuilder : typeof WebKitBlobBuilder !== "undefined" ? WebKitBlobBuilder : !Browser.hasBlobConstructor ? out("warning: no BlobBuilder") : null;
        Browser.URLObject = typeof window !== "undefined" ? window.URL ? window.URL : window.webkitURL : void 0;
        if (!Module.noImageDecoding && typeof Browser.URLObject === "undefined") {
          out("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available.");
          Module.noImageDecoding = true;
        }
        const imagePlugin = {};
        imagePlugin.canHandle = function imagePlugin_canHandle(name) {
          return !Module.noImageDecoding && /\.(jpg|jpeg|png|bmp)$/i.test(name);
        };
        imagePlugin.handle = function imagePlugin_handle(byteArray, name, onload, onerror) {
          let b = null;
          if (Browser.hasBlobConstructor) {
            try {
              b = new Blob([byteArray], { type: Browser.getMimetype(name) });
              if (b.size !== byteArray.length) {
                b = new Blob([new Uint8Array(byteArray).buffer], { type: Browser.getMimetype(name) });
              }
            } catch (e) {
              warnOnce(`Blob constructor present but fails: ${e}; falling back to blob builder`);
            }
          }
          if (!b) {
            const bb = new Browser.BlobBuilder();
            bb.append(new Uint8Array(byteArray).buffer);
            b = bb.getBlob();
          }
          const url = Browser.URLObject.createObjectURL(b);
          assert(typeof url === "string", "createObjectURL must return a url as a string");
          const img = new Image();
          img.onload = function img_onload() {
            assert(img.complete, `Image ${name} could not be decoded`);
            const canvas2 = document.createElement("canvas");
            canvas2.width = img.width;
            canvas2.height = img.height;
            const ctx = canvas2.getContext("2d");
            ctx.drawImage(img, 0, 0);
            Module.preloadedImages[name] = canvas2;
            Browser.URLObject.revokeObjectURL(url);
            if (onload)
              onload(byteArray);
          };
          img.onerror = function img_onerror(event) {
            out(`Image ${url} could not be decoded`);
            if (onerror)
              onerror();
          };
          img.src = url;
        };
        Module.preloadPlugins.push(imagePlugin);
        const audioPlugin = {};
        audioPlugin.canHandle = function audioPlugin_canHandle(name) {
          return !Module.noAudioDecoding && name.substr(-4) in { ".ogg": 1, ".wav": 1, ".mp3": 1 };
        };
        audioPlugin.handle = function audioPlugin_handle(byteArray, name, onload, onerror) {
          let done = false;
          function finish(audio) {
            if (done)
              return;
            done = true;
            Module.preloadedAudios[name] = audio;
            if (onload)
              onload(byteArray);
          }
          function fail() {
            if (done)
              return;
            done = true;
            Module.preloadedAudios[name] = new Audio();
            if (onerror)
              onerror();
          }
          if (Browser.hasBlobConstructor) {
            try {
              var b = new Blob([byteArray], { type: Browser.getMimetype(name) });
            } catch (e) {
              return fail();
            }
            const url = Browser.URLObject.createObjectURL(b);
            assert(typeof url === "string", "createObjectURL must return a url as a string");
            const audio = new Audio();
            audio.addEventListener("canplaythrough", () => {
              finish(audio);
            }, false);
            audio.onerror = function audio_onerror(event) {
              if (done)
                return;
              out(`warning: browser could not fully decode audio ${name}, trying slower base64 approach`);
              function encode64(data) {
                const BASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                const PAD = "=";
                let ret = "";
                let leftchar = 0;
                let leftbits = 0;
                for (let i2 = 0; i2 < data.length; i2++) {
                  leftchar = leftchar << 8 | data[i2];
                  leftbits += 8;
                  while (leftbits >= 6) {
                    const curr = leftchar >> leftbits - 6 & 63;
                    leftbits -= 6;
                    ret += BASE[curr];
                  }
                }
                if (leftbits == 2) {
                  ret += BASE[(leftchar & 3) << 4];
                  ret += PAD + PAD;
                } else if (leftbits == 4) {
                  ret += BASE[(leftchar & 15) << 2];
                  ret += PAD;
                }
                return ret;
              }
              audio.src = `data:audio/x-${name.substr(-3)};base64,${encode64(byteArray)}`;
              finish(audio);
            };
            audio.src = url;
            safeSetTimeout(() => {
              finish(audio);
            }, 1e4);
          } else {
            return fail();
          }
        };
        Module.preloadPlugins.push(audioPlugin);
        function pointerLockChange() {
          Browser.pointerLock = document.pointerLockElement === Module.canvas || document.mozPointerLockElement === Module.canvas || document.webkitPointerLockElement === Module.canvas || document.msPointerLockElement === Module.canvas;
        }
        const { canvas } = Module;
        if (canvas) {
          canvas.requestPointerLock = canvas.requestPointerLock || canvas.mozRequestPointerLock || canvas.webkitRequestPointerLock || canvas.msRequestPointerLock || function() {
          };
          canvas.exitPointerLock = document.exitPointerLock || document.mozExitPointerLock || document.webkitExitPointerLock || document.msExitPointerLock || function() {
          };
          canvas.exitPointerLock = canvas.exitPointerLock.bind(document);
          document.addEventListener("pointerlockchange", pointerLockChange, false);
          document.addEventListener("mozpointerlockchange", pointerLockChange, false);
          document.addEventListener("webkitpointerlockchange", pointerLockChange, false);
          document.addEventListener("mspointerlockchange", pointerLockChange, false);
          if (Module.elementPointerLock) {
            canvas.addEventListener("click", (ev) => {
              if (!Browser.pointerLock && Module.canvas.requestPointerLock) {
                Module.canvas.requestPointerLock();
                ev.preventDefault();
              }
            }, false);
          }
        }
      }, createContext(canvas, useWebGL, setInModule, webGLContextAttributes) {
        if (useWebGL && Module.ctx && canvas == Module.canvas)
          return Module.ctx;
        let ctx;
        let contextHandle;
        if (useWebGL) {
          const contextAttributes = { antialias: false, alpha: false, majorVersion: typeof WebGL2RenderingContext !== "undefined" ? 2 : 1 };
          if (webGLContextAttributes) {
            for (const attribute in webGLContextAttributes) {
              contextAttributes[attribute] = webGLContextAttributes[attribute];
            }
          }
          if (typeof GL !== "undefined") {
            contextHandle = GL.createContext(canvas, contextAttributes);
            if (contextHandle) {
              ctx = GL.getContext(contextHandle).GLctx;
            }
          }
        } else {
          ctx = canvas.getContext("2d");
        }
        if (!ctx)
          return null;
        if (setInModule) {
          if (!useWebGL)
            assert(typeof GLctx === "undefined", "cannot set in module if GLctx is used, but we are a non-GL context that would replace it");
          Module.ctx = ctx;
          if (useWebGL)
            GL.makeContextCurrent(contextHandle);
          Module.useWebGL = useWebGL;
          Browser.moduleContextCreatedCallbacks.forEach((callback) => {
            callback();
          });
          Browser.init();
        }
        return ctx;
      }, destroyContext(canvas, useWebGL, setInModule) {
      }, fullscreenHandlersInstalled: false, lockPointer: void 0, resizeCanvas: void 0, requestFullscreen(lockPointer, resizeCanvas) {
        Browser.lockPointer = lockPointer;
        Browser.resizeCanvas = resizeCanvas;
        if (typeof Browser.lockPointer === "undefined")
          Browser.lockPointer = true;
        if (typeof Browser.resizeCanvas === "undefined")
          Browser.resizeCanvas = false;
        const { canvas } = Module;
        function fullscreenChange() {
          Browser.isFullscreen = false;
          const canvasContainer2 = canvas.parentNode;
          if ((document.fullscreenElement || document.mozFullScreenElement || document.msFullscreenElement || document.webkitFullscreenElement || document.webkitCurrentFullScreenElement) === canvasContainer2) {
            canvas.exitFullscreen = Browser.exitFullscreen;
            if (Browser.lockPointer)
              canvas.requestPointerLock();
            Browser.isFullscreen = true;
            if (Browser.resizeCanvas) {
              Browser.setFullscreenCanvasSize();
            } else {
              Browser.updateCanvasDimensions(canvas);
            }
          } else {
            canvasContainer2.parentNode.insertBefore(canvas, canvasContainer2);
            canvasContainer2.parentNode.removeChild(canvasContainer2);
            if (Browser.resizeCanvas) {
              Browser.setWindowedCanvasSize();
            } else {
              Browser.updateCanvasDimensions(canvas);
            }
          }
          if (Module.onFullScreen)
            Module.onFullScreen(Browser.isFullscreen);
          if (Module.onFullscreen)
            Module.onFullscreen(Browser.isFullscreen);
        }
        if (!Browser.fullscreenHandlersInstalled) {
          Browser.fullscreenHandlersInstalled = true;
          document.addEventListener("fullscreenchange", fullscreenChange, false);
          document.addEventListener("mozfullscreenchange", fullscreenChange, false);
          document.addEventListener("webkitfullscreenchange", fullscreenChange, false);
          document.addEventListener("MSFullscreenChange", fullscreenChange, false);
        }
        const canvasContainer = document.createElement("div");
        canvas.parentNode.insertBefore(canvasContainer, canvas);
        canvasContainer.appendChild(canvas);
        canvasContainer.requestFullscreen = canvasContainer.requestFullscreen || canvasContainer.mozRequestFullScreen || canvasContainer.msRequestFullscreen || (canvasContainer.webkitRequestFullscreen ? function() {
          canvasContainer.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
        } : null) || (canvasContainer.webkitRequestFullScreen ? function() {
          canvasContainer.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
        } : null);
        canvasContainer.requestFullscreen();
      }, requestFullScreen() {
        abort("Module.requestFullScreen has been replaced by Module.requestFullscreen (without a capital S)");
      }, exitFullscreen() {
        if (!Browser.isFullscreen) {
          return false;
        }
        const CFS = document.exitFullscreen || document.cancelFullScreen || document.mozCancelFullScreen || document.msExitFullscreen || document.webkitCancelFullScreen || function() {
        };
        CFS.apply(document, []);
        return true;
      }, nextRAF: 0, fakeRequestAnimationFrame(func) {
        const now = Date.now();
        if (Browser.nextRAF === 0) {
          Browser.nextRAF = now + 1e3 / 60;
        } else {
          while (now + 2 >= Browser.nextRAF) {
            Browser.nextRAF += 1e3 / 60;
          }
        }
        const delay = Math.max(Browser.nextRAF - now, 0);
        setTimeout(func, delay);
      }, requestAnimationFrame(func) {
        if (typeof requestAnimationFrame === "function") {
          requestAnimationFrame(func);
          return;
        }
        const RAF = Browser.fakeRequestAnimationFrame;
        RAF(func);
      }, safeSetTimeout(func) {
        return safeSetTimeout(func);
      }, safeRequestAnimationFrame(func) {
        return Browser.requestAnimationFrame(() => {
          callUserCallback(func);
        });
      }, getMimetype(name) {
        return { jpg: "image/jpeg", jpeg: "image/jpeg", png: "image/png", bmp: "image/bmp", ogg: "audio/ogg", wav: "audio/wav", mp3: "audio/mpeg" }[name.substr(name.lastIndexOf(".") + 1)];
      }, getUserMedia(func) {
        if (!window.getUserMedia) {
          window.getUserMedia = navigator.getUserMedia || navigator.mozGetUserMedia;
        }
        window.getUserMedia(func);
      }, getMovementX(event) {
        return event.movementX || event.mozMovementX || event.webkitMovementX || 0;
      }, getMovementY(event) {
        return event.movementY || event.mozMovementY || event.webkitMovementY || 0;
      }, getMouseWheelDelta(event) {
        let delta = 0;
        switch (event.type) {
          case "DOMMouseScroll":
            delta = event.detail / 3;
            break;
          case "mousewheel":
            delta = event.wheelDelta / 120;
            break;
          case "wheel":
            delta = event.deltaY;
            switch (event.deltaMode) {
              case 0:
                delta /= 100;
                break;
              case 1:
                delta /= 3;
                break;
              case 2:
                delta *= 80;
                break;
              default:
                throw `unrecognized mouse wheel delta mode: ${event.deltaMode}`;
            }
            break;
          default:
            throw `unrecognized mouse wheel event: ${event.type}`;
        }
        return delta;
      }, mouseX: 0, mouseY: 0, mouseMovementX: 0, mouseMovementY: 0, touches: {}, lastTouches: {}, calculateMouseEvent(event) {
        if (Browser.pointerLock) {
          if (event.type != "mousemove" && "mozMovementX" in event) {
            Browser.mouseMovementX = Browser.mouseMovementY = 0;
          } else {
            Browser.mouseMovementX = Browser.getMovementX(event);
            Browser.mouseMovementY = Browser.getMovementY(event);
          }
          if (typeof SDL !== "undefined") {
            Browser.mouseX = SDL.mouseX + Browser.mouseMovementX;
            Browser.mouseY = SDL.mouseY + Browser.mouseMovementY;
          } else {
            Browser.mouseX += Browser.mouseMovementX;
            Browser.mouseY += Browser.mouseMovementY;
          }
        } else {
          const rect = Module.canvas.getBoundingClientRect();
          const cw = Module.canvas.width;
          const ch = Module.canvas.height;
          const scrollX = typeof window.scrollX !== "undefined" ? window.scrollX : window.pageXOffset;
          const scrollY = typeof window.scrollY !== "undefined" ? window.scrollY : window.pageYOffset;
          assert(typeof scrollX !== "undefined" && typeof scrollY !== "undefined", "Unable to retrieve scroll position, mouse positions likely broken.");
          if (event.type === "touchstart" || event.type === "touchend" || event.type === "touchmove") {
            const { touch } = event;
            if (touch === void 0) {
              return;
            }
            let adjustedX = touch.pageX - (scrollX + rect.left);
            let adjustedY = touch.pageY - (scrollY + rect.top);
            adjustedX = adjustedX * (cw / rect.width);
            adjustedY = adjustedY * (ch / rect.height);
            const coords = { x: adjustedX, y: adjustedY };
            if (event.type === "touchstart") {
              Browser.lastTouches[touch.identifier] = coords;
              Browser.touches[touch.identifier] = coords;
            } else if (event.type === "touchend" || event.type === "touchmove") {
              let last = Browser.touches[touch.identifier];
              if (!last)
                last = coords;
              Browser.lastTouches[touch.identifier] = last;
              Browser.touches[touch.identifier] = coords;
            }
            return;
          }
          let x = event.pageX - (scrollX + rect.left);
          let y = event.pageY - (scrollY + rect.top);
          x = x * (cw / rect.width);
          y = y * (ch / rect.height);
          Browser.mouseMovementX = x - Browser.mouseX;
          Browser.mouseMovementY = y - Browser.mouseY;
          Browser.mouseX = x;
          Browser.mouseY = y;
        }
      }, resizeListeners: [], updateResizeListeners() {
        const { canvas } = Module;
        Browser.resizeListeners.forEach((listener) => {
          listener(canvas.width, canvas.height);
        });
      }, setCanvasSize(width, height, noUpdates) {
        const { canvas } = Module;
        Browser.updateCanvasDimensions(canvas, width, height);
        if (!noUpdates)
          Browser.updateResizeListeners();
      }, windowedWidth: 0, windowedHeight: 0, setFullscreenCanvasSize() {
        if (typeof SDL !== "undefined") {
          let flags = HEAPU32[SDL.screen >> 2];
          flags = flags | 8388608;
          HEAP32[SDL.screen >> 2] = flags;
        }
        Browser.updateCanvasDimensions(Module.canvas);
        Browser.updateResizeListeners();
      }, setWindowedCanvasSize() {
        if (typeof SDL !== "undefined") {
          let flags = HEAPU32[SDL.screen >> 2];
          flags = flags & ~8388608;
          HEAP32[SDL.screen >> 2] = flags;
        }
        Browser.updateCanvasDimensions(Module.canvas);
        Browser.updateResizeListeners();
      }, updateCanvasDimensions(canvas, wNative, hNative) {
        if (wNative && hNative) {
          canvas.widthNative = wNative;
          canvas.heightNative = hNative;
        } else {
          wNative = canvas.widthNative;
          hNative = canvas.heightNative;
        }
        let w = wNative;
        let h = hNative;
        if (Module.forcedAspectRatio && Module.forcedAspectRatio > 0) {
          if (w / h < Module.forcedAspectRatio) {
            w = Math.round(h * Module.forcedAspectRatio);
          } else {
            h = Math.round(w / Module.forcedAspectRatio);
          }
        }
        if ((document.fullscreenElement || document.mozFullScreenElement || document.msFullscreenElement || document.webkitFullscreenElement || document.webkitCurrentFullScreenElement) === canvas.parentNode && typeof screen !== "undefined") {
          const factor = Math.min(screen.width / w, screen.height / h);
          w = Math.round(w * factor);
          h = Math.round(h * factor);
        }
        if (Browser.resizeCanvas) {
          if (canvas.width != w)
            canvas.width = w;
          if (canvas.height != h)
            canvas.height = h;
          if (typeof canvas.style !== "undefined") {
            canvas.style.removeProperty("width");
            canvas.style.removeProperty("height");
          }
        } else {
          if (canvas.width != wNative)
            canvas.width = wNative;
          if (canvas.height != hNative)
            canvas.height = hNative;
          if (typeof canvas.style !== "undefined") {
            if (w != wNative || h != hNative) {
              canvas.style.setProperty("width", `${w}px`, "important");
              canvas.style.setProperty("height", `${h}px`, "important");
            } else {
              canvas.style.removeProperty("width");
              canvas.style.removeProperty("height");
            }
          }
        }
      } };
      function callRuntimeCallbacks(callbacks) {
        while (callbacks.length > 0) {
          const callback = callbacks.shift();
          if (typeof callback === "function") {
            callback(Module);
            continue;
          }
          const { func } = callback;
          if (typeof func === "number") {
            if (callback.arg === void 0) {
              wasmTable.get(func)();
            } else {
              wasmTable.get(func)(callback.arg);
            }
          } else {
            func(callback.arg === void 0 ? null : callback.arg);
          }
        }
      }
      function demangle(func) {
        warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling");
        return func;
      }
      function demangleAll(text) {
        const regex = /\b_Z[\w\d_]+/g;
        return text.replace(regex, (x) => {
          const y = demangle(x);
          return x === y ? x : `${y} [${x}]`;
        });
      }
      function jsStackTrace() {
        let error = new Error();
        if (!error.stack) {
          try {
            throw new Error();
          } catch (e) {
            error = e;
          }
          if (!error.stack) {
            return "(no stack trace available)";
          }
        }
        return error.stack.toString();
      }
      function stackTrace() {
        let js = jsStackTrace();
        if (Module.extraStackTrace)
          js += `
${Module.extraStackTrace()}`;
        return demangleAll(js);
      }
      function ___cxa_allocate_exception(size) {
        return _malloc(size + 16) + 16;
      }
      function _atexit(func, arg) {
      }
      function ___cxa_atexit(a0, a1) {
        return _atexit(a0, a1);
      }
      function ExceptionInfo(excPtr) {
        this.excPtr = excPtr;
        this.ptr = excPtr - 16;
        this.set_type = function(type) {
          HEAP32[this.ptr + 4 >> 2] = type;
        };
        this.get_type = function() {
          return HEAP32[this.ptr + 4 >> 2];
        };
        this.set_destructor = function(destructor) {
          HEAP32[this.ptr + 8 >> 2] = destructor;
        };
        this.get_destructor = function() {
          return HEAP32[this.ptr + 8 >> 2];
        };
        this.set_refcount = function(refcount) {
          HEAP32[this.ptr >> 2] = refcount;
        };
        this.set_caught = function(caught) {
          caught = caught ? 1 : 0;
          HEAP8[this.ptr + 12 >> 0] = caught;
        };
        this.get_caught = function() {
          return HEAP8[this.ptr + 12 >> 0] != 0;
        };
        this.set_rethrown = function(rethrown) {
          rethrown = rethrown ? 1 : 0;
          HEAP8[this.ptr + 13 >> 0] = rethrown;
        };
        this.get_rethrown = function() {
          return HEAP8[this.ptr + 13 >> 0] != 0;
        };
        this.init = function(type, destructor) {
          this.set_type(type);
          this.set_destructor(destructor);
          this.set_refcount(0);
          this.set_caught(false);
          this.set_rethrown(false);
        };
        this.add_ref = function() {
          const value = HEAP32[this.ptr >> 2];
          HEAP32[this.ptr >> 2] = value + 1;
        };
        this.release_ref = function() {
          const prev = HEAP32[this.ptr >> 2];
          HEAP32[this.ptr >> 2] = prev - 1;
          assert(prev > 0);
          return prev === 1;
        };
      }
      function CatchInfo(ptr) {
        this.free = function() {
          _free(this.ptr);
          this.ptr = 0;
        };
        this.set_base_ptr = function(basePtr) {
          HEAP32[this.ptr >> 2] = basePtr;
        };
        this.get_base_ptr = function() {
          return HEAP32[this.ptr >> 2];
        };
        this.set_adjusted_ptr = function(adjustedPtr) {
          HEAP32[this.ptr + 4 >> 2] = adjustedPtr;
        };
        this.get_adjusted_ptr_addr = function() {
          return this.ptr + 4;
        };
        this.get_adjusted_ptr = function() {
          return HEAP32[this.ptr + 4 >> 2];
        };
        this.get_exception_ptr = function() {
          const isPointer = ___cxa_is_pointer_type(this.get_exception_info().get_type());
          if (isPointer) {
            return HEAP32[this.get_base_ptr() >> 2];
          }
          const adjusted = this.get_adjusted_ptr();
          if (adjusted !== 0)
            return adjusted;
          return this.get_base_ptr();
        };
        this.get_exception_info = function() {
          return new ExceptionInfo(this.get_base_ptr());
        };
        if (ptr === void 0) {
          this.ptr = _malloc(8);
          this.set_adjusted_ptr(0);
        } else {
          this.ptr = ptr;
        }
      }
      const exceptionCaught = [];
      function exception_addRef(info) {
        info.add_ref();
      }
      let uncaughtExceptionCount = 0;
      function ___cxa_begin_catch(ptr) {
        const catchInfo = new CatchInfo(ptr);
        const info = catchInfo.get_exception_info();
        if (!info.get_caught()) {
          info.set_caught(true);
          uncaughtExceptionCount--;
        }
        info.set_rethrown(false);
        exceptionCaught.push(catchInfo);
        exception_addRef(info);
        return catchInfo.get_exception_ptr();
      }
      let exceptionLast = 0;
      function ___cxa_free_exception(ptr) {
        try {
          return _free(new ExceptionInfo(ptr).ptr);
        } catch (e) {
          err(`exception during cxa_free_exception: ${e}`);
        }
      }
      function exception_decRef(info) {
        if (info.release_ref() && !info.get_rethrown()) {
          const destructor = info.get_destructor();
          if (destructor) {
            wasmTable.get(destructor)(info.excPtr);
          }
          ___cxa_free_exception(info.excPtr);
        }
      }
      function ___cxa_end_catch() {
        _setThrew(0);
        assert(exceptionCaught.length > 0);
        const catchInfo = exceptionCaught.pop();
        exception_decRef(catchInfo.get_exception_info());
        catchInfo.free();
        exceptionLast = 0;
      }
      function ___resumeException(catchInfoPtr) {
        const catchInfo = new CatchInfo(catchInfoPtr);
        const ptr = catchInfo.get_base_ptr();
        if (!exceptionLast) {
          exceptionLast = ptr;
        }
        catchInfo.free();
        throw `${ptr} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`;
      }
      function ___cxa_find_matching_catch_2() {
        const thrown = exceptionLast;
        if (!thrown) {
          setTempRet0(0);
          return 0 | 0;
        }
        const info = new ExceptionInfo(thrown);
        const thrownType = info.get_type();
        const catchInfo = new CatchInfo();
        catchInfo.set_base_ptr(thrown);
        catchInfo.set_adjusted_ptr(thrown);
        if (!thrownType) {
          setTempRet0(0);
          return catchInfo.ptr | 0;
        }
        const typeArray = Array.prototype.slice.call(arguments);
        for (let i2 = 0; i2 < typeArray.length; i2++) {
          const caughtType = typeArray[i2];
          if (caughtType === 0 || caughtType === thrownType) {
            break;
          }
          if (___cxa_can_catch(caughtType, thrownType, catchInfo.get_adjusted_ptr_addr())) {
            setTempRet0(caughtType);
            return catchInfo.ptr | 0;
          }
        }
        setTempRet0(thrownType);
        return catchInfo.ptr | 0;
      }
      function ___cxa_find_matching_catch_3() {
        const thrown = exceptionLast;
        if (!thrown) {
          setTempRet0(0);
          return 0 | 0;
        }
        const info = new ExceptionInfo(thrown);
        const thrownType = info.get_type();
        const catchInfo = new CatchInfo();
        catchInfo.set_base_ptr(thrown);
        catchInfo.set_adjusted_ptr(thrown);
        if (!thrownType) {
          setTempRet0(0);
          return catchInfo.ptr | 0;
        }
        const typeArray = Array.prototype.slice.call(arguments);
        for (let i2 = 0; i2 < typeArray.length; i2++) {
          const caughtType = typeArray[i2];
          if (caughtType === 0 || caughtType === thrownType) {
            break;
          }
          if (___cxa_can_catch(caughtType, thrownType, catchInfo.get_adjusted_ptr_addr())) {
            setTempRet0(caughtType);
            return catchInfo.ptr | 0;
          }
        }
        setTempRet0(thrownType);
        return catchInfo.ptr | 0;
      }
      function ___cxa_rethrow() {
        const catchInfo = exceptionCaught.pop();
        if (!catchInfo) {
          abort("no exception to throw");
        }
        const info = catchInfo.get_exception_info();
        const ptr = catchInfo.get_base_ptr();
        if (!info.get_rethrown()) {
          exceptionCaught.push(catchInfo);
          info.set_rethrown(true);
          info.set_caught(false);
          uncaughtExceptionCount++;
        } else {
          catchInfo.free();
        }
        exceptionLast = ptr;
        throw `${ptr} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`;
      }
      function ___cxa_thread_atexit(a0, a1) {
        return _atexit(a0, a1);
      }
      function ___cxa_throw(ptr, type, destructor) {
        const info = new ExceptionInfo(ptr);
        info.init(type, destructor);
        exceptionLast = ptr;
        uncaughtExceptionCount++;
        throw `${ptr} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`;
      }
      function setErrNo(value) {
        HEAP32[___errno_location() >> 2] = value;
        return value;
      }
      var PATH = { splitPath(filename) {
        const splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
        return splitPathRe.exec(filename).slice(1);
      }, normalizeArray(parts, allowAboveRoot) {
        let up = 0;
        for (let i2 = parts.length - 1; i2 >= 0; i2--) {
          const last = parts[i2];
          if (last === ".") {
            parts.splice(i2, 1);
          } else if (last === "..") {
            parts.splice(i2, 1);
            up++;
          } else if (up) {
            parts.splice(i2, 1);
            up--;
          }
        }
        if (allowAboveRoot) {
          for (; up; up--) {
            parts.unshift("..");
          }
        }
        return parts;
      }, normalize(path) {
        const isAbsolute = path.charAt(0) === "/";
        const trailingSlash = path.substr(-1) === "/";
        path = PATH.normalizeArray(path.split("/").filter((p) => !!p), !isAbsolute).join("/");
        if (!path && !isAbsolute) {
          path = ".";
        }
        if (path && trailingSlash) {
          path += "/";
        }
        return (isAbsolute ? "/" : "") + path;
      }, dirname(path) {
        const result = PATH.splitPath(path);
        const root = result[0];
        let dir = result[1];
        if (!root && !dir) {
          return ".";
        }
        if (dir) {
          dir = dir.substr(0, dir.length - 1);
        }
        return root + dir;
      }, basename(path) {
        if (path === "/")
          return "/";
        path = PATH.normalize(path);
        path = path.replace(/\/$/, "");
        const lastSlash = path.lastIndexOf("/");
        if (lastSlash === -1)
          return path;
        return path.substr(lastSlash + 1);
      }, extname(path) {
        return PATH.splitPath(path)[3];
      }, join() {
        const paths = Array.prototype.slice.call(arguments, 0);
        return PATH.normalize(paths.join("/"));
      }, join2(l, r) {
        return PATH.normalize(`${l}/${r}`);
      } };
      function getRandomDevice() {
        if (typeof crypto === "object" && typeof crypto.getRandomValues === "function") {
          const randomBuffer = new Uint8Array(1);
          return function() {
            crypto.getRandomValues(randomBuffer);
            return randomBuffer[0];
          };
        }
        if (ENVIRONMENT_IS_NODE) {
          try {
            const crypto_module = __require("crypto");
            return function() {
              return crypto_module.randomBytes(1)[0];
            };
          } catch (e) {
          }
        }
        return function() {
          abort("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: function(array) { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };");
        };
      }
      var PATH_FS = { resolve() {
        let resolvedPath = "";
        let resolvedAbsolute = false;
        for (let i2 = arguments.length - 1; i2 >= -1 && !resolvedAbsolute; i2--) {
          const path = i2 >= 0 ? arguments[i2] : FS.cwd();
          if (typeof path !== "string") {
            throw new TypeError("Arguments to path.resolve must be strings");
          } else if (!path) {
            return "";
          }
          resolvedPath = `${path}/${resolvedPath}`;
          resolvedAbsolute = path.charAt(0) === "/";
        }
        resolvedPath = PATH.normalizeArray(resolvedPath.split("/").filter((p) => !!p), !resolvedAbsolute).join("/");
        return (resolvedAbsolute ? "/" : "") + resolvedPath || ".";
      }, relative(from, to) {
        from = PATH_FS.resolve(from).substr(1);
        to = PATH_FS.resolve(to).substr(1);
        function trim(arr) {
          let start = 0;
          for (; start < arr.length; start++) {
            if (arr[start] !== "")
              break;
          }
          let end = arr.length - 1;
          for (; end >= 0; end--) {
            if (arr[end] !== "")
              break;
          }
          if (start > end)
            return [];
          return arr.slice(start, end - start + 1);
        }
        const fromParts = trim(from.split("/"));
        const toParts = trim(to.split("/"));
        const length = Math.min(fromParts.length, toParts.length);
        let samePartsLength = length;
        for (var i2 = 0; i2 < length; i2++) {
          if (fromParts[i2] !== toParts[i2]) {
            samePartsLength = i2;
            break;
          }
        }
        let outputParts = [];
        for (var i2 = samePartsLength; i2 < fromParts.length; i2++) {
          outputParts.push("..");
        }
        outputParts = outputParts.concat(toParts.slice(samePartsLength));
        return outputParts.join("/");
      } };
      var TTY = { ttys: [], init() {
      }, shutdown() {
      }, register(dev, ops) {
        TTY.ttys[dev] = { input: [], output: [], ops };
        FS.registerDevice(dev, TTY.stream_ops);
      }, stream_ops: { open(stream) {
        const tty = TTY.ttys[stream.node.rdev];
        if (!tty) {
          throw new FS.ErrnoError(43);
        }
        stream.tty = tty;
        stream.seekable = false;
      }, close(stream) {
        stream.tty.ops.flush(stream.tty);
      }, flush(stream) {
        stream.tty.ops.flush(stream.tty);
      }, read(stream, buffer2, offset, length, pos) {
        if (!stream.tty || !stream.tty.ops.get_char) {
          throw new FS.ErrnoError(60);
        }
        let bytesRead = 0;
        for (let i2 = 0; i2 < length; i2++) {
          var result;
          try {
            result = stream.tty.ops.get_char(stream.tty);
          } catch (e) {
            throw new FS.ErrnoError(29);
          }
          if (result === void 0 && bytesRead === 0) {
            throw new FS.ErrnoError(6);
          }
          if (result === null || result === void 0)
            break;
          bytesRead++;
          buffer2[offset + i2] = result;
        }
        if (bytesRead) {
          stream.node.timestamp = Date.now();
        }
        return bytesRead;
      }, write(stream, buffer2, offset, length, pos) {
        if (!stream.tty || !stream.tty.ops.put_char) {
          throw new FS.ErrnoError(60);
        }
        try {
          for (var i2 = 0; i2 < length; i2++) {
            stream.tty.ops.put_char(stream.tty, buffer2[offset + i2]);
          }
        } catch (e) {
          throw new FS.ErrnoError(29);
        }
        if (length) {
          stream.node.timestamp = Date.now();
        }
        return i2;
      } }, default_tty_ops: { get_char(tty) {
        if (!tty.input.length) {
          let result = null;
          if (ENVIRONMENT_IS_NODE) {
            const BUFSIZE = 256;
            const buf = Buffer.alloc(BUFSIZE);
            let bytesRead = 0;
            try {
              bytesRead = nodeFS.readSync(process.stdin.fd, buf, 0, BUFSIZE, null);
            } catch (e) {
              if (e.toString().includes("EOF"))
                bytesRead = 0;
              else
                throw e;
            }
            if (bytesRead > 0) {
              result = buf.slice(0, bytesRead).toString("utf-8");
            } else {
              result = null;
            }
          } else if (typeof window !== "undefined" && typeof window.prompt === "function") {
            result = window.prompt("Input: ");
            if (result !== null) {
              result += "\n";
            }
          } else if (typeof readline === "function") {
            result = readline();
            if (result !== null) {
              result += "\n";
            }
          }
          if (!result) {
            return null;
          }
          tty.input = intArrayFromString(result, true);
        }
        return tty.input.shift();
      }, put_char(tty, val) {
        if (val === null || val === 10) {
          out(UTF8ArrayToString(tty.output, 0));
          tty.output = [];
        } else {
          if (val != 0)
            tty.output.push(val);
        }
      }, flush(tty) {
        if (tty.output && tty.output.length > 0) {
          out(UTF8ArrayToString(tty.output, 0));
          tty.output = [];
        }
      } }, default_tty1_ops: { put_char(tty, val) {
        if (val === null || val === 10) {
          err(UTF8ArrayToString(tty.output, 0));
          tty.output = [];
        } else {
          if (val != 0)
            tty.output.push(val);
        }
      }, flush(tty) {
        if (tty.output && tty.output.length > 0) {
          err(UTF8ArrayToString(tty.output, 0));
          tty.output = [];
        }
      } } };
      function zeroMemory(address, size) {
        HEAPU8.fill(0, address, address + size);
      }
      function alignMemory(size, alignment) {
        assert(alignment, "alignment argument is required");
        return Math.ceil(size / alignment) * alignment;
      }
      function mmapAlloc(size) {
        size = alignMemory(size, 65536);
        const ptr = _memalign(65536, size);
        if (!ptr)
          return 0;
        zeroMemory(ptr, size);
        return ptr;
      }
      var MEMFS = { ops_table: null, mount(mount) {
        return MEMFS.createNode(null, "/", 16384 | 511, 0);
      }, createNode(parent, name, mode, dev) {
        if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
          throw new FS.ErrnoError(63);
        }
        if (!MEMFS.ops_table) {
          MEMFS.ops_table = { dir: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, lookup: MEMFS.node_ops.lookup, mknod: MEMFS.node_ops.mknod, rename: MEMFS.node_ops.rename, unlink: MEMFS.node_ops.unlink, rmdir: MEMFS.node_ops.rmdir, readdir: MEMFS.node_ops.readdir, symlink: MEMFS.node_ops.symlink }, stream: { llseek: MEMFS.stream_ops.llseek } }, file: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: { llseek: MEMFS.stream_ops.llseek, read: MEMFS.stream_ops.read, write: MEMFS.stream_ops.write, allocate: MEMFS.stream_ops.allocate, mmap: MEMFS.stream_ops.mmap, msync: MEMFS.stream_ops.msync } }, link: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, readlink: MEMFS.node_ops.readlink }, stream: {} }, chrdev: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: FS.chrdev_stream_ops } };
        }
        const node = FS.createNode(parent, name, mode, dev);
        if (FS.isDir(node.mode)) {
          node.node_ops = MEMFS.ops_table.dir.node;
          node.stream_ops = MEMFS.ops_table.dir.stream;
          node.contents = {};
        } else if (FS.isFile(node.mode)) {
          node.node_ops = MEMFS.ops_table.file.node;
          node.stream_ops = MEMFS.ops_table.file.stream;
          node.usedBytes = 0;
          node.contents = null;
        } else if (FS.isLink(node.mode)) {
          node.node_ops = MEMFS.ops_table.link.node;
          node.stream_ops = MEMFS.ops_table.link.stream;
        } else if (FS.isChrdev(node.mode)) {
          node.node_ops = MEMFS.ops_table.chrdev.node;
          node.stream_ops = MEMFS.ops_table.chrdev.stream;
        }
        node.timestamp = Date.now();
        if (parent) {
          parent.contents[name] = node;
          parent.timestamp = node.timestamp;
        }
        return node;
      }, getFileDataAsTypedArray(node) {
        if (!node.contents)
          return new Uint8Array(0);
        if (node.contents.subarray)
          return node.contents.subarray(0, node.usedBytes);
        return new Uint8Array(node.contents);
      }, expandFileStorage(node, newCapacity) {
        const prevCapacity = node.contents ? node.contents.length : 0;
        if (prevCapacity >= newCapacity)
          return;
        const CAPACITY_DOUBLING_MAX = 1024 * 1024;
        newCapacity = Math.max(newCapacity, prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125) >>> 0);
        if (prevCapacity != 0)
          newCapacity = Math.max(newCapacity, 256);
        const oldContents = node.contents;
        node.contents = new Uint8Array(newCapacity);
        if (node.usedBytes > 0)
          node.contents.set(oldContents.subarray(0, node.usedBytes), 0);
      }, resizeFileStorage(node, newSize) {
        if (node.usedBytes == newSize)
          return;
        if (newSize == 0) {
          node.contents = null;
          node.usedBytes = 0;
        } else {
          const oldContents = node.contents;
          node.contents = new Uint8Array(newSize);
          if (oldContents) {
            node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes)));
          }
          node.usedBytes = newSize;
        }
      }, node_ops: { getattr(node) {
        const attr = {};
        attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
        attr.ino = node.id;
        attr.mode = node.mode;
        attr.nlink = 1;
        attr.uid = 0;
        attr.gid = 0;
        attr.rdev = node.rdev;
        if (FS.isDir(node.mode)) {
          attr.size = 4096;
        } else if (FS.isFile(node.mode)) {
          attr.size = node.usedBytes;
        } else if (FS.isLink(node.mode)) {
          attr.size = node.link.length;
        } else {
          attr.size = 0;
        }
        attr.atime = new Date(node.timestamp);
        attr.mtime = new Date(node.timestamp);
        attr.ctime = new Date(node.timestamp);
        attr.blksize = 4096;
        attr.blocks = Math.ceil(attr.size / attr.blksize);
        return attr;
      }, setattr(node, attr) {
        if (attr.mode !== void 0) {
          node.mode = attr.mode;
        }
        if (attr.timestamp !== void 0) {
          node.timestamp = attr.timestamp;
        }
        if (attr.size !== void 0) {
          MEMFS.resizeFileStorage(node, attr.size);
        }
      }, lookup(parent, name) {
        throw FS.genericErrors[44];
      }, mknod(parent, name, mode, dev) {
        return MEMFS.createNode(parent, name, mode, dev);
      }, rename(old_node, new_dir, new_name) {
        if (FS.isDir(old_node.mode)) {
          let new_node;
          try {
            new_node = FS.lookupNode(new_dir, new_name);
          } catch (e) {
          }
          if (new_node) {
            for (const i2 in new_node.contents) {
              throw new FS.ErrnoError(55);
            }
          }
        }
        delete old_node.parent.contents[old_node.name];
        old_node.parent.timestamp = Date.now();
        old_node.name = new_name;
        new_dir.contents[new_name] = old_node;
        new_dir.timestamp = old_node.parent.timestamp;
        old_node.parent = new_dir;
      }, unlink(parent, name) {
        delete parent.contents[name];
        parent.timestamp = Date.now();
      }, rmdir(parent, name) {
        const node = FS.lookupNode(parent, name);
        for (const i2 in node.contents) {
          throw new FS.ErrnoError(55);
        }
        delete parent.contents[name];
        parent.timestamp = Date.now();
      }, readdir(node) {
        const entries = [".", ".."];
        for (const key2 in node.contents) {
          if (!node.contents.hasOwnProperty(key2)) {
            continue;
          }
          entries.push(key2);
        }
        return entries;
      }, symlink(parent, newname, oldpath) {
        const node = MEMFS.createNode(parent, newname, 511 | 40960, 0);
        node.link = oldpath;
        return node;
      }, readlink(node) {
        if (!FS.isLink(node.mode)) {
          throw new FS.ErrnoError(28);
        }
        return node.link;
      } }, stream_ops: { read(stream, buffer2, offset, length, position) {
        const { contents } = stream.node;
        if (position >= stream.node.usedBytes)
          return 0;
        const size = Math.min(stream.node.usedBytes - position, length);
        assert(size >= 0);
        if (size > 8 && contents.subarray) {
          buffer2.set(contents.subarray(position, position + size), offset);
        } else {
          for (let i2 = 0; i2 < size; i2++)
            buffer2[offset + i2] = contents[position + i2];
        }
        return size;
      }, write(stream, buffer2, offset, length, position, canOwn) {
        assert(!(buffer2 instanceof ArrayBuffer));
        if (buffer2.buffer === HEAP8.buffer) {
          canOwn = false;
        }
        if (!length)
          return 0;
        const { node } = stream;
        node.timestamp = Date.now();
        if (buffer2.subarray && (!node.contents || node.contents.subarray)) {
          if (canOwn) {
            assert(position === 0, "canOwn must imply no weird position inside the file");
            node.contents = buffer2.subarray(offset, offset + length);
            node.usedBytes = length;
            return length;
          }
          if (node.usedBytes === 0 && position === 0) {
            node.contents = buffer2.slice(offset, offset + length);
            node.usedBytes = length;
            return length;
          }
          if (position + length <= node.usedBytes) {
            node.contents.set(buffer2.subarray(offset, offset + length), position);
            return length;
          }
        }
        MEMFS.expandFileStorage(node, position + length);
        if (node.contents.subarray && buffer2.subarray) {
          node.contents.set(buffer2.subarray(offset, offset + length), position);
        } else {
          for (let i2 = 0; i2 < length; i2++) {
            node.contents[position + i2] = buffer2[offset + i2];
          }
        }
        node.usedBytes = Math.max(node.usedBytes, position + length);
        return length;
      }, llseek(stream, offset, whence) {
        let position = offset;
        if (whence === 1) {
          position += stream.position;
        } else if (whence === 2) {
          if (FS.isFile(stream.node.mode)) {
            position += stream.node.usedBytes;
          }
        }
        if (position < 0) {
          throw new FS.ErrnoError(28);
        }
        return position;
      }, allocate(stream, offset, length) {
        MEMFS.expandFileStorage(stream.node, offset + length);
        stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length);
      }, mmap(stream, address, length, position, prot, flags) {
        if (address !== 0) {
          throw new FS.ErrnoError(28);
        }
        if (!FS.isFile(stream.node.mode)) {
          throw new FS.ErrnoError(43);
        }
        let ptr;
        let allocated;
        let { contents } = stream.node;
        if (!(flags & 2) && contents.buffer === buffer) {
          allocated = false;
          ptr = contents.byteOffset;
        } else {
          if (position > 0 || position + length < contents.length) {
            if (contents.subarray) {
              contents = contents.subarray(position, position + length);
            } else {
              contents = Array.prototype.slice.call(contents, position, position + length);
            }
          }
          allocated = true;
          ptr = mmapAlloc(length);
          if (!ptr) {
            throw new FS.ErrnoError(48);
          }
          HEAP8.set(contents, ptr);
        }
        return { ptr, allocated };
      }, msync(stream, buffer2, offset, length, mmapFlags) {
        if (!FS.isFile(stream.node.mode)) {
          throw new FS.ErrnoError(43);
        }
        if (mmapFlags & 2) {
          return 0;
        }
        const bytesWritten = MEMFS.stream_ops.write(stream, buffer2, 0, length, offset, false);
        return 0;
      } } };
      function asyncLoad(url, onload, onerror, noRunDep) {
        const dep = !noRunDep ? getUniqueRunDependency(`al ${url}`) : "";
        readAsync(url, (arrayBuffer) => {
          assert(arrayBuffer, `Loading data file "${url}" failed (no arrayBuffer).`);
          onload(new Uint8Array(arrayBuffer));
          if (dep)
            removeRunDependency(dep);
        }, (event) => {
          if (onerror) {
            onerror();
          } else {
            throw `Loading data file "${url}" failed.`;
          }
        });
        if (dep)
          addRunDependency(dep);
      }
      const ERRNO_MESSAGES = { 0: "Success", 1: "Arg list too long", 2: "Permission denied", 3: "Address already in use", 4: "Address not available", 5: "Address family not supported by protocol family", 6: "No more processes", 7: "Socket already connected", 8: "Bad file number", 9: "Trying to read unreadable message", 10: "Mount device busy", 11: "Operation canceled", 12: "No children", 13: "Connection aborted", 14: "Connection refused", 15: "Connection reset by peer", 16: "File locking deadlock error", 17: "Destination address required", 18: "Math arg out of domain of func", 19: "Quota exceeded", 20: "File exists", 21: "Bad address", 22: "File too large", 23: "Host is unreachable", 24: "Identifier removed", 25: "Illegal byte sequence", 26: "Connection already in progress", 27: "Interrupted system call", 28: "Invalid argument", 29: "I/O error", 30: "Socket is already connected", 31: "Is a directory", 32: "Too many symbolic links", 33: "Too many open files", 34: "Too many links", 35: "Message too long", 36: "Multihop attempted", 37: "File or path name too long", 38: "Network interface is not configured", 39: "Connection reset by network", 40: "Network is unreachable", 41: "Too many open files in system", 42: "No buffer space available", 43: "No such device", 44: "No such file or directory", 45: "Exec format error", 46: "No record locks available", 47: "The link has been severed", 48: "Not enough core", 49: "No message of desired type", 50: "Protocol not available", 51: "No space left on device", 52: "Function not implemented", 53: "Socket is not connected", 54: "Not a directory", 55: "Directory not empty", 56: "State not recoverable", 57: "Socket operation on non-socket", 59: "Not a typewriter", 60: "No such device or address", 61: "Value too large for defined data type", 62: "Previous owner died", 63: "Not super-user", 64: "Broken pipe", 65: "Protocol error", 66: "Unknown protocol", 67: "Protocol wrong type for socket", 68: "Math result not representable", 69: "Read only file system", 70: "Illegal seek", 71: "No such process", 72: "Stale file handle", 73: "Connection timed out", 74: "Text file busy", 75: "Cross-device link", 100: "Device not a stream", 101: "Bad font file fmt", 102: "Invalid slot", 103: "Invalid request code", 104: "No anode", 105: "Block device required", 106: "Channel number out of range", 107: "Level 3 halted", 108: "Level 3 reset", 109: "Link number out of range", 110: "Protocol driver not attached", 111: "No CSI structure available", 112: "Level 2 halted", 113: "Invalid exchange", 114: "Invalid request descriptor", 115: "Exchange full", 116: "No data (for no delay io)", 117: "Timer expired", 118: "Out of streams resources", 119: "Machine is not on the network", 120: "Package not installed", 121: "The object is remote", 122: "Advertise error", 123: "Srmount error", 124: "Communication error on send", 125: "Cross mount point (not really error)", 126: "Given log. name not unique", 127: "f.d. invalid for this operation", 128: "Remote address changed", 129: "Can   access a needed shared lib", 130: "Accessing a corrupted shared lib", 131: ".lib section in a.out corrupted", 132: "Attempting to link in too many libs", 133: "Attempting to exec a shared library", 135: "Streams pipe error", 136: "Too many users", 137: "Socket type not supported", 138: "Not supported", 139: "Protocol family not supported", 140: "Can't send after socket shutdown", 141: "Too many references", 142: "Host is down", 148: "No medium (in tape drive)", 156: "Level 2 not synchronized" };
      const ERRNO_CODES = { EPERM: 63, ENOENT: 44, ESRCH: 71, EINTR: 27, EIO: 29, ENXIO: 60, E2BIG: 1, ENOEXEC: 45, EBADF: 8, ECHILD: 12, EAGAIN: 6, EWOULDBLOCK: 6, ENOMEM: 48, EACCES: 2, EFAULT: 21, ENOTBLK: 105, EBUSY: 10, EEXIST: 20, EXDEV: 75, ENODEV: 43, ENOTDIR: 54, EISDIR: 31, EINVAL: 28, ENFILE: 41, EMFILE: 33, ENOTTY: 59, ETXTBSY: 74, EFBIG: 22, ENOSPC: 51, ESPIPE: 70, EROFS: 69, EMLINK: 34, EPIPE: 64, EDOM: 18, ERANGE: 68, ENOMSG: 49, EIDRM: 24, ECHRNG: 106, EL2NSYNC: 156, EL3HLT: 107, EL3RST: 108, ELNRNG: 109, EUNATCH: 110, ENOCSI: 111, EL2HLT: 112, EDEADLK: 16, ENOLCK: 46, EBADE: 113, EBADR: 114, EXFULL: 115, ENOANO: 104, EBADRQC: 103, EBADSLT: 102, EDEADLOCK: 16, EBFONT: 101, ENOSTR: 100, ENODATA: 116, ETIME: 117, ENOSR: 118, ENONET: 119, ENOPKG: 120, EREMOTE: 121, ENOLINK: 47, EADV: 122, ESRMNT: 123, ECOMM: 124, EPROTO: 65, EMULTIHOP: 36, EDOTDOT: 125, EBADMSG: 9, ENOTUNIQ: 126, EBADFD: 127, EREMCHG: 128, ELIBACC: 129, ELIBBAD: 130, ELIBSCN: 131, ELIBMAX: 132, ELIBEXEC: 133, ENOSYS: 52, ENOTEMPTY: 55, ENAMETOOLONG: 37, ELOOP: 32, EOPNOTSUPP: 138, EPFNOSUPPORT: 139, ECONNRESET: 15, ENOBUFS: 42, EAFNOSUPPORT: 5, EPROTOTYPE: 67, ENOTSOCK: 57, ENOPROTOOPT: 50, ESHUTDOWN: 140, ECONNREFUSED: 14, EADDRINUSE: 3, ECONNABORTED: 13, ENETUNREACH: 40, ENETDOWN: 38, ETIMEDOUT: 73, EHOSTDOWN: 142, EHOSTUNREACH: 23, EINPROGRESS: 26, EALREADY: 7, EDESTADDRREQ: 17, EMSGSIZE: 35, EPROTONOSUPPORT: 66, ESOCKTNOSUPPORT: 137, EADDRNOTAVAIL: 4, ENETRESET: 39, EISCONN: 30, ENOTCONN: 53, ETOOMANYREFS: 141, EUSERS: 136, EDQUOT: 19, ESTALE: 72, ENOTSUP: 138, ENOMEDIUM: 148, EILSEQ: 25, EOVERFLOW: 61, ECANCELED: 11, ENOTRECOVERABLE: 56, EOWNERDEAD: 62, ESTRPIPE: 135 };
      var FS = { root: null, mounts: [], devices: {}, streams: [], nextInode: 1, nameTable: null, currentPath: "/", initialized: false, ignorePermissions: true, trackingDelegate: {}, tracking: { openFlags: { READ: 1, WRITE: 2 } }, ErrnoError: null, genericErrors: {}, filesystems: null, syncFSRequests: 0, lookupPath(path, opts) {
        path = PATH_FS.resolve(FS.cwd(), path);
        opts = opts || {};
        if (!path)
          return { path: "", node: null };
        const defaults = { follow_mount: true, recurse_count: 0 };
        for (const key2 in defaults) {
          if (opts[key2] === void 0) {
            opts[key2] = defaults[key2];
          }
        }
        if (opts.recurse_count > 8) {
          throw new FS.ErrnoError(32);
        }
        const parts = PATH.normalizeArray(path.split("/").filter((p) => !!p), false);
        let current = FS.root;
        let current_path = "/";
        for (let i2 = 0; i2 < parts.length; i2++) {
          const islast = i2 === parts.length - 1;
          if (islast && opts.parent) {
            break;
          }
          current = FS.lookupNode(current, parts[i2]);
          current_path = PATH.join2(current_path, parts[i2]);
          if (FS.isMountpoint(current)) {
            if (!islast || islast && opts.follow_mount) {
              current = current.mounted.root;
            }
          }
          if (!islast || opts.follow) {
            let count = 0;
            while (FS.isLink(current.mode)) {
              const link = FS.readlink(current_path);
              current_path = PATH_FS.resolve(PATH.dirname(current_path), link);
              const lookup = FS.lookupPath(current_path, { recurse_count: opts.recurse_count });
              current = lookup.node;
              if (count++ > 40) {
                throw new FS.ErrnoError(32);
              }
            }
          }
        }
        return { path: current_path, node: current };
      }, getPath(node) {
        let path;
        while (true) {
          if (FS.isRoot(node)) {
            const mount = node.mount.mountpoint;
            if (!path)
              return mount;
            return mount[mount.length - 1] !== "/" ? `${mount}/${path}` : mount + path;
          }
          path = path ? `${node.name}/${path}` : node.name;
          node = node.parent;
        }
      }, hashName(parentid, name) {
        let hash = 0;
        for (let i2 = 0; i2 < name.length; i2++) {
          hash = (hash << 5) - hash + name.charCodeAt(i2) | 0;
        }
        return (parentid + hash >>> 0) % FS.nameTable.length;
      }, hashAddNode(node) {
        const hash = FS.hashName(node.parent.id, node.name);
        node.name_next = FS.nameTable[hash];
        FS.nameTable[hash] = node;
      }, hashRemoveNode(node) {
        const hash = FS.hashName(node.parent.id, node.name);
        if (FS.nameTable[hash] === node) {
          FS.nameTable[hash] = node.name_next;
        } else {
          let current = FS.nameTable[hash];
          while (current) {
            if (current.name_next === node) {
              current.name_next = node.name_next;
              break;
            }
            current = current.name_next;
          }
        }
      }, lookupNode(parent, name) {
        const errCode = FS.mayLookup(parent);
        if (errCode) {
          throw new FS.ErrnoError(errCode, parent);
        }
        const hash = FS.hashName(parent.id, name);
        for (let node = FS.nameTable[hash]; node; node = node.name_next) {
          const nodeName = node.name;
          if (node.parent.id === parent.id && nodeName === name) {
            return node;
          }
        }
        return FS.lookup(parent, name);
      }, createNode(parent, name, mode, rdev) {
        assert(typeof parent === "object");
        const node = new FS.FSNode(parent, name, mode, rdev);
        FS.hashAddNode(node);
        return node;
      }, destroyNode(node) {
        FS.hashRemoveNode(node);
      }, isRoot(node) {
        return node === node.parent;
      }, isMountpoint(node) {
        return !!node.mounted;
      }, isFile(mode) {
        return (mode & 61440) === 32768;
      }, isDir(mode) {
        return (mode & 61440) === 16384;
      }, isLink(mode) {
        return (mode & 61440) === 40960;
      }, isChrdev(mode) {
        return (mode & 61440) === 8192;
      }, isBlkdev(mode) {
        return (mode & 61440) === 24576;
      }, isFIFO(mode) {
        return (mode & 61440) === 4096;
      }, isSocket(mode) {
        return (mode & 49152) === 49152;
      }, flagModes: { r: 0, "r+": 2, w: 577, "w+": 578, a: 1089, "a+": 1090 }, modeStringToFlags(str) {
        const flags = FS.flagModes[str];
        if (typeof flags === "undefined") {
          throw new Error(`Unknown file open mode: ${str}`);
        }
        return flags;
      }, flagsToPermissionString(flag) {
        let perms = ["r", "w", "rw"][flag & 3];
        if (flag & 512) {
          perms += "w";
        }
        return perms;
      }, nodePermissions(node, perms) {
        if (FS.ignorePermissions) {
          return 0;
        }
        if (perms.includes("r") && !(node.mode & 292)) {
          return 2;
        }
        if (perms.includes("w") && !(node.mode & 146)) {
          return 2;
        }
        if (perms.includes("x") && !(node.mode & 73)) {
          return 2;
        }
        return 0;
      }, mayLookup(dir) {
        const errCode = FS.nodePermissions(dir, "x");
        if (errCode)
          return errCode;
        if (!dir.node_ops.lookup)
          return 2;
        return 0;
      }, mayCreate(dir, name) {
        try {
          const node = FS.lookupNode(dir, name);
          return 20;
        } catch (e) {
        }
        return FS.nodePermissions(dir, "wx");
      }, mayDelete(dir, name, isdir) {
        let node;
        try {
          node = FS.lookupNode(dir, name);
        } catch (e) {
          return e.errno;
        }
        const errCode = FS.nodePermissions(dir, "wx");
        if (errCode) {
          return errCode;
        }
        if (isdir) {
          if (!FS.isDir(node.mode)) {
            return 54;
          }
          if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
            return 10;
          }
        } else {
          if (FS.isDir(node.mode)) {
            return 31;
          }
        }
        return 0;
      }, mayOpen(node, flags) {
        if (!node) {
          return 44;
        }
        if (FS.isLink(node.mode)) {
          return 32;
        }
        if (FS.isDir(node.mode)) {
          if (FS.flagsToPermissionString(flags) !== "r" || flags & 512) {
            return 31;
          }
        }
        return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
      }, MAX_OPEN_FDS: 4096, nextfd(fd_start, fd_end) {
        fd_start = fd_start || 0;
        fd_end = fd_end || FS.MAX_OPEN_FDS;
        for (let fd = fd_start; fd <= fd_end; fd++) {
          if (!FS.streams[fd]) {
            return fd;
          }
        }
        throw new FS.ErrnoError(33);
      }, getStream(fd) {
        return FS.streams[fd];
      }, createStream(stream, fd_start, fd_end) {
        if (!FS.FSStream) {
          FS.FSStream = function() {
          };
          FS.FSStream.prototype = { object: { get() {
            return this.node;
          }, set(val) {
            this.node = val;
          } }, isRead: { get() {
            return (this.flags & 2097155) !== 1;
          } }, isWrite: { get() {
            return (this.flags & 2097155) !== 0;
          } }, isAppend: { get() {
            return this.flags & 1024;
          } } };
        }
        const newStream = new FS.FSStream();
        for (const p in stream) {
          newStream[p] = stream[p];
        }
        stream = newStream;
        const fd = FS.nextfd(fd_start, fd_end);
        stream.fd = fd;
        FS.streams[fd] = stream;
        return stream;
      }, closeStream(fd) {
        FS.streams[fd] = null;
      }, chrdev_stream_ops: { open(stream) {
        const device = FS.getDevice(stream.node.rdev);
        stream.stream_ops = device.stream_ops;
        if (stream.stream_ops.open) {
          stream.stream_ops.open(stream);
        }
      }, llseek() {
        throw new FS.ErrnoError(70);
      } }, major(dev) {
        return dev >> 8;
      }, minor(dev) {
        return dev & 255;
      }, makedev(ma, mi) {
        return ma << 8 | mi;
      }, registerDevice(dev, ops) {
        FS.devices[dev] = { stream_ops: ops };
      }, getDevice(dev) {
        return FS.devices[dev];
      }, getMounts(mount) {
        const mounts = [];
        const check = [mount];
        while (check.length) {
          const m = check.pop();
          mounts.push(m);
          check.push.apply(check, m.mounts);
        }
        return mounts;
      }, syncfs(populate, callback) {
        if (typeof populate === "function") {
          callback = populate;
          populate = false;
        }
        FS.syncFSRequests++;
        if (FS.syncFSRequests > 1) {
          err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);
        }
        const mounts = FS.getMounts(FS.root.mount);
        let completed = 0;
        function doCallback(errCode) {
          assert(FS.syncFSRequests > 0);
          FS.syncFSRequests--;
          return callback(errCode);
        }
        function done(errCode) {
          if (errCode) {
            if (!done.errored) {
              done.errored = true;
              return doCallback(errCode);
            }
            return;
          }
          if (++completed >= mounts.length) {
            doCallback(null);
          }
        }
        mounts.forEach((mount) => {
          if (!mount.type.syncfs) {
            return done(null);
          }
          mount.type.syncfs(mount, populate, done);
        });
      }, mount(type, opts, mountpoint) {
        if (typeof type === "string") {
          throw type;
        }
        const root = mountpoint === "/";
        const pseudo = !mountpoint;
        let node;
        if (root && FS.root) {
          throw new FS.ErrnoError(10);
        } else if (!root && !pseudo) {
          const lookup = FS.lookupPath(mountpoint, { follow_mount: false });
          mountpoint = lookup.path;
          node = lookup.node;
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
          if (!FS.isDir(node.mode)) {
            throw new FS.ErrnoError(54);
          }
        }
        const mount = { type, opts, mountpoint, mounts: [] };
        const mountRoot = type.mount(mount);
        mountRoot.mount = mount;
        mount.root = mountRoot;
        if (root) {
          FS.root = mountRoot;
        } else if (node) {
          node.mounted = mount;
          if (node.mount) {
            node.mount.mounts.push(mount);
          }
        }
        return mountRoot;
      }, unmount(mountpoint) {
        const lookup = FS.lookupPath(mountpoint, { follow_mount: false });
        if (!FS.isMountpoint(lookup.node)) {
          throw new FS.ErrnoError(28);
        }
        const { node } = lookup;
        const mount = node.mounted;
        const mounts = FS.getMounts(mount);
        Object.keys(FS.nameTable).forEach((hash) => {
          let current = FS.nameTable[hash];
          while (current) {
            const next = current.name_next;
            if (mounts.includes(current.mount)) {
              FS.destroyNode(current);
            }
            current = next;
          }
        });
        node.mounted = null;
        const idx = node.mount.mounts.indexOf(mount);
        assert(idx !== -1);
        node.mount.mounts.splice(idx, 1);
      }, lookup(parent, name) {
        return parent.node_ops.lookup(parent, name);
      }, mknod(path, mode, dev) {
        const lookup = FS.lookupPath(path, { parent: true });
        const parent = lookup.node;
        const name = PATH.basename(path);
        if (!name || name === "." || name === "..") {
          throw new FS.ErrnoError(28);
        }
        const errCode = FS.mayCreate(parent, name);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.mknod) {
          throw new FS.ErrnoError(63);
        }
        return parent.node_ops.mknod(parent, name, mode, dev);
      }, create(path, mode) {
        mode = mode !== void 0 ? mode : 438;
        mode &= 4095;
        mode |= 32768;
        return FS.mknod(path, mode, 0);
      }, mkdir(path, mode) {
        mode = mode !== void 0 ? mode : 511;
        mode &= 511 | 512;
        mode |= 16384;
        return FS.mknod(path, mode, 0);
      }, mkdirTree(path, mode) {
        const dirs = path.split("/");
        let d = "";
        for (let i2 = 0; i2 < dirs.length; ++i2) {
          if (!dirs[i2])
            continue;
          d += `/${dirs[i2]}`;
          try {
            FS.mkdir(d, mode);
          } catch (e) {
            if (e.errno != 20)
              throw e;
          }
        }
      }, mkdev(path, mode, dev) {
        if (typeof dev === "undefined") {
          dev = mode;
          mode = 438;
        }
        mode |= 8192;
        return FS.mknod(path, mode, dev);
      }, symlink(oldpath, newpath) {
        if (!PATH_FS.resolve(oldpath)) {
          throw new FS.ErrnoError(44);
        }
        const lookup = FS.lookupPath(newpath, { parent: true });
        const parent = lookup.node;
        if (!parent) {
          throw new FS.ErrnoError(44);
        }
        const newname = PATH.basename(newpath);
        const errCode = FS.mayCreate(parent, newname);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.symlink) {
          throw new FS.ErrnoError(63);
        }
        return parent.node_ops.symlink(parent, newname, oldpath);
      }, rename(old_path, new_path) {
        const old_dirname = PATH.dirname(old_path);
        const new_dirname = PATH.dirname(new_path);
        const old_name = PATH.basename(old_path);
        const new_name = PATH.basename(new_path);
        let lookup;
        let old_dir;
        let new_dir;
        lookup = FS.lookupPath(old_path, { parent: true });
        old_dir = lookup.node;
        lookup = FS.lookupPath(new_path, { parent: true });
        new_dir = lookup.node;
        if (!old_dir || !new_dir)
          throw new FS.ErrnoError(44);
        if (old_dir.mount !== new_dir.mount) {
          throw new FS.ErrnoError(75);
        }
        const old_node = FS.lookupNode(old_dir, old_name);
        let relative = PATH_FS.relative(old_path, new_dirname);
        if (relative.charAt(0) !== ".") {
          throw new FS.ErrnoError(28);
        }
        relative = PATH_FS.relative(new_path, old_dirname);
        if (relative.charAt(0) !== ".") {
          throw new FS.ErrnoError(55);
        }
        let new_node;
        try {
          new_node = FS.lookupNode(new_dir, new_name);
        } catch (e) {
        }
        if (old_node === new_node) {
          return;
        }
        const isdir = FS.isDir(old_node.mode);
        let errCode = FS.mayDelete(old_dir, old_name, isdir);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        errCode = new_node ? FS.mayDelete(new_dir, new_name, isdir) : FS.mayCreate(new_dir, new_name);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!old_dir.node_ops.rename) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(old_node) || new_node && FS.isMountpoint(new_node)) {
          throw new FS.ErrnoError(10);
        }
        if (new_dir !== old_dir) {
          errCode = FS.nodePermissions(old_dir, "w");
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
        }
        try {
          if (FS.trackingDelegate.willMovePath) {
            FS.trackingDelegate.willMovePath(old_path, new_path);
          }
        } catch (e) {
          err(`FS.trackingDelegate['willMovePath']('${old_path}', '${new_path}') threw an exception: ${e.message}`);
        }
        FS.hashRemoveNode(old_node);
        try {
          old_dir.node_ops.rename(old_node, new_dir, new_name);
        } catch (e) {
          throw e;
        } finally {
          FS.hashAddNode(old_node);
        }
        try {
          if (FS.trackingDelegate.onMovePath)
            FS.trackingDelegate.onMovePath(old_path, new_path);
        } catch (e) {
          err(`FS.trackingDelegate['onMovePath']('${old_path}', '${new_path}') threw an exception: ${e.message}`);
        }
      }, rmdir(path) {
        const lookup = FS.lookupPath(path, { parent: true });
        const parent = lookup.node;
        const name = PATH.basename(path);
        const node = FS.lookupNode(parent, name);
        const errCode = FS.mayDelete(parent, name, true);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.rmdir) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(node)) {
          throw new FS.ErrnoError(10);
        }
        try {
          if (FS.trackingDelegate.willDeletePath) {
            FS.trackingDelegate.willDeletePath(path);
          }
        } catch (e) {
          err(`FS.trackingDelegate['willDeletePath']('${path}') threw an exception: ${e.message}`);
        }
        parent.node_ops.rmdir(parent, name);
        FS.destroyNode(node);
        try {
          if (FS.trackingDelegate.onDeletePath)
            FS.trackingDelegate.onDeletePath(path);
        } catch (e) {
          err(`FS.trackingDelegate['onDeletePath']('${path}') threw an exception: ${e.message}`);
        }
      }, readdir(path) {
        const lookup = FS.lookupPath(path, { follow: true });
        const { node } = lookup;
        if (!node.node_ops.readdir) {
          throw new FS.ErrnoError(54);
        }
        return node.node_ops.readdir(node);
      }, unlink(path) {
        const lookup = FS.lookupPath(path, { parent: true });
        const parent = lookup.node;
        const name = PATH.basename(path);
        const node = FS.lookupNode(parent, name);
        const errCode = FS.mayDelete(parent, name, false);
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        if (!parent.node_ops.unlink) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isMountpoint(node)) {
          throw new FS.ErrnoError(10);
        }
        try {
          if (FS.trackingDelegate.willDeletePath) {
            FS.trackingDelegate.willDeletePath(path);
          }
        } catch (e) {
          err(`FS.trackingDelegate['willDeletePath']('${path}') threw an exception: ${e.message}`);
        }
        parent.node_ops.unlink(parent, name);
        FS.destroyNode(node);
        try {
          if (FS.trackingDelegate.onDeletePath)
            FS.trackingDelegate.onDeletePath(path);
        } catch (e) {
          err(`FS.trackingDelegate['onDeletePath']('${path}') threw an exception: ${e.message}`);
        }
      }, readlink(path) {
        const lookup = FS.lookupPath(path);
        const link = lookup.node;
        if (!link) {
          throw new FS.ErrnoError(44);
        }
        if (!link.node_ops.readlink) {
          throw new FS.ErrnoError(28);
        }
        return PATH_FS.resolve(FS.getPath(link.parent), link.node_ops.readlink(link));
      }, stat(path, dontFollow) {
        const lookup = FS.lookupPath(path, { follow: !dontFollow });
        const { node } = lookup;
        if (!node) {
          throw new FS.ErrnoError(44);
        }
        if (!node.node_ops.getattr) {
          throw new FS.ErrnoError(63);
        }
        return node.node_ops.getattr(node);
      }, lstat(path) {
        return FS.stat(path, true);
      }, chmod(path, mode, dontFollow) {
        let node;
        if (typeof path === "string") {
          const lookup = FS.lookupPath(path, { follow: !dontFollow });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        node.node_ops.setattr(node, { mode: mode & 4095 | node.mode & ~4095, timestamp: Date.now() });
      }, lchmod(path, mode) {
        FS.chmod(path, mode, true);
      }, fchmod(fd, mode) {
        const stream = FS.getStream(fd);
        if (!stream) {
          throw new FS.ErrnoError(8);
        }
        FS.chmod(stream.node, mode);
      }, chown(path, uid, gid, dontFollow) {
        let node;
        if (typeof path === "string") {
          const lookup = FS.lookupPath(path, { follow: !dontFollow });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        node.node_ops.setattr(node, { timestamp: Date.now() });
      }, lchown(path, uid, gid) {
        FS.chown(path, uid, gid, true);
      }, fchown(fd, uid, gid) {
        const stream = FS.getStream(fd);
        if (!stream) {
          throw new FS.ErrnoError(8);
        }
        FS.chown(stream.node, uid, gid);
      }, truncate(path, len) {
        if (len < 0) {
          throw new FS.ErrnoError(28);
        }
        let node;
        if (typeof path === "string") {
          const lookup = FS.lookupPath(path, { follow: true });
          node = lookup.node;
        } else {
          node = path;
        }
        if (!node.node_ops.setattr) {
          throw new FS.ErrnoError(63);
        }
        if (FS.isDir(node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!FS.isFile(node.mode)) {
          throw new FS.ErrnoError(28);
        }
        const errCode = FS.nodePermissions(node, "w");
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        node.node_ops.setattr(node, { size: len, timestamp: Date.now() });
      }, ftruncate(fd, len) {
        const stream = FS.getStream(fd);
        if (!stream) {
          throw new FS.ErrnoError(8);
        }
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(28);
        }
        FS.truncate(stream.node, len);
      }, utime(path, atime, mtime) {
        const lookup = FS.lookupPath(path, { follow: true });
        const { node } = lookup;
        node.node_ops.setattr(node, { timestamp: Math.max(atime, mtime) });
      }, open(path, flags, mode, fd_start, fd_end) {
        if (path === "") {
          throw new FS.ErrnoError(44);
        }
        flags = typeof flags === "string" ? FS.modeStringToFlags(flags) : flags;
        mode = typeof mode === "undefined" ? 438 : mode;
        if (flags & 64) {
          mode = mode & 4095 | 32768;
        } else {
          mode = 0;
        }
        let node;
        if (typeof path === "object") {
          node = path;
        } else {
          path = PATH.normalize(path);
          try {
            const lookup = FS.lookupPath(path, { follow: !(flags & 131072) });
            node = lookup.node;
          } catch (e) {
          }
        }
        let created = false;
        if (flags & 64) {
          if (node) {
            if (flags & 128) {
              throw new FS.ErrnoError(20);
            }
          } else {
            node = FS.mknod(path, mode, 0);
            created = true;
          }
        }
        if (!node) {
          throw new FS.ErrnoError(44);
        }
        if (FS.isChrdev(node.mode)) {
          flags &= ~512;
        }
        if (flags & 65536 && !FS.isDir(node.mode)) {
          throw new FS.ErrnoError(54);
        }
        if (!created) {
          const errCode = FS.mayOpen(node, flags);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
        }
        if (flags & 512) {
          FS.truncate(node, 0);
        }
        flags &= ~(128 | 512 | 131072);
        const stream = FS.createStream({ node, path: FS.getPath(node), flags, seekable: true, position: 0, stream_ops: node.stream_ops, ungotten: [], error: false }, fd_start, fd_end);
        if (stream.stream_ops.open) {
          stream.stream_ops.open(stream);
        }
        if (Module.logReadFiles && !(flags & 1)) {
          if (!FS.readFiles)
            FS.readFiles = {};
          if (!(path in FS.readFiles)) {
            FS.readFiles[path] = 1;
            err(`FS.trackingDelegate error on read file: ${path}`);
          }
        }
        try {
          if (FS.trackingDelegate.onOpenFile) {
            let trackingFlags = 0;
            if ((flags & 2097155) !== 1) {
              trackingFlags |= FS.tracking.openFlags.READ;
            }
            if ((flags & 2097155) !== 0) {
              trackingFlags |= FS.tracking.openFlags.WRITE;
            }
            FS.trackingDelegate.onOpenFile(path, trackingFlags);
          }
        } catch (e) {
          err(`FS.trackingDelegate['onOpenFile']('${path}', flags) threw an exception: ${e.message}`);
        }
        return stream;
      }, close(stream) {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (stream.getdents)
          stream.getdents = null;
        try {
          if (stream.stream_ops.close) {
            stream.stream_ops.close(stream);
          }
        } catch (e) {
          throw e;
        } finally {
          FS.closeStream(stream.fd);
        }
        stream.fd = null;
      }, isClosed(stream) {
        return stream.fd === null;
      }, llseek(stream, offset, whence) {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (!stream.seekable || !stream.stream_ops.llseek) {
          throw new FS.ErrnoError(70);
        }
        if (whence != 0 && whence != 1 && whence != 2) {
          throw new FS.ErrnoError(28);
        }
        stream.position = stream.stream_ops.llseek(stream, offset, whence);
        stream.ungotten = [];
        return stream.position;
      }, read(stream, buffer2, offset, length, position) {
        if (length < 0 || position < 0) {
          throw new FS.ErrnoError(28);
        }
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if ((stream.flags & 2097155) === 1) {
          throw new FS.ErrnoError(8);
        }
        if (FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!stream.stream_ops.read) {
          throw new FS.ErrnoError(28);
        }
        const seeking = typeof position !== "undefined";
        if (!seeking) {
          position = stream.position;
        } else if (!stream.seekable) {
          throw new FS.ErrnoError(70);
        }
        const bytesRead = stream.stream_ops.read(stream, buffer2, offset, length, position);
        if (!seeking)
          stream.position += bytesRead;
        return bytesRead;
      }, write(stream, buffer2, offset, length, position, canOwn) {
        if (length < 0 || position < 0) {
          throw new FS.ErrnoError(28);
        }
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(8);
        }
        if (FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(31);
        }
        if (!stream.stream_ops.write) {
          throw new FS.ErrnoError(28);
        }
        if (stream.seekable && stream.flags & 1024) {
          FS.llseek(stream, 0, 2);
        }
        const seeking = typeof position !== "undefined";
        if (!seeking) {
          position = stream.position;
        } else if (!stream.seekable) {
          throw new FS.ErrnoError(70);
        }
        const bytesWritten = stream.stream_ops.write(stream, buffer2, offset, length, position, canOwn);
        if (!seeking)
          stream.position += bytesWritten;
        try {
          if (stream.path && FS.trackingDelegate.onWriteToFile)
            FS.trackingDelegate.onWriteToFile(stream.path);
        } catch (e) {
          err(`FS.trackingDelegate['onWriteToFile']('${stream.path}') threw an exception: ${e.message}`);
        }
        return bytesWritten;
      }, allocate(stream, offset, length) {
        if (FS.isClosed(stream)) {
          throw new FS.ErrnoError(8);
        }
        if (offset < 0 || length <= 0) {
          throw new FS.ErrnoError(28);
        }
        if ((stream.flags & 2097155) === 0) {
          throw new FS.ErrnoError(8);
        }
        if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {
          throw new FS.ErrnoError(43);
        }
        if (!stream.stream_ops.allocate) {
          throw new FS.ErrnoError(138);
        }
        stream.stream_ops.allocate(stream, offset, length);
      }, mmap(stream, address, length, position, prot, flags) {
        if ((prot & 2) !== 0 && (flags & 2) === 0 && (stream.flags & 2097155) !== 2) {
          throw new FS.ErrnoError(2);
        }
        if ((stream.flags & 2097155) === 1) {
          throw new FS.ErrnoError(2);
        }
        if (!stream.stream_ops.mmap) {
          throw new FS.ErrnoError(43);
        }
        return stream.stream_ops.mmap(stream, address, length, position, prot, flags);
      }, msync(stream, buffer2, offset, length, mmapFlags) {
        if (!stream || !stream.stream_ops.msync) {
          return 0;
        }
        return stream.stream_ops.msync(stream, buffer2, offset, length, mmapFlags);
      }, munmap(stream) {
        return 0;
      }, ioctl(stream, cmd, arg) {
        if (!stream.stream_ops.ioctl) {
          throw new FS.ErrnoError(59);
        }
        return stream.stream_ops.ioctl(stream, cmd, arg);
      }, readFile(path, opts) {
        opts = opts || {};
        opts.flags = opts.flags || 0;
        opts.encoding = opts.encoding || "binary";
        if (opts.encoding !== "utf8" && opts.encoding !== "binary") {
          throw new Error(`Invalid encoding type "${opts.encoding}"`);
        }
        let ret;
        const stream = FS.open(path, opts.flags);
        const stat = FS.stat(path);
        const length = stat.size;
        const buf = new Uint8Array(length);
        FS.read(stream, buf, 0, length, 0);
        if (opts.encoding === "utf8") {
          ret = UTF8ArrayToString(buf, 0);
        } else if (opts.encoding === "binary") {
          ret = buf;
        }
        FS.close(stream);
        return ret;
      }, writeFile(path, data, opts) {
        opts = opts || {};
        opts.flags = opts.flags || 577;
        const stream = FS.open(path, opts.flags, opts.mode);
        if (typeof data === "string") {
          const buf = new Uint8Array(lengthBytesUTF8(data) + 1);
          const actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length);
          FS.write(stream, buf, 0, actualNumBytes, void 0, opts.canOwn);
        } else if (ArrayBuffer.isView(data)) {
          FS.write(stream, data, 0, data.byteLength, void 0, opts.canOwn);
        } else {
          throw new Error("Unsupported data type");
        }
        FS.close(stream);
      }, cwd() {
        return FS.currentPath;
      }, chdir(path) {
        const lookup = FS.lookupPath(path, { follow: true });
        if (lookup.node === null) {
          throw new FS.ErrnoError(44);
        }
        if (!FS.isDir(lookup.node.mode)) {
          throw new FS.ErrnoError(54);
        }
        const errCode = FS.nodePermissions(lookup.node, "x");
        if (errCode) {
          throw new FS.ErrnoError(errCode);
        }
        FS.currentPath = lookup.path;
      }, createDefaultDirectories() {
        FS.mkdir("/tmp");
        FS.mkdir("/home");
        FS.mkdir("/home/<USER>");
      }, createDefaultDevices() {
        FS.mkdir("/dev");
        FS.registerDevice(FS.makedev(1, 3), { read() {
          return 0;
        }, write(stream, buffer2, offset, length, pos) {
          return length;
        } });
        FS.mkdev("/dev/null", FS.makedev(1, 3));
        TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
        TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
        FS.mkdev("/dev/tty", FS.makedev(5, 0));
        FS.mkdev("/dev/tty1", FS.makedev(6, 0));
        const random_device = getRandomDevice();
        FS.createDevice("/dev", "random", random_device);
        FS.createDevice("/dev", "urandom", random_device);
        FS.mkdir("/dev/shm");
        FS.mkdir("/dev/shm/tmp");
      }, createSpecialDirectories() {
        FS.mkdir("/proc");
        const proc_self = FS.mkdir("/proc/self");
        FS.mkdir("/proc/self/fd");
        FS.mount({ mount() {
          const node = FS.createNode(proc_self, "fd", 16384 | 511, 73);
          node.node_ops = { lookup(parent, name) {
            const fd = +name;
            const stream = FS.getStream(fd);
            if (!stream)
              throw new FS.ErrnoError(8);
            const ret = { parent: null, mount: { mountpoint: "fake" }, node_ops: { readlink() {
              return stream.path;
            } } };
            ret.parent = ret;
            return ret;
          } };
          return node;
        } }, {}, "/proc/self/fd");
      }, createStandardStreams() {
        if (Module.stdin) {
          FS.createDevice("/dev", "stdin", Module.stdin);
        } else {
          FS.symlink("/dev/tty", "/dev/stdin");
        }
        if (Module.stdout) {
          FS.createDevice("/dev", "stdout", null, Module.stdout);
        } else {
          FS.symlink("/dev/tty", "/dev/stdout");
        }
        if (Module.stderr) {
          FS.createDevice("/dev", "stderr", null, Module.stderr);
        } else {
          FS.symlink("/dev/tty1", "/dev/stderr");
        }
        const stdin = FS.open("/dev/stdin", 0);
        const stdout = FS.open("/dev/stdout", 1);
        const stderr = FS.open("/dev/stderr", 1);
        assert(stdin.fd === 0, `invalid handle for stdin (${stdin.fd})`);
        assert(stdout.fd === 1, `invalid handle for stdout (${stdout.fd})`);
        assert(stderr.fd === 2, `invalid handle for stderr (${stderr.fd})`);
      }, ensureErrnoError() {
        if (FS.ErrnoError)
          return;
        FS.ErrnoError = function ErrnoError(errno, node) {
          this.node = node;
          this.setErrno = function(errno2) {
            this.errno = errno2;
            for (const key2 in ERRNO_CODES) {
              if (ERRNO_CODES[key2] === errno2) {
                this.code = key2;
                break;
              }
            }
          };
          this.setErrno(errno);
          this.message = ERRNO_MESSAGES[errno];
          if (this.stack) {
            Object.defineProperty(this, "stack", { value: new Error().stack, writable: true });
            this.stack = demangleAll(this.stack);
          }
        };
        FS.ErrnoError.prototype = new Error();
        FS.ErrnoError.prototype.constructor = FS.ErrnoError;
        [44].forEach((code) => {
          FS.genericErrors[code] = new FS.ErrnoError(code);
          FS.genericErrors[code].stack = "<generic error, no stack>";
        });
      }, staticInit() {
        FS.ensureErrnoError();
        FS.nameTable = new Array(4096);
        FS.mount(MEMFS, {}, "/");
        FS.createDefaultDirectories();
        FS.createDefaultDevices();
        FS.createSpecialDirectories();
        FS.filesystems = { MEMFS };
      }, init(input, output, error) {
        assert(!FS.init.initialized, "FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)");
        FS.init.initialized = true;
        FS.ensureErrnoError();
        Module.stdin = input || Module.stdin;
        Module.stdout = output || Module.stdout;
        Module.stderr = error || Module.stderr;
        FS.createStandardStreams();
      }, quit() {
        FS.init.initialized = false;
        const fflush = Module._fflush;
        if (fflush)
          fflush(0);
        for (let i2 = 0; i2 < FS.streams.length; i2++) {
          const stream = FS.streams[i2];
          if (!stream) {
            continue;
          }
          FS.close(stream);
        }
      }, getMode(canRead, canWrite) {
        let mode = 0;
        if (canRead)
          mode |= 292 | 73;
        if (canWrite)
          mode |= 146;
        return mode;
      }, findObject(path, dontResolveLastLink) {
        const ret = FS.analyzePath(path, dontResolveLastLink);
        if (ret.exists) {
          return ret.object;
        }
        return null;
      }, analyzePath(path, dontResolveLastLink) {
        try {
          var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
          path = lookup.path;
        } catch (e) {
        }
        const ret = { isRoot: false, exists: false, error: 0, name: null, path: null, object: null, parentExists: false, parentPath: null, parentObject: null };
        try {
          var lookup = FS.lookupPath(path, { parent: true });
          ret.parentExists = true;
          ret.parentPath = lookup.path;
          ret.parentObject = lookup.node;
          ret.name = PATH.basename(path);
          lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
          ret.exists = true;
          ret.path = lookup.path;
          ret.object = lookup.node;
          ret.name = lookup.node.name;
          ret.isRoot = lookup.path === "/";
        } catch (e) {
          ret.error = e.errno;
        }
        return ret;
      }, createPath(parent, path, canRead, canWrite) {
        parent = typeof parent === "string" ? parent : FS.getPath(parent);
        const parts = path.split("/").reverse();
        while (parts.length) {
          const part = parts.pop();
          if (!part)
            continue;
          var current = PATH.join2(parent, part);
          try {
            FS.mkdir(current);
          } catch (e) {
          }
          parent = current;
        }
        return current;
      }, createFile(parent, name, properties, canRead, canWrite) {
        const path = PATH.join2(typeof parent === "string" ? parent : FS.getPath(parent), name);
        const mode = FS.getMode(canRead, canWrite);
        return FS.create(path, mode);
      }, createDataFile(parent, name, data, canRead, canWrite, canOwn) {
        const path = name ? PATH.join2(typeof parent === "string" ? parent : FS.getPath(parent), name) : parent;
        const mode = FS.getMode(canRead, canWrite);
        const node = FS.create(path, mode);
        if (data) {
          if (typeof data === "string") {
            const arr = new Array(data.length);
            for (let i2 = 0, len = data.length; i2 < len; ++i2)
              arr[i2] = data.charCodeAt(i2);
            data = arr;
          }
          FS.chmod(node, mode | 146);
          const stream = FS.open(node, 577);
          FS.write(stream, data, 0, data.length, 0, canOwn);
          FS.close(stream);
          FS.chmod(node, mode);
        }
        return node;
      }, createDevice(parent, name, input, output) {
        const path = PATH.join2(typeof parent === "string" ? parent : FS.getPath(parent), name);
        const mode = FS.getMode(!!input, !!output);
        if (!FS.createDevice.major)
          FS.createDevice.major = 64;
        const dev = FS.makedev(FS.createDevice.major++, 0);
        FS.registerDevice(dev, { open(stream) {
          stream.seekable = false;
        }, close(stream) {
          if (output && output.buffer && output.buffer.length) {
            output(10);
          }
        }, read(stream, buffer2, offset, length, pos) {
          let bytesRead = 0;
          for (let i2 = 0; i2 < length; i2++) {
            var result;
            try {
              result = input();
            } catch (e) {
              throw new FS.ErrnoError(29);
            }
            if (result === void 0 && bytesRead === 0) {
              throw new FS.ErrnoError(6);
            }
            if (result === null || result === void 0)
              break;
            bytesRead++;
            buffer2[offset + i2] = result;
          }
          if (bytesRead) {
            stream.node.timestamp = Date.now();
          }
          return bytesRead;
        }, write(stream, buffer2, offset, length, pos) {
          for (var i2 = 0; i2 < length; i2++) {
            try {
              output(buffer2[offset + i2]);
            } catch (e) {
              throw new FS.ErrnoError(29);
            }
          }
          if (length) {
            stream.node.timestamp = Date.now();
          }
          return i2;
        } });
        return FS.mkdev(path, mode, dev);
      }, forceLoadFile(obj) {
        if (obj.isDevice || obj.isFolder || obj.link || obj.contents)
          return true;
        if (typeof XMLHttpRequest !== "undefined") {
          throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
        } else if (read_) {
          try {
            obj.contents = intArrayFromString(read_(obj.url), true);
            obj.usedBytes = obj.contents.length;
          } catch (e) {
            throw new FS.ErrnoError(29);
          }
        } else {
          throw new Error("Cannot load without read() or XMLHttpRequest.");
        }
      }, createLazyFile(parent, name, url, canRead, canWrite) {
        function LazyUint8Array() {
          this.lengthKnown = false;
          this.chunks = [];
        }
        LazyUint8Array.prototype.get = function LazyUint8Array_get(idx) {
          if (idx > this.length - 1 || idx < 0) {
            return void 0;
          }
          const chunkOffset = idx % this.chunkSize;
          const chunkNum = idx / this.chunkSize | 0;
          return this.getter(chunkNum)[chunkOffset];
        };
        LazyUint8Array.prototype.setDataGetter = function LazyUint8Array_setDataGetter(getter) {
          this.getter = getter;
        };
        LazyUint8Array.prototype.cacheLength = function LazyUint8Array_cacheLength() {
          const xhr = new XMLHttpRequest();
          xhr.open("HEAD", url, false);
          xhr.send(null);
          if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304))
            throw new Error(`Couldn't load ${url}. Status: ${xhr.status}`);
          let datalength = Number(xhr.getResponseHeader("Content-length"));
          let header;
          const hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";
          const usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";
          let chunkSize = 1024 * 1024;
          if (!hasByteServing)
            chunkSize = datalength;
          const doXHR = function(from, to) {
            if (from > to)
              throw new Error(`invalid range (${from}, ${to}) or no bytes requested!`);
            if (to > datalength - 1)
              throw new Error(`only ${datalength} bytes available! programmer error!`);
            const xhr2 = new XMLHttpRequest();
            xhr2.open("GET", url, false);
            if (datalength !== chunkSize)
              xhr2.setRequestHeader("Range", `bytes=${from}-${to}`);
            if (typeof Uint8Array !== "undefined")
              xhr2.responseType = "arraybuffer";
            if (xhr2.overrideMimeType) {
              xhr2.overrideMimeType("text/plain; charset=x-user-defined");
            }
            xhr2.send(null);
            if (!(xhr2.status >= 200 && xhr2.status < 300 || xhr2.status === 304))
              throw new Error(`Couldn't load ${url}. Status: ${xhr2.status}`);
            if (xhr2.response !== void 0) {
              return new Uint8Array(xhr2.response || []);
            }
            return intArrayFromString(xhr2.responseText || "", true);
          };
          const lazyArray = this;
          lazyArray.setDataGetter((chunkNum) => {
            const start = chunkNum * chunkSize;
            let end = (chunkNum + 1) * chunkSize - 1;
            end = Math.min(end, datalength - 1);
            if (typeof lazyArray.chunks[chunkNum] === "undefined") {
              lazyArray.chunks[chunkNum] = doXHR(start, end);
            }
            if (typeof lazyArray.chunks[chunkNum] === "undefined")
              throw new Error("doXHR failed!");
            return lazyArray.chunks[chunkNum];
          });
          if (usesGzip || !datalength) {
            chunkSize = datalength = 1;
            datalength = this.getter(0).length;
            chunkSize = datalength;
            out("LazyFiles on gzip forces download of the whole file when length is accessed");
          }
          this._length = datalength;
          this._chunkSize = chunkSize;
          this.lengthKnown = true;
        };
        if (typeof XMLHttpRequest !== "undefined") {
          if (!ENVIRONMENT_IS_WORKER)
            throw "Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";
          const lazyArray = new LazyUint8Array();
          Object.defineProperties(lazyArray, { length: { get() {
            if (!this.lengthKnown) {
              this.cacheLength();
            }
            return this._length;
          } }, chunkSize: { get() {
            if (!this.lengthKnown) {
              this.cacheLength();
            }
            return this._chunkSize;
          } } });
          var properties = { isDevice: false, contents: lazyArray };
        } else {
          var properties = { isDevice: false, url };
        }
        const node = FS.createFile(parent, name, properties, canRead, canWrite);
        if (properties.contents) {
          node.contents = properties.contents;
        } else if (properties.url) {
          node.contents = null;
          node.url = properties.url;
        }
        Object.defineProperties(node, { usedBytes: { get() {
          return this.contents.length;
        } } });
        const stream_ops = {};
        const keys = Object.keys(node.stream_ops);
        keys.forEach((key2) => {
          const fn = node.stream_ops[key2];
          stream_ops[key2] = function forceLoadLazyFile() {
            FS.forceLoadFile(node);
            return fn.apply(null, arguments);
          };
        });
        stream_ops.read = function stream_ops_read(stream, buffer2, offset, length, position) {
          FS.forceLoadFile(node);
          const { contents } = stream.node;
          if (position >= contents.length)
            return 0;
          const size = Math.min(contents.length - position, length);
          assert(size >= 0);
          if (contents.slice) {
            for (var i2 = 0; i2 < size; i2++) {
              buffer2[offset + i2] = contents[position + i2];
            }
          } else {
            for (var i2 = 0; i2 < size; i2++) {
              buffer2[offset + i2] = contents.get(position + i2);
            }
          }
          return size;
        };
        node.stream_ops = stream_ops;
        return node;
      }, createPreloadedFile(parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) {
        Browser.init();
        const fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
        const dep = getUniqueRunDependency(`cp ${fullname}`);
        function processData(byteArray) {
          function finish(byteArray2) {
            if (preFinish)
              preFinish();
            if (!dontCreateFile) {
              FS.createDataFile(parent, name, byteArray2, canRead, canWrite, canOwn);
            }
            if (onload)
              onload();
            removeRunDependency(dep);
          }
          let handled = false;
          Module.preloadPlugins.forEach((plugin) => {
            if (handled)
              return;
            if (plugin.canHandle(fullname)) {
              plugin.handle(byteArray, fullname, finish, () => {
                if (onerror)
                  onerror();
                removeRunDependency(dep);
              });
              handled = true;
            }
          });
          if (!handled)
            finish(byteArray);
        }
        addRunDependency(dep);
        if (typeof url === "string") {
          asyncLoad(url, (byteArray) => {
            processData(byteArray);
          }, onerror);
        } else {
          processData(url);
        }
      }, indexedDB() {
        return window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB;
      }, DB_NAME() {
        return `EM_FS_${window.location.pathname}`;
      }, DB_VERSION: 20, DB_STORE_NAME: "FILE_DATA", saveFilesToDB(paths, onload, onerror) {
        onload = onload || function() {
        };
        onerror = onerror || function() {
        };
        const indexedDB = FS.indexedDB();
        try {
          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
        } catch (e) {
          return onerror(e);
        }
        openRequest.onupgradeneeded = function openRequest_onupgradeneeded() {
          out("creating db");
          const db = openRequest.result;
          db.createObjectStore(FS.DB_STORE_NAME);
        };
        openRequest.onsuccess = function openRequest_onsuccess() {
          const db = openRequest.result;
          const transaction = db.transaction([FS.DB_STORE_NAME], "readwrite");
          const files = transaction.objectStore(FS.DB_STORE_NAME);
          let ok = 0;
          let fail = 0;
          const total = paths.length;
          function finish() {
            if (fail == 0)
              onload();
            else
              onerror();
          }
          paths.forEach((path) => {
            const putRequest = files.put(FS.analyzePath(path).object.contents, path);
            putRequest.onsuccess = function putRequest_onsuccess() {
              ok++;
              if (ok + fail == total)
                finish();
            };
            putRequest.onerror = function putRequest_onerror() {
              fail++;
              if (ok + fail == total)
                finish();
            };
          });
          transaction.onerror = onerror;
        };
        openRequest.onerror = onerror;
      }, loadFilesFromDB(paths, onload, onerror) {
        onload = onload || function() {
        };
        onerror = onerror || function() {
        };
        const indexedDB = FS.indexedDB();
        try {
          var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
        } catch (e) {
          return onerror(e);
        }
        openRequest.onupgradeneeded = onerror;
        openRequest.onsuccess = function openRequest_onsuccess() {
          const db = openRequest.result;
          try {
            var transaction = db.transaction([FS.DB_STORE_NAME], "readonly");
          } catch (e) {
            onerror(e);
            return;
          }
          const files = transaction.objectStore(FS.DB_STORE_NAME);
          let ok = 0;
          let fail = 0;
          const total = paths.length;
          function finish() {
            if (fail == 0)
              onload();
            else
              onerror();
          }
          paths.forEach((path) => {
            const getRequest = files.get(path);
            getRequest.onsuccess = function getRequest_onsuccess() {
              if (FS.analyzePath(path).exists) {
                FS.unlink(path);
              }
              FS.createDataFile(PATH.dirname(path), PATH.basename(path), getRequest.result, true, true, true);
              ok++;
              if (ok + fail == total)
                finish();
            };
            getRequest.onerror = function getRequest_onerror() {
              fail++;
              if (ok + fail == total)
                finish();
            };
          });
          transaction.onerror = onerror;
        };
        openRequest.onerror = onerror;
      }, absolutePath() {
        abort("FS.absolutePath has been removed; use PATH_FS.resolve instead");
      }, createFolder() {
        abort("FS.createFolder has been removed; use FS.mkdir instead");
      }, createLink() {
        abort("FS.createLink has been removed; use FS.symlink instead");
      }, joinPath() {
        abort("FS.joinPath has been removed; use PATH.join instead");
      }, mmapAlloc() {
        abort("FS.mmapAlloc has been replaced by the top level function mmapAlloc");
      }, standardizePath() {
        abort("FS.standardizePath has been removed; use PATH.normalize instead");
      } };
      var SYSCALLS = { mappings: {}, DEFAULT_POLLMASK: 5, umask: 511, calculateAt(dirfd, path, allowEmpty) {
        if (path[0] === "/") {
          return path;
        }
        let dir;
        if (dirfd === -100) {
          dir = FS.cwd();
        } else {
          const dirstream = FS.getStream(dirfd);
          if (!dirstream)
            throw new FS.ErrnoError(8);
          dir = dirstream.path;
        }
        if (path.length == 0) {
          if (!allowEmpty) {
            throw new FS.ErrnoError(44);
          }
          return dir;
        }
        return PATH.join2(dir, path);
      }, doStat(func, path, buf) {
        try {
          var stat = func(path);
        } catch (e) {
          if (e && e.node && PATH.normalize(path) !== PATH.normalize(FS.getPath(e.node))) {
            return -54;
          }
          throw e;
        }
        HEAP32[buf >> 2] = stat.dev;
        HEAP32[buf + 4 >> 2] = 0;
        HEAP32[buf + 8 >> 2] = stat.ino;
        HEAP32[buf + 12 >> 2] = stat.mode;
        HEAP32[buf + 16 >> 2] = stat.nlink;
        HEAP32[buf + 20 >> 2] = stat.uid;
        HEAP32[buf + 24 >> 2] = stat.gid;
        HEAP32[buf + 28 >> 2] = stat.rdev;
        HEAP32[buf + 32 >> 2] = 0;
        tempI64 = [stat.size >>> 0, (tempDouble = stat.size, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 40 >> 2] = tempI64[0], HEAP32[buf + 44 >> 2] = tempI64[1];
        HEAP32[buf + 48 >> 2] = 4096;
        HEAP32[buf + 52 >> 2] = stat.blocks;
        HEAP32[buf + 56 >> 2] = stat.atime.getTime() / 1e3 | 0;
        HEAP32[buf + 60 >> 2] = 0;
        HEAP32[buf + 64 >> 2] = stat.mtime.getTime() / 1e3 | 0;
        HEAP32[buf + 68 >> 2] = 0;
        HEAP32[buf + 72 >> 2] = stat.ctime.getTime() / 1e3 | 0;
        HEAP32[buf + 76 >> 2] = 0;
        tempI64 = [stat.ino >>> 0, (tempDouble = stat.ino, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[buf + 80 >> 2] = tempI64[0], HEAP32[buf + 84 >> 2] = tempI64[1];
        return 0;
      }, doMsync(addr, stream, len, flags, offset) {
        const buffer2 = HEAPU8.slice(addr, addr + len);
        FS.msync(stream, buffer2, offset, len, flags);
      }, doMkdir(path, mode) {
        path = PATH.normalize(path);
        if (path[path.length - 1] === "/")
          path = path.substr(0, path.length - 1);
        FS.mkdir(path, mode, 0);
        return 0;
      }, doMknod(path, mode, dev) {
        switch (mode & 61440) {
          case 32768:
          case 8192:
          case 24576:
          case 4096:
          case 49152:
            break;
          default:
            return -28;
        }
        FS.mknod(path, mode, dev);
        return 0;
      }, doReadlink(path, buf, bufsize) {
        if (bufsize <= 0)
          return -28;
        const ret = FS.readlink(path);
        const len = Math.min(bufsize, lengthBytesUTF8(ret));
        const endChar = HEAP8[buf + len];
        stringToUTF8(ret, buf, bufsize + 1);
        HEAP8[buf + len] = endChar;
        return len;
      }, doAccess(path, amode) {
        if (amode & ~7) {
          return -28;
        }
        let node;
        const lookup = FS.lookupPath(path, { follow: true });
        node = lookup.node;
        if (!node) {
          return -44;
        }
        let perms = "";
        if (amode & 4)
          perms += "r";
        if (amode & 2)
          perms += "w";
        if (amode & 1)
          perms += "x";
        if (perms && FS.nodePermissions(node, perms)) {
          return -2;
        }
        return 0;
      }, doDup(path, flags, suggestFD) {
        const suggest = FS.getStream(suggestFD);
        if (suggest)
          FS.close(suggest);
        return FS.open(path, flags, 0, suggestFD, suggestFD).fd;
      }, doReadv(stream, iov, iovcnt, offset) {
        let ret = 0;
        for (let i2 = 0; i2 < iovcnt; i2++) {
          const ptr = HEAP32[iov + i2 * 8 >> 2];
          const len = HEAP32[iov + (i2 * 8 + 4) >> 2];
          const curr = FS.read(stream, HEAP8, ptr, len, offset);
          if (curr < 0)
            return -1;
          ret += curr;
          if (curr < len)
            break;
        }
        return ret;
      }, doWritev(stream, iov, iovcnt, offset) {
        let ret = 0;
        for (let i2 = 0; i2 < iovcnt; i2++) {
          const ptr = HEAP32[iov + i2 * 8 >> 2];
          const len = HEAP32[iov + (i2 * 8 + 4) >> 2];
          const curr = FS.write(stream, HEAP8, ptr, len, offset);
          if (curr < 0)
            return -1;
          ret += curr;
        }
        return ret;
      }, varargs: void 0, get() {
        assert(SYSCALLS.varargs != void 0);
        SYSCALLS.varargs += 4;
        const ret = HEAP32[SYSCALLS.varargs - 4 >> 2];
        return ret;
      }, getStr(ptr) {
        const ret = UTF8ToString(ptr);
        return ret;
      }, getStreamFromFD(fd) {
        const stream = FS.getStream(fd);
        if (!stream)
          throw new FS.ErrnoError(8);
        return stream;
      }, get64(low, high) {
        if (low >= 0)
          assert(high === 0);
        else
          assert(high === -1);
        return low;
      } };
      function ___sys_fcntl64(fd, cmd, varargs) {
        SYSCALLS.varargs = varargs;
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          switch (cmd) {
            case 0: {
              var arg = SYSCALLS.get();
              if (arg < 0) {
                return -28;
              }
              let newStream;
              newStream = FS.open(stream.path, stream.flags, 0, arg);
              return newStream.fd;
            }
            case 1:
            case 2:
              return 0;
            case 3:
              return stream.flags;
            case 4: {
              var arg = SYSCALLS.get();
              stream.flags |= arg;
              return 0;
            }
            case 12: {
              var arg = SYSCALLS.get();
              const offset = 0;
              HEAP16[arg + offset >> 1] = 2;
              return 0;
            }
            case 13:
            case 14:
              return 0;
            case 16:
            case 8:
              return -28;
            case 9:
              setErrNo(28);
              return -1;
            default: {
              return -28;
            }
          }
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return -e.errno;
        }
      }
      function ___sys_ioctl(fd, op, varargs) {
        SYSCALLS.varargs = varargs;
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          switch (op) {
            case 21509:
            case 21505: {
              if (!stream.tty)
                return -59;
              return 0;
            }
            case 21510:
            case 21511:
            case 21512:
            case 21506:
            case 21507:
            case 21508: {
              if (!stream.tty)
                return -59;
              return 0;
            }
            case 21519: {
              if (!stream.tty)
                return -59;
              var argp = SYSCALLS.get();
              HEAP32[argp >> 2] = 0;
              return 0;
            }
            case 21520: {
              if (!stream.tty)
                return -59;
              return -28;
            }
            case 21531: {
              var argp = SYSCALLS.get();
              return FS.ioctl(stream, op, argp);
            }
            case 21523: {
              if (!stream.tty)
                return -59;
              return 0;
            }
            case 21524: {
              if (!stream.tty)
                return -59;
              return 0;
            }
            default:
              abort(`bad ioctl syscall ${op}`);
          }
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return -e.errno;
        }
      }
      function syscallMmap2(addr, len, prot, flags, fd, off) {
        off <<= 12;
        let ptr;
        let allocated = false;
        if ((flags & 16) !== 0 && addr % 65536 !== 0) {
          return -28;
        }
        if ((flags & 32) !== 0) {
          ptr = mmapAlloc(len);
          if (!ptr)
            return -48;
          allocated = true;
        } else {
          const info = FS.getStream(fd);
          if (!info)
            return -8;
          const res = FS.mmap(info, addr, len, off, prot, flags);
          ptr = res.ptr;
          allocated = res.allocated;
        }
        SYSCALLS.mappings[ptr] = { malloc: ptr, len, allocated, fd, prot, flags, offset: off };
        return ptr;
      }
      function ___sys_mmap2(addr, len, prot, flags, fd, off) {
        try {
          return syscallMmap2(addr, len, prot, flags, fd, off);
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return -e.errno;
        }
      }
      function ___sys_open(path, flags, varargs) {
        SYSCALLS.varargs = varargs;
        try {
          const pathname = SYSCALLS.getStr(path);
          const mode = varargs ? SYSCALLS.get() : 0;
          const stream = FS.open(pathname, flags, mode);
          return stream.fd;
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return -e.errno;
        }
      }
      function ___sys_stat64(path, buf) {
        try {
          path = SYSCALLS.getStr(path);
          return SYSCALLS.doStat(FS.stat, path, buf);
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return -e.errno;
        }
      }
      const char_0 = 48;
      const char_9 = 57;
      function makeLegalFunctionName(name) {
        if (void 0 === name) {
          return "_unknown";
        }
        name = name.replace(/[^a-zA-Z0-9_]/g, "$");
        const f = name.charCodeAt(0);
        if (f >= char_0 && f <= char_9) {
          return `_${name}`;
        }
        return name;
      }
      function createNamedFunction(name, body) {
        name = makeLegalFunctionName(name);
        return new Function("body", `return function ${name}() {
    "use strict";    return body.apply(this, arguments);
};
`)(body);
      }
      const emval_free_list = [];
      const emval_handle_array = [{}, { value: void 0 }, { value: null }, { value: true }, { value: false }];
      function count_emval_handles() {
        let count = 0;
        for (let i2 = 5; i2 < emval_handle_array.length; ++i2) {
          if (emval_handle_array[i2] !== void 0) {
            ++count;
          }
        }
        return count;
      }
      function get_first_emval() {
        for (let i2 = 5; i2 < emval_handle_array.length; ++i2) {
          if (emval_handle_array[i2] !== void 0) {
            return emval_handle_array[i2];
          }
        }
        return null;
      }
      function init_emval() {
        Module.count_emval_handles = count_emval_handles;
        Module.get_first_emval = get_first_emval;
      }
      function __emval_register(value) {
        switch (value) {
          case void 0: {
            return 1;
          }
          case null: {
            return 2;
          }
          case true: {
            return 3;
          }
          case false: {
            return 4;
          }
          default: {
            const handle = emval_free_list.length ? emval_free_list.pop() : emval_handle_array.length;
            emval_handle_array[handle] = { refcount: 1, value };
            return handle;
          }
        }
      }
      function extendError(baseErrorType, errorName) {
        const errorClass = createNamedFunction(errorName, function(message) {
          this.name = errorName;
          this.message = message;
          const { stack } = new Error(message);
          if (stack !== void 0) {
            this.stack = `${this.toString()}
${stack.replace(/^Error(:[^\n]*)?\n/, "")}`;
          }
        });
        errorClass.prototype = Object.create(baseErrorType.prototype);
        errorClass.prototype.constructor = errorClass;
        errorClass.prototype.toString = function() {
          if (this.message === void 0) {
            return this.name;
          }
          return `${this.name}: ${this.message}`;
        };
        return errorClass;
      }
      let PureVirtualError = void 0;
      function embind_init_charCodes() {
        const codes = new Array(256);
        for (let i2 = 0; i2 < 256; ++i2) {
          codes[i2] = String.fromCharCode(i2);
        }
        embind_charCodes = codes;
      }
      var embind_charCodes = void 0;
      function readLatin1String(ptr) {
        let ret = "";
        let c = ptr;
        while (HEAPU8[c]) {
          ret += embind_charCodes[HEAPU8[c++]];
        }
        return ret;
      }
      function getInheritedInstanceCount() {
        return Object.keys(registeredInstances).length;
      }
      function getLiveInheritedInstances() {
        const rv = [];
        for (const k in registeredInstances) {
          if (registeredInstances.hasOwnProperty(k)) {
            rv.push(registeredInstances[k]);
          }
        }
        return rv;
      }
      const deletionQueue = [];
      function flushPendingDeletes() {
        while (deletionQueue.length) {
          const obj = deletionQueue.pop();
          obj.$$.deleteScheduled = false;
          obj.delete();
        }
      }
      let delayFunction = void 0;
      function setDelayFunction(fn) {
        delayFunction = fn;
        if (deletionQueue.length && delayFunction) {
          delayFunction(flushPendingDeletes);
        }
      }
      function init_embind() {
        Module.getInheritedInstanceCount = getInheritedInstanceCount;
        Module.getLiveInheritedInstances = getLiveInheritedInstances;
        Module.flushPendingDeletes = flushPendingDeletes;
        Module.setDelayFunction = setDelayFunction;
      }
      var registeredInstances = {};
      let BindingError = void 0;
      function throwBindingError(message) {
        throw new BindingError(message);
      }
      function getBasestPointer(class_, ptr) {
        if (ptr === void 0) {
          throwBindingError("ptr should not be undefined");
        }
        while (class_.baseClass) {
          ptr = class_.upcast(ptr);
          class_ = class_.baseClass;
        }
        return ptr;
      }
      function registerInheritedInstance(class_, ptr, instance) {
        ptr = getBasestPointer(class_, ptr);
        if (registeredInstances.hasOwnProperty(ptr)) {
          throwBindingError(`Tried to register registered instance: ${ptr}`);
        } else {
          registeredInstances[ptr] = instance;
        }
      }
      function requireHandle(handle) {
        if (!handle) {
          throwBindingError(`Cannot use deleted val. handle = ${handle}`);
        }
        return emval_handle_array[handle].value;
      }
      const registeredTypes = {};
      function getTypeName(type) {
        const ptr = ___getTypeName(type);
        const rv = readLatin1String(ptr);
        _free(ptr);
        return rv;
      }
      function requireRegisteredType(rawType, humanName) {
        const impl = registeredTypes[rawType];
        if (void 0 === impl) {
          throwBindingError(`${humanName} has unknown type ${getTypeName(rawType)}`);
        }
        return impl;
      }
      function unregisterInheritedInstance(class_, ptr) {
        ptr = getBasestPointer(class_, ptr);
        if (registeredInstances.hasOwnProperty(ptr)) {
          delete registeredInstances[ptr];
        } else {
          throwBindingError(`Tried to unregister unregistered instance: ${ptr}`);
        }
      }
      function detachFinalizer(handle) {
      }
      let finalizationGroup = false;
      function runDestructor($$) {
        if ($$.smartPtr) {
          $$.smartPtrType.rawDestructor($$.smartPtr);
        } else {
          $$.ptrType.registeredClass.rawDestructor($$.ptr);
        }
      }
      function releaseClassHandle($$) {
        $$.count.value -= 1;
        const toDelete = 0 === $$.count.value;
        if (toDelete) {
          runDestructor($$);
        }
      }
      function attachFinalizer(handle) {
        if ("undefined" === typeof FinalizationGroup) {
          attachFinalizer = function(handle2) {
            return handle2;
          };
          return handle;
        }
        finalizationGroup = new FinalizationGroup((iter) => {
          for (let result = iter.next(); !result.done; result = iter.next()) {
            const $$ = result.value;
            if (!$$.ptr) {
              console.warn(`object already deleted: ${$$.ptr}`);
            } else {
              releaseClassHandle($$);
            }
          }
        });
        attachFinalizer = function(handle2) {
          finalizationGroup.register(handle2, handle2.$$, handle2.$$);
          return handle2;
        };
        detachFinalizer = function(handle2) {
          finalizationGroup.unregister(handle2.$$);
        };
        return attachFinalizer(handle);
      }
      function __embind_create_inheriting_constructor(constructorName, wrapperType, properties) {
        constructorName = readLatin1String(constructorName);
        wrapperType = requireRegisteredType(wrapperType, "wrapper");
        properties = requireHandle(properties);
        const arraySlice = [].slice;
        const { registeredClass } = wrapperType;
        const wrapperPrototype = registeredClass.instancePrototype;
        const { baseClass } = registeredClass;
        const baseClassPrototype = baseClass.instancePrototype;
        const baseConstructor = registeredClass.baseClass.constructor;
        const ctor = createNamedFunction(constructorName, function() {
          registeredClass.baseClass.pureVirtualFunctions.forEach((name) => {
            if (this[name] === baseClassPrototype[name]) {
              throw new PureVirtualError(`Pure virtual function ${name} must be implemented in JavaScript`);
            }
          });
          Object.defineProperty(this, "__parent", { value: wrapperPrototype });
          this.__construct.apply(this, arraySlice.call(arguments));
        });
        wrapperPrototype.__construct = function __construct() {
          if (this === wrapperPrototype) {
            throwBindingError("Pass correct 'this' to __construct");
          }
          const inner = baseConstructor.implement.apply(void 0, [this].concat(arraySlice.call(arguments)));
          detachFinalizer(inner);
          const { $$ } = inner;
          inner.notifyOnDestruction();
          $$.preservePointerOnDelete = true;
          Object.defineProperties(this, { $$: { value: $$ } });
          attachFinalizer(this);
          registerInheritedInstance(registeredClass, $$.ptr, this);
        };
        wrapperPrototype.__destruct = function __destruct() {
          if (this === wrapperPrototype) {
            throwBindingError("Pass correct 'this' to __destruct");
          }
          detachFinalizer(this);
          unregisterInheritedInstance(registeredClass, this.$$.ptr);
        };
        ctor.prototype = Object.create(wrapperPrototype);
        for (const p in properties) {
          ctor.prototype[p] = properties[p];
        }
        return __emval_register(ctor);
      }
      const structRegistrations = {};
      function runDestructors(destructors) {
        while (destructors.length) {
          const ptr = destructors.pop();
          const del = destructors.pop();
          del(ptr);
        }
      }
      function simpleReadValueFromPointer(pointer) {
        return this.fromWireType(HEAPU32[pointer >> 2]);
      }
      const awaitingDependencies = {};
      const typeDependencies = {};
      let InternalError = void 0;
      function throwInternalError(message) {
        throw new InternalError(message);
      }
      function whenDependentTypesAreResolved(myTypes, dependentTypes, getTypeConverters) {
        myTypes.forEach((type) => {
          typeDependencies[type] = dependentTypes;
        });
        function onComplete(typeConverters2) {
          const myTypeConverters = getTypeConverters(typeConverters2);
          if (myTypeConverters.length !== myTypes.length) {
            throwInternalError("Mismatched type converter count");
          }
          for (let i2 = 0; i2 < myTypes.length; ++i2) {
            registerType(myTypes[i2], myTypeConverters[i2]);
          }
        }
        const typeConverters = new Array(dependentTypes.length);
        const unregisteredTypes = [];
        let registered = 0;
        dependentTypes.forEach((dt, i2) => {
          if (registeredTypes.hasOwnProperty(dt)) {
            typeConverters[i2] = registeredTypes[dt];
          } else {
            unregisteredTypes.push(dt);
            if (!awaitingDependencies.hasOwnProperty(dt)) {
              awaitingDependencies[dt] = [];
            }
            awaitingDependencies[dt].push(() => {
              typeConverters[i2] = registeredTypes[dt];
              ++registered;
              if (registered === unregisteredTypes.length) {
                onComplete(typeConverters);
              }
            });
          }
        });
        if (0 === unregisteredTypes.length) {
          onComplete(typeConverters);
        }
      }
      function __embind_finalize_value_object(structType) {
        const reg = structRegistrations[structType];
        delete structRegistrations[structType];
        const { rawConstructor } = reg;
        const { rawDestructor } = reg;
        const fieldRecords = reg.fields;
        const fieldTypes = fieldRecords.map((field) => field.getterReturnType).concat(fieldRecords.map((field) => field.setterArgumentType));
        whenDependentTypesAreResolved([structType], fieldTypes, (fieldTypes2) => {
          const fields = {};
          fieldRecords.forEach((field, i2) => {
            const { fieldName } = field;
            const getterReturnType = fieldTypes2[i2];
            const { getter } = field;
            const { getterContext } = field;
            const setterArgumentType = fieldTypes2[i2 + fieldRecords.length];
            const { setter } = field;
            const { setterContext } = field;
            fields[fieldName] = { read(ptr) {
              return getterReturnType.fromWireType(getter(getterContext, ptr));
            }, write(ptr, o) {
              const destructors = [];
              setter(setterContext, ptr, setterArgumentType.toWireType(destructors, o));
              runDestructors(destructors);
            } };
          });
          return [{ name: reg.name, fromWireType(ptr) {
            const rv = {};
            for (const i2 in fields) {
              rv[i2] = fields[i2].read(ptr);
            }
            rawDestructor(ptr);
            return rv;
          }, toWireType(destructors, o) {
            for (var fieldName in fields) {
              if (!(fieldName in o)) {
                throw new TypeError(`Missing field:  "${fieldName}"`);
              }
            }
            const ptr = rawConstructor();
            for (fieldName in fields) {
              fields[fieldName].write(ptr, o[fieldName]);
            }
            if (destructors !== null) {
              destructors.push(rawDestructor, ptr);
            }
            return ptr;
          }, argPackAdvance: 8, readValueFromPointer: simpleReadValueFromPointer, destructorFunction: rawDestructor }];
        });
      }
      function __embind_register_bigint(primitiveType, name, size, minRange, maxRange) {
      }
      function getShiftFromSize(size) {
        switch (size) {
          case 1:
            return 0;
          case 2:
            return 1;
          case 4:
            return 2;
          case 8:
            return 3;
          default:
            throw new TypeError(`Unknown type size: ${size}`);
        }
      }
      function registerType(rawType, registeredInstance, options) {
        options = options || {};
        if (!("argPackAdvance" in registeredInstance)) {
          throw new TypeError("registerType registeredInstance requires argPackAdvance");
        }
        const { name } = registeredInstance;
        if (!rawType) {
          throwBindingError(`type "${name}" must have a positive integer typeid pointer`);
        }
        if (registeredTypes.hasOwnProperty(rawType)) {
          if (options.ignoreDuplicateRegistrations) {
            return;
          }
          throwBindingError(`Cannot register type '${name}' twice`);
        }
        registeredTypes[rawType] = registeredInstance;
        delete typeDependencies[rawType];
        if (awaitingDependencies.hasOwnProperty(rawType)) {
          const callbacks = awaitingDependencies[rawType];
          delete awaitingDependencies[rawType];
          callbacks.forEach((cb) => {
            cb();
          });
        }
      }
      function __embind_register_bool(rawType, name, size, trueValue, falseValue) {
        const shift = getShiftFromSize(size);
        name = readLatin1String(name);
        registerType(rawType, { name, fromWireType(wt) {
          return !!wt;
        }, toWireType(destructors, o) {
          return o ? trueValue : falseValue;
        }, argPackAdvance: 8, readValueFromPointer(pointer) {
          let heap;
          if (size === 1) {
            heap = HEAP8;
          } else if (size === 2) {
            heap = HEAP16;
          } else if (size === 4) {
            heap = HEAP32;
          } else {
            throw new TypeError(`Unknown boolean type size: ${name}`);
          }
          return this.fromWireType(heap[pointer >> shift]);
        }, destructorFunction: null });
      }
      function ClassHandle_isAliasOf(other) {
        if (!(this instanceof ClassHandle)) {
          return false;
        }
        if (!(other instanceof ClassHandle)) {
          return false;
        }
        let leftClass = this.$$.ptrType.registeredClass;
        let left = this.$$.ptr;
        let rightClass = other.$$.ptrType.registeredClass;
        let right = other.$$.ptr;
        while (leftClass.baseClass) {
          left = leftClass.upcast(left);
          leftClass = leftClass.baseClass;
        }
        while (rightClass.baseClass) {
          right = rightClass.upcast(right);
          rightClass = rightClass.baseClass;
        }
        return leftClass === rightClass && left === right;
      }
      function shallowCopyInternalPointer(o) {
        return { count: o.count, deleteScheduled: o.deleteScheduled, preservePointerOnDelete: o.preservePointerOnDelete, ptr: o.ptr, ptrType: o.ptrType, smartPtr: o.smartPtr, smartPtrType: o.smartPtrType };
      }
      function throwInstanceAlreadyDeleted(obj) {
        function getInstanceTypeName(handle) {
          return handle.$$.ptrType.registeredClass.name;
        }
        throwBindingError(`${getInstanceTypeName(obj)} instance already deleted`);
      }
      function ClassHandle_clone() {
        if (!this.$$.ptr) {
          throwInstanceAlreadyDeleted(this);
        }
        if (this.$$.preservePointerOnDelete) {
          this.$$.count.value += 1;
          return this;
        }
        const clone = attachFinalizer(Object.create(Object.getPrototypeOf(this), { $$: { value: shallowCopyInternalPointer(this.$$) } }));
        clone.$$.count.value += 1;
        clone.$$.deleteScheduled = false;
        return clone;
      }
      function ClassHandle_delete() {
        if (!this.$$.ptr) {
          throwInstanceAlreadyDeleted(this);
        }
        if (this.$$.deleteScheduled && !this.$$.preservePointerOnDelete) {
          throwBindingError("Object already scheduled for deletion");
        }
        detachFinalizer(this);
        releaseClassHandle(this.$$);
        if (!this.$$.preservePointerOnDelete) {
          this.$$.smartPtr = void 0;
          this.$$.ptr = void 0;
        }
      }
      function ClassHandle_isDeleted() {
        return !this.$$.ptr;
      }
      function ClassHandle_deleteLater() {
        if (!this.$$.ptr) {
          throwInstanceAlreadyDeleted(this);
        }
        if (this.$$.deleteScheduled && !this.$$.preservePointerOnDelete) {
          throwBindingError("Object already scheduled for deletion");
        }
        deletionQueue.push(this);
        if (deletionQueue.length === 1 && delayFunction) {
          delayFunction(flushPendingDeletes);
        }
        this.$$.deleteScheduled = true;
        return this;
      }
      function init_ClassHandle() {
        ClassHandle.prototype.isAliasOf = ClassHandle_isAliasOf;
        ClassHandle.prototype.clone = ClassHandle_clone;
        ClassHandle.prototype.delete = ClassHandle_delete;
        ClassHandle.prototype.isDeleted = ClassHandle_isDeleted;
        ClassHandle.prototype.deleteLater = ClassHandle_deleteLater;
      }
      function ClassHandle() {
      }
      const registeredPointers = {};
      function ensureOverloadTable(proto, methodName, humanName) {
        if (void 0 === proto[methodName].overloadTable) {
          const prevFunc = proto[methodName];
          proto[methodName] = function() {
            if (!proto[methodName].overloadTable.hasOwnProperty(arguments.length)) {
              throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`);
            }
            return proto[methodName].overloadTable[arguments.length].apply(this, arguments);
          };
          proto[methodName].overloadTable = [];
          proto[methodName].overloadTable[prevFunc.argCount] = prevFunc;
        }
      }
      function exposePublicSymbol(name, value, numArguments) {
        if (Module.hasOwnProperty(name)) {
          if (void 0 === numArguments || void 0 !== Module[name].overloadTable && void 0 !== Module[name].overloadTable[numArguments]) {
            throwBindingError(`Cannot register public name '${name}' twice`);
          }
          ensureOverloadTable(Module, name, name);
          if (Module.hasOwnProperty(numArguments)) {
            throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`);
          }
          Module[name].overloadTable[numArguments] = value;
        } else {
          Module[name] = value;
          if (void 0 !== numArguments) {
            Module[name].numArguments = numArguments;
          }
        }
      }
      function RegisteredClass(name, constructor, instancePrototype, rawDestructor, baseClass, getActualType, upcast, downcast) {
        this.name = name;
        this.constructor = constructor;
        this.instancePrototype = instancePrototype;
        this.rawDestructor = rawDestructor;
        this.baseClass = baseClass;
        this.getActualType = getActualType;
        this.upcast = upcast;
        this.downcast = downcast;
        this.pureVirtualFunctions = [];
      }
      function upcastPointer(ptr, ptrClass, desiredClass) {
        while (ptrClass !== desiredClass) {
          if (!ptrClass.upcast) {
            throwBindingError(`Expected null or instance of ${desiredClass.name}, got an instance of ${ptrClass.name}`);
          }
          ptr = ptrClass.upcast(ptr);
          ptrClass = ptrClass.baseClass;
        }
        return ptr;
      }
      function constNoSmartPtrRawPointerToWireType(destructors, handle) {
        if (handle === null) {
          if (this.isReference) {
            throwBindingError(`null is not a valid ${this.name}`);
          }
          return 0;
        }
        if (!handle.$$) {
          throwBindingError(`Cannot pass "${_embind_repr(handle)}" as a ${this.name}`);
        }
        if (!handle.$$.ptr) {
          throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);
        }
        const handleClass = handle.$$.ptrType.registeredClass;
        const ptr = upcastPointer(handle.$$.ptr, handleClass, this.registeredClass);
        return ptr;
      }
      function genericPointerToWireType(destructors, handle) {
        let ptr;
        if (handle === null) {
          if (this.isReference) {
            throwBindingError(`null is not a valid ${this.name}`);
          }
          if (this.isSmartPointer) {
            ptr = this.rawConstructor();
            if (destructors !== null) {
              destructors.push(this.rawDestructor, ptr);
            }
            return ptr;
          }
          return 0;
        }
        if (!handle.$$) {
          throwBindingError(`Cannot pass "${_embind_repr(handle)}" as a ${this.name}`);
        }
        if (!handle.$$.ptr) {
          throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);
        }
        if (!this.isConst && handle.$$.ptrType.isConst) {
          throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType ? handle.$$.smartPtrType.name : handle.$$.ptrType.name} to parameter type ${this.name}`);
        }
        const handleClass = handle.$$.ptrType.registeredClass;
        ptr = upcastPointer(handle.$$.ptr, handleClass, this.registeredClass);
        if (this.isSmartPointer) {
          if (void 0 === handle.$$.smartPtr) {
            throwBindingError("Passing raw pointer to smart pointer is illegal");
          }
          switch (this.sharingPolicy) {
            case 0:
              if (handle.$$.smartPtrType === this) {
                ptr = handle.$$.smartPtr;
              } else {
                throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType ? handle.$$.smartPtrType.name : handle.$$.ptrType.name} to parameter type ${this.name}`);
              }
              break;
            case 1:
              ptr = handle.$$.smartPtr;
              break;
            case 2:
              if (handle.$$.smartPtrType === this) {
                ptr = handle.$$.smartPtr;
              } else {
                const clonedHandle = handle.clone();
                ptr = this.rawShare(ptr, __emval_register(() => {
                  clonedHandle.delete();
                }));
                if (destructors !== null) {
                  destructors.push(this.rawDestructor, ptr);
                }
              }
              break;
            default:
              throwBindingError("Unsupporting sharing policy");
          }
        }
        return ptr;
      }
      function nonConstNoSmartPtrRawPointerToWireType(destructors, handle) {
        if (handle === null) {
          if (this.isReference) {
            throwBindingError(`null is not a valid ${this.name}`);
          }
          return 0;
        }
        if (!handle.$$) {
          throwBindingError(`Cannot pass "${_embind_repr(handle)}" as a ${this.name}`);
        }
        if (!handle.$$.ptr) {
          throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);
        }
        if (handle.$$.ptrType.isConst) {
          throwBindingError(`Cannot convert argument of type ${handle.$$.ptrType.name} to parameter type ${this.name}`);
        }
        const handleClass = handle.$$.ptrType.registeredClass;
        const ptr = upcastPointer(handle.$$.ptr, handleClass, this.registeredClass);
        return ptr;
      }
      function RegisteredPointer_getPointee(ptr) {
        if (this.rawGetPointee) {
          ptr = this.rawGetPointee(ptr);
        }
        return ptr;
      }
      function RegisteredPointer_destructor(ptr) {
        if (this.rawDestructor) {
          this.rawDestructor(ptr);
        }
      }
      function RegisteredPointer_deleteObject(handle) {
        if (handle !== null) {
          handle.delete();
        }
      }
      function downcastPointer(ptr, ptrClass, desiredClass) {
        if (ptrClass === desiredClass) {
          return ptr;
        }
        if (void 0 === desiredClass.baseClass) {
          return null;
        }
        const rv = downcastPointer(ptr, ptrClass, desiredClass.baseClass);
        if (rv === null) {
          return null;
        }
        return desiredClass.downcast(rv);
      }
      function getInheritedInstance(class_, ptr) {
        ptr = getBasestPointer(class_, ptr);
        return registeredInstances[ptr];
      }
      function makeClassHandle(prototype, record) {
        if (!record.ptrType || !record.ptr) {
          throwInternalError("makeClassHandle requires ptr and ptrType");
        }
        const hasSmartPtrType = !!record.smartPtrType;
        const hasSmartPtr = !!record.smartPtr;
        if (hasSmartPtrType !== hasSmartPtr) {
          throwInternalError("Both smartPtrType and smartPtr must be specified");
        }
        record.count = { value: 1 };
        return attachFinalizer(Object.create(prototype, { $$: { value: record } }));
      }
      function RegisteredPointer_fromWireType(ptr) {
        const rawPointer = this.getPointee(ptr);
        if (!rawPointer) {
          this.destructor(ptr);
          return null;
        }
        const registeredInstance = getInheritedInstance(this.registeredClass, rawPointer);
        if (void 0 !== registeredInstance) {
          if (0 === registeredInstance.$$.count.value) {
            registeredInstance.$$.ptr = rawPointer;
            registeredInstance.$$.smartPtr = ptr;
            return registeredInstance.clone();
          }
          const rv = registeredInstance.clone();
          this.destructor(ptr);
          return rv;
        }
        function makeDefaultHandle() {
          if (this.isSmartPointer) {
            return makeClassHandle(this.registeredClass.instancePrototype, { ptrType: this.pointeeType, ptr: rawPointer, smartPtrType: this, smartPtr: ptr });
          }
          return makeClassHandle(this.registeredClass.instancePrototype, { ptrType: this, ptr });
        }
        const actualType = this.registeredClass.getActualType(rawPointer);
        const registeredPointerRecord = registeredPointers[actualType];
        if (!registeredPointerRecord) {
          return makeDefaultHandle.call(this);
        }
        let toType;
        if (this.isConst) {
          toType = registeredPointerRecord.constPointerType;
        } else {
          toType = registeredPointerRecord.pointerType;
        }
        const dp = downcastPointer(rawPointer, this.registeredClass, toType.registeredClass);
        if (dp === null) {
          return makeDefaultHandle.call(this);
        }
        if (this.isSmartPointer) {
          return makeClassHandle(toType.registeredClass.instancePrototype, { ptrType: toType, ptr: dp, smartPtrType: this, smartPtr: ptr });
        }
        return makeClassHandle(toType.registeredClass.instancePrototype, { ptrType: toType, ptr: dp });
      }
      function init_RegisteredPointer() {
        RegisteredPointer.prototype.getPointee = RegisteredPointer_getPointee;
        RegisteredPointer.prototype.destructor = RegisteredPointer_destructor;
        RegisteredPointer.prototype.argPackAdvance = 8;
        RegisteredPointer.prototype.readValueFromPointer = simpleReadValueFromPointer;
        RegisteredPointer.prototype.deleteObject = RegisteredPointer_deleteObject;
        RegisteredPointer.prototype.fromWireType = RegisteredPointer_fromWireType;
      }
      function RegisteredPointer(name, registeredClass, isReference, isConst, isSmartPointer, pointeeType, sharingPolicy, rawGetPointee, rawConstructor, rawShare, rawDestructor) {
        this.name = name;
        this.registeredClass = registeredClass;
        this.isReference = isReference;
        this.isConst = isConst;
        this.isSmartPointer = isSmartPointer;
        this.pointeeType = pointeeType;
        this.sharingPolicy = sharingPolicy;
        this.rawGetPointee = rawGetPointee;
        this.rawConstructor = rawConstructor;
        this.rawShare = rawShare;
        this.rawDestructor = rawDestructor;
        if (!isSmartPointer && registeredClass.baseClass === void 0) {
          if (isConst) {
            this.toWireType = constNoSmartPtrRawPointerToWireType;
            this.destructorFunction = null;
          } else {
            this.toWireType = nonConstNoSmartPtrRawPointerToWireType;
            this.destructorFunction = null;
          }
        } else {
          this.toWireType = genericPointerToWireType;
        }
      }
      function replacePublicSymbol(name, value, numArguments) {
        if (!Module.hasOwnProperty(name)) {
          throwInternalError("Replacing nonexistant public symbol");
        }
        if (void 0 !== Module[name].overloadTable && void 0 !== numArguments) {
          Module[name].overloadTable[numArguments] = value;
        } else {
          Module[name] = value;
          Module[name].argCount = numArguments;
        }
      }
      function dynCallLegacy(sig, ptr, args) {
        assert(`dynCall_${sig}` in Module, `bad function pointer type - no table for sig '${sig}'`);
        if (args && args.length) {
          assert(args.length === sig.substring(1).replace(/j/g, "--").length);
        } else {
          assert(sig.length == 1);
        }
        const f = Module[`dynCall_${sig}`];
        return args && args.length ? f.apply(null, [ptr].concat(args)) : f.call(null, ptr);
      }
      function dynCall(sig, ptr, args) {
        if (sig.includes("j")) {
          return dynCallLegacy(sig, ptr, args);
        }
        assert(wasmTable.get(ptr), `missing table entry in dynCall: ${ptr}`);
        return wasmTable.get(ptr).apply(null, args);
      }
      function getDynCaller(sig, ptr) {
        assert(sig.includes("j"), "getDynCaller should only be called with i64 sigs");
        const argCache = [];
        return function() {
          argCache.length = arguments.length;
          for (let i2 = 0; i2 < arguments.length; i2++) {
            argCache[i2] = arguments[i2];
          }
          return dynCall(sig, ptr, argCache);
        };
      }
      function embind__requireFunction(signature, rawFunction) {
        signature = readLatin1String(signature);
        function makeDynCaller() {
          if (signature.includes("j")) {
            return getDynCaller(signature, rawFunction);
          }
          return wasmTable.get(rawFunction);
        }
        const fp = makeDynCaller();
        if (typeof fp !== "function") {
          throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`);
        }
        return fp;
      }
      let UnboundTypeError = void 0;
      function throwUnboundTypeError(message, types) {
        const unboundTypes = [];
        const seen = {};
        function visit(type) {
          if (seen[type]) {
            return;
          }
          if (registeredTypes[type]) {
            return;
          }
          if (typeDependencies[type]) {
            typeDependencies[type].forEach(visit);
            return;
          }
          unboundTypes.push(type);
          seen[type] = true;
        }
        types.forEach(visit);
        throw new UnboundTypeError(`${message}: ${unboundTypes.map(getTypeName).join([", "])}`);
      }
      function __embind_register_class(rawType, rawPointerType, rawConstPointerType, baseClassRawType, getActualTypeSignature, getActualType, upcastSignature, upcast, downcastSignature, downcast, name, destructorSignature, rawDestructor) {
        name = readLatin1String(name);
        getActualType = embind__requireFunction(getActualTypeSignature, getActualType);
        if (upcast) {
          upcast = embind__requireFunction(upcastSignature, upcast);
        }
        if (downcast) {
          downcast = embind__requireFunction(downcastSignature, downcast);
        }
        rawDestructor = embind__requireFunction(destructorSignature, rawDestructor);
        const legalFunctionName = makeLegalFunctionName(name);
        exposePublicSymbol(legalFunctionName, () => {
          throwUnboundTypeError(`Cannot construct ${name} due to unbound types`, [baseClassRawType]);
        });
        whenDependentTypesAreResolved([rawType, rawPointerType, rawConstPointerType], baseClassRawType ? [baseClassRawType] : [], (base) => {
          base = base[0];
          let baseClass;
          let basePrototype;
          if (baseClassRawType) {
            baseClass = base.registeredClass;
            basePrototype = baseClass.instancePrototype;
          } else {
            basePrototype = ClassHandle.prototype;
          }
          const constructor = createNamedFunction(legalFunctionName, function() {
            if (Object.getPrototypeOf(this) !== instancePrototype) {
              throw new BindingError(`Use 'new' to construct ${name}`);
            }
            if (void 0 === registeredClass.constructor_body) {
              throw new BindingError(`${name} has no accessible constructor`);
            }
            const body = registeredClass.constructor_body[arguments.length];
            if (void 0 === body) {
              throw new BindingError(`Tried to invoke ctor of ${name} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(registeredClass.constructor_body).toString()}) parameters instead!`);
            }
            return body.apply(this, arguments);
          });
          var instancePrototype = Object.create(basePrototype, { constructor: { value: constructor } });
          constructor.prototype = instancePrototype;
          var registeredClass = new RegisteredClass(name, constructor, instancePrototype, rawDestructor, baseClass, getActualType, upcast, downcast);
          const referenceConverter = new RegisteredPointer(name, registeredClass, true, false, false);
          const pointerConverter = new RegisteredPointer(`${name}*`, registeredClass, false, false, false);
          const constPointerConverter = new RegisteredPointer(`${name} const*`, registeredClass, false, true, false);
          registeredPointers[rawType] = { pointerType: pointerConverter, constPointerType: constPointerConverter };
          replacePublicSymbol(legalFunctionName, constructor);
          return [referenceConverter, pointerConverter, constPointerConverter];
        });
      }
      function new_(constructor, argumentList) {
        if (!(constructor instanceof Function)) {
          throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`);
        }
        const dummy = createNamedFunction(constructor.name || "unknownFunctionName", () => {
        });
        dummy.prototype = constructor.prototype;
        const obj = new dummy();
        const r = constructor.apply(obj, argumentList);
        return r instanceof Object ? r : obj;
      }
      function craftInvokerFunction(humanName, argTypes, classType, cppInvokerFunc, cppTargetFunc) {
        const argCount = argTypes.length;
        if (argCount < 2) {
          throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!");
        }
        const isClassMethodFunc = argTypes[1] !== null && classType !== null;
        let needsDestructorStack = false;
        for (var i2 = 1; i2 < argTypes.length; ++i2) {
          if (argTypes[i2] !== null && argTypes[i2].destructorFunction === void 0) {
            needsDestructorStack = true;
            break;
          }
        }
        const returns = argTypes[0].name !== "void";
        let argsList = "";
        let argsListWired = "";
        for (var i2 = 0; i2 < argCount - 2; ++i2) {
          argsList += `${i2 !== 0 ? ", " : ""}arg${i2}`;
          argsListWired += `${i2 !== 0 ? ", " : ""}arg${i2}Wired`;
        }
        let invokerFnBody = `return function ${makeLegalFunctionName(humanName)}(${argsList}) {
if (arguments.length !== ${argCount - 2}) {
throwBindingError('function ${humanName} called with ' + arguments.length + ' arguments, expected ${argCount - 2} args!');
}
`;
        if (needsDestructorStack) {
          invokerFnBody += "var destructors = [];\n";
        }
        const dtorStack = needsDestructorStack ? "destructors" : "null";
        const args1 = ["throwBindingError", "invoker", "fn", "runDestructors", "retType", "classParam"];
        const args2 = [throwBindingError, cppInvokerFunc, cppTargetFunc, runDestructors, argTypes[0], argTypes[1]];
        if (isClassMethodFunc) {
          invokerFnBody += `var thisWired = classParam.toWireType(${dtorStack}, this);
`;
        }
        for (var i2 = 0; i2 < argCount - 2; ++i2) {
          invokerFnBody += `var arg${i2}Wired = argType${i2}.toWireType(${dtorStack}, arg${i2}); // ${argTypes[i2 + 2].name}
`;
          args1.push(`argType${i2}`);
          args2.push(argTypes[i2 + 2]);
        }
        if (isClassMethodFunc) {
          argsListWired = `thisWired${argsListWired.length > 0 ? ", " : ""}${argsListWired}`;
        }
        invokerFnBody += `${returns ? "var rv = " : ""}invoker(fn${argsListWired.length > 0 ? ", " : ""}${argsListWired});
`;
        if (needsDestructorStack) {
          invokerFnBody += "runDestructors(destructors);\n";
        } else {
          for (var i2 = isClassMethodFunc ? 1 : 2; i2 < argTypes.length; ++i2) {
            const paramName = i2 === 1 ? "thisWired" : `arg${i2 - 2}Wired`;
            if (argTypes[i2].destructorFunction !== null) {
              invokerFnBody += `${paramName}_dtor(${paramName}); // ${argTypes[i2].name}
`;
              args1.push(`${paramName}_dtor`);
              args2.push(argTypes[i2].destructorFunction);
            }
          }
        }
        if (returns) {
          invokerFnBody += "var ret = retType.fromWireType(rv);\nreturn ret;\n";
        } else {
        }
        invokerFnBody += "}\n";
        args1.push(invokerFnBody);
        const invokerFunction = new_(Function, args1).apply(null, args2);
        return invokerFunction;
      }
      function heap32VectorToArray(count, firstElement) {
        const array = [];
        for (let i2 = 0; i2 < count; i2++) {
          array.push(HEAP32[(firstElement >> 2) + i2]);
        }
        return array;
      }
      function __embind_register_class_class_function(rawClassType, methodName, argCount, rawArgTypesAddr, invokerSignature, rawInvoker, fn) {
        const rawArgTypes = heap32VectorToArray(argCount, rawArgTypesAddr);
        methodName = readLatin1String(methodName);
        rawInvoker = embind__requireFunction(invokerSignature, rawInvoker);
        whenDependentTypesAreResolved([], [rawClassType], (classType) => {
          classType = classType[0];
          const humanName = `${classType.name}.${methodName}`;
          function unboundTypesHandler() {
            throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`, rawArgTypes);
          }
          if (methodName.startsWith("@@")) {
            methodName = Symbol[methodName.substring(2)];
          }
          const proto = classType.registeredClass.constructor;
          if (void 0 === proto[methodName]) {
            unboundTypesHandler.argCount = argCount - 1;
            proto[methodName] = unboundTypesHandler;
          } else {
            ensureOverloadTable(proto, methodName, humanName);
            proto[methodName].overloadTable[argCount - 1] = unboundTypesHandler;
          }
          whenDependentTypesAreResolved([], rawArgTypes, (argTypes) => {
            const invokerArgsArray = [argTypes[0], null].concat(argTypes.slice(1));
            const func = craftInvokerFunction(humanName, invokerArgsArray, null, rawInvoker, fn);
            if (void 0 === proto[methodName].overloadTable) {
              func.argCount = argCount - 1;
              proto[methodName] = func;
            } else {
              proto[methodName].overloadTable[argCount - 1] = func;
            }
            return [];
          });
          return [];
        });
      }
      function __embind_register_class_constructor(rawClassType, argCount, rawArgTypesAddr, invokerSignature, invoker, rawConstructor) {
        assert(argCount > 0);
        const rawArgTypes = heap32VectorToArray(argCount, rawArgTypesAddr);
        invoker = embind__requireFunction(invokerSignature, invoker);
        whenDependentTypesAreResolved([], [rawClassType], (classType) => {
          classType = classType[0];
          const humanName = `constructor ${classType.name}`;
          if (void 0 === classType.registeredClass.constructor_body) {
            classType.registeredClass.constructor_body = [];
          }
          if (void 0 !== classType.registeredClass.constructor_body[argCount - 1]) {
            throw new BindingError(`Cannot register multiple constructors with identical number of parameters (${argCount - 1}) for class '${classType.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);
          }
          classType.registeredClass.constructor_body[argCount - 1] = function unboundTypeHandler() {
            throwUnboundTypeError(`Cannot construct ${classType.name} due to unbound types`, rawArgTypes);
          };
          whenDependentTypesAreResolved([], rawArgTypes, (argTypes) => {
            argTypes.splice(1, 0, null);
            classType.registeredClass.constructor_body[argCount - 1] = craftInvokerFunction(humanName, argTypes, null, invoker, rawConstructor);
            return [];
          });
          return [];
        });
      }
      function __embind_register_class_function(rawClassType, methodName, argCount, rawArgTypesAddr, invokerSignature, rawInvoker, context, isPureVirtual) {
        const rawArgTypes = heap32VectorToArray(argCount, rawArgTypesAddr);
        methodName = readLatin1String(methodName);
        rawInvoker = embind__requireFunction(invokerSignature, rawInvoker);
        whenDependentTypesAreResolved([], [rawClassType], (classType) => {
          classType = classType[0];
          const humanName = `${classType.name}.${methodName}`;
          if (methodName.startsWith("@@")) {
            methodName = Symbol[methodName.substring(2)];
          }
          if (isPureVirtual) {
            classType.registeredClass.pureVirtualFunctions.push(methodName);
          }
          function unboundTypesHandler() {
            throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`, rawArgTypes);
          }
          const proto = classType.registeredClass.instancePrototype;
          const method = proto[methodName];
          if (void 0 === method || void 0 === method.overloadTable && method.className !== classType.name && method.argCount === argCount - 2) {
            unboundTypesHandler.argCount = argCount - 2;
            unboundTypesHandler.className = classType.name;
            proto[methodName] = unboundTypesHandler;
          } else {
            ensureOverloadTable(proto, methodName, humanName);
            proto[methodName].overloadTable[argCount - 2] = unboundTypesHandler;
          }
          whenDependentTypesAreResolved([], rawArgTypes, (argTypes) => {
            const memberFunction = craftInvokerFunction(humanName, argTypes, classType, rawInvoker, context);
            if (void 0 === proto[methodName].overloadTable) {
              memberFunction.argCount = argCount - 2;
              proto[methodName] = memberFunction;
            } else {
              proto[methodName].overloadTable[argCount - 2] = memberFunction;
            }
            return [];
          });
          return [];
        });
      }
      function validateThis(this_, classType, humanName) {
        if (!(this_ instanceof Object)) {
          throwBindingError(`${humanName} with invalid "this": ${this_}`);
        }
        if (!(this_ instanceof classType.registeredClass.constructor)) {
          throwBindingError(`${humanName} incompatible with "this" of type ${this_.constructor.name}`);
        }
        if (!this_.$$.ptr) {
          throwBindingError(`cannot call emscripten binding method ${humanName} on deleted object`);
        }
        return upcastPointer(this_.$$.ptr, this_.$$.ptrType.registeredClass, classType.registeredClass);
      }
      function __embind_register_class_property(classType, fieldName, getterReturnType, getterSignature, getter, getterContext, setterArgumentType, setterSignature, setter, setterContext) {
        fieldName = readLatin1String(fieldName);
        getter = embind__requireFunction(getterSignature, getter);
        whenDependentTypesAreResolved([], [classType], (classType2) => {
          classType2 = classType2[0];
          const humanName = `${classType2.name}.${fieldName}`;
          const desc = { get() {
            throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`, [getterReturnType, setterArgumentType]);
          }, enumerable: true, configurable: true };
          if (setter) {
            desc.set = function() {
              throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`, [getterReturnType, setterArgumentType]);
            };
          } else {
            desc.set = function(v) {
              throwBindingError(`${humanName} is a read-only property`);
            };
          }
          Object.defineProperty(classType2.registeredClass.instancePrototype, fieldName, desc);
          whenDependentTypesAreResolved([], setter ? [getterReturnType, setterArgumentType] : [getterReturnType], (types) => {
            const getterReturnType2 = types[0];
            const desc2 = { get() {
              const ptr = validateThis(this, classType2, `${humanName} getter`);
              return getterReturnType2.fromWireType(getter(getterContext, ptr));
            }, enumerable: true };
            if (setter) {
              setter = embind__requireFunction(setterSignature, setter);
              const setterArgumentType2 = types[1];
              desc2.set = function(v) {
                const ptr = validateThis(this, classType2, `${humanName} setter`);
                const destructors = [];
                setter(setterContext, ptr, setterArgumentType2.toWireType(destructors, v));
                runDestructors(destructors);
              };
            }
            Object.defineProperty(classType2.registeredClass.instancePrototype, fieldName, desc2);
            return [];
          });
          return [];
        });
      }
      function __emval_decref(handle) {
        if (handle > 4 && 0 === --emval_handle_array[handle].refcount) {
          emval_handle_array[handle] = void 0;
          emval_free_list.push(handle);
        }
      }
      function __embind_register_emval(rawType, name) {
        name = readLatin1String(name);
        registerType(rawType, { name, fromWireType(handle) {
          const rv = emval_handle_array[handle].value;
          __emval_decref(handle);
          return rv;
        }, toWireType(destructors, value) {
          return __emval_register(value);
        }, argPackAdvance: 8, readValueFromPointer: simpleReadValueFromPointer, destructorFunction: null });
      }
      function _embind_repr(v) {
        if (v === null) {
          return "null";
        }
        const t = typeof v;
        if (t === "object" || t === "array" || t === "function") {
          return v.toString();
        }
        return `${v}`;
      }
      function floatReadValueFromPointer(name, shift) {
        switch (shift) {
          case 2:
            return function(pointer) {
              return this.fromWireType(HEAPF32[pointer >> 2]);
            };
          case 3:
            return function(pointer) {
              return this.fromWireType(HEAPF64[pointer >> 3]);
            };
          default:
            throw new TypeError(`Unknown float type: ${name}`);
        }
      }
      function __embind_register_float(rawType, name, size) {
        const shift = getShiftFromSize(size);
        name = readLatin1String(name);
        registerType(rawType, { name, fromWireType(value) {
          return value;
        }, toWireType(destructors, value) {
          if (typeof value !== "number" && typeof value !== "boolean") {
            throw new TypeError(`Cannot convert "${_embind_repr(value)}" to ${this.name}`);
          }
          return value;
        }, argPackAdvance: 8, readValueFromPointer: floatReadValueFromPointer(name, shift), destructorFunction: null });
      }
      function __embind_register_function(name, argCount, rawArgTypesAddr, signature, rawInvoker, fn) {
        const argTypes = heap32VectorToArray(argCount, rawArgTypesAddr);
        name = readLatin1String(name);
        rawInvoker = embind__requireFunction(signature, rawInvoker);
        exposePublicSymbol(name, () => {
          throwUnboundTypeError(`Cannot call ${name} due to unbound types`, argTypes);
        }, argCount - 1);
        whenDependentTypesAreResolved([], argTypes, (argTypes2) => {
          const invokerArgsArray = [argTypes2[0], null].concat(argTypes2.slice(1));
          replacePublicSymbol(name, craftInvokerFunction(name, invokerArgsArray, null, rawInvoker, fn), argCount - 1);
          return [];
        });
      }
      function integerReadValueFromPointer(name, shift, signed) {
        switch (shift) {
          case 0:
            return signed ? function readS8FromPointer(pointer) {
              return HEAP8[pointer];
            } : function readU8FromPointer(pointer) {
              return HEAPU8[pointer];
            };
          case 1:
            return signed ? function readS16FromPointer(pointer) {
              return HEAP16[pointer >> 1];
            } : function readU16FromPointer(pointer) {
              return HEAPU16[pointer >> 1];
            };
          case 2:
            return signed ? function readS32FromPointer(pointer) {
              return HEAP32[pointer >> 2];
            } : function readU32FromPointer(pointer) {
              return HEAPU32[pointer >> 2];
            };
          default:
            throw new TypeError(`Unknown integer type: ${name}`);
        }
      }
      function __embind_register_integer(primitiveType, name, size, minRange, maxRange) {
        name = readLatin1String(name);
        if (maxRange === -1) {
          maxRange = 4294967295;
        }
        const shift = getShiftFromSize(size);
        let fromWireType = function(value) {
          return value;
        };
        if (minRange === 0) {
          const bitshift = 32 - 8 * size;
          fromWireType = function(value) {
            return value << bitshift >>> bitshift;
          };
        }
        const isUnsignedType = name.includes("unsigned");
        registerType(primitiveType, { name, fromWireType, toWireType(destructors, value) {
          if (typeof value !== "number" && typeof value !== "boolean") {
            throw new TypeError(`Cannot convert "${_embind_repr(value)}" to ${this.name}`);
          }
          if (value < minRange || value > maxRange) {
            throw new TypeError(`Passing a number "${_embind_repr(value)}" from JS side to C/C++ side to an argument of type "${name}", which is outside the valid range [${minRange}, ${maxRange}]!`);
          }
          return isUnsignedType ? value >>> 0 : value | 0;
        }, argPackAdvance: 8, readValueFromPointer: integerReadValueFromPointer(name, shift, minRange !== 0), destructorFunction: null });
      }
      function __embind_register_memory_view(rawType, dataTypeIndex, name) {
        const typeMapping = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array];
        const TA = typeMapping[dataTypeIndex];
        function decodeMemoryView(handle) {
          handle = handle >> 2;
          const heap = HEAPU32;
          const size = heap[handle];
          const data = heap[handle + 1];
          return new TA(buffer, data, size);
        }
        name = readLatin1String(name);
        registerType(rawType, { name, fromWireType: decodeMemoryView, argPackAdvance: 8, readValueFromPointer: decodeMemoryView }, { ignoreDuplicateRegistrations: true });
      }
      function __embind_register_std_string(rawType, name) {
        name = readLatin1String(name);
        const stdStringIsUTF8 = name === "std::string";
        registerType(rawType, { name, fromWireType(value) {
          const length = HEAPU32[value >> 2];
          let str;
          if (stdStringIsUTF8) {
            let decodeStartPtr = value + 4;
            for (var i2 = 0; i2 <= length; ++i2) {
              const currentBytePtr = value + 4 + i2;
              if (i2 == length || HEAPU8[currentBytePtr] == 0) {
                const maxRead = currentBytePtr - decodeStartPtr;
                const stringSegment = UTF8ToString(decodeStartPtr, maxRead);
                if (str === void 0) {
                  str = stringSegment;
                } else {
                  str += String.fromCharCode(0);
                  str += stringSegment;
                }
                decodeStartPtr = currentBytePtr + 1;
              }
            }
          } else {
            const a = new Array(length);
            for (var i2 = 0; i2 < length; ++i2) {
              a[i2] = String.fromCharCode(HEAPU8[value + 4 + i2]);
            }
            str = a.join("");
          }
          _free(value);
          return str;
        }, toWireType(destructors, value) {
          if (value instanceof ArrayBuffer) {
            value = new Uint8Array(value);
          }
          let getLength;
          const valueIsOfTypeString = typeof value === "string";
          if (!(valueIsOfTypeString || value instanceof Uint8Array || value instanceof Uint8ClampedArray || value instanceof Int8Array)) {
            throwBindingError("Cannot pass non-string to std::string");
          }
          if (stdStringIsUTF8 && valueIsOfTypeString) {
            getLength = function() {
              return lengthBytesUTF8(value);
            };
          } else {
            getLength = function() {
              return value.length;
            };
          }
          const length = getLength();
          const ptr = _malloc(4 + length + 1);
          HEAPU32[ptr >> 2] = length;
          if (stdStringIsUTF8 && valueIsOfTypeString) {
            stringToUTF8(value, ptr + 4, length + 1);
          } else {
            if (valueIsOfTypeString) {
              for (var i2 = 0; i2 < length; ++i2) {
                const charCode = value.charCodeAt(i2);
                if (charCode > 255) {
                  _free(ptr);
                  throwBindingError("String has UTF-16 code units that do not fit in 8 bits");
                }
                HEAPU8[ptr + 4 + i2] = charCode;
              }
            } else {
              for (var i2 = 0; i2 < length; ++i2) {
                HEAPU8[ptr + 4 + i2] = value[i2];
              }
            }
          }
          if (destructors !== null) {
            destructors.push(_free, ptr);
          }
          return ptr;
        }, argPackAdvance: 8, readValueFromPointer: simpleReadValueFromPointer, destructorFunction(ptr) {
          _free(ptr);
        } });
      }
      function __embind_register_std_wstring(rawType, charSize, name) {
        name = readLatin1String(name);
        let decodeString;
        let encodeString;
        let getHeap;
        let lengthBytesUTF;
        let shift;
        if (charSize === 2) {
          decodeString = UTF16ToString;
          encodeString = stringToUTF16;
          lengthBytesUTF = lengthBytesUTF16;
          getHeap = function() {
            return HEAPU16;
          };
          shift = 1;
        } else if (charSize === 4) {
          decodeString = UTF32ToString;
          encodeString = stringToUTF32;
          lengthBytesUTF = lengthBytesUTF32;
          getHeap = function() {
            return HEAPU32;
          };
          shift = 2;
        }
        registerType(rawType, { name, fromWireType(value) {
          const length = HEAPU32[value >> 2];
          const HEAP = getHeap();
          let str;
          let decodeStartPtr = value + 4;
          for (let i2 = 0; i2 <= length; ++i2) {
            const currentBytePtr = value + 4 + i2 * charSize;
            if (i2 == length || HEAP[currentBytePtr >> shift] == 0) {
              const maxReadBytes = currentBytePtr - decodeStartPtr;
              const stringSegment = decodeString(decodeStartPtr, maxReadBytes);
              if (str === void 0) {
                str = stringSegment;
              } else {
                str += String.fromCharCode(0);
                str += stringSegment;
              }
              decodeStartPtr = currentBytePtr + charSize;
            }
          }
          _free(value);
          return str;
        }, toWireType(destructors, value) {
          if (!(typeof value === "string")) {
            throwBindingError(`Cannot pass non-string to C++ string type ${name}`);
          }
          const length = lengthBytesUTF(value);
          const ptr = _malloc(4 + length + charSize);
          HEAPU32[ptr >> 2] = length >> shift;
          encodeString(value, ptr + 4, length + charSize);
          if (destructors !== null) {
            destructors.push(_free, ptr);
          }
          return ptr;
        }, argPackAdvance: 8, readValueFromPointer: simpleReadValueFromPointer, destructorFunction(ptr) {
          _free(ptr);
        } });
      }
      function __embind_register_value_object(rawType, name, constructorSignature, rawConstructor, destructorSignature, rawDestructor) {
        structRegistrations[rawType] = { name: readLatin1String(name), rawConstructor: embind__requireFunction(constructorSignature, rawConstructor), rawDestructor: embind__requireFunction(destructorSignature, rawDestructor), fields: [] };
      }
      function __embind_register_value_object_field(structType, fieldName, getterReturnType, getterSignature, getter, getterContext, setterArgumentType, setterSignature, setter, setterContext) {
        structRegistrations[structType].fields.push({ fieldName: readLatin1String(fieldName), getterReturnType, getter: embind__requireFunction(getterSignature, getter), getterContext, setterArgumentType, setter: embind__requireFunction(setterSignature, setter), setterContext });
      }
      function __embind_register_void(rawType, name) {
        name = readLatin1String(name);
        registerType(rawType, { isVoid: true, name, argPackAdvance: 0, fromWireType() {
          return void 0;
        }, toWireType(destructors, o) {
          return void 0;
        } });
      }
      const emval_symbols = {};
      function getStringOrSymbol(address) {
        const symbol = emval_symbols[address];
        if (symbol === void 0) {
          return readLatin1String(address);
        }
        return symbol;
      }
      const emval_methodCallers = [];
      function __emval_call_void_method(caller, handle, methodName, args) {
        caller = emval_methodCallers[caller];
        handle = requireHandle(handle);
        methodName = getStringOrSymbol(methodName);
        caller(handle, methodName, null, args);
      }
      function __emval_addMethodCaller(caller) {
        const id = emval_methodCallers.length;
        emval_methodCallers.push(caller);
        return id;
      }
      function __emval_lookupTypes(argCount, argTypes) {
        const a = new Array(argCount);
        for (let i2 = 0; i2 < argCount; ++i2) {
          a[i2] = requireRegisteredType(HEAP32[(argTypes >> 2) + i2], `parameter ${i2}`);
        }
        return a;
      }
      function __emval_get_method_caller(argCount, argTypes) {
        const types = __emval_lookupTypes(argCount, argTypes);
        const retType = types[0];
        const signatureName = `${retType.name}_$${types.slice(1).map((t) => t.name).join("_")}$`;
        const params = ["retType"];
        const args = [retType];
        let argsList = "";
        for (var i2 = 0; i2 < argCount - 1; ++i2) {
          argsList += `${i2 !== 0 ? ", " : ""}arg${i2}`;
          params.push(`argType${i2}`);
          args.push(types[1 + i2]);
        }
        const functionName = makeLegalFunctionName(`methodCaller_${signatureName}`);
        let functionBody = `return function ${functionName}(handle, name, destructors, args) {
`;
        let offset = 0;
        for (var i2 = 0; i2 < argCount - 1; ++i2) {
          functionBody += `    var arg${i2} = argType${i2}.readValueFromPointer(args${offset ? `+${offset}` : ""});
`;
          offset += types[i2 + 1].argPackAdvance;
        }
        functionBody += `    var rv = handle[name](${argsList});
`;
        for (var i2 = 0; i2 < argCount - 1; ++i2) {
          if (types[i2 + 1].deleteObject) {
            functionBody += `    argType${i2}.deleteObject(arg${i2});
`;
          }
        }
        if (!retType.isVoid) {
          functionBody += "    return retType.toWireType(destructors, rv);\n";
        }
        functionBody += "};\n";
        params.push(functionBody);
        const invokerFunction = new_(Function, params).apply(null, args);
        return __emval_addMethodCaller(invokerFunction);
      }
      function __emval_incref(handle) {
        if (handle > 4) {
          emval_handle_array[handle].refcount += 1;
        }
      }
      function __emval_take_value(type, argv) {
        type = requireRegisteredType(type, "_emval_take_value");
        const v = type.readValueFromPointer(argv);
        return __emval_register(v);
      }
      function _abort() {
        abort();
      }
      const _emscripten_get_now_is_monotonic = true;
      function _clock_gettime(clk_id, tp) {
        let now;
        if (clk_id === 0) {
          now = Date.now();
        } else if ((clk_id === 1 || clk_id === 4) && _emscripten_get_now_is_monotonic) {
          now = _emscripten_get_now();
        } else {
          setErrNo(28);
          return -1;
        }
        HEAP32[tp >> 2] = now / 1e3 | 0;
        HEAP32[tp + 4 >> 2] = now % 1e3 * 1e3 * 1e3 | 0;
        return 0;
      }
      function _dlopen(filename, flag) {
        abort("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking");
      }
      function _dlsym(handle, symbol) {
        abort("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking");
      }
      const readAsmConstArgsArray = [];
      function readAsmConstArgs(sigPtr, buf) {
        assert(Array.isArray(readAsmConstArgsArray));
        assert(buf % 16 == 0);
        readAsmConstArgsArray.length = 0;
        let ch;
        buf >>= 2;
        while (ch = HEAPU8[sigPtr++]) {
          assert(ch === 100 || ch === 102 || ch === 105);
          const double = ch < 105;
          if (double && buf & 1)
            buf++;
          readAsmConstArgsArray.push(double ? HEAPF64[buf++ >> 1] : HEAP32[buf]);
          ++buf;
        }
        return readAsmConstArgsArray;
      }
      function _emscripten_asm_const_int(code, sigPtr, argbuf) {
        const args = readAsmConstArgs(sigPtr, argbuf);
        if (!ASM_CONSTS.hasOwnProperty(code))
          abort(`No EM_ASM constant found at address ${code}`);
        return ASM_CONSTS[code].apply(null, args);
      }
      function _emscripten_get_heap_max() {
        return 2147483648;
      }
      function _emscripten_memcpy_big(dest, src, num) {
        HEAPU8.copyWithin(dest, src, src + num);
      }
      function _emscripten_pc_get_function(pc) {
        abort("Cannot use emscripten_pc_get_function without -s USE_OFFSET_CONVERTER");
      }
      function emscripten_realloc_buffer(size) {
        try {
          wasmMemory.grow(size - buffer.byteLength + 65535 >>> 16);
          updateGlobalBufferAndViews(wasmMemory.buffer);
          return 1;
        } catch (e) {
          err(`emscripten_realloc_buffer: Attempted to grow heap from ${buffer.byteLength} bytes to ${size} bytes, but got error: ${e}`);
        }
      }
      function _emscripten_resize_heap(requestedSize) {
        const oldSize = HEAPU8.length;
        requestedSize = requestedSize >>> 0;
        assert(requestedSize > oldSize);
        const maxHeapSize = 2147483648;
        if (requestedSize > maxHeapSize) {
          err(`Cannot enlarge memory, asked to go up to ${requestedSize} bytes, but the limit is ${maxHeapSize} bytes!`);
          return false;
        }
        for (let cutDown = 1; cutDown <= 4; cutDown *= 2) {
          let overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);
          overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);
          var newSize = Math.min(maxHeapSize, alignUp(Math.max(requestedSize, overGrownHeapSize), 65536));
          const replacement = emscripten_realloc_buffer(newSize);
          if (replacement) {
            return true;
          }
        }
        err(`Failed to grow the heap from ${oldSize} bytes to ${newSize} bytes, not enough memory!`);
        return false;
      }
      function _emscripten_generate_pc(frame) {
        abort("Cannot use emscripten_generate_pc (needed by __builtin_return_address) without -s USE_OFFSET_CONVERTER");
      }
      const UNWIND_CACHE = {};
      function __emscripten_save_in_unwind_cache(callstack) {
        callstack.forEach((frame) => {
          const pc = _emscripten_generate_pc(frame);
          if (pc) {
            UNWIND_CACHE[pc] = frame;
          }
        });
      }
      function _emscripten_stack_snapshot() {
        const callstack = new Error().stack.split("\n");
        if (callstack[0] == "Error") {
          callstack.shift();
        }
        __emscripten_save_in_unwind_cache(callstack);
        UNWIND_CACHE.last_addr = _emscripten_generate_pc(callstack[2]);
        UNWIND_CACHE.last_stack = callstack;
        return UNWIND_CACHE.last_addr;
      }
      function _emscripten_stack_unwind_buffer(addr, buffer2, count) {
        let stack;
        if (UNWIND_CACHE.last_addr == addr) {
          stack = UNWIND_CACHE.last_stack;
        } else {
          stack = new Error().stack.split("\n");
          if (stack[0] == "Error") {
            stack.shift();
          }
          __emscripten_save_in_unwind_cache(stack);
        }
        let offset = 2;
        while (stack[offset] && _emscripten_generate_pc(stack[offset]) != addr) {
          ++offset;
        }
        for (var i2 = 0; i2 < count && stack[i2 + offset]; ++i2) {
          HEAP32[buffer2 + i2 * 4 >> 2] = _emscripten_generate_pc(stack[i2 + offset]);
        }
        return i2;
      }
      function _emscripten_thread_sleep(msecs) {
        const start = _emscripten_get_now();
        while (_emscripten_get_now() - start < msecs) {
        }
      }
      function __webgl_enable_ANGLE_instanced_arrays(ctx) {
        const ext = ctx.getExtension("ANGLE_instanced_arrays");
        if (ext) {
          ctx.vertexAttribDivisor = function(index, divisor) {
            ext.vertexAttribDivisorANGLE(index, divisor);
          };
          ctx.drawArraysInstanced = function(mode, first, count, primcount) {
            ext.drawArraysInstancedANGLE(mode, first, count, primcount);
          };
          ctx.drawElementsInstanced = function(mode, count, type, indices, primcount) {
            ext.drawElementsInstancedANGLE(mode, count, type, indices, primcount);
          };
          return 1;
        }
      }
      function __webgl_enable_OES_vertex_array_object(ctx) {
        const ext = ctx.getExtension("OES_vertex_array_object");
        if (ext) {
          ctx.createVertexArray = function() {
            return ext.createVertexArrayOES();
          };
          ctx.deleteVertexArray = function(vao) {
            ext.deleteVertexArrayOES(vao);
          };
          ctx.bindVertexArray = function(vao) {
            ext.bindVertexArrayOES(vao);
          };
          ctx.isVertexArray = function(vao) {
            return ext.isVertexArrayOES(vao);
          };
          return 1;
        }
      }
      function __webgl_enable_WEBGL_draw_buffers(ctx) {
        const ext = ctx.getExtension("WEBGL_draw_buffers");
        if (ext) {
          ctx.drawBuffers = function(n, bufs) {
            ext.drawBuffersWEBGL(n, bufs);
          };
          return 1;
        }
      }
      function __webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(ctx) {
        return !!(ctx.dibvbi = ctx.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"));
      }
      function __webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(ctx) {
        return !!(ctx.mdibvbi = ctx.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"));
      }
      function __webgl_enable_WEBGL_multi_draw(ctx) {
        return !!(ctx.multiDrawWebgl = ctx.getExtension("WEBGL_multi_draw"));
      }
      var GL = { counter: 1, buffers: [], mappedBuffers: {}, programs: [], framebuffers: [], renderbuffers: [], textures: [], shaders: [], vaos: [], contexts: [], offscreenCanvases: {}, queries: [], samplers: [], transformFeedbacks: [], syncs: [], byteSizeByTypeRoot: 5120, byteSizeByType: [1, 1, 2, 2, 4, 4, 4, 2, 3, 4, 8], stringCache: {}, stringiCache: {}, unpackAlignment: 4, recordError: function recordError(errorCode) {
        if (!GL.lastError) {
          GL.lastError = errorCode;
        }
      }, getNewId(table) {
        const ret = GL.counter++;
        for (let i2 = table.length; i2 < ret; i2++) {
          table[i2] = null;
        }
        return ret;
      }, MAX_TEMP_BUFFER_SIZE: 2097152, numTempVertexBuffersPerSize: 64, log2ceilLookup(i2) {
        return 32 - Math.clz32(i2 === 0 ? 0 : i2 - 1);
      }, generateTempBuffers(quads, context) {
        const largestIndex = GL.log2ceilLookup(GL.MAX_TEMP_BUFFER_SIZE);
        context.tempVertexBufferCounters1 = [];
        context.tempVertexBufferCounters2 = [];
        context.tempVertexBufferCounters1.length = context.tempVertexBufferCounters2.length = largestIndex + 1;
        context.tempVertexBuffers1 = [];
        context.tempVertexBuffers2 = [];
        context.tempVertexBuffers1.length = context.tempVertexBuffers2.length = largestIndex + 1;
        context.tempIndexBuffers = [];
        context.tempIndexBuffers.length = largestIndex + 1;
        for (var i2 = 0; i2 <= largestIndex; ++i2) {
          context.tempIndexBuffers[i2] = null;
          context.tempVertexBufferCounters1[i2] = context.tempVertexBufferCounters2[i2] = 0;
          const ringbufferLength = GL.numTempVertexBuffersPerSize;
          context.tempVertexBuffers1[i2] = [];
          context.tempVertexBuffers2[i2] = [];
          const ringbuffer1 = context.tempVertexBuffers1[i2];
          const ringbuffer2 = context.tempVertexBuffers2[i2];
          ringbuffer1.length = ringbuffer2.length = ringbufferLength;
          for (let j = 0; j < ringbufferLength; ++j) {
            ringbuffer1[j] = ringbuffer2[j] = null;
          }
        }
        if (quads) {
          context.tempQuadIndexBuffer = GLctx.createBuffer();
          context.GLctx.bindBuffer(34963, context.tempQuadIndexBuffer);
          const numIndexes = GL.MAX_TEMP_BUFFER_SIZE >> 1;
          const quadIndexes = new Uint16Array(numIndexes);
          var i2 = 0;
          let v = 0;
          while (1) {
            quadIndexes[i2++] = v;
            if (i2 >= numIndexes)
              break;
            quadIndexes[i2++] = v + 1;
            if (i2 >= numIndexes)
              break;
            quadIndexes[i2++] = v + 2;
            if (i2 >= numIndexes)
              break;
            quadIndexes[i2++] = v;
            if (i2 >= numIndexes)
              break;
            quadIndexes[i2++] = v + 2;
            if (i2 >= numIndexes)
              break;
            quadIndexes[i2++] = v + 3;
            if (i2 >= numIndexes)
              break;
            v += 4;
          }
          context.GLctx.bufferData(34963, quadIndexes, 35044);
          context.GLctx.bindBuffer(34963, null);
        }
      }, getTempVertexBuffer: function getTempVertexBuffer(sizeBytes) {
        const idx = GL.log2ceilLookup(sizeBytes);
        const ringbuffer = GL.currentContext.tempVertexBuffers1[idx];
        const nextFreeBufferIndex = GL.currentContext.tempVertexBufferCounters1[idx];
        GL.currentContext.tempVertexBufferCounters1[idx] = GL.currentContext.tempVertexBufferCounters1[idx] + 1 & GL.numTempVertexBuffersPerSize - 1;
        const vbo = ringbuffer[nextFreeBufferIndex];
        if (vbo) {
          return vbo;
        }
        const prevVBO = GLctx.getParameter(34964);
        ringbuffer[nextFreeBufferIndex] = GLctx.createBuffer();
        GLctx.bindBuffer(34962, ringbuffer[nextFreeBufferIndex]);
        GLctx.bufferData(34962, 1 << idx, 35048);
        GLctx.bindBuffer(34962, prevVBO);
        return ringbuffer[nextFreeBufferIndex];
      }, getTempIndexBuffer: function getTempIndexBuffer(sizeBytes) {
        const idx = GL.log2ceilLookup(sizeBytes);
        const ibo = GL.currentContext.tempIndexBuffers[idx];
        if (ibo) {
          return ibo;
        }
        const prevIBO = GLctx.getParameter(34965);
        GL.currentContext.tempIndexBuffers[idx] = GLctx.createBuffer();
        GLctx.bindBuffer(34963, GL.currentContext.tempIndexBuffers[idx]);
        GLctx.bufferData(34963, 1 << idx, 35048);
        GLctx.bindBuffer(34963, prevIBO);
        return GL.currentContext.tempIndexBuffers[idx];
      }, newRenderingFrameStarted: function newRenderingFrameStarted() {
        if (!GL.currentContext) {
          return;
        }
        let vb = GL.currentContext.tempVertexBuffers1;
        GL.currentContext.tempVertexBuffers1 = GL.currentContext.tempVertexBuffers2;
        GL.currentContext.tempVertexBuffers2 = vb;
        vb = GL.currentContext.tempVertexBufferCounters1;
        GL.currentContext.tempVertexBufferCounters1 = GL.currentContext.tempVertexBufferCounters2;
        GL.currentContext.tempVertexBufferCounters2 = vb;
        const largestIndex = GL.log2ceilLookup(GL.MAX_TEMP_BUFFER_SIZE);
        for (let i2 = 0; i2 <= largestIndex; ++i2) {
          GL.currentContext.tempVertexBufferCounters1[i2] = 0;
        }
      }, getSource(shader, count, string, length) {
        let source = "";
        for (let i2 = 0; i2 < count; ++i2) {
          const len = length ? HEAP32[length + i2 * 4 >> 2] : -1;
          source += UTF8ToString(HEAP32[string + i2 * 4 >> 2], len < 0 ? void 0 : len);
        }
        return source;
      }, calcBufLength: function calcBufLength(size, type, stride, count) {
        if (stride > 0) {
          return count * stride;
        }
        const typeSize = GL.byteSizeByType[type - GL.byteSizeByTypeRoot];
        return size * typeSize * count;
      }, usedTempBuffers: [], preDrawHandleClientVertexAttribBindings: function preDrawHandleClientVertexAttribBindings(count) {
        GL.resetBufferBinding = false;
        for (let i2 = 0; i2 < GL.currentContext.maxVertexAttribs; ++i2) {
          const cb = GL.currentContext.clientBuffers[i2];
          if (!cb.clientside || !cb.enabled)
            continue;
          GL.resetBufferBinding = true;
          const size = GL.calcBufLength(cb.size, cb.type, cb.stride, count);
          const buf = GL.getTempVertexBuffer(size);
          GLctx.bindBuffer(34962, buf);
          GLctx.bufferSubData(34962, 0, HEAPU8.subarray(cb.ptr, cb.ptr + size));
          cb.vertexAttribPointerAdaptor.call(GLctx, i2, cb.size, cb.type, cb.normalized, cb.stride, 0);
        }
      }, postDrawHandleClientVertexAttribBindings: function postDrawHandleClientVertexAttribBindings() {
        if (GL.resetBufferBinding) {
          GLctx.bindBuffer(34962, GL.buffers[GLctx.currentArrayBufferBinding]);
        }
      }, createContext(canvas, webGLContextAttributes) {
        if (webGLContextAttributes.renderViaOffscreenBackBuffer)
          webGLContextAttributes.preserveDrawingBuffer = true;
        if (!canvas.getContextSafariWebGL2Fixed) {
          canvas.getContextSafariWebGL2Fixed = canvas.getContext;
          canvas.getContext = function(ver, attrs) {
            const gl = canvas.getContextSafariWebGL2Fixed(ver, attrs);
            return ver == "webgl" == gl instanceof WebGLRenderingContext ? gl : null;
          };
        }
        const ctx = webGLContextAttributes.majorVersion > 1 ? canvas.getContext("webgl2", webGLContextAttributes) : canvas.getContext("webgl", webGLContextAttributes);
        if (!ctx)
          return 0;
        const handle = GL.registerContext(ctx, webGLContextAttributes);
        return handle;
      }, enableOffscreenFramebufferAttributes(webGLContextAttributes) {
        webGLContextAttributes.renderViaOffscreenBackBuffer = true;
        webGLContextAttributes.preserveDrawingBuffer = true;
      }, createOffscreenFramebuffer(context) {
        const gl = context.GLctx;
        const fbo = gl.createFramebuffer();
        gl.bindFramebuffer(36160, fbo);
        context.defaultFbo = fbo;
        context.defaultFboForbidBlitFramebuffer = false;
        if (gl.getContextAttributes().antialias) {
          context.defaultFboForbidBlitFramebuffer = true;
        } else {
          const firefoxMatch = navigator.userAgent.toLowerCase().match(/firefox\/(\d\d)/);
          if (firefoxMatch != null) {
            const firefoxVersion = firefoxMatch[1];
            context.defaultFboForbidBlitFramebuffer = firefoxVersion < 67;
          }
        }
        context.defaultColorTarget = gl.createTexture();
        context.defaultDepthTarget = gl.createRenderbuffer();
        GL.resizeOffscreenFramebuffer(context);
        gl.bindTexture(3553, context.defaultColorTarget);
        gl.texParameteri(3553, 10241, 9728);
        gl.texParameteri(3553, 10240, 9728);
        gl.texParameteri(3553, 10242, 33071);
        gl.texParameteri(3553, 10243, 33071);
        gl.texImage2D(3553, 0, 6408, gl.canvas.width, gl.canvas.height, 0, 6408, 5121, null);
        gl.framebufferTexture2D(36160, 36064, 3553, context.defaultColorTarget, 0);
        gl.bindTexture(3553, null);
        const depthTarget = gl.createRenderbuffer();
        gl.bindRenderbuffer(36161, context.defaultDepthTarget);
        gl.renderbufferStorage(36161, 33189, gl.canvas.width, gl.canvas.height);
        gl.framebufferRenderbuffer(36160, 36096, 36161, context.defaultDepthTarget);
        gl.bindRenderbuffer(36161, null);
        const vertices = [-1, -1, -1, 1, 1, -1, 1, 1];
        const vb = gl.createBuffer();
        gl.bindBuffer(34962, vb);
        gl.bufferData(34962, new Float32Array(vertices), 35044);
        gl.bindBuffer(34962, null);
        context.blitVB = vb;
        const vsCode = "attribute vec2 pos;varying lowp vec2 tex;void main() { tex = pos * 0.5 + vec2(0.5,0.5); gl_Position = vec4(pos, 0.0, 1.0); }";
        const vs = gl.createShader(35633);
        gl.shaderSource(vs, vsCode);
        gl.compileShader(vs);
        const fsCode = "varying lowp vec2 tex;uniform sampler2D sampler;void main() { gl_FragColor = texture2D(sampler, tex); }";
        const fs = gl.createShader(35632);
        gl.shaderSource(fs, fsCode);
        gl.compileShader(fs);
        const blitProgram = gl.createProgram();
        gl.attachShader(blitProgram, vs);
        gl.attachShader(blitProgram, fs);
        gl.linkProgram(blitProgram);
        context.blitProgram = blitProgram;
        context.blitPosLoc = gl.getAttribLocation(blitProgram, "pos");
        gl.useProgram(blitProgram);
        gl.uniform1i(gl.getUniformLocation(blitProgram, "sampler"), 0);
        gl.useProgram(null);
        context.defaultVao = void 0;
        if (gl.createVertexArray) {
          context.defaultVao = gl.createVertexArray();
          gl.bindVertexArray(context.defaultVao);
          gl.enableVertexAttribArray(context.blitPosLoc);
          gl.bindVertexArray(null);
        }
      }, resizeOffscreenFramebuffer(context) {
        const gl = context.GLctx;
        if (context.defaultColorTarget) {
          const prevTextureBinding = gl.getParameter(32873);
          gl.bindTexture(3553, context.defaultColorTarget);
          gl.texImage2D(3553, 0, 6408, gl.drawingBufferWidth, gl.drawingBufferHeight, 0, 6408, 5121, null);
          gl.bindTexture(3553, prevTextureBinding);
        }
        if (context.defaultDepthTarget) {
          const prevRenderBufferBinding = gl.getParameter(36007);
          gl.bindRenderbuffer(36161, context.defaultDepthTarget);
          gl.renderbufferStorage(36161, 33189, gl.drawingBufferWidth, gl.drawingBufferHeight);
          gl.bindRenderbuffer(36161, prevRenderBufferBinding);
        }
      }, blitOffscreenFramebuffer(context) {
        const gl = context.GLctx;
        const prevScissorTest = gl.getParameter(3089);
        if (prevScissorTest)
          gl.disable(3089);
        const prevFbo = gl.getParameter(36006);
        if (gl.blitFramebuffer && !context.defaultFboForbidBlitFramebuffer) {
          gl.bindFramebuffer(36008, context.defaultFbo);
          gl.bindFramebuffer(36009, null);
          gl.blitFramebuffer(0, 0, gl.canvas.width, gl.canvas.height, 0, 0, gl.canvas.width, gl.canvas.height, 16384, 9728);
        } else {
          let draw2 = function() {
            gl.vertexAttribPointer(context.blitPosLoc, 2, 5126, false, 0, 0);
            gl.drawArrays(5, 0, 4);
          };
          var draw = draw2;
          gl.bindFramebuffer(36160, null);
          const prevProgram = gl.getParameter(35725);
          gl.useProgram(context.blitProgram);
          const prevVB = gl.getParameter(34964);
          gl.bindBuffer(34962, context.blitVB);
          const prevActiveTexture = gl.getParameter(34016);
          gl.activeTexture(33984);
          const prevTextureBinding = gl.getParameter(32873);
          gl.bindTexture(3553, context.defaultColorTarget);
          const prevBlend = gl.getParameter(3042);
          if (prevBlend)
            gl.disable(3042);
          const prevCullFace = gl.getParameter(2884);
          if (prevCullFace)
            gl.disable(2884);
          const prevDepthTest = gl.getParameter(2929);
          if (prevDepthTest)
            gl.disable(2929);
          const prevStencilTest = gl.getParameter(2960);
          if (prevStencilTest)
            gl.disable(2960);
          if (context.defaultVao) {
            const prevVAO = gl.getParameter(34229);
            gl.bindVertexArray(context.defaultVao);
            draw2();
            gl.bindVertexArray(prevVAO);
          } else {
            const prevVertexAttribPointer = { buffer: gl.getVertexAttrib(context.blitPosLoc, 34975), size: gl.getVertexAttrib(context.blitPosLoc, 34339), stride: gl.getVertexAttrib(context.blitPosLoc, 34340), type: gl.getVertexAttrib(context.blitPosLoc, 34341), normalized: gl.getVertexAttrib(context.blitPosLoc, 34922), pointer: gl.getVertexAttribOffset(context.blitPosLoc, 34373) };
            const maxVertexAttribs = gl.getParameter(34921);
            const prevVertexAttribEnables = [];
            for (var i2 = 0; i2 < maxVertexAttribs; ++i2) {
              var prevEnabled = gl.getVertexAttrib(i2, 34338);
              const wantEnabled = i2 == context.blitPosLoc;
              if (prevEnabled && !wantEnabled) {
                gl.disableVertexAttribArray(i2);
              }
              if (!prevEnabled && wantEnabled) {
                gl.enableVertexAttribArray(i2);
              }
              prevVertexAttribEnables[i2] = prevEnabled;
            }
            draw2();
            for (var i2 = 0; i2 < maxVertexAttribs; ++i2) {
              var prevEnabled = prevVertexAttribEnables[i2];
              const nowEnabled = i2 == context.blitPosLoc;
              if (prevEnabled && !nowEnabled) {
                gl.enableVertexAttribArray(i2);
              }
              if (!prevEnabled && nowEnabled) {
                gl.disableVertexAttribArray(i2);
              }
            }
            gl.bindBuffer(34962, prevVertexAttribPointer.buffer);
            gl.vertexAttribPointer(context.blitPosLoc, prevVertexAttribPointer.size, prevVertexAttribPointer.type, prevVertexAttribPointer.normalized, prevVertexAttribPointer.stride, prevVertexAttribPointer.offset);
          }
          if (prevStencilTest)
            gl.enable(2960);
          if (prevDepthTest)
            gl.enable(2929);
          if (prevCullFace)
            gl.enable(2884);
          if (prevBlend)
            gl.enable(3042);
          gl.bindTexture(3553, prevTextureBinding);
          gl.activeTexture(prevActiveTexture);
          gl.bindBuffer(34962, prevVB);
          gl.useProgram(prevProgram);
        }
        gl.bindFramebuffer(36160, prevFbo);
        if (prevScissorTest)
          gl.enable(3089);
      }, registerContext(ctx, webGLContextAttributes) {
        const handle = GL.getNewId(GL.contexts);
        const context = { handle, attributes: webGLContextAttributes, version: webGLContextAttributes.majorVersion, GLctx: ctx };
        if (ctx.canvas)
          ctx.canvas.GLctxObject = context;
        GL.contexts[handle] = context;
        if (typeof webGLContextAttributes.enableExtensionsByDefault === "undefined" || webGLContextAttributes.enableExtensionsByDefault) {
          GL.initExtensions(context);
        }
        context.maxVertexAttribs = context.GLctx.getParameter(34921);
        context.clientBuffers = [];
        for (let i2 = 0; i2 < context.maxVertexAttribs; i2++) {
          context.clientBuffers[i2] = { enabled: false, clientside: false, size: 0, type: 0, normalized: 0, stride: 0, ptr: 0, vertexAttribPointerAdaptor: null };
        }
        GL.generateTempBuffers(false, context);
        if (webGLContextAttributes.renderViaOffscreenBackBuffer)
          GL.createOffscreenFramebuffer(context);
        return handle;
      }, makeContextCurrent(contextHandle) {
        GL.currentContext = GL.contexts[contextHandle];
        Module.ctx = GLctx = GL.currentContext && GL.currentContext.GLctx;
        return !(contextHandle && !GLctx);
      }, getContext(contextHandle) {
        return GL.contexts[contextHandle];
      }, deleteContext(contextHandle) {
        if (GL.currentContext === GL.contexts[contextHandle])
          GL.currentContext = null;
        if (typeof JSEvents === "object")
          JSEvents.removeAllHandlersOnTarget(GL.contexts[contextHandle].GLctx.canvas);
        if (GL.contexts[contextHandle] && GL.contexts[contextHandle].GLctx.canvas)
          GL.contexts[contextHandle].GLctx.canvas.GLctxObject = void 0;
        GL.contexts[contextHandle] = null;
      }, initExtensions(context) {
        if (!context)
          context = GL.currentContext;
        if (context.initExtensionsDone)
          return;
        context.initExtensionsDone = true;
        const { GLctx: GLctx2 } = context;
        __webgl_enable_ANGLE_instanced_arrays(GLctx2);
        __webgl_enable_OES_vertex_array_object(GLctx2);
        __webgl_enable_WEBGL_draw_buffers(GLctx2);
        __webgl_enable_WEBGL_draw_instanced_base_vertex_base_instance(GLctx2);
        __webgl_enable_WEBGL_multi_draw_instanced_base_vertex_base_instance(GLctx2);
        if (context.version >= 2) {
          GLctx2.disjointTimerQueryExt = GLctx2.getExtension("EXT_disjoint_timer_query_webgl2");
        }
        if (context.version < 2 || !GLctx2.disjointTimerQueryExt) {
          GLctx2.disjointTimerQueryExt = GLctx2.getExtension("EXT_disjoint_timer_query");
        }
        __webgl_enable_WEBGL_multi_draw(GLctx2);
        const exts = GLctx2.getSupportedExtensions() || [];
        exts.forEach((ext) => {
          if (!ext.includes("lose_context") && !ext.includes("debug")) {
            GLctx2.getExtension(ext);
          }
        });
      } };
      var JSEvents = { inEventHandler: 0, removeAllEventListeners() {
        for (let i2 = JSEvents.eventHandlers.length - 1; i2 >= 0; --i2) {
          JSEvents._removeHandler(i2);
        }
        JSEvents.eventHandlers = [];
        JSEvents.deferredCalls = [];
      }, registerRemoveEventListeners() {
        if (!JSEvents.removeEventListenersRegistered) {
          __ATEXIT__.push(JSEvents.removeAllEventListeners);
          JSEvents.removeEventListenersRegistered = true;
        }
      }, deferredCalls: [], deferCall(targetFunction, precedence, argsList) {
        function arraysHaveEqualContent(arrA, arrB) {
          if (arrA.length != arrB.length)
            return false;
          for (const i2 in arrA) {
            if (arrA[i2] != arrB[i2])
              return false;
          }
          return true;
        }
        for (const i2 in JSEvents.deferredCalls) {
          const call = JSEvents.deferredCalls[i2];
          if (call.targetFunction == targetFunction && arraysHaveEqualContent(call.argsList, argsList)) {
            return;
          }
        }
        JSEvents.deferredCalls.push({ targetFunction, precedence, argsList });
        JSEvents.deferredCalls.sort((x, y) => x.precedence < y.precedence);
      }, removeDeferredCalls(targetFunction) {
        for (let i2 = 0; i2 < JSEvents.deferredCalls.length; ++i2) {
          if (JSEvents.deferredCalls[i2].targetFunction == targetFunction) {
            JSEvents.deferredCalls.splice(i2, 1);
            --i2;
          }
        }
      }, canPerformEventHandlerRequests() {
        return JSEvents.inEventHandler && JSEvents.currentEventHandler.allowsDeferredCalls;
      }, runDeferredCalls() {
        if (!JSEvents.canPerformEventHandlerRequests()) {
          return;
        }
        for (let i2 = 0; i2 < JSEvents.deferredCalls.length; ++i2) {
          const call = JSEvents.deferredCalls[i2];
          JSEvents.deferredCalls.splice(i2, 1);
          --i2;
          call.targetFunction.apply(null, call.argsList);
        }
      }, eventHandlers: [], removeAllHandlersOnTarget(target, eventTypeString) {
        for (let i2 = 0; i2 < JSEvents.eventHandlers.length; ++i2) {
          if (JSEvents.eventHandlers[i2].target == target && (!eventTypeString || eventTypeString == JSEvents.eventHandlers[i2].eventTypeString)) {
            JSEvents._removeHandler(i2--);
          }
        }
      }, _removeHandler(i2) {
        const h = JSEvents.eventHandlers[i2];
        h.target.removeEventListener(h.eventTypeString, h.eventListenerFunc, h.useCapture);
        JSEvents.eventHandlers.splice(i2, 1);
      }, registerOrRemoveHandler(eventHandler) {
        const jsEventHandler = function jsEventHandler2(event) {
          ++JSEvents.inEventHandler;
          JSEvents.currentEventHandler = eventHandler;
          JSEvents.runDeferredCalls();
          eventHandler.handlerFunc(event);
          JSEvents.runDeferredCalls();
          --JSEvents.inEventHandler;
        };
        if (eventHandler.callbackfunc) {
          eventHandler.eventListenerFunc = jsEventHandler;
          eventHandler.target.addEventListener(eventHandler.eventTypeString, jsEventHandler, eventHandler.useCapture);
          JSEvents.eventHandlers.push(eventHandler);
          JSEvents.registerRemoveEventListeners();
        } else {
          for (let i2 = 0; i2 < JSEvents.eventHandlers.length; ++i2) {
            if (JSEvents.eventHandlers[i2].target == eventHandler.target && JSEvents.eventHandlers[i2].eventTypeString == eventHandler.eventTypeString) {
              JSEvents._removeHandler(i2--);
            }
          }
        }
      }, getNodeNameForTarget(target) {
        if (!target)
          return "";
        if (target == window)
          return "#window";
        if (target == screen)
          return "#screen";
        return target && target.nodeName ? target.nodeName : "";
      }, fullscreenEnabled() {
        return document.fullscreenEnabled || document.webkitFullscreenEnabled;
      } };
      const __emscripten_webgl_power_preferences = ["default", "low-power", "high-performance"];
      const specialHTMLTargets = [0, typeof document !== "undefined" ? document : 0, typeof window !== "undefined" ? window : 0];
      function findEventTarget(target) {
        warnOnce("Rules for selecting event targets in HTML5 API are changing: instead of using document.getElementById() that only can refer to elements by their DOM ID, new event target selection mechanism uses the more flexible function document.querySelector() that can look up element names, classes, and complex CSS selectors. Build with -s DISABLE_DEPRECATED_FIND_EVENT_TARGET_BEHAVIOR=1 to change to the new lookup rules. See https://github.com/emscripten-core/emscripten/pull/7977 for more details.");
        try {
          if (!target)
            return window;
          if (typeof target === "number")
            target = specialHTMLTargets[target] || UTF8ToString(target);
          if (target === "#window")
            return window;
          else if (target === "#document")
            return document;
          else if (target === "#screen")
            return screen;
          else if (target === "#canvas")
            return Module.canvas;
          return typeof target === "string" ? document.getElementById(target) : target;
        } catch (e) {
          return null;
        }
      }
      function findCanvasEventTarget(target) {
        if (typeof target === "number")
          target = UTF8ToString(target);
        if (!target || target === "#canvas") {
          if (typeof GL !== "undefined" && GL.offscreenCanvases.canvas)
            return GL.offscreenCanvases.canvas;
          return Module.canvas;
        }
        if (typeof GL !== "undefined" && GL.offscreenCanvases[target])
          return GL.offscreenCanvases[target];
        return findEventTarget(target);
      }
      function _emscripten_webgl_do_create_context(target, attributes) {
        assert(attributes);
        const a = attributes >> 2;
        const powerPreference = HEAP32[a + (24 >> 2)];
        const contextAttributes = { alpha: !!HEAP32[a + (0 >> 2)], depth: !!HEAP32[a + (4 >> 2)], stencil: !!HEAP32[a + (8 >> 2)], antialias: !!HEAP32[a + (12 >> 2)], premultipliedAlpha: !!HEAP32[a + (16 >> 2)], preserveDrawingBuffer: !!HEAP32[a + (20 >> 2)], powerPreference: __emscripten_webgl_power_preferences[powerPreference], failIfMajorPerformanceCaveat: !!HEAP32[a + (28 >> 2)], majorVersion: HEAP32[a + (32 >> 2)], minorVersion: HEAP32[a + (36 >> 2)], enableExtensionsByDefault: HEAP32[a + (40 >> 2)], explicitSwapControl: HEAP32[a + (44 >> 2)], proxyContextToMainThread: HEAP32[a + (48 >> 2)], renderViaOffscreenBackBuffer: HEAP32[a + (52 >> 2)] };
        let canvas = findCanvasEventTarget(target);
        if (!canvas) {
          return 0;
        }
        if (canvas.offscreenCanvas)
          canvas = canvas.offscreenCanvas;
        if (contextAttributes.explicitSwapControl) {
          const supportsOffscreenCanvas = canvas.transferControlToOffscreen || typeof OffscreenCanvas !== "undefined" && canvas instanceof OffscreenCanvas;
          if (!supportsOffscreenCanvas) {
            if (!contextAttributes.renderViaOffscreenBackBuffer) {
              contextAttributes.renderViaOffscreenBackBuffer = true;
            }
          }
          if (canvas.transferControlToOffscreen) {
            if (!canvas.controlTransferredOffscreen) {
              GL.offscreenCanvases[canvas.id] = { canvas: canvas.transferControlToOffscreen(), canvasSharedPtr: _malloc(12), id: canvas.id };
              canvas.controlTransferredOffscreen = true;
            } else if (!GL.offscreenCanvases[canvas.id]) {
              return 0;
            }
            canvas = GL.offscreenCanvases[canvas.id];
          }
        }
        const contextHandle = GL.createContext(canvas, contextAttributes);
        return contextHandle;
      }
      function _emscripten_webgl_create_context(a0, a1) {
        return _emscripten_webgl_do_create_context(a0, a1);
      }
      function _emscripten_webgl_do_get_current_context() {
        return GL.currentContext ? GL.currentContext.handle : 0;
      }
      function _emscripten_webgl_get_current_context() {
        return _emscripten_webgl_do_get_current_context();
      }
      Module._emscripten_webgl_get_current_context = _emscripten_webgl_get_current_context;
      function _emscripten_webgl_make_context_current(contextHandle) {
        const success = GL.makeContextCurrent(contextHandle);
        return success ? 0 : -5;
      }
      Module._emscripten_webgl_make_context_current = _emscripten_webgl_make_context_current;
      function _emscripten_webgl_destroy_context(contextHandle) {
        if (GL.currentContext == contextHandle)
          GL.currentContext = 0;
        GL.deleteContext(contextHandle);
      }
      function _emscripten_webgl_get_context_attributes(c, a) {
        if (!a)
          return -5;
        c = GL.contexts[c];
        if (!c)
          return -3;
        let t = c.GLctx;
        if (!t)
          return -3;
        t = t.getContextAttributes();
        HEAP32[a >> 2] = t.alpha;
        HEAP32[a + 4 >> 2] = t.depth;
        HEAP32[a + 8 >> 2] = t.stencil;
        HEAP32[a + 12 >> 2] = t.antialias;
        HEAP32[a + 16 >> 2] = t.premultipliedAlpha;
        HEAP32[a + 20 >> 2] = t.preserveDrawingBuffer;
        const power = t.powerPreference && __emscripten_webgl_power_preferences.indexOf(t.powerPreference);
        HEAP32[a + 24 >> 2] = power;
        HEAP32[a + 28 >> 2] = t.failIfMajorPerformanceCaveat;
        HEAP32[a + 32 >> 2] = c.version;
        HEAP32[a + 36 >> 2] = 0;
        HEAP32[a + 40 >> 2] = c.attributes.enableExtensionsByDefault;
        return 0;
      }
      function _emscripten_webgl_init_context_attributes(attributes) {
        assert(attributes);
        const a = attributes >> 2;
        for (let i2 = 0; i2 < 56 >> 2; ++i2) {
          HEAP32[a + i2] = 0;
        }
        HEAP32[a + (0 >> 2)] = HEAP32[a + (4 >> 2)] = HEAP32[a + (12 >> 2)] = HEAP32[a + (16 >> 2)] = HEAP32[a + (32 >> 2)] = HEAP32[a + (40 >> 2)] = 1;
      }
      const ENV = {};
      function getExecutableName() {
        return thisProgram || "./this.program";
      }
      function getEnvStrings() {
        if (!getEnvStrings.strings) {
          const lang = `${(typeof navigator === "object" && navigator.languages && navigator.languages[0] || "C").replace("-", "_")}.UTF-8`;
          const env = { USER: "web_user", LOGNAME: "web_user", PATH: "/", PWD: "/", HOME: "/home/<USER>", LANG: lang, _: getExecutableName() };
          for (var x in ENV) {
            if (ENV[x] === void 0)
              delete env[x];
            else
              env[x] = ENV[x];
          }
          const strings = [];
          for (var x in env) {
            strings.push(`${x}=${env[x]}`);
          }
          getEnvStrings.strings = strings;
        }
        return getEnvStrings.strings;
      }
      function _environ_get(__environ, environ_buf) {
        let bufSize = 0;
        getEnvStrings().forEach((string, i2) => {
          const ptr = environ_buf + bufSize;
          HEAP32[__environ + i2 * 4 >> 2] = ptr;
          writeAsciiToMemory(string, ptr);
          bufSize += string.length + 1;
        });
        return 0;
      }
      function _environ_sizes_get(penviron_count, penviron_buf_size) {
        const strings = getEnvStrings();
        HEAP32[penviron_count >> 2] = strings.length;
        let bufSize = 0;
        strings.forEach((string) => {
          bufSize += string.length + 1;
        });
        HEAP32[penviron_buf_size >> 2] = bufSize;
        return 0;
      }
      function _fd_close(fd) {
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          FS.close(stream);
          return 0;
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return e.errno;
        }
      }
      function _fd_read(fd, iov, iovcnt, pnum) {
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          const num = SYSCALLS.doReadv(stream, iov, iovcnt);
          HEAP32[pnum >> 2] = num;
          return 0;
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return e.errno;
        }
      }
      function _fd_seek(fd, offset_low, offset_high, whence, newOffset) {
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          const HIGH_OFFSET = 4294967296;
          const offset = offset_high * HIGH_OFFSET + (offset_low >>> 0);
          const DOUBLE_LIMIT = 9007199254740992;
          if (offset <= -DOUBLE_LIMIT || offset >= DOUBLE_LIMIT) {
            return -61;
          }
          FS.llseek(stream, offset, whence);
          tempI64 = [stream.position >>> 0, (tempDouble = stream.position, +Math.abs(tempDouble) >= 1 ? tempDouble > 0 ? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>> 0 : ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>> 0 : 0)], HEAP32[newOffset >> 2] = tempI64[0], HEAP32[newOffset + 4 >> 2] = tempI64[1];
          if (stream.getdents && offset === 0 && whence === 0)
            stream.getdents = null;
          return 0;
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return e.errno;
        }
      }
      function _fd_write(fd, iov, iovcnt, pnum) {
        try {
          const stream = SYSCALLS.getStreamFromFD(fd);
          const num = SYSCALLS.doWritev(stream, iov, iovcnt);
          HEAP32[pnum >> 2] = num;
          return 0;
        } catch (e) {
          if (typeof FS === "undefined" || !(e instanceof FS.ErrnoError))
            abort(e);
          return e.errno;
        }
      }
      function _getTempRet0() {
        return getTempRet0();
      }
      function _getentropy(buffer2, size) {
        if (!_getentropy.randomDevice) {
          _getentropy.randomDevice = getRandomDevice();
        }
        for (let i2 = 0; i2 < size; i2++) {
          HEAP8[buffer2 + i2 >> 0] = _getentropy.randomDevice();
        }
        return 0;
      }
      function _glActiveTexture(x0) {
        GLctx.activeTexture(x0);
      }
      function _glAttachShader(program, shader) {
        GLctx.attachShader(GL.programs[program], GL.shaders[shader]);
      }
      function _glBindAttribLocation(program, index, name) {
        GLctx.bindAttribLocation(GL.programs[program], index, UTF8ToString(name));
      }
      function _glBindBuffer(target, buffer2) {
        if (target == 34962) {
          GLctx.currentArrayBufferBinding = buffer2;
        } else if (target == 34963) {
          GLctx.currentElementArrayBufferBinding = buffer2;
        }
        if (target == 35051) {
          GLctx.currentPixelPackBufferBinding = buffer2;
        } else if (target == 35052) {
          GLctx.currentPixelUnpackBufferBinding = buffer2;
        }
        GLctx.bindBuffer(target, GL.buffers[buffer2]);
      }
      function _glBindBufferBase(target, index, buffer2) {
        GLctx.bindBufferBase(target, index, GL.buffers[buffer2]);
      }
      function _glBindFramebuffer(target, framebuffer) {
        GLctx.bindFramebuffer(target, framebuffer ? GL.framebuffers[framebuffer] : GL.currentContext.defaultFbo);
      }
      function _glBindTexture(target, texture) {
        GLctx.bindTexture(target, GL.textures[texture]);
      }
      function _glBindVertexArray(vao) {
        GLctx.bindVertexArray(GL.vaos[vao]);
        const ibo = GLctx.getParameter(34965);
        GLctx.currentElementArrayBufferBinding = ibo ? ibo.name | 0 : 0;
      }
      function _glBufferData(target, size, data, usage) {
        if (GL.currentContext.version >= 2) {
          if (data) {
            GLctx.bufferData(target, HEAPU8, usage, data, size);
          } else {
            GLctx.bufferData(target, size, usage);
          }
        } else {
          GLctx.bufferData(target, data ? HEAPU8.subarray(data, data + size) : size, usage);
        }
      }
      function convertI32PairToI53(lo, hi) {
        assert(hi === (hi | 0));
        return (lo >>> 0) + hi * 4294967296;
      }
      function _glClientWaitSync(sync, flags, timeoutLo, timeoutHi) {
        return GLctx.clientWaitSync(GL.syncs[sync], flags, convertI32PairToI53(timeoutLo, timeoutHi));
      }
      function _glCompileShader(shader) {
        GLctx.compileShader(GL.shaders[shader]);
      }
      function _glCreateProgram() {
        const id = GL.getNewId(GL.programs);
        const program = GLctx.createProgram();
        program.name = id;
        program.maxUniformLength = program.maxAttributeLength = program.maxUniformBlockNameLength = 0;
        program.uniformIdCounter = 1;
        GL.programs[id] = program;
        return id;
      }
      function _glCreateShader(shaderType) {
        const id = GL.getNewId(GL.shaders);
        GL.shaders[id] = GLctx.createShader(shaderType);
        return id;
      }
      function _glDeleteBuffers(n, buffers) {
        for (let i2 = 0; i2 < n; i2++) {
          const id = HEAP32[buffers + i2 * 4 >> 2];
          const buffer2 = GL.buffers[id];
          if (!buffer2)
            continue;
          GLctx.deleteBuffer(buffer2);
          buffer2.name = 0;
          GL.buffers[id] = null;
          if (id == GLctx.currentArrayBufferBinding)
            GLctx.currentArrayBufferBinding = 0;
          if (id == GLctx.currentElementArrayBufferBinding)
            GLctx.currentElementArrayBufferBinding = 0;
          if (id == GLctx.currentPixelPackBufferBinding)
            GLctx.currentPixelPackBufferBinding = 0;
          if (id == GLctx.currentPixelUnpackBufferBinding)
            GLctx.currentPixelUnpackBufferBinding = 0;
        }
      }
      function _glDeleteFramebuffers(n, framebuffers) {
        for (let i2 = 0; i2 < n; ++i2) {
          const id = HEAP32[framebuffers + i2 * 4 >> 2];
          const framebuffer = GL.framebuffers[id];
          if (!framebuffer)
            continue;
          GLctx.deleteFramebuffer(framebuffer);
          framebuffer.name = 0;
          GL.framebuffers[id] = null;
        }
      }
      function _glDeleteProgram(id) {
        if (!id)
          return;
        const program = GL.programs[id];
        if (!program) {
          GL.recordError(1281);
          return;
        }
        GLctx.deleteProgram(program);
        program.name = 0;
        GL.programs[id] = null;
      }
      function _glDeleteShader(id) {
        if (!id)
          return;
        const shader = GL.shaders[id];
        if (!shader) {
          GL.recordError(1281);
          return;
        }
        GLctx.deleteShader(shader);
        GL.shaders[id] = null;
      }
      function _glDeleteSync(id) {
        if (!id)
          return;
        const sync = GL.syncs[id];
        if (!sync) {
          GL.recordError(1281);
          return;
        }
        GLctx.deleteSync(sync);
        sync.name = 0;
        GL.syncs[id] = null;
      }
      function _glDeleteTextures(n, textures) {
        for (let i2 = 0; i2 < n; i2++) {
          const id = HEAP32[textures + i2 * 4 >> 2];
          const texture = GL.textures[id];
          if (!texture)
            continue;
          GLctx.deleteTexture(texture);
          texture.name = 0;
          GL.textures[id] = null;
        }
      }
      function _glDeleteVertexArrays(n, vaos) {
        for (let i2 = 0; i2 < n; i2++) {
          const id = HEAP32[vaos + i2 * 4 >> 2];
          GLctx.deleteVertexArray(GL.vaos[id]);
          GL.vaos[id] = null;
        }
      }
      function _glDisable(x0) {
        GLctx.disable(x0);
      }
      function _glDisableVertexAttribArray(index) {
        const cb = GL.currentContext.clientBuffers[index];
        cb.enabled = false;
        GLctx.disableVertexAttribArray(index);
      }
      function _glDrawArrays(mode, first, count) {
        GL.preDrawHandleClientVertexAttribBindings(first + count);
        GLctx.drawArrays(mode, first, count);
        GL.postDrawHandleClientVertexAttribBindings();
      }
      const tempFixedLengthArray = [];
      function _glDrawBuffers(n, bufs) {
        const bufArray = tempFixedLengthArray[n];
        for (let i2 = 0; i2 < n; i2++) {
          bufArray[i2] = HEAP32[bufs + i2 * 4 >> 2];
        }
        GLctx.drawBuffers(bufArray);
      }
      function _glEnableVertexAttribArray(index) {
        const cb = GL.currentContext.clientBuffers[index];
        cb.enabled = true;
        GLctx.enableVertexAttribArray(index);
      }
      function _glFenceSync(condition, flags) {
        const sync = GLctx.fenceSync(condition, flags);
        if (sync) {
          const id = GL.getNewId(GL.syncs);
          sync.name = id;
          GL.syncs[id] = sync;
          return id;
        }
        return 0;
      }
      function _glFinish() {
        GLctx.finish();
      }
      function _glFlush() {
        GLctx.flush();
      }
      function _glFramebufferTexture2D(target, attachment, textarget, texture, level) {
        GLctx.framebufferTexture2D(target, attachment, textarget, GL.textures[texture], level);
      }
      function _glFramebufferTextureLayer(target, attachment, texture, level, layer) {
        GLctx.framebufferTextureLayer(target, attachment, GL.textures[texture], level, layer);
      }
      function __glGenObject(n, buffers, createFunction, objectTable) {
        for (let i2 = 0; i2 < n; i2++) {
          const buffer2 = GLctx[createFunction]();
          const id = buffer2 && GL.getNewId(objectTable);
          if (buffer2) {
            buffer2.name = id;
            objectTable[id] = buffer2;
          } else {
            GL.recordError(1282);
          }
          HEAP32[buffers + i2 * 4 >> 2] = id;
        }
      }
      function _glGenBuffers(n, buffers) {
        __glGenObject(n, buffers, "createBuffer", GL.buffers);
      }
      function _glGenFramebuffers(n, ids) {
        __glGenObject(n, ids, "createFramebuffer", GL.framebuffers);
      }
      function _glGenTextures(n, textures) {
        __glGenObject(n, textures, "createTexture", GL.textures);
      }
      function _glGenVertexArrays(n, arrays) {
        __glGenObject(n, arrays, "createVertexArray", GL.vaos);
      }
      function _glGetAttribLocation(program, name) {
        return GLctx.getAttribLocation(GL.programs[program], UTF8ToString(name));
      }
      function _glGetError() {
        const error = GLctx.getError() || GL.lastError;
        GL.lastError = 0;
        return error;
      }
      function readI53FromI64(ptr) {
        return HEAPU32[ptr >> 2] + HEAP32[ptr + 4 >> 2] * 4294967296;
      }
      function readI53FromU64(ptr) {
        return HEAPU32[ptr >> 2] + HEAPU32[ptr + 4 >> 2] * 4294967296;
      }
      function writeI53ToI64(ptr, num) {
        HEAPU32[ptr >> 2] = num;
        HEAPU32[ptr + 4 >> 2] = (num - HEAPU32[ptr >> 2]) / 4294967296;
        const deserialized = num >= 0 ? readI53FromU64(ptr) : readI53FromI64(ptr);
        if (deserialized != num)
          warnOnce(`writeI53ToI64() out of range: serialized JS Number ${num} to Wasm heap as bytes lo=0x${HEAPU32[ptr >> 2].toString(16)}, hi=0x${HEAPU32[ptr + 4 >> 2].toString(16)}, which deserializes back to ${deserialized} instead!`);
      }
      function emscriptenWebGLGet(name_, p, type) {
        if (!p) {
          GL.recordError(1281);
          return;
        }
        let ret = void 0;
        switch (name_) {
          case 36346:
            ret = 1;
            break;
          case 36344:
            if (type != 0 && type != 1) {
              GL.recordError(1280);
            }
            return;
          case 34814:
          case 36345:
            ret = 0;
            break;
          case 34466:
            var formats = GLctx.getParameter(34467);
            ret = formats ? formats.length : 0;
            break;
          case 33309:
            if (GL.currentContext.version < 2) {
              GL.recordError(1282);
              return;
            }
            var exts = GLctx.getSupportedExtensions() || [];
            ret = 2 * exts.length;
            break;
          case 33307:
          case 33308:
            if (GL.currentContext.version < 2) {
              GL.recordError(1280);
              return;
            }
            ret = name_ == 33307 ? 3 : 0;
            break;
        }
        if (ret === void 0) {
          const result = GLctx.getParameter(name_);
          switch (typeof result) {
            case "number":
              ret = result;
              break;
            case "boolean":
              ret = result ? 1 : 0;
              break;
            case "string":
              GL.recordError(1280);
              return;
            case "object":
              if (result === null) {
                switch (name_) {
                  case 34964:
                  case 35725:
                  case 34965:
                  case 36006:
                  case 36007:
                  case 32873:
                  case 34229:
                  case 36662:
                  case 36663:
                  case 35053:
                  case 35055:
                  case 36010:
                  case 35097:
                  case 35869:
                  case 32874:
                  case 36389:
                  case 35983:
                  case 35368:
                  case 34068: {
                    ret = 0;
                    break;
                  }
                  default: {
                    GL.recordError(1280);
                    return;
                  }
                }
              } else if (result instanceof Float32Array || result instanceof Uint32Array || result instanceof Int32Array || result instanceof Array) {
                for (let i2 = 0; i2 < result.length; ++i2) {
                  switch (type) {
                    case 0:
                      HEAP32[p + i2 * 4 >> 2] = result[i2];
                      break;
                    case 2:
                      HEAPF32[p + i2 * 4 >> 2] = result[i2];
                      break;
                    case 4:
                      HEAP8[p + i2 >> 0] = result[i2] ? 1 : 0;
                      break;
                  }
                }
                return;
              } else {
                try {
                  ret = result.name | 0;
                } catch (e) {
                  GL.recordError(1280);
                  err(`GL_INVALID_ENUM in glGet${type}v: Unknown object returned from WebGL getParameter(${name_})! (error: ${e})`);
                  return;
                }
              }
              break;
            default:
              GL.recordError(1280);
              err(`GL_INVALID_ENUM in glGet${type}v: Native code calling glGet${type}v(${name_}) and it returns ${result} of type ${typeof result}!`);
              return;
          }
        }
        switch (type) {
          case 1:
            writeI53ToI64(p, ret);
            break;
          case 0:
            HEAP32[p >> 2] = ret;
            break;
          case 2:
            HEAPF32[p >> 2] = ret;
            break;
          case 4:
            HEAP8[p >> 0] = ret ? 1 : 0;
            break;
        }
      }
      function _glGetIntegerv(name_, p) {
        emscriptenWebGLGet(name_, p, 0);
      }
      function stringToNewUTF8(jsString) {
        const length = lengthBytesUTF8(jsString) + 1;
        const cString = _malloc(length);
        stringToUTF8(jsString, cString, length);
        return cString;
      }
      function _glGetString(name_) {
        let ret = GL.stringCache[name_];
        if (!ret) {
          switch (name_) {
            case 7939:
              var exts = GLctx.getSupportedExtensions() || [];
              exts = exts.concat(exts.map((e) => `GL_${e}`));
              ret = stringToNewUTF8(exts.join(" "));
              break;
            case 7936:
            case 7937:
            case 37445:
            case 37446:
              var s = GLctx.getParameter(name_);
              if (!s) {
                GL.recordError(1280);
              }
              ret = s && stringToNewUTF8(s);
              break;
            case 7938:
              var glVersion = GLctx.getParameter(7938);
              if (GL.currentContext.version >= 2)
                glVersion = `OpenGL ES 3.0 (${glVersion})`;
              else {
                glVersion = `OpenGL ES 2.0 (${glVersion})`;
              }
              ret = stringToNewUTF8(glVersion);
              break;
            case 35724:
              var glslVersion = GLctx.getParameter(35724);
              var ver_re = /^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/;
              var ver_num = glslVersion.match(ver_re);
              if (ver_num !== null) {
                if (ver_num[1].length == 3)
                  ver_num[1] = `${ver_num[1]}0`;
                glslVersion = `OpenGL ES GLSL ES ${ver_num[1]} (${glslVersion})`;
              }
              ret = stringToNewUTF8(glslVersion);
              break;
            default:
              GL.recordError(1280);
          }
          GL.stringCache[name_] = ret;
        }
        return ret;
      }
      function _glGetUniformBlockIndex(program, uniformBlockName) {
        return GLctx.getUniformBlockIndex(GL.programs[program], UTF8ToString(uniformBlockName));
      }
      function jstoi_q(str) {
        return parseInt(str);
      }
      function webglGetLeftBracePos(name) {
        return name.slice(-1) == "]" && name.lastIndexOf("[");
      }
      function webglPrepareUniformLocationsBeforeFirstUse(program) {
        let { uniformLocsById } = program;
        const { uniformSizeAndIdsByName } = program;
        let i2;
        let j;
        if (!uniformLocsById) {
          program.uniformLocsById = uniformLocsById = {};
          program.uniformArrayNamesById = {};
          for (i2 = 0; i2 < GLctx.getProgramParameter(program, 35718); ++i2) {
            const u = GLctx.getActiveUniform(program, i2);
            const nm = u.name;
            const sz = u.size;
            const lb = webglGetLeftBracePos(nm);
            const arrayName = lb > 0 ? nm.slice(0, lb) : nm;
            let id = program.uniformIdCounter;
            program.uniformIdCounter += sz;
            uniformSizeAndIdsByName[arrayName] = [sz, id];
            for (j = 0; j < sz; ++j) {
              uniformLocsById[id] = j;
              program.uniformArrayNamesById[id++] = arrayName;
            }
          }
        }
      }
      function _glGetUniformLocation(program, name) {
        name = UTF8ToString(name);
        if (program = GL.programs[program]) {
          webglPrepareUniformLocationsBeforeFirstUse(program);
          const { uniformLocsById } = program;
          let arrayIndex = 0;
          let uniformBaseName = name;
          const leftBrace = webglGetLeftBracePos(name);
          if (leftBrace > 0) {
            arrayIndex = jstoi_q(name.slice(leftBrace + 1)) >>> 0;
            uniformBaseName = name.slice(0, leftBrace);
          }
          const sizeAndId = program.uniformSizeAndIdsByName[uniformBaseName];
          if (sizeAndId && arrayIndex < sizeAndId[0]) {
            arrayIndex += sizeAndId[1];
            if (uniformLocsById[arrayIndex] = uniformLocsById[arrayIndex] || GLctx.getUniformLocation(program, name)) {
              return arrayIndex;
            }
          }
        } else {
          GL.recordError(1281);
        }
        return -1;
      }
      function _glLinkProgram(program) {
        program = GL.programs[program];
        GLctx.linkProgram(program);
        program.uniformLocsById = 0;
        program.uniformSizeAndIdsByName = {};
      }
      function _glPixelStorei(pname, param) {
        if (pname == 3317) {
          GL.unpackAlignment = param;
        }
        GLctx.pixelStorei(pname, param);
      }
      function computeUnpackAlignedImageSize(width, height, sizePerPixel, alignment) {
        function roundedToNextMultipleOf(x, y) {
          return x + y - 1 & -y;
        }
        const plainRowSize = width * sizePerPixel;
        const alignedRowSize = roundedToNextMultipleOf(plainRowSize, alignment);
        return height * alignedRowSize;
      }
      function __colorChannelsInGlTextureFormat(format) {
        const colorChannels = { 5: 3, 6: 4, 8: 2, 29502: 3, 29504: 4, 26917: 2, 26918: 2, 29846: 3, 29847: 4 };
        return colorChannels[format - 6402] || 1;
      }
      function heapObjectForWebGLType(type) {
        type -= 5120;
        if (type == 0)
          return HEAP8;
        if (type == 1)
          return HEAPU8;
        if (type == 2)
          return HEAP16;
        if (type == 4)
          return HEAP32;
        if (type == 6)
          return HEAPF32;
        if (type == 5 || type == 28922 || type == 28520 || type == 30779 || type == 30782)
          return HEAPU32;
        return HEAPU16;
      }
      function heapAccessShiftForWebGLHeap(heap) {
        return 31 - Math.clz32(heap.BYTES_PER_ELEMENT);
      }
      function emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, internalFormat) {
        const heap = heapObjectForWebGLType(type);
        const shift = heapAccessShiftForWebGLHeap(heap);
        const byteSize = 1 << shift;
        const sizePerPixel = __colorChannelsInGlTextureFormat(format) * byteSize;
        const bytes = computeUnpackAlignedImageSize(width, height, sizePerPixel, GL.unpackAlignment);
        return heap.subarray(pixels >> shift, pixels + bytes >> shift);
      }
      function _glReadPixels(x, y, width, height, format, type, pixels) {
        if (GL.currentContext.version >= 2) {
          if (GLctx.currentPixelPackBufferBinding) {
            GLctx.readPixels(x, y, width, height, format, type, pixels);
          } else {
            const heap = heapObjectForWebGLType(type);
            GLctx.readPixels(x, y, width, height, format, type, heap, pixels >> heapAccessShiftForWebGLHeap(heap));
          }
          return;
        }
        const pixelData = emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, format);
        if (!pixelData) {
          GL.recordError(1280);
          return;
        }
        GLctx.readPixels(x, y, width, height, format, type, pixelData);
      }
      function _glShaderSource(shader, count, string, length) {
        const source = GL.getSource(shader, count, string, length);
        GLctx.shaderSource(GL.shaders[shader], source);
      }
      function _glTexImage2D(target, level, internalFormat, width, height, border, format, type, pixels) {
        if (GL.currentContext.version >= 2) {
          if (GLctx.currentPixelUnpackBufferBinding) {
            GLctx.texImage2D(target, level, internalFormat, width, height, border, format, type, pixels);
          } else if (pixels) {
            const heap = heapObjectForWebGLType(type);
            GLctx.texImage2D(target, level, internalFormat, width, height, border, format, type, heap, pixels >> heapAccessShiftForWebGLHeap(heap));
          } else {
            GLctx.texImage2D(target, level, internalFormat, width, height, border, format, type, null);
          }
          return;
        }
        GLctx.texImage2D(target, level, internalFormat, width, height, border, format, type, pixels ? emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, internalFormat) : null);
      }
      function _glTexParameterfv(target, pname, params) {
        const param = HEAPF32[params >> 2];
        GLctx.texParameterf(target, pname, param);
      }
      function _glTexParameteri(x0, x1, x2) {
        GLctx.texParameteri(x0, x1, x2);
      }
      function _glTexStorage2D(x0, x1, x2, x3, x4) {
        GLctx.texStorage2D(x0, x1, x2, x3, x4);
      }
      function _glTexStorage3D(x0, x1, x2, x3, x4, x5) {
        GLctx.texStorage3D(x0, x1, x2, x3, x4, x5);
      }
      function _glTexSubImage2D(target, level, xoffset, yoffset, width, height, format, type, pixels) {
        if (GL.currentContext.version >= 2) {
          if (GLctx.currentPixelUnpackBufferBinding) {
            GLctx.texSubImage2D(target, level, xoffset, yoffset, width, height, format, type, pixels);
          } else if (pixels) {
            const heap = heapObjectForWebGLType(type);
            GLctx.texSubImage2D(target, level, xoffset, yoffset, width, height, format, type, heap, pixels >> heapAccessShiftForWebGLHeap(heap));
          } else {
            GLctx.texSubImage2D(target, level, xoffset, yoffset, width, height, format, type, null);
          }
          return;
        }
        let pixelData = null;
        if (pixels)
          pixelData = emscriptenWebGLGetTexPixelData(type, format, width, height, pixels, 0);
        GLctx.texSubImage2D(target, level, xoffset, yoffset, width, height, format, type, pixelData);
      }
      function _glTexSubImage3D(target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, pixels) {
        if (GLctx.currentPixelUnpackBufferBinding) {
          GLctx.texSubImage3D(target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, pixels);
        } else if (pixels) {
          const heap = heapObjectForWebGLType(type);
          GLctx.texSubImage3D(target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, heap, pixels >> heapAccessShiftForWebGLHeap(heap));
        } else {
          GLctx.texSubImage3D(target, level, xoffset, yoffset, zoffset, width, height, depth, format, type, null);
        }
      }
      function webglGetUniformLocation(location) {
        const p = GLctx.currentProgram;
        if (p) {
          let webglLoc = p.uniformLocsById[location];
          if (typeof webglLoc === "number") {
            p.uniformLocsById[location] = webglLoc = GLctx.getUniformLocation(p, p.uniformArrayNamesById[location] + (webglLoc > 0 ? `[${webglLoc}]` : ""));
          }
          return webglLoc;
        }
        GL.recordError(1282);
      }
      function _glUniform1f(location, v0) {
        GLctx.uniform1f(webglGetUniformLocation(location), v0);
      }
      function _glUniform1i(location, v0) {
        GLctx.uniform1i(webglGetUniformLocation(location), v0);
      }
      const miniTempWebGLFloatBuffers = [];
      function _glUniform2fv(location, count, value) {
        if (GL.currentContext.version >= 2) {
          GLctx.uniform2fv(webglGetUniformLocation(location), HEAPF32, value >> 2, count * 2);
          return;
        }
        if (count <= 144) {
          var view = miniTempWebGLFloatBuffers[2 * count - 1];
          for (let i2 = 0; i2 < 2 * count; i2 += 2) {
            view[i2] = HEAPF32[value + 4 * i2 >> 2];
            view[i2 + 1] = HEAPF32[value + (4 * i2 + 4) >> 2];
          }
        } else {
          var view = HEAPF32.subarray(value >> 2, value + count * 8 >> 2);
        }
        GLctx.uniform2fv(webglGetUniformLocation(location), view);
      }
      function _glUniform4fv(location, count, value) {
        if (GL.currentContext.version >= 2) {
          GLctx.uniform4fv(webglGetUniformLocation(location), HEAPF32, value >> 2, count * 4);
          return;
        }
        if (count <= 72) {
          var view = miniTempWebGLFloatBuffers[4 * count - 1];
          const heap = HEAPF32;
          value >>= 2;
          for (let i2 = 0; i2 < 4 * count; i2 += 4) {
            const dst = value + i2;
            view[i2] = heap[dst];
            view[i2 + 1] = heap[dst + 1];
            view[i2 + 2] = heap[dst + 2];
            view[i2 + 3] = heap[dst + 3];
          }
        } else {
          var view = HEAPF32.subarray(value >> 2, value + count * 16 >> 2);
        }
        GLctx.uniform4fv(webglGetUniformLocation(location), view);
      }
      const __miniTempWebGLIntBuffers = [];
      function _glUniform4iv(location, count, value) {
        if (GL.currentContext.version >= 2) {
          GLctx.uniform4iv(webglGetUniformLocation(location), HEAP32, value >> 2, count * 4);
          return;
        }
        if (count <= 72) {
          var view = __miniTempWebGLIntBuffers[4 * count - 1];
          for (let i2 = 0; i2 < 4 * count; i2 += 4) {
            view[i2] = HEAP32[value + 4 * i2 >> 2];
            view[i2 + 1] = HEAP32[value + (4 * i2 + 4) >> 2];
            view[i2 + 2] = HEAP32[value + (4 * i2 + 8) >> 2];
            view[i2 + 3] = HEAP32[value + (4 * i2 + 12) >> 2];
          }
        } else {
          var view = HEAP32.subarray(value >> 2, value + count * 16 >> 2);
        }
        GLctx.uniform4iv(webglGetUniformLocation(location), view);
      }
      function _glUniformBlockBinding(program, uniformBlockIndex, uniformBlockBinding) {
        program = GL.programs[program];
        GLctx.uniformBlockBinding(program, uniformBlockIndex, uniformBlockBinding);
      }
      function _glUniformMatrix4fv(location, count, transpose, value) {
        if (GL.currentContext.version >= 2) {
          GLctx.uniformMatrix4fv(webglGetUniformLocation(location), !!transpose, HEAPF32, value >> 2, count * 16);
          return;
        }
        if (count <= 18) {
          var view = miniTempWebGLFloatBuffers[16 * count - 1];
          const heap = HEAPF32;
          value >>= 2;
          for (let i2 = 0; i2 < 16 * count; i2 += 16) {
            const dst = value + i2;
            view[i2] = heap[dst];
            view[i2 + 1] = heap[dst + 1];
            view[i2 + 2] = heap[dst + 2];
            view[i2 + 3] = heap[dst + 3];
            view[i2 + 4] = heap[dst + 4];
            view[i2 + 5] = heap[dst + 5];
            view[i2 + 6] = heap[dst + 6];
            view[i2 + 7] = heap[dst + 7];
            view[i2 + 8] = heap[dst + 8];
            view[i2 + 9] = heap[dst + 9];
            view[i2 + 10] = heap[dst + 10];
            view[i2 + 11] = heap[dst + 11];
            view[i2 + 12] = heap[dst + 12];
            view[i2 + 13] = heap[dst + 13];
            view[i2 + 14] = heap[dst + 14];
            view[i2 + 15] = heap[dst + 15];
          }
        } else {
          var view = HEAPF32.subarray(value >> 2, value + count * 64 >> 2);
        }
        GLctx.uniformMatrix4fv(webglGetUniformLocation(location), !!transpose, view);
      }
      function _glUseProgram(program) {
        program = GL.programs[program];
        GLctx.useProgram(program);
        GLctx.currentProgram = program;
      }
      function _glVertexAttribPointer(index, size, type, normalized, stride, ptr) {
        const cb = GL.currentContext.clientBuffers[index];
        if (!GLctx.currentArrayBufferBinding) {
          cb.size = size;
          cb.type = type;
          cb.normalized = normalized;
          cb.stride = stride;
          cb.ptr = ptr;
          cb.clientside = true;
          cb.vertexAttribPointerAdaptor = function(index2, size2, type2, normalized2, stride2, ptr2) {
            this.vertexAttribPointer(index2, size2, type2, normalized2, stride2, ptr2);
          };
          return;
        }
        cb.clientside = false;
        GLctx.vertexAttribPointer(index, size, type, !!normalized, stride, ptr);
      }
      function _glViewport(x0, x1, x2, x3) {
        GLctx.viewport(x0, x1, x2, x3);
      }
      function _gmtime_r(time, tmPtr) {
        const date = new Date(HEAP32[time >> 2] * 1e3);
        HEAP32[tmPtr >> 2] = date.getUTCSeconds();
        HEAP32[tmPtr + 4 >> 2] = date.getUTCMinutes();
        HEAP32[tmPtr + 8 >> 2] = date.getUTCHours();
        HEAP32[tmPtr + 12 >> 2] = date.getUTCDate();
        HEAP32[tmPtr + 16 >> 2] = date.getUTCMonth();
        HEAP32[tmPtr + 20 >> 2] = date.getUTCFullYear() - 1900;
        HEAP32[tmPtr + 24 >> 2] = date.getUTCDay();
        HEAP32[tmPtr + 36 >> 2] = 0;
        HEAP32[tmPtr + 32 >> 2] = 0;
        const start = Date.UTC(date.getUTCFullYear(), 0, 1, 0, 0, 0, 0);
        const yday = (date.getTime() - start) / (1e3 * 60 * 60 * 24) | 0;
        HEAP32[tmPtr + 28 >> 2] = yday;
        if (!_gmtime_r.GMTString)
          _gmtime_r.GMTString = allocateUTF8("GMT");
        HEAP32[tmPtr + 40 >> 2] = _gmtime_r.GMTString;
        return tmPtr;
      }
      function _llvm_eh_typeid_for(type) {
        return type;
      }
      function _tzset() {
        if (_tzset.called)
          return;
        _tzset.called = true;
        const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
        const winter = new Date(currentYear, 0, 1);
        const summer = new Date(currentYear, 6, 1);
        const winterOffset = winter.getTimezoneOffset();
        const summerOffset = summer.getTimezoneOffset();
        const stdTimezoneOffset = Math.max(winterOffset, summerOffset);
        HEAP32[__get_timezone() >> 2] = stdTimezoneOffset * 60;
        HEAP32[__get_daylight() >> 2] = Number(winterOffset != summerOffset);
        function extractZone(date) {
          const match = date.toTimeString().match(/\(([A-Za-z ]+)\)$/);
          return match ? match[1] : "GMT";
        }
        const winterName = extractZone(winter);
        const summerName = extractZone(summer);
        const winterNamePtr = allocateUTF8(winterName);
        const summerNamePtr = allocateUTF8(summerName);
        if (summerOffset < winterOffset) {
          HEAP32[__get_tzname() >> 2] = winterNamePtr;
          HEAP32[__get_tzname() + 4 >> 2] = summerNamePtr;
        } else {
          HEAP32[__get_tzname() >> 2] = summerNamePtr;
          HEAP32[__get_tzname() + 4 >> 2] = winterNamePtr;
        }
      }
      function _localtime_r(time, tmPtr) {
        _tzset();
        const date = new Date(HEAP32[time >> 2] * 1e3);
        HEAP32[tmPtr >> 2] = date.getSeconds();
        HEAP32[tmPtr + 4 >> 2] = date.getMinutes();
        HEAP32[tmPtr + 8 >> 2] = date.getHours();
        HEAP32[tmPtr + 12 >> 2] = date.getDate();
        HEAP32[tmPtr + 16 >> 2] = date.getMonth();
        HEAP32[tmPtr + 20 >> 2] = date.getFullYear() - 1900;
        HEAP32[tmPtr + 24 >> 2] = date.getDay();
        const start = new Date(date.getFullYear(), 0, 1);
        const yday = (date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24) | 0;
        HEAP32[tmPtr + 28 >> 2] = yday;
        HEAP32[tmPtr + 36 >> 2] = -(date.getTimezoneOffset() * 60);
        const summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
        const winterOffset = start.getTimezoneOffset();
        const dst = (summerOffset != winterOffset && date.getTimezoneOffset() == Math.min(winterOffset, summerOffset)) | 0;
        HEAP32[tmPtr + 32 >> 2] = dst;
        const zonePtr = HEAP32[__get_tzname() + (dst ? 4 : 0) >> 2];
        HEAP32[tmPtr + 40 >> 2] = zonePtr;
        return tmPtr;
      }
      function _mktime(tmPtr) {
        _tzset();
        const date = new Date(HEAP32[tmPtr + 20 >> 2] + 1900, HEAP32[tmPtr + 16 >> 2], HEAP32[tmPtr + 12 >> 2], HEAP32[tmPtr + 8 >> 2], HEAP32[tmPtr + 4 >> 2], HEAP32[tmPtr >> 2], 0);
        const dst = HEAP32[tmPtr + 32 >> 2];
        const guessedOffset = date.getTimezoneOffset();
        const start = new Date(date.getFullYear(), 0, 1);
        const summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
        const winterOffset = start.getTimezoneOffset();
        const dstOffset = Math.min(winterOffset, summerOffset);
        if (dst < 0) {
          HEAP32[tmPtr + 32 >> 2] = Number(summerOffset != winterOffset && dstOffset == guessedOffset);
        } else if (dst > 0 != (dstOffset == guessedOffset)) {
          const nonDstOffset = Math.max(winterOffset, summerOffset);
          const trueOffset = dst > 0 ? dstOffset : nonDstOffset;
          date.setTime(date.getTime() + (trueOffset - guessedOffset) * 6e4);
        }
        HEAP32[tmPtr + 24 >> 2] = date.getDay();
        const yday = (date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24) | 0;
        HEAP32[tmPtr + 28 >> 2] = yday;
        HEAP32[tmPtr >> 2] = date.getSeconds();
        HEAP32[tmPtr + 4 >> 2] = date.getMinutes();
        HEAP32[tmPtr + 8 >> 2] = date.getHours();
        HEAP32[tmPtr + 12 >> 2] = date.getDate();
        HEAP32[tmPtr + 16 >> 2] = date.getMonth();
        return date.getTime() / 1e3 | 0;
      }
      function _proc_exit(code) {
        procExit(code);
      }
      function _setTempRet0(val) {
        setTempRet0(val);
      }
      function __isLeapYear(year) {
        return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
      }
      function __arraySum(array, index) {
        let sum = 0;
        for (let i2 = 0; i2 <= index; sum += array[i2++]) {
        }
        return sum;
      }
      const __MONTH_DAYS_LEAP = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
      const __MONTH_DAYS_REGULAR = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
      function __addDays(date, days) {
        const newDate = new Date(date.getTime());
        while (days > 0) {
          const leap = __isLeapYear(newDate.getFullYear());
          const currentMonth = newDate.getMonth();
          const daysInCurrentMonth = (leap ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[currentMonth];
          if (days > daysInCurrentMonth - newDate.getDate()) {
            days -= daysInCurrentMonth - newDate.getDate() + 1;
            newDate.setDate(1);
            if (currentMonth < 11) {
              newDate.setMonth(currentMonth + 1);
            } else {
              newDate.setMonth(0);
              newDate.setFullYear(newDate.getFullYear() + 1);
            }
          } else {
            newDate.setDate(newDate.getDate() + days);
            return newDate;
          }
        }
        return newDate;
      }
      function _strftime(s, maxsize, format, tm) {
        const tm_zone = HEAP32[tm + 40 >> 2];
        const date = { tm_sec: HEAP32[tm >> 2], tm_min: HEAP32[tm + 4 >> 2], tm_hour: HEAP32[tm + 8 >> 2], tm_mday: HEAP32[tm + 12 >> 2], tm_mon: HEAP32[tm + 16 >> 2], tm_year: HEAP32[tm + 20 >> 2], tm_wday: HEAP32[tm + 24 >> 2], tm_yday: HEAP32[tm + 28 >> 2], tm_isdst: HEAP32[tm + 32 >> 2], tm_gmtoff: HEAP32[tm + 36 >> 2], tm_zone: tm_zone ? UTF8ToString(tm_zone) : "" };
        let pattern = UTF8ToString(format);
        const EXPANSION_RULES_1 = { "%c": "%a %b %d %H:%M:%S %Y", "%D": "%m/%d/%y", "%F": "%Y-%m-%d", "%h": "%b", "%r": "%I:%M:%S %p", "%R": "%H:%M", "%T": "%H:%M:%S", "%x": "%m/%d/%y", "%X": "%H:%M:%S", "%Ec": "%c", "%EC": "%C", "%Ex": "%m/%d/%y", "%EX": "%H:%M:%S", "%Ey": "%y", "%EY": "%Y", "%Od": "%d", "%Oe": "%e", "%OH": "%H", "%OI": "%I", "%Om": "%m", "%OM": "%M", "%OS": "%S", "%Ou": "%u", "%OU": "%U", "%OV": "%V", "%Ow": "%w", "%OW": "%W", "%Oy": "%y" };
        for (var rule in EXPANSION_RULES_1) {
          pattern = pattern.replace(new RegExp(rule, "g"), EXPANSION_RULES_1[rule]);
        }
        const WEEKDAYS = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
        const MONTHS = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        function leadingSomething(value, digits, character) {
          let str = typeof value === "number" ? value.toString() : value || "";
          while (str.length < digits) {
            str = character[0] + str;
          }
          return str;
        }
        function leadingNulls(value, digits) {
          return leadingSomething(value, digits, "0");
        }
        function compareByDay(date1, date2) {
          function sgn(value) {
            return value < 0 ? -1 : value > 0 ? 1 : 0;
          }
          let compare;
          if ((compare = sgn(date1.getFullYear() - date2.getFullYear())) === 0) {
            if ((compare = sgn(date1.getMonth() - date2.getMonth())) === 0) {
              compare = sgn(date1.getDate() - date2.getDate());
            }
          }
          return compare;
        }
        function getFirstWeekStartDate(janFourth) {
          switch (janFourth.getDay()) {
            case 0:
              return new Date(janFourth.getFullYear() - 1, 11, 29);
            case 1:
              return janFourth;
            case 2:
              return new Date(janFourth.getFullYear(), 0, 3);
            case 3:
              return new Date(janFourth.getFullYear(), 0, 2);
            case 4:
              return new Date(janFourth.getFullYear(), 0, 1);
            case 5:
              return new Date(janFourth.getFullYear() - 1, 11, 31);
            case 6:
              return new Date(janFourth.getFullYear() - 1, 11, 30);
          }
        }
        function getWeekBasedYear(date2) {
          const thisDate = __addDays(new Date(date2.tm_year + 1900, 0, 1), date2.tm_yday);
          const janFourthThisYear = new Date(thisDate.getFullYear(), 0, 4);
          const janFourthNextYear = new Date(thisDate.getFullYear() + 1, 0, 4);
          const firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);
          const firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);
          if (compareByDay(firstWeekStartThisYear, thisDate) <= 0) {
            if (compareByDay(firstWeekStartNextYear, thisDate) <= 0) {
              return thisDate.getFullYear() + 1;
            }
            return thisDate.getFullYear();
          }
          return thisDate.getFullYear() - 1;
        }
        const EXPANSION_RULES_2 = { "%a"(date2) {
          return WEEKDAYS[date2.tm_wday].substring(0, 3);
        }, "%A"(date2) {
          return WEEKDAYS[date2.tm_wday];
        }, "%b"(date2) {
          return MONTHS[date2.tm_mon].substring(0, 3);
        }, "%B"(date2) {
          return MONTHS[date2.tm_mon];
        }, "%C"(date2) {
          const year = date2.tm_year + 1900;
          return leadingNulls(year / 100 | 0, 2);
        }, "%d"(date2) {
          return leadingNulls(date2.tm_mday, 2);
        }, "%e"(date2) {
          return leadingSomething(date2.tm_mday, 2, " ");
        }, "%g"(date2) {
          return getWeekBasedYear(date2).toString().substring(2);
        }, "%G"(date2) {
          return getWeekBasedYear(date2);
        }, "%H"(date2) {
          return leadingNulls(date2.tm_hour, 2);
        }, "%I"(date2) {
          let twelveHour = date2.tm_hour;
          if (twelveHour == 0)
            twelveHour = 12;
          else if (twelveHour > 12)
            twelveHour -= 12;
          return leadingNulls(twelveHour, 2);
        }, "%j"(date2) {
          return leadingNulls(date2.tm_mday + __arraySum(__isLeapYear(date2.tm_year + 1900) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, date2.tm_mon - 1), 3);
        }, "%m"(date2) {
          return leadingNulls(date2.tm_mon + 1, 2);
        }, "%M"(date2) {
          return leadingNulls(date2.tm_min, 2);
        }, "%n"() {
          return "\n";
        }, "%p"(date2) {
          if (date2.tm_hour >= 0 && date2.tm_hour < 12) {
            return "AM";
          }
          return "PM";
        }, "%S"(date2) {
          return leadingNulls(date2.tm_sec, 2);
        }, "%t"() {
          return "	";
        }, "%u"(date2) {
          return date2.tm_wday || 7;
        }, "%U"(date2) {
          const janFirst = new Date(date2.tm_year + 1900, 0, 1);
          const firstSunday = janFirst.getDay() === 0 ? janFirst : __addDays(janFirst, 7 - janFirst.getDay());
          const endDate = new Date(date2.tm_year + 1900, date2.tm_mon, date2.tm_mday);
          if (compareByDay(firstSunday, endDate) < 0) {
            const februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth() - 1) - 31;
            const firstSundayUntilEndJanuary = 31 - firstSunday.getDate();
            const days = firstSundayUntilEndJanuary + februaryFirstUntilEndMonth + endDate.getDate();
            return leadingNulls(Math.ceil(days / 7), 2);
          }
          return compareByDay(firstSunday, janFirst) === 0 ? "01" : "00";
        }, "%V"(date2) {
          const janFourthThisYear = new Date(date2.tm_year + 1900, 0, 4);
          const janFourthNextYear = new Date(date2.tm_year + 1901, 0, 4);
          const firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);
          const firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);
          const endDate = __addDays(new Date(date2.tm_year + 1900, 0, 1), date2.tm_yday);
          if (compareByDay(endDate, firstWeekStartThisYear) < 0) {
            return "53";
          }
          if (compareByDay(firstWeekStartNextYear, endDate) <= 0) {
            return "01";
          }
          let daysDifference;
          if (firstWeekStartThisYear.getFullYear() < date2.tm_year + 1900) {
            daysDifference = date2.tm_yday + 32 - firstWeekStartThisYear.getDate();
          } else {
            daysDifference = date2.tm_yday + 1 - firstWeekStartThisYear.getDate();
          }
          return leadingNulls(Math.ceil(daysDifference / 7), 2);
        }, "%w"(date2) {
          return date2.tm_wday;
        }, "%W"(date2) {
          const janFirst = new Date(date2.tm_year, 0, 1);
          const firstMonday = janFirst.getDay() === 1 ? janFirst : __addDays(janFirst, janFirst.getDay() === 0 ? 1 : 7 - janFirst.getDay() + 1);
          const endDate = new Date(date2.tm_year + 1900, date2.tm_mon, date2.tm_mday);
          if (compareByDay(firstMonday, endDate) < 0) {
            const februaryFirstUntilEndMonth = __arraySum(__isLeapYear(endDate.getFullYear()) ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR, endDate.getMonth() - 1) - 31;
            const firstMondayUntilEndJanuary = 31 - firstMonday.getDate();
            const days = firstMondayUntilEndJanuary + februaryFirstUntilEndMonth + endDate.getDate();
            return leadingNulls(Math.ceil(days / 7), 2);
          }
          return compareByDay(firstMonday, janFirst) === 0 ? "01" : "00";
        }, "%y"(date2) {
          return (date2.tm_year + 1900).toString().substring(2);
        }, "%Y"(date2) {
          return date2.tm_year + 1900;
        }, "%z"(date2) {
          let off = date2.tm_gmtoff;
          const ahead = off >= 0;
          off = Math.abs(off) / 60;
          off = off / 60 * 100 + off % 60;
          return (ahead ? "+" : "-") + String(`0000${off}`).slice(-4);
        }, "%Z"(date2) {
          return date2.tm_zone;
        }, "%%"() {
          return "%";
        } };
        for (var rule in EXPANSION_RULES_2) {
          if (pattern.includes(rule)) {
            pattern = pattern.replace(new RegExp(rule, "g"), EXPANSION_RULES_2[rule](date));
          }
        }
        const bytes = intArrayFromString(pattern, false);
        if (bytes.length > maxsize) {
          return 0;
        }
        writeArrayToMemory(bytes, s);
        return bytes.length - 1;
      }
      function _strftime_l(s, maxsize, format, tm) {
        return _strftime(s, maxsize, format, tm);
      }
      Module.requestFullscreen = function Module_requestFullscreen(lockPointer, resizeCanvas) {
        Browser.requestFullscreen(lockPointer, resizeCanvas);
      };
      Module.requestFullScreen = function Module_requestFullScreen() {
        Browser.requestFullScreen();
      };
      Module.requestAnimationFrame = function Module_requestAnimationFrame(func) {
        Browser.requestAnimationFrame(func);
      };
      Module.setCanvasSize = function Module_setCanvasSize(width, height, noUpdates) {
        Browser.setCanvasSize(width, height, noUpdates);
      };
      Module.pauseMainLoop = function Module_pauseMainLoop() {
        Browser.mainLoop.pause();
      };
      Module.resumeMainLoop = function Module_resumeMainLoop() {
        Browser.mainLoop.resume();
      };
      Module.getUserMedia = function Module_getUserMedia() {
        Browser.getUserMedia();
      };
      Module.createContext = function Module_createContext(canvas, useWebGL, setInModule, webGLContextAttributes) {
        return Browser.createContext(canvas, useWebGL, setInModule, webGLContextAttributes);
      };
      const FSNode = function(parent, name, mode, rdev) {
        if (!parent) {
          parent = this;
        }
        this.parent = parent;
        this.mount = parent.mount;
        this.mounted = null;
        this.id = FS.nextInode++;
        this.name = name;
        this.mode = mode;
        this.node_ops = {};
        this.stream_ops = {};
        this.rdev = rdev;
      };
      const readMode = 292 | 73;
      const writeMode = 146;
      Object.defineProperties(FSNode.prototype, { read: { get() {
        return (this.mode & readMode) === readMode;
      }, set(val) {
        val ? this.mode |= readMode : this.mode &= ~readMode;
      } }, write: { get() {
        return (this.mode & writeMode) === writeMode;
      }, set(val) {
        val ? this.mode |= writeMode : this.mode &= ~writeMode;
      } }, isFolder: { get() {
        return FS.isDir(this.mode);
      } }, isDevice: { get() {
        return FS.isChrdev(this.mode);
      } } });
      FS.FSNode = FSNode;
      FS.staticInit();
      Module.FS_createPath = FS.createPath;
      Module.FS_createDataFile = FS.createDataFile;
      Module.FS_createPreloadedFile = FS.createPreloadedFile;
      Module.FS_createLazyFile = FS.createLazyFile;
      Module.FS_createDevice = FS.createDevice;
      Module.FS_unlink = FS.unlink;
      init_emval();
      PureVirtualError = Module.PureVirtualError = extendError(Error, "PureVirtualError");
      embind_init_charCodes();
      init_embind();
      BindingError = Module.BindingError = extendError(Error, "BindingError");
      InternalError = Module.InternalError = extendError(Error, "InternalError");
      init_ClassHandle();
      init_RegisteredPointer();
      UnboundTypeError = Module.UnboundTypeError = extendError(Error, "UnboundTypeError");
      let GLctx;
      for (var i = 0; i < 32; ++i)
        tempFixedLengthArray.push(new Array(i));
      const miniTempWebGLFloatBuffersStorage = new Float32Array(288);
      for (var i = 0; i < 288; ++i) {
        miniTempWebGLFloatBuffers[i] = miniTempWebGLFloatBuffersStorage.subarray(0, i + 1);
      }
      const __miniTempWebGLIntBuffersStorage = new Int32Array(288);
      for (var i = 0; i < 288; ++i) {
        __miniTempWebGLIntBuffers[i] = __miniTempWebGLIntBuffersStorage.subarray(0, i + 1);
      }
      const ASSERTIONS = true;
      function intArrayFromString(stringy, dontAddNull, length) {
        const len = length > 0 ? length : lengthBytesUTF8(stringy) + 1;
        const u8array = new Array(len);
        const numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
        if (dontAddNull)
          u8array.length = numBytesWritten;
        return u8array;
      }
      const decodeBase64 = typeof atob === "function" ? atob : function(input) {
        const keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
        let output = "";
        let chr1;
        let chr2;
        let chr3;
        let enc1;
        let enc2;
        let enc3;
        let enc4;
        let i2 = 0;
        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
        do {
          enc1 = keyStr.indexOf(input.charAt(i2++));
          enc2 = keyStr.indexOf(input.charAt(i2++));
          enc3 = keyStr.indexOf(input.charAt(i2++));
          enc4 = keyStr.indexOf(input.charAt(i2++));
          chr1 = enc1 << 2 | enc2 >> 4;
          chr2 = (enc2 & 15) << 4 | enc3 >> 2;
          chr3 = (enc3 & 3) << 6 | enc4;
          output = output + String.fromCharCode(chr1);
          if (enc3 !== 64) {
            output = output + String.fromCharCode(chr2);
          }
          if (enc4 !== 64) {
            output = output + String.fromCharCode(chr3);
          }
        } while (i2 < input.length);
        return output;
      };
      function intArrayFromBase64(s) {
        if (typeof ENVIRONMENT_IS_NODE === "boolean" && ENVIRONMENT_IS_NODE) {
          const buf = Buffer.from(s, "base64");
          return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength);
        }
        try {
          const decoded = decodeBase64(s);
          const bytes = new Uint8Array(decoded.length);
          for (let i2 = 0; i2 < decoded.length; ++i2) {
            bytes[i2] = decoded.charCodeAt(i2);
          }
          return bytes;
        } catch (_) {
          throw new Error("Converting base64 string to bytes failed.");
        }
      }
      var asmLibraryArg = { HaveOffsetConverter, __cxa_allocate_exception: ___cxa_allocate_exception, __cxa_atexit: ___cxa_atexit, __cxa_begin_catch: ___cxa_begin_catch, __cxa_end_catch: ___cxa_end_catch, __cxa_find_matching_catch_2: ___cxa_find_matching_catch_2, __cxa_find_matching_catch_3: ___cxa_find_matching_catch_3, __cxa_free_exception: ___cxa_free_exception, __cxa_rethrow: ___cxa_rethrow, __cxa_thread_atexit: ___cxa_thread_atexit, __cxa_throw: ___cxa_throw, __resumeException: ___resumeException, __sys_fcntl64: ___sys_fcntl64, __sys_ioctl: ___sys_ioctl, __sys_mmap2: ___sys_mmap2, __sys_open: ___sys_open, __sys_stat64: ___sys_stat64, _embind_create_inheriting_constructor: __embind_create_inheriting_constructor, _embind_finalize_value_object: __embind_finalize_value_object, _embind_register_bigint: __embind_register_bigint, _embind_register_bool: __embind_register_bool, _embind_register_class: __embind_register_class, _embind_register_class_class_function: __embind_register_class_class_function, _embind_register_class_constructor: __embind_register_class_constructor, _embind_register_class_function: __embind_register_class_function, _embind_register_class_property: __embind_register_class_property, _embind_register_emval: __embind_register_emval, _embind_register_float: __embind_register_float, _embind_register_function: __embind_register_function, _embind_register_integer: __embind_register_integer, _embind_register_memory_view: __embind_register_memory_view, _embind_register_std_string: __embind_register_std_string, _embind_register_std_wstring: __embind_register_std_wstring, _embind_register_value_object: __embind_register_value_object, _embind_register_value_object_field: __embind_register_value_object_field, _embind_register_void: __embind_register_void, _emval_call_void_method: __emval_call_void_method, _emval_decref: __emval_decref, _emval_get_method_caller: __emval_get_method_caller, _emval_incref: __emval_incref, _emval_take_value: __emval_take_value, abort: _abort, clock_gettime: _clock_gettime, dlopen: _dlopen, dlsym: _dlsym, emscripten_asm_const_int: _emscripten_asm_const_int, emscripten_get_heap_max: _emscripten_get_heap_max, emscripten_memcpy_big: _emscripten_memcpy_big, emscripten_pc_get_function: _emscripten_pc_get_function, emscripten_resize_heap: _emscripten_resize_heap, emscripten_stack_snapshot: _emscripten_stack_snapshot, emscripten_stack_unwind_buffer: _emscripten_stack_unwind_buffer, emscripten_thread_sleep: _emscripten_thread_sleep, emscripten_webgl_create_context: _emscripten_webgl_create_context, emscripten_webgl_destroy_context: _emscripten_webgl_destroy_context, emscripten_webgl_get_context_attributes: _emscripten_webgl_get_context_attributes, emscripten_webgl_get_current_context: _emscripten_webgl_get_current_context, emscripten_webgl_init_context_attributes: _emscripten_webgl_init_context_attributes, emscripten_webgl_make_context_current: _emscripten_webgl_make_context_current, environ_get: _environ_get, environ_sizes_get: _environ_sizes_get, exit: _exit, fd_close: _fd_close, fd_read: _fd_read, fd_seek: _fd_seek, fd_write: _fd_write, getTempRet0: _getTempRet0, getentropy: _getentropy, glActiveTexture: _glActiveTexture, glAttachShader: _glAttachShader, glBindAttribLocation: _glBindAttribLocation, glBindBuffer: _glBindBuffer, glBindBufferBase: _glBindBufferBase, glBindFramebuffer: _glBindFramebuffer, glBindTexture: _glBindTexture, glBindVertexArray: _glBindVertexArray, glBufferData: _glBufferData, glClientWaitSync: _glClientWaitSync, glCompileShader: _glCompileShader, glCreateProgram: _glCreateProgram, glCreateShader: _glCreateShader, glDeleteBuffers: _glDeleteBuffers, glDeleteFramebuffers: _glDeleteFramebuffers, glDeleteProgram: _glDeleteProgram, glDeleteShader: _glDeleteShader, glDeleteSync: _glDeleteSync, glDeleteTextures: _glDeleteTextures, glDeleteVertexArrays: _glDeleteVertexArrays, glDisable: _glDisable, glDisableVertexAttribArray: _glDisableVertexAttribArray, glDrawArrays: _glDrawArrays, glDrawBuffers: _glDrawBuffers, glEnableVertexAttribArray: _glEnableVertexAttribArray, glFenceSync: _glFenceSync, glFinish: _glFinish, glFlush: _glFlush, glFramebufferTexture2D: _glFramebufferTexture2D, glFramebufferTextureLayer: _glFramebufferTextureLayer, glGenBuffers: _glGenBuffers, glGenFramebuffers: _glGenFramebuffers, glGenTextures: _glGenTextures, glGenVertexArrays: _glGenVertexArrays, glGetAttribLocation: _glGetAttribLocation, glGetError: _glGetError, glGetIntegerv: _glGetIntegerv, glGetString: _glGetString, glGetUniformBlockIndex: _glGetUniformBlockIndex, glGetUniformLocation: _glGetUniformLocation, glLinkProgram: _glLinkProgram, glPixelStorei: _glPixelStorei, glReadPixels: _glReadPixels, glShaderSource: _glShaderSource, glTexImage2D: _glTexImage2D, glTexParameterfv: _glTexParameterfv, glTexParameteri: _glTexParameteri, glTexStorage2D: _glTexStorage2D, glTexStorage3D: _glTexStorage3D, glTexSubImage2D: _glTexSubImage2D, glTexSubImage3D: _glTexSubImage3D, glUniform1f: _glUniform1f, glUniform1i: _glUniform1i, glUniform2fv: _glUniform2fv, glUniform4fv: _glUniform4fv, glUniform4iv: _glUniform4iv, glUniformBlockBinding: _glUniformBlockBinding, glUniformMatrix4fv: _glUniformMatrix4fv, glUseProgram: _glUseProgram, glVertexAttribPointer: _glVertexAttribPointer, glViewport: _glViewport, gmtime_r: _gmtime_r, invoke_diii, invoke_i, invoke_ii, invoke_iii, invoke_iiii, invoke_iiiii, invoke_iiiiii, invoke_iiiiiii, invoke_iiiiiiiddi, invoke_iiiiiiii, invoke_iiiiiiiii, invoke_v, invoke_vdiii, invoke_vi, invoke_vididdii, invoke_vidii, invoke_vii, invoke_viid, invoke_viidi, invoke_viididii, invoke_viii, invoke_viiid, invoke_viiidd, invoke_viiiddi, invoke_viiidi, invoke_viiii, invoke_viiiid, invoke_viiiidi, invoke_viiiidid, invoke_viiiidiidiiiiiii, invoke_viiiifiifiiiiiii, invoke_viiiii, invoke_viiiiid, invoke_viiiiif, invoke_viiiiii, invoke_viiiiiid, invoke_viiiiiif, invoke_viiiiiii, invoke_viiiiiiiddi, invoke_viiiiiiidiiii, invoke_viiiiiiifiiii, invoke_viiiiiiii, invoke_viiiiiiiidd, invoke_viiiiiiiidf, invoke_viiiiiiiii, invoke_viiiiiiiiidd, invoke_viiiiiiiiiddi, invoke_viiiiiiiiii, invoke_viiiiiiiiiiddi, invoke_viiiiiiiiiii, invoke_viiiiiiiiiiii, invoke_viiiiiiiiiiiii, llvm_eh_typeid_for: _llvm_eh_typeid_for, localtime_r: _localtime_r, mktime: _mktime, proc_exit: _proc_exit, setTempRet0: _setTempRet0, strftime: _strftime, strftime_l: _strftime_l };
      const asm = createWasm();
      const ___wasm_call_ctors = Module.___wasm_call_ctors = createExportWrapper("__wasm_call_ctors");
      var _free = Module._free = createExportWrapper("free");
      var _malloc = Module._malloc = createExportWrapper("malloc");
      var ___errno_location = Module.___errno_location = createExportWrapper("__errno_location");
      const _fflush = Module._fflush = createExportWrapper("fflush");
      var ___getTypeName = Module.___getTypeName = createExportWrapper("__getTypeName");
      const ___embind_register_native_and_builtin_types = Module.___embind_register_native_and_builtin_types = createExportWrapper("__embind_register_native_and_builtin_types");
      const _emscripten_main_thread_process_queued_calls = Module._emscripten_main_thread_process_queued_calls = createExportWrapper("emscripten_main_thread_process_queued_calls");
      var _emscripten_stack_get_end = Module._emscripten_stack_get_end = function() {
        return (_emscripten_stack_get_end = Module._emscripten_stack_get_end = Module.asm.emscripten_stack_get_end).apply(null, arguments);
      };
      var __get_tzname = Module.__get_tzname = createExportWrapper("_get_tzname");
      var __get_daylight = Module.__get_daylight = createExportWrapper("_get_daylight");
      var __get_timezone = Module.__get_timezone = createExportWrapper("_get_timezone");
      var stackSave = Module.stackSave = createExportWrapper("stackSave");
      var stackRestore = Module.stackRestore = createExportWrapper("stackRestore");
      var stackAlloc = Module.stackAlloc = createExportWrapper("stackAlloc");
      var _emscripten_stack_init = Module._emscripten_stack_init = function() {
        return (_emscripten_stack_init = Module._emscripten_stack_init = Module.asm.emscripten_stack_init).apply(null, arguments);
      };
      var _emscripten_stack_get_free = Module._emscripten_stack_get_free = function() {
        return (_emscripten_stack_get_free = Module._emscripten_stack_get_free = Module.asm.emscripten_stack_get_free).apply(null, arguments);
      };
      const _saveSetjmp = Module._saveSetjmp = createExportWrapper("saveSetjmp");
      var _setThrew = Module._setThrew = createExportWrapper("setThrew");
      var ___cxa_can_catch = Module.___cxa_can_catch = createExportWrapper("__cxa_can_catch");
      var ___cxa_is_pointer_type = Module.___cxa_is_pointer_type = createExportWrapper("__cxa_is_pointer_type");
      var _memalign = Module._memalign = createExportWrapper("memalign");
      const _emscripten_builtin_malloc = Module._emscripten_builtin_malloc = createExportWrapper("emscripten_builtin_malloc");
      const _emscripten_builtin_free = Module._emscripten_builtin_free = createExportWrapper("emscripten_builtin_free");
      const _emscripten_builtin_memalign = Module._emscripten_builtin_memalign = createExportWrapper("emscripten_builtin_memalign");
      const dynCall_jii = Module.dynCall_jii = createExportWrapper("dynCall_jii");
      const dynCall_viji = Module.dynCall_viji = createExportWrapper("dynCall_viji");
      const dynCall_jjj = Module.dynCall_jjj = createExportWrapper("dynCall_jjj");
      const dynCall_iiiijj = Module.dynCall_iiiijj = createExportWrapper("dynCall_iiiijj");
      const dynCall_viijj = Module.dynCall_viijj = createExportWrapper("dynCall_viijj");
      const dynCall_viiijjjj = Module.dynCall_viiijjjj = createExportWrapper("dynCall_viiijjjj");
      const dynCall_ji = Module.dynCall_ji = createExportWrapper("dynCall_ji");
      const dynCall_vij = Module.dynCall_vij = createExportWrapper("dynCall_vij");
      const dynCall_viijii = Module.dynCall_viijii = createExportWrapper("dynCall_viijii");
      const dynCall_vj = Module.dynCall_vj = createExportWrapper("dynCall_vj");
      const dynCall_viij = Module.dynCall_viij = createExportWrapper("dynCall_viij");
      const dynCall_viiiiij = Module.dynCall_viiiiij = createExportWrapper("dynCall_viiiiij");
      const dynCall_iijjiiii = Module.dynCall_iijjiiii = createExportWrapper("dynCall_iijjiiii");
      const dynCall_jiji = Module.dynCall_jiji = createExportWrapper("dynCall_jiji");
      const dynCall_iiiiij = Module.dynCall_iiiiij = createExportWrapper("dynCall_iiiiij");
      const dynCall_iiiiijj = Module.dynCall_iiiiijj = createExportWrapper("dynCall_iiiiijj");
      const dynCall_iiiiiijj = Module.dynCall_iiiiiijj = createExportWrapper("dynCall_iiiiiijj");
      function invoke_viid(index, a1, a2, a3) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_vi(index, a1) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iii(index, a1, a2) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiii(index, a1, a2, a3, a4, a5) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiiiii(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_v(index) {
        const sp = stackSave();
        try {
          wasmTable.get(index)();
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_vii(index, a1, a2) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_ii(index, a1) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viii(index, a1, a2, a3) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiii(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiii(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiii(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiidd(index, a1, a2, a3, a4, a5) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4, a5);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_diii(index, a1, a2, a3) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiii(index, a1, a2, a3) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiidd(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiiddi(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiddi(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_i(index) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)();
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiidi(index, a1, a2, a3, a4, a5) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiidi(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viididii(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiidf(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiidd(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiid(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiid(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiidiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiif(index, a1, a2, a3, a4, a5, a6) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiif(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiifiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiifiifiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiidiidiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_vididdii(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiid(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_vidii(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiiiiddi(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiiiiiddi(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_iiiiiiiddi(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
        const sp = stackSave();
        try {
          return wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiid(index, a1, a2, a3, a4, a5) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viiiidid(index, a1, a2, a3, a4, a5, a6, a7) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4, a5, a6, a7);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_viidi(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      function invoke_vdiii(index, a1, a2, a3, a4) {
        const sp = stackSave();
        try {
          wasmTable.get(index)(a1, a2, a3, a4);
        } catch (e) {
          stackRestore(sp);
          if (e !== e + 0 && e !== "longjmp")
            throw e;
          _setThrew(1, 0);
        }
      }
      if (!Object.getOwnPropertyDescriptor(Module, "intArrayFromString"))
        Module.intArrayFromString = function() {
          abort("'intArrayFromString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "intArrayToString"))
        Module.intArrayToString = function() {
          abort("'intArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ccall"))
        Module.ccall = function() {
          abort("'ccall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "cwrap"))
        Module.cwrap = function() {
          abort("'cwrap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setValue"))
        Module.setValue = function() {
          abort("'setValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getValue"))
        Module.getValue = function() {
          abort("'getValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "allocate"))
        Module.allocate = function() {
          abort("'allocate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UTF8ArrayToString"))
        Module.UTF8ArrayToString = function() {
          abort("'UTF8ArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UTF8ToString"))
        Module.UTF8ToString = function() {
          abort("'UTF8ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToUTF8Array"))
        Module.stringToUTF8Array = function() {
          abort("'stringToUTF8Array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToUTF8"))
        Module.stringToUTF8 = function() {
          abort("'stringToUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "lengthBytesUTF8"))
        Module.lengthBytesUTF8 = function() {
          abort("'lengthBytesUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stackTrace"))
        Module.stackTrace = function() {
          abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addOnPreRun"))
        Module.addOnPreRun = function() {
          abort("'addOnPreRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addOnInit"))
        Module.addOnInit = function() {
          abort("'addOnInit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addOnPreMain"))
        Module.addOnPreMain = function() {
          abort("'addOnPreMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addOnExit"))
        Module.addOnExit = function() {
          abort("'addOnExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addOnPostRun"))
        Module.addOnPostRun = function() {
          abort("'addOnPostRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeStringToMemory"))
        Module.writeStringToMemory = function() {
          abort("'writeStringToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeArrayToMemory"))
        Module.writeArrayToMemory = function() {
          abort("'writeArrayToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeAsciiToMemory"))
        Module.writeAsciiToMemory = function() {
          abort("'writeAsciiToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      Module.addRunDependency = addRunDependency;
      Module.removeRunDependency = removeRunDependency;
      if (!Object.getOwnPropertyDescriptor(Module, "FS_createFolder"))
        Module.FS_createFolder = function() {
          abort("'FS_createFolder' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      Module.FS_createPath = FS.createPath;
      Module.FS_createDataFile = FS.createDataFile;
      Module.FS_createPreloadedFile = FS.createPreloadedFile;
      Module.FS_createLazyFile = FS.createLazyFile;
      if (!Object.getOwnPropertyDescriptor(Module, "FS_createLink"))
        Module.FS_createLink = function() {
          abort("'FS_createLink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      Module.FS_createDevice = FS.createDevice;
      Module.FS_unlink = FS.unlink;
      if (!Object.getOwnPropertyDescriptor(Module, "getLEB"))
        Module.getLEB = function() {
          abort("'getLEB' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getFunctionTables"))
        Module.getFunctionTables = function() {
          abort("'getFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "alignFunctionTables"))
        Module.alignFunctionTables = function() {
          abort("'alignFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerFunctions"))
        Module.registerFunctions = function() {
          abort("'registerFunctions' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "addFunction"))
        Module.addFunction = function() {
          abort("'addFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "removeFunction"))
        Module.removeFunction = function() {
          abort("'removeFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getFuncWrapper"))
        Module.getFuncWrapper = function() {
          abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "prettyPrint"))
        Module.prettyPrint = function() {
          abort("'prettyPrint' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "dynCall"))
        Module.dynCall = function() {
          abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getCompilerSetting"))
        Module.getCompilerSetting = function() {
          abort("'getCompilerSetting' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "print"))
        Module.print = function() {
          abort("'print' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "printErr"))
        Module.printErr = function() {
          abort("'printErr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getTempRet0"))
        Module.getTempRet0 = function() {
          abort("'getTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setTempRet0"))
        Module.setTempRet0 = function() {
          abort("'setTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "callMain"))
        Module.callMain = function() {
          abort("'callMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "abort"))
        Module.abort = function() {
          abort("'abort' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "keepRuntimeAlive"))
        Module.keepRuntimeAlive = function() {
          abort("'keepRuntimeAlive' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "zeroMemory"))
        Module.zeroMemory = function() {
          abort("'zeroMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToNewUTF8"))
        Module.stringToNewUTF8 = function() {
          abort("'stringToNewUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setFileTime"))
        Module.setFileTime = function() {
          abort("'setFileTime' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscripten_realloc_buffer"))
        Module.emscripten_realloc_buffer = function() {
          abort("'emscripten_realloc_buffer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ENV"))
        Module.ENV = function() {
          abort("'ENV' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ERRNO_CODES"))
        Module.ERRNO_CODES = function() {
          abort("'ERRNO_CODES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ERRNO_MESSAGES"))
        Module.ERRNO_MESSAGES = function() {
          abort("'ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setErrNo"))
        Module.setErrNo = function() {
          abort("'setErrNo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "inetPton4"))
        Module.inetPton4 = function() {
          abort("'inetPton4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "inetNtop4"))
        Module.inetNtop4 = function() {
          abort("'inetNtop4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "inetPton6"))
        Module.inetPton6 = function() {
          abort("'inetPton6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "inetNtop6"))
        Module.inetNtop6 = function() {
          abort("'inetNtop6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readSockaddr"))
        Module.readSockaddr = function() {
          abort("'readSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeSockaddr"))
        Module.writeSockaddr = function() {
          abort("'writeSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "DNS"))
        Module.DNS = function() {
          abort("'DNS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getHostByName"))
        Module.getHostByName = function() {
          abort("'getHostByName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "GAI_ERRNO_MESSAGES"))
        Module.GAI_ERRNO_MESSAGES = function() {
          abort("'GAI_ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "Protocols"))
        Module.Protocols = function() {
          abort("'Protocols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "Sockets"))
        Module.Sockets = function() {
          abort("'Sockets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getRandomDevice"))
        Module.getRandomDevice = function() {
          abort("'getRandomDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "traverseStack"))
        Module.traverseStack = function() {
          abort("'traverseStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UNWIND_CACHE"))
        Module.UNWIND_CACHE = function() {
          abort("'UNWIND_CACHE' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "withBuiltinMalloc"))
        Module.withBuiltinMalloc = function() {
          abort("'withBuiltinMalloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readAsmConstArgsArray"))
        Module.readAsmConstArgsArray = function() {
          abort("'readAsmConstArgsArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readAsmConstArgs"))
        Module.readAsmConstArgs = function() {
          abort("'readAsmConstArgs' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "mainThreadEM_ASM"))
        Module.mainThreadEM_ASM = function() {
          abort("'mainThreadEM_ASM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "jstoi_q"))
        Module.jstoi_q = function() {
          abort("'jstoi_q' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "jstoi_s"))
        Module.jstoi_s = function() {
          abort("'jstoi_s' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getExecutableName"))
        Module.getExecutableName = function() {
          abort("'getExecutableName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "listenOnce"))
        Module.listenOnce = function() {
          abort("'listenOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "autoResumeAudioContext"))
        Module.autoResumeAudioContext = function() {
          abort("'autoResumeAudioContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "dynCallLegacy"))
        Module.dynCallLegacy = function() {
          abort("'dynCallLegacy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getDynCaller"))
        Module.getDynCaller = function() {
          abort("'getDynCaller' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "dynCall"))
        Module.dynCall = function() {
          abort("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "callRuntimeCallbacks"))
        Module.callRuntimeCallbacks = function() {
          abort("'callRuntimeCallbacks' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "runtimeKeepalivePush"))
        Module.runtimeKeepalivePush = function() {
          abort("'runtimeKeepalivePush' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "runtimeKeepalivePop"))
        Module.runtimeKeepalivePop = function() {
          abort("'runtimeKeepalivePop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "callUserCallback"))
        Module.callUserCallback = function() {
          abort("'callUserCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "maybeExit"))
        Module.maybeExit = function() {
          abort("'maybeExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "safeSetTimeout"))
        Module.safeSetTimeout = function() {
          abort("'safeSetTimeout' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "asmjsMangle"))
        Module.asmjsMangle = function() {
          abort("'asmjsMangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "asyncLoad"))
        Module.asyncLoad = function() {
          abort("'asyncLoad' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "alignMemory"))
        Module.alignMemory = function() {
          abort("'alignMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "mmapAlloc"))
        Module.mmapAlloc = function() {
          abort("'mmapAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "reallyNegative"))
        Module.reallyNegative = function() {
          abort("'reallyNegative' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "unSign"))
        Module.unSign = function() {
          abort("'unSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "reSign"))
        Module.reSign = function() {
          abort("'reSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "formatString"))
        Module.formatString = function() {
          abort("'formatString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "PATH"))
        Module.PATH = function() {
          abort("'PATH' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "PATH_FS"))
        Module.PATH_FS = function() {
          abort("'PATH_FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SYSCALLS"))
        Module.SYSCALLS = function() {
          abort("'SYSCALLS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "syscallMmap2"))
        Module.syscallMmap2 = function() {
          abort("'syscallMmap2' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "syscallMunmap"))
        Module.syscallMunmap = function() {
          abort("'syscallMunmap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getSocketFromFD"))
        Module.getSocketFromFD = function() {
          abort("'getSocketFromFD' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getSocketAddress"))
        Module.getSocketAddress = function() {
          abort("'getSocketAddress' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "JSEvents"))
        Module.JSEvents = function() {
          abort("'JSEvents' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerKeyEventCallback"))
        Module.registerKeyEventCallback = function() {
          abort("'registerKeyEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "specialHTMLTargets"))
        Module.specialHTMLTargets = function() {
          abort("'specialHTMLTargets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "findEventTarget"))
        Module.findEventTarget = function() {
          abort("'findEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "findCanvasEventTarget"))
        Module.findCanvasEventTarget = function() {
          abort("'findCanvasEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getBoundingClientRect"))
        Module.getBoundingClientRect = function() {
          abort("'getBoundingClientRect' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillMouseEventData"))
        Module.fillMouseEventData = function() {
          abort("'fillMouseEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerMouseEventCallback"))
        Module.registerMouseEventCallback = function() {
          abort("'registerMouseEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerWheelEventCallback"))
        Module.registerWheelEventCallback = function() {
          abort("'registerWheelEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerUiEventCallback"))
        Module.registerUiEventCallback = function() {
          abort("'registerUiEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerFocusEventCallback"))
        Module.registerFocusEventCallback = function() {
          abort("'registerFocusEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillDeviceOrientationEventData"))
        Module.fillDeviceOrientationEventData = function() {
          abort("'fillDeviceOrientationEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerDeviceOrientationEventCallback"))
        Module.registerDeviceOrientationEventCallback = function() {
          abort("'registerDeviceOrientationEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillDeviceMotionEventData"))
        Module.fillDeviceMotionEventData = function() {
          abort("'fillDeviceMotionEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerDeviceMotionEventCallback"))
        Module.registerDeviceMotionEventCallback = function() {
          abort("'registerDeviceMotionEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "screenOrientation"))
        Module.screenOrientation = function() {
          abort("'screenOrientation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillOrientationChangeEventData"))
        Module.fillOrientationChangeEventData = function() {
          abort("'fillOrientationChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerOrientationChangeEventCallback"))
        Module.registerOrientationChangeEventCallback = function() {
          abort("'registerOrientationChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillFullscreenChangeEventData"))
        Module.fillFullscreenChangeEventData = function() {
          abort("'fillFullscreenChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerFullscreenChangeEventCallback"))
        Module.registerFullscreenChangeEventCallback = function() {
          abort("'registerFullscreenChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerRestoreOldStyle"))
        Module.registerRestoreOldStyle = function() {
          abort("'registerRestoreOldStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "hideEverythingExceptGivenElement"))
        Module.hideEverythingExceptGivenElement = function() {
          abort("'hideEverythingExceptGivenElement' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "restoreHiddenElements"))
        Module.restoreHiddenElements = function() {
          abort("'restoreHiddenElements' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setLetterbox"))
        Module.setLetterbox = function() {
          abort("'setLetterbox' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "currentFullscreenStrategy"))
        Module.currentFullscreenStrategy = function() {
          abort("'currentFullscreenStrategy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "restoreOldWindowedStyle"))
        Module.restoreOldWindowedStyle = function() {
          abort("'restoreOldWindowedStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "softFullscreenResizeWebGLRenderTarget"))
        Module.softFullscreenResizeWebGLRenderTarget = function() {
          abort("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "doRequestFullscreen"))
        Module.doRequestFullscreen = function() {
          abort("'doRequestFullscreen' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillPointerlockChangeEventData"))
        Module.fillPointerlockChangeEventData = function() {
          abort("'fillPointerlockChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerPointerlockChangeEventCallback"))
        Module.registerPointerlockChangeEventCallback = function() {
          abort("'registerPointerlockChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerPointerlockErrorEventCallback"))
        Module.registerPointerlockErrorEventCallback = function() {
          abort("'registerPointerlockErrorEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "requestPointerLock"))
        Module.requestPointerLock = function() {
          abort("'requestPointerLock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillVisibilityChangeEventData"))
        Module.fillVisibilityChangeEventData = function() {
          abort("'fillVisibilityChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerVisibilityChangeEventCallback"))
        Module.registerVisibilityChangeEventCallback = function() {
          abort("'registerVisibilityChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerTouchEventCallback"))
        Module.registerTouchEventCallback = function() {
          abort("'registerTouchEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillGamepadEventData"))
        Module.fillGamepadEventData = function() {
          abort("'fillGamepadEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerGamepadEventCallback"))
        Module.registerGamepadEventCallback = function() {
          abort("'registerGamepadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerBeforeUnloadEventCallback"))
        Module.registerBeforeUnloadEventCallback = function() {
          abort("'registerBeforeUnloadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "fillBatteryEventData"))
        Module.fillBatteryEventData = function() {
          abort("'fillBatteryEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "battery"))
        Module.battery = function() {
          abort("'battery' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerBatteryEventCallback"))
        Module.registerBatteryEventCallback = function() {
          abort("'registerBatteryEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setCanvasElementSize"))
        Module.setCanvasElementSize = function() {
          abort("'setCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getCanvasElementSize"))
        Module.getCanvasElementSize = function() {
          abort("'getCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "polyfillSetImmediate"))
        Module.polyfillSetImmediate = function() {
          abort("'polyfillSetImmediate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "demangle"))
        Module.demangle = function() {
          abort("'demangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "demangleAll"))
        Module.demangleAll = function() {
          abort("'demangleAll' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "jsStackTrace"))
        Module.jsStackTrace = function() {
          abort("'jsStackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stackTrace"))
        Module.stackTrace = function() {
          abort("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getEnvStrings"))
        Module.getEnvStrings = function() {
          abort("'getEnvStrings' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "checkWasiClock"))
        Module.checkWasiClock = function() {
          abort("'checkWasiClock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeI53ToI64"))
        Module.writeI53ToI64 = function() {
          abort("'writeI53ToI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeI53ToI64Clamped"))
        Module.writeI53ToI64Clamped = function() {
          abort("'writeI53ToI64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeI53ToI64Signaling"))
        Module.writeI53ToI64Signaling = function() {
          abort("'writeI53ToI64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeI53ToU64Clamped"))
        Module.writeI53ToU64Clamped = function() {
          abort("'writeI53ToU64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeI53ToU64Signaling"))
        Module.writeI53ToU64Signaling = function() {
          abort("'writeI53ToU64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readI53FromI64"))
        Module.readI53FromI64 = function() {
          abort("'readI53FromI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readI53FromU64"))
        Module.readI53FromU64 = function() {
          abort("'readI53FromU64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "convertI32PairToI53"))
        Module.convertI32PairToI53 = function() {
          abort("'convertI32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "convertU32PairToI53"))
        Module.convertU32PairToI53 = function() {
          abort("'convertU32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "uncaughtExceptionCount"))
        Module.uncaughtExceptionCount = function() {
          abort("'uncaughtExceptionCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "exceptionLast"))
        Module.exceptionLast = function() {
          abort("'exceptionLast' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "exceptionCaught"))
        Module.exceptionCaught = function() {
          abort("'exceptionCaught' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ExceptionInfo"))
        Module.ExceptionInfo = function() {
          abort("'ExceptionInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "CatchInfo"))
        Module.CatchInfo = function() {
          abort("'CatchInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "exception_addRef"))
        Module.exception_addRef = function() {
          abort("'exception_addRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "exception_decRef"))
        Module.exception_decRef = function() {
          abort("'exception_decRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "Browser"))
        Module.Browser = function() {
          abort("'Browser' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "funcWrappers"))
        Module.funcWrappers = function() {
          abort("'funcWrappers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getFuncWrapper"))
        Module.getFuncWrapper = function() {
          abort("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setMainLoop"))
        Module.setMainLoop = function() {
          abort("'setMainLoop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "wget"))
        Module.wget = function() {
          abort("'wget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "FS"))
        Module.FS = function() {
          abort("'FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "MEMFS"))
        Module.MEMFS = function() {
          abort("'MEMFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "TTY"))
        Module.TTY = function() {
          abort("'TTY' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "PIPEFS"))
        Module.PIPEFS = function() {
          abort("'PIPEFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SOCKFS"))
        Module.SOCKFS = function() {
          abort("'SOCKFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "_setNetworkCallback"))
        Module._setNetworkCallback = function() {
          abort("'_setNetworkCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "tempFixedLengthArray"))
        Module.tempFixedLengthArray = function() {
          abort("'tempFixedLengthArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "miniTempWebGLFloatBuffers"))
        Module.miniTempWebGLFloatBuffers = function() {
          abort("'miniTempWebGLFloatBuffers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "heapObjectForWebGLType"))
        Module.heapObjectForWebGLType = function() {
          abort("'heapObjectForWebGLType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "heapAccessShiftForWebGLHeap"))
        Module.heapAccessShiftForWebGLHeap = function() {
          abort("'heapAccessShiftForWebGLHeap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      Module.GL = GL;
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGet"))
        Module.emscriptenWebGLGet = function() {
          abort("'emscriptenWebGLGet' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "computeUnpackAlignedImageSize"))
        Module.computeUnpackAlignedImageSize = function() {
          abort("'computeUnpackAlignedImageSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGetTexPixelData"))
        Module.emscriptenWebGLGetTexPixelData = function() {
          abort("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGetUniform"))
        Module.emscriptenWebGLGetUniform = function() {
          abort("'emscriptenWebGLGetUniform' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "webglGetUniformLocation"))
        Module.webglGetUniformLocation = function() {
          abort("'webglGetUniformLocation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "webglPrepareUniformLocationsBeforeFirstUse"))
        Module.webglPrepareUniformLocationsBeforeFirstUse = function() {
          abort("'webglPrepareUniformLocationsBeforeFirstUse' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "webglGetLeftBracePos"))
        Module.webglGetLeftBracePos = function() {
          abort("'webglGetLeftBracePos' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGetVertexAttrib"))
        Module.emscriptenWebGLGetVertexAttrib = function() {
          abort("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGetBufferBinding"))
        Module.emscriptenWebGLGetBufferBinding = function() {
          abort("'emscriptenWebGLGetBufferBinding' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLValidateMapBufferTarget"))
        Module.emscriptenWebGLValidateMapBufferTarget = function() {
          abort("'emscriptenWebGLValidateMapBufferTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "writeGLArray"))
        Module.writeGLArray = function() {
          abort("'writeGLArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "AL"))
        Module.AL = function() {
          abort("'AL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SDL_unicode"))
        Module.SDL_unicode = function() {
          abort("'SDL_unicode' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SDL_ttfContext"))
        Module.SDL_ttfContext = function() {
          abort("'SDL_ttfContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SDL_audio"))
        Module.SDL_audio = function() {
          abort("'SDL_audio' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SDL"))
        Module.SDL = function() {
          abort("'SDL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "SDL_gfx"))
        Module.SDL_gfx = function() {
          abort("'SDL_gfx' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "GLUT"))
        Module.GLUT = function() {
          abort("'GLUT' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "EGL"))
        Module.EGL = function() {
          abort("'EGL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "GLFW_Window"))
        Module.GLFW_Window = function() {
          abort("'GLFW_Window' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "GLFW"))
        Module.GLFW = function() {
          abort("'GLFW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "GLEW"))
        Module.GLEW = function() {
          abort("'GLEW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "IDBStore"))
        Module.IDBStore = function() {
          abort("'IDBStore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "runAndAbortIfError"))
        Module.runAndAbortIfError = function() {
          abort("'runAndAbortIfError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_handle_array"))
        Module.emval_handle_array = function() {
          abort("'emval_handle_array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_free_list"))
        Module.emval_free_list = function() {
          abort("'emval_free_list' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_symbols"))
        Module.emval_symbols = function() {
          abort("'emval_symbols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "init_emval"))
        Module.init_emval = function() {
          abort("'init_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "count_emval_handles"))
        Module.count_emval_handles = function() {
          abort("'count_emval_handles' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "get_first_emval"))
        Module.get_first_emval = function() {
          abort("'get_first_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getStringOrSymbol"))
        Module.getStringOrSymbol = function() {
          abort("'getStringOrSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "requireHandle"))
        Module.requireHandle = function() {
          abort("'requireHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_newers"))
        Module.emval_newers = function() {
          abort("'emval_newers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "craftEmvalAllocator"))
        Module.craftEmvalAllocator = function() {
          abort("'craftEmvalAllocator' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_get_global"))
        Module.emval_get_global = function() {
          abort("'emval_get_global' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emval_methodCallers"))
        Module.emval_methodCallers = function() {
          abort("'emval_methodCallers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "InternalError"))
        Module.InternalError = function() {
          abort("'InternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "BindingError"))
        Module.BindingError = function() {
          abort("'BindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UnboundTypeError"))
        Module.UnboundTypeError = function() {
          abort("'UnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "PureVirtualError"))
        Module.PureVirtualError = function() {
          abort("'PureVirtualError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "init_embind"))
        Module.init_embind = function() {
          abort("'init_embind' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "throwInternalError"))
        Module.throwInternalError = function() {
          abort("'throwInternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "throwBindingError"))
        Module.throwBindingError = function() {
          abort("'throwBindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "throwUnboundTypeError"))
        Module.throwUnboundTypeError = function() {
          abort("'throwUnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ensureOverloadTable"))
        Module.ensureOverloadTable = function() {
          abort("'ensureOverloadTable' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "exposePublicSymbol"))
        Module.exposePublicSymbol = function() {
          abort("'exposePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "replacePublicSymbol"))
        Module.replacePublicSymbol = function() {
          abort("'replacePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "extendError"))
        Module.extendError = function() {
          abort("'extendError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "createNamedFunction"))
        Module.createNamedFunction = function() {
          abort("'createNamedFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registeredInstances"))
        Module.registeredInstances = function() {
          abort("'registeredInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getBasestPointer"))
        Module.getBasestPointer = function() {
          abort("'getBasestPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerInheritedInstance"))
        Module.registerInheritedInstance = function() {
          abort("'registerInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "unregisterInheritedInstance"))
        Module.unregisterInheritedInstance = function() {
          abort("'unregisterInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getInheritedInstance"))
        Module.getInheritedInstance = function() {
          abort("'getInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getInheritedInstanceCount"))
        Module.getInheritedInstanceCount = function() {
          abort("'getInheritedInstanceCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getLiveInheritedInstances"))
        Module.getLiveInheritedInstances = function() {
          abort("'getLiveInheritedInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registeredTypes"))
        Module.registeredTypes = function() {
          abort("'registeredTypes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "awaitingDependencies"))
        Module.awaitingDependencies = function() {
          abort("'awaitingDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "typeDependencies"))
        Module.typeDependencies = function() {
          abort("'typeDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registeredPointers"))
        Module.registeredPointers = function() {
          abort("'registeredPointers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "registerType"))
        Module.registerType = function() {
          abort("'registerType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "whenDependentTypesAreResolved"))
        Module.whenDependentTypesAreResolved = function() {
          abort("'whenDependentTypesAreResolved' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "embind_charCodes"))
        Module.embind_charCodes = function() {
          abort("'embind_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "embind_init_charCodes"))
        Module.embind_init_charCodes = function() {
          abort("'embind_init_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "readLatin1String"))
        Module.readLatin1String = function() {
          abort("'readLatin1String' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getTypeName"))
        Module.getTypeName = function() {
          abort("'getTypeName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "heap32VectorToArray"))
        Module.heap32VectorToArray = function() {
          abort("'heap32VectorToArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "requireRegisteredType"))
        Module.requireRegisteredType = function() {
          abort("'requireRegisteredType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "getShiftFromSize"))
        Module.getShiftFromSize = function() {
          abort("'getShiftFromSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "integerReadValueFromPointer"))
        Module.integerReadValueFromPointer = function() {
          abort("'integerReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "enumReadValueFromPointer"))
        Module.enumReadValueFromPointer = function() {
          abort("'enumReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "floatReadValueFromPointer"))
        Module.floatReadValueFromPointer = function() {
          abort("'floatReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "simpleReadValueFromPointer"))
        Module.simpleReadValueFromPointer = function() {
          abort("'simpleReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "runDestructors"))
        Module.runDestructors = function() {
          abort("'runDestructors' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "new_"))
        Module.new_ = function() {
          abort("'new_' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "craftInvokerFunction"))
        Module.craftInvokerFunction = function() {
          abort("'craftInvokerFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "embind__requireFunction"))
        Module.embind__requireFunction = function() {
          abort("'embind__requireFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "tupleRegistrations"))
        Module.tupleRegistrations = function() {
          abort("'tupleRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "structRegistrations"))
        Module.structRegistrations = function() {
          abort("'structRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "genericPointerToWireType"))
        Module.genericPointerToWireType = function() {
          abort("'genericPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "constNoSmartPtrRawPointerToWireType"))
        Module.constNoSmartPtrRawPointerToWireType = function() {
          abort("'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "nonConstNoSmartPtrRawPointerToWireType"))
        Module.nonConstNoSmartPtrRawPointerToWireType = function() {
          abort("'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "init_RegisteredPointer"))
        Module.init_RegisteredPointer = function() {
          abort("'init_RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredPointer"))
        Module.RegisteredPointer = function() {
          abort("'RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredPointer_getPointee"))
        Module.RegisteredPointer_getPointee = function() {
          abort("'RegisteredPointer_getPointee' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredPointer_destructor"))
        Module.RegisteredPointer_destructor = function() {
          abort("'RegisteredPointer_destructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredPointer_deleteObject"))
        Module.RegisteredPointer_deleteObject = function() {
          abort("'RegisteredPointer_deleteObject' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredPointer_fromWireType"))
        Module.RegisteredPointer_fromWireType = function() {
          abort("'RegisteredPointer_fromWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "runDestructor"))
        Module.runDestructor = function() {
          abort("'runDestructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "releaseClassHandle"))
        Module.releaseClassHandle = function() {
          abort("'releaseClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "finalizationGroup"))
        Module.finalizationGroup = function() {
          abort("'finalizationGroup' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "detachFinalizer_deps"))
        Module.detachFinalizer_deps = function() {
          abort("'detachFinalizer_deps' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "detachFinalizer"))
        Module.detachFinalizer = function() {
          abort("'detachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "attachFinalizer"))
        Module.attachFinalizer = function() {
          abort("'attachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "makeClassHandle"))
        Module.makeClassHandle = function() {
          abort("'makeClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "init_ClassHandle"))
        Module.init_ClassHandle = function() {
          abort("'init_ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle"))
        Module.ClassHandle = function() {
          abort("'ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle_isAliasOf"))
        Module.ClassHandle_isAliasOf = function() {
          abort("'ClassHandle_isAliasOf' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "throwInstanceAlreadyDeleted"))
        Module.throwInstanceAlreadyDeleted = function() {
          abort("'throwInstanceAlreadyDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle_clone"))
        Module.ClassHandle_clone = function() {
          abort("'ClassHandle_clone' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle_delete"))
        Module.ClassHandle_delete = function() {
          abort("'ClassHandle_delete' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "deletionQueue"))
        Module.deletionQueue = function() {
          abort("'deletionQueue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle_isDeleted"))
        Module.ClassHandle_isDeleted = function() {
          abort("'ClassHandle_isDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "ClassHandle_deleteLater"))
        Module.ClassHandle_deleteLater = function() {
          abort("'ClassHandle_deleteLater' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "flushPendingDeletes"))
        Module.flushPendingDeletes = function() {
          abort("'flushPendingDeletes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "delayFunction"))
        Module.delayFunction = function() {
          abort("'delayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "setDelayFunction"))
        Module.setDelayFunction = function() {
          abort("'setDelayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "RegisteredClass"))
        Module.RegisteredClass = function() {
          abort("'RegisteredClass' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "shallowCopyInternalPointer"))
        Module.shallowCopyInternalPointer = function() {
          abort("'shallowCopyInternalPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "downcastPointer"))
        Module.downcastPointer = function() {
          abort("'downcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "upcastPointer"))
        Module.upcastPointer = function() {
          abort("'upcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "validateThis"))
        Module.validateThis = function() {
          abort("'validateThis' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "char_0"))
        Module.char_0 = function() {
          abort("'char_0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "char_9"))
        Module.char_9 = function() {
          abort("'char_9' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "makeLegalFunctionName"))
        Module.makeLegalFunctionName = function() {
          abort("'makeLegalFunctionName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "emscriptenWebGLGetIndexed"))
        Module.emscriptenWebGLGetIndexed = function() {
          abort("'emscriptenWebGLGetIndexed' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "warnOnce"))
        Module.warnOnce = function() {
          abort("'warnOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stackSave"))
        Module.stackSave = function() {
          abort("'stackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stackRestore"))
        Module.stackRestore = function() {
          abort("'stackRestore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stackAlloc"))
        Module.stackAlloc = function() {
          abort("'stackAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "AsciiToString"))
        Module.AsciiToString = function() {
          abort("'AsciiToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToAscii"))
        Module.stringToAscii = function() {
          abort("'stringToAscii' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UTF16ToString"))
        Module.UTF16ToString = function() {
          abort("'UTF16ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToUTF16"))
        Module.stringToUTF16 = function() {
          abort("'stringToUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "lengthBytesUTF16"))
        Module.lengthBytesUTF16 = function() {
          abort("'lengthBytesUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "UTF32ToString"))
        Module.UTF32ToString = function() {
          abort("'UTF32ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "stringToUTF32"))
        Module.stringToUTF32 = function() {
          abort("'stringToUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "lengthBytesUTF32"))
        Module.lengthBytesUTF32 = function() {
          abort("'lengthBytesUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "allocateUTF8"))
        Module.allocateUTF8 = function() {
          abort("'allocateUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      if (!Object.getOwnPropertyDescriptor(Module, "allocateUTF8OnStack"))
        Module.allocateUTF8OnStack = function() {
          abort("'allocateUTF8OnStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        };
      Module.writeStackCookie = writeStackCookie;
      Module.checkStackCookie = checkStackCookie;
      if (!Object.getOwnPropertyDescriptor(Module, "ALLOC_NORMAL"))
        Object.defineProperty(Module, "ALLOC_NORMAL", { configurable: true, get() {
          abort("'ALLOC_NORMAL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        } });
      if (!Object.getOwnPropertyDescriptor(Module, "ALLOC_STACK"))
        Object.defineProperty(Module, "ALLOC_STACK", { configurable: true, get() {
          abort("'ALLOC_STACK' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)");
        } });
      let calledRun;
      function ExitStatus(status) {
        this.name = "ExitStatus";
        this.message = `Program terminated with exit(${status})`;
        this.status = status;
      }
      dependenciesFulfilled = function runCaller() {
        if (!calledRun)
          run();
        if (!calledRun)
          dependenciesFulfilled = runCaller;
      };
      function stackCheckInit() {
        _emscripten_stack_init();
        writeStackCookie();
      }
      function run(args) {
        args = args || arguments_;
        if (runDependencies > 0) {
          return;
        }
        stackCheckInit();
        preRun();
        if (runDependencies > 0) {
          return;
        }
        function doRun() {
          if (calledRun)
            return;
          calledRun = true;
          Module.calledRun = true;
          if (ABORT)
            return;
          initRuntime();
          readyPromiseResolve(Module);
          if (Module.onRuntimeInitialized)
            Module.onRuntimeInitialized();
          assert(!Module._main, 'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]');
          postRun();
        }
        if (Module.setStatus) {
          Module.setStatus("Running...");
          setTimeout(() => {
            setTimeout(() => {
              Module.setStatus("");
            }, 1);
            doRun();
          }, 1);
        } else {
          doRun();
        }
        checkStackCookie();
      }
      Module.run = run;
      function checkUnflushedContent() {
        const oldOut = out;
        const oldErr = err;
        let has = false;
        out = err = function(x) {
          has = true;
        };
        try {
          const flush = Module._fflush;
          if (flush)
            flush(0);
          ["stdout", "stderr"].forEach((name) => {
            const info = FS.analyzePath(`/dev/${name}`);
            if (!info)
              return;
            const stream = info.object;
            const { rdev } = stream;
            const tty = TTY.ttys[rdev];
            if (tty && tty.output && tty.output.length) {
              has = true;
            }
          });
        } catch (e) {
        }
        out = oldOut;
        err = oldErr;
        if (has) {
          warnOnce("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.");
        }
      }
      function exit(status, implicit) {
        EXITSTATUS = status;
        checkUnflushedContent();
        if (keepRuntimeAlive()) {
          if (!implicit) {
            const msg = `program exited (with status: ${status}), but EXIT_RUNTIME is not set, so halting execution but not exiting the runtime or preventing further async execution (build with EXIT_RUNTIME=1, if you want a true shutdown)`;
            readyPromiseReject(msg);
            err(msg);
          }
        } else {
          exitRuntime();
        }
        procExit(status);
      }
      function procExit(code) {
        EXITSTATUS = code;
        if (!keepRuntimeAlive()) {
          if (Module.onExit)
            Module.onExit(code);
          ABORT = true;
        }
        quit_(code, new ExitStatus(code));
      }
      if (Module.preInit) {
        if (typeof Module.preInit === "function")
          Module.preInit = [Module.preInit];
        while (Module.preInit.length > 0) {
          Module.preInit.pop()();
        }
      }
      run();
      return createMediapipeSolutionsWasm2.ready;
    };
  }();
  var selfie_segmentation_solution_simd_wasm_bin_default = createMediapipeSolutionsWasm;

  // src/index.ts
  var vbSeq = 0;
  var VirtualBackground = class {
    constructor(core) {
      this.core = core;
      __publicField(this, "seq");
      __publicField(this, "_core");
      __publicField(this, "log");
      __publicField(this, "startResolve");
      __publicField(this, "startReject");
      vbSeq = vbSeq + 1;
      this.seq = vbSeq;
      this._core = core;
      this.log = core.log.createChild({ id: `${this.getAlias()}${vbSeq}` });
      this.log.info("created");
      this._core.room.videoManager.createMediapipeSolutionsWasm = selfie_segmentation_solution_simd_wasm_bin_default;
    }
    getName() {
      return "VirtualBackground";
    }
    getAlias() {
      return "vb";
    }
    getValidateRule(method) {
      switch (method) {
        case "start":
          return startValidateRule(this._core);
        case "update":
          return updateValidateRule(this._core);
        case "stop":
          return stopValidateRule(this._core);
      }
    }
    getGroup() {
      return "vb";
    }
    async start(options) {
      const { type = "blur", src } = options;
      const { auth, sign, timestamp } = await authEffect({
        sdkAppId: options.sdkAppId,
        userId: options.userId,
        userSig: options.userSig
      });
      if (!auth) {
        const { RtcError, ErrorCode, ErrorCodeDictionary } = this._core.errorModule;
        throw new RtcError({
          code: ErrorCode.SERVER_ERROR,
          extraCode: ErrorCodeDictionary.NEED_TO_BUY,
          messageParams: {
            value: "Virtual Background",
            url: "https://cloud.tencent.com/document/product/647/85386"
          }
        });
      }
      this.core.room.videoManager.setVirtualBackground({
        type,
        imageUrl: src
      });
    }
    async update(options) {
      const { type, src } = options;
      console.warn("new update");
      return this.core.room.videoManager.setVirtualBackground({
        type,
        imageUrl: src
      });
    }
    async stop() {
      return this.core.room.videoManager.setVirtualBackground();
    }
  };
  var src_default = VirtualBackground;
  return __toCommonJS(src_exports);
})().default;
//# sourceMappingURL=virtual-background.iife.js.map
