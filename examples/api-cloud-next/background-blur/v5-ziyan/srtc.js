"use strict";var TRTC=(()=>{var sh=Object.create;var Li=Object.defineProperty;var Sd=Object.getOwnPropertyDescriptor;var oh=Object.getOwnPropertyNames;var nh=Object.getPrototypeOf,ah=Object.prototype.hasOwnProperty;var ch=(r,e,t)=>e in r?Li(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var xi=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),it=(r,e)=>{for(var t in e)Li(r,t,{get:e[t],enumerable:!0})},Id=(r,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of oh(e))!ah.call(r,s)&&s!==t&&Li(r,s,{get:()=>e[s],enumerable:!(i=Sd(e,s))||i.enumerable});return r};var pe=(r,e,t)=>(t=r!=null?sh(nh(r)):{},Id(e||!r||!r.__esModule?Li(t,"default",{value:r,enumerable:!0}):t,r)),dh=r=>Id(Li({},"__esModule",{value:!0}),r),y=(r,e,t,i)=>{for(var s=i>1?void 0:i?Sd(e,t):e,o=r.length-1,n;o>=0;o--)(n=r[o])&&(s=(i?n(e,t,s):n(s))||s);return i&&s&&Li(e,t,s),s};var ae=(r,e,t)=>(ch(r,typeof e!="symbol"?e+"":e,t),t);var Zn=xi((a_,Qn)=>{"use strict";var S={};S.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)};S.localCName=S.generateIdentifier();S.splitLines=function(r){return r.trim().split(`
`).map(e=>e.trim())};S.splitSections=function(r){return r.split(`
m=`).map((t,i)=>(i>0?"m="+t:t).trim()+`\r
`)};S.getDescription=function(r){let e=S.splitSections(r);return e&&e[0]};S.getMediaSections=function(r){let e=S.splitSections(r);return e.shift(),e};S.matchPrefix=function(r,e){return S.splitLines(r).filter(t=>t.indexOf(e)===0)};S.parseCandidate=function(r){let e;r.indexOf("a=candidate:")===0?e=r.substring(12).split(" "):e=r.substring(10).split(" ");let t={foundation:e[0],component:{1:"rtp",2:"rtcp"}[e[1]]||e[1],protocol:e[2].toLowerCase(),priority:parseInt(e[3],10),ip:e[4],address:e[4],port:parseInt(e[5],10),type:e[7]};for(let i=8;i<e.length;i+=2)switch(e[i]){case"raddr":t.relatedAddress=e[i+1];break;case"rport":t.relatedPort=parseInt(e[i+1],10);break;case"tcptype":t.tcpType=e[i+1];break;case"ufrag":t.ufrag=e[i+1],t.usernameFragment=e[i+1];break;default:t[e[i]]===void 0&&(t[e[i]]=e[i+1]);break}return t};S.writeCandidate=function(r){let e=[];e.push(r.foundation);let t=r.component;t==="rtp"?e.push(1):t==="rtcp"?e.push(2):e.push(t),e.push(r.protocol.toUpperCase()),e.push(r.priority),e.push(r.address||r.ip),e.push(r.port);let i=r.type;return e.push("typ"),e.push(i),i!=="host"&&r.relatedAddress&&r.relatedPort&&(e.push("raddr"),e.push(r.relatedAddress),e.push("rport"),e.push(r.relatedPort)),r.tcpType&&r.protocol.toLowerCase()==="tcp"&&(e.push("tcptype"),e.push(r.tcpType)),(r.usernameFragment||r.ufrag)&&(e.push("ufrag"),e.push(r.usernameFragment||r.ufrag)),"candidate:"+e.join(" ")};S.parseIceOptions=function(r){return r.substring(14).split(" ")};S.parseRtpMap=function(r){let e=r.substring(9).split(" "),t={payloadType:parseInt(e.shift(),10)};return e=e[0].split("/"),t.name=e[0],t.clockRate=parseInt(e[1],10),t.channels=e.length===3?parseInt(e[2],10):1,t.numChannels=t.channels,t};S.writeRtpMap=function(r){let e=r.payloadType;r.preferredPayloadType!==void 0&&(e=r.preferredPayloadType);let t=r.channels||r.numChannels||1;return"a=rtpmap:"+e+" "+r.name+"/"+r.clockRate+(t!==1?"/"+t:"")+`\r
`};S.parseExtmap=function(r){let e=r.substring(9).split(" ");return{id:parseInt(e[0],10),direction:e[0].indexOf("/")>0?e[0].split("/")[1]:"sendrecv",uri:e[1],attributes:e.slice(2).join(" ")}};S.writeExtmap=function(r){return"a=extmap:"+(r.id||r.preferredId)+(r.direction&&r.direction!=="sendrecv"?"/"+r.direction:"")+" "+r.uri+(r.attributes?" "+r.attributes:"")+`\r
`};S.parseFmtp=function(r){let e={},t,i=r.substring(r.indexOf(" ")+1).split(";");for(let s=0;s<i.length;s++)t=i[s].trim().split("="),e[t[0].trim()]=t[1];return e};S.writeFmtp=function(r){let e="",t=r.payloadType;if(r.preferredPayloadType!==void 0&&(t=r.preferredPayloadType),r.parameters&&Object.keys(r.parameters).length){let i=[];Object.keys(r.parameters).forEach(s=>{r.parameters[s]!==void 0?i.push(s+"="+r.parameters[s]):i.push(s)}),e+="a=fmtp:"+t+" "+i.join(";")+`\r
`}return e};S.parseRtcpFb=function(r){let e=r.substring(r.indexOf(" ")+1).split(" ");return{type:e.shift(),parameter:e.join(" ")}};S.writeRtcpFb=function(r){let e="",t=r.payloadType;return r.preferredPayloadType!==void 0&&(t=r.preferredPayloadType),r.rtcpFeedback&&r.rtcpFeedback.length&&r.rtcpFeedback.forEach(i=>{e+="a=rtcp-fb:"+t+" "+i.type+(i.parameter&&i.parameter.length?" "+i.parameter:"")+`\r
`}),e};S.parseSsrcMedia=function(r){let e=r.indexOf(" "),t={ssrc:parseInt(r.substring(7,e),10)},i=r.indexOf(":",e);return i>-1?(t.attribute=r.substring(e+1,i),t.value=r.substring(i+1)):t.attribute=r.substring(e+1),t};S.parseSsrcGroup=function(r){let e=r.substring(13).split(" ");return{semantics:e.shift(),ssrcs:e.map(t=>parseInt(t,10))}};S.getMid=function(r){let e=S.matchPrefix(r,"a=mid:")[0];if(e)return e.substring(6)};S.parseFingerprint=function(r){let e=r.substring(14).split(" ");return{algorithm:e[0].toLowerCase(),value:e[1].toUpperCase()}};S.getDtlsParameters=function(r,e){let t=S.matchPrefix(r+e,"a=fingerprint:");return{role:"auto",fingerprints:t.map(S.parseFingerprint)}};S.writeDtlsParameters=function(r,e){let t="a=setup:"+e+`\r
`;return r.fingerprints.forEach(i=>{t+="a=fingerprint:"+i.algorithm+" "+i.value+`\r
`}),t};S.parseCryptoLine=function(r){let e=r.substring(9).split(" ");return{tag:parseInt(e[0],10),cryptoSuite:e[1],keyParams:e[2],sessionParams:e.slice(3)}};S.writeCryptoLine=function(r){return"a=crypto:"+r.tag+" "+r.cryptoSuite+" "+(typeof r.keyParams=="object"?S.writeCryptoKeyParams(r.keyParams):r.keyParams)+(r.sessionParams?" "+r.sessionParams.join(" "):"")+`\r
`};S.parseCryptoKeyParams=function(r){if(r.indexOf("inline:")!==0)return null;let e=r.substring(7).split("|");return{keyMethod:"inline",keySalt:e[0],lifeTime:e[1],mkiValue:e[2]?e[2].split(":")[0]:void 0,mkiLength:e[2]?e[2].split(":")[1]:void 0}};S.writeCryptoKeyParams=function(r){return r.keyMethod+":"+r.keySalt+(r.lifeTime?"|"+r.lifeTime:"")+(r.mkiValue&&r.mkiLength?"|"+r.mkiValue+":"+r.mkiLength:"")};S.getCryptoParameters=function(r,e){return S.matchPrefix(r+e,"a=crypto:").map(S.parseCryptoLine)};S.getIceParameters=function(r,e){let t=S.matchPrefix(r+e,"a=ice-ufrag:")[0],i=S.matchPrefix(r+e,"a=ice-pwd:")[0];return t&&i?{usernameFragment:t.substring(12),password:i.substring(10)}:null};S.writeIceParameters=function(r){let e="a=ice-ufrag:"+r.usernameFragment+`\r
a=ice-pwd:`+r.password+`\r
`;return r.iceLite&&(e+=`a=ice-lite\r
`),e};S.parseRtpParameters=function(r){let e={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=S.splitLines(r)[0].split(" ");e.profile=i[2];for(let o=3;o<i.length;o++){let n=i[o],a=S.matchPrefix(r,"a=rtpmap:"+n+" ")[0];if(a){let c=S.parseRtpMap(a),d=S.matchPrefix(r,"a=fmtp:"+n+" ");switch(c.parameters=d.length?S.parseFmtp(d[0]):{},c.rtcpFeedback=S.matchPrefix(r,"a=rtcp-fb:"+n+" ").map(S.parseRtcpFb),e.codecs.push(c),c.name.toUpperCase()){case"RED":case"ULPFEC":e.fecMechanisms.push(c.name.toUpperCase());break;default:break}}}S.matchPrefix(r,"a=extmap:").forEach(o=>{e.headerExtensions.push(S.parseExtmap(o))});let s=S.matchPrefix(r,"a=rtcp-fb:* ").map(S.parseRtcpFb);return e.codecs.forEach(o=>{s.forEach(n=>{o.rtcpFeedback.find(c=>c.type===n.type&&c.parameter===n.parameter)||o.rtcpFeedback.push(n)})}),e};S.writeRtpDescription=function(r,e){let t="";t+="m="+r+" ",t+=e.codecs.length>0?"9":"0",t+=" "+(e.profile||"UDP/TLS/RTP/SAVPF")+" ",t+=e.codecs.map(s=>s.preferredPayloadType!==void 0?s.preferredPayloadType:s.payloadType).join(" ")+`\r
`,t+=`c=IN IP4 0.0.0.0\r
`,t+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,e.codecs.forEach(s=>{t+=S.writeRtpMap(s),t+=S.writeFmtp(s),t+=S.writeRtcpFb(s)});let i=0;return e.codecs.forEach(s=>{s.maxptime>i&&(i=s.maxptime)}),i>0&&(t+="a=maxptime:"+i+`\r
`),e.headerExtensions&&e.headerExtensions.forEach(s=>{t+=S.writeExtmap(s)}),t};S.parseRtpEncodingParameters=function(r){let e=[],t=S.parseRtpParameters(r),i=t.fecMechanisms.indexOf("RED")!==-1,s=t.fecMechanisms.indexOf("ULPFEC")!==-1,o=S.matchPrefix(r,"a=ssrc:").map(u=>S.parseSsrcMedia(u)).filter(u=>u.attribute==="cname"),n=o.length>0&&o[0].ssrc,a,c=S.matchPrefix(r,"a=ssrc-group:FID").map(u=>u.substring(17).split(" ").map(p=>parseInt(p,10)));c.length>0&&c[0].length>1&&c[0][0]===n&&(a=c[0][1]),t.codecs.forEach(u=>{if(u.name.toUpperCase()==="RTX"&&u.parameters.apt){let h={ssrc:n,codecPayloadType:parseInt(u.parameters.apt,10)};n&&a&&(h.rtx={ssrc:a}),e.push(h),i&&(h=JSON.parse(JSON.stringify(h)),h.fec={ssrc:n,mechanism:s?"red+ulpfec":"red"},e.push(h))}}),e.length===0&&n&&e.push({ssrc:n});let d=S.matchPrefix(r,"b=");return d.length&&(d[0].indexOf("b=TIAS:")===0?d=parseInt(d[0].substring(7),10):d[0].indexOf("b=AS:")===0?d=parseInt(d[0].substring(5),10)*1e3*.95-50*40*8:d=void 0,e.forEach(u=>{u.maxBitrate=d})),e};S.parseRtcpParameters=function(r){let e={},t=S.matchPrefix(r,"a=ssrc:").map(o=>S.parseSsrcMedia(o)).filter(o=>o.attribute==="cname")[0];t&&(e.cname=t.value,e.ssrc=t.ssrc);let i=S.matchPrefix(r,"a=rtcp-rsize");e.reducedSize=i.length>0,e.compound=i.length===0;let s=S.matchPrefix(r,"a=rtcp-mux");return e.mux=s.length>0,e};S.writeRtcpParameters=function(r){let e="";return r.reducedSize&&(e+=`a=rtcp-rsize\r
`),r.mux&&(e+=`a=rtcp-mux\r
`),r.ssrc!==void 0&&r.cname&&(e+="a=ssrc:"+r.ssrc+" cname:"+r.cname+`\r
`),e};S.parseMsid=function(r){let e,t=S.matchPrefix(r,"a=msid:");if(t.length===1)return e=t[0].substring(7).split(" "),{stream:e[0],track:e[1]};let i=S.matchPrefix(r,"a=ssrc:").map(s=>S.parseSsrcMedia(s)).filter(s=>s.attribute==="msid");if(i.length>0)return e=i[0].value.split(" "),{stream:e[0],track:e[1]}};S.parseSctpDescription=function(r){let e=S.parseMLine(r),t=S.matchPrefix(r,"a=max-message-size:"),i;t.length>0&&(i=parseInt(t[0].substring(19),10)),isNaN(i)&&(i=65536);let s=S.matchPrefix(r,"a=sctp-port:");if(s.length>0)return{port:parseInt(s[0].substring(12),10),protocol:e.fmt,maxMessageSize:i};let o=S.matchPrefix(r,"a=sctpmap:");if(o.length>0){let n=o[0].substring(10).split(" ");return{port:parseInt(n[0],10),protocol:n[1],maxMessageSize:i}}};S.writeSctpDescription=function(r,e){let t=[];return r.protocol!=="DTLS/SCTP"?t=["m="+r.kind+" 9 "+r.protocol+" "+e.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+e.port+`\r
`]:t=["m="+r.kind+" 9 "+r.protocol+" "+e.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+e.port+" "+e.protocol+` 65535\r
`],e.maxMessageSize!==void 0&&t.push("a=max-message-size:"+e.maxMessageSize+`\r
`),t.join("")};S.generateSessionId=function(){return Math.random().toString().substr(2,22)};S.writeSessionBoilerplate=function(r,e,t){let i,s=e!==void 0?e:2;return r?i=r:i=S.generateSessionId(),`v=0\r
o=`+(t||"thisisadapterortc")+" "+i+" "+s+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`};S.getDirection=function(r,e){let t=S.splitLines(r);for(let i=0;i<t.length;i++)switch(t[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return t[i].substring(2);default:}return e?S.getDirection(e):"sendrecv"};S.getKind=function(r){return S.splitLines(r)[0].split(" ")[0].substring(2)};S.isRejected=function(r){return r.split(" ",2)[1]==="0"};S.parseMLine=function(r){let t=S.splitLines(r)[0].substring(2).split(" ");return{kind:t[0],port:parseInt(t[1],10),protocol:t[2],fmt:t.slice(3).join(" ")}};S.parseOLine=function(r){let t=S.matchPrefix(r,"o=")[0].substring(2).split(" ");return{username:t[0],sessionId:t[1],sessionVersion:parseInt(t[2],10),netType:t[3],addressType:t[4],address:t[5]}};S.isValidSDP=function(r){if(typeof r!="string"||r.length===0)return!1;let e=S.splitLines(r);for(let t=0;t<e.length;t++)if(e[t].length<2||e[t].charAt(1)!=="=")return!1;return!0};typeof Qn=="object"&&(Qn.exports=S)});var ve=xi((h_,ta)=>{"use strict";var lh=Object.prototype.hasOwnProperty,Ee="~";function vr(){}Object.create&&(vr.prototype=Object.create(null),new vr().__proto__||(Ee=!1));function hh(r,e,t){this.fn=r,this.context=e,this.once=t||!1}function Ld(r,e,t,i,s){if(typeof t!="function")throw new TypeError("The listener must be a function");var o=new hh(t,i||r,s),n=Ee?Ee+e:e;return r._events[n]?r._events[n].fn?r._events[n]=[r._events[n],o]:r._events[n].push(o):(r._events[n]=o,r._eventsCount++),r}function Ks(r,e){--r._eventsCount===0?r._events=new vr:delete r._events[e]}function me(){this._events=new vr,this._eventsCount=0}me.prototype.eventNames=function(){var e=[],t,i;if(this._eventsCount===0)return e;for(i in t=this._events)lh.call(t,i)&&e.push(Ee?i.slice(1):i);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e};me.prototype.listeners=function(e){var t=Ee?Ee+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var s=0,o=i.length,n=new Array(o);s<o;s++)n[s]=i[s].fn;return n};me.prototype.listenerCount=function(e){var t=Ee?Ee+e:e,i=this._events[t];return i?i.fn?1:i.length:0};me.prototype.emit=function(e,t,i,s,o,n){var a=Ee?Ee+e:e;if(!this._events[a])return!1;var c=this._events[a],d=arguments.length,u,h;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,i),!0;case 4:return c.fn.call(c.context,t,i,s),!0;case 5:return c.fn.call(c.context,t,i,s,o),!0;case 6:return c.fn.call(c.context,t,i,s,o,n),!0}for(h=1,u=new Array(d-1);h<d;h++)u[h-1]=arguments[h];c.fn.apply(c.context,u)}else{var p=c.length,g;for(h=0;h<p;h++)switch(c[h].once&&this.removeListener(e,c[h].fn,void 0,!0),d){case 1:c[h].fn.call(c[h].context);break;case 2:c[h].fn.call(c[h].context,t);break;case 3:c[h].fn.call(c[h].context,t,i);break;case 4:c[h].fn.call(c[h].context,t,i,s);break;default:if(!u)for(g=1,u=new Array(d-1);g<d;g++)u[g-1]=arguments[g];c[h].fn.apply(c[h].context,u)}}return!0};me.prototype.on=function(e,t,i){return Ld(this,e,t,i,!1)};me.prototype.once=function(e,t,i){return Ld(this,e,t,i,!0)};me.prototype.removeListener=function(e,t,i,s){var o=Ee?Ee+e:e;if(!this._events[o])return this;if(!t)return Ks(this,o),this;var n=this._events[o];if(n.fn)n.fn===t&&(!s||n.once)&&(!i||n.context===i)&&Ks(this,o);else{for(var a=0,c=[],d=n.length;a<d;a++)(n[a].fn!==t||s&&!n[a].once||i&&n[a].context!==i)&&c.push(n[a]);c.length?this._events[o]=c.length===1?c[0]:c:Ks(this,o)}return this};me.prototype.removeAllListeners=function(e){var t;return e?(t=Ee?Ee+e:e,this._events[t]&&Ks(this,t)):(this._events=new vr,this._eventsCount=0),this};me.prototype.off=me.prototype.removeListener;me.prototype.addListener=me.prototype.on;me.prefixed=Ee;me.EventEmitter=me;typeof ta<"u"&&(ta.exports=me)});var ed=xi((Kb,Rl)=>{var Il=Rl.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(r){return r.encoding?"rtpmap:%d %s/%s/%s":r.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(r){return r.address!=null?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%d trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(r){return r.subtype!=null?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(r){return"extmap:%d"+(r.direction?"/%s":"%v")+(r["encrypt-uri"]?" %s":"%v")+" %s"+(r.config?" %s":"")}},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(r){return r.sessionConfig!=null?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(r){var e="candidate:%s %d %s %d %s %d typ %s";return e+=r.raddr!=null?" raddr %s rport %d":"%v%v",e+=r.tcptype!=null?" tcptype %s":"%v",r.generation!=null&&(e+=" generation %d"),e+=r["network-id"]!=null?" network-id %d":"%v",e+=r["network-cost"]!=null?" network-cost %d":"%v",e}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(r){var e="ssrc:%d";return r.attribute!=null&&(e+=" %s",r.value!=null&&(e+=":%s")),e}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(r){return r.maxMessageSize!=null?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(r){return r.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(r){return"imageattr:%s %s %s"+(r.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(r){return"simulcast:%s %s"+(r.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"invalid",names:["value"]}]};Object.keys(Il).forEach(function(r){var e=Il[r];e.forEach(function(t){t.reg||(t.reg=/(.*)/),t.format||(t.format="%s")})})});var yl=xi(Ut=>{var Tr=function(r){return String(Number(r))===r?Number(r):r},Tm=function(r,e,t,i){if(i&&!t)e[i]=Tr(r[1]);else for(var s=0;s<t.length;s+=1)r[s+1]!=null&&(e[t[s]]=Tr(r[s+1]))},Em=function(r,e,t){var i=r.name&&r.names;r.push&&!e[r.push]?e[r.push]=[]:i&&!e[r.name]&&(e[r.name]={});var s=r.push?{}:i?e[r.name]:e;Tm(t.match(r.reg),s,r.names,r.name),r.push&&e[r.push].push(s)},Al=ed(),gm=RegExp.prototype.test.bind(/^([a-z])=(.*)/);Ut.parse=function(r){var e={},t=[],i=e;return r.split(/(\r\n|\r|\n)/).filter(gm).forEach(function(s){var o=s[0],n=s.slice(2);o==="m"&&(t.push({rtp:[],fmtp:[]}),i=t[t.length-1]);for(var a=0;a<(Al[o]||[]).length;a+=1){var c=Al[o][a];if(c.reg.test(n))return Em(c,i,n)}}),e.media=t,e};var Cl=function(r,e){var t=e.split(/=(.+)/,2);return t.length===2?r[t[0]]=Tr(t[1]):t.length===1&&e.length>1&&(r[t[0]]=void 0),r};Ut.parseParams=function(r){return r.split(/;\s?/).reduce(Cl,{})};Ut.parseFmtpConfig=Ut.parseParams;Ut.parsePayloads=function(r){return r.toString().split(" ").map(Number)};Ut.parseRemoteCandidates=function(r){for(var e=[],t=r.split(" ").map(Tr),i=0;i<t.length;i+=3)e.push({component:t[i],ip:t[i+1],port:t[i+2]});return e};Ut.parseImageAttributes=function(r){return r.split(" ").map(function(e){return e.substring(1,e.length-1).split(",").reduce(Cl,{})})};Ut.parseSimulcastStreamList=function(r){return r.split(";").map(function(e){return e.split(",").map(function(t){var i,s=!1;return t[0]!=="~"?i=Tr(t):(i=Tr(t.substring(1,t.length)),s=!0),{scid:i,paused:s}})})}});var Nl=xi((Yb,bl)=>{var td=ed(),Sm=/%[sdv%]/g,Im=function(r){var e=1,t=arguments,i=t.length;return r.replace(Sm,function(s){if(e>=i)return s;var o=t[e];switch(e+=1,s){case"%%":return"%";case"%s":return String(o);case"%d":return Number(o);case"%v":return""}})},Cs=function(r,e,t){var i=e.format instanceof Function?e.format(e.push?t:t[e.name]):e.format,s=[r+"="+i];if(e.names)for(var o=0;o<e.names.length;o+=1){var n=e.names[o];e.name?s.push(t[e.name][n]):s.push(t[e.names[o]])}else s.push(t[e.name]);return Im.apply(null,s)},Rm=["v","o","s","i","u","e","p","c","b","t","r","z","a"],Am=["i","c","b","a"];bl.exports=function(r,e){e=e||{},r.version==null&&(r.version=0),r.name==null&&(r.name=" "),r.media.forEach(function(o){o.payloads==null&&(o.payloads="")});var t=e.outerOrder||Rm,i=e.innerOrder||Am,s=[];return t.forEach(function(o){td[o].forEach(function(n){n.name in r&&r[n.name]!=null?s.push(Cs(o,n,r)):n.push in r&&r[n.push]!=null&&r[n.push].forEach(function(a){s.push(Cs(o,n,a))})})}),r.media.forEach(function(o){s.push(Cs("m",td.m[0],o)),i.forEach(function(n){td[n].forEach(function(a){a.name in o&&o[a.name]!=null?s.push(Cs(n,a,o)):a.push in o&&o[a.push]!=null&&o[a.push].forEach(function(c){s.push(Cs(n,a,c))})})})}),s.join(`\r
`)+`\r
`}});var id=xi(wt=>{var vi=yl(),Cm=Nl();wt.write=Cm;wt.parse=vi.parse;wt.parseFmtpConfig=vi.parseFmtpConfig;wt.parseParams=vi.parseParams;wt.parsePayloads=vi.parsePayloads;wt.parseRemoteCandidates=vi.parseRemoteCandidates;wt.parseImageAttributes=vi.parseImageAttributes;wt.parseSimulcastStreamList=vi.parseSimulcastStreamList});var qm={};it(qm,{default:()=>Xm});var Ad=!0,Cd=!0;function Rr(r,e,t){let i=r.match(e);return i&&i.length>=t&&parseInt(i[t],10)}function rt(r,e,t){if(!r.RTCPeerConnection)return;let i=r.RTCPeerConnection.prototype,s=i.addEventListener;i.addEventListener=function(n,a){if(n!==e)return s.apply(this,arguments);let c=d=>{let u=t(d);u&&(a.handleEvent?a.handleEvent(u):a(u))};return this._eventMap=this._eventMap||{},this._eventMap[e]||(this._eventMap[e]=new Map),this._eventMap[e].set(a,c),s.apply(this,[n,c])};let o=i.removeEventListener;i.removeEventListener=function(n,a){if(n!==e||!this._eventMap||!this._eventMap[e])return o.apply(this,arguments);if(!this._eventMap[e].has(a))return o.apply(this,arguments);let c=this._eventMap[e].get(a);return this._eventMap[e].delete(a),this._eventMap[e].size===0&&delete this._eventMap[e],Object.keys(this._eventMap).length===0&&delete this._eventMap,o.apply(this,[n,c])},Object.defineProperty(i,"on"+e,{get(){return this["_on"+e]},set(n){this["_on"+e]&&(this.removeEventListener(e,this["_on"+e]),delete this["_on"+e]),n&&this.addEventListener(e,this["_on"+e]=n)},enumerable:!0,configurable:!0})}function yd(r){return typeof r!="boolean"?new Error("Argument type: "+typeof r+". Please use a boolean."):(Ad=r,r?"adapter.js logging disabled":"adapter.js logging enabled")}function bd(r){return typeof r!="boolean"?new Error("Argument type: "+typeof r+". Please use a boolean."):(Cd=!r,"adapter.js deprecation warnings "+(r?"disabled":"enabled"))}function xs(){if(typeof window=="object"){if(Ad)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function Vi(r,e){!Cd||console.warn(r+" is deprecated, please use "+e+" instead.")}function Nd(r){let e={browser:null,version:null};if(typeof r>"u"||!r.navigator||!r.navigator.userAgent)return e.browser="Not a browser.",e;let{navigator:t}=r;if(t.mozGetUserMedia)e.browser="firefox",e.version=Rr(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia||r.isSecureContext===!1&&r.webkitRTCPeerConnection)e.browser="chrome",e.version=Rr(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(r.RTCPeerConnection&&t.userAgent.match(/AppleWebKit\/(\d+)\./))e.browser="safari",e.version=Rr(t.userAgent,/AppleWebKit\/(\d+)\./,1),e.supportsUnifiedPlan=r.RTCRtpTransceiver&&"currentDirection"in r.RTCRtpTransceiver.prototype;else return e.browser="Not a supported browser.",e;return e}function Rd(r){return Object.prototype.toString.call(r)==="[object Object]"}function bn(r){return Rd(r)?Object.keys(r).reduce(function(e,t){let i=Rd(r[t]),s=i?bn(r[t]):r[t],o=i&&!Object.keys(s).length;return s===void 0||o?e:Object.assign(e,{[t]:s})},{}):r}function yn(r,e,t){!e||t.has(e.id)||(t.set(e.id,e),Object.keys(e).forEach(i=>{i.endsWith("Id")?yn(r,r.get(e[i]),t):i.endsWith("Ids")&&e[i].forEach(s=>{yn(r,r.get(s),t)})}))}function Nn(r,e,t){let i=t?"outbound-rtp":"inbound-rtp",s=new Map;if(e===null)return s;let o=[];return r.forEach(n=>{n.type==="track"&&n.trackIdentifier===e.id&&o.push(n)}),o.forEach(n=>{r.forEach(a=>{a.type===i&&a.trackId===n.id&&yn(r,a,s)})}),s}var ws={};it(ws,{fixNegotiationNeeded:()=>Ln,shimAddTrackRemoveTrack:()=>Mn,shimAddTrackRemoveTrackWithNative:()=>Od,shimGetDisplayMedia:()=>Dd,shimGetSendersWithDtmf:()=>On,shimGetStats:()=>kn,shimGetUserMedia:()=>Vs,shimMediaStream:()=>vn,shimOnTrack:()=>Dn,shimPeerConnection:()=>Us,shimSenderReceiverGetStats:()=>Pn});var vd=xs;function Vs(r,e){let t=r&&r.navigator;if(!t.mediaDevices)return;let i=function(a){if(typeof a!="object"||a.mandatory||a.optional)return a;let c={};return Object.keys(a).forEach(d=>{if(d==="require"||d==="advanced"||d==="mediaSource")return;let u=typeof a[d]=="object"?a[d]:{ideal:a[d]};u.exact!==void 0&&typeof u.exact=="number"&&(u.min=u.max=u.exact);let h=function(p,g){return p?p+g.charAt(0).toUpperCase()+g.slice(1):g==="deviceId"?"sourceId":g};if(u.ideal!==void 0){c.optional=c.optional||[];let p={};typeof u.ideal=="number"?(p[h("min",d)]=u.ideal,c.optional.push(p),p={},p[h("max",d)]=u.ideal,c.optional.push(p)):(p[h("",d)]=u.ideal,c.optional.push(p))}u.exact!==void 0&&typeof u.exact!="number"?(c.mandatory=c.mandatory||{},c.mandatory[h("",d)]=u.exact):["min","max"].forEach(p=>{u[p]!==void 0&&(c.mandatory=c.mandatory||{},c.mandatory[h(p,d)]=u[p])})}),a.advanced&&(c.optional=(c.optional||[]).concat(a.advanced)),c},s=function(a,c){if(e.version>=61)return c(a);if(a=JSON.parse(JSON.stringify(a)),a&&typeof a.audio=="object"){let d=function(u,h,p){h in u&&!(p in u)&&(u[p]=u[h],delete u[h])};a=JSON.parse(JSON.stringify(a)),d(a.audio,"autoGainControl","googAutoGainControl"),d(a.audio,"noiseSuppression","googNoiseSuppression"),a.audio=i(a.audio)}if(a&&typeof a.video=="object"){let d=a.video.facingMode;d=d&&(typeof d=="object"?d:{ideal:d});let u=e.version<66;if(d&&(d.exact==="user"||d.exact==="environment"||d.ideal==="user"||d.ideal==="environment")&&!(t.mediaDevices.getSupportedConstraints&&t.mediaDevices.getSupportedConstraints().facingMode&&!u)){delete a.video.facingMode;let h;if(d.exact==="environment"||d.ideal==="environment"?h=["back","rear"]:(d.exact==="user"||d.ideal==="user")&&(h=["front"]),h)return t.mediaDevices.enumerateDevices().then(p=>{p=p.filter(C=>C.kind==="videoinput");let g=p.find(C=>h.some(N=>C.label.toLowerCase().includes(N)));return!g&&p.length&&h.includes("back")&&(g=p[p.length-1]),g&&(a.video.deviceId=d.exact?{exact:g.deviceId}:{ideal:g.deviceId}),a.video=i(a.video),vd("chrome: "+JSON.stringify(a)),c(a)})}a.video=i(a.video)}return vd("chrome: "+JSON.stringify(a)),c(a)},o=function(a){return e.version>=64?a:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[a.name]||a.name,message:a.message,constraint:a.constraint||a.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},n=function(a,c,d){s(a,u=>{t.webkitGetUserMedia(u,c,h=>{d&&d(o(h))})})};if(t.getUserMedia=n.bind(t),t.mediaDevices.getUserMedia){let a=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(c){return s(c,d=>a(d).then(u=>{if(d.audio&&!u.getAudioTracks().length||d.video&&!u.getVideoTracks().length)throw u.getTracks().forEach(h=>{h.stop()}),new DOMException("","NotFoundError");return u},u=>Promise.reject(o(u))))}}}function Dd(r,e){if(!(r.navigator.mediaDevices&&"getDisplayMedia"in r.navigator.mediaDevices)&&!!r.navigator.mediaDevices){if(typeof e!="function"){console.error("shimGetDisplayMedia: getSourceId argument is not a function");return}r.navigator.mediaDevices.getDisplayMedia=function(i){return e(i).then(s=>{let o=i.video&&i.video.width,n=i.video&&i.video.height,a=i.video&&i.video.frameRate;return i.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:s,maxFrameRate:a||3}},o&&(i.video.mandatory.maxWidth=o),n&&(i.video.mandatory.maxHeight=n),r.navigator.mediaDevices.getUserMedia(i)})}}}function vn(r){r.MediaStream=r.MediaStream||r.webkitMediaStream}function Dn(r){if(typeof r=="object"&&r.RTCPeerConnection&&!("ontrack"in r.RTCPeerConnection.prototype)){Object.defineProperty(r.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(t){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=t)},enumerable:!0,configurable:!0});let e=r.RTCPeerConnection.prototype.setRemoteDescription;r.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=i=>{i.stream.addEventListener("addtrack",s=>{let o;r.RTCPeerConnection.prototype.getReceivers?o=this.getReceivers().find(a=>a.track&&a.track.id===s.track.id):o={track:s.track};let n=new Event("track");n.track=s.track,n.receiver=o,n.transceiver={receiver:o},n.streams=[i.stream],this.dispatchEvent(n)}),i.stream.getTracks().forEach(s=>{let o;r.RTCPeerConnection.prototype.getReceivers?o=this.getReceivers().find(a=>a.track&&a.track.id===s.id):o={track:s};let n=new Event("track");n.track=s,n.receiver=o,n.transceiver={receiver:o},n.streams=[i.stream],this.dispatchEvent(n)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}else rt(r,"track",e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e))}function On(r){if(typeof r=="object"&&r.RTCPeerConnection&&!("getSenders"in r.RTCPeerConnection.prototype)&&"createDTMFSender"in r.RTCPeerConnection.prototype){let e=function(s,o){return{track:o,get dtmf(){return this._dtmf===void 0&&(o.kind==="audio"?this._dtmf=s.createDTMFSender(o):this._dtmf=null),this._dtmf},_pc:s}};if(!r.RTCPeerConnection.prototype.getSenders){r.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};let s=r.RTCPeerConnection.prototype.addTrack;r.RTCPeerConnection.prototype.addTrack=function(a,c){let d=s.apply(this,arguments);return d||(d=e(this,a),this._senders.push(d)),d};let o=r.RTCPeerConnection.prototype.removeTrack;r.RTCPeerConnection.prototype.removeTrack=function(a){o.apply(this,arguments);let c=this._senders.indexOf(a);c!==-1&&this._senders.splice(c,1)}}let t=r.RTCPeerConnection.prototype.addStream;r.RTCPeerConnection.prototype.addStream=function(o){this._senders=this._senders||[],t.apply(this,[o]),o.getTracks().forEach(n=>{this._senders.push(e(this,n))})};let i=r.RTCPeerConnection.prototype.removeStream;r.RTCPeerConnection.prototype.removeStream=function(o){this._senders=this._senders||[],i.apply(this,[o]),o.getTracks().forEach(n=>{let a=this._senders.find(c=>c.track===n);a&&this._senders.splice(this._senders.indexOf(a),1)})}}else if(typeof r=="object"&&r.RTCPeerConnection&&"getSenders"in r.RTCPeerConnection.prototype&&"createDTMFSender"in r.RTCPeerConnection.prototype&&r.RTCRtpSender&&!("dtmf"in r.RTCRtpSender.prototype)){let e=r.RTCPeerConnection.prototype.getSenders;r.RTCPeerConnection.prototype.getSenders=function(){let i=e.apply(this,[]);return i.forEach(s=>s._pc=this),i},Object.defineProperty(r.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function kn(r){if(!r.RTCPeerConnection)return;let e=r.RTCPeerConnection.prototype.getStats;r.RTCPeerConnection.prototype.getStats=function(){let[i,s,o]=arguments;if(arguments.length>0&&typeof i=="function")return e.apply(this,arguments);if(e.length===0&&(arguments.length===0||typeof i!="function"))return e.apply(this,[]);let n=function(c){let d={};return c.result().forEach(h=>{let p={id:h.id,timestamp:h.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[h.type]||h.type};h.names().forEach(g=>{p[g]=h.stat(g)}),d[p.id]=p}),d},a=function(c){return new Map(Object.keys(c).map(d=>[d,c[d]]))};if(arguments.length>=2){let c=function(d){s(a(n(d)))};return e.apply(this,[c,i])}return new Promise((c,d)=>{e.apply(this,[function(u){c(a(n(u)))},d])}).then(s,o)}}function Pn(r){if(!(typeof r=="object"&&r.RTCPeerConnection&&r.RTCRtpSender&&r.RTCRtpReceiver))return;if(!("getStats"in r.RTCRtpSender.prototype)){let t=r.RTCPeerConnection.prototype.getSenders;t&&(r.RTCPeerConnection.prototype.getSenders=function(){let o=t.apply(this,[]);return o.forEach(n=>n._pc=this),o});let i=r.RTCPeerConnection.prototype.addTrack;i&&(r.RTCPeerConnection.prototype.addTrack=function(){let o=i.apply(this,arguments);return o._pc=this,o}),r.RTCRtpSender.prototype.getStats=function(){let o=this;return this._pc.getStats().then(n=>Nn(n,o.track,!0))}}if(!("getStats"in r.RTCRtpReceiver.prototype)){let t=r.RTCPeerConnection.prototype.getReceivers;t&&(r.RTCPeerConnection.prototype.getReceivers=function(){let s=t.apply(this,[]);return s.forEach(o=>o._pc=this),s}),rt(r,"track",i=>(i.receiver._pc=i.srcElement,i)),r.RTCRtpReceiver.prototype.getStats=function(){let s=this;return this._pc.getStats().then(o=>Nn(o,s.track,!1))}}if(!("getStats"in r.RTCRtpSender.prototype&&"getStats"in r.RTCRtpReceiver.prototype))return;let e=r.RTCPeerConnection.prototype.getStats;r.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof r.MediaStreamTrack){let i=arguments[0],s,o,n;return this.getSenders().forEach(a=>{a.track===i&&(s?n=!0:s=a)}),this.getReceivers().forEach(a=>(a.track===i&&(o?n=!0:o=a),a.track===i)),n||s&&o?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):s?s.getStats():o?o.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return e.apply(this,arguments)}}function Od(r){r.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(n=>this._shimmedLocalStreams[n][0])};let e=r.RTCPeerConnection.prototype.addTrack;r.RTCPeerConnection.prototype.addTrack=function(n,a){if(!a)return e.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};let c=e.apply(this,arguments);return this._shimmedLocalStreams[a.id]?this._shimmedLocalStreams[a.id].indexOf(c)===-1&&this._shimmedLocalStreams[a.id].push(c):this._shimmedLocalStreams[a.id]=[a,c],c};let t=r.RTCPeerConnection.prototype.addStream;r.RTCPeerConnection.prototype.addStream=function(n){this._shimmedLocalStreams=this._shimmedLocalStreams||{},n.getTracks().forEach(d=>{if(this.getSenders().find(h=>h.track===d))throw new DOMException("Track already exists.","InvalidAccessError")});let a=this.getSenders();t.apply(this,arguments);let c=this.getSenders().filter(d=>a.indexOf(d)===-1);this._shimmedLocalStreams[n.id]=[n].concat(c)};let i=r.RTCPeerConnection.prototype.removeStream;r.RTCPeerConnection.prototype.removeStream=function(n){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[n.id],i.apply(this,arguments)};let s=r.RTCPeerConnection.prototype.removeTrack;r.RTCPeerConnection.prototype.removeTrack=function(n){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},n&&Object.keys(this._shimmedLocalStreams).forEach(a=>{let c=this._shimmedLocalStreams[a].indexOf(n);c!==-1&&this._shimmedLocalStreams[a].splice(c,1),this._shimmedLocalStreams[a].length===1&&delete this._shimmedLocalStreams[a]}),s.apply(this,arguments)}}function Mn(r,e){if(!r.RTCPeerConnection)return;if(r.RTCPeerConnection.prototype.addTrack&&e.version>=65)return Od(r);let t=r.RTCPeerConnection.prototype.getLocalStreams;r.RTCPeerConnection.prototype.getLocalStreams=function(){let u=t.apply(this);return this._reverseStreams=this._reverseStreams||{},u.map(h=>this._reverseStreams[h.id])};let i=r.RTCPeerConnection.prototype.addStream;r.RTCPeerConnection.prototype.addStream=function(u){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},u.getTracks().forEach(h=>{if(this.getSenders().find(g=>g.track===h))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[u.id]){let h=new r.MediaStream(u.getTracks());this._streams[u.id]=h,this._reverseStreams[h.id]=u,u=h}i.apply(this,[u])};let s=r.RTCPeerConnection.prototype.removeStream;r.RTCPeerConnection.prototype.removeStream=function(u){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},s.apply(this,[this._streams[u.id]||u]),delete this._reverseStreams[this._streams[u.id]?this._streams[u.id].id:u.id],delete this._streams[u.id]},r.RTCPeerConnection.prototype.addTrack=function(u,h){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");let p=[].slice.call(arguments,1);if(p.length!==1||!p[0].getTracks().find(N=>N===u))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(N=>N.track===u))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};let C=this._streams[h.id];if(C)C.addTrack(u),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{let N=new r.MediaStream([u]);this._streams[h.id]=N,this._reverseStreams[N.id]=h,this.addStream(N)}return this.getSenders().find(N=>N.track===u)};function o(d,u){let h=u.sdp;return Object.keys(d._reverseStreams||[]).forEach(p=>{let g=d._reverseStreams[p],C=d._streams[g.id];h=h.replace(new RegExp(C.id,"g"),g.id)}),new RTCSessionDescription({type:u.type,sdp:h})}function n(d,u){let h=u.sdp;return Object.keys(d._reverseStreams||[]).forEach(p=>{let g=d._reverseStreams[p],C=d._streams[g.id];h=h.replace(new RegExp(g.id,"g"),C.id)}),new RTCSessionDescription({type:u.type,sdp:h})}["createOffer","createAnswer"].forEach(function(d){let u=r.RTCPeerConnection.prototype[d],h={[d](){let p=arguments;return arguments.length&&typeof arguments[0]=="function"?u.apply(this,[C=>{let N=o(this,C);p[0].apply(null,[N])},C=>{p[1]&&p[1].apply(null,C)},arguments[2]]):u.apply(this,arguments).then(C=>o(this,C))}};r.RTCPeerConnection.prototype[d]=h[d]});let a=r.RTCPeerConnection.prototype.setLocalDescription;r.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?a.apply(this,arguments):(arguments[0]=n(this,arguments[0]),a.apply(this,arguments))};let c=Object.getOwnPropertyDescriptor(r.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(r.RTCPeerConnection.prototype,"localDescription",{get(){let d=c.get.apply(this);return d.type===""?d:o(this,d)}}),r.RTCPeerConnection.prototype.removeTrack=function(u){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!u._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(u._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let p;Object.keys(this._streams).forEach(g=>{this._streams[g].getTracks().find(N=>u.track===N)&&(p=this._streams[g])}),p&&(p.getTracks().length===1?this.removeStream(this._reverseStreams[p.id]):p.removeTrack(u.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Us(r,e){!r.RTCPeerConnection&&r.webkitRTCPeerConnection&&(r.RTCPeerConnection=r.webkitRTCPeerConnection),!!r.RTCPeerConnection&&e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(t){let i=r.RTCPeerConnection.prototype[t],s={[t](){return arguments[0]=new(t==="addIceCandidate"?r.RTCIceCandidate:r.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}};r.RTCPeerConnection.prototype[t]=s[t]})}function Ln(r,e){rt(r,"negotiationneeded",t=>{let i=t.target;if(!((e.version<72||i.getConfiguration&&i.getConfiguration().sdpSemantics==="plan-b")&&i.signalingState!=="stable"))return t})}var Fs={};it(Fs,{shimAddTransceiver:()=>$n,shimCreateAnswer:()=>Gn,shimCreateOffer:()=>Hn,shimGetDisplayMedia:()=>kd,shimGetParameters:()=>Fn,shimGetUserMedia:()=>Bs,shimOnTrack:()=>xn,shimPeerConnection:()=>$s,shimRTCDataChannel:()=>Bn,shimReceiverGetStats:()=>Un,shimRemoveStream:()=>wn,shimSenderGetStats:()=>Vn});function Bs(r,e){let t=r&&r.navigator,i=r&&r.MediaStreamTrack;if(t.getUserMedia=function(s,o,n){Vi("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),t.mediaDevices.getUserMedia(s).then(o,n)},!(e.version>55&&"autoGainControl"in t.mediaDevices.getSupportedConstraints())){let s=function(n,a,c){a in n&&!(c in n)&&(n[c]=n[a],delete n[a])},o=t.mediaDevices.getUserMedia.bind(t.mediaDevices);if(t.mediaDevices.getUserMedia=function(n){return typeof n=="object"&&typeof n.audio=="object"&&(n=JSON.parse(JSON.stringify(n)),s(n.audio,"autoGainControl","mozAutoGainControl"),s(n.audio,"noiseSuppression","mozNoiseSuppression")),o(n)},i&&i.prototype.getSettings){let n=i.prototype.getSettings;i.prototype.getSettings=function(){let a=n.apply(this,arguments);return s(a,"mozAutoGainControl","autoGainControl"),s(a,"mozNoiseSuppression","noiseSuppression"),a}}if(i&&i.prototype.applyConstraints){let n=i.prototype.applyConstraints;i.prototype.applyConstraints=function(a){return this.kind==="audio"&&typeof a=="object"&&(a=JSON.parse(JSON.stringify(a)),s(a,"autoGainControl","mozAutoGainControl"),s(a,"noiseSuppression","mozNoiseSuppression")),n.apply(this,[a])}}}}function kd(r,e){r.navigator.mediaDevices&&"getDisplayMedia"in r.navigator.mediaDevices||!r.navigator.mediaDevices||(r.navigator.mediaDevices.getDisplayMedia=function(i){if(!(i&&i.video)){let s=new DOMException("getDisplayMedia without video constraints is undefined");return s.name="NotFoundError",s.code=8,Promise.reject(s)}return i.video===!0?i.video={mediaSource:e}:i.video.mediaSource=e,r.navigator.mediaDevices.getUserMedia(i)})}function xn(r){typeof r=="object"&&r.RTCTrackEvent&&"receiver"in r.RTCTrackEvent.prototype&&!("transceiver"in r.RTCTrackEvent.prototype)&&Object.defineProperty(r.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function $s(r,e){if(typeof r!="object"||!(r.RTCPeerConnection||r.mozRTCPeerConnection))return;!r.RTCPeerConnection&&r.mozRTCPeerConnection&&(r.RTCPeerConnection=r.mozRTCPeerConnection),e.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(s){let o=r.RTCPeerConnection.prototype[s],n={[s](){return arguments[0]=new(s==="addIceCandidate"?r.RTCIceCandidate:r.RTCSessionDescription)(arguments[0]),o.apply(this,arguments)}};r.RTCPeerConnection.prototype[s]=n[s]});let t={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},i=r.RTCPeerConnection.prototype.getStats;r.RTCPeerConnection.prototype.getStats=function(){let[o,n,a]=arguments;return i.apply(this,[o||null]).then(c=>{if(e.version<53&&!n)try{c.forEach(d=>{d.type=t[d.type]||d.type})}catch(d){if(d.name!=="TypeError")throw d;c.forEach((u,h)=>{c.set(h,Object.assign({},u,{type:t[u.type]||u.type}))})}return c}).then(n,a)}}function Vn(r){if(!(typeof r=="object"&&r.RTCPeerConnection&&r.RTCRtpSender)||r.RTCRtpSender&&"getStats"in r.RTCRtpSender.prototype)return;let e=r.RTCPeerConnection.prototype.getSenders;e&&(r.RTCPeerConnection.prototype.getSenders=function(){let s=e.apply(this,[]);return s.forEach(o=>o._pc=this),s});let t=r.RTCPeerConnection.prototype.addTrack;t&&(r.RTCPeerConnection.prototype.addTrack=function(){let s=t.apply(this,arguments);return s._pc=this,s}),r.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Un(r){if(!(typeof r=="object"&&r.RTCPeerConnection&&r.RTCRtpSender)||r.RTCRtpSender&&"getStats"in r.RTCRtpReceiver.prototype)return;let e=r.RTCPeerConnection.prototype.getReceivers;e&&(r.RTCPeerConnection.prototype.getReceivers=function(){let i=e.apply(this,[]);return i.forEach(s=>s._pc=this),i}),rt(r,"track",t=>(t.receiver._pc=t.srcElement,t)),r.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function wn(r){!r.RTCPeerConnection||"removeStream"in r.RTCPeerConnection.prototype||(r.RTCPeerConnection.prototype.removeStream=function(t){Vi("removeStream","removeTrack"),this.getSenders().forEach(i=>{i.track&&t.getTracks().includes(i.track)&&this.removeTrack(i)})})}function Bn(r){r.DataChannel&&!r.RTCDataChannel&&(r.RTCDataChannel=r.DataChannel)}function $n(r){if(!(typeof r=="object"&&r.RTCPeerConnection))return;let e=r.RTCPeerConnection.prototype.addTransceiver;e&&(r.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let i=arguments[1]&&arguments[1].sendEncodings;i===void 0&&(i=[]),i=[...i];let s=i.length>0;s&&i.forEach(n=>{if("rid"in n&&!/^[a-z0-9]{0,16}$/i.test(n.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in n&&!(parseFloat(n.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in n&&!(parseFloat(n.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});let o=e.apply(this,arguments);if(s){let{sender:n}=o,a=n.getParameters();(!("encodings"in a)||a.encodings.length===1&&Object.keys(a.encodings[0]).length===0)&&(a.encodings=i,n.sendEncodings=i,this.setParametersPromises.push(n.setParameters(a).then(()=>{delete n.sendEncodings}).catch(()=>{delete n.sendEncodings})))}return o})}function Fn(r){if(!(typeof r=="object"&&r.RTCRtpSender))return;let e=r.RTCRtpSender.prototype.getParameters;e&&(r.RTCRtpSender.prototype.getParameters=function(){let i=e.apply(this,arguments);return"encodings"in i||(i.encodings=[].concat(this.sendEncodings||[{}])),i})}function Hn(r){if(!(typeof r=="object"&&r.RTCPeerConnection))return;let e=r.RTCPeerConnection.prototype.createOffer;r.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}function Gn(r){if(!(typeof r=="object"&&r.RTCPeerConnection))return;let e=r.RTCPeerConnection.prototype.createAnswer;r.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>e.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):e.apply(this,arguments)}}var Hs={};it(Hs,{shimAudioContext:()=>qn,shimCallbacksAPI:()=>Kn,shimConstraints:()=>Pd,shimCreateOfferLegacy:()=>Xn,shimGetUserMedia:()=>Jn,shimLocalStreamsAPI:()=>Wn,shimRTCIceServerUrls:()=>Yn,shimRemoteStreamsAPI:()=>jn,shimTrackEventTransceiver:()=>zn});function Wn(r){if(!(typeof r!="object"||!r.RTCPeerConnection)){if("getLocalStreams"in r.RTCPeerConnection.prototype||(r.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in r.RTCPeerConnection.prototype)){let e=r.RTCPeerConnection.prototype.addTrack;r.RTCPeerConnection.prototype.addStream=function(i){this._localStreams||(this._localStreams=[]),this._localStreams.includes(i)||this._localStreams.push(i),i.getAudioTracks().forEach(s=>e.call(this,s,i)),i.getVideoTracks().forEach(s=>e.call(this,s,i))},r.RTCPeerConnection.prototype.addTrack=function(i,...s){return s&&s.forEach(o=>{this._localStreams?this._localStreams.includes(o)||this._localStreams.push(o):this._localStreams=[o]}),e.apply(this,arguments)}}"removeStream"in r.RTCPeerConnection.prototype||(r.RTCPeerConnection.prototype.removeStream=function(t){this._localStreams||(this._localStreams=[]);let i=this._localStreams.indexOf(t);if(i===-1)return;this._localStreams.splice(i,1);let s=t.getTracks();this.getSenders().forEach(o=>{s.includes(o.track)&&this.removeTrack(o)})})}}function jn(r){if(!(typeof r!="object"||!r.RTCPeerConnection)&&("getRemoteStreams"in r.RTCPeerConnection.prototype||(r.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in r.RTCPeerConnection.prototype))){Object.defineProperty(r.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(t){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=t),this.addEventListener("track",this._onaddstreampoly=i=>{i.streams.forEach(s=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(s))return;this._remoteStreams.push(s);let o=new Event("addstream");o.stream=s,this.dispatchEvent(o)})})}});let e=r.RTCPeerConnection.prototype.setRemoteDescription;r.RTCPeerConnection.prototype.setRemoteDescription=function(){let i=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(s){s.streams.forEach(o=>{if(i._remoteStreams||(i._remoteStreams=[]),i._remoteStreams.indexOf(o)>=0)return;i._remoteStreams.push(o);let n=new Event("addstream");n.stream=o,i.dispatchEvent(n)})}),e.apply(i,arguments)}}}function Kn(r){if(typeof r!="object"||!r.RTCPeerConnection)return;let e=r.RTCPeerConnection.prototype,t=e.createOffer,i=e.createAnswer,s=e.setLocalDescription,o=e.setRemoteDescription,n=e.addIceCandidate;e.createOffer=function(d,u){let h=arguments.length>=2?arguments[2]:arguments[0],p=t.apply(this,[h]);return u?(p.then(d,u),Promise.resolve()):p},e.createAnswer=function(d,u){let h=arguments.length>=2?arguments[2]:arguments[0],p=i.apply(this,[h]);return u?(p.then(d,u),Promise.resolve()):p};let a=function(c,d,u){let h=s.apply(this,[c]);return u?(h.then(d,u),Promise.resolve()):h};e.setLocalDescription=a,a=function(c,d,u){let h=o.apply(this,[c]);return u?(h.then(d,u),Promise.resolve()):h},e.setRemoteDescription=a,a=function(c,d,u){let h=n.apply(this,[c]);return u?(h.then(d,u),Promise.resolve()):h},e.addIceCandidate=a}function Jn(r){let e=r&&r.navigator;if(e.mediaDevices&&e.mediaDevices.getUserMedia){let t=e.mediaDevices,i=t.getUserMedia.bind(t);e.mediaDevices.getUserMedia=s=>i(Pd(s))}!e.getUserMedia&&e.mediaDevices&&e.mediaDevices.getUserMedia&&(e.getUserMedia=function(i,s,o){e.mediaDevices.getUserMedia(i).then(s,o)}.bind(e))}function Pd(r){return r&&r.video!==void 0?Object.assign({},r,{video:bn(r.video)}):r}function Yn(r){if(!r.RTCPeerConnection)return;let e=r.RTCPeerConnection;r.RTCPeerConnection=function(i,s){if(i&&i.iceServers){let o=[];for(let n=0;n<i.iceServers.length;n++){let a=i.iceServers[n];a.urls===void 0&&a.url?(Vi("RTCIceServer.url","RTCIceServer.urls"),a=JSON.parse(JSON.stringify(a)),a.urls=a.url,delete a.url,o.push(a)):o.push(i.iceServers[n])}i.iceServers=o}return new e(i,s)},r.RTCPeerConnection.prototype=e.prototype,"generateCertificate"in e&&Object.defineProperty(r.RTCPeerConnection,"generateCertificate",{get(){return e.generateCertificate}})}function zn(r){typeof r=="object"&&r.RTCTrackEvent&&"receiver"in r.RTCTrackEvent.prototype&&!("transceiver"in r.RTCTrackEvent.prototype)&&Object.defineProperty(r.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Xn(r){let e=r.RTCPeerConnection.prototype.createOffer;r.RTCPeerConnection.prototype.createOffer=function(i){if(i){typeof i.offerToReceiveAudio<"u"&&(i.offerToReceiveAudio=!!i.offerToReceiveAudio);let s=this.getTransceivers().find(n=>n.receiver.track.kind==="audio");i.offerToReceiveAudio===!1&&s?s.direction==="sendrecv"?s.setDirection?s.setDirection("sendonly"):s.direction="sendonly":s.direction==="recvonly"&&(s.setDirection?s.setDirection("inactive"):s.direction="inactive"):i.offerToReceiveAudio===!0&&!s&&this.addTransceiver("audio",{direction:"recvonly"}),typeof i.offerToReceiveVideo<"u"&&(i.offerToReceiveVideo=!!i.offerToReceiveVideo);let o=this.getTransceivers().find(n=>n.receiver.track.kind==="video");i.offerToReceiveVideo===!1&&o?o.direction==="sendrecv"?o.setDirection?o.setDirection("sendonly"):o.direction="sendonly":o.direction==="recvonly"&&(o.setDirection?o.setDirection("inactive"):o.direction="inactive"):i.offerToReceiveVideo===!0&&!o&&this.addTransceiver("video",{direction:"recvonly"})}return e.apply(this,arguments)}}function qn(r){typeof r!="object"||r.AudioContext||(r.AudioContext=r.webkitAudioContext)}var ea={};it(ea,{removeExtmapAllowMixed:()=>js,shimAddIceCandidateNullOrEmpty:()=>br,shimConnectionState:()=>Ws,shimMaxMessageSize:()=>Cr,shimParameterlessSetLocalDescription:()=>Nr,shimRTCIceCandidate:()=>Ar,shimRTCIceCandidateRelayProtocol:()=>Gs,shimSendThrowTypeError:()=>yr});var Ui=pe(Zn());function Ar(r){if(!r.RTCIceCandidate||r.RTCIceCandidate&&"foundation"in r.RTCIceCandidate.prototype)return;let e=r.RTCIceCandidate;r.RTCIceCandidate=function(i){if(typeof i=="object"&&i.candidate&&i.candidate.indexOf("a=")===0&&(i=JSON.parse(JSON.stringify(i)),i.candidate=i.candidate.substring(2)),i.candidate&&i.candidate.length){let s=new e(i),o=Ui.default.parseCandidate(i.candidate);for(let n in o)n in s||Object.defineProperty(s,n,{value:o[n]});return s.toJSON=function(){return{candidate:s.candidate,sdpMid:s.sdpMid,sdpMLineIndex:s.sdpMLineIndex,usernameFragment:s.usernameFragment}},s}return new e(i)},r.RTCIceCandidate.prototype=e.prototype,rt(r,"icecandidate",t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new r.RTCIceCandidate(t.candidate),writable:"false"}),t))}function Gs(r){!r.RTCIceCandidate||r.RTCIceCandidate&&"relayProtocol"in r.RTCIceCandidate.prototype||rt(r,"icecandidate",e=>{if(e.candidate){let t=Ui.default.parseCandidate(e.candidate.candidate);t.type==="relay"&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e})}function Cr(r,e){if(!r.RTCPeerConnection)return;"sctp"in r.RTCPeerConnection.prototype||Object.defineProperty(r.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});let t=function(a){if(!a||!a.sdp)return!1;let c=Ui.default.splitSections(a.sdp);return c.shift(),c.some(d=>{let u=Ui.default.parseMLine(d);return u&&u.kind==="application"&&u.protocol.indexOf("SCTP")!==-1})},i=function(a){let c=a.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(c===null||c.length<2)return-1;let d=parseInt(c[1],10);return d!==d?-1:d},s=function(a){let c=65536;return e.browser==="firefox"&&(e.version<57?a===-1?c=16384:c=2147483637:e.version<60?c=e.version===57?65535:65536:c=2147483637),c},o=function(a,c){let d=65536;e.browser==="firefox"&&e.version===57&&(d=65535);let u=Ui.default.matchPrefix(a.sdp,"a=max-message-size:");return u.length>0?d=parseInt(u[0].substring(19),10):e.browser==="firefox"&&c!==-1&&(d=2147483637),d},n=r.RTCPeerConnection.prototype.setRemoteDescription;r.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,e.browser==="chrome"&&e.version>=76){let{sdpSemantics:c}=this.getConfiguration();c==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(t(arguments[0])){let c=i(arguments[0]),d=s(c),u=o(arguments[0],c),h;d===0&&u===0?h=Number.POSITIVE_INFINITY:d===0||u===0?h=Math.max(d,u):h=Math.min(d,u);let p={};Object.defineProperty(p,"maxMessageSize",{get(){return h}}),this._sctp=p}return n.apply(this,arguments)}}function yr(r){if(!(r.RTCPeerConnection&&"createDataChannel"in r.RTCPeerConnection.prototype))return;function e(i,s){let o=i.send;i.send=function(){let a=arguments[0],c=a.length||a.size||a.byteLength;if(i.readyState==="open"&&s.sctp&&c>s.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+s.sctp.maxMessageSize+" bytes)");return o.apply(i,arguments)}}let t=r.RTCPeerConnection.prototype.createDataChannel;r.RTCPeerConnection.prototype.createDataChannel=function(){let s=t.apply(this,arguments);return e(s,this),s},rt(r,"datachannel",i=>(e(i.channel,i.target),i))}function Ws(r){if(!r.RTCPeerConnection||"connectionState"in r.RTCPeerConnection.prototype)return;let e=r.RTCPeerConnection.prototype;Object.defineProperty(e,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(e,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(t){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),t&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=t)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(t=>{let i=e[t];e[t]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=s=>{let o=s.target;if(o._lastConnectionState!==o.connectionState){o._lastConnectionState=o.connectionState;let n=new Event("connectionstatechange",s);o.dispatchEvent(n)}return s},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),i.apply(this,arguments)}})}function js(r,e){if(!r.RTCPeerConnection||e.browser==="chrome"&&e.version>=71||e.browser==="safari"&&e.version>=605)return;let t=r.RTCPeerConnection.prototype.setRemoteDescription;r.RTCPeerConnection.prototype.setRemoteDescription=function(s){if(s&&s.sdp&&s.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){let o=s.sdp.split(`
`).filter(n=>n.trim()!=="a=extmap-allow-mixed").join(`
`);r.RTCSessionDescription&&s instanceof r.RTCSessionDescription?arguments[0]=new r.RTCSessionDescription({type:s.type,sdp:o}):s.sdp=o}return t.apply(this,arguments)}}function br(r,e){if(!(r.RTCPeerConnection&&r.RTCPeerConnection.prototype))return;let t=r.RTCPeerConnection.prototype.addIceCandidate;!t||t.length===0||(r.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(e.browser==="chrome"&&e.version<78||e.browser==="firefox"&&e.version<68||e.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():t.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function Nr(r,e){if(!(r.RTCPeerConnection&&r.RTCPeerConnection.prototype))return;let t=r.RTCPeerConnection.prototype.setLocalDescription;!t||t.length===0||(r.RTCPeerConnection.prototype.setLocalDescription=function(){let s=arguments[0]||{};if(typeof s!="object"||s.type&&s.sdp)return t.apply(this,arguments);if(s={type:s.type,sdp:s.sdp},!s.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":s.type="offer";break;default:s.type="answer";break}return s.sdp||s.type!=="offer"&&s.type!=="answer"?t.apply(this,[s]):(s.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(n=>t.apply(this,[n]))})}var uh=pe(Zn());function Md({window:r}={},e={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){let t=xs,i=Nd(r),s={browserDetails:i,commonShim:ea,extractVersion:Rr,disableLog:yd,disableWarnings:bd,sdp:uh};switch(i.browser){case"chrome":if(!ws||!Us||!e.shimChrome)return t("Chrome shim is not included in this adapter release."),s;if(i.version===null)return t("Chrome shim can not determine version, not shimming."),s;t("adapter.js shimming chrome."),s.browserShim=ws,br(r,i),Nr(r,i),Vs(r,i),vn(r,i),Us(r,i),Dn(r,i),Mn(r,i),On(r,i),kn(r,i),Pn(r,i),Ln(r,i),Ar(r,i),Gs(r,i),Ws(r,i),Cr(r,i),yr(r,i),js(r,i);break;case"firefox":if(!Fs||!$s||!e.shimFirefox)return t("Firefox shim is not included in this adapter release."),s;t("adapter.js shimming firefox."),s.browserShim=Fs,br(r,i),Nr(r,i),Bs(r,i),$s(r,i),xn(r,i),wn(r,i),Vn(r,i),Un(r,i),Bn(r,i),$n(r,i),Fn(r,i),Hn(r,i),Gn(r,i),Ar(r,i),Ws(r,i),Cr(r,i),yr(r,i);break;case"safari":if(!Hs||!e.shimSafari)return t("Safari shim is not included in this adapter release."),s;t("adapter.js shimming safari."),s.browserShim=Hs,br(r,i),Nr(r,i),Yn(r,i),Xn(r,i),Kn(r,i),Wn(r,i),jn(r,i),zn(r,i),Jn(r,i),qn(r,i),Ar(r,i),Gs(r,i),Cr(r,i),yr(r,i),js(r,i);break;default:t("Unsupported browser!");break}return s}var u_=Md({window:typeof window>"u"?void 0:window});var pl=pe(ve());var xd=(D=>(D[D.INVALID_PARAMETER=4096]="INVALID_PARAMETER",D[D.INVALID_OPERATION=4097]="INVALID_OPERATION",D[D.NOT_SUPPORTED=4098]="NOT_SUPPORTED",D[D.DEVICE_NOT_FOUND=4099]="DEVICE_NOT_FOUND",D[D.INITIALIZE_FAILED=4100]="INITIALIZE_FAILED",D[D.SIGNAL_CHANNEL_SETUP_FAILED=16385]="SIGNAL_CHANNEL_SETUP_FAILED",D[D.SIGNAL_CHANNEL_ERROR=16386]="SIGNAL_CHANNEL_ERROR",D[D.ICE_TRANSPORT_ERROR=16387]="ICE_TRANSPORT_ERROR",D[D.JOIN_ROOM_FAILED=16388]="JOIN_ROOM_FAILED",D[D.CREATE_OFFER_FAILED=16389]="CREATE_OFFER_FAILED",D[D.SIGNAL_CHANNEL_RECONNECTION_FAILED=16390]="SIGNAL_CHANNEL_RECONNECTION_FAILED",D[D.UPLINK_RECONNECTION_FAILED=16391]="UPLINK_RECONNECTION_FAILED",D[D.DOWNLINK_RECONNECTION_FAILED=16392]="DOWNLINK_RECONNECTION_FAILED",D[D.REMOTE_STREAM_NOT_EXIST=16400]="REMOTE_STREAM_NOT_EXIST",D[D.CLIENT_BANNED=16448]="CLIENT_BANNED",D[D.SERVER_TIMEOUT=16449]="SERVER_TIMEOUT",D[D.SUBSCRIPTION_TIMEOUT=16450]="SUBSCRIPTION_TIMEOUT",D[D.PLAY_NOT_ALLOWED=16451]="PLAY_NOT_ALLOWED",D[D.DEVICE_AUTO_RECOVER_FAILED=16452]="DEVICE_AUTO_RECOVER_FAILED",D[D.START_PUBLISH_CDN_FAILED=16453]="START_PUBLISH_CDN_FAILED",D[D.STOP_PUBLISH_CDN_FAILED=16454]="STOP_PUBLISH_CDN_FAILED",D[D.START_MIX_TRANSCODE_FAILED=16455]="START_MIX_TRANSCODE_FAILED",D[D.STOP_MIX_TRANSCODE_FAILED=16456]="STOP_MIX_TRANSCODE_FAILED",D[D.NOT_SUPPORTED_H264=16457]="NOT_SUPPORTED_H264",D[D.SWITCH_ROLE_FAILED=16458]="SWITCH_ROLE_FAILED",D[D.API_CALL_TIMEOUT=16459]="API_CALL_TIMEOUT",D[D.SCHEDULE_FAILED=16460]="SCHEDULE_FAILED",D[D.API_CALL_ABORTED=16461]="API_CALL_ABORTED",D[D.UNKNOWN=65535]="UNKNOWN",D))(xd||{}),E=xd;var ph=function(r){for(let e in E)if(E[e]===r)return e;return"UNKNOWN"},ia=class extends Error{code;extraCode;message;originMessage;name;constraint;constructor({name:e="RtcError",message:t,code:i=E.UNKNOWN,extraCode:s=0,constraint:o}){let n=`<${ph(i)} 0x${i.toString(16)}>`,a=`${t}${o?` constraint: ${o}`:""}${t?.includes(n)?"":` ${n}`}`;super(a),this.code=i,this.extraCode=s,this.name=e,this.message=a,this.constraint=o,this.originMessage=t}getCode(){return this.code}getExtraCode(){return this.extraCode}toString(){return this.originMessage}},A=ia;var Vd=new Date().getTime(),ra=0,Ud=function(r){Vd=r,ra=Vd-new Date().getTime();let e=new Date;e.setTime(r),I.info(`baseTime from server: ${e} offset: ${ra}`)},Dr=function(){return new Date().getTime()+ra},Js=function(){let r=new Date;return r.setTime(Dr()),r.toLocaleString()};var be={};it(be,{bytes2ms:()=>zh,copyProperties:()=>Yh,deepClone:()=>Ro,deepMerge:()=>lt,env:()=>Ht,fibonacci:()=>er,formatedTime:()=>rp,getConstructorName:()=>yo,getContainerFromElement:()=>bo,getEnv:()=>Kh,getInternalVersion:()=>Qh,getLoggerUrl:()=>fi,getMuteStateFromFlag:()=>Kt,getNetworkQuality:()=>ep,getNetworkType:()=>Co,getOSType:()=>zd,getReconnectionTimeout:()=>we,getStringByteLength:()=>No,getSysInfo:()=>Jh,getTerminalType:()=>Yd,getTurnServer:()=>tp,getValueType:()=>te,glog:()=>Qd,ipv4ToUint32:()=>ts,isArray:()=>oe,isAudioWorkletSupported:()=>ka,isBoolean:()=>Z,isConstructor:()=>Zr,isEmpty:()=>es,isFunction:()=>W,isLangChinese:()=>ut,isMediaStreamTrack:()=>qh,isNumber:()=>K,isObject:()=>Xe,isOverseaSdkAppId:()=>Ao,isPlainObject:()=>Ue,isPromise:()=>Qr,isRemoteTrack:()=>jt,isString:()=>j,isUndefined:()=>f,logVisibilityState:()=>La,ms2bytes:()=>Xh,ms2samples:()=>qd,performanceNow:()=>U,promiseAny:()=>Pa,samples2ms:()=>Xd,stringify:()=>qe,stringifyIncludeValue:()=>Ma});var Ht={};it(Ht,{ANDROID_VERSION:()=>na,CHROME_MAJOR_VERSION:()=>ci,CHROME_VERSION:()=>lo,EDGE_VERSION:()=>zs,EDG_MAJOR_VERSION:()=>ca,EDG_VERSION:()=>Xs,FIREFOX_MAJOR_VERSION:()=>aa,FIREFOX_VERSION:()=>Ys,HUAWEI_VERSION:()=>no,IE_VERSION:()=>Eh,IOS_MAIN_VERSION:()=>ha,IOS_VERSION:()=>Ft,IPADQQB_VERSION:()=>so,IS_ANDROID:()=>ge,IS_ANDROID_WEBVIEW:()=>Rh,IS_ANY_SAFARI:()=>la,IS_CHROME:()=>ji,IS_CHROME_OS:()=>Hd,IS_CHROMIUM_BASE:()=>ai,IS_EDG:()=>Fi,IS_EDGE:()=>$i,IS_ELECTRON:()=>Sh,IS_FIREFOX:()=>z,IS_HEADLESS_CHROME:()=>ua,IS_HUAWEI:()=>Ih,IS_HUAWEIBROWSER:()=>wr,IS_IE:()=>Fd,IS_IE8:()=>Th,IS_IOS:()=>We,IS_IOS_13_OR_14:()=>Ah,IS_IOS_15_1:()=>Hr,IS_IPAD:()=>Bi,IS_IPADQQB:()=>xr,IS_IPAD_PRO:()=>oa,IS_IPHONE:()=>$t,IS_IPOD:()=>$d,IS_LINUX:()=>Vr,IS_LOCAL:()=>Ke,IS_MAC:()=>je,IS_MACQQB:()=>Lr,IS_MIBROWSER:()=>Ur,IS_MQQB:()=>Wi,IS_NATIVE_ANDROID:()=>fh,IS_OLD_ANDROID:()=>_h,IS_OPPOBROWSER:()=>$r,IS_SAFARI:()=>xe,IS_SAFARI_15_1:()=>pa,IS_SAMSUNGBROWSER:()=>Br,IS_SOGOU:()=>kr,IS_SOGOUM:()=>Or,IS_TBS:()=>nt,IS_UCBROWSER:()=>da,IS_VIVOBROWSER:()=>Fr,IS_WECHAT:()=>Hi,IS_WIN:()=>Ct,IS_WQQB:()=>Mr,IS_WX:()=>gh,IS_X5MQQB:()=>Gi,IS_XWEB:()=>Pr,MACQQB_VERSION:()=>ro,MI_VERSION:()=>oo,MQQB_VERSION:()=>wi,OPPO_VERSION:()=>co,SAFARI_VERSION:()=>Ki,SAMSUNG_VERSION:()=>ao,SOGOUM_VERSION:()=>qs,SOGOU_VERSION:()=>Qs,TBS_VERSION:()=>Zs,USER_AGENT:()=>Ge,VIVO_VERSION:()=>uo,WECHAT_VERSION:()=>to,WQQB_VERSION:()=>io,XWEB_VERSION:()=>eo,browserInfo:()=>ot,getBrowserInfo:()=>Gd,getChromeMajorVersion:()=>at,getOSName:()=>ho,getOSString:()=>ma,getUserAgentData:()=>Gr,isLocalStorageEnabled:()=>yt});var Ge=typeof navigator>"u"?"":navigator.userAgent,B=r=>new RegExp(r,"i").test(Ge),ce=r=>{if(B(r)){let e=new RegExp(`${r}\\/([\\d.]+)`),t=Ge.match(e);if(t&&t[1])return t[1]}return""},sa=r=>{if(B(r)){let e=new RegExp(`${r}\\/(\\d+)`),t=Ge.match(e);if(t&&t[1])return parseFloat(t[1])}return NaN},wd=/AppleWebKit\/([\d.]+)/i.exec(Ge),mh=wd?parseFloat(wd[1]):NaN,Bi=B("iPad"),oa=typeof navigator<"u"&&navigator.maxTouchPoints&&navigator.maxTouchPoints>2&&B("Macintosh"),$t=B("iPhone")&&!Bi,$d=B("iPod"),We=$t||Bi||$d||oa,ge=B("Android"),na=function(){if(ge){let r=Ge.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(r){let e=r[1]&&parseFloat(r[1]),t=r[2]&&parseFloat(r[2]);if(e&&t)return parseFloat(`${r[1]}.${r[2]}`);if(e)return e}}return NaN}(),_h=ge&&B("webkit")&&na<2.3,fh=ge&&na<5&&mh<537,z=B("Firefox"),Ys=ce("Firefox"),aa=sa("Firefox"),$i=B("Edge"),zs=ce("Edge"),Fi=B("Edg"),Xs=ce("Edg"),ca=sa("Edg"),Or=B("SogouMobileBrowser"),qs=ce("SogouMobileBrowser"),kr=B("MetaSr\\s"),Qs=ce("MetaSr\\s"),nt=B("TBS"),Zs=ce("TBS"),Pr=B("XWEB"),eo=ce("XWEB"),Th=B("MSIE\\s8\\.0"),Fd=B("MSIE\\/\\d+"),Eh=function(){if(Fd){let r=/MSIE\s(\d+)\.\d/.exec(Ge),e=r&&parseFloat(r[1]);return!e&&/Trident\/7.0/i.test(Ge)&&/rv:11.0/.test(Ge)&&(e=11),e}return NaN}(),Hi=B("(micromessenger|webbrowser)"),to=ce("MicroMessenger"),Gi=!nt&&B("MQQBrowser")&&B("COVC"),Wi=!nt&&B("MQQBrowser")&&!B("COVC"),wi=Wi||Gi?ce("MQQBrowser"):"",Mr=!nt&&B(" QQBrowser"),io=ce(" QQBrowser"),Lr=!nt&&B("QQBrowserLite"),ro=ce("QQBrowserLite"),xr=!nt&&B("MQBHD"),so=ce("MQBHD"),Ct=B("Windows"),je=!We&&B("MAC OS X"),Vr=!ge&&B("Linux"),Hd=B("CrOS"),gh=B("MicroMessenger"),da=B("UCBrowser"),Sh=B("Electron"),Ur=B("MiuiBrowser"),oo=ce("MiuiBrowser"),wr=B("HuaweiBrowser"),Ih=B("Huawei"),no=ce("HuaweiBrowser"),Br=B("SamsungBrowser"),ao=ce("SamsungBrowser"),$r=B("HeyTapBrowser"),co=ce("HeyTapBrowser"),Fr=B("VivoBrowser"),uo=ce("VivoBrowser"),at=()=>sa("Chrome"),ai=B("Chrome"),ji=!$i&&!kr&&!Or&&!nt&&!Pr&&!Fi&&!Mr&&!Ur&&!wr&&!Br&&!$r&&!Fr&&ai,ua=B("HeadlessChrome"),ci=at(),lo=ce("Chrome"),xe=!ai&&!Wi&&!Gi&&!Lr&&!xr&&B("Safari"),la=xe||We,Ki=ce("Version"),Rh=/Android.*(wv|.0.0.0)/.test(Ge),Ft=(()=>{if(oa)return Ki;if(We){let r=Ge.match(/OS (\d+)_(\d+)/i);if(r&&r[1]){let e=r[1];return r[2]&&(e+=`.${r[2]}`),e}}return""})(),ha=Number(Ft.split(".")[0]),pa=Ki==="15.1",Hr=Ft==="15.1",Ah=(()=>{let r=Number(Ft.split(".")[0]);return r===14||r===13})(),Ke=typeof location<"u"?location.protocol==="file:"||location.hostname==="localhost"||location.hostname==="127.0.0.1":!1,yt=(()=>{let r;return()=>{if(f(r))try{r=!!window.localStorage}catch{r=!1}return r}})(),ot=Gd();function Gd(){let r=new Map([[z,["Firefox",Ys]],[Fi,["Edg",Xs]],[ji,["Chrome",lo]],[xe,["Safari",Ki]],[nt,["TBS",Zs]],[Pr,["XWEB",eo]],[Hi&&$t,["WeChat",to]],[Mr,["QQ(Win)",io]],[Wi,["QQ(Mobile)",wi]],[Gi,["QQ(Mobile X5)",wi]],[Lr,["QQ(Mac)",ro]],[xr,["QQ(iPad)",so]],[Ur,["MI",oo]],[wr,["HW",no]],[Br,["Samsung",ao]],[$r,["OPPO",co]],[Fr,["VIVO",uo]],[$i,["EDGE",zs]],[Or,["SogouMobile",qs]],[kr,["Sogou",Qs]]]),e="unknown",t="unknown";return r.has(!0)&&([e,t]=r.get(!0)),{name:e,version:t}}var st=null;async function Gr(){if(st)return st;if(!navigator.userAgentData||!W(navigator.userAgentData.getHighEntropyValues))return null;try{return st=await navigator.userAgentData.getHighEntropyValues(["architecture","bitness","model","platformVersion","fullVersionList"]),st}catch{return null}}var Bd=new Map([[ge,"Android"],[We,"iOS"],[Ct,"Windows"],[je,"MacOS"],[Vr,"Linux"],[Hd,"ChromeOS"]]),ho=function(){return Bd.get(!0)?Bd.get(!0):st?st.platform:"unknown"},ma=()=>{let r=ho();return r+=`/${ot.name}/${xe?ot.version:ot.version.split(".")[0]}`,st?.platformVersion&&(r+=`/${st.platformVersion}`),st?.architecture&&(r+=`/${st.architecture}`),r};var Oa={};it(Oa,{AUDIO_MUTE_BIT:()=>Eo,AUDIO_STAT_BIT:()=>zi,AUX_STAT_BIT:()=>Kr,AUX_STREAM_MSID:()=>Ra,BACKEND_ENV:()=>To,BASE_DOC_URL:()=>ct,BASE_HOST:()=>po,CAPABILITIES_KEYS:()=>Io,CLASS_NAME:()=>Fh,CLOUD_CONSOLE_URL:()=>yh,DATA_FREEZE_TIMING:()=>So,DOC_URL:()=>bh,DTLS_STATE_UNKNOWN:()=>Ye,ENV_NAME:()=>bt,EXCHANGE_SDP_TIMEOUT:()=>Na,INTERVAL:()=>_i,IS_WORKER:()=>Ji,IS_WORKLET:()=>Wr,KIBANA_EVENT:()=>ye,LOCAL_STREAM_PUBLISH_STATE:()=>Kd,LOGGER_CMD_TYPE:()=>li,LOGGER_DOMAIN:()=>Ea,LOGGER_DOMAIN_OVERSEA:()=>ga,LOG_LEVEL:()=>Je,LOG_LEVEL_NAME:()=>Gh,MAIN_STREAM_MSID:()=>Jr,MICROPHONE_COMMUNICATIONS:()=>Hh,MICROPHONE_DEFAULT:()=>mi,NAME:()=>l,NETWORK_TYPE:()=>Gt,NOT_SUPPORTED_H264:()=>hi,PAUSED_RETRY_COUNT:()=>va,PEERCONNECTION_CONNECTING_TIMEOUT:()=>zr,PEER_CONNECTION_STATE:()=>Q,PEER_LEAVE_REASON:()=>Da,RAF:()=>qr,RECOVER_CAPTURE_INTERVAL:()=>Qi,REMOTE_STREAM_TYPE_AUX:()=>Ca,REMOTE_STREAM_TYPE_MAIN:()=>Aa,RENDER_FREEZE_TIMING:()=>Uh,RIC:()=>dt,SCHEDULE_DOMAIN:()=>pi,SCHEDULE_TIMEOUT:()=>$h,SDP_SEMANTICS_PLAN_B:()=>Xi,SDP_SEMANTICS_UNIFIED_PLAN:()=>Wt,SECOND_HOST:()=>fa,SIGNAL_PING_PONG_INTERVAL:()=>vh,SIGNAL_PING_TIMEOUT:()=>Nh,SIGNAL_RECONNECTION_COUNT:()=>Yr,SMALL_STAT_BIT:()=>Ia,SPEAKER_DEFAULT:()=>Xr,STORAGE_EXPIRES_TIME:()=>mo,STREAM_TYPE_BIG:()=>wh,STREAM_TYPE_SMALL:()=>Bh,SUBSCRIBE_SMALL_RETRY_COUNT:()=>qi,SYNC_USER_LIST_INTERVAL:()=>Vh,Scene:()=>Nt,Switch:()=>Wd,THIRD_HOST:()=>Ta,TIMEOUT:()=>Zi,TRANSPORT_DIRECTION:()=>$,TRTC_ERROR_ASSISTANCE:()=>Sa,TRTC_QUALITY_BAD:()=>Mh,TRTC_QUALITY_DISCONNECTED:()=>xh,TRTC_QUALITY_EXCELLENT:()=>Oh,TRTC_QUALITY_GOOD:()=>kh,TRTC_QUALITY_POOR:()=>Ph,TRTC_QUALITY_UNKNOWN:()=>Dh,TRTC_QUALITY_VERY_BAD:()=>Lh,UPDATE_OFFER_TIMEOUT:()=>ba,VIDEO_MUTE_BIT:()=>go,VIDEO_STAT_BIT:()=>Yi,audioProfileMap:()=>_o,getRetryCount:()=>vt,getScriptDir:()=>Ch,innerVersion:()=>di,loggerProxy:()=>jr,screenProfileMap:()=>fo,setLoggerProxy:()=>ui,setRetryCount:()=>ya,setVersion:()=>_a,version:()=>Se,videoProfileMap:()=>Ve});var di="4.15.00.1600",Se="5.0.0";function _a(r){Se=r;let[e,t,i]=r.split(".").map(s=>parseInt(s,10));di=`${e}.${Math.min(15,t)}.${Math.min(15,i)}.${t.toString().padStart(2,"0")}${i.toString().padStart(2,"0")}`}var Ji=typeof importScripts<"u",Wr=typeof registerProcessor<"u",Ch=()=>{let r=Ji?self.location.href:document.currentScript.src;return r.substring(0,r.lastIndexOf("/")+1)},jr="",ui=r=>jr=r,po="web.sdk.qcloud.com",fa="web.sdk.tencent.cn",Ta="web.sdk.cloud.tencent.cn",yh="https://console.cloud.tencent.com/trtc",ct=`https://${po}/trtc/webrtc/v5/doc`,bh=`${ct}/zh-cn/`,Ea="https://yun.tim.qq.com",ga="https://apisgp.my-imcloud.com",Sa="trtc_error_assistance",li={LOG:"jssdk_log",EVENT:"jssdk_event",KEY_POINT:"jssdk_new_endreport"},bt={QCLOUD:"qcloud",OLD_CLOUD_LADDER:"trtc",WEBRTC:"webrtc"},Je=(n=>(n[n.TRACE=0]="TRACE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.NONE=5]="NONE",n))(Je||{}),Nh=18e3,vh=2e3,Gt={unknown:0,wifi:1,"3g":2,"2g":3,"4g":4,wired:5},mo=7*24*3600*1e3,Wd=(s=>(s.USEAINS="useAINS",s.ENABLEDEBUG="enableDebug",s.USEV2="useV2",s.USEWT="useWt",s))(Wd||{}),_o={standard:{sampleRate:48e3,channelCount:1,bitrate:40},"standard-stereo":{sampleRate:48e3,channelCount:2,bitrate:64},high:{sampleRate:48e3,channelCount:1,bitrate:192},"high-stereo":{sampleRate:48e3,channelCount:2,bitrate:192}},Ve={"120p":{width:160,height:120,frameRate:15,bitrate:200},"120p_2":{width:160,height:120,frameRate:15,bitrate:100},"180p":{width:320,height:180,frameRate:15,bitrate:350},"180p_2":{width:320,height:180,frameRate:15,bitrate:150},"240p":{width:320,height:240,frameRate:15,bitrate:400},"240p_2":{width:320,height:240,frameRate:15,bitrate:200},"360p":{width:640,height:360,frameRate:15,bitrate:800},"360p_2":{width:640,height:360,frameRate:15,bitrate:400},"480p":{width:640,height:480,frameRate:15,bitrate:900},"480p_2":{width:640,height:480,frameRate:15,bitrate:500},"720p":{width:1280,height:720,frameRate:15,bitrate:1500},"1080p":{width:1920,height:1080,frameRate:15,bitrate:2e3},"1440p":{width:2560,height:1440,frameRate:30,bitrate:4860},"4K":{width:3840,height:2160,frameRate:30,bitrate:9e3}},fo={"480p":{width:640,height:480,frameRate:5,bitrate:900},"480p_2":{width:640,height:480,frameRate:30,bitrate:1e3},"720p":{width:1280,height:720,frameRate:5,bitrate:1200},"720p_2":{width:1280,height:720,frameRate:30,bitrate:3e3},"1080p":{width:1920,height:1080,frameRate:5,bitrate:1600},"1080p_2":{width:1920,height:1080,frameRate:30,bitrate:4e3}},l={CANVAS:"canvas",AUDIO:"audio",VIDEO:"video",SCREEN:"screen",SMALL:"small",BIG:"big",AUXILIARY:"auxiliary",SMALL_VIDEO:"smallVideo",FACING_MODE_USER:"user",FACING_MODE_ENVIRONMENT:"environment",MUTE:"mute",UNMUTE:"unmute",ENDED:"ended",PLAYING:"playing",PAUSE:"pause",ERROR:"error",LOADEDDATA:"loadeddata",AUDIO_INPUT:"audioinput",VIDEO_INPUT:"videoinput",DETAIL:"detail",TEXT:"text",MAIN:"main",BACKUP:"backup",BANNED:"banned",KICK:"kick",USER_TIME_OUT:"user_time_out",ROOM_DISBAND:"room_disband",SEI_MESSAGE:"sei-message",ADD:"add",REMOVE:"remove",REPLACE:"replace",TRACK:"track",SUBSCRIBE:"subscribe",UNSUBSCRIBE:"unsubscribe",TRANSCEIVER_DIRECTION_SENDONLY:"sendonly",TRANSCEIVER_DIRECTION_RECVONLY:"recvonly",ENTER_PICTURE_IN_PICTURE:"enterpictureinpicture",LEAVE_PICTURE_IN_PICTURE:"leavepictureinpicture"},$={INACTIVE:"inactive",SENDONLY:"sendonly",RECVONLY:"recvonly"},To={OLD_CLOUD_LADDER:"wss://trtc.rtc.qq.com",WEBRTC:"wss://webrtc.qq.com"},Nt=(t=>(t.LIVE="live",t.RTC="rtc",t))(Nt||{}),Yi=1,Ia=2,Kr=4,zi=8,Eo=64,go=16,Jr="5Y2wZK8nANNAoVw6dSAHVjNxrD1ObBM2kBPV",Ra="224d130c-7b5c-415b-aaa2-79c2eb5a6df2",Aa=l.MAIN,Ca=l.AUXILIARY,Dh=0,Oh=1,kh=2,Ph=3,Mh=4,Lh=5,xh=6,Ye="unknown",Q={NEW:"new",CONNECTING:"connecting",FAILED:"failed",CLOSED:"closed",DISCONNECTED:"disconnected",CONNECTED:"connected",COMPLETED:"completed"},jd=1/0;function ya(r){jd=r}function vt(){return jd}var Yr=30,ye={JOIN:"join",DELTA_JOIN:"delta-join",REJOIN:"rejoin",LEAVE:"leave",DELTA_LEAVE:"delta-leave",PUBLISH:"publish",DELTA_PUBLISH:"delta-publish",UNPUBLISH:"unpublish",SUBSCRIBE:"subscribe",UNSUBSCRIBE:"unsubscribe",UPLINK_CONNECTION:"uplink-connection",UPLINK_RECONNECTION:"uplink-reconnection",DOWNLINK_CONNECTION:"downlink-connection",DOWNLINK_RECONNECTION:"downlink-reconnection",ON_TRACK:"ontrack",ICE_CONNECTION_STATE:"iceConnectionState",LOCAL_STREAM_INITIALIZE:"stream-initialize",SIGNAL_CONNECTION:"websocketConnectionState",SIGNAL_RECONNECTION:"websocketReconnectionState",UPDATE_STREAM:"update-stream",RECOVER_LOCAL_AUDIO_TRACK:"recover-local-audio-track",RECOVER_LOCAL_VIDEO_TRACK:"recover-local-video-track",RECOVER_SUBSCRIPTION:"recover-subscription",START_MIX_TRANSCODE:"start-mix-transcode",STOP_MIX_TRANSCODE:"stop-mix-transcode",PLAYER_ERROR:"player-error",SCHEDULE:"schedule",LOAD_WORKLET:"load-worklet",VIDEO_FROZEN_COUNT:"videoFrozenCount",GET_USER_MEDIA_RETRY:"getUserMedia-retry"},Vh=1e4,ba=1e4,Na=1e4,Wt="unified-plan",Xi="plan-b",hi=1028,Kd=(i=>(i[i.UNPUBLISH=-1]="UNPUBLISH",i[i.PUBLISHING=0]="PUBLISHING",i[i.PUBLISHED=1]="PUBLISHED",i))(Kd||{}),So=500,Uh=1e3,wh=l.BIG,Bh=l.SMALL,zr=10*1e3,pi={MAIN:"schedule.rtc.qq.com",BACKUP:"schedule.rtc.qcloud.com",MAIN_OVERSEA:"schedule.rtc.tencentcloud.com",BACKUP_OVERSEA:"schedule-ecdn.rtc.tencentcloud.com"},$h=2e3,Fh={TRTC:"TRTC",CLIENT:"Client",LOCAL_STREAM:"LocalStream",REMOTE_STREAM:"RemoteStream",STREAM:"Stream"},va=5,mi="default",Xr=mi,Hh="communications",Gh=Object.keys(Je),Da=["normal leave","timeout leave","kick","role change"],qi=10,Qi=2e3,dt="ric",qr="raf",_i="interval",Zi="timeout",Io=["width","height","frameRate","facingMode","sampleRate","sampleSize","channelCount","deviceId"];var ze=1e8;var jh="trtc_env",Kh=function(){return new URLSearchParams(location.search).get(jh)||""},Ao=r=>Number(r)<14e8,fi=function(r,e){let t;jr?t=jr:t=Ao(r)?ga:Ea;let i=Math.floor(Math.random()*2**31);return`${t}/v5/AVQualityReportSvc/C2S?random=${i}&sdkappid=${r}&cmdtype=${e}`};function Yd(){return ge?4:$t?2:Bi?3:je?12:Ct?5:Vr?13:1}function zd(){return ge?"Android":$t?"iPhone":Bi?"iPad":je?"Mac":Ct?"Windows":Vr?"Linux":"unknown"}function Co(){let{userAgent:r,connection:e}=navigator,t=(r.match(/NetType\/\S+/)||[])[0]||"";t=t.toLowerCase().replace("nettype/",""),t==="3gnet"&&(t="3g");let i=e&&e.type&&e.type.toLowerCase(),s=e&&e.effectiveType&&e.effectiveType.toLowerCase();s==="slow-2"&&(s="2g");let o=t||"unknown";if(i)switch(i){case"cellular":case"wimax":o=s||"unknown";break;case"wifi":o="wifi";break;case"ethernet":o="wired";break;case"none":case"other":case"unknown":default:o="unknown";break}return o}var Jh=function(r){return{AbilityOption:{AVLimit:r,GeneralLimit:{CPULimit:{uint32_CPU_num:navigator.hardwareConcurrency||0,str_CPU_name:String(navigator.platform),uint32_CPU_maxfreq:0,model:"",uint32_total_memory:0},uint32_terminal_type:Yd(),uint32_device_type:0,str_os_verion:zd(),uint32_link_type:1,str_client_version:di,uint32_net_type:Gt[Co()],ua:navigator.userAgent,version:""}}}};function Yh(r,e){for(let t of Reflect.ownKeys(e))if(t!=="constructor"&&t!=="prototype"&&t!=="name"){let i=Object.getOwnPropertyDescriptor(e,t)||"";Object.defineProperty(r,t,i)}return r}function zh(r,e=48e3){return Xd(r/4,e)}function Xd(r,e=48e3){return r*1e3/e}function Xh(r,e=48e3){return qd(r,e)*4}function qd(r,e=48e3){return r*e/1e3}var Qd=typeof window<"u"&&typeof window.glog=="function"?window.glog:()=>{},ut=()=>{let r=navigator.language;return r=r.substring(0,2),r==="zh"},Ue=function(r){if(!r||typeof r!="object"||Object.prototype.toString.call(r)!="[object Object]")return!1;let e=Object.getPrototypeOf(r);if(e===null)return!0;let t=Object.prototype.hasOwnProperty.call(e,"constructor")&&e.constructor;return typeof t=="function"&&t instanceof t&&Function.prototype.toString.call(t)===Function.prototype.toString.call(Object)};function er(r,e=1,t=1){return r<=1?t:er(r-1,t,e+t)}function we(r){return r>8?30*1e3:er(r)*1e3}function te(r){return Reflect.apply(Object.prototype.toString,r,[]).replace(/^\[object\s(\w+)\]$/,"$1").toLowerCase()}var W=r=>typeof r=="function",f=r=>typeof r>"u",j=r=>typeof r=="string",K=r=>typeof r=="number",Z=r=>typeof r=="boolean",Xe=r=>te(r)==="object",oe=r=>te(r)==="array",qh=r=>te(r)==="MediaStreamTrack".toLowerCase(),jt=r=>r.isRemote,Qr=r=>te(r)==="promise",Zr=r=>W(r)&&r.prototype.constructor===r,yo=r=>Zr(r)?r.prototype.constructor.name:"",ka=typeof AudioWorkletNode<"u";function Pa(r){return new Promise((e,t)=>{let i=[];r.forEach(s=>{s.then(e).catch(o=>{i.push(o),i.length===r.length&&t(i)})})})}function U(){return!performance||!performance.now?Date.now():Math.floor(performance.now())}var Jd=r=>+r<10?`0${r}`:r,Qh=r=>{let e=r.match(/^\d+\.\d+\.\d+/)[0];if(!e)return r;let t=e.split("."),i=Jd(t[1])+Jd(t[2]);return t[1]-15>0&&(t[1]="15"),t[2]-15>0&&(t[2]="15"),`${t.join(".")}.${i}`},Zh=Object.prototype.hasOwnProperty,{toString:y_}=Object.prototype;function es(r){if(r==null)return!0;if(typeof r=="boolean")return!1;if(typeof r=="number")return r===0;if(typeof r=="string"||typeof r=="function"||Array.isArray(r))return r.length===0;if(r instanceof Error)return r.message==="";if(Ue(r))switch(Object.prototype.toString.call(r)){case"[object File]":case"[object Map]":case"[object Set]":return r.size===0;case"[object Object]":{for(let e in r)if(Zh.call(r,e))return!1;return!0}}return!1}function Kt(r,e){return{userId:e,hasAudio:!!(r&zi),hasVideo:!!(r&Yi),hasAuxiliary:!!(r&Kr),hasSmall:!!(r&Ia),audioMuted:!!(r&Eo),videoMuted:!!(r&go),audioAvailable:!!(r&zi)&&!(r&Eo),videoAvailable:!!(r&Yi)&&!(r&go)}}function ep(r,e){return r>50||e>500?5:r>30||e>350?4:r>20||e>200?3:r>10||e>100?2:r>=0||e>=0?1:0}function tp(r){let e={urls:`turn:${r.url}`};return!f(r.username)&&!f(r.credential)&&(e.username=r.username,e.credential=r.credential,e.credentialType="password",f(r.credentialType)||(e.credentialType=r.credentialType)),e}function ts(r,e=!0){if(!j(r))return 0;let t=r.split(".");return e?(Number(t[0])<<24|Number(t[1])<<16|Number(t[2])<<8|Number(t[3]))>>>0:(Number(t[3])<<24|Number(t[2])<<16|Number(t[1])<<8|Number(t[0]))>>>0}var lt=function(r,e,t,i){if(!(Xe(r)&&Xe(e)))return 0;let s=0,o=Object.keys(e),n;for(let a=0,c=o.length;a<c;a++)if(n=o[a],!(f(e[n])||t&&t.includes(n)))if(Xe(r[n])&&Xe(e[n]))s+=lt(r[n],e[n],t,i);else{if(i&&i.includes(e[n]))continue;r[n]!==e[n]&&(r[n]=Ro(e[n]),s+=1)}return s};function Ro(r){if(oe(r)){let e=[];return r.forEach((t,i)=>{e[i]=Ro(t)}),e}if(Xe(r)){let e={};return Object.keys(r).forEach(t=>{e[t]=Ro(r[t])}),e}return r}var bo=r=>j(r)?document.getElementById(r):r,ip=new Intl.DateTimeFormat("zh-CN",{dateStyle:"short",timeStyle:"medium"}),rp=()=>ip.format(new Date);function qe(r,{keysToInclude:e,keysToExclude:t}){try{if(oe(r))return`[${r.map(n=>qe(n,{keysToInclude:e,keysToExclude:t})).join(",")}]`;if(!Ue(r)||!oe(e)&&!oe(t))return JSON.stringify(r);let i={},s=new Set(e),o=new Set(t);return Object.keys(r).forEach(n=>{(o.size===0&&s.has(n)||s.size===0&&!o.has(n))&&(i[n]=Ue(r[n])||oe(r[n])?JSON.parse(qe(r[n],{keysToExclude:t,keysToInclude:e})):r[n])}),JSON.stringify(i)}catch{return"{}"}}function Ma(r,e=!1){let t=[];return Object.keys(r).forEach(i=>{e===r[i]&&t.push(i)}),qe(r,{keysToInclude:t})}function No(r){return r.replace(/[\u4e00-\u9fa5]/g,"aa").length}var La=(()=>{let r=!1,e=document.visibilityState;return()=>{document.visibilityState!==e&&I.info(`visibility change: ${document.visibilityState}`),!r&&(document.addEventListener("visibilitychange",()=>{I.info(`visibility change: ${document.visibilityState}`),e=document.visibilityState}),r=!0)}})();function Jt({url:r,body:e,method:t,timeout:i}){let s=new XMLHttpRequest;return new Promise((o,n)=>{s.onreadystatechange=()=>{if(s.readyState===4)if(s.status>=200&&s.status<300)try{let a=JSON.parse(s.response);o({data:a})}catch{o({data:s.response})}else n({status:s.status,statusText:s.statusText||"request failed!"})},s.timeout=i||5e3,s.open(t||"POST",r,!0),s.send(e)})}var sp=0,op=1,Zd=2;function np({retryFunction:r,settings:e,onError:t,onRetrying:i,onRetryFailed:s,onRetrySuccess:o,context:n}){return function(...a){let{retries:c=5,timeout:d=1e3}=e,u=0,h=-1,p=sp,g=async(C,N)=>{let Ce=n||this;try{let oi=await r.apply(Ce,a);u>0&&o&&o.call(this,u),u=0,C(oi)}catch(oi){let Ms=()=>{clearTimeout(h),u=0,p=Zd,N(oi)},Ls=()=>{p!==Zd&&u<(W(c)?c():c)?(u++,p=op,W(i)&&i.call(this,u,Ms),h=window.setTimeout(()=>{h=-1,g(C,N)},W(d)?d(u):d)):(Ms(),W(s)&&s.call(this,oi))};W(t)?t.call(this,{error:oi,retry:Ls,reject:N,retryFuncArgs:a,retryCount:u}):Ls()}};return new Promise(g)}}var ht=np;var tr=class{userId;remoteUserId;id;sdkAppId;type;isLocal;constructor(e){this.id=e.id,this.userId=e.userId,this.sdkAppId=e.sdkAppId,this.remoteUserId=e.remoteUserId,this.isLocal=Z(e.isLocal)?e.isLocal:!0,this.type=this.isLocal?"":e.type}createChild(e){return Object.setPrototypeOf(e,this)}setUserId(e){this.userId=e}setSdkAppId(e){this.sdkAppId=e}log(e,t){let i=this.isLocal?this.userId:this.remoteUserId;t.unshift(`[${this.isLocal?"\u2191":"\u2193"}${this.type&&this.type!=="main"?"*":""}${this.id}${i?`|${i}`:""}]`),I.log(e,t,f(this.userId)||es(this.userId),this.userId,this.sdkAppId)}info(...e){this.log(2,e)}debug(...e){this.log(1,e)}warn(...e){this.log(3,e)}error(...e){this.log(4,e)}};var eu=pe(ve(),1),ap=new eu.default,_=ap;var ir=(x=>(x.ROOM_DESTROY="1",x.JOIN_START="21",x.JOIN_SCHEDULE_SUCCESS="22",x.JOIN_SIGNAL_CONNECTION_START="23",x.JOIN_SIGNAL_CONNECTION_END="24",x.JOIN_SEND_CMD="25",x.JOIN_RECEIVED_CMD_RES="26",x.JOIN_SUCCESS="27",x.JOIN_FAILED="28",x.LEAVE_START="51",x.LEAVE_SEND_CMD="52",x.LEAVE_SUCCESS="53",x.PUBLISH_START="61",x.SEND_FIRST_VIDEO_FRAME="62",x.PUBLISH_FAILED="63",x.SUBSCRIBE_START="81",x.SUBSCRIBE_SUCCESS="82",x.SUBSCRIBE_FAILED="84",x.UNSUBSCRIBE_SUCCESS="83",x.LOCAL_TRACK_CAPTURE_START="101",x.LOCAL_TRACK_CAPTURE_SUCCESS="102",x.LOCAL_TRACK_CAPTURE_FAILED="103",x.LOCAL_TRACK_PUBLISHED="104",x.LOCAL_TRACK_UNPUBLISHED="105",x.LOCAL_TRACK_REPLACED="106",x.SWITCH_DEVICE_SUCCESS="107",x.TRACK_MUTED="108",x.TRACK_UNMUTED="109",x.REMOTE_TRACK_SUBSCRIBED="110",x.REMOTE_TRACK_UNSUBSCRIBED="111",x.PLAY_TRACK_START="151",x.PLAYER_STATE_CHANGED="152",x.VIDEO_LOADED_DATA="153",x.AUTOPLAY_DIALOG_CLICK_CONFIRM="154",x.WORKLET_LOADED_SUCCESS="155",x.WORKLET_LOADED_FAILED="156",x.SIGNAL_CONNECTION_STATE_CHANGED="201",x.PEER_CONNECTION_STATE_CHANGED="202",x.SINGLE_CONNECTION_STAT="203",x.HEARTBEAT_REPORT="251",x.RECEIVED_PUBLISHED_USER_LIST="252",x.REMOTE_PUBLISH_STATE_CHANGED="253",x.AUDIO_LEVEL_INTERVAL="260",x.NETWORK_QUALITY="261",x.API_SUCCESS_RATE="262",x))(ir||{});var m=ir;var cp="%cTRTC%c%s",dp="padding: 1px 4px;border-radius: 3px;color: #fff;background: #1E88E5;",up="display: inline",lp=!(We||ge||ua),xa=class{_isEnableUploadLog=!0;_localJoinedUser=new Map;_queue=[];_timeoutId=-1;_logLevel=1;_logLevelToUpload=2;constructor(){!Ji&&!Wr&&(this.checkURLParam(),this.installEvents())}get isAbleToUpload(){return this._isEnableUploadLog&&this._timeoutId!==-1}installEvents(){_.on(m.JOIN_SCHEDULE_SUCCESS,({schedule:e})=>{e?.config?.logLevelToUpload&&Je[e.config.logLevelToUpload]&&(this._logLevelToUpload=e.config.logLevelToUpload)}),_.on(m.JOIN_SUCCESS,({room:e})=>{this.addJoinedUser({userId:e.userId,sdkAppId:e.sdkAppId}),this.startUpload()}),_.once(m.JOIN_FAILED,()=>{this.startUpload()}),_.on(m.LEAVE_SUCCESS,({room:e})=>{this.deleteJoinedUser(e.userId)})}startUpload(){this._timeoutId===-1&&this.uploadInterval()}addJoinedUser(e){this._localJoinedUser.set(e.userId,e),this.startUpload()}deleteJoinedUser(e){this._localJoinedUser.delete(e)}uploadInterval(){this.upload().catch(()=>{}),this._timeoutId=window.setTimeout(()=>this.uploadInterval(),2e3)}getLogsToUpload(){let e={map:new Map,splicedQueue:[]};if(this._queue[0].forAllJoinedClients&&this._localJoinedUser.size===0)return e;let t=0;for(;t<this._queue.length&&t!==50;t++){let i=this._queue[t];if(i.forAllJoinedClients)this._localJoinedUser.forEach(({userId:s,sdkAppId:o})=>{e.map.has(s)?e.map.get(s).logs.push(i):e.map.set(s,{userId:s,sdkAppId:o,logs:[i]})});else if(j(i.userId)&&K(i.sdkAppId)){let{userId:s,sdkAppId:o}=i;e.map.has(s)?e.map.get(s).logs.push(i):e.map.set(s,{userId:s,sdkAppId:o,logs:[i]})}}return e.map.size>0&&(e.splicedQueue=this._queue.splice(0,t)),e}async upload(){if(this._queue.length===0||!this._isEnableUploadLog)return;let{map:e,splicedQueue:t}=this.getLogsToUpload();if(e.size===0)return;try{let s=[...e.values()];for(let o=0;o<s.length;o++){let{userId:n,sdkAppId:a,logs:c}=s[o];await this.uploadLogWithRetry(JSON.stringify({timestamp:Js(),sdkAppId:String(a),userId:n,version:Se,log:c.map(d=>d.log).join(`
`)}),a),c.forEach(d=>d.uploaded=!0)}}catch{}let i=t.filter(s=>!s.uploaded);i.length>0&&(this._queue=i.concat(this._queue))}uploadLogWithRetry(e,t){return ht({retryFunction:()=>Jt({url:fi(t,li.LOG),body:e,timeout:5e3}),settings:{retries:3,timeout:1e3},onError:({retry:i})=>{i()}})()}getPrefix(e){let t=new Date;t.setTime(Dr());let i=String(t.getMilliseconds());return"padStart"in String.prototype&&(i=i.toString().padStart(3,"0")),`[${t.toTimeString().replace(/.*(\d{2}:\d{2}:\d{2}).*/,"$1")}:${i}] <${Je[e]}>`}getLogLevel(){return this._logLevel}setLogLevel(e){f(Je[e])||(this._logLevel!==e&&this.info("setLogLevel",e),this._logLevel=e)}enableUploadLog(){this._isEnableUploadLog=!0}disableUploadLog(){this.warn("disableUploadLog"),this._isEnableUploadLog=!1}logChunkToString(e){if(j(e))return e;try{return JSON.stringify(e)}catch{return""}}log(e,t,i=!0,s,o){if(t.unshift(this.getPrefix(e)),this._isEnableUploadLog&&e>=this._logLevelToUpload&&this._queue.push({log:t.reduce((a,c)=>`${a} ${this.logChunkToString(c)}`.trim(),""),level:e,userId:s,sdkAppId:o,forAllJoinedClients:i}),e<this._logLevel)return;let n=Je[e]?.toLowerCase()||"info";lp?console[n](cp,dp,up,...t):console[n](...t)}debug(...e){this.log(1,e)}info(...e){this.log(2,e)}warn(...e){this.log(3,e)}error(...e){this.log(4,e)}createLogger(e){return new tr(e)}checkURLParam(){let t=new URLSearchParams(location.search).get("logLevelToUpload"),i=t?Number(t):-1;Je[i]&&(this._logLevelToUpload=i)}},I=new xa;var hp=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{let e=Math.random()*16|0;return(r=="x"?e:e&3|8).toString(16)})},Va=hp;var Ua=class{_prefix="TRTC";_queue=new Map;constructor(){this.checkStorage()}getRealKey(e){return`${this._prefix}_${e}`}checkStorage(){if(!yt())return;setInterval(this.doFlush.bind(this),2e4),Object.keys(localStorage).filter(i=>{if(i.startsWith(this._prefix)){let s=JSON.parse(localStorage.getItem(i));if(s&&s.expiresIn<Date.now())return!0}return!1}).forEach(i=>localStorage.removeItem(i))}doFlush(){if(!!yt())try{for(let[e,t]of this._queue)localStorage.setItem(e,JSON.stringify(t))}catch(e){I.warn(e)}}getItem(e){if(!yt())return null;try{let t=JSON.parse(localStorage.getItem(this.getRealKey(e)));return t&&t.expiresIn>=Date.now()?t.value:null}catch(t){I.warn(t)}}setItem(e,t){if(!!yt())try{let i={expiresIn:Date.now()+mo,value:t};this._queue.set(this.getRealKey(e),i)}catch(i){I.warn(i)}}deleteItem(e){if(!yt())return!1;try{return e=this.getRealKey(e),this._queue.delete(e),localStorage.removeItem(e),!0}catch(t){return I.warn(t),!1}}clear(){if(!!yt())try{localStorage.clear()}catch(e){I.warn(e)}}},rr=new Ua;var Xt={};it(Xt,{HTTPS_API:()=>Ep,IS_GET_CAPABILITIES_SUPPORTED:()=>$a,IS_GET_SETTINGS_SUPPORTED:()=>$e,IS_SEI_SUPPORTED:()=>Ze,IS_SPC_SUPPORTED:()=>ss,basis:()=>Rp,checkSystemRequirementsInternal:()=>Ba,decodeSupportStatus:()=>Do,encodeSupportStatus:()=>su,getBrowserInfo:()=>_p,getDisplayResolution:()=>ou,isAddTransceiverSupported:()=>Be,isBrowserSupported:()=>wa,isGetReceiversSupported:()=>Ti,isGetSendersSupported:()=>sr,isGetTransceiversSupported:()=>pt,isGetUserMediaSupported:()=>nu,isMediaDevicesSupported:()=>vo,isMediaSessionSupported:()=>uu,isMediaStreamTrackProcessorSupported:()=>fp,isReplaceTrackSupported:()=>Ip,isScreenCaptureApiAvailable:()=>is,isSelectedCandidatePair:()=>zt,isSetParametersSupported:()=>os,isSmallStreamAPISupported:()=>cu,isSmallStreamSupported:()=>rs,isStopTransceiverSupported:()=>Sp,isTRTCSupported:()=>Tp,isUnifiedPlanDefault:()=>gp,isUsedInHttpProtocol:()=>Yt,isWebAudioSupported:()=>au,isWebCodecSupported:()=>du,isWebCodecsSupported:()=>ru,isWebRTCSupported:()=>Fa,isWebTransportSupported:()=>lu});var b={AVOID_REPEATED_CALL:"AVOID_REPEATED_CALL",INVALID_PARAMETER_REQUIRED:"INVALID_PARAMETER_REQUIRED",INVALID_PARAMETER_TYPE:"INVALID_PARAMETER_TYPE",INVALID_PARAMETER_EMPTY:"INVALID_PARAMETER_EMPTY",INVALID_PARAMETER_INSTANCE:"INVALID_PARAMETER_INSTANCE",INVALID_PARAMETER_RANGE:"INVALID_PARAMETER_RANGE",INVALID_PARAMETER_MIN:"INVALID_PARAMETER_MIN",INVALID_PARAMETER_MAX:"INVALID_PARAMETER_MAX",INVALID_PARAMETER_STREAMTYPE:"INVALID_PARAMETER_STREAMTYPE",API_CALL_TIMEOUT:"API_CALL_TIMEOUT",SIGNAL_CHANNEL_RECONNECTION_FAILED:"SIGNAL_CHANNEL_RECONNECTION_FAILED",SIGNAL_CHANNEL_SETUP_FAILED:"SIGNAL_CHANNEL_SETUP_FAILED",ERROR_MESSAGE:"ERROR_MESSAGE",EXCHANGE_SDP_TIMEOUT:"EXCHANGE_SDP_TIMEOUT",DOWNLINK_RECONNECTION_FAILED:"DOWNLINK_RECONNECTION_FAILED",EXCHANGE_SDP_FAILED:"EXCHANGE_SDP_FAILED",UPDATE_OFFER_TIMEOUT:"UPDATE_OFFER_TIMEOUT",UPLINK_RECONNECTION_FAILED:"UPLINK_RECONNECTION_FAILED",INVALID_RECORDID:"INVALID_RECORDID",INVALID_PURE_AUDIO:"INVALID_PURE_AUDIO",INVALID_STREAMID:"INVALID_STREAMID",INVALID_USER_DEFINE_RECORDID:"INVALID_USER_DEFINE_RECORDID",INVALID_USER_DEFINE_PUSH_ARGS:"INVALID_USER_DEFINE_PUSH_ARGS",INVALID_PROXY:"INVALID_PROXY",INVALID_JOIN:"INVALID_JOIN",INVALID_ROOMID_STRING:"INVALID_ROOMID_STRING",INVALID_ROOMID_INTEGER:"INVALID_ROOMID_INTEGER",INVALID_SIGNAL_CHANNEL:"INVALID_SIGNAL_CHANNEL",JOIN_ROOM_TIMEOUT:"JOIN_ROOM_TIMEOUT",JOIN_ROOM_FAILED:"JOIN_ROOM_FAILED",REJOIN_ROOM_FAILED:"REJOIN_ROOM_FAILED",INVALID_DESTROY:"INVALID_DESTROY",INVALID_PUBLISH:"INVALID_PUBLISH",INVALID_UNPUBLISH:"INVALID_UNPUBLISH",INVALID_AUDIENCE:"INVALID_AUDIENCE",INVALID_INITIALIZE:"INVALID_INITIALIZE",INVALID_DUPLICATE_PUBLISHING:"INVALID_DUPLICATE_PUBLISHING",INVALID_SUBSCRIBE_UNDEFINED:"INVALID_SUBSCRIBE_UNDEFINED",INVALID_SUBSCRIBE_LOCAL:"INVALID_SUBSCRIBE_LOCAL",INVALID_REMOTE_STREAM:"INVALID_REMOTE_STREAM",SUBSCRIBE_FAILED:"SUBSCRIBE_FAILED",INVALID_ROLE:"INVALID_ROLE",INVALID_PARAMETER_SWITCH_ROLE:"INVALID_PARAMETER_SWITCH_ROLE",INVALID_OPERATION_SWITCH_ROLE:"INVALID_OPERATION_SWITCH_ROLE",SWITCH_ROLE_TIMEOUT:"SWITCH_ROLE_TIMEOUT",SWITCH_ROLE_FAILED:"SWITCH_ROLE_FAILED",CLIENT_BANNED:"CLIENT_BANNED",INVALID_OPERATION_START_PUBLISH_CDN:"INVALID_OPERATION_START_PUBLISH_CDN",INVALID_OPERATION_STOP_PUBLISH_CDN:"INVALID_OPERATION_STOP_PUBLISH_CDN",INVALID_STREAM_ID:"INVALID_STREAM_ID",START_PUBLISH_CDN_FAILED:"START_PUBLISH_CDN_FAILED",STOP_PUBLISH_CDN_FAILED:"STOP_PUBLISH_CDN_FAILED",START_MIX_TRANSCODE:"START_MIX_TRANSCODE",STOP_MIX_TRANSCODE:"STOP_MIX_TRANSCODE",INVALID_AUDIO_VOLUME:"INVALID_AUDIO_VOLUME",ENABLE_SMALL_STREAM_PUBLISHED:"ENABLE_SMALL_STREAM_PUBLISHED",DISABLE_SMALL_STREAM_PUBLISHED:"DISABLE_SMALL_STREAM_PUBLISHED",NOT_SUPPORTED_SMALL_STREAM:"NOT_SUPPORTED_SMALL_STREAM",INVALID_SMALL_STREAM_PROFILE:"INVALID_SMALL_STREAM_PROFILE",INVALID_PARAMETER_REMOTE_STREAM:"INVALID_PARAMETER_REMOTE_STREAM",INVALID_OPERATION_CHANGE_SMALL:"INVALID_OPERATION_CHANGE_SMALL",REMOTE_NOT_PUBLISH_SMALL_STREAM:"REMOTE_NOT_PUBLISH_SMALL_STREAM",INVALID_SWITCH_DEVICE:"INVALID_SWITCH_DEVICE",INVALID_SWITCH_DEVICE_PUBLISHING:"INVALID_SWITCH_DEVICE_PUBLISHING",INVALID_REPLACE_TRACK:"INVALID_REPLACE_TRACK",INVALID_INITIALIZE_LOCAL_STREAM:"INVALID_INITIALIZE_LOCAL_STREAM",INVALID_ADD_TRACK_REPETITIVE:"INVALID_ADD_TRACK_REPETITIVE",INVALID_ADD_TRACK_REMOVING:"INVALID_ADD_TRACK_REMOVING",INVALID_ADD_TRACK_PUBLISHING:"INVALID_ADD_TRACK_PUBLISHING",INVALID_STREAM_INITIALIZED:"INVALID_STREAM_INITIALIZED",INVALID_ADD_TRACK_NUMBER:"INVALID_ADD_TRACK_NUMBER",INVALID_REMOVE_AUDIO_TRACK:"INVALID_REMOVE_AUDIO_TRACK",INVALID_REMOVE_AUDIO_ADDING:"INVALID_REMOVE_AUDIO_ADDING",INVALID_REMOVE_AUDIO_ON:"INVALID_REMOVE_AUDIO_ON",INVALID_REMOVE_TRACK_PUBLISHING:"INVALID_REMOVE_TRACK_PUBLISHING",INVALID_REMOVE_TRACK_NOT_TRACK:"INVALID_REMOVE_TRACK_NOT_TRACK",INVALID_REMOVE_TRACK_NUMBER:"INVALID_REMOVE_TRACK_NUMBER",INVALID_REPLACE_TRACK_NO_TRACK:"INVALID_REPLACE_TRACK_NO_TRACK",REPEAT_JOIN:"REPEAT_JOIN",CLIENT_DESTROYED:"CLIENT_DESTROYED",NOT_BUG_PACKAGE:"NOT_BUG_PACKAGE",START_MIX_TRANSCODE_FAILED:"START_MIX_TRANSCODE_FAILED",STOP_MIX_TRANSCODE_FAILED:"STOP_MIX_TRANSCODE_FAILED",MIX_TRANSCODE_NOT_STARTED:"MIX_TRANSCODE_NOT_STARTED",CANNOT_LESS_THAN_ZERO:"CANNOT_LESS_THAN_ZERO",MIX_PARAMS_VIDEO_FRAMERATE:"MIX_PARAMS_VIDEO_FRAMERATE",MIX_PARAMS_VIDEO_GOP:"MIX_PARAMS_VIDEO_GOP",MIX_PARAMS_AUDIO_BITRATE:"MIX_PARAMS_AUDIO_BITRATE",MIX_PARAMS_USER_Z_ORDER:"MIX_PARAMS_USER_Z_ORDER",MIX_PARAMS_NOT_SELF:"MIX_PARAMS_NOT_SELF",MIX_PARAMS_USER_STREAM:"MIX_PARAMS_USER_STREAM",INVALID_PLAY:"INVALID_PLAY",INVALID_ELEMENT_ID:"INVALID_ELEMENT_ID",INVALID_ELEMENT_ID_TYPE:"INVALID_ELEMENT_ID_TYPE",PLAY_FAILED:"PLAY_FAILED",INVALID_USERID:"INVALID_USERID",INVALID_CREATE_STREAM_SOURCE:"INVALID_CREATE_STREAM_SOURCE",INVALID_CREATE_STREAM_SCREEN:"INVALID_CREATE_STREAM_SCREEN",INVALID_CREATE_STREAM_AUDIO:"INVALID_CREATE_STREAM_AUDIO",INVALID_CREATE_STREAM_SCREEN_AUDIO:"INVALID_CREATE_STREAM_SCREEN_AUDIO",NOT_SUPPORTED_HTTP:"NOT_SUPPORTED_HTTP",NOT_SUPPORTED_WEBRTC:"NOT_SUPPORTED_WEBRTC",NOT_SUPPORTED_PROFILE:"NOT_SUPPORTED_PROFILE",NOT_SUPPORTED_MEDIA:"NOT_SUPPORTED_MEDIA",NOT_SUPPORTED_H264ENCODE:"NOT_SUPPORTED_H264ENCODE",NOT_SUPPORTED_H264DECODE:"NOT_SUPPORTED_H264DECODE",NOT_SUPPORTED_TRACK:"NOT_SUPPORTED_TRACK",NOT_SUPPORTED_SWITCH_DEVICE:"NOT_SUPPORTED_SWITCH_DEVICE",NOT_SUPPORTED_CAPTURE:"NOT_SUPPORTED_CAPTURE",NOT_SUPPORTED_AUX:"NOT_SUPPORTED_AUX",MICROPHONE_NOT_FOUND:"MICROPHONE_NOT_FOUND",CAMERA_NOT_FOUND:"CAMERA_NOT_FOUND",SIGNAL_RESPONSE_FAILED:"SIGNAL_RESPONSE_FAILED",CATCH_HANDLER_ERROR:"CATCH_HANDLER_ERROR",API_NOT_EXIST:"API_NOT_EXIST",CONNECTION_CLOSED:"CONNECTION_CLOSED",SUBSCRIBE_ALL_FALSE:"SUBSCRIBE_ALL_FALSE",SEI_NOT_SUPPORT:"SEI_NOT_SUPPORT",SEI_DISABLED:"SEI_DISABLED",SEI_EMPTY:"SEI_EMPTY",SEI_OVERSIZE:"SEI_OVERSIZE",SEI_BEFORE_PUBLISH:"SEI_BEFORE_PUBLISH",SEI_NOT_VIDEO:"SEI_NOT_VIDEO",CALL_FREQUENCY_LIMIT:"CALL_FREQUENCY_LIMIT",CONNECTION_ABORTED:"CONNECTION_ABORTED",API_CALL_ABORTED:"API_CALL_ABORTED",DUPLICATE_AUX:"DUPLICATE_AUX"},ue={AVOID_REPEATED_CALL(r){return`previous ${r.name}() is ongoing, please avoid repeated calls.`},INVALID_PARAMETER_REQUIRED({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' is a required param when calling ${t}(), received: ${i}.`},INVALID_PARAMETER_TYPE({key:r,rule:e,fnName:t,value:i}){let s=`${r||e.name}`,o="";return Array.isArray(e.type)?o=e.type.join("|"):o=e.type,`'${s}' must be type of ${o} when calling ${t}(), received type: ${te(i)}.`},INVALID_PARAMETER_EMPTY({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' cannot be '${i}' when calling ${t}().`},INVALID_PARAMETER_INSTANCE({key:r,rule:e,fnName:t,value:i}){let s=`${r||e.name}`,o=`${e.instanceOf.name||e.instanceOf}`;return`'${s}' must be instanceof ${o} when calling ${t}(), received type: ${te(i)}.`},INVALID_PARAMETER_RANGE({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' must be one of ${e.values.join("|")} when calling ${t}(), received: ${i}.`},INVALID_PARAMETER_MIN({key:r,rule:e,fnName:t,value:i}){return`the min value of ${r||e.name} is ${e.min}, received: ${i}.`},INVALID_PARAMETER_MAX({key:r,rule:e,fnName:t,value:i}){return`the max value of ${r||e.name} is ${e.max}, received: ${i}.`},API_CALL_TIMEOUT(r){return`${r.commandDesc||r.command} timeout observed.`},SIGNAL_CHANNEL_RECONNECTION_FAILED:"signal channel reconnection failed, please check your network.",SIGNAL_CHANNEL_SETUP_FAILED(r){return`SignalChannel setup failure: (errorCode: ${r.errorCode}, errorMsg: ${r.errorMsg} }).`},ERROR_MESSAGE(r){let e=`${r.type} failed`;return r.message&&(e=`${e}: ${r.message}.`),e},EXCHANGE_SDP_TIMEOUT:"exchange sdp timeout.",DOWNLINK_RECONNECTION_FAILED:"downlink reconnection failed, please check your network and re-join room.",EXCHANGE_SDP_FAILED(r){return`exchange sdp failed ${r.errMsg}.`},UPDATE_OFFER_TIMEOUT:"update offer timeout observed.",UPLINK_RECONNECTION_FAILED:"uplink reconnection failed, please check your network and publish again.",INVALID_RECORDID:"recordId must be an integer number.",INVALID_PURE_AUDIO:"pureAudioPushMode must be 1 or 2.",INVALID_STREAMID:"streamId must be a sting literal within 64 bytes, and not be empty.",INVALID_USER_DEFINE_RECORDID:"userDefineRecordId must be a sting literal contains (a-zA-Z),(0-9), underline and hyphen, within 64 bytes, and not be empty.",INVALID_USER_DEFINE_PUSH_ARGS:"userDefinePushArgs must be a sting literal within 256 bytes, and not be empty.",INVALID_PROXY:'proxy server url must start with "wss://".',INVALID_JOIN:"duplicate join() called.",INVALID_ROOMID_STRING(r){return`'${r}' must be validate string when useStringRoomId is true.`},INVALID_ROOMID_INTEGER(r){return`'${r}' must be an integer between [1, 4294967294] when useStringRoomId is false.`},INVALID_SIGNAL_CHANNEL:"SignalChannel is not ready yet.",JOIN_ROOM_TIMEOUT:"join room timeout.",JOIN_ROOM_FAILED({error:r,code:e}){return`Failed to join room - ${r} code: ${e}`},REJOIN_ROOM_FAILED(r){return`reJoin room: ${r.roomId} failed, please check your network.`},INVALID_DESTROY:"please call leave() before destroy().",INVALID_PUBLISH:"please call join() before publish().",INVALID_UNPUBLISH:"stream has not been published yet.",INVALID_AUDIENCE:`no permission to publish() under live/${"audience"}, please call switchRole("${"anchor"}") firstly before publish().`,INVALID_INITIALIZE:"cannot publish stream because stream is not initialized, is switching device, or has been closed.",INVALID_DUPLICATE_PUBLISHING(r){return`duplicate ${r} stream publishing, please unpublish your prev ${r} stream and then re-publish.`},INVALID_SUBSCRIBE_UNDEFINED:"stream is undefined or null.",INVALID_SUBSCRIBE_LOCAL:"stream cannot be LocalStream.",INVALID_REMOTE_STREAM:"remoteStream does not exist because it has been unpublished by remote peer.",SUBSCRIBE_FAILED({message:r,userId:e,streamType:t}){return`failed to subscribe ${e} ${t} stream, reason: ${r}.`},INVALID_ROLE:"switchRole can only be called in live mode.",INVALID_PARAMETER_SWITCH_ROLE:`role could only be set to a value as ${"anchor"} or ${"audience"}.`,INVALID_OPERATION_SWITCH_ROLE:"please call join() before switchRole().",SWITCH_ROLE_TIMEOUT:"switchRole timeout.",SWITCH_ROLE_FAILED(r){return`switchRole failed, errCode: ${r.code} errMsg: ${r.message}.`},CLIENT_BANNED(r){return`client was banned because of ${r.message}.`},INVALID_OPERATION_START_PUBLISH_CDN:"please call startPublishCDNStream() after join room and publish the local stream.",INVALID_OPERATION_STOP_PUBLISH_CDN:"please call startPublishCDNStream() before stopPublishCDNStream().",START_PUBLISH_CDN_FAILED(r){return`startPublishCDNStream failed, errMsg: ${r.message}.`},STOP_PUBLISH_CDN_FAILED(r){return`stopPublishCDNStream failed, errMsg: ${r.message}.`},INVALID_STREAM_ID(r){return`'${r}' can only consist of uppercase and lowercase english letters (a-zA-Z), numbers (0-9), hyphens and underscores.`},START_MIX_TRANSCODE:"please call startMixTranscode() after join().",STOP_MIX_TRANSCODE:"please call stopMixTranscode() after startMixTranscode().",INVALID_AUDIO_VOLUME:"interval must be a number.",ENABLE_SMALL_STREAM_PUBLISHED:"Cannot enable small stream after localStream published.",DISABLE_SMALL_STREAM_PUBLISHED:"Cannot disable small stream after localStream published.",NOT_SUPPORTED_SMALL_STREAM:"your browser does not support opening small stream.",INVALID_SMALL_STREAM_PROFILE:"small stream profile is invalid.",INVALID_PARAMETER_REMOTE_STREAM:"remoteStream is invalid.",INVALID_OPERATION_CHANGE_SMALL:"cannot switch to the small stream without subscribing to the video of remoteStream.",REMOTE_NOT_PUBLISH_SMALL_STREAM:"remote peer does not publish small stream.",INVALID_SWITCH_DEVICE:"cannot switch device on current stream.",INVALID_SWITCH_DEVICE_PUBLISHING:"cannot switch device when publishing localStream.",INVALID_REPLACE_TRACK:"cannot replace track when publishing localStream.",INVALID_INITIALIZE_LOCAL_STREAM:"local stream has not initialized yet.",INVALID_ADD_TRACK_REPETITIVE:"previous addTrack is ongoing, please avoid repetitive execution.",INVALID_ADD_TRACK_REMOVING:"cannot add track when a track is removing.",INVALID_ADD_TRACK_PUBLISHING:"cannot add track when publishing localStream.",INVALID_STREAM_INITIALIZED:"your local stream haven't been initialized yet.",INVALID_ADD_TRACK_NUMBER:"a Stream has at most one audio track and one video track.",INVALID_REMOVE_AUDIO_TRACK:"remove audio track is not supported on your browser.",INVALID_REMOVE_AUDIO_ADDING:"cannot remove track when a track is adding.",INVALID_REMOVE_AUDIO_ON:"previous removeTrack is ongoing, please avoid repetitive execution.",INVALID_REMOVE_TRACK_PUBLISHING:"cannot remove track when publishing localStream.",INVALID_REMOVE_TRACK_NOT_TRACK:"localStream has not this track.",INVALID_REMOVE_TRACK_NUMBER:"remove the only video track is not supported, please use replaceTrack or muteVideo.",INVALID_REPLACE_TRACK_NO_TRACK(r){return`cannot replace ${r.kind} track because stream has not ${r.kind} track`},NOT_BUG_PACKAGE:"You need to buy packages, refer to tencent console.",START_MIX_TRANSCODE_FAILED(r){return`startMixTranscode failed, errMsg: ${r.message}.`},STOP_MIX_TRANSCODE_FAILED(r){return`stopMixTranscode failed, errMsg: ${r.message}.`},MIX_TRANSCODE_NOT_STARTED:"mixTranscode has not been started.",CANNOT_LESS_THAN_ZERO({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' cannot be less than 0 when calling ${t}().`},MIX_PARAMS_VIDEO_FRAMERATE:"'config.videoFramerate' should be an integer between 0 and 30, excluding 0.",MIX_PARAMS_VIDEO_GOP:"'config.videoGOP' should be an integer between 1 and 8.",MIX_PARAMS_AUDIO_BITRATE:"'config.audioBitrate' should be an integer between 32 and 192.",MIX_PARAMS_USER_Z_ORDER(r){return`'${r}' is required and must be between 1 and 15.`},MIX_PARAMS_NOT_SELF:"'config.mixUsers' must contain self.",MIX_PARAMS_USER_STREAM:"'config.videoWidth' and 'config.videoHeight' of output stream should be contain all mix stream.",INVALID_PLAY:"duplicate play() call observed, please stop() firstly.",INVALID_ELEMENT_ID:({key:r,fnName:e})=>`'${r}' is not found in the document object when calling ${e}().`,INVALID_ELEMENT_ID_TYPE:({key:r,fnName:e,type:t})=>`the element corresponding to '${r}' must be instanceof HTMLElement when calling ${e}(), received: ${t}.`,PLAY_FAILED:r=>`${r.media} play failed\uFF0Cbrowser exception: ${r.error.toString()}`,INVALID_USERID:"userId cannot be all spaces.",INVALID_CREATE_STREAM_SOURCE:"LocalStream must be created by createStream() with either audio/video or audioSource/videoSource, but can not be mixed with audio/video and audioSource/videoSource.",INVALID_CREATE_STREAM_SCREEN:"screen/video cannot be both true.",INVALID_CREATE_STREAM_AUDIO:"audio/screenAudio cannot be both true.",INVALID_CREATE_STREAM_SCREEN_AUDIO:"when screen is true, screenAudio can be configured.",NOT_SUPPORTED_HTTP:"http protocol does not support the ability to capture microphone, camera and screen. please use https to deploy your page.",NOT_SUPPORTED_WEBRTC:"your browser or environment does not support full WebRTC capabilities.",NOT_SUPPORTED_PROFILE:"your browser does not support setVideoProfile.",NOT_SUPPORTED_MEDIA:"your browser or environment does not support navigator.mediaDevices.",NOT_SUPPORTED_H264ENCODE:"your device does not support H.264 encoding.",NOT_SUPPORTED_H264DECODE:"your device does not support H.264 decoding.",NOT_SUPPORTED_TRACK(r){return`${r}Track is not supported on your browser.`},NOT_SUPPORTED_SWITCH_DEVICE:"switchDevice is not supported on your browser.",NOT_SUPPORTED_CAPTURE:"Your browser or environment does not support screen sharing, please check whether the browser version.",MICROPHONE_NOT_FOUND:"no microphone detected, please check your microphone.",CAMERA_NOT_FOUND:"no camera detected, please check your camera.",SIGNAL_RESPONSE_FAILED(r){return`${r.signalResponse} failed, response code is ${r.code} , errMsg: ${r.message}.`},CATCH_HANDLER_ERROR({name:r,event:e}){return`an error was caught on ${r}.on('${e}', handler), please check your code on 'handler'.`},API_NOT_EXIST({name:r}){return`experimental api ${r} does not exist.`},REPEAT_JOIN:r=>`[${r}] is calling client.join api or has already joined room, please avoid repeated join.`,CONNECTION_CLOSED:"remoteStream has been unsubscribed or unpublished by remote user.",SUBSCRIBE_ALL_FALSE:"cannot subscribe when both audio & video are false, use client.unsubscribe() instead",CLIENT_DESTROYED({funName:r}){return`failed to call ${r}() because client was destroyed.`},SEI_NOT_SUPPORT:r=>`not support to sendSEIMessage${r===!1?" without using h264 codec":""}`,SEI_DISABLED:"SEI is disabled",SEI_EMPTY:"buffer cannot be empty",SEI_OVERSIZE:r=>`buffer size(${r}) is over 1000 Bytes`,SEI_BEFORE_PUBLISH:"please call sendSEIMessage() after publish() success",SEI_NOT_VIDEO:"cannot send sei when localStream has not video.",CALL_FREQUENCY_LIMIT:({isSize:r,name:e,timesInSecond:t,maxSizeInSecond:i})=>`api ${e} call ${r?"size":"times"} is over ${r?`${i} bytes`:t} in a second.`,CONNECTION_ABORTED(r){return`connection aborted due to: ${r}`},API_CALL_ABORTED(r){let e;return r.message.includes("REMOTE_STREAM_NOT_EXIST")?e=`Subscribe ${r.userId} ${r.streamType} stream aborted, reason: remote user ${r.userId} unpublished stream.`:e=`API aborted, reason: ${r.message}`,e},DUPLICATE_AUX:"only one auxiliary stream can be published in a room.",NOT_SUPPORTED_AUX:"publish auxiliary stream is not supported on your browser.",INVALID_PARAMETER_STREAMTYPE:r=>`'streamType' is required when 'userId' is not '*', calling ${r}()`};var tu=(r,e)=>e?`${ct}/${r}/${e}`:`${ct}/${r}/index.html`;var pp=()=>{if(window.TRTC_ERROR_INFO&&window.TRTC_ERROR_LINK)return{TRTC_ERROR_INFO:window.TRTC_ERROR_INFO,TRTC_ERROR_LINK:window.TRTC_ERROR_LINK};let r=localStorage.getItem(Sa);if(r){r=JSON.parse(r);let e=document.createElement("script");e.type="text/javascript",e.text=r.message,document.body.appendChild(e);let t=window.TRTC_ERROR_INFO,i=window.TRTC_ERROR_LINK;return document.body.removeChild(e),{TRTC_ERROR_INFO:t,TRTC_ERROR_LINK:i}}return{}};function v(r){let{key:e,data:t,link:i,addDocLink:s=!0}=r,o="",n="",a="";W(ue[e])?o=ue[e](t):j(ue[e])&&(o=ue[e]);let{TRTC_ERROR_INFO:c,TRTC_ERROR_LINK:d}=pp();i?a=`${i.className}.html#${i.fnName}`:d&&d[e]&&(W(d[e])?a=d[e](t):j(d[e])&&(a=d[e]));let u=o;return ut()&&(c&&c[e]&&(W(c[e])?n=c[e](t):j(c[e])&&(n=c[e])),n&&(s?u=`${n}
\u8BF7\u67E5\u770B\u6587\u6863: ${tu("zh-cn",a)}

`:u=`${n}

`,u+=o)),s&&(u+=` 
Refer to: ${tu("en",a)}
`),u}var X={result:!1,detail:{isBrowserSupported:!1,isWebRTCSupported:!1,isWebCodecsSupported:!1,isMediaDevicesSupported:!1,isScreenShareSupported:!1,isSmallStreamSupported:!1,isH264EncodeSupported:!1,isVp8EncodeSupported:!1,isH264DecodeSupported:!1,isVp8DecodeSupported:!1}},mp=new Map([[z,["Firefox",Ys]],[Fi,["Edg",Xs]],[ji,["Chrome",lo]],[xe,["Safari",Ki]],[nt,["TBS",Zs]],[Pr,["XWEB",eo]],[Hi&&$t,["WeChat",to]],[Mr,["QQ(Win)",io]],[Wi,["QQ(Mobile)",wi]],[Gi,["QQ(Mobile X5)",wi]],[Lr,["QQ(Mac)",ro]],[xr,["QQ(iPad)",so]],[Ur,["MI",oo]],[wr,["HW",no]],[Br,["Samsung",ao]],[$r,["OPPO",co]],[Fr,["VIVO",uo]],[$i,["EDGE",zs]],[Or,["SogouMobile",qs]],[kr,["Sogou",Qs]]]);function _p(){let r=mp.get(!0),e=r?r[0]:"unknown",t=r?r[1]:"unknown";return{browserName:e,browserVersion:t}}var wa=function(){return!(da||$i||Fi&&ca<80||z&&aa<56)},ru=function(){return["VideoDecoder","VideoEncoder","AudioEncoder","AudioDecoder","MediaStreamTrackGenerator"].every(e=>e in window)},vo=function(){if(!navigator.mediaDevices)return Yt()||I.error(ue.NOT_SUPPORTED_MEDIA),!1;let r=["getUserMedia","enumerateDevices"];return r.filter(e=>e in navigator.mediaDevices).length===r.length},iu=!1;function Yt(){return location.protocol==="http:"&&!Ke?(iu||I.error(v({key:b.NOT_SUPPORTED_HTTP})),iu=!0,!0):!1}var fp=function(){return window?.OffscreenCanvas&&window?.MediaStreamTrackProcessor&&window?.MediaStreamTrackGenerator},su=async function(){if(X.detail.isH264EncodeSupported||X.detail.isVp8EncodeSupported)return{isH264EncodeSupported:X.detail.isH264EncodeSupported,isVp8EncodeSupported:X.detail.isVp8EncodeSupported};let r,e=!1,t=!1;try{let i=new RTCPeerConnection,s=document.createElement(l.CANVAS);s.getContext("2d");let o=s.captureStream(0);return i.addTrack(o.getVideoTracks()[0],o),r=await i.createOffer(),r.sdp.toLowerCase().indexOf("h264")!==-1&&(e=!0),r.sdp.toLowerCase().indexOf("vp8")!==-1&&(t=!0),i.close(),X.detail.isH264EncodeSupported=e,X.detail.isVp8EncodeSupported=t,{isH264EncodeSupported:X.detail.isH264EncodeSupported,isVp8EncodeSupported:X.detail.isVp8EncodeSupported}}catch{return{isH264EncodeSupported:!1,isVp8EncodeSupported:!1}}},Do=async function(){if(X.detail.isH264DecodeSupported&&X.detail.isVp8DecodeSupported)return{isH264DecodeSupported:X.detail.isH264DecodeSupported,isVp8DecodeSupported:X.detail.isVp8DecodeSupported};let r,e=!1,t=!1;try{let i=new RTCPeerConnection;return r=await i.createOffer({offerToReceiveAudio:!0,offerToReceiveVideo:!0}),r.sdp.toLowerCase().indexOf("h264")!==-1&&(e=!0),r.sdp.toLowerCase().indexOf("vp8")!==-1&&(t=!0),i.close(),{isH264DecodeSupported:e,isVp8DecodeSupported:t}}catch{return{isH264DecodeSupported:!1,isVp8DecodeSupported:!1}}},Ba=async function(){if(X.result)return X;let r=wa(),e=Fa(),t=ru(),i=vo(),{isH264EncodeSupported:s,isVp8EncodeSupported:o}=await su(),{isH264DecodeSupported:n,isVp8DecodeSupported:a}=await Do();return X.result=r&&e&&i&&(s||o)&&(n||a),X.detail.isBrowserSupported=r,X.detail.isWebRTCSupported=e,X.detail.isWebCodecsSupported=t,X.detail.isMediaDevicesSupported=i,X.detail.isScreenShareSupported=is(),X.detail.isSmallStreamSupported=rs(),X.detail.isH264EncodeSupported=s,X.detail.isVp8EncodeSupported=o,X.detail.isH264DecodeSupported=n,X.detail.isVp8DecodeSupported=a,X.result||I.error(`${navigator.userAgent} ${Ma(X.detail,!1)}`),X},Tp=function(){return X.result},is=function(){return!!(navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)},Ep=(r,e,t)=>{location.protocol==="http:"&&!Ke&&(r[e]=()=>{throw new A({code:E.INVALID_OPERATION,message:ue.NOT_SUPPORTED_HTTP})})},zt=function(r){return r.type==="candidate-pair"&&r.nominated&&(r.state==="in-progress"||r.state==="succeeded")?!(Z(r.selected)&&!r.selected):!1};function ou(){let r="";if(screen.width){let e=screen.width?screen.width*window.devicePixelRatio:"",t=screen.height?screen.height*window.devicePixelRatio:"";r+=`${e} * ${t}`}return r}function nu(){return navigator.getUserMedia||navigator.mediaDevices&&navigator.mediaDevices.getUserMedia}function au(){let r={isSupported:!1},e=["AudioContext","webkitAudioContext","mozAudioContext","msAudioContext"];for(let t=0;t<e.length;t++)if(e[t]in window){r.isSupported=!0;break}return r.isSupported}function cu(){return"captureStream"in HTMLCanvasElement.prototype}function rs(){return Hi||We||ci&&ci<63?!1:!!(wa()&&cu())}var gp=function(){if(f(window.RTCRtpTransceiver)||!("currentDirection"in RTCRtpTransceiver.prototype))return!1;let r=null,e=!1;try{r=new RTCPeerConnection({sdpSemantics:Wt}),r.addTransceiver(l.AUDIO),e=!0}catch{}return r?.close(),e};function Ti(){return"RTCPeerConnection"in window&&"getReceivers"in window.RTCPeerConnection.prototype}function sr(){return"RTCPeerConnection"in window&&"getSenders"in window.RTCPeerConnection.prototype}function pt(){return"RTCPeerConnection"in window&&"getTransceivers"in window.RTCPeerConnection.prototype}function Be(){return ha===11?!1:"RTCPeerConnection"in window&&"addTransceiver"in window.RTCPeerConnection.prototype}var ss=(()=>!(!Be()||ai&&ci<74))();function Sp(){return"RTCRtpTransceiver"in window&&"stop"in window.RTCRtpTransceiver.prototype}function Ip(){return"RTCRtpSender"in window&&"replaceTrack"in window.RTCRtpSender.prototype}function os(){return"RTCRtpSender"in window&&"setParameters"in window.RTCRtpSender.prototype&&sr()}var $e=window.MediaStreamTrack&&"getSettings"in MediaStreamTrack.prototype,$a=window.MediaStreamTrack&&"getCapabilities"in MediaStreamTrack.prototype,Ze="RTCRtpSender"in window&&"createEncodedStreams"in window.RTCRtpSender.prototype&&at()>=86,Fa=function(){return["RTCPeerConnection","webkitRTCPeerConnection","RTCIceGatherer"].filter(e=>e in window).length>0};function du(){let r={AudioDecoder:!1,AudioEncoder:!1,VideoDecoder:!1,VideoEncoder:!1,ImageDecoder:!1};return f(window.AudioDecoder)||(r.AudioDecoder=!0),f(window.AudioEncoder)||(r.AudioEncoder=!0),f(window.VideoDecoder)||(r.VideoDecoder=!0),f(window.VideoEncoder)||(r.VideoEncoder=!0),f(window.ImageDecoder)||(r.ImageDecoder=!0),r}function uu(){return"mediaSession"in navigator&&!f(navigator.mediaSession.setActionHandler)}function lu(){return!f(window.WebTransport)}function Rp(){let r={browser:`${ot.name}/${ot.version}`,os:ho(),displayResolution:ou(),isScreenShareSupported:is(),isWebRTCSupported:Fa(),isGetUserMediaSupported:nu(),isWebAudioSupported:au(),isWebSocketsSupported:"WebSocket"in window&&window.WebSocket.CLOSING===2,isWebCodecSupported:du(),isMediaSessionSupported:uu(),isWebTransportSupported:lu()};return navigator.userAgent.includes("miniProgram")&&(r.browser=`mini/${r.browser}`),r}var pu=pe(ve(),1),hu=Symbol("instance"),Tf=Symbol("abortCtrl"),Ha=Symbol("cacheResult"),Ei=class{constructor(e,t,i){this.oldState=e,this.newState=t,this.action=i,this.aborted=!1}abort(e){this.aborted=!0,cs.call(e,this.oldState,new Error(`action '${this.action}' aborted`))}toString(){return`${this.action}ing`}},ns=class extends Error{constructor(e,t,i){super(t),this.state=e,this.message=t,this.cause=i}};function Ap(r){return typeof r=="object"&&r&&"then"in r}var as=new Map;function q(r,e,t={}){return(i,s,o)=>{let n=t.action||s;if(!t.context){let c=as.get(i)||[];as.has(i)||as.set(i,c),c.push({from:r,to:e,action:n})}let a=o.value;o.value=function(...c){let d=this;if(t.context&&(d=k.get(typeof t.context=="function"?t.context.call(this,...c):t.context)),d.state===e)return d[Ha];d.state instanceof Ei&&d.state.action==t.abortAction&&d.state.abort(d);let u=null;if(Array.isArray(r)?r.length==0?d.state instanceof Ei&&d.state.abort(d):(typeof d.state!="string"||!r.includes(d.state))&&(u=new ns(d._state,`${d.name} ${n} to ${e} failed: current state ${d._state} not in from config`)):r!==d.state&&(u=new ns(d._state,`${d.name} ${n} to ${e} failed: current state ${d._state} not from ${r}`)),u)if(t.fail)t.fail.call(this,u);else{if(t.ignoreError)return u;throw u}let h=d.state,p=new Ei(h,e,n);cs.call(d,p);let g=N=>{var Ce;return d[Ha]=N,p.aborted||(cs.call(d,e),(Ce=t.success)===null||Ce===void 0||Ce.call(this,d[Ha])),N},C=N=>{let Ce=N instanceof Error?N.message:String(N);if(cs.call(d,h,N),t.fail)t.fail.call(this,new ns(d._state,`action '${n}' failed :${Ce}`,N instanceof Error?N:new Error(Ce)));else{if(t.ignoreError)return N;throw N}};try{let N=a.apply(this,c);return Ap(N)?N.then(g).catch(C):g(N)}catch(N){C(N)}}}}var Cp=(()=>typeof window<"u"&&window.__AFSM__?(t,i)=>{window.dispatchEvent(new CustomEvent(t,{detail:i}))}:typeof importScripts<"u"?(t,i)=>{postMessage({type:t,payload:i})}:()=>{})();function cs(r,e){let t=this._state;this._state=r;let i=r.toString();r&&this.emit(i,t),this.emit(k.STATECHANGED,r,t,e),this.updateDevTools({value:r,old:t,err:e instanceof Error?e.message:String(e)})}var k=class extends pu.default{constructor(e,t,i){super(),this.name=e,this.groupName=t,this._state=k.INIT,e||(e=Date.now().toString(36)),i?Object.setPrototypeOf(this,i):i=Object.getPrototypeOf(this),t||(this.groupName=this.constructor.name);let s=i[hu];s?this.name=s.name+"-"+s.count++:i[hu]={name:this.name,count:0},this.updateDevTools({diagram:this.stateDiagram})}get stateDiagram(){let e=Object.getPrototypeOf(this),t=as.get(e)||[],i=new Set,s=[],o=[],n=new Set,a=Object.getPrototypeOf(e);as.has(a)&&(a.stateDiagram.forEach(d=>i.add(d)),a.allStates.forEach(d=>n.add(d))),t.forEach(({from:d,to:u,action:h})=>{typeof d=="string"?s.push({from:d,to:u,action:h}):d.length?d.forEach(p=>{s.push({from:p,to:u,action:h})}):o.push({to:u,action:h})}),s.forEach(({from:d,to:u,action:h})=>{n.add(d),n.add(u),n.add(h+"ing"),i.add(`${d} --> ${h}ing : ${h}`),i.add(`${h}ing --> ${u} : ${h} \u{1F7E2}`),i.add(`${h}ing --> ${d} : ${h} \u{1F534}`)}),o.forEach(({to:d,action:u})=>{i.add(`${u}ing --> ${d} : ${u} \u{1F7E2}`),n.forEach(h=>{h!==d&&i.add(`${h} --> ${u}ing : ${u}`)})});let c=[...i];return Object.defineProperties(e,{stateDiagram:{value:c},allStates:{value:n}}),c}static get(e){let t;return typeof e=="string"?(t=k.instances.get(e),t||k.instances.set(e,t=new k(e,void 0,Object.create(k.prototype)))):(t=k.instances2.get(e),t||k.instances2.set(e,t=new k(e.constructor.name,void 0,Object.create(k.prototype)))),t}static getState(e){var t;return(t=k.get(e))===null||t===void 0?void 0:t.state}updateDevTools(e={}){Cp(k.UPDATEAFSM,Object.assign({name:this.name,group:this.groupName},e))}get state(){return this._state}set state(e){cs.call(this,e)}};k.STATECHANGED="stateChanged";k.UPDATEAFSM="updateAFSM";k.INIT="[*]";k.ON="on";k.OFF="off";k.instances=new Map;k.instances2=new WeakMap;var mu=window?.requestIdleCallback||function(r){let e=Date.now();return setTimeout(()=>{r({didTimeout:!1,timeRemaining(){return Math.max(0,50-(Date.now()-e))}})},1e3)},yp=window?.cancelIdleCallback||function(r){clearTimeout(r)},bp=window?.cancelAnimationFrame||window?.mozCancelAnimationFrame,ds=class{static generateTaskID(){return this.currentTaskID++}static run(e=Zi,t,i){e===_i?i={delay:2e3,count:0,backgroundTask:!0,...i}:e===dt?i={delay:1e4,count:0,...i}:e===qr?i={fps:60,delay:16.6,count:0,backgroundTask:!0,...i}:i={delay:2e3,count:0,backgroundTask:!0,...i},Xe(t)&&(i={...i,...t}),W(e)&&(t=e,e=Zi);let s={taskID:this.generateTaskID(),loopCount:0,intervalID:null,timeoutID:null,rafID:null,ricID:null,taskName:e,callback:t,...i};return this.taskMap.set(s.taskID,s),this[e](s),s.taskID}static interval(e){let t=()=>{e.callback(),e.loopCount+=1,this.isBreakLoop(e)};return e.intervalID=setInterval(t,e.delay)}static intervalInWorker(e){e.delay=(1e3/e.fps).toFixed(2);let t=new Worker(URL.createObjectURL(new Blob([`
        let timerID = null;
        self.onmessage = function (e) {
          if (e.data === 'start') {
            timerID = setInterval(() => {
              self.postMessage('tick');
            }, ${e.delay});
          } else if (e.data === 'stop') {
            clearInterval(timerID);
          }
        };
      `])));t.onmessage=i=>{i.data==="tick"&&(e.callback(),e.loopCount+=1,this.isBreakLoop(e)&&t.postMessage("stop"))},e.worker=t,t.postMessage("start")}static timeout(e){let t=()=>{if(e.callback(),e.loopCount+=1,!this.isBreakLoop(e))return e.timeoutID=setTimeout(t,e.delay)};return e.timeoutID=setTimeout(t,e.delay)}static ric(e){let t=U(),i,s=()=>{if(i=U()-t,i>=e.delay&&(t=U()-Math.floor(i%e.delay),e.callback(),e.loopCount+=1),!this.isBreakLoop(e))return e.ricID=mu(s,{timeout:e.delay})};return e.ricID=mu(s,{timeout:e.delay})}static raf(e){e.delay=(1e3/e.fps).toFixed(2);let t=U(),i,s=()=>{if(document.hidden&&e.backgroundTask)return i=U()-t,t=U(),e.callback(),e.loopCount+=1,this.isBreakLoop(e)?void 0:e.timeoutID=setTimeout(s,e.delay-Math.floor(i%e.delay));if(i=U()-t,i>=e.delay&&(t=U()-Math.floor(i%e.delay),e.callback(),e.loopCount+=1),!this.isBreakLoop(e))return e.rafID=requestAnimationFrame(s)};if(e.rafID=requestAnimationFrame(s),e.backgroundTask){let o=()=>{if(document.hidden){let n=U()-t;n>=e.delay?s():e.timeoutID=setTimeout(s,e.delay-n)}};document.addEventListener("visibilitychange",o),e.onVisibilitychange=o,document.hidden&&o()}return e.taskID}static hasTask(e){return this.taskMap.has(e)}static clearTask(e){if(!this.taskMap.has(e))return!0;let{intervalID:t,timeoutID:i,rafID:s,ricID:o,onVisibilitychange:n,worker:a}=this.taskMap.get(e);return a&&a.terminate(),t&&clearInterval(t),i&&clearTimeout(i),s&&bp(s),o&&yp(o),n&&document.removeEventListener("visibilitychange",n),this.taskMap.delete(e),!0}static isBreakLoop(e){return this.taskMap.has(e.taskID)?e.count!==0&&e.loopCount>=e.count?(this.clearTask(e.taskID),!0):!1:!0}};ds.taskMap=new Map,ds.currentTaskID=1;var J=ds;var Af={STREAM_ADDED:"stream-added",STREAM_REMOVED:"stream-removed",STREAM_UPDATED:"stream-updated",STREAM_SUBSCRIBED:"stream-subscribed",CONNECTION_STATE_CHANGED:"connection-state-changed",PEER_JOIN:"peer-join",PEER_LEAVE:"peer-leave",MUTE_AUDIO:"mute-audio",MUTE_VIDEO:"mute-video",UNMUTE_AUDIO:"unmute-audio",UNMUTE_VIDEO:"unmute-video",CLIENT_BANNED:"client-banned",NETWORK_QUALITY:"network-quality",AUDIO_VOLUME:"audio-volume",SEI_MESSAGE:l.SEI_MESSAGE,ERROR:"error"};var mt={LOADED_DATA:l.LOADEDDATA,MEDIA_TRACK_CHANGED:"media-track-changed",PLAYER_STATE_CHANGED:"player-state-changed"};var Ga=class{constructor(){this._roomIdMap=new Map;typeof registerProcessor>"u"&&(this._configs={sdkAppId:"",userId:"",version:Se,env:bt.QCLOUD,browserVersion:ot.name+ot.version,ua:navigator.userAgent})}setConfig({sdkAppId:e,env:t,userId:i,roomId:s}){e!==this._configs.sdkAppId&&(this._configs.sdkAppId=String(e)),this._configs.env=t,this._configs.userId=i,this._roomIdMap.set(i,String(s))}logSuccessEvent(e){Ke||!I.isAbleToUpload||this._configs.env===bt.QCLOUD&&this.uploadEventToKibana({...e,result:"success"})}logFailedEvent(e){if(Ke||!I.isAbleToUpload)return;let{eventType:t,code:i,error:s,userId:o}=e,n={roomId:this._roomIdMap.get(o||this._configs.userId),userId:o,eventType:t,result:"failed",code:i||s?.extraCode||s?.code||E.UNKNOWN};this._configs.env===bt.QCLOUD&&this.uploadEventToKibana({...n,error:s})}uploadEventToKibana(e){let t=`stat-${e.eventType}-${e.result}`;(e.eventType==="delta-join"||e.eventType==="delta-leave"||e.eventType==="delta-publish")&&(t=`${e.eventType}:${e.delta}`),this.uploadEvent({log:t,userId:e.userId}),e.result==="failed"&&(t=`stat-${e.eventType}-${e.result}-${e.code}`,this.uploadEvent({log:t,userId:e.userId,error:e.error}))}uploadEvent({log:e,userId:t,error:i}){let s={timestamp:Js(),sdkAppId:this._configs.sdkAppId,userId:t||this._configs.userId,version:Se,log:e};i&&(s.errorInfo=i.message),this.sendRequest(fi(this._configs.sdkAppId,li.LOG),s)}sendRequest(e,t){if(!I.isAbleToUpload){setTimeout(()=>{this.sendRequest(e,t)},1e3);return}Jt({url:e,body:JSON.stringify(t)}).catch(()=>{})}},Y=new Ga;var Dt="trtc_autoplay",Wa=`${Dt}_mask`,or=`${Dt}_wrapper`,_u=`${Dt}_header`,ja=`${Dt}_content`,Oo=`${Dt}_action_wrapper`,Ka=`${Dt}_question`,Ja=`${Dt}_collapse`,ko=`${Dt}_action_confirm`,fu=`${Dt}_detail`,Tu="#2473E8",za="dialog",Np=`${za}-show`,vp=`${za}-1`,Dp=`${za}-2`,Eu=!1,Xa=()=>!!document.querySelector(`.${or}`),Su=`${ct}/${ut()?"zh-cn":"en"}/tutorial-21-advanced-auto-play-policy.html`,gu=`<br><a href='${Su}' target='_blank'>${ut()?"\u5176\u4ED6\u65B9\u6848\uFF1F":"Any other solution?"}</a>`,Op=`${ut()?`\u6D4F\u89C8\u5668\u81EA\u52A8\u64AD\u653E\u7B56\u7565\uFF1A\u5728\u7528\u6237\u4E0E\u9875\u9762\u4EA7\u751F\u4EA4\u4E92\uFF08\u70B9\u51FB\u3001\u89E6\u6478\uFF09\u4E4B\u524D\uFF0C\u6D4F\u89C8\u5668\u7981\u6B62\u64AD\u653E\u6709\u58F0\u5A92\u4F53\u3002\u8BE5\u5F39\u7A97\u7528\u4E8E\u5E2E\u52A9\u7528\u6237\u6062\u590D\u97F3\u89C6\u9891\u64AD\u653E\u3002${gu}`:`Autoplay Policy: Before user interacts with the web page (clicking, touching), page will not be allowed to play media with sound. This Dialog is used to help users resume playback. ${gu}`}`,Ya=class{content="\u97F3\u89C6\u9891\u64AD\u653E\u88AB\u6D4F\u89C8\u5668\u62E6\u622A\uFF0C\u8BF7\u70B9\u51FB\u201C\u6062\u590D\u64AD\u653E\u201D\u3002";_dialogNode=null;_bodyPosition="";_showDetail=!1;_isCollapseClicked=!1;_isQuestionClicked=!1;constructor(){if(ut()||(this.content='Media playback failed. Click the "Resume" to resume playback.'),!Eu){let e=document.createElement("style");e.innerHTML=`.${Wa}{position:fixed;top:0;left:0;right:0;bottom:0;width:100vw;height:100vh;display:flex;justify-content:center;align-items:center;background:rgba(0,0,0,0.5);z-index:1500;}.${Wa} div:not(.${Oo}){display:block !important;}.${or}{padding:14px;background:#fff;border-radius:3px;box-shadow:0px 3px 15px #434343;border:1px solid #d1cfcf;max-width:500px;}.${or} a{color:${Tu};}.${_u}{overflow:hidden;text-overflow:ellipsis;font-size:16px;font-weight:600;}.${ja}{margin:8px 0;}.${Oo}{width:100%;display:flex !important;align-items:center;justify-content:right;float:right;}.${Ja}{margin-right:auto;cursor:pointer}.${Ka}{height:100%;line-height:16px;cursor:pointer;}.${ko}{margin-left:8px;color:#fff;background:${Tu};padding:4px 12px;outline:none;border:1px solid;border-radius:3px;font-weight:bold;}.${ko}:hover{opacity:0.9;}.${Ja},.${ko},.${ja},.${Ka}{font-size:14px;}@media screen and (max-width:750px){.${or}{width:80vw;}}`,document.head.appendChild(e),Eu=!0}this.addDiaLog()}createDiaLog(){let e=document.createElement("template");e.innerHTML=`<div class="${Wa}"><div class='${or}'><div class='${_u}'>${location.host}</div><div class='${ja}'>${this.content}</div><div class='${fu}' style="visibility:hidden;width:100%;height:0;font-size:12px;color:gray;">${Op}</div><div class='${Oo}'></div></div></div>`.trim();let t=document.createElement("button");t.className=ko,t.innerText=ut()?"\u6062\u590D\u64AD\u653E":"Resume",t.onclick=this.onConfirm.bind(this);let i=document.createElement("div");i.className=Ka,i.innerHTML=`<?xml version="1.0" encoding="UTF-8"?>
    <svg class="icon" width="18" height="18" p-id="2030" t="1639646523624" version="1.1" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path d="m464 784.35c0 26.51 21.49 48 48 48s48-21.49 48-48-21.49-48-48-48-48 21.49-48 48z" p-id="2031"/>
    <path d="m512 960c-247.04 0-448-200.96-448-448s200.96-448 448-448 448 200.96 448 448-200.96 448-448 448zm0-831.71c-211.58 0-383.71 172.13-383.71 383.71 0 211.55 172.13 383.71 383.71 383.71 211.55 0 383.71-172.16 383.71-383.71 0-211.58-172.16-383.71-383.71-383.71z" p-id="2032"/>
    <path d="m512 673.7c-17.665 0-32.001-14.336-32.001-31.999v-54.112c0-52.353 40-92.352 75.328-127.65 25.887-25.92 52.672-52.672 52.672-74.017 0-53.343-43.072-96.735-95.999-96.735-53.823 0-95.999 41.536-95.999 94.559 0 17.665-14.336 31.999-32.001 31.999s-32.001-14.336-32.001-31.999c0-87.424 71.775-158.56 160-158.56s160 72.095 160 160.74c0 47.904-36.32 84.192-71.424 119.3-27.84 27.776-56.576 56.512-56.576 82.336v54.112c0 17.665-14.336 32.032-32.001 32.032z" p-id="2033"/>
    </svg>
    `,i.onclick=this.onQuestionClick.bind(this);let s=document.createElement("div");s.className=Ja,s.innerText=`${ut()?"\u8BE6\u60C5 >":"Detail >"}`,s.onclick=this.onCollapseClick.bind(this);let o=e.content.firstChild,n=o.querySelector(`.${Oo}`);return n.appendChild(s),n.appendChild(i),n.appendChild(t),o}addDiaLog(){Xa()||(this._dialogNode=this.createDiaLog(),document.body.appendChild(this._dialogNode),this._dialogNode.onclick=this.onConfirm.bind(this),this._dialogNode.querySelector(`.${or}`).onclick=e=>e.stopPropagation(),this._bodyPosition=document.body.style.position,document.body.style.position="fixed",I.info("show autoplay dialog"),Y.uploadEvent({log:Np}))}deleteDiaLog(){this._dialogNode&&(document.body.removeChild(this._dialogNode),document.body.style.position=this._bodyPosition,this._dialogNode=null)}onConfirm(){I.warn("confirm clicked, try resume stream"),_.emit(m.AUTOPLAY_DIALOG_CLICK_CONFIRM),this.deleteDiaLog()}onCollapseClick(){let e=this._dialogNode.querySelector(`.${fu}`);e.style.visibility=`${this._showDetail?"hidden":"visible"}`,e.style.height=`${this._showDetail?0:"fit-content"}`,this._showDetail=!this._showDetail,this._isCollapseClicked||Y.uploadEvent({log:vp}),this._isCollapseClicked=!0}onQuestionClick(){window.open(Su,"_blank"),this._isQuestionClicked||Y.uploadEvent({log:Dp}),this._isQuestionClicked=!0}},Iu=Ya;var gi={};it(gi,{create:()=>_e,remove:()=>ie});var us=new WeakMap;function _e(r,e){us.has(r)||us.set(r,[]);let t=us.get(r),s={add:(o,n)=>("addEventListener"in e?(t.push(e.removeEventListener.bind(e,o,n)),e.addEventListener(o,n)):(t.push(e.off.bind(e,o,n)),e.on(o,n)),s)};return s}function ie(r){let e=us.get(r);e&&(e.forEach(t=>t()),us.delete(r))}var Ot=class extends k{constructor(t,i){super(t.id,`${i}-player`);this.kind=i;this.id=t.id,this._log=t.log,this.track=t.track,this.container=t.container,this.muted=t.muted,this._pausedRetryCount=va,this._state="STOPPED",this.bindTrackEvents()}id;element=null;container=null;mediaStream=new MediaStream;track;url;attr;muted;_log;_pausedRetryCount;_isElementPlayingFired=!1;_interval;get isPlaying(){return this._state==="PLAYING"}get isStopped(){return this._state==="STOPPED"}setAttr(t){this.attr=t}setTrack(t){if(t!==this.track&&(this.unbindTrackEvents(),this.track=t,this.emit(mt.MEDIA_TRACK_CHANGED,t),t!==null&&(this.bindTrackEvents(),this.element))){let i=new MediaStream;i.addTrack(t),this.element.srcObject=i}}setUrl(t){this.track&&(this.unbindTrackEvents(),this.element&&(this.element.srcObject=null),this.track=null),t!==this.url&&(this.url=t,t!==null&&this.element&&(this.element.crossOrigin="anonymous",this.element.src=t))}setContainer(t){this.container=t,this.track&&this.element&&this.container&&this.container.appendChild(this.element)}async play(){if(this.element&&this.element.parentElement!==this.container&&this.container&&this.container.append(this.element),!this.isPlaying)try{this.bindAutoPlayEvent(),await this.element.play()}catch(t){let i=v({key:b.PLAY_FAILED,data:{media:this.kind,error:t}});if(this.track&&!this.track.muted&&this._log.warn(t),i.includes("NotAllowedError"))throw new A({code:E.PLAY_NOT_ALLOWED,message:i})}}stop(){this.unbindEvents(),this._isElementPlayingFired=!1,this.element&&(this.container&&this.container.removeChild(this.element),this.element.srcObject=null,this.element=null),this.handleStopped(l.ENDED),this._interval>0&&J.clearTask(this._interval)}pause(){this.isPlaying&&this.element?.pause()}resume(){return this.isPlaying?Promise.resolve():Hr?this.replay():this.play().catch(()=>{})}setMuted(t){this.element&&(this.element.muted=t),this.muted=t}setRect(t,i){this.element&&(this.element.style.width=`${t}px`,this.element.style.height=`${i}px`)}replay(){return this.stop(),this.play().catch(()=>{})}bindElementEvents(){if(this.element){let t=this.handleElementEvent.bind(this);return _e(this.element,this.element).add(l.PLAYING,t).add(l.ENDED,t).add(l.PAUSE,t).add(l.ERROR,t).add(l.LOADEDDATA,t)}}bindTrackEvents(){if(this.track){let t=this.handleTrackEvent.bind(this);gi?.create(this.track,this.track).add(l.ENDED,t).add(l.MUTE,t).add(l.UNMUTE,t),this.track.readyState===l.ENDED&&this.handleTrackEvent({type:l.ENDED}),this.track.muted&&this.handleTrackEvent({type:l.MUTE})}}bindAutoPlayEvent(){_.on(m.AUTOPLAY_DIALOG_CLICK_CONFIRM,this.resume,this)}unbindTrackEvents(){this.track&&ie(this.track)}unbindEvents(){this.element&&ie(this.element),this.unbindTrackEvents(),_.off(m.AUTOPLAY_DIALOG_CLICK_CONFIRM,this.resume,this)}handleElementEvent(t){switch(t.type){case l.PLAYING:this._isElementPlayingFired=!0,this._log.info(`${this.kind} player is playing`),this.handlePlaying(l.PLAYING),this._interval&&(J.clearTask(this._interval),this._interval=-1);break;case l.ENDED:this._log.info(`${this.kind} player is ended`),this.handleStopped(l.ENDED);break;case l.PAUSE:this._log.info(`${this.kind} player is paused`),this.handlePaused(l.PAUSE);let s=this.container&&document.getElementById(this.container.id);s||this._log.warn(`${this.kind} player has been remove, element ID: ${this.container?.id}`);let o=at();this._pausedRetryCount>0&&(this.kind===l.VIDEO&&!Xa()||this.kind===l.AUDIO&&(K(o)&&o<=70||!s))&&(this._log.info(`${this.kind} player auto resume when paused`),this.resume(),this._pausedRetryCount--),We&&(this._interval=J.run(Zi,()=>{this.element&&this._state==="PAUSED"&&this.resume()},{delay:3e3}));break;case l.ERROR:if(this.element&&this.element.error){let{code:n,message:a}=this.element.error;this._log.error(`${this.kind} player error observed. code: ${n} message: ${a} userAgent: ${navigator.userAgent}`),Y.uploadEvent({log:`stat-${this.kind}-${ye.PLAYER_ERROR}-${n}-${navigator.userAgent}`,error:this.element.error})}break;case l.LOADEDDATA:this.kind===l.VIDEO&&this.emit(mt.LOADED_DATA);break}}handleTrackEvent(t){switch(t.type){case l.ENDED:this._log.info(`${this.kind} track is ended`),this.handleStopped(l.ENDED);break;case l.MUTE:this._log.info(`${this.kind} track is unable to provide media output`),this.handlePaused(l.MUTE);break;case l.UNMUTE:this._log.info(`${this.kind} track is able to provide media output`),this._isElementPlayingFired&&this.handlePlaying(l.UNMUTE);break}}handlePlaying(t){this.emit(mt.PLAYER_STATE_CHANGED,{type:this.kind,state:"PLAYING",reason:t})}handlePaused(t){this.emit(mt.PLAYER_STATE_CHANGED,{type:this.kind,state:"PAUSED",reason:t})}handleStopped(t){this.emit(mt.PLAYER_STATE_CHANGED,{type:this.kind,state:"STOPPED",reason:t})}getElement(){return this.element}};y([q([],"PLAYING")],Ot.prototype,"handlePlaying",1),y([q("PLAYING","PAUSED",{ignoreError:!0})],Ot.prototype,"handlePaused",1),y([q([],"STOPPED")],Ot.prototype,"handleStopped",1);var ls=class extends Ot{mirror;objectFit;constructor(e){super(e,l.VIDEO),f(e.mirror)||(this.mirror=e.mirror),f(e.objectFit)||(this.objectFit=e.objectFit)}initializeElement(){let e=document.createElement(l.VIDEO);if(this.track){let i=new MediaStream;i.addTrack(this.track),e.srcObject=i}e.muted=!0;let t=`width: 100%; height: 100%; object-fit: ${this.objectFit};background-color: black;`;this.mirror&&(t+="transform: scaleX(-1);"),e.setAttribute("id",`video_${this.id}`),e.setAttribute("style",t),e.setAttribute("autoplay","autoplay"),e.setAttribute("playsinline","playsinline"),this.container?.appendChild(e),this.element=e,this.bindElementEvents()}bindElementEvents(){let e=super.bindElementEvents();this.handleElementEvent=this.handleElementEvent.bind(this),e&&e.add(l.ENTER_PICTURE_IN_PICTURE,this.handleElementEvent).add(l.LEAVE_PICTURE_IN_PICTURE,this.handleElementEvent)}handleElementEvent(e){super.handleElementEvent(e);let t=e.type;if(this.mirror&&this.element){let i=this.element.style.transform;t===l.ENTER_PICTURE_IN_PICTURE?this.element.style.transform=i.replace("scaleX(-1)",""):t===l.LEAVE_PICTURE_IN_PICTURE&&!i.includes("scaleX")&&(this.element.style.transform=`${i} scaleX(-1)`)}}setAttr(e){let t=Object.assign({autoplay:"autoplay",playsinline:"playsinline",muted:!0},e);t.style=Object.assign({width:"100%",height:"100%"},t.style),super.setAttr(t)}setMirror(e){this.element&&(this.element.style.transform=e?"scaleX(-1)":""),this.mirror=e}setObjectFit(e){this.element&&(this.element.style.objectFit=`${e}`),this.objectFit=e}play(){return this.element||this.initializeElement(),super.play()}getVideoFrame(){if(!this.element)return"";let e=document.createElement("canvas");return e.width=this.element.videoWidth,e.height=this.element.videoHeight,e.getContext("2d").drawImage(this.element,0,0),e.toDataURL("image/png")}getElement(){return this.element}};var kp='class VolumeMeter extends AudioWorkletProcessor{constructor(){super(),this.volume=0,this.intervalTime=200,this.tick=this.intervalTime,this.isStop=!1,this.port.onmessage=t=>{let{data:e}=t;switch(e.name){case"setIntervalTime":this.intervalTime=e.intervalTime;break;case"stop":this.isStop=!0}}}process(t){let e=t[0];if(this.isStop)return!1;if(e.length>0){let i=e[0],s=0;for(let l=0;l<i.length;++l)s=Math.max(Math.abs(i[l]),s);this.volume=s,this.tick-=i.length,this.tick<0&&(this.tick+=this.intervalTime/1e3*sampleRate,this.port.postMessage({volume:this.volume}))}return!0}}registerProcessor("volume-meter",VolumeMeter);',Au=!1,qa=class{context_;blob_;constructor(e){this.context_=e.context,this.blob_=new Blob([kp],{type:"application/javascript"}),this.addModuleToContext()}async addModuleToContext(){try{await this.context_.audioWorklet.addModule(URL.createObjectURL(this.blob_)),I.info("worklet addModule success"),_.emit(m.WORKLET_LOADED_SUCCESS),Au=!0}catch(e){I.info(`worklet addModule catch error. ${e.message}`),_.emit(m.WORKLET_LOADED_FAILED)}}get initWorkletSuccess(){return Au}},Cu=qa;typeof window<"u"&&(window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext);var Pp=0,_t=(r,e)=>{let t=new window.AudioContext(e),i=++Pp,s=()=>{t.state==="suspended"?(t.resume(),document.addEventListener("click",s)):t.state==="interrupted"?t.resume():document.removeEventListener("click",s)};return document.addEventListener("click",s),t.onstatechange=()=>{I.info(`${r}-${i} context state: ${t.state}`),s()},t};var Si,Po,Mo=0,Qa=class{_volume;_log;_track;_stream;_audioCtx;_destination;_streamSource;_scriptProcessorNode;_audioWorkletNode;_interval;constructor(e){let{track:t,log:i}=e;this._volume=0,this._log=i,this._track=t,Si||(Si=_t("volume-meter")),this._audioCtx=Si,this._destination=this._audioCtx.destination;let s=new MediaStream;s.addTrack(this._track),this._streamSource=this._audioCtx.createMediaStreamSource(s),this._audioWorkletNode=null,this._scriptProcessorNode=null,this._interval=200,_.on(m.AUDIO_LEVEL_INTERVAL,this.handleAudioLevelInterval,this),ka?(_.on(m.WORKLET_LOADED_SUCCESS,this.initAudioWorklet,this),_.on(m.WORKLET_LOADED_FAILED,this.initScriptProcessor,this),this.preload()):this.initScriptProcessor(),Mo+=1}preload(){Po?Po.initWorkletSuccess&&this.initAudioWorklet():Po=new Cu({context:Si})}initAudioWorklet(){if(!this._audioWorkletNode)try{this._audioWorkletNode=new AudioWorkletNode(this._audioCtx,"volume-meter"),this._audioWorkletNode.port.onmessage=e=>{this._volume=e.data.volume||0},this._streamSource.connect(this._audioWorkletNode).connect(this._destination),this.handleAudioLevelInterval({interval:this._interval})}catch(e){Y.logFailedEvent({userId:this._log.userId,eventType:ye.LOAD_WORKLET,error:e}),this.initScriptProcessor()}}initScriptProcessor(){if(!this._scriptProcessorNode)try{this._scriptProcessorNode=this._audioCtx.createScriptProcessor(2048,1,1),this._scriptProcessorNode.onaudioprocess=e=>{let t=e.inputBuffer.getChannelData(0),i=0;for(let s=0;s<t.length;++s)i+=t[s]*t[s];this._volume=Math.sqrt(i/t.length)||0},this._streamSource.connect(this._scriptProcessorNode),this._scriptProcessorNode.connect(this._destination)}catch(e){this._log.error(`volumeMeter init script processor error: ${e}`)}}destroy(){this._streamSource&&this._streamSource.disconnect(),this._scriptProcessorNode&&(this._scriptProcessorNode.onaudioprocess=null,this._scriptProcessorNode.disconnect()),this._audioWorkletNode&&(this._audioWorkletNode.port.postMessage({name:"stop"}),this._audioWorkletNode.port.onmessage=null,this._audioWorkletNode.disconnect()),this._audioWorkletNode=null,this._scriptProcessorNode=null,this._audioCtx=null,_.off(m.AUDIO_LEVEL_INTERVAL,this.handleAudioLevelInterval,this),_.off(m.WORKLET_LOADED_SUCCESS,this.initAudioWorklet,this),_.off(m.WORKLET_LOADED_FAILED,this.initScriptProcessor,this),Mo>0&&(Mo-=1),Mo===0&&(Si?.close(),Si=null,Po=null)}resume(){Si?.resume()}setTrack(e){if(!(!this._audioCtx||!this._audioWorkletNode)){if(!e)return this._streamSource.disconnect();this._track=e,this._streamSource.disconnect(),this._streamSource=this._audioCtx.createMediaStreamSource(new MediaStream([e])),this._streamSource.connect(this._audioWorkletNode)}}getInternalAudioLevel(){return this._volume}getCalculatedVolume(){return parseFloat(this._volume.toFixed(2))}handleAudioLevelInterval(e){let{interval:t}=e;this._interval=t,this._audioWorkletNode?.port.postMessage({name:"setIntervalTime",intervalTime:t})}},yu=Qa;var qt=null,Za=0,Lo=class extends Ot{_volumeMeter;_gainedTrack=null;_gainNode=null;_destination=null;_mediaStreamSource=null;_sourceElement=null;_outputDeviceId;_volume=1;_loop=!1;constructor(e){super(e,l.AUDIO),this._outputDeviceId=e.outputDeviceId,this.track&&this.initVolumeMeter(this.track)}setTrack(e){this.track!==e&&(this._volumeMeter?this._volumeMeter.setTrack(e):e&&this.initVolumeMeter(e)),super.setTrack(e)}initVolumeMeter(e){this._volumeMeter=new yu({track:this._gainedTrack||e,log:this._log})}initializeElement(){if((Ft==="15.2"||Ft==="15.3"||Ft==="15.4")&&this.muted){this._log.info("audioElement is muted.");return}let t=document.createElement(l.AUDIO);this.track&&(t.srcObject=new MediaStream([this._gainedTrack||this.track])),t.muted=this.muted,t.setAttribute("id",`audio_${this.id}`),t.setAttribute("autoplay","autoplay"),t.setAttribute("playsinline","playsinline"),this.element=t,this.bindElementEvents()}async play(){this.element||this.initializeElement(),this._outputDeviceId&&await this.setSinkId(this._outputDeviceId),!this._volumeMeter&&this.track&&this.initVolumeMeter(this.track),this.setVolume(this._volume),await super.play()}stop(){this._volumeMeter&&(this._volumeMeter.destroy(),this._volumeMeter=null),this.destroyGain(),super.stop()}async resume(){await super.resume(),this._volumeMeter&&this._volumeMeter.resume()}async setSinkId(e){this._outputDeviceId!==e&&(this.element&&await this.element.setSinkId(e),this._outputDeviceId=e)}setVolume(e){this._volume=e,this._gainNode?this._gainNode.gain.value=e:e>1?this.createGain(e):this.element&&(this.element.volume=e)}createGain(e){qt||(qt=_t("player")),this._log.info(`create gainNode ${e}`);let t=qt.createMediaStreamSource(new MediaStream([this.track])),i=qt.createGain(),s=qt.createMediaStreamDestination();t.connect(i),i.connect(s),i.gain.value=e,this._gainNode=i,this._destination=s,this._mediaStreamSource=t,this._gainedTrack=s.stream.getAudioTracks()[0],Za+=1,this.element&&(ie(this.element),this._sourceElement=this.element,this.element.muted=!0,this.element=null),this.play()}destroyGain(){!this._gainNode||(this._log.info("destroy gainNode"),this._gainNode?.disconnect(),this._destination?.disconnect(),this._mediaStreamSource?.disconnect(),this._gainNode=null,this._destination=null,this._mediaStreamSource=null,this._sourceElement&&(this._sourceElement.srcObject=null,this._sourceElement=null),this._gainedTrack=null,Za-=1,Za===0&&qt&&(this._log.info("destroy gain audioContext"),qt.close(),qt=null))}setLoop(e){!this.element||(this.element.loop=e,this._loop=e)}getAudioLevel(){return this._volumeMeter?.getCalculatedVolume()||0}getInternalAudioLevel(){return this._volumeMeter?.getInternalAudioLevel()}};var Ii=class extends k{id=Va();userId="";isRemote;mediaType;room;user;_log;_inputTrack;_outputTrack;isPlayCalled;container=null;player;subVideoPlayerMap;playerMuted=!1;abortCtrl;audioOutputDeviceId;playbackVolume=1;objectFit="cover";mirror=!1;isScreen=!1;manager;constructor({userId:e,sdkAppId:t,mediaType:i,room:s,isInitPlayer:o=!0}){super(),f(e)||(this.userId=e),this.mediaType=i,this._log=I.createLogger({id:`${this.kind[0]}t`,userId:(s||this.room)?.userId,remoteUserId:this instanceof Ie?void 0:this.userId,sdkAppId:t,type:this.mediaType===2?"auxiliary":"main",isLocal:this instanceof Ie}),o&&this.initPlayer()}get log(){return this._log||I}get kind(){return this.mediaType===1?l.AUDIO:l.VIDEO}get muted(){return!!(this.outMediaTrack&&!this.outMediaTrack.enabled)}get strMediaType(){return this.mediaType===4?l.VIDEO:this.mediaType===2?l.SCREEN:l.AUDIO}get streamType(){return(this.mediaType&2)===0?"main":"auxiliary"}async play(e,t){let i=oe(e)?e[0]:e;if(this.isPlayCalled){this.log.info(`play update options: ${JSON.stringify(t)}`),t&&!f(t.muted)&&this.setPlayerMute(t.muted),t&&!f(t.objectFit)&&(this.objectFit=t.objectFit),this.isScreen?this.mirror=!1:t&&!f(t.mirror)&&(this.mirror=t.mirror),this.kind===l.VIDEO&&(this.player.setObjectFit(this.objectFit),this.player.setMirror(this.mirror),this.container!==i&&i&&(this.container=i,this.player.setContainer(i)),oe(e)&&e.length>=1&&await this.playSubContainer(e.slice(1),t));return}if(t&&!f(t.muted)?this.setPlayerMute(t.muted):(!this.isRemote||this.kind===l.VIDEO)&&this.setPlayerMute(!0),t&&!f(t.objectFit)&&(this.objectFit=t.objectFit),this.isRemote||(this.mirror=!0),this.isScreen?this.mirror=!1:t&&!f(t.mirror)&&(this.mirror=t.mirror),this.kind===l.VIDEO&&(this.player.setObjectFit(this.objectFit),this.player.setMirror(this.mirror)),this.isPlayCalled=!0,i&&(this.container=i,this.player.setContainer(i)),_.emit(m.PLAY_TRACK_START,{track:this}),!this._outputTrack){this.log.info("play has not mediaTrack, abort");return}this._log.info(`play with options: ${JSON.stringify(t)}`);try{this.player.setTrack(this._outputTrack),await this.player.play(),oe(e)&&e.length>1&&await this.playSubContainer(e.slice(1),t)}catch(s){throw this.handleAutoPlayFailed(),this.emit("error",s),s}}async playSubContainer(e,t){if(!this._outputTrack||this.kind===l.AUDIO)return;this.subVideoPlayerMap||(this.subVideoPlayerMap=new Map),this.subVideoPlayerMap.forEach((s,o)=>{e.find(n=>o===n)||(s.stop(),s.setContainer(null),this.subVideoPlayerMap?.delete(o))});for(let[s,o]of e.entries()){let n=this.subVideoPlayerMap.get(o);n?t&&(f(t.mirror)||n.setMirror(t.mirror),f(t.objectFit)||n.setObjectFit(t.objectFit)):this.subVideoPlayerMap.set(o,new ls({id:this.userId||this.id,track:this.outMediaTrack,container:o,muted:this.playerMuted,objectFit:this.objectFit,mirror:this.mirror,log:this.log.createChild({id:`vp-sub${s+1}`})}))}let i=[...this.subVideoPlayerMap.values()];for(let s of i)await s.play()}initPlayer(){this.log.info("create player"),this.kind===l.AUDIO?this.player=new Lo({id:this.userId||this.id,track:this.outMediaTrack,container:this.container||null,muted:this.playerMuted,outputDeviceId:this.audioOutputDeviceId,log:this.log}):(this.player=new ls({id:this.userId||this.id,track:this.outMediaTrack,container:this.container||null,muted:this.playerMuted,objectFit:this.objectFit,mirror:this.mirror,log:this.log}),this.player.on(mt.LOADED_DATA,()=>{_.emit(m.VIDEO_LOADED_DATA,{track:this})}),this.player.on(mt.MEDIA_TRACK_CHANGED,e=>{this.subVideoPlayerMap?.forEach(t=>t.setTrack(e))})),this.player.on(mt.PLAYER_STATE_CHANGED,e=>{_.emit(m.PLAYER_STATE_CHANGED,{track:this,...e}),this.emit("player-state-changed",e)})}async setAudioOutput(e){this.audioOutputDeviceId=e,await this.player?.setSinkId(e)}setAudioVolume(e){this.playbackVolume=e,this.log.info(`setAudioVolume to ${e}`),this.player?.setVolume(e)}getAudioLevel(){return this.player?.getAudioLevel()||0}getInternalAudioLevel(){return this.player?.getInternalAudioLevel()||0}stop(){!this.isPlayCalled||(this.isPlayCalled=!1,this.player&&(this.log.info(`stop ${this.kind} player`),this.player.stop(),this.player.setContainer(null)),this.subVideoPlayerMap&&this.subVideoPlayerMap.size>0&&this.subVideoPlayerMap.forEach(e=>{e.stop(),e.setContainer(null)}),this.container=null)}async resume(){!this.isPlayCalled||await this.player?.resume()}close(){this.log.info("close"),this.isPlayCalled&&this.stop()}setMute(e){return this.outMediaTrack?(this.outMediaTrack.enabled=!e,this.emit(e?"mute":"unmute",this),_.emit(e?m.TRACK_MUTED:m.TRACK_UNMUTED,{track:this}),!0):!1}setPlayerMute(e){this.playerMuted=e,this.player.setMuted(e)}get mediaTrack(){return this._inputTrack||null}get outMediaTrack(){return this._outputTrack||null}setInputMediaStreamTrack(e){let t=this._inputTrack;if(e!==t)return this._inputTrack=e,this.emit("input-media-track-changed",e||null,t||null),this.manager?this.manager.changeInput(this,t):this.setOutputMediaStreamTrack(e)}setOutputMediaStreamTrack(e){let t=this._outputTrack;e!==t&&(this.isRemote?this.log.debug("setOutputMediaStreamTrack",e.label):this.log.info("setOutputMediaStreamTrack",e.getSettings?.().deviceId,e.label),this._outputTrack=e,e.enabled=!this.muted,this.updatePlayingState(!!e),this.emit("output-media-track-changed",e))}setMediaType(e){this.mediaType=e}updatePlayingState(e){if(this.isPlayCalled){if(e){if(this.player.setTrack(this.outMediaTrack),this.player.isStopped){this.player.play().catch(()=>this.handleAutoPlayFailed()),this.log.info(`playing state updated, play ${this.kind}`);return}}else if(!this.player.isStopped){this.player.stop(),this.log.info(`playing state updated, stop ${this.kind}`);return}}this.log.debug(`updatePlayingState abort ${this.isPlayCalled} ${e} ${this.player.isStopped}`)}handleAutoPlayFailed(){if(this.room&&this.room.enableAutoPlayDialog)new Iu;else{let e=()=>{this.resume().then(()=>{document.removeEventListener("click",e,!0),document.removeEventListener("touchstart",e,!0)})};document.addEventListener("click",e,!0),document.addEventListener("touchstart",e,!0)}}};y([q([],k.INIT)],Ii.prototype,"close",1);var Mp=Object.prototype.hasOwnProperty,{toString:iE}=Object.prototype;function Lp(r){if(r==null)return!0;if(typeof r=="boolean")return!1;if(typeof r=="number")return r===0;if(typeof r=="string"||typeof r=="function"||Array.isArray(r))return r.length===0;if(r instanceof Error)return r.message==="";if(Ue(r))switch(Object.prototype.toString.call(r)){case"[object File]":case"[object Map]":case"[object Set]":return r.size===0;case"[object Object]":{for(let e in r)if(Mp.call(r,e))return!1;return!0}}return!1}var kt=Lp;var Nu=pe(ve(),1);var bu=r=>e=>e.deviceId===r;var ps=class{kind;type;devices=[];constructor(e,t="Input"){this.kind=e,this.type=t}update(e,t){let i=e.filter(s=>s.kind===`${this.kind}${this.type.toLocaleLowerCase()}`);t&&(i.forEach(s=>{if(s.deviceId&&!this.devices.find(bu(s.deviceId))){let o=`${this.kind}${this.type}Added`;I.warn(`${o}: ${JSON.stringify(s)}`),t.emit(o,s)}}),this.devices.forEach(s=>{if(s.deviceId&&!i.find(bu(s.deviceId))){let o=`${this.kind}${this.type}Removed`;I.warn(`${o}: ${JSON.stringify(s)}`),t.emit(o,s)}})),this.devices=i}hasDevice(e){return!!this.devices.find(t=>t.deviceId===e)}},ec=class extends Nu.EventEmitter{audioInputs=new ps(l.AUDIO);videoInputs=new ps(l.VIDEO);audioOutputs=new ps(l.AUDIO,"Output");constructor(){super(),this.init(),navigator.mediaDevices&&(navigator.mediaDevices.addEventListener&&navigator.mediaDevices.addEventListener("devicechange",this.update.bind(this)),"ondevicechange"in navigator.mediaDevices||J.run(_i,()=>{this.update()},{delay:1e4}))}init(){xo().then(e=>{this.audioInputs.update(e),this.videoInputs.update(e),this.audioOutputs.update(e)})}async update(){let e=await xo();return this.audioInputs.update(e,this),this.videoInputs.update(e,this),this.audioOutputs.update(e,this),this}},re=Wr||Ji?null:new ec;async function xo(){if(Yt()||!vo())return[];let r=await navigator.mediaDevices.enumerateDevices(),e={audio:!1,video:!1};if(r.forEach(({deviceId:t,kind:i,label:s})=>{t===s&&s===""&&(i==="audioinput"?e.audio=!0:i==="videoinput"&&(e.video=!0))}),e.audio||e.video){let t=await navigator.mediaDevices.getUserMedia(e);r=await navigator.mediaDevices.enumerateDevices(),t.getTracks().forEach(i=>i.stop())}return r.map((t,i)=>{let s={kind:t.kind,deviceId:t.deviceId,groupId:t.groupId,label:t.label||`${t.kind}_${i}`};return t.deviceId.length>0&&tc.add(`${t.deviceId}_${t.kind}`),t.getCapabilities&&(s.getCapabilities=()=>t.getCapabilities()),s})}function Oe(){return re.update().then(r=>r.audioInputs.devices)}function Fe(){return re.update().then(r=>r.videoInputs.devices)}async function nr(){return re.update().then(r=>r.audioOutputs.devices)}var tc=new Set;function vu(r){if(r instanceof CanvasCaptureMediaStreamTrack||!(r instanceof MediaStreamTrack))return!1;let e=r.label.toLocaleLowerCase();if(e.includes("camera")||e.includes("webcam"))return!0;let i=`${(r?.getSettings()||{}).deviceId}_${l.VIDEO_INPUT}`;return!!tc.has(i)}function Du(r){if(r instanceof CanvasCaptureMediaStreamTrack||!(r instanceof MediaStreamTrack))return!1;let e=r.label.toLocaleLowerCase();if(e.includes("mic")||e.includes("\u9EA6\u514B\u98CE"))return!0;let i=`${(r?.getSettings()||{}).deviceId}_${l.AUDIO_INPUT}`;return!!tc.has(i)}async function ic(r,e){let i=(await Oe()).find(s=>s.deviceId===mi);return i?.groupId===r&&i.label===e}async function Ou({newDeviceId:r,oldDeviceId:e,oldGroupId:t,oldLabel:i,kind:s}){return r!==e?!1:s===l.AUDIO&&r===mi?await ic(t,i):!0}var xp=async function(r){let e=Up(r);I.info(`getUserMedia with constraints: ${JSON.stringify(e)}`);let t=[],i=[],s=["label","deviceId"];e.audio&&(t=await Oe(),I.info(`microphones: ${qe(t,{keysToInclude:s})}`)),e.video&&(i=await Fe(),I.info(`cameras: ${qe(i,{keysToInclude:s})}`));try{let o=await navigator.mediaDevices.getUserMedia(e);return $a&&o.getTracks().forEach(n=>{I.info(`${n.kind} capabilities: ${qe(n.getCapabilities(),{keysToInclude:Io})}`)}),o}catch(o){if(o.name==="NotFoundError"){if(r.video&&i&&i.length===0)throw new A({code:E.DEVICE_NOT_FOUND,message:v({key:b.CAMERA_NOT_FOUND})});if(r.audio&&t&&t.length===0)throw new A({code:E.DEVICE_NOT_FOUND,message:v({key:b.MICROPHONE_NOT_FOUND})})}throw new A({code:E.INITIALIZE_FAILED,name:o.name,message:o.message,constraint:o.constraint})}},Vp=ht({retryFunction:xp,settings:{retries:3,timeout:500},onError:({error:r,retry:e,reject:t,retryFuncArgs:i,retryCount:s})=>{r.name==="NotReadableError"?(i[0].video&&(i[0].maxResolution=!1,i[0].frameRate&&(i[0].frameRate=i[0].frameRate>10?10:5)),s===2&&(i[0].useTrueAsConstraint=!0),e()):t(r),i[0].microphoneId&&ku(i[0].microphoneId,!1),i[0].cameraId&&ku(i[0].cameraId,!0)},onRetrying:r=>{I.warn(`getUserMedia NotReadableError observed, retrying [${r}/3]`)},onRetryFailed:r=>{Y.logFailedEvent({eventType:ye.GET_USER_MEDIA_RETRY,error:r})},onRetrySuccess:r=>{Y.logSuccessEvent({eventType:ye.GET_USER_MEDIA_RETRY}),Y.uploadEvent({log:`stat-${ye.GET_USER_MEDIA_RETRY}-success-${r}`})}});async function ku(r,e){let i=(e?await Fe():await Oe()).find(s=>s.deviceId===r);i&&W(i.getCapabilities)&&I.warn(qe(i.getCapabilities(),{keysToInclude:Io}))}function Up(r){return{audio:wp(r),video:Bp(r)}}function wp(r){if(!r.audio)return!1;if(r.useTrueAsConstraint)return!0;let e={echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0};return kt(r.microphoneId)||(e.deviceId=r.useExact?{exact:r.microphoneId}:r.microphoneId),K(r.channelCount)&&r.channelCount>1&&(e.channelCount=r.channelCount),Z(r.echoCancellation)&&!r.echoCancellation&&(e.echoCancellation=!1),Z(r.noiseSuppression)&&!r.noiseSuppression&&(e.noiseSuppression=!1),Z(r.autoGainControl)&&!r.autoGainControl&&(e.autoGainControl=!1),kt(e)?!0:e}function Bp(r){if(!r.video)return!1;if(r.useTrueAsConstraint)return!0;let{maxResolution:e=!0}=r,t={};return r.cameraId?t.deviceId=r.useExact?{exact:r.cameraId}:r.cameraId:r.facingMode&&(t.facingMode=r.facingMode),r.width&&(t.width={ideal:r.width},e&&!z&&(t.width.max=r.width)),r.height&&(t.height={ideal:r.height},e&&!z&&(t.height.max=r.height)),z&&je&&r.width&&r.height&&r.width*r.height<352*288&&(t.width=r.width,t.height=r.height),r.frameRate&&(t.frameRate=r.frameRate),kt(t)?!0:t}var Pu=Vp;function Vo(r){return F((e,t)=>async function(...i){return await r.apply(this,i),e.apply(this,i)})}function Uo(r){return F((e,t)=>async function(...i){return r.call(this,e.apply(this,i))})}function F(r){return function(e,t,i){return i.value=r(i.value,t),i}}var Qt=new WeakMap,Tt=new WeakMap;function Ri(){return function(r,e,t){let i=t.value,s=({fn:o,args:n,context:a,resolve:c,reject:d})=>{o.apply(a,n).then(c,d)};return t.value=function(...o){return new Promise((a,c)=>{if(Qt.has(this)){let d=Qt.get(this),{length:u}=d;d.push({fn:i,args:o,context:this,resolve:a,reject:c}),u===0&&s({fn:i,args:o,context:this,resolve:a,reject:c})}else Qt.set(this,[{fn:i,args:o,context:this,resolve:a,reject:c}]),s({fn:i,args:o,context:this,resolve:a,reject:c})}).finally(()=>{let a=Qt.get(this);a&&(a.shift(),a[0]&&s({...a[0]}))})},t}}function Mu(r){return function(e,t,i){let s=i.value;return i.value=function(...o){let n=Qt.get(this);if(n){let a=n.filter((c,d)=>{if(d===0)return!0;let u=!0;return c.args.forEach(h=>{o.find(p=>p===h)||(u=!1)}),u?(c.reject(new A({code:E.API_CALL_ABORTED,message:r})),!1):!0});Qt.set(this,a)}return s.apply(this,o)},i}}function Lu(r){return function(e,t,i){let s=i.value;return i.value=function(...o){let n=[];return Qt.get(this)?.forEach(a=>n.push(a)),Tt.get(this)?.forEach(a=>a.forEach(c=>n.push(c))),n.forEach(a=>{a.reject(new A({code:E.API_CALL_ABORTED,message:r}))}),Qt.delete(this),Tt.delete(this),s.apply(this,o)},i}}function rc(r){return function(e,t,i){let s=i.value,o=a=>K(r)?a[r]:r(...a),n=({fn:a,args:c,context:d,resolve:u,reject:h})=>{a.apply(d,c).then(u,h).finally(()=>{if(Tt.has(d)&&Tt.get(d).has(o(c))){let p=Tt.get(d).get(o(c));p&&(p.shift(),p[0]&&n({...p[0]}))}})};return i.value=function(...a){return new Promise((c,d)=>{if(Tt.has(this))if(Tt.get(this).has(o(a))){let u=Tt.get(this).get(o(a));if(u){let{length:h}=u;u.push({fn:s,args:a,context:this,resolve:c,reject:d}),h===0&&n({fn:s,args:a,context:this,resolve:c,reject:d})}}else Tt.get(this).set(o(a),[{fn:s,args:a,context:this,resolve:c,reject:d}]),n({fn:s,args:a,context:this,resolve:c,reject:d});else{let u=new Map;u.set(o(a),[{fn:s,args:a,context:this,resolve:c,reject:d}]),Tt.set(this,u),n({fn:s,args:a,context:this,resolve:c,reject:d})}})},i}}var Ie=class extends Ii{isRemote=!1;deviceId;groupId="";label="";_isRecapturing=!1;_lastRecaptureTime=0;_onMuteTimeoutId=-1;profile;constructor(e,t=!0){super({mediaType:e,isInitPlayer:t}),this.onTrackMuted=this.onTrackMuted.bind(this),this.onTrackUnmuted=this.onTrackUnmuted.bind(this),this.onTrackEnded=this.onTrackEnded.bind(this)}installTrackEvent(e){_e(e,e).add(l.MUTE,this.onTrackMuted).add(l.UNMUTE,this.onTrackUnmuted).add(l.ENDED,this.onTrackEnded),e.muted&&this.onTrackMuted(),e.readyState===l.ENDED&&this.onTrackEnded()}uninstallTrackEvent(e){ie(e)}setStateToCapture(){}async capture(e){try{_.emit(m.LOCAL_TRACK_CAPTURE_START,{track:this});let t;e.customSource?(t=new MediaStream,t.addTrack(e.customSource)):(this.mediaTrack?.stop(),t=await Pu(e));let i=t.getTracks()[0];return await this.setInputMediaStreamTrack(i),e.customSource||(this.updateDeviceIdInUse(),this.listenDeviceChange()),_.emit(m.LOCAL_TRACK_CAPTURE_SUCCESS,{track:this}),t}catch(t){throw _.emit(m.LOCAL_TRACK_CAPTURE_FAILED,{track:this,error:t}),this.log.error(`getUserMedia error observed ${t}`),t}}setInputMediaStreamTrack(e){this.state===k.INIT&&this.setStateToCapture(),this._inputTrack&&this.uninstallTrackEvent(this._inputTrack);let t=super.setInputMediaStreamTrack(e);return this.installTrackEvent(e),t}publish(e,t){return this.room=e,this.emit("4",{mediaType:this.strMediaType,state:"starting",prevState:"stopped"}),this.userId=e.userId,this._log.setUserId(e.userId),this._log.setSdkAppId(e.sdkAppId),_.emit(m.LOCAL_TRACK_PUBLISHED,{track:this}),t}unpublish(){this.room&&this.room.localTracks.delete(this),_.emit(m.LOCAL_TRACK_UNPUBLISHED,{track:this})}async updateDeviceIdInUse(){if(this.mediaTrack&&$e){let{deviceId:e,groupId:t}=this.mediaTrack.getSettings(),{label:i}=this.mediaTrack;await Ou({newDeviceId:e,oldDeviceId:this.deviceId,oldGroupId:this.groupId,oldLabel:this.label,kind:this.kind})||(this.deviceId=e,this.label=i,t&&(this.groupId=t),xo().then(o=>{let n=o.find(a=>a.deviceId===e);n&&this.emit("2",n)}))}}setProfile(e){this.log.info("setProfile",e),Object.assign(this.profile,e)}isNeedToRecapture(e=!1){return!(!this.deviceId||!this.mediaTrack||this.kind===l.AUDIO&&!Du(this.mediaTrack)||this.kind===l.VIDEO&&!vu(this.mediaTrack)||this._isRecapturing||e&&je&&xe)}onTrackMuted(){if(La(),!!this.isNeedToRecapture(!0)){if(Date.now()-this._lastRecaptureTime<Qi){setTimeout(()=>this.onTrackMuted(),Qi);return}this._onMuteTimeoutId=setTimeout(async()=>{this.mediaTrack?.muted&&document.visibilityState==="visible"&&this.recapture(await this.getRecoverCaptureDeviceId())},5e3)}}onTrackUnmuted(){this._onMuteTimeoutId>0&&clearTimeout(this._onMuteTimeoutId)}async onTrackEnded(){if(!!this.isNeedToRecapture()){if(Date.now()-this._lastRecaptureTime<Qi){setTimeout(()=>this.onTrackEnded(),Qi);return}this.recapture(await this.getRecoverCaptureDeviceId())}}async recapture(e){if(this._isRecapturing||!this._inputTrack)return;this.log.warn("recapture trying"),this._inputTrack?.stop(),this._isRecapturing=!0,this._lastRecaptureTime=Date.now();let t={useExact:!0};if(e==="user"||e==="environment")t.facingMode=e;else{let i;(this.kind==="audio"?await Oe():await Fe()).find(o=>o.deviceId===e)&&(i=e),t.deviceId=i}return this.capture(t).then(()=>{this._isRecapturing=!1,this.log.warn("recapture success"),this.emit("1",{deviceId:this.deviceId})}).catch(i=>{this._isRecapturing=!1,this.log.warn(`recapture failed ${i.message}`),this.emit("5",i)})}async getRecoverCaptureDeviceId(){let e=this instanceof fe;if(e&&this.facingMode)return this.facingMode;let{deviceId:t}=this;if(t){let i=(ms.get(t)||0)+1;if(ms.set(t,i),i>=3){let s=e?(await Fe()).find(o=>!ms.has(o.deviceId)):(await Oe()).find(o=>!ms.has(o.deviceId));s&&(this.log.warn(`${t} capture fail ${i} times, change new ${s.deviceId}`),t=s.deviceId)}}return t}close(){super.close(),this._inputTrack&&(this._inputTrack.stop(),this.uninstallTrackEvent(this._inputTrack)),this.manager?.removeInput(this)}};y([q(k.INIT,"capture",{ignoreError:!0})],Ie.prototype,"setStateToCapture",1),y([Ri()],Ie.prototype,"capture",1),y([q("capture","publish",{ignoreError:!0,success(){this.room.localTracks.add(this),this.emit("4",{mediaType:this.strMediaType,state:"started",prevState:"starting"})},fail(r){let e="error",t=r.cause;t.message.includes("timeout")?e="timeout":t.code===E.API_CALL_ABORTED&&(e="api-call"),this.emit("4",{mediaType:this.strMediaType,state:"stopped",prevState:"starting",reason:e,error:t})}})],Ie.prototype,"publish",1),y([F(r=>async function(){let e=this.state==="publish"?"started":"starting";r.call(this),this.emit("4",{mediaType:this.strMediaType,state:"stopped",prevState:e,reason:"api-call"}),this.mediaType===2&&(this.mediaType=4)}),q([],"capture")],Ie.prototype,"unpublish",1);var ms=new Map;_.on(m.SWITCH_DEVICE_SUCCESS,r=>{r.track.deviceId&&ms.delete(r.track.deviceId)});var Ne=class extends Ie{mediaType=1;volume=0;captureVolume=1;profile={echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0,sampleRate:48e3,channelCount:1,bitrate:40};playerMuted=!0;constructor(e){super(1),this.manager=e}getAudioLevel(){let e=(this.volume||super.getAudioLevel())*this.captureVolume;return e>1?1:e}capture({deviceId:e,customSource:t,useExact:i=!1}){return super.capture({video:!1,audio:!0,microphoneId:e,echoCancellation:this.profile.echoCancellation,autoGainControl:this.profile.autoGainControl,noiseSuppression:this.profile.noiseSuppression,sampleRate:this.profile.sampleRate,channelCount:this.profile.channelCount,useExact:i,customSource:t})}async switchDevice(e){if(!!this.mediaTrack){if(this.deviceId===e)if(e===mi){if(await ic(this.groupId,this.label))return}else return;try{this.log.info(`switchDevice audio to: ${e}`),this.mediaTrack&&this.mediaTrack.stop(),await this.capture({deviceId:e,useExact:!0}),this.room&&await this.room.replaceTrack(this),_.emit(m.SWITCH_DEVICE_SUCCESS,{track:this}),this.log.info("switch microphone success")}catch(t){throw this.log.error(`switch microphone failed ${t}`),this.deviceId&&this.recapture(this.deviceId),t}}}listenDeviceChange(){re&&!re.listeners("audioInputRemoved").includes(this.handleMicrophoneRemoved)&&re.on("audioInputRemoved",this.handleMicrophoneRemoved,this)}async handleMicrophoneRemoved(e){if(e.deviceId===this.deviceId){this.log.warn(`current microphone is lost: ${JSON.stringify(e)}`);let t=await Oe();t[0]?this.recapture(t[0].deviceId):re.on("audioInputAdded",this.handleMicrophoneAdded,this)}}handleMicrophoneAdded(e){this.log.warn(`microphone added: ${JSON.stringify(e)}`),this.recapture(e.deviceId)}async update3A({echoCancellation:e,noiseSuppression:t,autoGainControl:i}){let s=!1;!f(e)&&e!==this.profile.echoCancellation&&(this.profile.echoCancellation=e,s=!0),!f(t)&&t!==this.profile.noiseSuppression&&(this.profile.noiseSuppression=t,s=!0),!f(i)&&i!==this.profile.autoGainControl&&(this.profile.autoGainControl=i,s=!0),s&&this.deviceId&&await this.recapture(this.deviceId)}setCaptureVolume(e){let t=e/100;t!==this.captureVolume&&(this.manager?.setCaptureVolume(t),this.captureVolume=t)}close(){re.off("audioInputAdded",this.handleMicrophoneAdded,this),re.off("audioInputRemoved",this.handleMicrophoneRemoved,this),super.close()}};var fe=class extends Ie{mediaType=4;profile={width:640,height:480,frameRate:15,bitrate:500};states={bytesSent:0,packetsSent:0,framesEncoded:0,framesSent:0,frameWidth:0,frameHeight:0};small;isNeedToSetBandwidth;get facingMode(){if(!(!$e||!this.mediaTrack))return this.mediaTrack.getSettings().facingMode}constructor(e){super(4),this.manager=e;let t=()=>{this.isAllowed2k4k(this.profile)?this.room&&this.settings.height>=1440&&this.state==="publish"&&this.room.sendAbilityStatus({"2k4k":1}):(this.log.warn("Resolution is reset to 1080p, need to upgrade ability here https://cloud.tencent.com/document/product/647/85386"),this.setProfile({...this.profile,width:1920,height:1080}),this.applyProfile())};this.on("input-media-track-changed",t),this.on("publish",t)}capture({deviceId:e,facingMode:t,useExact:i=!1,customSource:s}){return super.capture({audio:!1,video:!0,facingMode:t||this.facingMode,cameraId:e,width:this.profile.width,height:this.profile.height,frameRate:this.profile.frameRate,useExact:i,customSource:s})}setOutputMediaStreamTrack(e){return super.setOutputMediaStreamTrack(e),this.room?.replaceTrack(this)}setProfile(e){!e||(e.width*e.height<=160*120&&ge&&ai&&(this.log.warn(`resolution is ${e.width}*${e.height}, fallback to 240*180`),e.width>e.height?(e.width=240,e.height=180):(e.width=180,e.height=240),e.bitrate=Math.max(e.bitrate,150)),e.bitrate&&(this.isNeedToSetBandwidth=e.bitrate!==this.profile.bitrate),this.isAllowed2k4k(this.profile)?super.setProfile(e):(this.log.warn("Resolution is reset to 1080p, need to upgrade ability here https://cloud.tencent.com/document/product/647/85386"),super.setProfile({...this.profile,width:1920,height:1080})))}applyProfile(){if(!this.mediaTrack)return;let e=this.settings;if(e.height!==this.profile.height||e.width!==this.profile.width||e.frameRate!==this.profile.frameRate)return this.mediaTrack?.applyConstraints({width:this.profile.width,height:this.profile.height,frameRate:this.profile.frameRate}).then(()=>{if(this.manager&&this.manager.changeInput(this),this.room&&this.settings.height>=1440&&this.state==="publish"&&this.room.sendAbilityStatus({"2k4k":1}),this.isNeedToSetBandwidth&&this.room&&this.room.setBandWidth)return this.isNeedToSetBandwidth=!1,this.room.setBandWidth({bandwidth:this.profile.bitrate,type:l.VIDEO,videoType:l.BIG})})}get settings(){let e={width:this.profile.width,height:this.profile.height,frameRate:this.profile.frameRate};return $e&&this.mediaTrack&&Object.assign(e,this.mediaTrack.getSettings()),e}isAllowed2k4k(e){return!this.room||!this.room.scheduleResult||this.isScreen||e.height*e.width<2560*1440?!0:this.room.scheduleResult.trtcAutoConf?.["2k4k"]===1}isNeedToSwitchDevice(e){return!(!this.mediaTrack||this.deviceId===e||this.facingMode===e)}async switchDevice(e){try{if(!this.isNeedToSwitchDevice(e))return;let t={useExact:!0};e==="user"||e==="environment"?t.facingMode=e:t.deviceId=e,this.mediaTrack.stop(),await this.capture(t),_.emit(m.SWITCH_DEVICE_SUCCESS,{track:this}),this.log.info("switch camera success")}catch(t){throw this.log.error(`switch camera failed ${t}`),this.deviceId&&this.recapture(this.deviceId),t}}listenDeviceChange(){re&&!re.listeners("videoInputRemoved").includes(this.handleCameraRemoved)&&re.on("videoInputRemoved",this.handleCameraRemoved,this)}async handleCameraRemoved(e){if(e.deviceId===this.deviceId){this.log.warn(`current camera is lost: ${JSON.stringify(e)}`);let t=await Fe();t[0]?this.recapture(t[0].deviceId):re.on("videoInputAdded",this.handleCameraAdded,this)}}async handleCameraAdded(e){this.log.warn(`camera added: ${JSON.stringify(e)}`),this.recapture(e.deviceId)}close(){re.off("videoInputAdded",this.handleCameraAdded,this),re.off("videoInputRemoved",this.handleCameraRemoved,this),super.close()}};var Gp=async function(r){let e=null,t=jp(r);I.info(`getDisplayMedia with constraints: ${JSON.stringify(t)}`);let i=await navigator.mediaDevices.getDisplayMedia(t);if(r.systemAudio&&i.getAudioTracks().length===0&&(ji&&ci<74||xe||z)&&I.warn("Your browser not support capture system audio"),r.frameRate&&i.getVideoTracks()[0]&&i.getVideoTracks()[0].applyConstraints({frameRate:{min:r.frameRate,ideal:r.frameRate},width:r.width,height:r.height}).catch(s=>{I.warn(`screen applyConstraints failed: ${s}`)}),r.audio){let s=Wp(r);I.info(`getUserMedia with constraints: ${JSON.stringify(s)}`),e=await navigator.mediaDevices.getUserMedia(s),i.addTrack(e.getAudioTracks()[0])}return i};function Wp(r){let e={echoCancellation:r.echoCancellation,autoGainControl:r.autoGainControl,noiseSuppression:r.noiseSuppression,sampleRate:r.sampleRate,channelCount:r.channelCount};return f(r.microphoneId)||(e.deviceId=r.microphoneId),{audio:e,video:!1}}function jp(r){let e={systemAudio:"include",selfBrowserSurface:"include",surfaceSwitching:"include"},t={width:xe?{max:r.width}:{ideal:r.width,max:r.width},height:xe?{max:r.height}:{ideal:r.height,max:r.height},frameRate:r.frameRate,displaySurface:"monitor"};if(e.video=t,r.systemAudio){let{echoCancellation:i=!0,noiseSuppression:s=!1,autoGainControl:o=!1}=r;e.audio={echoCancellation:i,noiseSuppression:s,autoGainControl:o,sampleRate:48e3}}return e}var Uu=Gp;var ke=class extends fe{profile={width:1920,height:1080,frameRate:5,bitrate:1600};objectFit="contain";isScreen=!0;constructor(e){super(e),this._log.id=`s-${this._log.id}`}async capture({systemAudio:e=!1,autoGainControl:t,echoCancellation:i,noiseSuppression:s,audioTrack:o,videoTrack:n}){try{let a;return n||o?(a=new MediaStream,n&&a.addTrack(n),o&&a.addTrack(o)):a=await Uu({audio:!1,systemAudio:e,width:this.profile.width,height:this.profile.height,frameRate:this.profile.frameRate,autoGainControl:t,echoCancellation:i,noiseSuppression:s}),await this.setInputMediaStreamTrack(a.getVideoTracks()[0]),a}catch(a){throw this.log.error(`getDisplayMedia error observed ${a}`),a instanceof A?a:new A({code:E.INITIALIZE_FAILED,name:a.name,message:a.message})}}async switchDevice(e){throw new Error("Method not implemented.")}};var Pt=class extends Ne{constructor(e){super(e),this._log.id=`s-${this._log.id}`}};var ar=class extends Ne{_inputs=new Map;_destination;audioContext=_t("audio-mixer");constructor(e){super(e),this._log.id=`m-${this._log.id}`,this.audioContext.state!=="running"&&this._log.warn(`audio mixer context state: ${this.audioContext.state}`),this._destination=this.audioContext.createMediaStreamDestination(),this._destination.channelCount=1}setInputMediaStreamTrack(e){this._inputTrack=e;let t=this.audioContext.createMediaStreamSource(new MediaStream([e]));this._inputs.set(e,{sourceNode:t}),t.connect(this._destination),this._outputTrack||(this._outputTrack=this._destination.stream.getAudioTracks()[0],this.setStateToCapture())}setTrackCaptureVolume(e,t){if(!this._inputs.has(e)||!this._destination)return;let i=this._inputs.get(e),{sourceNode:s}=i;i.gainNode||(this._log.info("create GainNode"),i.gainNode=this.audioContext.createGain()),i.gainNode.gain.value=t,s.disconnect(),s.connect(i.gainNode).connect(this._destination)}publish(e,t){return this.room=e,t}unpublish(){delete this.room}replaceTrack(e,t){!this.hasMix||(t&&(this._inputs.get(t)?.sourceNode.disconnect(),this._inputs.delete(t)),this.setInputMediaStreamTrack(e.outMediaTrack))}removeTrack(e){let t=this._inputs.get(e);!t||(t.sourceNode.disconnect(),this._inputs.delete(e),this._inputTrack===e&&delete this._inputTrack,this._inputs.size===0&&(delete this._outputTrack,this.unpublish(),this.close()))}close(){this.stop(),this.audioContext.close()}get hasMix(){return!!this._outputTrack}set channelCount(e){this._destination.channelCount=e}};y([q("capture","publish",{ignoreError:!0})],ar.prototype,"publish",1),y([q([],"capture")],ar.prototype,"unpublish",1);var cr=class extends Ie{musicId;mediaType=1;volume=0;profile={echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0,sampleRate:48e3,channelCount:1,bitrate:40};playerMuted=!1;audio;_audioContext;_destination;_source=null;constructor(e,t){super(1),this.manager=e,this.initAudioContext(),this._destination=this._audioContext.createMediaStreamDestination(),this._destination.channelCount=1,this.audio=document.createElement("audio"),this.audio.src=t.url,this.audio.crossOrigin="anonymous",Z(t.loop)&&this.loop(t.loop),K(t.volume)&&this.setVolume(t.volume),t.id&&(this.musicId=t.id),this._source=this._audioContext.createMediaElementSource(this.audio),this._source.connect(this._destination),this.setInputMediaStreamTrack(this._destination.stream.getAudioTracks()[0]),this.installPlayerEvent()}async play(){try{await this.audio.play(),await super.play(null,{muted:!1}),this._log.info(`music ${this.musicId} play success`)}catch(e){this._log.error(`music ${this.musicId} play error`,e)}}installPlayerEvent(){this.audio.addEventListener("error",this.handleAudioError.bind(this))}uninstallPlayerEvent(){this.audio.removeEventListener("error",this.handleAudioError.bind(this))}handleAudioError(e){this._log.warn(`local music ${this.musicId} audio error`,e)}initAudioContext(){this._audioContext=_t("music"),this._audioContext.state!=="running"&&this._log.warn(`local music ${this.musicId} context state: ${this._audioContext.state}`)}destroy(){this._source?.disconnect(),this._destination?.disconnect(),this.uninstallPlayerEvent(),this.player.stop(),this._audioContext.close().then(()=>{this._source=null,this._destination=null,this._audioContext=null})}getStream(){return this._destination?.stream||null}seek(e){if(e<0&&e>this.duration()){this._log.warn("Time beyond song duration.");return}this.audio.currentTime=e}getPosition(){return this.audio.currentTime||0}setVolume(e){if(e>1&&e<0){this.log.warn("volume is out of range");return}this.audio.volume=e}getVolume(){return this.audio.volume||0}setPlayBackRate(e){if(e>8&&e<0){this.log.warn("rate is out of range");return}this.audio.playbackRate=e}getPlayBackRate(){return this.audio.playbackRate||0}duration(){return this.audio.duration||0}loop(e){return this.audio&&Z(e)&&(this.audio.loop=e),this.audio.loop||!1}setOperation(e){e==="pause"&&this.audio.pause(),e==="resume"&&(this.audio.pause(),this.audio.play()),e==="stop"&&(this.audio.pause(),this.seek(0))}listenDeviceChange(){}};var Zt="input",Ai="output",wu="ondump",Bu="dumped";function Kp(r){let e;return Jp(r)?e="intl-schedule.rtc.qq.com":e="schedule.rtc.qq.com",e}var Jp=r=>(r=Number(r),r>0&&r<14e8);async function $u({sdkAppId:r,userId:e,userSig:t,timestamp:i}){let o=`https://${Kp(r)}/api/v1/audioAiAuth?sdkAppId=${r}&userId=${e}&userSig=${t}&timestamp=${i}`,n=await fetch(o),{data:{errCode:a,errMsg:c,sign:d,status:u}}=await n.json();if(u==="1")return{auth:!0,sign:d,status:u,message:c};let h="Init RTCAIDenoiser failed.",p="";switch(a){case 1:p="Please check your params.";break;case 2:p="You need to buy packages. Refer to: https://cloud.tencent.com/document/product/647/44247";break;case 3:p="Server is invalid. Please contact our engineer. ";break;case 4:p="Your packages is not active. Refer to: https://cloud.tencent.com/document/product/647/44247";break;case 5:p="Your packages is expired. Refer to: https://cloud.tencent.com/document/product/647/44247";break;case 6:p="Your version is not supported.";break}return{auth:!1,status:u,message:c?`${h} Reason: ${c}. ${p}`:`${h}, ${p}`}}var Hu=pe(ve(),1);function Fu(r,e){e=e||{};let t=r.numberOfChannels,{sampleRate:i}=r,s=e.float32?3:1,o=s===3?32:16,n;return t===2?n=zp(r.getChannelData(0),r.getChannelData(1)):n=r.getChannelData(0),Yp(n,s,i,t,o)}function Yp(r,e,t,i,s){let o=s/8,n=i*o,a=new ArrayBuffer(44+r.length*o),c=new DataView(a);return wo(c,0,"RIFF"),c.setUint32(4,36+r.length*o,!0),wo(c,8,"WAVE"),wo(c,12,"fmt "),c.setUint32(16,16,!0),c.setUint16(20,e,!0),c.setUint16(22,i,!0),c.setUint32(24,t,!0),c.setUint32(28,t*n,!0),c.setUint16(32,n,!0),c.setUint16(34,s,!0),wo(c,36,"data"),c.setUint32(40,r.length*o,!0),e===1?qp(c,44,r):Xp(c,44,r),a}function zp(r,e){let t=r.length+e.length,i=new Float32Array(t),s=0,o=0;for(;s<t;)i[s++]=r[o],i[s++]=e[o],o++;return i}function Xp(r,e,t){for(let i=0;i<t.length;i++,e+=4)r.setFloat32(e,t[i],!0)}function qp(r,e,t){for(let i=0;i<t.length;i++,e+=2){let s=Math.max(-1,Math.min(1,t[i]));r.setInt16(e,s<0?s*32768:s*32767,!0)}}function wo(r,e,t){for(let i=0;i<t.length;i++)r.setUint8(e+i,t.charCodeAt(i))}var sc=class{audioContext_;inputPCM_=new Float32Array;outputPCM_=new Float32Array;constructor(e){this.audioContext_=e}onDump(e,t){if(t===Zt){let i=this.inputPCM_.length,s=new Float32Array(i+e[0].length);s.set(this.inputPCM_),s.set(e[0],i),this.inputPCM_=s}if(t===Ai){let i=this.outputPCM_.length,s=new Float32Array(i+e[0].length);s.set(this.outputPCM_),s.set(e[0],i),this.outputPCM_=s}}getBlob(e){let t=e===Zt?this.inputPCM_:this.outputPCM_,i=this.audioContext_.createBuffer(2,t.length,48e3);i.copyToChannel(t,0,0),i.copyToChannel(t,1,0);let s=Fu(i),o=new window.Blob([new DataView(s)],{type:"audio/wav"});return i=null,o}reset(){this.inputPCM_=new Float32Array,this.outputPCM_=new Float32Array}destroy(){this.reset()}},oc=sc;var nc=class{audioContext_;destination_;gainNode_;log_;workletNode_;isDumping_=!1;dump_;trackConstraint_={};audioTrack_;source_;denoiserTrack_;enableDenoise_=!0;emitter_=new Hu.default;constructor({sdkAppId:e,userId:t,audioContext:i,sign:s,status:o,worklet:n,timestamp:a,logger:c}){this.audioContext_=i,this.destination_=this.audioContext_.createMediaStreamDestination(),this.gainNode_=this.audioContext_.createGain(),this.gainNode_.gain.value=1.1,this.log_=c,this.workletNode_=n,this.workletNode_.connect(this.gainNode_).connect(this.destination_),this.workletNode_.port.postMessage({type:"init",data:{sdkAppId:String(e),userId:t,timestamp:a,sign:s,status:o}}),this.workletNode_.port.onmessage=d=>{let{type:u,data:h}=d.data;if(u===wu&&this.isDumping_){let{inputPCM:p,outputPCM:g}=h;this.dump_.onDump(p,Zt),this.dump_.onDump(g,Ai)}u===Bu&&this.dumped()},this.dump_=new oc(this.audioContext_)}dumped(){this.isDumping_=!1;let e=this.dump_.getBlob(Zt),t=this.dump_.getBlob(Ai);this.emitter_.emit("ondumpend",{blob:e,name:Zt}),this.emitter_.emit("ondumpend",{blob:t,name:Ai}),this.dumpedWAV(e,Zt),this.dumpedWAV(t,Ai),this.dump_.reset()}dumpedWAV(e,t){let i=window.URL.createObjectURL(e),s=document.createElement("a");s.href=i,s.download=`${t}-${Date.now()}.wav`,s.click(),window.URL.revokeObjectURL(i),s.href=""}async process(e){if(this.audioTrack_=e,!this.audioTrack_)throw new Error("RTCAIDenoiser: cannot process without audioTrack.");let t=new MediaStream;t.addTrack(this.audioTrack_),this.source_=this.audioContext_.createMediaStreamSource(t),await this.source_.connect(this.workletNode_),this.trackConstraint_=this.audioTrack_.getConstraints(),this.trackConstraint_.noiseSuppression=!1,await this.audioTrack_.applyConstraints(this.trackConstraint_);let i=this.destination_.stream;return this.denoiserTrack_=i.getAudioTracks()[0],this.log_.info(`RTCAIDenoiser: denoiser process track ID: ${e.id} success.`),this.denoiserTrack_}async updateTrack(e){let t=new MediaStream;await t.addTrack(e);let i=this.audioContext_.createMediaStreamSource(t);this.source_.disconnect(),i.connect(this.workletNode_),this.audioTrack_?.stop(),this.audioTrack_=e,this.source_=i,this.log_.info("RTCAIDenoiser: updateTrack success.")}async disable(){return this.enableDenoise_=!1,this.workletNode_?.port.postMessage({type:"disable"}),this.trackConstraint_.noiseSuppression=!0,await this.audioTrack_?.applyConstraints(this.trackConstraint_),this.log_.info("RTCAIDenoiser: disable ai denoiser."),this.enableDenoise_}async enable(){return this.enableDenoise_=!0,this.workletNode_?.port.postMessage({type:"enable"}),this.trackConstraint_.noiseSuppression=!1,await this.audioTrack_?.applyConstraints(this.trackConstraint_),this.log_.info("RTCAIDenoiser: enable ai denoiser."),this.enableDenoise_}startDump(){return this.isDumping_?(this.log_.info("RTCAIDenoiser: data is currently being dumped."),!1):(this.workletNode_.port.postMessage({type:"startDump"}),this.dump_?this.dump_.reset():this.dump_=new oc(this.audioContext_),this.isDumping_=!0,this.log_.info("RTCAIDenoiser: start dump data."),!0)}stopDump(){!this.isDumping_||(this.workletNode_.port.postMessage({type:"stopDump"}),this.isDumping_=!1,this.log_.info("RTCAIDenoiser: stop dump data."))}getAudioTrack(){return this.audioTrack_}getDenoiserTrack(){return this.destination_.stream.getAudioTracks()[0]}get enabled(){return this.enableDenoise_}destroy(){this.log_.info("RTCAIDenoiser: destroy processor."),this.audioTrack_?.stop(),this.workletNode_?.port.postMessage({type:"destroy"}),this.workletNode_.port.onmessage=null,this.source_?.disconnect(),this.destination_?.disconnect(),this.workletNode_?.disconnect(),this.dump_?.destroy(),this.emitter_.removeAllListeners()}on(e,t,i){this.emitter_.on(e,t,i)}off(e,t,i){e==="*"?this.emitter_.removeAllListeners():this.emitter_.off(e,t,i)}},Gu=nc;var ac=class{constructor({assetsPath:e,log:t,audioContext:i}){this.isLoaded_=!1;this.audioContext_=i,this.log_=t,this.assetsPath_=e}async createProcessor(e){let t=String(Date.now()).slice(0,-3),{auth:i,sign:s,status:o,message:n}=await $u({...e,timestamp:t});if(!i)throw this.log_.info(`RTCAIDenoiser: ${e.userId} auth result: ${i}. Message: ${n}`),new Error(n);try{await this.load()}catch{throw new Error("Init wasm failed, please check your assetsPath.")}let a=await this.initWorklet(),c=new Gu({...e,audioContext:this.audioContext_,timestamp:t,sign:s,status:o,worklet:a,logger:this.log_});return this.log_.info(`RTCAIDenoiser: ${e.userId} create denoiser processor success.`),c}async load(){if(!this.isLoaded_)try{await this.audioContext_.audioWorklet.addModule(`${this.assetsPath_}/denoiser-wasm.js`),this.isLoaded_=!0}catch(e){throw this.log_.error(`Init assets from ${this.assetsPath_} failed! Reason: ${e}`),e}}async initWorklet(){try{return new AudioWorkletNode(this.audioContext_,"trtc-denoiser-processor",{numberOfInputs:1,numberOfOutputs:1})}catch{return await this.load(),new AudioWorkletNode(this.audioContext_,"trtc-denoiser-processor",{numberOfInputs:1,numberOfOutputs:1})}}destroy(){this.audioContext_?.close()}isSupported(){return"AudioWorklet"in window&&"WebAssembly"in window}},Wu=ac;var Bo=class{_localAudioTrack=null;_localScreenAudioTrack=null;localMixAudioTrack;isDenoiserInit=!1;isDenoiserEnabled=!1;isDenoiserProcessed=!1;mixedMusicMap=new Map;cacheMusicMap=new Map;_log;denoiserTrack=null;denoiser=null;denoiserProcessor;initProcessorOptions;denoiserContext;constructor({room:e}){this._log=I.createLogger({id:"am",userId:e.userId,sdkAppId:e.sdkAppId})}get mixedAudioTrack(){return this.localMixAudioTrack?this.localMixAudioTrack.outMediaTrack:null}get hasScreenAudioTrack(){return this._localScreenAudioTrack!==null}get hasAudioTrack(){return this._localAudioTrack!==null}changeInput(e,t){if(e instanceof Pt){this._localScreenAudioTrack=e,e._outputTrack=e.mediaTrack;return}if(e instanceof cr){e._outputTrack=e.mediaTrack,this.localMixAudioTrack?.replaceTrack(e,t);return}if(e instanceof Ne){this._localAudioTrack=e,this.isDenoiserProcessed&&this.denoiserProcessor?.updateTrack(e.mediaTrack),e.setOutputMediaStreamTrack(this.isDenoiserProcessed?this.denoiserTrack:e.mediaTrack),this.localMixAudioTrack?.replaceTrack(e,t);return}if(e instanceof Mt)return e.setOutputMediaStreamTrack(e.mediaTrack)}removeInput(e){if(e instanceof Ne)return this.removeAudioTrack(e);e instanceof Mt}async addMusicSource(e){this._log.info(`add music source, id: ${e.id} url: ${e.url}`);let{id:t,url:i,loop:s,volume:o}=e;if(this.mixedMusicMap.has(t))return;let n;return this.cacheMusicMap.has(t)?n=this.cacheMusicMap.get(t):n=new cr(this,e),n.play(),this._log.info(`start mix audio ${t} success.`),this.mixedMusicMap.set(t,n),this.cacheMusicMap.set(t,n),n}async updateMusicSource(e){let{id:t,volume:i,loop:s,operation:o,seekFrom:n}=e;if(this._log.info(`update music source, ${JSON.stringify(e)}`),this.mixedMusicMap.has(t)){let a=this.mixedMusicMap.get(t);f(i)||a.setVolume(i),f(s)||a.loop(s),f(o)||a.setOperation(o),f(n)||a.seek(n)}}async addAudioTrack(e){if(this.localMixAudioTrack||(this.localMixAudioTrack=new ar(this)),this._log.info(`start add audioTrack, userId: ${e.userId}`),e===this._localAudioTrack){if($e){let t=await e.mediaTrack?.getSettings();this.localMixAudioTrack.channelCount=t?.channelCount||1}this.mixedMusicMap.forEach(t=>{this.localMixAudioTrack?.setInputMediaStreamTrack(t.outMediaTrack)})}this.localMixAudioTrack.setInputMediaStreamTrack(e.outMediaTrack),this._localAudioTrack&&this._localAudioTrack.captureVolume!==1&&this.setCaptureVolume(this._localAudioTrack.captureVolume)}removeAudioTrack(e){e===this._localAudioTrack&&(this.localMixAudioTrack&&this.mixedMusicMap.forEach(t=>{this.localMixAudioTrack.removeTrack(t.outMediaTrack)}),this._localAudioTrack=null),this.localMixAudioTrack&&(this.localMixAudioTrack.removeTrack(e.outMediaTrack),this.localMixAudioTrack.hasMix||delete this.localMixAudioTrack)}removeMusicSource({id:e}){if(!!this.localMixAudioTrack){if(this.mixedMusicMap.has(e)){this._log.info(`remove music source, music id: ${e}`);let t=this.mixedMusicMap.get(e);t.stop(),this.localMixAudioTrack.removeTrack(t.outMediaTrack),this.mixedMusicMap.delete(e)}e==="*"&&this.destroyAllMusic()}}destroyAllMusic(){this._log.info("destroy all music source."),this.mixedMusicMap.forEach((e,t)=>{this.removeMusicSource({id:t})})}destroyAllCache(){this._log.info("destroy all music cache."),this.cacheMusicMap.forEach(e=>e.stop())}async initDenoiser(e){let{assetsPath:t,sdkAppId:i,userId:s,userSig:o}=e;try{(!this.denoiserContext||this.denoiserContext.state==="closed")&&(this.denoiserContext=_t("denoiser")),this.denoiser||(this.denoiser=new Wu({assetsPath:t,log:this._log,audioContext:this.denoiserContext})),this.denoiserProcessor||(this.denoiserProcessor=await this.denoiser.createProcessor({sdkAppId:i,userId:s,userSig:o})),this.isDenoiserInit=!0,this.initProcessorOptions=e}catch(n){throw n}}async enableDenoiser(e){if(this.isDenoiserEnabled=!0,!!this.hasAudioTrack)if(this.isDenoiserProcessed)this.denoiserProcessor?.enable();else{let t=this._localAudioTrack?.mediaTrack;if(!t)return;this.denoiserProcessor||await this.initDenoiser(this.initProcessorOptions),this.denoiserTrack=await this.denoiserProcessor.process(t),this.isDenoiserProcessed=!0,this._localAudioTrack._outputTrack=this.denoiserTrack,this.localMixAudioTrack?.replaceTrack(this._localAudioTrack,t)}}async disableDenoiser(){this.isDenoiserEnabled&&(this.denoiserProcessor?.disable(),this.isDenoiserEnabled=!1)}setCaptureVolume(e){this.localMixAudioTrack&&this._localAudioTrack&&this._localAudioTrack.mediaTrack&&this.localMixAudioTrack?.setTrackCaptureVolume(this._localAudioTrack.mediaTrack,e)}destroyDenoiserProcessor(){this.denoiserProcessor&&(this.denoiserProcessor.destroy(),delete this.denoiserProcessor,this.isDenoiserInit=!1,this.isDenoiserEnabled=!1,this.isDenoiserProcessed=!1,this.denoiserTrack?.stop(),this.denoiserTrack=null),this.denoiserContext&&(this.denoiserContext.close(),delete this.denoiserContext)}destroy(){this.destroyAllMusic(),this.destroyAllCache(),this.destroyDenoiserProcessor(),this.denoiser?.destroy(),this.localMixAudioTrack&&(this.localMixAudioTrack.close(),delete this.localMixAudioTrack)}};var cc=class extends Ii{constructor(t,i,s){super({userId:i.userId,sdkAppId:t.sdkAppId,mediaType:s,room:t});this.room=t;this.user=i;this.tinyId=i.tinyId}tinyId;isRemote=!0;setMute(t){return this.hasFlag&&super.setMute(t)}setInputMediaStreamTrack(t){super.setInputMediaStreamTrack(t),this.hasFlag&&this.isSubscribed&&this.player.setTrack(this.outMediaTrack)}waitHasMediaTrack(){return new Promise(t=>{this.mediaTrack?t():this.once("input-media-track-changed",t)})}get isSubscribing(){return this.state.toString()==="subscribeing"}get isSubscribed(){return this.state===cc.STATE_SUBSCRIBE}subscribe(t){return t}unsubscribe(){this.player.setTrack(null),this.streamType==="main"&&this.kind==="video"&&this.room.changeType(!1,this.user)}updatePlayingState(t){if(this.isPlayCalled&&this.player.isStopped===t){if(t&&(!this.isSubscribed||!this.hasFlag||!this.outMediaTrack)){this.log.info(`abort play, isSubscribed:${this.isSubscribed} hasFlag:${this.hasFlag} hasTrack:${!!this.outMediaTrack}`);return}super.updatePlayingState(t)}}onFlagChanged(){this.updatePlayingState(this.hasFlag)}},et=cc;ae(et,"STATE_SUBSCRIBE","subscribe"),y([q(k.INIT,et.STATE_SUBSCRIBE,{success(){this.log.info("subscribed"),_.emit(m.REMOTE_TRACK_SUBSCRIBED,{track:this}),this.updatePlayingState(!0)},ignoreError:!0})],et.prototype,"subscribe",1),y([q(et.STATE_SUBSCRIBE,k.INIT,{success(){this.log.info("unsubscribed"),this.updatePlayingState(!1),_.emit(m.REMOTE_TRACK_UNSUBSCRIBED,{track:this})}})],et.prototype,"unsubscribe",1);var Mt=class extends et{volume=0;mediaType=1;stat={bytesReceived:0,packetsReceived:0,packetsLost:0,end2EndDelay:0,jitterBufferDelay:0};constructor(e,t){super(e,t,1),this.manager=e.audioManager}getAudioLevel(){let e=(this.volume||super.getAudioLevel())*this.playbackVolume;return e>1?1:e}get hasFlag(){return this.user.muteState.hasAudio&&!this.user.muteState.audioMuted}isFlagChanged(e){let t=e.hasAudio&&!e.audioMuted;return this.hasFlag||(this.volume=0),this.hasFlag!==t}};var Et=class extends et{mediaType=4;stat={bytesReceived:0,packetsReceived:0,packetsLost:0,framesReceived:0,framesDecoded:0,frameWidth:0,frameHeight:0,end2EndDelay:0,jitterBufferDelay:0};constructor(e,t,i=4){super(e,t,i),this.manager=e.videoManager}changeType(e){this.room.changeType(e,this.user)}get hasFlag(){return this.user.muteState.hasVideo&&!this.user.muteState.videoMuted}isFlagChanged(e){let t=e.hasVideo&&!e.videoMuted;return this.hasFlag!==t}},dr=class extends Et{mediaType=2;objectFit="contain";constructor(e,t){super(e,t,2)}get hasFlag(){return this.user.muteState.hasAuxiliary}isFlagChanged(e){let t=e.hasAuxiliary;return this.hasFlag!==t}};function de(...r){}var Qp=r=>r();function uc(){this.dispose()}var Zp=()=>typeof __FASTRX_DEVTOOLS__<"u",em=1,Lt=class extends Function{toString(){return`${this.name}(${this.args.length?[...this.args].join(", "):""})`}subscribe(e){let t=new dc(e,this,this.streamId++);return tt.subscribe({id:this.id,end:!1},{nodeId:t.sourceId,streamId:t.id}),this(t),t}},_s=class{constructor(){this.defers=new Set,this.disposed=!1}next(e){}complete(){this.dispose()}error(e){this.dispose()}get bindDispose(){return()=>this.dispose()}dispose(){this.disposed=!0,this.complete=de,this.error=de,this.next=de,this.dispose=de,this.subscribe=de,this.doDefer()}subscribe(e){return e instanceof Lt?e.subscribe(this):e(this),this}get bindSubscribe(){return e=>this.subscribe(e)}doDefer(){this.defers.forEach(Qp),this.defers.clear()}defer(e){this.defers.add(e)}removeDefer(e){this.defers.delete(e)}reset(){this.disposed=!1,delete this.complete,delete this.next,delete this.dispose,delete this.next,delete this.subscribe}resetNext(){delete this.next}resetComplete(){delete this.complete}resetError(){delete this.error}},L=class extends _s{constructor(e){super(),this.sink=e,e.defer(this.bindDispose)}next(e){this.sink.next(e)}complete(){this.sink.complete()}error(e){this.sink.error(e)}};function ju(r,...e){return e.reduce((t,i)=>i(t),r)}function xt(r,e,t){if(Zp()){let i=Object.defineProperties(Object.setPrototypeOf(r,Lt.prototype),{streamId:{value:0,writable:!0,configurable:!0},name:{value:e,writable:!0,configurable:!0},args:{value:t,writable:!0,configurable:!0},id:{value:0,writable:!0,configurable:!0}});tt.create(i);for(let s=0;s<t.length;s++){let o=t[s];typeof o=="function"&&o instanceof Lt&&tt.addSource(i,o)}return i}return r}function w(r,e){return function(...t){return i=>{if(i instanceof Lt){let s=xt(o=>{let n=new r(o,...t);n.sourceId=s.id,n.subscribe(i)},e,arguments);return s.source=i,tt.pipe(s),s}else return s=>i(new r(s,...t))}}}function ei(r,e){window.postMessage({source:"fastrx-devtools-backend",payload:{event:r,payload:e}})}var dc=class extends L{constructor(e,t,i){super(e),this.source=t,this.id=i,this.sourceId=e.sourceId,this.defer(()=>{tt.defer(this.source,this.id)})}next(e){tt.next(this.source,this.id,e),this.sink.next(e)}complete(){tt.complete(this.source,this.id),this.sink.complete()}error(e){tt.complete(this.source,this.id,e),this.sink.error(e)}},tt={addSource(r,e){ei("addSource",{id:r.id,name:r.toString(),source:{id:e.id,name:e.toString()}})},next(r,e,t){ei("next",{id:r.id,streamId:e,data:t&&t.toString()})},subscribe({id:r,end:e},t){ei("subscribe",{id:r,end:e,sink:{nodeId:t&&t.nodeId,streamId:t&&t.streamId}})},complete(r,e,t){ei("complete",{id:r.id,streamId:e,err:t?t.toString():null})},defer(r,e){ei("defer",{id:r.id,streamId:e})},pipe(r){ei("pipe",{name:r.toString(),id:r.id,source:{id:r.source.id,name:r.source.toString()}})},update(r){ei("update",{id:r.id,name:r.toString()})},create(r){r.id||(r.id=em++),ei("create",{name:r.toString(),id:r.id})}},$o=class extends Error{constructor(e){super(`timeout after ${e}ms`),this.timeout=e}};var lc=class extends _s{constructor(e){super(),this.source=e,this.sinks=new Set}add(e){e.defer(()=>this.remove(e)),this.sinks.add(e).size===1&&(this.reset(),this.subscribe(this.source))}remove(e){this.sinks.delete(e),this.sinks.size===0&&this.dispose()}next(e){this.sinks.forEach(t=>t.next(e))}complete(){this.sinks.forEach(e=>e.complete()),this.sinks.clear()}error(e){this.sinks.forEach(t=>t.error(e)),this.sinks.clear()}};function Fo(){return r=>{let e=new lc(r);if(r instanceof Lt){let t=xt(i=>{e.add(i)},"share",arguments);return e.sourceId=t.id,t.source=r,tt.pipe(t),t}return xt(e.add.bind(e),"share",arguments)}}function tm(...r){return xt(e=>{let t=r.length,i=t,s=t,o=new Array(t),n=()=>{--s===0&&e.complete()},a=(c,d)=>{let u=new L(e);u.next=h=>{--i===0?(u.next=p=>{o[d]=p,e.next(o)},u.next(h)):o[d]=h},u.complete=n,u.subscribe(c)};r.forEach(a)},"combineLatest",arguments)}var hc=class extends L{constructor(e,...t){super(e);let i=new L(this.sink);i.next=s=>this.buffer=s,i.complete=de,i.subscribe(tm(...t))}next(e){this.buffer&&this.sink.next([e,...this.buffer])}},PS=w(hc,"withLatestFrom"),pc=class extends L{constructor(e,t,i){super(e),this.bufferSize=t,this.startBufferEvery=i,this.buffer=[],this.count=0,this.startBufferEvery&&(this.buffers=[[]])}next(e){this.startBufferEvery?(this.count++===this.startBufferEvery&&(this.buffers.push([]),this.count=1),this.buffers.forEach(t=>{t.push(e)}),this.buffers[0].length===this.bufferSize&&this.sink.next(this.buffers.shift())):(this.buffer.push(e),this.buffer.length===this.bufferSize&&(this.sink.next(this.buffer),this.buffer=[]))}complete(){this.buffer.length?this.sink.next(this.buffer):this.buffers.length&&this.buffers.forEach(e=>this.sink.next(e)),super.complete()}},MS=w(pc,"bufferCount"),mc=class extends L{constructor(e,t){super(e),this.buffer=[];let i=new L(e);i.next=s=>{e.next(this.buffer),this.buffer=[]},i.complete=de,i.subscribe(t)}next(e){this.buffer.push(e)}complete(){this.buffer.length&&this.sink.next(this.buffer),super.complete()}},LS=w(mc,"buffer");function _c(r){let e=arguments,t=Fo()(xt(i=>{t.next=s=>i.next(s),t.complete=()=>i.complete(),t.error=s=>i.error(s),r&&i.subscribe(r)},"subject",e));return t.next=de,t.complete=de,t.error=de,t}function Ku(r){return xt(e=>{let t=0,i=setInterval(()=>e.next(t++),r);return e.defer(()=>{clearInterval(i)}),"interval"},"interval",arguments)}var fc=class extends L{constructor(e,t,i){super(e),this.f=t;let s=()=>{this.sink.next(this.acc),this.sink.complete()};typeof i>"u"?this.next=o=>{this.acc=o,this.complete=s,this.resetNext()}:(this.acc=i,this.complete=s)}next(e){this.acc=this.f(this.acc,e)}},im=w(fc,"reduce");var Tc=class extends L{constructor(e,t,i){super(e),this.filter=t,this.thisArg=i}next(e){this.filter.call(this.thisArg,e)&&this.sink.next(e)}},rm=w(Tc,"filter"),Ec=class extends L{next(e){}},jS=w(Ec,"ignoreElements"),gc=class extends L{constructor(e,t){super(e),this.count=t}next(e){this.sink.next(e),--this.count===0&&this.complete()}},sm=w(gc,"take"),Sc=class extends L{constructor(e,t){super(e);let i=new L(e);i.next=()=>e.complete(),i.complete=uc,i.subscribe(t)}},KS=w(Sc,"takeUntil"),Ic=class extends L{constructor(e,t){super(e),this.f=t}next(e){this.f(e)?this.sink.next(e):this.complete()}},om=w(Ic,"takeWhile");var Rc=class extends L{constructor(e,t){super(e),this.count=t}next(e){--this.count===0&&(this.next=super.next)}},JS=w(Rc,"skip"),Ac=class extends L{constructor(e,t){super(e),e.next=de;let i=new L(e);i.next=()=>{i.dispose(),e.resetNext()},i.complete=uc,i.subscribe(t)}},YS=w(Ac,"skipUntil"),Cc=class extends L{constructor(e,t){super(e),this.f=t}next(e){this.f(e)||(this.next=super.next,this.next(e))}},nm=w(Cc,"skipWhile"),am={leading:!0,trailing:!1},yc=class extends L{constructor(e,t,i){super(e),this.durationSelector=t,this.trailing=i}cacheValue(e){this.last=e,this.disposed&&this.throttle(e)}send(e){this.sink.next(e),this.throttle(e)}throttle(e){this.reset(),this.subscribe(this.durationSelector(e))}next(){this.complete()}complete(){this.dispose(),this.trailing&&this.send(this.last)}},bc=class extends L{constructor(e,t,i=am){super(e),this.durationSelector=t,this.config=i,this._throttle=new yc(this.sink,this.durationSelector,this.config.trailing),this._throttle.dispose()}next(e){this._throttle.disposed&&this.config.leading?this._throttle.send(e):this._throttle.cacheValue(e)}complete(){this._throttle.throttle=de,this._throttle.complete(),super.complete()}},zS=w(bc,"throttle");var Nc=class extends L{next(){this.complete()}complete(){this.dispose(),this.sink.next(this.last)}},vc=class extends L{constructor(e,t){super(e),this.durationSelector=t,this._debounce=new Nc(this.sink),this._debounce.dispose()}next(e){this._debounce.dispose(),this._debounce.reset(),this._debounce.last=e,this._debounce.subscribe(this.durationSelector(e))}complete(){this._debounce.complete(),super.complete()}},XS=w(vc,"debounce");var Dc=class extends L{constructor(e,t,i){super(e),this.count=t,this.defaultValue=i}next(e){this.count--===0&&(this.defaultValue=e,this.complete())}complete(){if(this.defaultValue===void 0){this.error(new Error("not enough elements in sequence"));return}else this.sink.next(this.defaultValue);super.complete()}},qS=w(Dc,"elementAt");var Oc=class extends L{constructor(e,t){super(e),this.f=t,this.i=0}next(e){this.f(e)?(this.sink.next(this.i++),this.complete()):++this.i}},QS=w(Oc,"findIndex"),kc=class extends L{constructor(e,t,i){super(e),this.f=t,this.defaultValue=i,this.index=0}next(e){(!this.f||this.f(e,this.index++))&&(this.defaultValue=e,this.complete())}complete(){if(this.defaultValue===void 0){this.error(new Error("no elements in sequence"));return}else this.sink.next(this.defaultValue);super.complete()}},ZS=w(kc,"first"),Pc=class extends L{constructor(e,t,i){super(e),this.f=t,this.defaultValue=i,this.index=0}next(e){(!this.f||this.f(e,this.index++))&&(this.defaultValue=e)}complete(){if(this.defaultValue===void 0){this.error(new Error("no elements in sequence"));return}else this.sink.next(this.defaultValue);super.complete()}},eI=w(Pc,"last"),Mc=class extends L{constructor(e,t){super(e),this.predicate=t,this.index=0}next(e){this.predicate(e,this.index++)?this.result=!0:(this.result=!1,this.complete())}complete(){if(this.result===void 0){this.error(new Error("no elements in sequence"));return}else this.sink.next(this.result);super.complete()}},tI=w(Mc,"every");var Lc=class extends L{constructor(e,t,i){super(e),this.f=t,typeof i>"u"?this.next=s=>{this.acc=s,this.resetNext(),this.sink.next(this.acc)}:this.acc=i}next(e){this.sink.next(this.acc=this.f(this.acc,e))}},oI=w(Lc,"scan"),xc=class extends L{constructor(){super(...arguments),this.hasLast=!1}next(e){this.hasLast?this.sink.next([this.last,e]):this.hasLast=!0,this.last=e}},nI=w(xc,"pairwise"),Vc=class extends L{constructor(e,t,i){super(e),this.mapper=t,this.thisArg=i}next(e){super.next(this.mapper.call(this.thisArg,e))}},Ju=w(Vc,"map");var ur=class extends L{constructor(e,t,i){super(e),this.data=t,this.context=i}next(e){let t=this.context.combineResults;t?this.sink.next(t(this.data,e)):this.sink.next(e)}tryComplete(){this.context.resetComplete(),this.dispose()}},lr=class extends L{constructor(e,t,i){super(e),this.makeSource=t,this.combineResults=i,this.index=0}subInner(e,t){let i=this.currentSink=new t(this.sink,e,this);this.complete=this.tryComplete,i.complete=i.tryComplete,i.subscribe(this.makeSource(e,this.index++))}tryComplete(){this.currentSink.resetComplete(),this.dispose()}},Ho=class extends ur{},Go=class extends lr{next(e){this.subInner(e,Ho),this.next=t=>{this.currentSink.dispose(),this.subInner(t,Ho)}}},cm=w(Go,"switchMap");function Jo(r){return(e,t)=>r(()=>e,t)}var aI=Jo(w(Go,"switchMapTo")),Uc=class extends ur{tryComplete(){this.dispose(),this.context.sources.length?this.context.subNext():(this.context.resetNext(),this.context.resetComplete())}},Wo=class extends lr{constructor(){super(...arguments),this.sources=[],this.next2=this.sources.push.bind(this.sources)}next(e){this.next2(e),this.subNext()}subNext(){this.next=this.next2,this.subInner(this.sources.shift(),Uc),this.disposed&&this.sources.length===0&&this.currentSink.resetComplete()}tryComplete(){this.sources.length===0&&this.currentSink.resetComplete(),this.dispose()}},cI=w(Wo,"concatMap"),dI=Jo(w(Wo,"concatMapTo")),wc=class extends ur{tryComplete(){this.context.inners.delete(this),super.dispose(),this.context.inners.size===0&&this.context.resetComplete()}},jo=class extends lr{constructor(){super(...arguments),this.inners=new Set}next(e){this.subInner(e,wc),this.inners.add(this.currentSink)}tryComplete(){this.inners.size===1?this.inners.forEach(e=>e.resetComplete()):this.dispose()}},uI=w(jo,"mergeMap"),lI=Jo(w(jo,"mergeMapTo")),Bc=class extends ur{dispose(){this.context.resetNext(),super.dispose()}},Ko=class extends lr{next(e){this.next=de,this.subInner(e,Bc)}},hI=w(Ko,"exhaustMap"),pI=Jo(w(Ko,"exhaustMapTo")),$c=class extends L{constructor(e,t){super(e),this.f=t,this.groups=new Map}next(e){let t=this.f(e),i=this.groups.get(t);typeof i>"u"&&(i=_c(),i.key=t,this.groups.set(t,i),super.next(i)),i.next(e)}complete(){this.groups.forEach(e=>e.complete()),super.complete()}error(e){this.groups.forEach(t=>t.error(e)),super.error(e)}},mI=w($c,"groupBy"),Fc=class extends L{constructor(){super(...arguments),this.start=new Date}next(e){this.sink.next({value:e,interval:Number(new Date)-Number(this.start)}),this.start=new Date}},_I=w(Fc,"timeInterval"),Hc=class extends L{constructor(e,t){super(e),this.miniseconds=t,this.buffer=[],this.id=setInterval(()=>{this.sink.next(this.buffer.concat()),this.buffer.length=0},this.miniseconds)}next(e){this.buffer.push(e)}complete(){this.sink.next(this.buffer),super.complete()}dispose(){clearInterval(this.id),super.dispose()}},fI=w(Hc,"bufferTime"),Gc=class extends L{constructor(e,t){super(e),this.buffer=[],this.delayTime=t}dispose(){clearTimeout(this.timeoutId),super.dispose()}delay(e){this.timeoutId=setTimeout(()=>{let t=this.buffer.shift();if(t){let{time:i,data:s}=t;super.next(s),this.buffer.length&&this.delay(Number(this.buffer[0].time)-Number(i))}},e)}next(e){this.buffer.length||this.delay(this.delayTime),this.buffer.push({time:new Date,data:e})}complete(){this.timeoutId=setTimeout(()=>super.complete(),this.delayTime)}},TI=w(Gc,"delay"),Wc=class extends L{constructor(e,t){super(e),this.selector=t}error(e){this.dispose(),this.selector(e)(this.sink)}},EI=w(Wc,"catchError");var jc=class extends L{constructor(e,t){super(e),t instanceof Function?this.next=i=>{t(i),e.next(i)}:(t.next&&(this.next=i=>{t.next(i),e.next(i)}),t.complete&&(this.complete=()=>{t.complete(),e.complete()}),t.error&&(this.error=i=>{t.error(i),e.error(i)}))}},RI=w(jc,"tap"),Kc=class extends L{constructor(e,t){super(e),this.timeout=t,this.id=setTimeout(()=>this.error(new $o(this.timeout)),this.timeout)}next(e){super.next(e),clearTimeout(this.id),this.next=super.next}dispose(){clearTimeout(this.id),super.dispose()}},AI=w(Kc,"timeout");var $I=ju(Ku(250),Ju(()=>performance.now()),Fo());var hr=new Map;_.on(m.JOIN_SUCCESS,({room:r})=>{le(r.userId,{eventId:32788,eventDesc:"join room"})});_.on(m.LEAVE_START,({room:r})=>{le(r.userId,{eventId:32789,eventDesc:"leave room"})});_.on(m.LOCAL_TRACK_PUBLISHED,({track:r})=>{if(r.room){let e=32769;r.mediaType===4?e=32768:r.mediaType===2&&(e=32805),le(r.room.userId,{eventId:e,eventDesc:`publish ${r.kind}`})}});_.on(m.LOCAL_TRACK_UNPUBLISHED,({track:r})=>{if(r.room){let e=32771;r.mediaType===4?e=32770:r.mediaType===2&&(e=32806),le(r.room.userId,{eventId:e,eventDesc:`unpublish ${r.kind}`})}});_.on(m.TRACK_MUTED,({track:r})=>{r.room&&(r.kind===l.AUDIO?le(r.room.userId,{eventId:r.isRemote?32785:32772,eventDesc:"mute audio",remoteUserId:r.isRemote?r.userId:void 0}):le(r.room.userId,{eventId:r.isRemote?32784:32773,eventDesc:"mute video",remoteUserId:r.isRemote?r.userId:void 0}))});_.on(m.TRACK_UNMUTED,({track:r})=>{r.room&&(r.kind===l.AUDIO?le(r.room.userId,{eventId:r.isRemote?32787:32774,eventDesc:"unmute audio",remoteUserId:r.isRemote?r.userId:void 0}):le(r.room.userId,{eventId:r.isRemote?32786:32775,eventDesc:"unmute video",remoteUserId:r.isRemote?r.userId:void 0}))});_.on(m.REMOTE_TRACK_SUBSCRIBED,({track:r})=>{!r.room||(r.mediaType===1&&le(r.room.userId,{eventId:32777,eventDesc:`${l.SUBSCRIBE} ${r.kind}`,remoteUserId:r.userId}),r.mediaType===4&&le(r.room.userId,{eventId:32776,eventDesc:`${l.SUBSCRIBE} ${r.kind}`,remoteUserId:r.userId}),r.mediaType===8&&le(r.room.userId,{eventId:32803,eventDesc:`${l.SUBSCRIBE} ${l.SMALL_VIDEO}`,remoteUserId:r.userId}))});_.on(m.REMOTE_TRACK_UNSUBSCRIBED,({track:r})=>{!r.room||(r.mediaType===1&&le(r.room.userId,{eventId:32779,eventDesc:`${l.UNSUBSCRIBE} ${r.kind}`,remoteUserId:r.userId}),r.mediaType===4&&le(r.room.userId,{eventId:32778,eventDesc:`${l.UNSUBSCRIBE} ${r.kind}`,remoteUserId:r.userId}),r.mediaType===8&&le(r.room.userId,{eventId:32804,eventDesc:`${l.UNSUBSCRIBE} ${l.SMALL_VIDEO}`,remoteUserId:r.userId}))});_.on(m.SWITCH_DEVICE_SUCCESS,({track:r})=>{r.room&&le(r.room.userId,{eventId:r.kind===l.VIDEO?32780:32781,eventDesc:`switch ${r.kind===l.VIDEO?"camera":"microphone"}`})});_.on(m.LOCAL_TRACK_REPLACED,({track:r})=>{r.room&&le(r.room.userId,{eventId:r.kind===l.VIDEO?32782:32783,eventDesc:`replace ${r.kind}`})});_.on(m.SIGNAL_CONNECTION_STATE_CHANGED,({room:r,prevState:e,state:t})=>{let i,s;switch(t){case"CONNECTED":e==="RECONNECTING"?(i=32795,s="signal reconnected"):(i=32791,s="signal connected");break;case"DISCONNECTED":e==="RECONNECTING"?(i=32796,s="signal reconnect fail"):(i=32790,s="signal disconnected");break;case"RECONNECTING":i=32794,s="signal reconnecting";break}i&&s&&le(r.userId,{eventId:i,eventDesc:s})});_.on(m.PEER_CONNECTION_STATE_CHANGED,({room:r,prevState:e,state:t,remoteUserId:i})=>{let s=!!i,o=s?"downlink":"uplink",n,a;switch(t){case"CONNECTED":e==="RECONNECTING"?(n=s?32801:32798,a=`${o} reconnected`):(n=s?32793:32792,a=`${o} connected`);break;case"DISCONNECTED":e==="RECONNECTING"&&(n=s?32802:32799,a=`${o} reconnect fail`);break;case"RECONNECTING":n=s?32800:32797,a=`${o} reconnecting`;break}n&&a&&le(r.userId,{eventId:n,eventDesc:a,remoteUserId:i})});function le(r,e){let t={...e,timestamp:Dr()};hr.has(r)?hr.get(r).push(t):hr.set(r,[t])}function Yu(r){if(hr.has(r)){let e=hr.get(r).map(t=>({uint32_event_id:t.eventId,uint64_date:t.timestamp,str_userid:t.remoteUserId,str_event_json:t.eventDesc}));return hr.delete(r),e}return[]}var zu=pe(ve(),1);var Yo=class extends zu.EventEmitter{constructor(t,i,s="userId"){super();this.mySelfId=t;this._log=i;this.key=s}userMap=new Map;remotePublishedUserMap=new Map;getPublishedUser(t){return this.remotePublishedUserMap.get(t)}addUser(t){let i=t[this.key],{userId:s,tinyId:o,role:n}=t;if(this.userMap.has(i))return;let a={userId:s,tinyId:o,role:n===20?"anchor":"audience"};this.userMap.set(i,a),this.emit("1",a)}deleteUser(t,i){let s=this.userMap.get(t);if(!s)return;let o=`peer leave [${t}]`;f(i)||(o+=`:${Da[i]}`),this._log.info(o);let n=this.remotePublishedUserMap.get(t);if(n){let a=n.muteState;n.flag=0,this.emit("5",n.userId),this.deleteRemotePublishedUser(t),this.emit("6",{prevMuteState:a,muteState:n.muteState,flag:0})}this.userMap.delete(t),this.emit("2",s.userId)}setUserList(t){this.userMap.forEach(i=>{t.findIndex(s=>s[this.key]===i[this.key])<0&&this.deleteUser(i[this.key],0)}),t.forEach(i=>{!this.userMap.has(i[this.key])&&i[this.key]!==this.mySelfId&&this.addUser(i)})}addRemotePublishedUser(t){this.remotePublishedUserMap.has(t[this.key])||this.remotePublishedUserMap.set(t[this.key],t)}deleteRemotePublishedUser(t){!this.remotePublishedUserMap.has(t)||this.remotePublishedUserMap.delete(t)}setRemotePublishedUserList(t){this.remotePublishedUserMap.forEach(i=>{let s=i[this.key];if(t.findIndex(o=>o[this.key]===i[this.key])<0){this._log.info(`remote [${s}] unpublish`);let o=i.muteState;i.flag=0,this.emit("5",i.userId),this.deleteRemotePublishedUser(s),this.emit("6",{prevMuteState:o,muteState:i.muteState,flag:0})}}),t.forEach(i=>{let s=i[this.key];if(s===this.mySelfId)return;let{flag:o,userId:n,tinyId:a}=i,c=Kt(o,n),d=this.remotePublishedUserMap.get(s)?.muteState;if(d){let u=this.remotePublishedUserMap.get(s);u&&u.flag!==o&&(u.flag=o,this._log.info(`remote publish updated: ${JSON.stringify(u.muteState)}`),this.emit("6",{prevMuteState:d,muteState:c,flag:o}))}else this._log.info(`remote publish. state: ${JSON.stringify(c)}`),this.addUser({userId:n,tinyId:a,role:20}),this.emit("3",i),this.emit("6",{prevMuteState:Kt(0,n),muteState:c,flag:o})})}clear(){this.userMap.clear(),this.remotePublishedUserMap.clear()}};function Xu({timesInSecond:r,maxSizeInSecond:e,getSize:t}){return F((i,s)=>{let o=new Map;return _.on(m.ROOM_DESTROY,({room:n})=>o.delete(n)),function(...n){let a=o.get(this);if(a||(a={callCountInSecond:0,timestamp:0,totalSizeInSecond:0},o.set(this,a)),a.timestamp===0?a.timestamp=Date.now():Date.now()-a.timestamp>1e3&&(a.timestamp=Date.now(),a.callCountInSecond=0,a.totalSizeInSecond=0),t&&(a.totalSizeInSecond+=t(...n)),a.timestamp!==0&&Date.now()-a.timestamp<1e3&&(a.callCountInSecond>=r||a.totalSizeInSecond>e))throw new A({code:E.INVALID_OPERATION,message:v({key:b.CALL_FREQUENCY_LIMIT,data:{isTimes:a.callCountInSecond>=r,isSize:a.totalSizeInSecond>e,name:s,timesInSecond:r,maxSizeInSecond:e}})});a.callCountInSecond++,i.call(this,...n)}})}var qu=!0,Qu=function(){qu&&(qu=!1,I.getLogLevel()!==5&&(console.info("******************************************************************************"),console.info("*   TRTC Web SDK"),console.info(`*   API Document: ${ct}/en/index.html`),console.info(`*   Changelog: ${ct}/en/tutorial-01-info-changelog.html`),console.info("*   Report issues: https://github.com/LiteAVSDK/TRTC_Web/issues"),console.info("******************************************************************************")),I.info("TRTC Web SDK Version:",Se),I.info("UA:",navigator.userAgent),I.info(`URL: ${location.href}${self.frameElement?.tagName==="IFRAME"?" in iframe":""}`),Gr().then(r=>{if(r){let e=`UAData: ${r.platform}/${r.platformVersion}`;r.architecture&&r.bitness&&(e+=` ${r.architecture}/${r.bitness}`),r.mobile&&(e+=" mobile"),r.model&&(e+=` model: ${r.model}`),r.fullVersionList&&(e+=` ${r.fullVersionList.filter(t=>t.brand!=="Not/A)Brand").map(t=>`${t.brand}/${t.version}`).join(",")}`),I.info(e)}}))};var fs={SCENE_LIVE:"live",SCENE_RTC:"rtc",ROLE_ANCHOR:"anchor",ROLE_AUDIENCE:"audience",STREAM_TYPE_MAIN:"main",STREAM_TYPE_SUB:"sub",AUDIO_PROFILE_STANDARD:"standard",AUDIO_PROFILE_STANDARD_STEREO:"standard-stereo",AUDIO_PROFILE_HIGH:"high",AUDIO_PROFILE_HIGH_STEREO:"high-stereo",QOS_PREFERENCE_SMOOTH:"smooth",QOS_PREFERENCE_CLEAR:"clear"};var P={INVALID_PARAMETER:5e3,INVALID_OPERATION:5100,ENV_NOT_SUPPORTED:5200,DEVICE_ERROR:5300,SERVER_ERROR:5400,OPERATION_FAILED:5500,OPERATION_ABORT:5998,UNKNOWN_ERROR:5999},St=(T=>(T[T.INVALID_PARAMETER=5e3]="INVALID_PARAMETER",T[T.INVALID_PARAMETER_REQUIRED=5001]="INVALID_PARAMETER_REQUIRED",T[T.INVALID_PARAMETER_TYPE=5002]="INVALID_PARAMETER_TYPE",T[T.INVALID_PARAMETER_EMPTY=5003]="INVALID_PARAMETER_EMPTY",T[T.INVALID_PARAMETER_INSTANCE=5004]="INVALID_PARAMETER_INSTANCE",T[T.INVALID_PARAMETER_RANGE=5005]="INVALID_PARAMETER_RANGE",T[T.INVALID_PARAMETER_LESS_THAN_ZERO=5006]="INVALID_PARAMETER_LESS_THAN_ZERO",T[T.INVALID_PARAMETER_MIN=5007]="INVALID_PARAMETER_MIN",T[T.INVALID_PARAMETER_MAX=5008]="INVALID_PARAMETER_MAX",T[T.INVALID_ELEMENT_ID=5009]="INVALID_ELEMENT_ID",T[T.INVALID_ELEMENT_ID_TYPE=5010]="INVALID_ELEMENT_ID_TYPE",T[T.INVALID_STREAM_ID=5011]="INVALID_STREAM_ID",T[T.INVALID_ROOM_ID_STRING=5012]="INVALID_ROOM_ID_STRING",T[T.INVALID_ROOM_ID_INTEGER=5013]="INVALID_ROOM_ID_INTEGER",T[T.INVALID_STREAM_TYPE=5014]="INVALID_STREAM_TYPE",T[T.INVALID_ROOM_ID_REQUIED=5015]="INVALID_ROOM_ID_REQUIED",T[T.INVALID_ROOM_ID_INTEGER_STRING=5016]="INVALID_ROOM_ID_INTEGER_STRING",T[T.INVALID_OPERATION=5100]="INVALID_OPERATION",T[T.INVALID_OPERATION_NOT_JOINED=5101]="INVALID_OPERATION_NOT_JOINED",T[T.INVALID_OPERATION_REMOTE_USER_NOT_EXIST=5102]="INVALID_OPERATION_REMOTE_USER_NOT_EXIST",T[T.INVALID_OPERATION_STREAM_TYPE_NOT_EXIST=5103]="INVALID_OPERATION_STREAM_TYPE_NOT_EXIST",T[T.INVALID_OPERATION_REPEAT_CALL=5104]="INVALID_OPERATION_REPEAT_CALL",T[T.INVALID_OPERATION_NEED_VIDEO=5105]="INVALID_OPERATION_NEED_VIDEO",T[T.INVALID_OPERATION_NEED_AUDIO=5106]="INVALID_OPERATION_NEED_AUDIO",T[T.ENV_NOT_SUPPORTED=5200]="ENV_NOT_SUPPORTED",T[T.NOT_SUPPORTED_HTTP=5201]="NOT_SUPPORTED_HTTP",T[T.NOT_SUPPORTED_WEBRTC=5202]="NOT_SUPPORTED_WEBRTC",T[T.NOT_SUPPORTED_H264_ENCODE=5203]="NOT_SUPPORTED_H264_ENCODE",T[T.NOT_SUPPORTED_H264_DECODE=5204]="NOT_SUPPORTED_H264_DECODE",T[T.NOT_SUPPORTED_SCREEN_SHARE=5205]="NOT_SUPPORTED_SCREEN_SHARE",T[T.NOT_SUPPORTED_SMALL_VIDEO=5206]="NOT_SUPPORTED_SMALL_VIDEO",T[T.NOT_SUPPORTED_SEI=5207]="NOT_SUPPORTED_SEI",T[T.NOT_SUPPORTED_WEBGL=5208]="NOT_SUPPORTED_WEBGL",T[T.NOT_SUPPORTED_CHROME_VERSION=5209]="NOT_SUPPORTED_CHROME_VERSION",T[T.DEVICE_ERROR=5300]="DEVICE_ERROR",T[T.DEVICE_NOT_FOUND_ERROR=5301]="DEVICE_NOT_FOUND_ERROR",T[T.DEVICE_NOT_ALLOWED_ERROR=5302]="DEVICE_NOT_ALLOWED_ERROR",T[T.DEVICE_NOT_READABLE_ERROR=5303]="DEVICE_NOT_READABLE_ERROR",T[T.DEVICE_OVERCONSTRAINED_ERROR=5304]="DEVICE_OVERCONSTRAINED_ERROR",T[T.DEVICE_INVALID_STATE_ERROR=5305]="DEVICE_INVALID_STATE_ERROR",T[T.DEVICE_SECURITY_ERROR=5306]="DEVICE_SECURITY_ERROR",T[T.DEVICE_ABORT_ERROR=5307]="DEVICE_ABORT_ERROR",T[T.CAMERA_RECOVER_FAILED=5308]="CAMERA_RECOVER_FAILED",T[T.MICROPHONE_RECOVER_FAILED=5309]="MICROPHONE_RECOVER_FAILED",T[T.SERVER_ERROR=5400]="SERVER_ERROR",T[T.NEED_TO_BUY=5401]="NEED_TO_BUY",T[T.ACCOUNT_NO_MONEY=-100013]="ACCOUNT_NO_MONEY",T[T.OPERATION_FAILED=5500]="OPERATION_FAILED",T[T.FIREWALL_RESTRICTION=5501]="FIREWALL_RESTRICTION",T[T.REJOIN_FAILED=5502]="REJOIN_FAILED",T[T.EVENT_HANDLER_ERROR=5503]="EVENT_HANDLER_ERROR",T[T.VIDEO_CONTEXT_ERROR=5504]="VIDEO_CONTEXT_ERROR",T[T.OPERATION_ABORT=5998]="OPERATION_ABORT",T[T.UNKNOWN_ERROR=5999]="UNKNOWN_ERROR",T))(St||{});function el({code:r,params:e,enableDocLink:t=!1}){let i="",s,o=St[r];try{s=Zu[o]}catch{s=Zu.UNKNOWN_ERROR}return W(s)?i=s(e):j(s)&&(i=s),t&&(i+=" doc:"),i}var Zu={...ue,INVALID_PARAMETER({fnName:r}){return`the parameters of the '${r}' you called does not meet the requirements, please check the API documentation.`},INVALID_PARAMETER_REQUIRED({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' is a required param when calling ${t}(), received: ${i}.`},INVALID_PARAMETER_TYPE({key:r,rule:e,fnName:t,value:i}){let s=`${r||e.name}`,o="";return Array.isArray(e.type)?o=e.type.join("|"):o=e.type,`'${s}' must be type of ${o} when calling ${t}(), received type: ${te(i)}.`},INVALID_PARAMETER_EMPTY({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' cannot be '${i}' when calling ${t}().`},INVALID_PARAMETER_INSTANCE({key:r,rule:e,fnName:t,value:i}){let s=`${r||e.name}`,o=`${e.instanceOf.name||e.instanceOf}`;return`'${s}' must be instanceof ${o} when calling ${t}(), received type: ${te(i)}.`},INVALID_PARAMETER_RANGE({key:r,rule:e,fnName:t,value:i}){return`'${r||e.name}' must be one of ${e.values.join("|")} when calling ${t}(), received: ${i}.`},INVALID_PARAMETER_LESS_THAN_ZERO({key:r,rule:e,fnName:t}){return`'${r||e.name}' cannot be less than 0 when calling ${t}().`},INVALID_PARAMETER_MIN({key:r,rule:e,value:t}){return`the min value of ${r||e.name} is ${e.min}, received: ${t}.`},INVALID_PARAMETER_MAX({key:r,rule:e,value:t}){return`the max value of ${r||e.name} is ${e.max}, received: ${t}.`},INVALID_ELEMENT_ID({key:r,fnName:e}){return`'${r}' is not found in the document object when calling ${e}().`},INVALID_ELEMENT_ID_TYPE({key:r,fnName:e,type:t}){return`the element corresponding to '${r}' must be instanceof HTMLElement when calling ${e}(), received: ${t}.`},INVALID_STREAM_ID({key:r}){return`'${r}' can only consist of uppercase and lowercase english letters (a-zA-Z), numbers (0-9), hyphens and underscores.`},INVALID_ROOM_ID_STRING({key:r}){return`'${r}' must be a valid string.`},INVALID_ROOM_ID_INTEGER({key:r}){return`'${r}' must be an integer between [1, 4294967294].`},INVALID_ROOM_ID_INTEGER_STRING({key:r}){return`'${r}' must be an integer but go a string, use 'parseInt' to convert it or use 'strRoomId' instead.`},INVALID_ROOM_ID_REQUIED(){return"at least one of 'roomId'(between [1, 4294967294]) and 'strRoomId'(not empty) is required."},INVALID_STREAM_TYPE:({fnName:r})=>`'streamType' is required when 'userId' is not '*', calling ${r}()`,INVALID_IMAGE_URL:"The 'src' param must be filled in when the background type is image.",INVALID_OPERATION({fnName:r}){return`the API '${r}' you called does not meet the requirements, please check the API documentation.`},INVALID_OPERATION_NOT_JOINED({fnName:r}){return`cannot ${r} because you are not enter room yet.`},INVALID_OPERATION_REMOTE_USER_NOT_EXIST({fnName:r,value:e}){return`cannot ${r} because remote user(userId: ${e.userId}) does not publishing stream.`},INVALID_OPERATION_STREAM_TYPE_NOT_EXIST({fnName:r,value:e}){return`cannot ${r} because remote user(userId: ${e.userId}) does not publishing ${e.streamType} video.`},INVALID_OPERATION_REPEAT_CALL({fnName:r}){return`you are already ${r}(), cannot repeated call '${r}'.`},INVALID_OPERATION_NEED_VIDEO({fnName:r}){return`cannot call '${r}' because the camera is not turned on.`},INVALID_OPERATION_NEED_AUDIO({fnName:r}){return`cannot call '${r}' because the microphone is not turned on.`},ENV_NOT_SUPPORTED({fnName:r}){return`the current browser does not support the capability of the function '${r}' you are calling, please check the API documentation.`},NOT_SUPPORTED_WEBRTC:"the current browser does not support WebRTC capability, please check the SDK documentation.",NOT_SUPPORTED_H264_ENCODE:"this browser does not support H264 encode.",NOT_SUPPORTED_H264_DECODE:"this browser does not support H264 decode.",NOT_SUPPORTED_SCREEN_SHARE:"this browser does not support screen share, please check the browser version.",NOT_SUPPORTED_SMALL_VIDEO:"this browser does not support small video, please check the browser version.",NOT_SUPPORTED_SEI:"this browser does not support SEI, please check the browser version.",NOT_SUPPORTED_WEBGL:"this browser does not support WebGL, please check the browser version.",NOT_SUPPORTED_CHROME_VERSION({fnName:r}){return`cannot call ${r} because the browser version is too low, please upgrade to the latest version`},DEVICE_ERROR({fnName:r,error:e}){return`'${r}' got device exception${e?`, error: ${e.toString()}.`:"."}`},DEVICE_NOT_FOUND_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`NotFoundError, no ${e} detected, please check your device and the configuration on '${r}'${t?`, error: ${t.toString()}.`:"."}`},DEVICE_NOT_ALLOWED_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`NotAllowedError, you have disabled ${e} access, please allow the current application to use the ${e}${t?`, error: ${t.toString()}.`:"."}`},DEVICE_NOT_READABLE_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`NotReadableError, the ${e} maybe in use by another APP, please check if the device is pre-occupied by another APP.`},DEVICE_OVERCONSTRAINED_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`OverconstrainedError, the device ID is incorrect, please check whether the device ID passed in is correct${t?`, error: ${t.toString()}.`:"."}`},DEVICE_INVALID_STATE_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`InvalidStateError, after the user clicks and interacts with the page, turn on the ${e}${t?`, error: ${t.toString()}.`:"."}`},DEVICE_SECURITY_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`SecurityError, check whether the system security policy restricts the use of the ${e}, and it is recommended to turn on the ${e} after the user interacts with the page${t?`, error: ${t.toString()}.`:"."}`},DEVICE_ABORT_ERROR({fnName:r,deviceType:e=Ci(r),error:t}){return`AbortError, an unknown exception in the system makes the device unusable, recommended to change the device or browser and re-check whether the device is normal${t?` error: ${t.toString()}.`:"."}`},CAMERA_RECOVER_FAILED({error:r}){return`camera recover capture failed ${r?.name||""}: ${r?.originMessage||r?.message}`},MICROPHONE_RECOVER_FAILED({error:r}){return`microphone recover capture failed ${r?.name||""}: ${r?.originMessage||r?.message}`},OPERATION_FAILED({fnName:r,error:e}){return`'${r}' failed, reason: ${e?.toString()}`},FIREWALL_RESTRICTION(){return"media connection failure due to firewall restrictions, please try to change your network."},EVENT_HANDLER_ERROR({eventName:r}){return`an error was caught on trtc.on('${r}', handler), please check your code on 'handler'.`},VIDEO_CONTEXT_ERROR({reason:r,error:e}){return`video context error ${r} ${e?.name||""} ${e?.message||""}`},SERVER_ERROR({fnName:r,error:e}){return`'${r}' got server error: ${e?.toString()}, please check the SDK documentation.`},NEED_TO_BUY({value:r,url:e}){return`You need to buy packages for ${r}. Refer to: ${e}`},ACCOUNT_NO_MONEY:({fnParams:r})=>`your TRTC account run out of credit, please recharge.${r.sdkAppId?` SDKAppId: ${r.sdkAppId}`:""}`,OPERATION_ABORT({fnName:r}){return`'${r}' abort`},UNKNOWN_ERROR({fnName:r,error:e}){return`'${r}' throw unknown exception${e?`, error: ${e.toString()}.`:"."}`}};function Ci(r){if(!r)return"camera";let e=r.toLowerCase();return e.includes("screen")?"screen share":e.includes("audio")?"microphone":"camera"}var yi=class extends Error{name="RtcError";code;extraCode;functionName;message;handler;originError;constructor({code:e,extraCode:t,message:i="",messageParams:s,fnName:o="",originError:n}){let a;i?a=i:a=el({code:t||e,params:{fnName:o,error:n,...s}}),super(a),this.name=St[e],this.code=e,this.extraCode=t,this.functionName=o,this.originError=n,this.message=a,this.extraCode===5302&&this.originError?.message.includes("system")&&(this.handler=()=>{let c={startLocalVideo:"Camera",startLocalAudio:"Microphone",startScreenShare:"ScreenCapture"},d={startLocalVideo:"webcam",startLocalAudio:"microphone"},u=document.createElement("a");Ct?u.href=`ms-settings:privacy-${d[this.functionName]}`:je&&(u.href=`x-apple.systempreferences:com.apple.preference.security?Privacy_${c[this.functionName]}`),u.href.length>0&&u.click()})}static convertFrom(e,t,i){let s=e;if(e instanceof A){let{stack:o}=e,n={code:P.UNKNOWN_ERROR,fnName:t,originError:e};switch(e.getCode()){case E.INVALID_PARAMETER:n.code=P.INVALID_PARAMETER;break;case E.INVALID_OPERATION:n.code=P.INVALID_OPERATION;break;case E.NOT_SUPPORTED:case E.NOT_SUPPORTED_H264:n.code=P.ENV_NOT_SUPPORTED,e.getCode()===E.NOT_SUPPORTED_H264&&(n.extraCode=e.message.includes(ue.NOT_SUPPORTED_H264ENCODE)?5203:5204);break;case E.DEVICE_NOT_FOUND:case E.DEVICE_AUTO_RECOVER_FAILED:n.code=P.DEVICE_ERROR;break;case E.JOIN_ROOM_FAILED:n.messageParams={fnParams:i};case E.SERVER_TIMEOUT:case E.SWITCH_ROLE_FAILED:n.code=P.SERVER_ERROR,n.extraCode=e.getExtraCode();break;case E.API_CALL_ABORTED:n.code=P.OPERATION_ABORT;break;case E.INITIALIZE_FAILED:n.code=5300,n.extraCode=dm(e.name);break;case E.UNKNOWN:break;default:n.code=P.OPERATION_FAILED}s=new yi(n),o&&(s.stack+=o.substr(o.indexOf(`
`)))}else{if(e instanceof yi)return e;s=new yi({code:P.UNKNOWN_ERROR,fnName:t,originError:e})}return s}};function dm(r){let e;switch(r){case"NotFoundError":e=5301;break;case"NotAllowedError":e=5302;break;case"NotReadableError":e=5303;break;case"OverconstrainedError":e=5304;break;case"InvalidStateError":e=5305;break;case"SecurityError":e=5306;break;case"AbortError":e=5307;break;default:e=5300}return e}var O=yi;var tl={type:"object",properties:{cameraId:{type:"string"},useFrontCamera:{type:"boolean"},fillMode:{type:"string",values:["contain","cover","fill"]},mirror:{type:"boolean"},small:{properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},videoTrack:{instanceOf:MediaStreamTrack}}},il={type:"object",properties:{systemAudio:{type:"boolean"},fillMode:{type:"string",values:["contain","cover","fill"]},profile:{type:["string","object"],properties:{width:{type:"number"},height:{type:"number"},frameRate:{type:"number"},bitrate:{type:"number"}}},videoTrack:{instanceOf:MediaStreamTrack},audioTrack:{instanceOf:MediaStreamTrack}}},pr={type:["string",HTMLElement,null,"array"],arrayItem:{instanceOf:HTMLElement},validate(r,e,t){if(j(r)){let i=document.getElementById(r);if(!i)throw new O({code:P.INVALID_PARAMETER,extraCode:5009,fnName:t,messageParams:{key:e}});if(!(i instanceof HTMLElement))throw new O({code:P.INVALID_PARAMETER,extraCode:5010,fnName:t,messageParams:{key:e,type:te(i)}})}}},rl={name:"userId",required:!0,type:"string"},sl={type:"object",properties:{microphoneId:{type:"string"},audioTrack:{instanceOf:MediaStreamTrack},captureVolume:{type:"number",min:0},earMonitorVolume:{type:"number",min:0,max:100},echoCancellation:{type:"boolean"},autoGainControl:{type:"boolean"},noiseSuppression:{type:"boolean"}}};function Xo(r,e){if(!r)throw new O({code:P.INVALID_OPERATION,extraCode:5101,fnName:e})}function ol(r,e,t){if(!r)throw new O({code:P.INVALID_OPERATION,extraCode:5102,fnName:e,messageParams:{value:t}})}var OA={type:"number",notLessThanZero:!0},um={create:[{name:"RoomConfig",instanceOf:Function},{name:"CreateConfig",type:"object",properties:{plugins:{type:"array",arrayItem:{instanceOf:Function}}}}],enterRoom:{name:"EnterRoomConfig",type:"object",required:!0,validate(r,e,t){if(this._room.isJoined)throw new O({code:P.INVALID_OPERATION,extraCode:5104,fnName:t});if(r.roomId){if(j(r.roomId))throw new O({code:P.INVALID_PARAMETER,extraCode:5016,fnName:t,messageParams:{key:e}});if(!(/^[1-9]\d*$/.test(String(r.roomId))&&r.roomId<4294967295))throw new O({code:P.INVALID_PARAMETER,extraCode:5013,fnName:t,messageParams:{key:e}})}else if(r.strRoomId){if(!/^[A-Za-z\d\s!#$%&()+\-:;<=.>?@[\]^_{}|~,]{1,64}$/.test(r.strRoomId))throw new O({code:P.INVALID_PARAMETER,extraCode:5012,fnName:t,messageParams:{key:e}})}else throw new O({code:P.INVALID_PARAMETER,extraCode:5015,fnName:t})},properties:{sdkAppId:{required:!0,type:"number",allowEmpty:!1},userId:{required:!0,type:"string",allowEmpty:!1},userSig:{required:!0,type:"string",allowEmpty:!1},scene:{type:"string",values:["live","rtc"]},role:{type:"string",values:["audience","anchor"]},roomId:{type:["string","number"]},strRoomId:{type:"string"},proxy:{type:["object","string"],properties:{websocketProxy:{type:"string"},turnServer:{type:["object","array"],properties:{url:{required:!0,type:"string"},username:{type:"string"},credential:{type:"string"},credentialType:{type:"string",values:["password"]}}},loggerProxy:{type:"string"},webtransportProxy:{type:"string"}}},enableAutoPlayDialog:{type:"boolean"},userDefineRecordId:{type:"string"}}},startLocalVideo:{name:"LocalVideoConfig",type:"object",properties:{view:pr,publish:{type:"boolean"},option:tl},validate(r){if(!r?.option?.videoTrack&&Yt())throw new O({code:P.ENV_NOT_SUPPORTED,extraCode:5201})}},updateLocalVideo:{name:"updateLocalVideoConfig",type:"object",required:!0,properties:{view:{...pr,required:!1},publish:{type:"boolean"},mute:{type:"boolean"},option:tl}},startLocalAudio:{name:"LocalAudioConfig",type:"object",properties:{publish:{type:"boolean"},option:sl},validate(r){if(!r?.option?.audioTrack&&Yt())throw new O({code:P.ENV_NOT_SUPPORTED,extraCode:5201})}},updateLocalAudio:{name:"updateLocalAudioConfig",type:"object",required:!0,properties:{publish:{type:"boolean"},mute:{type:"boolean"},option:sl}},startScreenShare:{name:"ScreenShareConfig",type:"object",properties:{view:pr,publish:{type:"boolean"},option:il},validate(r,e,t,i,s){if(!r?.option?.videoTrack&&Yt())throw new O({code:P.ENV_NOT_SUPPORTED,extraCode:5201});if(!is())throw new O({code:P.ENV_NOT_SUPPORTED,fnName:t,extraCode:5205})}},updateScreenShare:{name:"updateScreenShareConfig",type:"object",required:!0,properties:{view:pr,publish:{type:"boolean"},option:il}},muteRemoteAudio:[rl,{name:"mute",required:!0,type:"boolean"}],setRemoteAudioVolume:[rl,{name:"volume",required:!0,type:"number",min:0}],startRemoteVideo:{name:"startRemoteVideoConfig",type:"object",required:!0,properties:{view:pr,userId:{type:"string",required:!0},streamType:{values:["main","sub"],required:!0},option:{type:"object",properties:{fillMode:{type:"string",values:["contain","cover","fill"]},mirror:{type:"boolean"}}}},validate(r,e,t){Xo(this._room.isJoined,t);let i=this._room.remotePublishedUserMap.get(r.userId);if(ol(!!i,t,r),i&&(r.streamType==="main"&&!i.muteState.videoAvailable||r.streamType==="sub"&&!i.muteState.hasAuxiliary))throw new O({code:P.INVALID_OPERATION,extraCode:5103,fnName:t,messageParams:{value:r}})}},updateRemoteVideo:{name:"updateRemoteVideoConfig",type:"object",required:!0,properties:{view:{...pr,required:!1},userId:{type:"string",required:!0},streamType:{values:["main","sub"],required:!0},option:{type:"object",properties:{fillMode:{type:"string",values:["contain","cover","fill"]},mirror:{type:"boolean"}}}},validate(r,e,t){Xo(this._room.isJoined,t);let i=this._room.remotePublishedUserMap.get(r.userId);if(ol(!!i,t,r),i&&(r.streamType==="main"&&!i.muteState.videoAvailable||r.streamType==="sub"&&!i.muteState.hasAuxiliary))throw new O({code:P.INVALID_OPERATION,extraCode:5103,fnName:t,messageParams:{value:r}})}},stopRemoteVideo:{name:"stopRemoteVideoConfig",type:"object",required:!0,properties:{userId:{type:"string",required:!0},streamType:{values:["main","sub"]}},validate(r,e,t){if(r.userId!=="*"&&f(r.streamType))throw new O({code:P.INVALID_PARAMETER,extraCode:5014,fnName:t})}},switchRole:{name:"role",required:!0,values:["anchor","audience"],validate(r,e,t){Xo(this._room.isJoined,t)}},enableAudioVolumeEvaluation:[{name:"interval",type:"number"},{name:"enableInBackground",type:"boolean"}],sendSEIMessage:[{name:"buffer",required:!0,instanceOf:ArrayBuffer,validate(r,e,t,i){if(!Ze)throw new O({code:P.ENV_NOT_SUPPORTED,fnName:t,extraCode:5207});if(!this._room.enableSEI)throw new O({code:P.INVALID_OPERATION,messageParams:{key:b.SEI_DISABLED}});if(r.byteLength>1e3)throw new O({code:P.INVALID_PARAMETER,messageParams:{key:b.SEI_OVERSIZE,data:r.byteLength}});if(r.byteLength===0)throw new O({code:P.INVALID_PARAMETER,messageParams:{key:b.SEI_EMPTY}});if(Xo(this._room.isJoined,t),!this._room.isMainStreamPublished)throw new O({code:P.INVALID_PARAMETER,messageParams:{key:b.SEI_BEFORE_PUBLISH}})}},{name:"options",type:"object",properties:{seiPayloadType:{type:"number",values:[5,243]}}}]},he={TRTC:um};var Te=class extends Error{};function lm(r,e){let t=[];return lt(t,r),lt(t,e),t}function hm(r){this._resolve=Promise.resolve(r)}function pm(r){this._reject=Promise.reject(r)}var mr=class{constructor(e,t){this.instance=e;this.group=t;let i=mr.instances.get(e);i?i.set(t,this):mr.instances.set(e,new Map([[t,this]]))}started=!1;ops=[];startSame=()=>!0;mergeUpdate=lm;static get(e,t){let i=mr.instances.get(e);return i&&i.get(t)||new mr(e,t)}action(e,t,i){let s=a=>(e===0?this.started=!0:e===3&&(this.started=!1),this.ops.shift(),this.currentOp?.action(),a),o=a=>{throw this.ops.shift(),e===0&&this.currentOp?.type===2&&this.ops.shift().reject(new Te("start failed")),this.currentOp?.action(),a},n={type:e,action:()=>t(...n.args).then(s,o),args:i,resolve:hm,reject:pm};try{switch(this.state){case 1:if(e===0)throw new Te("already started");break;case 4:if(e===2)throw new Te("not started");break;default:return this.cacheOp(n)}}catch(a){return Promise.reject(a)}return this.ops.push(n),n.promise=t(...n.args).then(s,o)}cacheOp(e){if(this.ops.length===1)switch(this.state){case 0:case 2:if(e.type===0)throw new Te("already start");break;case 3:switch(e.type){case 2:throw new Te("update not allowed when stopping");case 3:return this.currentOp.promise}break;default:throw new Te("unknown state")}else switch(e.type){case 3:switch(this.lastOpType){case 3:return this.lastOp.promise;default:let i=new Te("keep stop");if(this.ops.slice(1).forEach(s=>s.reject(i)),this.ops=this.ops.slice(0,1),this.state===3)return this.currentOp.promise}break;case 2:switch(this.lastOpType){case 2:return this.lastOp.args=this.mergeUpdate(this.lastOp.args,e.args),this.lastOp.promise;case 3:throw new Te("update not allowed after stop")}break;case 0:switch(this.lastOpType){case 2:throw new Te("start not allowed after update");case 0:throw new Te("duplicate start");case 3:if(this.startSame(this.currentOp.args,e.args))throw this.ops.pop().reject(new Te("keep start")),new Te("already start")}}e.promise=new Promise((i,s)=>{e._resolve?e._resolve.then(i):e.resolve=i,e._reject?e._reject.catch(s):e.reject=s});let{action:t}=e;return e.action=()=>t().then(e.resolve,e.reject),this.ops.push(e),e.promise}get lastOp(){return this.ops[this.ops.length-1]}get lastOpType(){return this.lastOp.type}get currentOp(){return this.ops[0]}get state(){return this.currentOp?this.currentOp.type:this.started?1:4}},Vt=mr;ae(Vt,"instances",new WeakMap);var qo=new WeakMap,Qo=(r,e)=>{if(e instanceof Te){let{stack:t}=e;e=new O({code:P.OPERATION_ABORT,message:`${r} abort: ${e.message}`,fnName:r}),t&&(e.stack+=t.substr(t.indexOf(`
`)))}throw e};function ti(r,e){return F((t,i)=>function(...s){let o=Vt.get(this,typeof r=="string"?r:r.call(this,...s));return e&&(o.startSame=e.bind(this)),o.action(0,t.bind(this),s).catch(Qo.bind(null,i))})}function bi(r,e){let{merge:t,debounce:i}=e||{};return F((s,o)=>function(...n){let a=Vt.get(this,typeof r=="string"?r:r.call(this,...n));if(t&&(a.mergeUpdate=t.bind(this)),i&&i.isNeedToDebounce.apply(this,n)){let{delay:c,getKey:d}=i;return new Promise((u,h)=>{let p=qo.get(this)?.get(d(...n));if(p){let{timeoutId:C,resolve:N}=p;clearTimeout(C),N()}let g=setTimeout(()=>{if(a.state===3||a.state===4)return u();a.action(2,s.bind(this),n).catch(Qo.bind(null,o)).then(u,h)},c);qo.has(this)?qo.get(this)?.set(d(...n),{timeoutId:g,resolve:u}):qo.set(this,new Map([[d(...n),{timeoutId:g,resolve:u}]]))})}return a.action(2,s.bind(this),n).catch(Qo.bind(null,o))})}function ii(r){return F((e,t)=>function(...i){return Vt.get(this,typeof r=="string"?r:r.call(this,...i)).action(3,e.bind(this),i).catch(Qo.bind(null,t))})}var M={ERROR:"error",AUTOPLAY_FAILED:"autoplay-failed",KICKED_OUT:"kicked-out",REMOTE_USER_ENTER:"remote-user-enter",REMOTE_USER_EXIT:"remote-user-exit",REMOTE_AUDIO_AVAILABLE:"remote-audio-available",REMOTE_AUDIO_UNAVAILABLE:"remote-audio-unavailable",REMOTE_VIDEO_AVAILABLE:"remote-video-available",REMOTE_VIDEO_UNAVAILABLE:"remote-video-unavailable",AUDIO_VOLUME:"audio-volume",NETWORK_QUALITY:"network-quality",CONNECTION_STATE_CHANGED:"connection-state-changed",AUDIO_PLAY_STATE_CHANGED:"audio-play-state-changed",VIDEO_PLAY_STATE_CHANGED:"video-play-state-changed",SCREEN_SHARE_STOPPED:"screen-share-stopped",DEVICE_CHANGED:"device-changed",PUBLISH_STATE_CHANGED:"publish-state-changed",STATISTICS:"statistics",SEI_MESSAGE:"sei-message",TRACK:"track"};function Zo(r){return r==="sub"?"auxiliary":r==="auxiliary"?"sub":"main"}function Ts(r){return r===fs.QOS_PREFERENCE_CLEAR?"detail":r===fs.QOS_PREFERENCE_SMOOTH?"motion":""}function en(r,e){return{room:r,innerEmitter:_,constants:Oa,environment:Ht,utils:be,eventLogger:Y,log:r.getLogger(),errorModule:e,clearStarted(t,i){let s=t.getAlias(),o=Vt.instances.get(r);if(!!o)if(i){let n=o.get(s+i);if(!n)return;n.started=!1}else o.forEach((n,a)=>{a.startsWith(s)&&(n.started=!1)})}}}var cl=(r,e)=>{let{emit:t}=r;return r.emit=(...i)=>{try{return t.apply(r,i)}catch(s){let o=v({key:b.CATCH_HANDLER_ERROR,data:{name:e,event:i[0]},addDocLink:!1});return I.warn(`${o}

${s.stack}`),!1}},r};var tn=new WeakMap;function dl(r,e){return F((t,i)=>function(...s){let o=tn.get(this)?.get(e(...s));o&&o>0&&clearTimeout(o);let n=window.setTimeout(()=>{t.apply(this,s)},r);tn.has(this)?tn.get(this)?.set(e(...s),n):tn.set(this,new Map([[e(...s),n]]))})}var ul="5.2.1";function Re(...r){return F((e,t)=>function(...i){try{sn.call(this,r,i,t,this._name)}catch(s){return Promise.reject(s)}return e.apply(this,i)})}function Jc(...r){return F((e,t)=>function(...i){try{sn.call(this,r,i,t,this._name)}catch(s){throw s}return e.apply(this,i)})}function sn(r,e,t,i){if(oe(r))for(let s=0;s<r.length;s++)rn.call(this,{rule:r[s],value:e[s],key:r[s].name,fnName:t,className:i});else rn.call(this,{rule:r,value:e[0],key:r.name,fnName:t,className:i})}function rn({rule:r,value:e,key:t,fnName:i,className:s}){function o(c){return{code:P.INVALID_PARAMETER,extraCode:c,fnName:i,messageParams:{key:t,rule:r,value:e}}}if(f(e)){if(r.required)throw new O(o(5001));if(f(r.defaultValue)){W(r.validate)&&r.validate.call(this,e,t,i,s,this);return}e=r.defaultValue}if(Array.isArray(r.type)){let c=!1;for(let d=0;d<r.type.length;d++)r.type[d]===null&&e===null&&(c=!0),W(r.type[d])&&e instanceof r.type[d]&&(c=!0),j(r.type[d])&&te(e)===r.type[d].toLowerCase()&&(c=!0);if(!c)throw new O({code:P.INVALID_PARAMETER,extraCode:5002,fnName:i,messageParams:{key:t,rule:{type:r.type.map(d=>Zr(d)?yo(d):j(d)?d:te(d))},value:e}})}else if(!f(r.type)&&te(e)!==r.type)throw new O(o(5002));if(r.allowEmpty===!1){let c=K(e)&&(e===0||Number.isNaN(e)),d=j(e)&&e.trim()==="";if(c||d)throw new O(o(5003))}if(r.notLessThanZero&&K(e)&&e<0)throw new O(o(5006));if(!f(r.min)&&K(e)&&e<r.min)throw new O(o(5007));if(!f(r.max)&&K(e)&&e>r.max)throw new O(o(5008));if(j(r.instanceOf)){if(!e||e._name!==r.instanceOf)throw new O(o(5004))}else if(W(r.instanceOf)&&!(e instanceof r.instanceOf))throw new O(o(5004));if(Array.isArray(r.values)&&!r.values.includes(e))throw new O(o(5005));let{properties:n}=r;Ue(n)&&Xe(e)&&Object.keys(n).forEach(c=>{rn.call(this,{rule:n[c],value:e&&e[c],key:`${c}`,fnName:i,className:s})});let{arrayItem:a}=r;Ue(a)&&oe(e)&&e.forEach((c,d)=>{rn.call(this,{rule:a,value:c,key:`${t}[${d}]`,fnName:i,className:s})}),W(r.validate)&&r.validate.call(this,e,t,i,s,this)}function ee(r={}){let{namePrefix:e=()=>"",getRemoteId:t=()=>""}=r;return F((i,s)=>function(...o){function n(h,p,g){if(g&&g.includes(h))return"hided";if(p===o||h in o)return p;try{return p instanceof HTMLElement?`id: ${p.id} type:${te(p)}`:(JSON.stringify(p),p)}catch{return`type:${te(p)}`}}let a=this._log||loggerManager,c=e.call(this,...o),u=c!==""?`${c}.${s}`:s;o.length>0?a.info(`${u}() ${JSON.stringify(o,(h,p)=>n(h,p,["userSig","privateMapKey"]))}`):a.info(`${u}()`);try{let h=i.apply(this,o),p=U();return Qr(h)?h.then(g=>(a.info(`${u}() success ${t.call(this,...o)}`),_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:u,cost:U()-p}),g)).catch(g=>{throw g=O.convertFrom.call(this,g,u,o.length===1?o[0]:o),a.error(`${u}() failed ${t.call(this,...o)} ${g} params: ${JSON.stringify(o,n)}`),_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:u,error:g}),g}):(_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:u,cost:U()-p}),h)}catch(h){throw h=O.convertFrom.call(this,h,u),a.error(`${u}() failed ${h} params: ${JSON.stringify(o,n)}`),_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:u,error:h}),h}})}var on=r=>F((e,t)=>async function(i,s){let o=this._plugins.get(i);if(!o){this._log.warn(`plugin ${i} is not found`);return}return sn.call(this,o.getValidateRule(r),[s],t,"TRTC"),e.call(this,o,s)});var nn=0,Es=class{constructor(e){this.core=e;nn=nn+1,this.log=e.log.createChild({id:`${this.getAlias()}${nn}`}),this.log.info(`[audioMixer] created id=${this.getAlias()}${nn}`),this.core=e}log;getName(){return"AudioMixer"}getAlias(){return"ax"}getGroup(e){return e?.id}getValidateRule(e){switch(e){case"start":return Es.startValidateRule;case"update":return Es.updateValidateRule;case"stop":return Es.stopValidateRule}}async start(e){let{room:t}=this.core;await t.audioManager.addMusicSource(e)}async update(e){let{room:t}=this.core;await t.audioManager.updateMusicSource(e)}async stop(e){let{room:t}=this.core;await t.audioManager.removeMusicSource(e)}},Ni=Es;ae(Ni,"startValidateRule",{name:"options",required:!0,type:"object",properties:{id:{type:"string",required:!0},url:{type:"string",required:!0},loop:{type:"boolean"},volume:{type:"number"}},validate(e,t,i){if(e.url!=="*"){let s=["mp3","ogg","wav","flac"],o=e.url.split(".").pop(),n=s.indexOf(o)>=0,a=e.url.startsWith("blob"),c=e.url.startsWith("data");if(!(n||a||c))throw new O({code:P.INVALID_PARAMETER,message:"start audioMixer plugin: music url is invalid, please check your file format.",fnName:i})}}}),ae(Ni,"updateValidateRule",{name:"options",required:!0,type:"object",properties:{id:{type:"string",required:!0},loop:{type:"boolean"},volume:{type:"number"},seekFrom:{type:"number"},operation:{type:"string",values:["pause","resume","stop"]}}}),ae(Ni,"stopValidateRule",{name:"options",type:"object",required:!0,properties:{id:{type:"string",required:!0}}});var an=0,gs=class{constructor(e){this.core=e;an=an+1,this.log=e.log.createChild({id:`${this.getAlias()}${an}`}),this.log.info(`[audioDenoiser] created id=${this.getAlias()}${an}`)}log;static startValidateRule(e){return{name:"options",required:!0,type:"object",properties:{assetsPath:{type:"string",required:!0},sdkAppId:{type:"number",required:!0},userId:{type:"string",required:!0},userSig:{type:"string",required:!0}},validate(t,i,s,o){if(!e.room.audioManager.hasAudioTrack)throw new O({code:P.INVALID_OPERATION,extraCode:5106,fnName:s})}}}getName(){return"AIDenoiser"}getAlias(){return"ad"}getGroup(e){return`AIDenoiser_${Date.now()}`}getValidateRule(e){switch(e){case"start":return gs.startValidateRule(this.core);case"update":return gs.updateValidateRule;case"stop":return gs.stopValidateRule}}async start(e){let{room:t}=this.core,{assetsPath:i,sdkAppId:s,userId:o,userSig:n}=e;if(i&&!t.audioManager.isDenoiserInit)try{await t.audioManager.initDenoiser({assetsPath:i,sdkAppId:s,userId:o,userSig:n})}catch(a){if(a.message)throw new O({code:P.INVALID_PARAMETER,message:a.message})}await t.audioManager.enableDenoiser(e),t.sendAbilityStatus({ai_denoise:1})}async update(e){}async stop(e){let{room:t}=this.core;await t.audioManager.disableDenoiser()}},_r=gs;ae(_r,"updateValidateRule",{type:"object"}),ae(_r,"stopValidateRule",{type:"object"});var ll=0,Yc=new Set,Pe=null;_a(ul);var Ss=class extends pl.EventEmitter{_room;_eventListened=new Set;_localVideoTrack=null;_localAudioTrack=null;_localScreenTrack=null;_localScreenAudioTrack=null;_localVideoConfig=null;_localScreenConfig=null;_localAudioConfig=null;_remoteVideoConfigMap=new Map;_remoteAudioConfigMap=new Map;_remoteAudioMuteMap=new Map;_mediaTrackMap=new WeakMap;_log=I.createLogger({id:`t${++ll}`});_plugins=new Map;_networkQuality=null;static create(e){}static _create(e,t){Qu();let i=new Ss(e,t||{});return Yc.add(i),Pe?i.emit(M.DEVICE_CHANGED,{type:"speaker",action:"active",device:Pe}):nr().then(s=>{s[0]&&(Pe=s[0],i.emit(M.DEVICE_CHANGED,{type:"speaker",action:"active",device:s[0]}))}),i}constructor(e,t){super(),this._room=new e({logger:this._log,frameWorkType:Ss.frameWorkType,...t});let i={RtcError:O,ErrorCode:P,ErrorCodeDictionary:St};t.plugins&&t.plugins.forEach(n=>{let a=new n(en(this._room,i));this._plugins.set(a.getName(),a)});let s=new Ni(en(this._room,i));this._plugins.set(s.getName(),s);let o=new _r(en(this._room,i));this._plugins.set(o.getName(),o),this._room.on("audio-volume",n=>{!n.find(a=>a.userId==="")&&this._localAudioTrack&&n.push({userId:"",volume:Math.floor(this._localAudioTrack.getAudioLevel()*100)}),this.emit(M.AUDIO_VOLUME,{result:n.sort((a,c)=>c.volume-a.volume)})}),this._room.videoManager.on("error",({reason:n,error:a})=>{this._log.error(new O({code:P.OPERATION_FAILED,extraCode:5504,messageParams:{reason:n,error:a}}))}),this.on(M.REMOTE_AUDIO_UNAVAILABLE,({userId:n})=>{this._stopRemoteAudio({userId:n},!1).catch(()=>{})}),this.on(M.REMOTE_VIDEO_UNAVAILABLE,({userId:n,streamType:a})=>{this._stopRemoteVideo({userId:n,streamType:a},!1).catch(()=>{})}),_e(re,re).add("audioInputAdded",n=>{this.emit(M.DEVICE_CHANGED,{type:"microphone",action:"add",device:n})}).add("audioInputRemoved",n=>{this.emit(M.DEVICE_CHANGED,{type:"microphone",action:"remove",device:n})}).add("videoInputAdded",n=>{this.emit(M.DEVICE_CHANGED,{type:"camera",action:"add",device:n})}).add("videoInputRemoved",n=>{this.emit(M.DEVICE_CHANGED,{type:"camera",action:"remove",device:n})}).add("audioOutputAdded",async n=>{if(this.emit(M.DEVICE_CHANGED,{type:"speaker",action:"add",device:n}),Pe&&Pe.deviceId===Xr){let a=(await nr()).find(c=>c.deviceId===Xr);a&&Pe.groupId!==a.groupId&&(Pe=a,this.emit(M.DEVICE_CHANGED,{type:"speaker",action:"active",device:a}))}}).add("audioOutputRemoved",async n=>{this.emit(M.DEVICE_CHANGED,{type:"speaker",action:"remove",device:n});let a=(await nr())[0];a&&Pe&&(Pe.deviceId===n.deviceId||Pe.deviceId===Xr&&Pe.groupId!==a.groupId)&&(Pe=a,this.emit(M.DEVICE_CHANGED,{type:"speaker",action:"active",device:a}))}),cl(this,"trtc")}async enterRoom(e){let{scene:t="rtc",enableAutoPlayDialog:i=!0,autoReceiveAudio:s=!0,autoReceiveVideo:o=!0}=e;e.proxy&&(this._room.setProxyServer(e.proxy),!j(e.proxy)&&e.proxy.turnServer&&this._room.setTurnServer?.(e.proxy.turnServer,e.proxy.iceTransportPolicy)),this._room.enableAutoPlayDialog=i,this._room.autoReceiveAudio=s,this._room.autoReceiveVideo=o,Z(e.enableHWEncoder)&&(this._room.enableHWEncoder=e.enableHWEncoder);let n={sdkAppId:e.sdkAppId,userId:e.userId,userSig:e.userSig,privateMapKey:e.privateMapKey||null,role:e.role==="audience"?21:20,roomId:e.roomId||0,strRoomId:e.strRoomId||"",businessInfo:e.businessInfo||null,streamId:null,userDefineRecordId:e.userDefineRecordId||null,frameWorkType:e.frameWorkType,component:e.component,language:e.language};e.strRoomId&&!e.roomId&&(this._room.useStringRoomId=!0),_e(this,this._room).add("peer-join",a=>{let{userId:c}=a;this.emit(M.REMOTE_USER_ENTER,{userId:c})}).add("peer-leave",a=>{this.emit(M.REMOTE_USER_EXIT,{userId:a})}).add("banned",a=>{this._exitRoom().then(()=>{this.emit(M.KICKED_OUT,{reason:a.reason})})}).add("error",a=>{this._exitRoom().then(()=>{this.emit(M.ERROR,O.convertFrom(a))})}).add("signal-connection-state-changed",a=>{this.emit(M.CONNECTION_STATE_CHANGED,a)}).add("network-quality",a=>{this._networkQuality=a,this.emit(M.NETWORK_QUALITY,a)}).add("remote-published",a=>{[a.remoteAudioTrack,a.remoteVideoTrack,a.remoteAuxiliaryTrack].forEach(d=>{_e(d,d).add("player-state-changed",u=>{let h={...u,userId:a.userId};d.kind===l.VIDEO&&(h.streamType=Zo(d.streamType)),this.emit(d.kind===l.AUDIO?M.AUDIO_PLAY_STATE_CHANGED:M.VIDEO_PLAY_STATE_CHANGED,h)}).add("error",u=>{u.getCode()===E.PLAY_NOT_ALLOWED&&this.emit(M.AUTOPLAY_FAILED,{userId:d.userId})})})}).add("remote-unpublished",a=>{[a.remoteAudioTrack,a.remoteVideoTrack,a.remoteAuxiliaryTrack].forEach(d=>{ie(d)})}).add("remote-publish-state-changed",({prevMuteState:a,muteState:c})=>{let{userId:d}=c,u=this._room.remotePublishedUserMap.get(d),h=a.audioAvailable,p=a.videoAvailable,{audioAvailable:g,videoAvailable:C}=c;g||this._remoteAudioConfigMap.delete(d),C||this._remoteVideoConfigMap.delete(`${d}_${"main"}`),c.hasAuxiliary||this._remoteVideoConfigMap.delete(`${d}_${"sub"}`),h!==g&&this.emit(g?M.REMOTE_AUDIO_AVAILABLE:M.REMOTE_AUDIO_UNAVAILABLE,{userId:d}),p!==C&&this.emit(C?M.REMOTE_VIDEO_AVAILABLE:M.REMOTE_VIDEO_UNAVAILABLE,{userId:d,streamType:"main"}),a.hasAuxiliary!==c.hasAuxiliary&&this.emit(c.hasAuxiliary?M.REMOTE_VIDEO_AVAILABLE:M.REMOTE_VIDEO_UNAVAILABLE,{userId:d,streamType:"sub"})}).add("firewall-restriction",()=>{this.emit(M.ERROR,new O({code:P.OPERATION_FAILED,extraCode:5501}))}).add("sei-message",a=>{this.emit(M.SEI_MESSAGE,{...a,streamType:Zo(a.streamType)})}).add("heartbeat-report",a=>{let c={2:"big",3:"small",7:"sub"},d={rtt:a.msg_up_stream_info.msg_network_status.uint32_rtt||a.msg_down_stream_info[0]?.msg_network_status.uint32_rtt||this._networkQuality?.uplinkRTT||this._networkQuality?.downlinkRTT||0,upLoss:this._networkQuality?.uplinkLoss||0,downLoss:this._networkQuality?.downlinkLoss||0,bytesSent:a.bytes_sent||0,bytesReceived:a.bytes_received||0,localStatistics:{audio:{bitrate:(a.msg_up_stream_info.msg_audio_status?.uint32_audio_codec_bitrate||0)/1e3,audioLevel:(a.msg_up_stream_info.msg_audio_status?.uint32_audio_level||0)/ze},video:a.msg_up_stream_info.msg_video_status.filter(u=>c[u.uint32_video_stream_type]).map(u=>({bitrate:(u.uint32_video_codec_bitrate||0)/1e3,width:u.uint32_video_width,height:u.uint32_video_height,frameRate:u.uint32_video_enc_fps,videoType:c[u.uint32_video_stream_type]}))},remoteStatistics:a.msg_down_stream_info.map(u=>({userId:u.msg_user_info.str_identifier,audio:{bitrate:(u.msg_audio_status.uint32_audio_codec_bitrate||0)/1e3,audioLevel:(u.msg_audio_status.uint32_audio_level||0)/ze},video:u.msg_video_status.map(h=>({bitrate:(h.uint32_video_codec_bitrate||0)/1e3,width:h.uint32_video_width,height:h.uint32_video_height,frameRate:h.uint32_video_dec_fps,videoType:c[h.uint32_video_stream_type]}))}))};this.emit(M.STATISTICS,d)}),this._handleReceiveMode(),await this._room.join(n,t,Ss.frameWorkType),this._checkTrackToPublish()}async exitRoom(){return await this._exitRoom()}async switchRole(e){await this._room.switchRole(e),e==="anchor"&&this._checkTrackToPublish()}destroy(){ie(re),this.removeAllListeners(),this._room.destroy(),Yc.delete(this),this._localAudioTrack&&this.stopLocalAudio(),this._localVideoTrack&&this.stopLocalVideo(),this._localScreenTrack&&this.stopScreenShare()}async startLocalAudio(e={publish:!0}){if(this._localAudioTrack){this._log.warn("local audio is already started");return}let{publish:t=!0,mute:i,option:s}=e,o=new Ne(this._room.audioManager),n={},a={muted:!0};s&&(f(s.microphoneId)?f(s.audioTrack)||(n.customSource=s.audioTrack):n.deviceId=s.microphoneId,s&&K(s.captureVolume)&&o.setCaptureVolume(s.captureVolume),f(s.profile)||(j(s.profile)?_o[s.profile]&&o.setProfile(_o[s.profile]):o.setProfile(s.profile)),K(s.earMonitorVolume)&&(a.muted=!(s.earMonitorVolume>0),a.volume=s.earMonitorVolume),f(s.echoCancellation)||(o.profile.echoCancellation=s.echoCancellation),f(s.noiseSuppression)||(o.profile.noiseSuppression=s.noiseSuppression),f(s.autoGainControl)||(o.profile.autoGainControl=s.autoGainControl)),o.on("5",c=>{this.emit(M.ERROR,new O({code:P.DEVICE_ERROR,extraCode:5309,messageParams:{error:c}}))}),o.on("2",c=>{this.emit(M.DEVICE_CHANGED,{type:"microphone",action:"active",device:c})}),o.on("4",c=>{let d;c.error&&(d=O.convertFrom(c.error)),this.emit(M.PUBLISH_STATE_CHANGED,{...c,error:d})}),this._listenOutputTrackChanged(o),await o.capture(n),f(i)||o.setMute(i),_e(o,o).add("player-state-changed",c=>{this.emit(M.AUDIO_PLAY_STATE_CHANGED,{...c,userId:""})}),t&&this._room.isJoined&&this._room.publish(o).catch(()=>{}),await this._updateAudioPlayOption({playOption:a,track:o}),this._localAudioTrack=o,this._localAudioConfig={...e,publish:t}}async updateLocalAudio(e){if(!this._localAudioTrack||!this._localAudioConfig)return;let{publish:t,mute:i,option:s}=e,o={};s&&(s.microphoneId?await this._localAudioTrack.switchDevice(s.microphoneId):f(s.audioTrack)||await this._localAudioTrack.setInputMediaStreamTrack(s.audioTrack),f(s.captureVolume)||this._localAudioTrack.setCaptureVolume(s.captureVolume),f(s.earMonitorVolume)||(o.muted=!(s.earMonitorVolume>0),o.volume=s.earMonitorVolume),await this._localAudioTrack.update3A(s)),this._room.isJoined&&!f(t)&&(t&&!this._localAudioConfig.publish&&this._room.publish(this._localAudioTrack).catch(()=>{}),this._localAudioConfig.publish&&!t&&this._room.unpublish(this._localAudioTrack).catch(()=>{})),f(i)||this._localAudioTrack.setMute(i),await this._updateAudioPlayOption({playOption:o,track:this._localAudioTrack,prevConfig:this._localAudioConfig}),lt(this._localAudioConfig,e)}async stopLocalAudio(){!this._localAudioTrack||(this._room.isJoined&&await this._room.unpublish(this._localAudioTrack).catch(()=>{}),this._localAudioTrack.stop(),this._localAudioTrack.close(),ie(this._localAudioTrack),this._localAudioTrack=null,this._localAudioConfig=null)}async startLocalVideo(e={publish:!0,view:null}){if(this._localVideoTrack){this._log.warn("local video is already started");return}let{view:t,publish:i=!0,mute:s,option:o}=e,n=new fe(this._room.videoManager),a={},c={};if(o&&(o.cameraId?a.deviceId=o.cameraId:f(o.useFrontCamera)?f(o.videoTrack)||(a.customSource=o.videoTrack):a.facingMode=o.useFrontCamera?l.FACING_MODE_USER:l.FACING_MODE_ENVIRONMENT,f(o.profile)||(j(o.profile)?Ve[o.profile]&&n.setProfile(Ve[o.profile]):n.setProfile(o.profile)),f(o.fillMode)||(c.objectFit=o.fillMode),f(o.mirror)||(c.mirror=o.mirror),f(o.small)||(rs()?j(o.small)?n.small=Ve[o.small]:o.small===!0?n.small=Ve["120p"]:n.small=o.small:this._log.warn("small stream is not supported"))),n.on("5",d=>{this.emit(M.ERROR,new O({code:P.DEVICE_ERROR,extraCode:5308,messageParams:{error:d}}))}),n.on("2",d=>{this.emit(M.DEVICE_CHANGED,{type:"camera",action:"active",device:d})}),n.on("4",d=>{let u;d.error&&(u=O.convertFrom(d.error)),this.emit(M.PUBLISH_STATE_CHANGED,{...d,error:u})}),this._listenOutputTrackChanged(n),await n.capture(a),f(s)||n.setMute(s),o?.qosPreference&&n.mediaTrack){let d=Ts(o.qosPreference);n.mediaTrack.contentHint=d}_e(n,n).add("player-state-changed",d=>{this.emit(M.VIDEO_PLAY_STATE_CHANGED,{...d,userId:"",streamType:"main"})}),i&&this._room.isJoined&&this._room.publish(n).catch(()=>{}),await this._updateVideoPlayOption({view:t,playOption:c,track:n}),this._localVideoTrack=n,this._localVideoConfig={...e,view:t,publish:i}}async updateLocalVideo(e){if(!this._localVideoTrack||!this._localVideoConfig)return;let{view:t,publish:i,mute:s,option:o}=e,n={};if(o){if(f(o.profile)||(j(o.profile)?Ve[o.profile]&&this._localVideoTrack.setProfile(Ve[o.profile]):this._localVideoTrack.setProfile(o.profile),(!o.cameraId||!this._localVideoTrack.isNeedToSwitchDevice(o.cameraId))&&f(o.useFrontCamera)&&this._localVideoTrack.applyProfile()),o.cameraId?await this._localVideoTrack.switchDevice(o.cameraId):f(o.useFrontCamera)?f(o.videoTrack)||await this._localVideoTrack.setInputMediaStreamTrack(o.videoTrack):await this._localVideoTrack.switchDevice(o.useFrontCamera?l.FACING_MODE_USER:l.FACING_MODE_ENVIRONMENT),f(o.fillMode)||(n.objectFit=o.fillMode),f(o.mirror)||(n.mirror=o.mirror),o.qosPreference&&this._localVideoTrack.mediaTrack){let a=Ts(o.qosPreference);this._localVideoTrack.mediaTrack.contentHint=a}if(o.small){let a=!this._localVideoTrack.small;rs()?(o.small===!0?this._localVideoTrack.small=Ve["120p"]:j(o.small)?this._localVideoTrack.small=Ve[o.small]:this._localVideoTrack.small=o.small,this._room.videoManager.update(),a&&this._room.enableSmall(!0)):this._log.warn("small stream is not supported")}else o.small===!1&&this._localVideoTrack.small&&(delete this._localVideoTrack.small,this._room.videoManager.update(),this._room.enableSmall(!1))}this._room.isJoined&&!f(i)&&(i&&!this._localVideoConfig.publish&&this._room.publish(this._localVideoTrack).catch(()=>{}),this._localVideoConfig.publish&&!i&&this._room.unpublish(this._localVideoTrack).catch(()=>{})),f(s)||this._localVideoTrack.setMute(s),await this._updateVideoPlayOption({view:t,playOption:n,track:this._localVideoTrack,prevConfig:this._localVideoConfig}),lt(this._localVideoConfig,e)}async stopLocalVideo(){!this._localVideoTrack||(this._room.isJoined&&await this._room.unpublish(this._localVideoTrack).catch(()=>{}),this._localVideoTrack.stop(),this._localVideoTrack.close(),ie(this._localVideoTrack),this._localVideoTrack=null,this._localVideoConfig=null)}async startScreenShare(e={publish:!0,view:null}){if(this._localScreenTrack){this._log.warn("screen share is already started");return}let{view:t=null,publish:i=!0,option:s}=e,o=new ke(this._room.videoManager);o.on("4",u=>{let h;u.error&&(h=O.convertFrom(u.error)),this.emit(M.PUBLISH_STATE_CHANGED,{...u,error:h})}),this._listenOutputTrackChanged(o);let n=null;o.setMediaType(2);let a={},c={};s&&(f(s.profile)||(j(s.profile)?fo[s.profile]&&o.setProfile(fo[s.profile]):o.setProfile(s.profile)),s.systemAudio&&(a.systemAudio=!0,a.echoCancellation=s.echoCancellation,a.noiseSuppression=s.noiseSuppression,a.autoGainControl=s.autoGainControl),f(s.fillMode)||(c.objectFit=s.fillMode),s.videoTrack&&(a.videoTrack=s.videoTrack),s.audioTrack&&(a.audioTrack=s.audioTrack));let d=await o.capture(a);if(s?.qosPreference){let u=Ts(s.qosPreference);o.mediaTrack.contentHint=u}if(o.mediaTrack.addEventListener(l.ENDED,()=>{this._stopScreenShare(),this.emit(M.SCREEN_SHARE_STOPPED)}),d.getAudioTracks()[0]&&(n=new Pt(this._room.audioManager),n.setInputMediaStreamTrack(d.getAudioTracks()[0])),_e(o,o).add("player-state-changed",u=>{this.emit(M.VIDEO_PLAY_STATE_CHANGED,{...u,userId:"",streamType:"sub"})}),i&&this._room.isJoined){let u=[o];n&&u.push(n),this._room.publish(...u).catch(()=>{})}await this._updateVideoPlayOption({view:t,playOption:c,track:o}),this._localScreenTrack=o,this._localScreenAudioTrack=n,this._localScreenConfig={...e,view:t,publish:i}}async updateScreenShare(e){if(!this._localScreenTrack||!this._localScreenConfig)return;let{view:t,publish:i,option:s}=e,o={};if(s&&(f(s.fillMode)||(o.objectFit=s.fillMode),s.qosPreference)){let n=Ts(s.qosPreference);this._localScreenTrack.mediaTrack.contentHint=n}this._room.isJoined&&!f(i)&&(i&&!this._localScreenConfig.publish&&this._room.publish(this._localScreenTrack).catch(()=>{}),this._localScreenConfig.publish&&!i&&this._room.unpublish(this._localScreenTrack).catch(()=>{})),await this._updateVideoPlayOption({view:t,playOption:o,track:this._localScreenTrack,prevConfig:this._localScreenConfig}),lt(this._localScreenConfig,e)}async stopScreenShare(){return await this._stopScreenShare()}async startRemoteVideo(e){let{view:t,userId:i,streamType:s,option:o}=e,n=`${i}_${s}`;if(this._remoteVideoConfigMap.has(n)){this._log.warn(`remote video has already started. userId:${i}, streamType:${s}`);return}let a=this._room.remotePublishedUserMap.get(i);if(!a)return;let c={},d=s==="main"?a.remoteVideoTrack:a.remoteAuxiliaryTrack;this._listenOutputTrackChanged(d),o&&(f(o.fillMode)||(c.objectFit=o.fillMode),f(o.mirror)||(c.mirror=o.mirror),s==="main"&&!f(o.small)&&this._room.changeType(o.small,d.user)),await this._room.subscribe(d),await this._updateVideoPlayOption({view:t,playOption:c,track:d}),this._remoteVideoConfigMap.set(n,e),this._emitTrackEvent(d)}async updateRemoteVideo(e){let{view:t,userId:i,streamType:s,option:o}=e,n=`${i}_${s}`;if(!this._remoteVideoConfigMap.has(n)||!this._room.remotePublishedUserMap.has(i))return;let a={};o&&(f(o.fillMode)||(a.objectFit=o.fillMode),f(o.mirror)||(a.mirror=o.mirror));let c=null,d=this._room.remotePublishedUserMap.get(i);s==="main"&&d?.muteState.hasVideo&&(c=d.remoteVideoTrack),s==="sub"&&d?.muteState.hasAuxiliary&&(c=d.remoteAuxiliaryTrack);let u=this._remoteVideoConfigMap.get(n);c&&(s==="main"&&o&&!f(o.small)&&this._room.changeType(o.small,c.user),await this._updateVideoPlayOption({view:t,playOption:a,track:c,prevConfig:u})),lt(u,e)}async stopRemoteVideo(e){return this._stopRemoteVideo(e)}async _stopRemoteVideo(e,t=!0){let i=[],s=this._room.remotePublishedUserMap.get(e.userId);if(s){let{muteState:o,remoteVideoTrack:n,remoteAuxiliaryTrack:a}=s;e.streamType==="main"&&(n.stop(),o.hasVideo&&i.push(n)),e.streamType==="sub"&&(a.stop(),o.hasAuxiliary&&i.push(a))}for(let o of i)t&&(await this._room.unsubscribe(o),this._mediaTrackMap.get(o.outMediaTrack)===o.userId&&this._mediaTrackMap.delete(o.outMediaTrack));this._remoteVideoConfigMap.delete(`${e.userId}_${e.streamType}`)}async muteRemoteAudio(e,t){if(e==="*")if(t)await this._stopRemoteAudio({userId:e});else{let i=[...this._room.remotePublishedUserMap.values()];for(let s of i)s.muteState.hasAudio&&await this._startRemoteAudio({userId:s.userId})}else t?await this._stopRemoteAudio({userId:e}):await this._startRemoteAudio({userId:e});this._remoteAudioMuteMap.set(e,t)}setRemoteAudioVolume(e,t){if(e==="*"){let i=[...this._room.remotePublishedUserMap.values()];for(let s of i)this._updateAudioPlayOption({playOption:{volume:t},track:s.remoteAudioTrack})}else if(e){let i=this._room.remotePublishedUserMap.get(e);i&&this._updateAudioPlayOption({playOption:{volume:t},track:i.remoteAudioTrack})}}async startPlugin(e,t){return e.start(t)}async updatePlugin(e,t){return e.update(t)}async stopPlugin(e,t){return e.stop(t)}enableAudioVolumeEvaluation(e=2e3,t=!1){this._room.enableAudioVolumeEvaluation(e,t)}on(e,t,i){return super.on(e,t,i),this._eventListened.add(e),this}off(e,t,i){return e==="*"?(this._eventListened.clear(),this.removeAllListeners()):super.off(e,t,i),this}getVideoTrack(e={userId:"",streamType:"main"}){let{userId:t="",streamType:i="main"}=e;if(t===""){if(i==="main"&&this._localVideoTrack)return this._localVideoTrack.mediaTrack;if(i==="sub"&&this._localScreenTrack)return this._localScreenTrack.mediaTrack}else{let s=this._room.remotePublishedUserMap.get(t);if(s)return i==="main"?s.remoteVideoTrack.mediaTrack:s.remoteAuxiliaryTrack.mediaTrack}return null}getAudioTrack(e){if(e){let t=this._room.remotePublishedUserMap.get(e);if(t)return t.remoteAudioTrack.mediaTrack}else if(this._localAudioTrack)return this._localAudioTrack.mediaTrack;return null}setCurrentSpeaker(e){this._localAudioTrack?.setAudioOutput(e),this._room.remotePublishedUserMap.forEach(t=>t.remoteAudioTrack.setAudioOutput(e))}_startRemoteAudio(e){return this._doStartRemoteAudio(e)}async _doStartRemoteAudio(e){let{userId:t,option:i}=e;if(this._remoteAudioConfigMap.has(t)){this._log.warn(`remote audio has already started. userId:${t}`);return}let s=this._room.remotePublishedUserMap.get(t);if(!s)return;let o={};i&&(f(i.volume)||(o.volume=i.volume));let n=s.remoteAudioTrack;this._listenOutputTrackChanged(n),await this._room.subscribe(n),await this._updateAudioPlayOption({playOption:o,track:n}),this._remoteAudioConfigMap.set(t,e),this._emitTrackEvent(n)}async _stopRemoteAudio(e,t=!0){let i=this._room.remotePublishedUserMap.get(e.userId);i&&(i.remoteAudioTrack.stop(),i.muteState.hasAudio&&t&&await this._room.unsubscribe(i.remoteAudioTrack),this._mediaTrackMap.get(i.remoteAudioTrack.outMediaTrack)===e.userId&&this._mediaTrackMap.delete(i.remoteAudioTrack.outMediaTrack)),this._remoteAudioConfigMap.delete(`${e.userId}`)}async _updateVideoPlayOption({view:e,playOption:t,track:i,prevConfig:s}){if(f(e)&&s&&s.view&&!es(t)){let o;oe(s.view)?o=s.view:o=bo(s.view),o&&await i.play(o,t)}if(!f(e)){let o;oe(e)?o=e:o=bo(e),o?await i.play(o,t):i.stop()}}async _updateAudioPlayOption({playOption:e={},track:t,prevConfig:i}){if(!t.isPlayCalled)try{await t.play(null,e)}catch{}f(e.muted)||t.setPlayerMute(e.muted),f(e.volume)||t.setAudioVolume(e.volume/100)}_listenOutputTrackChanged(e){e.listeners("output-media-track-changed").length===0&&e.on("output-media-track-changed",()=>this._emitTrackEvent(e))}_emitTrackEvent(e){let t=e.isRemote?e.userId:"";e.outMediaTrack&&this._mediaTrackMap.get(e.outMediaTrack)!==t&&(this._mediaTrackMap.set(e.outMediaTrack,t),this.emit(M.TRACK,{userId:t,streamType:Zo(e.streamType),track:e.outMediaTrack}))}_checkTrackToPublish(){let e=[];if(this._localAudioConfig?.publish&&this._localAudioTrack&&e.push(this._localAudioTrack),this._localVideoConfig?.publish&&this._localVideoTrack&&e.push(this._localVideoTrack),this._localScreenConfig?.publish&&(this._localScreenTrack&&e.push(this._localScreenTrack),this._localScreenAudioTrack&&e.push(this._localScreenAudioTrack)),e.length!==0)return this._room.publish(...e).catch(()=>{})}_handleReceiveMode(){this._room.autoReceiveAudio&&_e(this,this).add(M.REMOTE_AUDIO_AVAILABLE,async({userId:e})=>{this._remoteAudioMuteMap.get("*")||this._remoteAudioMuteMap.get(e)||this._doStartRemoteAudio({userId:e}).catch(()=>{})}),this._room.autoReceiveVideo&&_e(this,this).add(M.REMOTE_VIDEO_AVAILABLE,({userId:e,streamType:t})=>{let i=this._room.remotePublishedUserMap.get(e);if(i){let s=t==="main"?i.remoteVideoTrack:i.remoteAuxiliaryTrack;this._room.subscribe(s).then(()=>{this._emitTrackEvent(s)}).catch(()=>{})}})}async _exitRoom(){this._room.isJoined&&await this._room.leave(),[...this._remoteAudioConfigMap.keys()].forEach(e=>{this._stopRemoteAudio({userId:e}).catch()}),[...this._remoteVideoConfigMap.keys()].forEach(e=>{let t=e.includes("main")?"main":"sub",i=e.split(`_${t}`)[0];i&&this._stopRemoteVideo({userId:i,streamType:t}).catch()}),this._remoteVideoConfigMap.clear(),this._remoteAudioConfigMap.clear(),this._remoteAudioMuteMap.clear(),this._room.remotePublishedUserMap.forEach(e=>{ie(e.remoteAudioTrack),ie(e.remoteVideoTrack),ie(e.remoteAuxiliaryTrack)}),ie(this)}async _stopScreenShare(){if(!!this._localScreenTrack){if(this._room.isJoined){let e=[this._localScreenTrack];this._localScreenAudioTrack&&e.push(this._localScreenAudioTrack),await this._room?.unpublish(...e).catch(()=>{})}this._localScreenTrack.stop(),this._localScreenTrack.close(),this._localScreenAudioTrack?.stop(),this._localScreenAudioTrack?.close(),ie(this._localScreenTrack),this._localScreenTrack=null,this._localScreenAudioTrack=null,this._localScreenConfig=null}}sendSEIMessage(e,t){this._room.sendSEI(e,t||{seiPayloadType:243})}static setLogLevel(e,t){I.setLogLevel(e),f(t)||(t?I.enableUploadLog():I.disableUploadLog())}static isSupported(){return Ba()}static getCameraList(){return Fe()}static getMicrophoneList(){return Oe()}static getSpeakerList(){return nr()}static async setCurrentSpeaker(e){(await nr()).forEach(i=>{i.deviceId===e&&(Yc.forEach(s=>{s.setCurrentSpeaker(e),s.emit(M.DEVICE_CHANGED,{type:"speaker",action:"active",device:i})}),Pe=i)})}},G=Ss;ae(G,"_loggerManager",I),ae(G,"EVENT",M),ae(G,"ERROR_CODE",P),ae(G,"TYPE",fs),ae(G,"frameWorkType",30),y([Re(he.TRTC.enterRoom),ti("room",([r],[e])=>(r.roomId||r.strRoomId)===(e.roomId||e.strRoomId)&&r.userId===e.userId&&r.sdkAppId===e.sdkAppId),F(r=>function(e){return this._log.setUserId(e.userId),this._log.setSdkAppId(e.sdkAppId),r.call(this,e).catch(t=>{throw ie(this),t})}),ee()],G.prototype,"enterRoom",1),y([ee()],G.prototype,"exitRoom",1),y([Re(he.TRTC.switchRole),bi("room",{merge:(r,e)=>e}),ee()],G.prototype,"switchRole",1),y([ee()],G.prototype,"destroy",1),y([Re(he.TRTC.startLocalAudio),ti("audio",([r],[e])=>r?.option?.microphoneId===e?.option?.microphoneId),ee()],G.prototype,"startLocalAudio",1),y([Re(he.TRTC.updateLocalAudio),bi("audio",{debounce:{delay:200,getKey:()=>`${ll}-localAudio`,isNeedToDebounce:r=>!f(r.option?.captureVolume)}}),ee()],G.prototype,"updateLocalAudio",1),y([ii("audio"),ee()],G.prototype,"stopLocalAudio",1),y([Re(he.TRTC.startLocalVideo),ti("video",([r],[e])=>r?.option?.cameraId===e?.option?.cameraId),ee()],G.prototype,"startLocalVideo",1),y([Re(he.TRTC.updateLocalVideo),bi("video"),ee()],G.prototype,"updateLocalVideo",1),y([ii("video"),ee()],G.prototype,"stopLocalVideo",1),y([Re(he.TRTC.startScreenShare),ti("screen",()=>!0),ee()],G.prototype,"startScreenShare",1),y([Re(he.TRTC.updateScreenShare),bi("screen"),ee()],G.prototype,"updateScreenShare",1),y([ee()],G.prototype,"stopScreenShare",1),y([Re(he.TRTC.startRemoteVideo),ti(r=>`v${r.userId}${r.streamType}`,()=>!0),ee({getRemoteId:r=>`${r.userId}_${r.streamType}`})],G.prototype,"startRemoteVideo",1),y([Re(he.TRTC.updateRemoteVideo),bi(r=>`v${r.userId}${r.streamType}`),ee({getRemoteId:r=>`${r.userId}_${r.streamType}`})],G.prototype,"updateRemoteVideo",1),y([Re(he.TRTC.stopRemoteVideo),F(r=>async function(e){if(e.userId==="*"){let t=[];return this._room.remotePublishedUserMap.forEach(i=>{this._remoteVideoConfigMap.has(`${i.userId}_${"main"}`)&&t.push(this.stopRemoteVideo({streamType:"main",userId:i.userId}).catch(()=>{})),this._remoteVideoConfigMap.has(`${i.userId}_${"sub"}`)&&t.push(this.stopRemoteVideo({streamType:"sub",userId:i.userId}).catch(()=>{}))}),Promise.all(t)}return r.call(this,e)}),ee({getRemoteId:r=>`${r.userId}_${r.streamType}`})],G.prototype,"stopRemoteVideo",1),y([ii(r=>`v${r.userId}${r.streamType}`)],G.prototype,"_stopRemoteVideo",1),y([Re(...he.TRTC.muteRemoteAudio),ee({getRemoteId:r=>r})],G.prototype,"muteRemoteAudio",1),y([Jc(...he.TRTC.setRemoteAudioVolume),dl(200,r=>r),ee({getRemoteId:r=>r})],G.prototype,"setRemoteAudioVolume",1),y([on("start"),ti((r,e)=>r.getAlias()+r.getGroup(e)),ee({namePrefix:r=>r.getName()})],G.prototype,"startPlugin",1),y([on("update"),bi((r,e)=>r.getAlias()+r.getGroup(e)),ee({namePrefix:r=>r.getName()})],G.prototype,"updatePlugin",1),y([on("stop"),ii((r,e)=>r.getAlias()+r.getGroup(e)),ee({namePrefix:r=>r.getName()})],G.prototype,"stopPlugin",1),y([Jc(...he.TRTC.enableAudioVolumeEvaluation)],G.prototype,"enableAudioVolumeEvaluation",1),y([ti(r=>`a${r.userId}`,()=>!0)],G.prototype,"_startRemoteAudio",1),y([F(r=>async function(e){return e.userId==="*"?Promise.all([...this._room.remotePublishedUserMap.values()].map(t=>this._stopRemoteAudio({...e,userId:t.userId}).catch(()=>{}))):r.call(this,e)}),ii(r=>`a${r.userId}`)],G.prototype,"_stopRemoteAudio",1),y([ii("room")],G.prototype,"_exitRoom",1),y([ii("screen")],G.prototype,"_stopScreenShare",1),y([Re(he.TRTC.sendSEIMessage),Xu({timesInSecond:30,maxSizeInSecond:8e3,getSize:(...r)=>r[0].byteLength})],G.prototype,"sendSEIMessage",1),y([Re(he.TRTC.create)],G,"_create",1);var Is=G;var zc=class{constructor(){this._set=new Set;_.on(m.LEAVE_SUCCESS,this.delete,this)}add({room:e,roomId:t}){if(e.scene==="rtc")return;let i=this.getKey(e.userId,t||e.roomId,e.sdkAppId,e.useStringRoomId);this._set.add(i)}delete({room:e,roomId:t}){if(e.scene==="rtc")return;let i=this.getKey(e.userId,e.roomId||t,e.sdkAppId,e.useStringRoomId);this._set.delete(i)}getKey(e,t,i,s){return`${i}_${t}_${e}_${s}`}isJoined({userId:e,roomId:t,sdkAppId:i,room:s}){return s.scene==="rtc"?!1:this._set.has(this.getKey(e,t,i,s.useStringRoomId))}};async function _m(){let r,e;try{let h=await Oe();r=h&&h.length}catch{}try{let h=await Fe();e=h&&h.length}catch{}let t={microphone:r,camera:e},{isH264EncodeSupported:i,isVp8EncodeSupported:s,isH264DecodeSupported:o,isVp8DecodeSupported:n}=this.checkSystemResult.detail,a=Xt.basis(),c={webRTC:a.isWebRTCSupported,getUserMedia:a.isGetUserMediaSupported,webSocket:a.isWebSocketsSupported,screenShare:a.isScreenShareSupported,webAudio:a.isWebAudioSupported,h264Encode:i,h264Decode:o,vp8Encode:s,vp8Decode:n},d={browser:a.browser,os:a.os,trtc:c,devices:t},u={isWebCodecSupported:a.isWebCodecSupported,isMediaSessionSupported:a.isMediaSessionSupported,isWebTransportSupported:a.isWebTransportSupported};Y.uploadEvent({log:`trtcstats-${JSON.stringify(d)}`,userId:this.userId}),this._log.info(`TrtcStats-${JSON.stringify(d)}`),Y.uploadEvent({log:`trtcadvancedstats-${JSON.stringify(u)}`,userId:this.userId})}function ml(){return F(r=>{let e=new zc;return async function(t,i,s){let o=String(t.roomId||t.strRoomId);if(this.userId=t.userId,this.sdkAppId=t.sdkAppId,this.userSig=t.userSig,this._log.setSdkAppId(this.sdkAppId),this._log.setUserId(this.userId),this.scene=i,t.privateMapKey=t.privateMapKey||"",this.isJoined)throw new A({code:E.INVALID_OPERATION,message:v({key:b.INVALID_JOIN})});if(this.checkDestroy(),e.isJoined({userId:this.userId,roomId:o,sdkAppId:this.sdkAppId,room:this}))throw new A({code:E.INVALID_OPERATION,message:v({key:b.REPEAT_JOIN,data:this.userId})});e.add({room:this,roomId:o}),this.role=t.role===21?"audience":"anchor",this._log.info(`Join() => joining room: ${o} useStringRoomId: ${this.useStringRoomId} scene: ${this.scene} role: ${this.role}`),_.emit(m.JOIN_START,{room:this,roomId:o,params:t}),this.checkSystemResult=await Xt.checkSystemRequirementsInternal(),this.checkDestroy();let n=be.getEnv();n||(n=bt.QCLOUD,this.proxy_ws&&(this.proxy_ws.startsWith(To.OLD_CLOUD_LADDER)?n=bt.OLD_CLOUD_LADDER:this.proxy_ws.startsWith(To.WEBRTC)&&(n=bt.WEBRTC))),Y.setConfig({env:n,sdkAppId:String(this.sdkAppId),userId:this.userId,roomId:o}),_m.call(this);let{isH264EncodeSupported:a,isVp8EncodeSupported:c}=this.checkSystemResult.detail;if(!Xt.isWebRTCSupported()||!a&&!c)throw new A({code:E.NOT_SUPPORTED,message:v({key:b.NOT_SUPPORTED_WEBRTC})});try{!this.proxy_ws&&!this.proxy_wt&&!this.scheduleResult.domains&&!be.getEnv()&&await this.schedule(o,s,Se);let d=await r.call(this,t,i,s);return this.roomId=o,this._joinedTimestamp=be.performanceNow(),_.emit(m.JOIN_SUCCESS,{room:this}),Y.uploadEvent({log:`stat-conv-${Number(Ke)}-${location.hostname}`,userId:this.userId}),d}catch(d){throw e.delete({room:this,roomId:o}),_.emit(m.JOIN_FAILED,{room:this,error:d}),d}}})}var _l=()=>F(r=>async function(...e){_.emit(m.LEAVE_START,{room:this}),await r.call(this),_.emit(m.LEAVE_SUCCESS,{room:this,roomId:this.roomId})});function fl(){return F(r=>function(...e){let t=r.apply(this,e);return e.forEach(i=>!i.isSubscribed&&i.subscribe(t)),t})}var El=pe(ve());var se={SETUP_SUCCESS:"1",SETUP_FAILED:"5",CONNECTION_STATE_CHANGED:"2",CONNECTED:"3",RECONNECT_FAILED:"4"},Me={DISCONNECTED:"DISCONNECTED",CONNECTING:"CONNECTING",CONNECTED:"CONNECTED"},It={CLIENT_BANNED:9,CHANNEL_SETUP_RESULT:19,CHANNEL_RECONNECT_RESULT:514,JOIN_ROOM_RESULT:20,PEER_JOIN:4134,PEER_LEAVE:4135,STREAM_ADDED:16,STREAM_REMOVED:18,UPLINK_NETWORK_STATS:22,UPDATE_REMOTE_MUTE_STAT:23,PUBLISH_RESULT:4098,PUBLISH_STATE_CHANGE_RESULT:4112,UNPUBLISH_RESULT:4100,SUBSCRIBE_RESULT:4102,UNSUBSCRIBE_RESULT:4104,SUBSCRIBE_CHANGE_RESULT:4106,MUTE_RESULT:4108,UPDATE_OFFER_RESULT:4128,START_PUBLISH_TENCENT_CDN_RES:1286,STOP_PUBLISH_TENCENT_CDN_RES:1288,START_PUBLISH_GIVEN_CDN_RES:777,STOP_PUBLISH_GIVEN_CDN_RES:779,START_MIX_TRANSCODE_RES:781,STOP_MIX_TRANSCODE_RES:783,USER_LIST_RES:4137,SWITCH_ROLE_RES:4110,UPDATE_CONSTRAINT_CONFIG_RES:772,REBUILD_PEER_CONNECTION_RES:4150,SPC_PUBLISH_RESULT:4146,SPC_SUBSCRIBE_RESULT:4156,ABILITY_STATUS_REPORT_RESULT:4158},Tl=[It.UPDATE_REMOTE_MUTE_STAT,It.UPLINK_NETWORK_STATS,It.USER_LIST_RES,It.MUTE_RESULT],V={CLIENT_BANNED:"client-banned",CHANNEL_SETUP_RESULT:"channel-setup-result",CHANNEL_RECONNECT_RESULT:"channel-reconnect-result",JOIN_ROOM_RESULT:"join-room-result",PEER_JOIN:"peer-join",PEER_LEAVE:"peer-leave",STREAM_ADDED:"stream-added",STREAM_REMOVED:"stream-removed",UPLINK_NETWORK_STATS:"uplink-network-stats",UPDATE_REMOTE_MUTE_STAT:"update-remote-mute-stat",PUBLISH_RESULT:"publish-result",PUBLISH_STATE_CHANGE_RESULT:"publish-state-change-result",UNPUBLISH_RESULT:"unpublish-result",SUBSCRIBE_RESULT:"subscribe-result",SUBSCRIBE_CHANGE_RESULT:"subscribe-change-result",UNSUBSCRIBE_RESULT:"unsubscribe-result",UPDATE_OFFER_RESULT:"update-offer-result",START_PUBLISH_TENCENT_CDN_RES:"start-publish-tencent-cdn-res",STOP_PUBLISH_TENCENT_CDN_RES:"stop-publish-tencent-cdn-res",START_PUBLISH_GIVEN_CDN_RES:"start-publish-given-cdn-res",STOP_PUBLISH_GIVEN_CDN_RES:"stop-publish-given-cdn-res",START_MIX_TRANSCODE_RES:"start-mix-transcode-res",STOP_MIX_TRANSCODE_RES:"stop-mix-transcode-res",USER_LIST_RES:"user-list-res",SWITCH_ROLE_RES:"switch_role_res",MUTE_RESULT:"mute-result",UPDATE_CONSTRAINT_CONFIG_RES:"update-contraint-config-res",REBUILD_PEER_CONNECTION_RES:"rebuild-pc-res",SPC_PUBLISH_RESULT:"spc-publish-result",SPC_SUBSCRIBE_RESULT:"spc-subscribe-result",ABILITY_STATUS_REPORT_RESULT:"ability-status-report"},H={PUBLISH_CHANGE:"publish_change",JOIN_ROOM:"join",LEAVE_ROOM:"leave",ON_QUALITY_REPORT:"quality_report",UPDATE_MUTE_STAT:"mute_uplink",PUBLISH:"publish",PUBLISH_STATE_CHANGE:"publish_state_change",UNPUBLISH:"unpublish",SUBSCRIBE:"subscribe",RECEIVE_DATA_USER_LIST:"receive_data_userlist",UNSUBSCRIBE:"unsubscribe",SUBSCRIBE_CHANGE:"subscribe_change",START_PUBLISH_TENCENT_CDN:"start_publishing",STOP_PUBLISH_TENCENT_CDN:"stop_publishing",START_PUBLISH_GIVEN_CDN:"start_push_user_cdn",STOP_PUBLISH_GIVEN_CDN:"stop_push_user_cdn",START_MIX_TRANSCODE:"start_mcu_mix",STOP_MIX_TRANSCODE:"stop_mcu_mix",GET_USER_LIST:"get_user_list",SWITCH_ROLE:"change_role",UPDATE_CONSTRAINT_CONFIG:"update_constraint_config",REBUILD_PEER_CONNECTION:"rebuild_pc",READY_TO_RECEIVE_DATA:"ready_to_receive",SPC_JOIN_ROOM:"join/v2",SPC_PUBLISH:"publish/v2",SPC_SUBSCRIBE:"subscribe/v3",ABILITY_STATUS_REPORT:"ability_status_report"};var cn=new Set;function gl(r){let e=[...cn.values()].find(t=>t.room.userId===r&&!t.room.isJoined);return e||null}var Rs=class extends El.default{room;url;backupUrl;_socketInUse;_socket;_backupSocket;_backupTimer=-1;_signalInfo={tinyId:void 0,clientIp:"",signalIp:"",relayIp:"",relayInnerIp:"",relayPort:0};_currentState=Me.DISCONNECTED;_reconnectionCount=0;_reconnectionTimer=-1;_seq=0;_log;_lastMessageTime=-1;_prevTime=-1;bytesSent=0;bytesReceived=0;keepAlive=!1;get urlParam(){return`?sdkAppId=${encodeURIComponent(this.sdkAppId)}&userId=${encodeURIComponent(this.userId)}&userSig=${encodeURIComponent(this.userSig)}&keepAlive=${encodeURIComponent(Number(this.keepAlive))}`}get _urlWithParam(){return`${this.url}${this.urlParam}`}get _backupUrlWithParam(){return`${this.backupUrl}${this.urlParam}`}get isConnected(){return this._currentState===Me.CONNECTED}get isConnecting(){return this._currentState===Me.CONNECTING}get sdkAppId(){return this.room.sdkAppId}get userId(){return this.room.userId}get userSig(){return this.room.userSig}constructor(e){super(),this.room=e.room,(this.room.scheduleResult?.config?.keepAliveClient||0)-cn.size>0&&this.room.enableSPC&&(this.keepAlive=!0,cn.add(this)),this.url=e.url,this.backupUrl=e.backupUrl,this._seq=0,this._log=I.createLogger({id:"ws",userId:this.userId,sdkAppId:this.sdkAppId})}get isOnline(){return this._currentState===Me.CONNECTED&&Date.now()-this._lastMessageTime<12*1e3}connect(e){return this.isConnected?Promise.resolve():new Promise((t,i)=>{this._prevTime<0&&(this._prevTime=U()),this._log.info(`connect to ${this.url}${e?` timeout: ${e}`:""} keepAlive: ${Number(this.keepAlive)}`),this.emitConnectionStateChanged(Me.CONNECTING),this._socket=new WebSocket(this._urlWithParam),this.bindSocket(this._socket),this._backupTimer=setTimeout(()=>{this.isConnected||(this._log.info("trying to connect to backupUrl"),this.tryConnectBackup())},5e3);let s=-1;e&&(s=setTimeout(()=>{this.close(),i(new A({code:E.JOIN_ROOM_FAILED,message:"join room timeout"}))},e)),this.once(se.CONNECTED,()=>{clearTimeout(s),t()}),this.once(se.RECONNECT_FAILED,o=>{clearTimeout(s),i(o)})})}tryConnectBackup(){this._backupSocket||(this.unbindAndCloseSocket(l.MAIN),this._log.debug(`try to connect to url: ${this._backupUrlWithParam}`),this._backupSocket=new WebSocket(this._backupUrlWithParam),this.bindSocket(this._backupSocket))}bindSocket(e){e.onopen=this.onopen.bind(this),e.onclose=this.onclose.bind(this),e.onerror=this.onerror.bind(this),e.onmessage=this.onmessage.bind(this)}unbindSocket(e){this.clearBackupTimer(),e.onopen=()=>{},e.onclose=()=>{},e.onerror=()=>{},e.onmessage=()=>{}}unbindAndCloseSocket(e){if(e===l.MAIN){if(this._socket){this.unbindSocket(this._socket);try{this._socket.close(1e3)}catch{}this._socket=null}}else if(this._backupSocket){this.unbindSocket(this._backupSocket);try{this._backupSocket.close(1e3)}catch{}this._backupSocket=null}}clearBackupTimer(){this._backupTimer!==-1&&(clearTimeout(this._backupTimer),this._backupTimer=-1)}clearReconnectionTimer(){this._reconnectionTimer!==-1&&(clearTimeout(this._reconnectionTimer),this._reconnectionTimer=-1)}onopen(e){if(this.isConnected)return;this.isReconnecting&&!this._signalInfo.tinyId&&this.stopReconnection(),this.clearBackupTimer(),e.target===this._socket?(this.unbindAndCloseSocket(l.BACKUP),this._socketInUse=this._socket):(this.unbindAndCloseSocket(l.MAIN),this._socketInUse=this._backupSocket),this.emitConnectionStateChanged(Me.CONNECTED),this.emit(se.CONNECTED)}onclose(e){let{url:t}=e.target,i=e.target===this._socketInUse;if(this._log.info(`websocket[${t} InUse: ${i}] is closed with code: ${e.code}`),i&&(this.emitConnectionStateChanged(Me.DISCONNECTED),!e.wasClean||e.code!==1e3)){this._log.warn(`onclose code:${e.code} reason:${e.reason}`),this._log.warn("close current websocket and schedule a reconnect timeout"),this._socketInUse.onclose=()=>{},this._socketInUse.close(4011);let s=this._socketInUse===this._socket;this._socket=null,this._backupSocket=null,this._socketInUse=null,this.reconnect(s?l.BACKUP:l.MAIN)}}onerror(e){let{url:t}=e.target;if(this._log.error(`websocket[${t}] error observed`),!this.isConnected)this.isReconnecting||_.emit(m.API_SUCCESS_RATE,{room:this.room,apiName:"WebsocketConnect",error:new Error("ws onerror")}),e.target==this._socket?(this.unbindAndCloseSocket(l.MAIN),this.reconnect(l.BACKUP)):(this.unbindAndCloseSocket(l.BACKUP),this.reconnect(l.MAIN));else if(e.target===this._socketInUse){this.unbindAndCloseSocket(l.MAIN),this.unbindAndCloseSocket(l.BACKUP);let i=this._socketInUse===this._socket;this._socketInUse=null,this.reconnect(i?l.BACKUP:l.MAIN)}}onmessage(e){if(!this.isConnected)return;this._lastMessageTime=Date.now(),this.bytesReceived+=No(e.data);let t=JSON.parse(e.data),{cmd:i,data:s}=t,o=Object.values(It),a=Object.keys(It)[o.indexOf(i)],c=V[a];switch(Tl.includes(i)||(this._log.debug(`received ${i} msg: ${e.data}`),c&&this._log.info(`Received event: [ ${c} ]`)),i){case It.CHANNEL_SETUP_RESULT:{if(t.code===0)this._signalInfo.clientIp=s.clientIp,this._signalInfo.signalIp=s.signalInnerIp,this._signalInfo.tinyId=t.tinyId,s.svrTime&&Ud(s.svrTime),this._log.info("ChannelSetup Success"),_.emit(m.API_SUCCESS_RATE,{room:this.room,apiName:"WebsocketConnect",cost:U()-this._prevTime}),this._prevTime=-1,this.emit(se.SETUP_SUCCESS,{signalInfo:this._signalInfo});else{let d=new A({code:E.SIGNAL_CHANNEL_SETUP_FAILED,extraCode:t.code,message:v({key:b.SIGNAL_CHANNEL_SETUP_FAILED,data:{errorCode:t.code,errorMsg:t.message}})});this._log.error(`${t.code}, ${t.message}`),this.close(),_.emit(m.API_SUCCESS_RATE,{room:this.room,apiName:"WebsocketConnect",error:d}),this.emit(se.SETUP_FAILED,d)}break}case It.JOIN_ROOM_RESULT:{t.code===0&&(this._signalInfo.relayIp=s.relayOuterIp,this._signalInfo.relayInnerIp=s.relayInnerIp,this._signalInfo.relayPort=s.relayPort,this._log.info(`signalIp:${this._signalInfo.signalIp} clientIp:${this._signalInfo.clientIp} relayIp: ${this._signalInfo.relayIp}`)),this.emit(c,{data:t});break}case It.CHANNEL_RECONNECT_RESULT:{t.code===0?(this._log.warn("reconnect success"),this.stopReconnection(),_.emit(m.API_SUCCESS_RATE,{room:this.room,apiName:"WebsocketReconnect",cost:U()-this._prevTime}),this._prevTime=-1,this.room.syncUserList(),this.room.checkConnectionsToReconnect()):(this._log.warn(`reconnect failed, ${t.code} ${t.message}`),this.room.reJoin());break}default:this.emit(c,{data:t});break}}reconnect(e=l.MAIN){if(!this.room.isJoined&&this.keepAlive){this.close();return}if(this.isReconnecting)return;if(this._reconnectionCount>=Yr){this._log.warn(`SDK has tried reconnect signal channel for ${Yr} times, but all failed. please check your network`);let s=new A({code:E.SIGNAL_CHANNEL_RECONNECTION_FAILED,message:v({key:b.SIGNAL_CHANNEL_RECONNECTION_FAILED})});_.emit(m.API_SUCCESS_RATE,{room:this.room,apiName:"WebsocketReconnect",error:s}),this.emit(se.RECONNECT_FAILED,s);return}this._reconnectionCount++,this._log.warn(`reconnect ${e} [${this._reconnectionCount}/${Yr}]`);let t=this.getReconnectionUrl(e);this.emitConnectionStateChanged(Me.CONNECTING),this._prevTime<0&&(this._prevTime=U()),e===l.MAIN?(this._socket=new WebSocket(t),this.bindSocket(this._socket)):(this._backupSocket=new WebSocket(t),this.bindSocket(this._backupSocket));let i=we(this._reconnectionCount);this._reconnectionTimer=setTimeout(()=>{this._log.warn(`reconnect ${e} timeout(${i/1e3}s), try again`),this.clearReconnectionTimer(),this.unbindAndCloseSocket(l.MAIN),this.unbindAndCloseSocket(l.BACKUP),this.reconnect(e===l.MAIN?l.BACKUP:l.MAIN)},i)}get isReconnecting(){return this._reconnectionTimer!==-1}getReconnectionUrl(e){let t=e===l.MAIN?this._urlWithParam:this._backupUrlWithParam;if(this._signalInfo.tinyId&&t.indexOf("&rc=1")===-1){let{roomId:i,useStringRoomId:s}=this.room;t+=`&rc=1&relayInnerIp=${this._signalInfo.relayInnerIp}&relayOuterIp=${this._signalInfo.relayIp}&relayPort=${this._signalInfo.relayPort}&roomId=${i}&useStringRoomId=${s}`}return t}send(e,t={}){if(this.isConnected&&!this.room.isLeft){let i={cmd:e,data:t,userId:this.userId,tinyId:this._signalInfo.tinyId,seq:++this._seq},s=JSON.stringify(i);return this._socketInUse.send(s),this.bytesSent+=No(s),i.seq}}sendWaitForResponse({command:e,data:t,timeout:i=5e3,responseCommand:s,commandDesc:o,enableLog:n=!0}){return new Promise((a,c)=>{let d=setTimeout(()=>{this.off(s,u);let p=new A({code:E.API_CALL_TIMEOUT,message:v({key:b.API_CALL_TIMEOUT,data:{commandDesc:o,command:e}})});n&&this._log.warn(p),c(p)},i),u=p=>{p.data.seq===h&&(clearTimeout(d),this.off(s,u),a(p))};this.on(s,u);let h=this.send(e,t)})}sendWaitForResponseWithRetry(e){let{commandDesc:t,command:i,retries:s=0,retryTimeout:o=0}=e;return ht({retryFunction:this.sendWaitForResponse,onRetrying:n=>{this._log.warn(`${t||i} timeout observed, retrying [${n}/${s}]`)},settings:{retries:s,timeout:o},context:this})(e)}getCurrentState(){return this._currentState}getSignalInfo(){return this._signalInfo}stopReconnection(){this.isReconnecting&&(this._reconnectionCount=0,this.clearReconnectionTimer())}close(){this._log.info("closed"),cn.delete(this),this.clearBackupTimer(),this.stopReconnection(),this._signalInfo={tinyId:void 0,clientIp:"",signalIp:"",relayIp:"",relayInnerIp:"",relayPort:0},this._socketInUse=null,this.bytesSent=0,this.bytesReceived=0,this.unbindAndCloseSocket(l.MAIN),this.unbindAndCloseSocket(l.BACKUP),this.emitConnectionStateChanged(Me.DISCONNECTED)}stopKeepAliveIn(e=3600){if(this.keepAlive){this._log.info(`stopKeepAlive in ${e}s`);let t=setTimeout(()=>{this.keepAlive=!1,this._log.info(`close due to not used ${e}s`),this.close()},e*1e3),i=s=>{s instanceof Ei&&s.action==="join"&&(this._log.info("stopKeepAlive clear timeout"),clearTimeout(t),this.room.off(k.STATECHANGED,i))};this.room.on(k.STATECHANGED,i)}}emitConnectionStateChanged(e){e!==this._currentState&&(this._log.info(`${this._currentState} -> ${e}`),this.emit(se.CONNECTION_STATE_CHANGED,{prevState:this._currentState,state:e}),this._currentState=e)}};var Sl=pe(ve());var As=class{constructor(e,t=!1){this.dataView=e;this.isSEI&&(t?this.addPreventionByte():this.removePreventionByte())}addPreventionByte(){let e=this.seiPayloadStartIndex,t=this.dataView.byteLength-2,i=[],s=0;for(let n=e;n<=t;n++){let a=this.dataView.getInt8(n);switch(a){case 0:case 1:case 2:case 3:s===2&&(i.push(3),s=0),a==0?s++:s=0,i.push(a);break;default:s=0,i.push(a);break}}i.push(this.dataView.getInt8(this.dataView.byteLength-1));let o=new DataView(new Uint8Array([...new Uint8Array(this.dataView.buffer).slice(0,e),...i]).buffer);this.dataView=o}removePreventionByte(){let e=this.seiPayloadStartIndex,t=this.dataView.byteLength-1,i=[],s=0;for(let n=e;n<=t;n++)switch(this.dataView.getInt8(n)){case 0:s++,i.push(this.dataView.getInt8(n));break;case 3:s!==2&&i.push(this.dataView.getInt8(n)),s=0;break;default:i.push(this.dataView.getInt8(n)),s=0;break}let o=new DataView(new Uint8Array([...new Uint8Array(this.dataView.buffer).slice(0,e),...i]).buffer);this.dataView=o}get isSEI(){return this.dataView.getUint8(4)===6}get seiPayloadStartIndex(){let e=6;for(let t=6;t<this.dataView.buffer.byteLength&&(e++,this.dataView.getUint8(t)===255);t++);return e}get seiPayloadType(){return this.isSEI?this.dataView.getUint8(5):null}get seiPayload(){if(!this.isSEI)return null;let e=0,t=6;for(let o=6;o<this.dataView.buffer.byteLength;o++){let n=this.dataView.getUint8(o);if(t++,n===255)e+=255;else{e+=n;break}}let i=new ArrayBuffer(e),s=new DataView(i);for(let o=0;o<i.byteLength;o++,t++)s.setInt8(o,this.dataView.getInt8(t));return s}};var Xc=class{constructor(e,t){this._singlePC=e;this._log=t}_seiMessageList=[];_smallSeiMessageList=[];_seiPayloadType=243;_seiAbortMap=new Map;_nonSeiAbortMap=new Map;onSEIMessage=null;isRunning(e){return this._seiAbortMap.has(e)}start(e){let t=e.direction===l.TRANSCEIVER_DIRECTION_SENDONLY,i=(t?e.sender:e.receiver).createEncodedStreams(),s=i.readable,o=i.writable,n=new TransformStream({transform:t?(c,d)=>this.encodeVideoFrame(c,d,e):(c,d)=>this.decodeVideoFrame(c,d,e)}),a=new AbortController;s.pipeThrough(n).pipeTo(o,a).catch(c=>{this._log.warn(c)}),this._seiAbortMap.set(e,a)}restart(e){this.stop(e),this.start(e)}stop(e){this._seiAbortMap.get(e)?.abort(),this._seiAbortMap.delete(e)}destroy(){this._seiAbortMap.forEach(e=>e.abort()),this._seiAbortMap.clear(),this._nonSeiAbortMap.forEach(e=>e.abort()),this._nonSeiAbortMap.clear(),this.onSEIMessage=null}handleEncodedStreams(){try{this._singlePC.getPeerConnection().getTransceivers().forEach((t,i)=>{if(!(t.direction==="inactive"||!t.mid))if(i<4)i===1||i===2?this.isRunning(t)||this.start(t):this._nonSeiAbortMap.has(t)||this.pipeSenderOrReceiver(t);else{let s=(i-4)%3===1,o=(i-4)%3===2;t.receiver?.track?.kind===l.VIDEO&&(s||o)?this.isRunning(t)||this.start(t):this._nonSeiAbortMap.has(t)||this.pipeSenderOrReceiver(t)}})}catch(e){this._log.warn(e)}}pipeSenderOrReceiver(e){let i=e.direction===l.TRANSCEIVER_DIRECTION_SENDONLY?e.sender:e.receiver,{readable:s,writable:o}=i.createEncodedStreams(),n=new AbortController;this._nonSeiAbortMap.set(e,n),s.pipeTo(o,n).catch(()=>{})}push(e,t){t&&t.seiPayloadType&&(this._seiPayloadType=t.seiPayloadType),this._seiMessageList.push(e),this._singlePC.getPeerConnection()?.getSenders()[2]?.track&&this._smallSeiMessageList.push(e)}hasSEI(e){let t=new DataView(e);return t.getInt32(0)===1&&t.getInt8(4)===6}isEmptyFrame(e){return e.type==="empty"||e.data.byteLength===0}getNaluCount(e){let t=0,i=0,s=new DataView(e);for(let o=0;o<e.byteLength;o++)switch(s.getUint8(o)){case 0:t++;break;case 1:(t===2||t===3)&&i++,t=0;break;default:t=0;break}return i}encodeVideoFrame(e,t,i){try{let s=Number(i.mid)===2?this._smallSeiMessageList:this._seiMessageList;if(this._singlePC.isUsingH264&&s.length>0&&!this.isEmptyFrame(e)){let o=this.getNaluCount(e.data),n=9-o;if(n<=0)return;let a=s.splice(0,n).reverse().map(this.encodeSEINalu.bind(this)),c=a.reduce((g,C)=>g+C.dataView.byteLength,0),d=new ArrayBuffer(c+e.data.byteLength),u=new DataView(d),h=new DataView(e.data),p=0;for(let g=0;g<a.length;g++)for(let C=0;C<a[g].dataView.byteLength;C++)u.setInt8(p++,a[g].dataView.getInt8(C));for(let g=0;g<e.data.byteLength;g++)u.setInt8(p++,h.getInt8(g));e.data=d,this._log.debug(`${a.length} sei sent`)}}catch(s){this._log.warn(s)}t.enqueue(e)}decodeVideoFrame(e,t,i){try{if(this._singlePC.isUsingH264&&!this.isEmptyFrame(e)&&this.hasSEI(e.data)){let s=[],o=new DataView(e.data),n=0,a=-1,c=-1;for(let d=0;d<e.data.byteLength;d++){let u=o.getUint8(d);if(u===0)n++;else if(u===1){if(n===2||n===3){let h=d-n;if(a===-1?a=h:c===-1&&(c=h,s.push(new As(new DataView(o.buffer.slice(a,c)))),a=h,c=-1),!(o.getUint8(d+1)===6)){e.data=new DataView(o.buffer.slice(h)).buffer;break}}n=0}else n=0}this._log.debug(`${s.length} sei received`),W(this.onSEIMessage)&&s.reverse().forEach(d=>{let u=Number(i.mid);this.onSEIMessage({seiPayloadType:d.seiPayloadType,data:d.seiPayload.buffer,mid:u,streamType:(u-4)%3===2?"auxiliary":"main"})})}}catch(s){this._log.warn(s)}t.enqueue(e)}encodeSEINalu(e){let t=e.byteLength,i=parseInt(String(t/255),10),s=t%255,o=[];o.push(0,0,0,1,6,this._seiPayloadType);for(let a=0;a<i;a++)o.push(255);o.push(s);let n=new DataView(e);return o.push(...new Uint8Array(n.buffer)),o.push(128),new As(new DataView(new Uint8Array(o).buffer),!0)}},fr=Xc;var qc=0,Qc=!1,dn=new Set,fm=r=>qc>2&&!Qc&&dn.size===0&&r,Zc=!1,Le=class{userId;tinyId;_sdpSemantics;_isUplink;_room;_log;_signalChannel;_isErrorObserved=!1;_waitForPeerConnectionConnectedPromise;_waitForPeerConnectionConnectedPromiseReject=null;_peerConnection=null;_emitter=new Sl.default;_currentState="DISCONNECTED";_isReconnecting=!1;_reconnectionCount=0;_reconnectionTimer=-1;_isFirstConnection=!0;_prevTime=-1;_enableSEI;_sei;_localAddress;_remoteAddress;constructor(e){this.userId=e.userId,this.tinyId=e.tinyId,this._room=e.room,this._sdpSemantics=e.room.sdpSemantics,this._isUplink=e.isUplink,this._log=I.createLogger({id:"n",userId:this._room.userId,remoteUserId:this.userId,sdkAppId:this._room.sdkAppId,isLocal:this._isUplink}),this._signalChannel=e.signalChannel,this._enableSEI=e.enableSEI,this._enableSEI&&Ze&&(this._sei=new fr(this,this._log,this._isUplink))}beforeConnect(){this._prevTime<0&&(this._prevTime=U())}async afterConnect(e){try{await e,this._isFirstConnection?(this._isFirstConnection=!1,_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"PeerConnectionConnect",cost:Math.min(U()-this._prevTime,30*1e3)})):this._isReconnecting&&_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"PeerConnectionReconnect",cost:U()-this._prevTime}),this._prevTime=-1}catch(t){throw this._isFirstConnection?(this._isFirstConnection=!1,_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"PeerConnectionConnect",error:t})):this._isReconnecting&&this._reconnectionCount>=3&&_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"PeerConnectionReconnect",error:t}),t}}initialize(){let e={encodedInsertableStreams:this._enableSEI&&Ze,iceServers:this._room.getIceServers(),iceTransportPolicy:this._room.getIceTransportPolicy(),sdpSemantics:this._sdpSemantics,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"};this._peerConnection=new RTCPeerConnection(e),this._peerConnection.onconnectionstatechange=this.onConnectionStateChange.bind(this)}close(e){this._log.info("close connection"),this._emitter.emit("closed",e),this._isReconnecting&&this.stopReconnection(),this.closePeerConnection(),this._sei&&(this._sei.destroy(),this._sei=null),dn.delete(this)}closePeerConnection(e=!1){this._peerConnection&&(this._log.info("close pc"),this._peerConnection.onconnectionstatechange=null,this._peerConnection.close(),this._peerConnection=null,e&&this.emitConnectionStateChangedEvent("DISCONNECTED")),this._waitForPeerConnectionConnectedPromiseReject&&this._waitForPeerConnectionConnectedPromiseReject(new A({code:E.API_CALL_ABORTED,message:"connection closed"}))}getDTLSTransportState(){if(!this._peerConnection)return Ye;let e=null;if(this._isUplink){if(!sr()||this._peerConnection.getSenders().length===0)return Ye;e=this._peerConnection.getSenders()[0].transport}else{if(!Ti()||this._peerConnection.getReceivers().length===0)return Ye;e=this._peerConnection.getReceivers()[0].transport}return e?e.state:Ye}onConnectionStateChange(e){let t=this._peerConnection.iceConnectionState,i=this.getDTLSTransportState();if(this._log.info(`connectionState: ${e.target.connectionState}, ICE: ${t}, DTLS: ${i}`),e.target.connectionState===Q.CONNECTING&&this.emitConnectionStateChangedEvent("CONNECTING"),e.target.connectionState===Q.FAILED||e.target.connectionState===Q.CLOSED){let s=`connection ${e.target.connectionState}. ICE Transport state: ${t}, DTLS Transport state: ${i}`,o=new A({message:s,code:E.ICE_TRANSPORT_ERROR});this.emitConnectionStateChangedEvent("DISCONNECTED"),this.startReconnection(),this._isErrorObserved||this._emitter.emit("error",o)}(e.target.connectionState===Q.CONNECTED||e.target.connectionState===Q.COMPLETED)&&(this.logSelectedCandidate(),Y.logSuccessEvent({userId:this._room.userId,eventType:ye.ICE_CONNECTION_STATE}),this.emitConnectionStateChangedEvent("CONNECTED"))}emitConnectionStateChangedEvent(e){return e===this._currentState?!1:(e==="CONNECTED"?(qc=0,Qc=!1,Zc=!0,dn.add(this)):dn.delete(this),_.emit(m.PEER_CONNECTION_STATE_CHANGED,{room:this._room,prevState:this._currentState,state:e,remoteUserId:this._isUplink?void 0:this.userId}),this._emitter.emit("connection-state-changed",{prevState:this._currentState,state:e}),this._currentState=e,!0)}getPeerConnection(){return this._peerConnection}getRoom(){return this._room}getUserId(){return this.userId}getTinyId(){return this.tinyId}async logSelectedCandidate(){if(!this._peerConnection)return;let e=await this._peerConnection.getStats();for(let[,t]of e)if(zt(t)){let i=e.get(t.localCandidateId),s=e.get(t.remoteCandidateId);i&&(this._log.info(`local candidate: ${i.candidateType} ${i.protocol}:${i.ip||i.address}:${i.port} ${i.networkType||""} ${i.candidateType==="relay"?`relayProtocol:${i.relayProtocol}`:""}`),this._localAddress=`${i.ip||i.address}:${i.port}`),s&&(this._log.info(`remote candidate: ${s.candidateType} ${s.protocol}:${s.ip||s.address}:${s.port}`),this._remoteAddress=`${s.protocol}:${s.ip||s.address}`);break}}getCurrentState(){return this._currentState}waitForPeerConnectionConnected(){return this._waitForPeerConnectionConnectedPromise?this._waitForPeerConnectionConnectedPromise:(this._waitForPeerConnectionConnectedPromise=new Promise((e,t)=>{if(this._currentState==="CONNECTED")return e();this._waitForPeerConnectionConnectedPromiseReject=t;let i=a=>{a.state==="CONNECTED"&&(clearTimeout(n),o(),e())},s=({room:a})=>{a===this._room&&(clearTimeout(n),o(),t(new A({code:E.API_CALL_ABORTED,message:v({key:b.CONNECTION_ABORTED,data:"leave room"})})))},o=()=>{_.off(m.LEAVE_SUCCESS,s,this),this._emitter.off("connection-state-changed",i,this)},n=setTimeout(()=>{o();let a=new A({code:E.API_CALL_TIMEOUT,message:"connection timeout"});qc+=1,fm(this._signalChannel.isConnected)&&(this._log.warn("firewall restrition"),Qc=!0,this._emitter.emit("firewall-restriction")),t(a)},zr);_.on(m.LEAVE_SUCCESS,s,this),this._emitter.on("connection-state-changed",i,this)}),this._waitForPeerConnectionConnectedPromise=this._waitForPeerConnectionConnectedPromise.finally(()=>{this._waitForPeerConnectionConnectedPromise=null,this._waitForPeerConnectionConnectedPromiseReject=null}),this._waitForPeerConnectionConnectedPromise)}getReconnectionCount(){return this._reconnectionCount}startReconnection(){this._isReconnecting=!0,this.reconnect()}clearReconnectionTimer(){this._reconnectionTimer!==-1&&(clearTimeout(this._reconnectionTimer),this._reconnectionTimer=-1)}stopReconnection(){this._log.info("stop reconnection"),this._isReconnecting=!1,this._reconnectionCount=0,this.clearReconnectionTimer(),this._signalChannel.off(se.CONNECTED,this.reconnect,this)}beforeReconnect(){if(this._reconnectionTimer!==-1)return this._log.warn("reconnect() is reconnecting, ignore"),-1;if(this._reconnectionCount>=vt()){this._log.warn(`SDK has tried reconnect for ${this._reconnectionCount} times, but all failed, please check your network`),this.stopReconnection();let e=new A({code:this._isUplink?E.UPLINK_RECONNECTION_FAILED:E.DOWNLINK_RECONNECTION_FAILED,message:v({key:this._isUplink?b.UPLINK_RECONNECTION_FAILED:b.DOWNLINK_RECONNECTION_FAILED})});return this.emitConnectionStateChangedEvent("DISCONNECTED"),this._emitter.emit("error",e),-1}return this._signalChannel.isConnected?(this._reconnectionCount+=1,this._log.warn(`reconnect() trying [${this._reconnectionCount}]`),1):(this._log.warn("reconnect() signal channel is not connected, suspend reconnection until signal is connected"),this._signalChannel.once(se.CONNECTED,this.reconnect,this),-1)}on(e,t,i){this._emitter.on(e,t,i)}off(e,t,i){this._emitter.off(e,t,i)}getIsReconnecting(){return this._isReconnecting}get isH264(){return!!this._peerConnection?.remoteDescription?.sdp.includes("H264")}};var rd=pe(id());var ne=function(r){return rd.default.parse(r)},He=function(r){return rd.default.write(r)},vl=function(r){let e=ne(r);return e.media.forEach(t=>{t.type===l.AUDIO&&t.fmtp.forEach(i=>{i.config+=";sprop-stereo=1;stereo=1"})}),He(e)};function Dl(r){let e=ne(r);return e.media.forEach(t=>{if(t.type===l.VIDEO){let i=new Set;t.rtp.forEach(({payload:o,codec:n})=>n==="H264"&&i.add(o)),t.fmtp.forEach(({payload:o,config:n})=>{let a=n.match(/apt=(\d+)/);a&&a[1]&&i.has(Number(a[1]))&&i.add(o)});let s=({payload:o})=>!i.has(o);t.rtp=t.rtp.filter(s),t.rtcpFb=t.rtcpFb?.filter(s),t.fmtp=t.fmtp.filter(s),t.payloads=t.payloads?.split(" ").filter(o=>!i.has(Number(o))).join(" ")}}),He(e)}function un(r){return Object.keys(r).filter(e=>r[e])}var ys=class extends Le{_flag=0;role="anchor";remoteAudioTrack;remoteVideoTrack;remoteAuxiliaryTrack;ssrc={audio:0,video:0,auxiliary:0};_isSDPExchanging=!1;constructor(e){super({...e,isUplink:!1}),this.flag=e.flag,this.remoteAudioTrack=e.remoteAudioTrack||new Mt(this._room,this),this.remoteVideoTrack=e.remoteVideoTrack||new Et(this._room,this),this.remoteAuxiliaryTrack=e.remoteAuxiliaryTrack||new dr(this._room,this)}get subscribeState(){let e={audio:!1,video:!1,auxiliary:!1,smallVideo:!1};return this.remoteVideoTrack.isSubscribed&&(this.remoteVideoTrack.mediaType&8?e.smallVideo=!0:e.video=!0),this.remoteAudioTrack.isSubscribed&&(e.audio=!0),this.remoteAuxiliaryTrack.isSubscribed&&(e.auxiliary=!0),e}get muteState(){return Kt(this.flag,this.userId)}get flag(){return this._flag}set flag(e){e!==this._flag&&(this._flag=e,this.remoteAudioTrack?.onFlagChanged(),this.remoteVideoTrack?.onFlagChanged(),this.remoteAuxiliaryTrack?.onFlagChanged())}get hasMainStream(){return this.muteState.hasAudio||this.muteState.hasVideo||this.muteState.hasSmall}get hasAuxStream(){return this.muteState.hasAuxiliary}get isMainStreamSubscribed(){return(this.subscribeState.audio||this.subscribeState.video||this.subscribeState.smallVideo)&&(this.muteState.hasAudio||this.muteState.hasVideo||this.muteState.hasSmall)}get isAuxStreamSubscribed(){return this.subscribeState.auxiliary&&this.muteState.hasAuxiliary}get isSmallStreamSubscribed(){return this.subscribeState.smallVideo&&this.muteState.hasSmall}get isBigStreamSubscribed(){return this.subscribeState.video&&this.muteState.hasVideo}isStreamUnpublished(e){return e===l.MAIN?!this.muteState.hasAudio&&!this.muteState.hasVideo:!this.muteState.hasAuxiliary}initialize(){super.initialize(),this.installEvents(),this._peerConnection.ontrack=this.onTrack.bind(this)}close(e){super.close(e),this.emitConnectionStateChangedEvent("DISCONNECTED"),this.remoteAudioTrack.close(),this.remoteVideoTrack.close(),this.remoteAuxiliaryTrack.close(),this.uninstallEvents()}installEvents(){}uninstallEvents(){this._emitter.removeAllListeners()}emitConnectionStateChangedEvent(e){let t=this._currentState,i=super.emitConnectionStateChangedEvent(e);return i&&t!==e&&(this.remoteVideoTrack?.emit("connection-state-changed",{prevState:t,state:e}),this.remoteAuxiliaryTrack?.emit("connection-state-changed",{prevState:t,state:e})),i}onTrack(e){let t=e.streams[0],{track:i}=e,s=t.id===Jr?l.MAIN:l.AUXILIARY;this._log.debug(`ontrack ${s} ${i.kind}`);let o=l.AUDIO;i.kind===l.VIDEO&&(o=s===l.MAIN?l.VIDEO:l.AUXILIARY);let n=this.remoteAudioTrack;o===l.VIDEO?n=this.remoteVideoTrack:o===l.AUXILIARY&&(n=this.remoteAuxiliaryTrack),n.setInputMediaStreamTrack(i)}addRRTRLine(e){let t=e.split(`\r
`),i=new Map;t.forEach((o,n)=>{/^a=rtcp-fb:/.test(o)&&t[n+1]&&!/^a=rtcp-fb:/.test(t[n+1])&&i.set(n+1,`${o.match(/^a=rtcp-fb:\d+/)[0]} rrtr`)});let s=[...i];for(let o=0;o<s.length;o++){let[n,a]=s[o];t.splice(n+o,0,a)}return t.join(`\r
`)}addSPSDescription(e){let t=ne(e);return t.media.forEach(i=>{i.type===l.VIDEO&&i.fmtp.forEach(s=>{s.config+=";sps-pps-idr-in-keyframe=1"})}),He(t)}removeSDESDescription(e){let t=["urn:ietf:params:rtp-hdrext:sdes:mid","urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id","urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id"],i=ne(e);return i.media.forEach(s=>{!s.ext||(s.ext=s.ext.filter(o=>!t.includes(o.uri)))}),He(i)}isSubscriptionStateNotChanged(e){return JSON.stringify(e)===JSON.stringify(this.subscribeState)}async subscribe(e,t){try{if((this._peerConnection?.connectionState===Q.NEW||this._peerConnection?.connectionState===Q.CONNECTING)&&await this.waitForPeerConnectionConnected(),this.isSubscriptionStateNotChanged(e)){this._peerConnection||(this.initialize(),await this.connect(e));return}if(this._log.info(`subscribe ${t} ${JSON.stringify(e)}`),this._peerConnection||this._isSDPExchanging){let i="subscribe_change";Object.values(e).find(s=>s===!0)||(i="unsubscribe"),await this.sendSubscription(i,e)}else this.initialize(),await this.connect(e)}catch(i){throw this._room.isJoined&&this.isStreamUnpublished(t)?(this._log.warn(`${i.message} ${JSON.stringify(this.muteState)}`),new A({code:E.REMOTE_STREAM_NOT_EXIST,message:`remote user ${this.userId} unpublished stream`})):i}}async unsubscribe({remoteTracks:e,streamType:t}){if(this._currentState==="CONNECTED"&&(t==="main"&&!this.isMainStreamSubscribed||t==="auxiliary"&&!this.isAuxStreamSubscribed)){this._log.info(`${t} stream already unsubscribed`);return}let i={...this.subscribeState};e.forEach(o=>{switch(o.mediaType){case 1:i.audio=!1;break;case 4:i.video=!1;break;case 8:i.smallVideo=!1;break;case 2:i.auxiliary=!1;break;default:break}});let s="subscribe_change";Object.values(i).find(o=>o===!0)||(s="unsubscribe"),this._log.info(`${s==="unsubscribe"?s:"subscribe"} ${t} [${un(i)}]`),await this.sendSubscription(s,i),s==="unsubscribe"&&(this.closePeerConnection(),this.emitConnectionStateChangedEvent("DISCONNECTED"))}sendSubscription(e,t=this.subscribeState){let i={srcTinyId:this.tinyId,srcUserId:this.userId},s=H.UNSUBSCRIBE,o=V.UNSUBSCRIBE_RESULT;return e==="subscribe_change"&&(i={audio:t.audio,bigVideo:t.video,auxVideo:t.auxiliary,smallVideo:t.smallVideo,srcTinyId:this.tinyId},s=H.SUBSCRIBE_CHANGE,o=V.SUBSCRIBE_CHANGE_RESULT),this._signalChannel.sendWaitForResponse({command:s,data:i,responseCommand:o,timeout:1e4}).then(({data:n})=>{if(n.code!==0){let a=new A({code:n.code,message:v({key:b.ERROR_MESSAGE,data:{type:e,message:n.message}})});throw this._log.error(a),a}})}async connect(e=this.subscribeState){try{await this.exchangeSDP(e),await this.waitForPeerConnectionConnected()}catch(t){throw this.closePeerConnection(!0),t}}async exchangeSDP(e){try{this._isSDPExchanging=!0,await this.createOffer(),this._log.info("createOffer success, sending offer");let{type:t,sdp:i}=this._peerConnection.localDescription,s={type:t,sdp:i,srcUserId:this.userId,srcTinyId:this.tinyId,audio:e.audio,bigVideo:e.video,auxVideo:e.auxiliary,smallVideo:e.smallVideo},o=await this._signalChannel.sendWaitForResponse({command:H.SUBSCRIBE,commandDesc:"exchange sdp",data:s,responseCommand:V.SUBSCRIBE_RESULT,timeout:Na});if(!this._peerConnection){let n=new A({code:E.INVALID_OPERATION,message:v({key:b.CONNECTION_CLOSED})});throw this._log.warn(n),n}await this.onSubscribeResult(o),this._isSDPExchanging=!1}catch(t){throw this._isSDPExchanging=!1,t}}async createOffer(){let e={voiceActivityDetection:!1};Be()&&this._sdpSemantics===Wt?(this._peerConnection.addTransceiver(l.AUDIO,{direction:$.RECVONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:$.RECVONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:$.RECVONLY})):(e.offerToReceiveAudio=!0,e.offerToReceiveVideo=!0);let t=await this._peerConnection.createOffer(e);if(t.sdp){let{isH264DecodeSupported:i}=await Do();i||(this._log.warn("remove h264 desc from sdp"),t.sdp=Dl(t.sdp)),t.sdp=this.addRRTRLine(t.sdp),t.sdp=this.addSPSDescription(t.sdp),t.sdp=vl(t.sdp),this._sdpSemantics===Wt&&(t.sdp=this.removeSDESDescription(t.sdp))}await this._peerConnection.setLocalDescription(t)}async onSubscribeResult(e){let{code:t,message:i=""}=e&&e.data||{},{type:s,sdp:o}=e&&e.data&&e.data.data||{};if(t===hi)throw new A({code:E.NOT_SUPPORTED_H264,message:v({key:b.NOT_SUPPORTED_H264DECODE})});try{if(t!==0)throw new A({code:t,message:v({key:b.EXCHANGE_SDP_FAILED,data:{errMsg:i}})});this._log.debug(`accept remote answer: ${o}`),await this._peerConnection.setRemoteDescription({type:s,sdp:o}),this._sei&&(this._sei.handleEncodedStreams(),this._sei.onSEIMessage=n=>{this._emitter.emit("sei-message",{...n,userId:this.userId})}),this.updateSSRC(o)}catch(n){throw this._log.error(n),n}}updateSSRC(e){try{ne(e).media.forEach(i=>{if(!!i.ssrcs)if(i.type===l.AUDIO){let s=i.ssrcs.find(o=>o.value?.includes(Jr));s&&(this.ssrc.audio=Number(s.id))}else{let s=i.ssrcs.find(n=>n.value?.includes(Jr)),o=i.ssrcs.find(n=>n.value?.includes(Ra));s&&(this.ssrc.video=Number(s.id)),o&&(this.ssrc.auxiliary=Number(o.id))}})}catch{}}getMainStreamVideoTrackId(){return this.remoteVideoTrack&&this.remoteVideoTrack.mediaTrack?this.remoteVideoTrack.mediaTrack.id:""}getAuxStreamVideoTrackId(){return this.remoteAuxiliaryTrack&&this.remoteAuxiliaryTrack.mediaTrack?this.remoteAuxiliaryTrack.mediaTrack.id:""}async reconnect(){if(!(super.beforeReconnect()<0))try{this.closePeerConnection(),this.initialize(),await this.connect(),this.stopReconnection(),this._log.warn("reconnect() success")}catch{let i=we(this._reconnectionCount);this._log.warn(`reconnect() timeout, try again after ${i/1e3}s`),this._reconnectionTimer=setTimeout(()=>{this.clearReconnectionTimer(),this.reconnect()},i)}}getIsReconnecting(){return this._isReconnecting}clearReconnectionTimer(){this._reconnectionTimer!==-1&&(clearTimeout(this._reconnectionTimer),this._reconnectionTimer=-1)}getCurrentState(){return this._currentState}setDelay({audioDelay:e,videoDelay:t}){this.remoteAudioTrack.stat.end2EndDelay=e,this.remoteVideoTrack.stat.end2EndDelay=t}};y([F(r=>function(...e){return new Promise((t,i)=>{let s=o=>{this._emitter.off("closed",s),i(new A({code:E.API_CALL_ABORTED,message:v({key:b.CONNECTION_ABORTED,data:o})}))};this._emitter.on("closed",s),r.apply(this,e).then(t,i).finally(()=>{this._emitter.off("closed",s)})})})],ys.prototype,"subscribe",1),y([Uo(Le.prototype.afterConnect),Vo(Le.prototype.beforeConnect)],ys.prototype,"connect",1);var sd=ys;var kl={voiceActivityDetection:!1},bs=class extends Le{localMainAudioTrack=null;localMainVideoTrack=null;localAuxAudioTrack=null;localAuxVideoTrack=null;ssrc={audio:0,video:0,small:0,auxiliary:0};_isPublishingAux=!1;_publishingLocalAudioTrack;_publishingLocalVideoTrack;_mediaSettings={videoCodec:"",videoWidth:0,videoHeight:0,videoBps:0,videoFps:0,audioCodec:"opus",audioFs:0,audioChannel:0,audioBps:0,smallVideoWidth:0,smallVideoHeight:0,smallVideoFps:0,smallVideoBps:0,auxVideoWidth:0,auxVideoHeight:0,auxVideoFps:0,auxVideoBps:0};_audioManager;constructor(e){super({...e,isUplink:!0}),this._audioManager=e.audioManager}get isMainStreamPublished(){return!!(this.localMainAudioTrack||this.localMainVideoTrack)}get isAuxStreamPublished(){return!!(this.localAuxVideoTrack||this.localAuxAudioTrack)}get publishState(){let e={audio:!1,bigVideo:!1,smallVideo:!1,auxVideo:!1};if(this._peerConnection){let t=this._peerConnection.getSenders();t&&(pt()?(e.audio=!!t[0]?.track,e.bigVideo=!!t[1]?.track,e.smallVideo=!!t[2]?.track,e.auxVideo=!!t[3]?.track):t.forEach(i=>{i.track&&(i.track.kind===l.AUDIO?e.audio=!0:(e.bigVideo=!0,this._room.videoManager.hasSmall&&(e.smallVideo=!0)))}))}return e}initialize(){super.initialize(),this.installEvents()}reset(){this._isReconnecting&&this.stopReconnection(),this.closePeerConnection(),this.uninstallEvents()}close(e){super.close(e),this.reset(),this.emitConnectionStateChangedEvent("DISCONNECTED")}installEvents(){this._emitter.listeners("connection-state-changed").includes(this.handleConnectionStateChange)||this._emitter.on("connection-state-changed",this.handleConnectionStateChange,this)}uninstallEvents(){this._emitter.off("connection-state-changed",this.handleConnectionStateChange,this)}emitConnectionStateChangedEvent(e,t){let i=this._currentState,s=super.emitConnectionStateChangedEvent(e);return s&&i!==e&&(t?t.emit("connection-state-changed",{prevState:i,state:e}):(this.localMainVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}),this.localAuxVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}),this._publishingLocalVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}))),s}async publish({localAudioTrack:e,localVideoTrack:t,isAuxiliary:i}){this._peerConnection||this.initialize(),e&&(this._publishingLocalAudioTrack=e),t&&(this._publishingLocalVideoTrack=t),this._isPublishingAux=i;let s;t&&!i&&t.small&&(s=this._room.videoManager.smallTrack),this.sendMediaSettings(),Be()?await this.publishByTransceiver({localAudioTrack:e,localVideoTrack:t,smallTrack:s,isAuxiliary:i}):await this.publishByAddTrack({localAudioTrack:e,localVideoTrack:t,smallTrack:s}),this._publishingLocalAudioTrack=null,this._publishingLocalVideoTrack=null,this._isPublishingAux=!1,i?(t&&(this.localAuxVideoTrack=t),e&&(this.localAuxAudioTrack=e)):(t&&(this.localMainVideoTrack=t),e&&(this.localMainAudioTrack=e)),this._sei?.handleEncodedStreams(),this.installTrackMuteEvents(e,t),this.sendMutedFlag()}async publishByTransceiver({localAudioTrack:e,localVideoTrack:t,smallTrack:i,isAuxiliary:s}){this._log.info("publish by transceiver");let o=new MediaStream,n=t?.outMediaTrack,a=this._audioManager.mixedAudioTrack;a&&o.addTrack(a),n&&o.addTrack(n);let c=this._peerConnection.getTransceivers();if(c.length===0)this._peerConnection.addTransceiver(a||l.AUDIO,{direction:$.SENDONLY,streams:[o]}),this._peerConnection.addTransceiver(s?l.VIDEO:n||l.VIDEO,{direction:$.SENDONLY,streams:[o]}),this._peerConnection.addTransceiver(i||l.VIDEO,{direction:$.SENDONLY,streams:[o]}),this._peerConnection.addTransceiver(s?n||l.VIDEO:l.VIDEO,{direction:$.SENDONLY,streams:[o]}),await this.connect();else{let d=[];if(a&&(c[0].sender.track||d.push(0),await c[0].sender.replaceTrack(a),await this.setBandwidth({bandwidth:e?.profile.bitrate||40,type:l.AUDIO})),n){let u=s?3:1;await c[u].sender.replaceTrack(n),await this.setBandwidth({bandwidth:t.profile.bitrate,type:l.VIDEO,videoType:s?l.AUXILIARY:l.BIG}),d.push(u),i&&(await c[2].sender.replaceTrack(i),await this.setBandwidth({bandwidth:t.small.bitrate,type:l.VIDEO,videoType:l.SMALL}),d.push(2))}await this.setTransceiverDirection($.SENDONLY,d),await this.doPublishChange(),t?.emit("connection-state-changed",{prevState:"DISCONNECTED",state:"CONNECTING"}),t?.emit("connection-state-changed",{prevState:"CONNECTING",state:"CONNECTED"})}}async publishByAddTrack({localAudioTrack:e,localVideoTrack:t,smallTrack:i}){this._log.info("publish by addtrack");let s=t?.outMediaTrack,o=this._audioManager.mixedAudioTrack;if(this._peerConnection&&this._peerConnection.connectionState!=="new"){o&&await this.addTrack(e),s&&await this.addTrack(t);return}let n=new MediaStream;if(o&&n.addTrack(o),s&&n.addTrack(s),o&&this._peerConnection.addTrack(o,n),s&&(this._peerConnection.addTrack(s,n),i)){let a=new MediaStream;a.addTrack(i),this._peerConnection.addTrack(i,a)}await this.connect()}async enableSmall(e){let t=this._peerConnection.getTransceivers();e?this._room.videoManager.smallTrack&&(await t[2].sender.replaceTrack(this._room.videoManager.smallTrack),await this.setTransceiverDirection($.SENDONLY,[2])):(await t[2].sender.replaceTrack(null),await this.setTransceiverDirection($.INACTIVE,[2])),this.updateMediaSettings(),await this.doPublishChange()}installTrackMuteEvents(...e){e.forEach(t=>{t&&(t?.on("mute",this.sendMutedFlag,this),t?.on("unmute",this.sendMutedFlag,this))})}uninstallTrackMuteEvents(...e){e.forEach(t=>{t&&(t?.off("mute",this.sendMutedFlag,this),t?.off("unmute",this.sendMutedFlag,this))})}async unpublish({localAudioTrack:e,localVideoTrack:t}){if(!pt()){if(e&&e.outMediaTrack&&!t&&this.localMainVideoTrack){await this.removeTrack(e),this.localMainAudioTrack=null;return}if(t&&t.outMediaTrack&&!e&&this.localMainAudioTrack){await this.removeTrack(t),this.localMainVideoTrack=null;return}await this.doUnpublish(),this.uninstallTrackMuteEvents(e,t),this.emitConnectionStateChangedEvent("DISCONNECTED",t);return}let i=t&&t===this.localAuxVideoTrack,s=t?.outMediaTrack,o=this._peerConnection.getSenders(),n=[];e&&(this._audioManager.mixedAudioTrack||(await o[0].replaceTrack(null),n.push(0)),i?this.localAuxAudioTrack=null:this.localMainAudioTrack=null),s&&(i?(await o[3].replaceTrack(null),this.localAuxVideoTrack=null,this._mediaSettings={...this._mediaSettings,auxVideoBps:0,auxVideoFps:0,auxVideoWidth:0,auxVideoHeight:0},n.push(3)):(await o[1].replaceTrack(null),await o[2].replaceTrack(null),this.localMainVideoTrack=null,this._mediaSettings={...this._mediaSettings,videoWidth:0,videoHeight:0,videoBps:0,videoFps:0,audioFs:0,audioChannel:0,audioBps:0,smallVideoWidth:0,smallVideoHeight:0,smallVideoFps:0,smallVideoBps:0},n.push(1,2))),this.isMainStreamPublished||this.isAuxStreamPublished?(await this.setTransceiverDirection($.INACTIVE,n),await this.doPublishChange(!1)):await this.doUnpublish(),this.uninstallTrackMuteEvents(e,t),t?.emit("connection-state-changed",{prevState:this._currentState,state:"DISCONNECTED"})}async doPublishChange(e=!0){let t={state:this.publishState,constraintConfig:this._mediaSettings},i=await this._signalChannel.sendWaitForResponse({command:H.PUBLISH_STATE_CHANGE,data:t,responseCommand:V.PUBLISH_STATE_CHANGE_RESULT,enableLog:e});this.checkPublishResultCode(i.data.code,i.data.message)}doUnpublish(e=!1){return this._signalChannel.sendWaitForResponse({command:H.UNPUBLISH,commandDesc:"unpublish",responseCommand:V.UNPUBLISH_RESULT,enableLog:e}).catch(t=>{if(t.getCode()===E.API_CALL_TIMEOUT)return Promise.resolve();throw t})}updateMediaSettings(){let{detail:{isH264EncodeSupported:e,isVp8EncodeSupported:t}}=this._room.checkSystemResult;e?this._mediaSettings.videoCodec="H264":t&&(this._mediaSettings.videoCodec="VP8");let i=this._publishingLocalAudioTrack||this.localMainAudioTrack||this.localAuxAudioTrack,{localMainVideoTrack:s,localAuxVideoTrack:o}=this;if(this._publishingLocalVideoTrack&&(this._isPublishingAux?o=this._publishingLocalVideoTrack:s=this._publishingLocalVideoTrack),$e){if(i&&i.outMediaTrack){let n=i.outMediaTrack.getSettings();this._mediaSettings.audioChannel=n.channelCount||1,this._mediaSettings.audioBps=i.profile.bitrate*1e3,this._mediaSettings.audioFs=n.sampleRate||0}if(s&&s.outMediaTrack){let n=s.outMediaTrack.getSettings();this._mediaSettings.videoWidth=n.width||0,this._mediaSettings.videoHeight=n.height||0,this._mediaSettings.videoFps=n.frameRate||0,this._mediaSettings.videoBps=s.profile.bitrate*1e3,s.small&&(this._mediaSettings.smallVideoWidth=s.small.width,this._mediaSettings.smallVideoHeight=s.small.height,this._mediaSettings.smallVideoFps=s.small.frameRate,this._mediaSettings.smallVideoBps=s.small.bitrate*1e3)}if(o&&o.outMediaTrack){let n=o.outMediaTrack.getSettings();this._mediaSettings.auxVideoWidth=n.width||0,this._mediaSettings.auxVideoHeight=n.height||0,this._mediaSettings.auxVideoFps=n.frameRate||0,this._mediaSettings.auxVideoBps=o.profile.bitrate*1e3}}else i&&i.outMediaTrack&&(this._mediaSettings.audioChannel=i.profile.channelCount,this._mediaSettings.audioBps=i.profile.bitrate*1e3,this._mediaSettings.audioFs=i.profile.sampleRate),s&&s.outMediaTrack&&(this._mediaSettings.videoWidth=s.profile.width,this._mediaSettings.videoHeight=s.profile.height,this._mediaSettings.videoFps=s.profile.frameRate,this._mediaSettings.videoBps=s.profile.bitrate*1e3);this._log.info(`updateMediaSettings: ${JSON.stringify(this._mediaSettings)}`)}sendMediaSettings(){this.updateMediaSettings(),this._signalChannel.sendWaitForResponse({command:H.UPDATE_CONSTRAINT_CONFIG,data:this._mediaSettings,responseCommand:V.UPDATE_CONSTRAINT_CONFIG_RES}).then(e=>{e.data.code!==0&&this._log.warn(e.data.message)}).catch(()=>{})}async addTrack(e){if(!this._peerConnection)return;let t=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is adding ${e.kind} track to current published local ${t?l.AUXILIARY:l.MAIN} stream`),this._sei?.handleEncodedStreams(),Be()?await this.addTrackByTransceiver(e,t):await this.addTrackBySender(e)}async addTrackByTransceiver(e,t){if(!e.mediaTrack)return;let i=this._peerConnection.getTransceivers();if(e.kind===l.AUDIO)await i[0].sender.replaceTrack(this._audioManager.mixedAudioTrack);else{let s=t?3:1;await i[s].sender.replaceTrack(e.outMediaTrack),s===1&&this.localMainVideoTrack?.small&&await i[2].sender.replaceTrack(this._room.videoManager.smallTrack),i[s].direction===$.INACTIVE&&await this.setTransceiverDirection($.SENDONLY,[s])}this.updateMediaSettings(),await this.doPublishChange()}async addTrackBySender(e){if(!e.outMediaTrack)return;let t=e.outMediaTrack;pt()&&this._peerConnection.getTransceivers().findIndex(o=>o.direction==="stopped")>=0&&(this._log.warn("transceiver is stopping, negotiate sdp first"),await this.updateOffer("remove",t));let i=this._peerConnection.getSenders().find(o=>o.track&&o.track.kind===t.kind);if(i&&i.track){this._log.warn("sender already exists, remove sender first");let o=i.track;this.removeSender(i),await this.updateOffer("remove",o)}let s=t.kind===l.VIDEO?t:this._audioManager.mixedAudioTrack;if(s&&this._peerConnection.addTrack(t,new MediaStream([s])),t.kind===l.VIDEO&&e instanceof fe&&e.small){let o=new MediaStream,{smallTrack:n}=this._room.videoManager;o.addTrack(n),this._peerConnection.addTrack(n,o)}await this.updateOffer("add",t)}isNeedToResetOfferOrder(){if(this._sdpSemantics===Xi||!this._peerConnection||!this._peerConnection.localDescription)return!1;let{sdp:e}=this._peerConnection.localDescription,t=ne(e);for(let i=0;i<t.media.length;i++)if(Number(t.media[i].mid)===0&&t.media[i].type===l.VIDEO)return!0;return!1}removeSender(e){let t=null;pt()&&(t=this._peerConnection.getTransceivers().find(i=>i.sender&&i.sender.track===e.track)),this._peerConnection.removeTrack(e),t&&W(t.stop)&&(this._log.info("stop transceiver"),t.stop())}async removeTrack(e){if(!this._peerConnection)return;let t=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is removing ${e.kind} track from current published local ${t?l.AUXILIARY:l.MAIN} stream`),Be()?await this.removeTrackByTransceiver(e,t):await this.removeTrackBySender(e)}async removeTrackByTransceiver(e,t){if(!e.outMediaTrack)return;let i=this._peerConnection.getTransceivers();if(e.kind===l.AUDIO)this._audioManager.mixedAudioTrack||await i[0].sender.replaceTrack(null);else{let s=t?3:1;await i[s].sender.replaceTrack(null),s===1&&e.small&&await i[2].sender.replaceTrack(null),await this.setTransceiverDirection($.INACTIVE,[s])}this.updateMediaSettings(),await this.doPublishChange()}async setTransceiverDirection(e,t){if(!z)return;let i=!1,s=!1;this._log.info(`setting transceiver ${t.join(",")} direction to ${e}`);let o=this._peerConnection.getTransceivers();if(t.forEach(c=>{o[c].direction!==e&&(o[c].direction=e,i=!0)}),i){this._log.info("updating offer");let c=await this._peerConnection.createOffer();await this._peerConnection.setLocalDescription(c)}let n=-1,a=this._peerConnection.remoteDescription.sdp.split(`\r
`).map(c=>{if(c.match(new RegExp(`a=(${$.INACTIVE}|${$.RECVONLY}|${$.SENDONLY})`))&&n++,t.includes(n)){if(e===$.INACTIVE&&c.includes(`a=${$.RECVONLY}`))return s=!0,`a=${e}`;if(e===$.SENDONLY&&c.includes(`a=${$.INACTIVE}`))return s=!0,`a=${$.RECVONLY}`}return c}).join(`\r
`);s&&(this._log.info("updating answer"),await this._peerConnection.setRemoteDescription({type:"answer",sdp:a}))}async removeTrackBySender(e){if(!e.outMediaTrack)return;if(e.kind===l.VIDEO&&this.isNeedToResetOfferOrder()&&this.localMainAudioTrack){this.reset(),this.initialize(),await this.publish({localAudioTrack:this.localMainAudioTrack,isAuxiliary:!1});return}let t=this._peerConnection.getSenders().find(i=>i.track===e.outMediaTrack);t&&(this.removeSender(t),e.kind===l.VIDEO&&e.small&&this._peerConnection.getSenders().forEach(i=>{i.track&&i.track.kind===l.VIDEO&&this.removeSender(i)})),await this.updateOffer("remove",e.outMediaTrack)}async replaceTrack(e){let t=this._peerConnection?.getSenders();if(!t||t.length===0||!e.mediaTrack)return;let i;if(Be()?i=e.kind===l.AUDIO?t[0]:t[1]:i=t.find(o=>o.track&&o.track.kind===e.kind),!i)return;let s=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is replacing ${e.kind} track on ${s?l.AUXILIARY:l.MAIN} stream`),e.kind===l.AUDIO?await i.replaceTrack(this._audioManager.mixedAudioTrack):e.kind===l.VIDEO&&(s?t[3]&&await t[3].replaceTrack(e.outMediaTrack):await i.replaceTrack(e.outMediaTrack))}async updateOffer(e,t){try{let i=await this._peerConnection.createOffer(kl);z&&i.sdp&&(i.sdp=this.setSDPDirection(i.sdp,"sendrecv")),await this._peerConnection.setLocalDescription(i);let s=this.updateMediaSettings(),o={action:e,trackId:t.id,kind:t.kind===l.VIDEO?"bigVideo":t.kind,type:"offer",sdp:this._peerConnection.localDescription.sdp,constraintConfig:s,state:this.publishState};this._log.info("createOffer success, sending updated offer to remote server"),this._log.debug(`updatedOffer: ${o.sdp}`);let n=await this._signalChannel.sendWaitForResponse({command:H.PUBLISH_CHANGE,data:o,responseCommand:V.UPDATE_OFFER_RESULT,timeout:ba,commandDesc:"update offer"}),{code:a,message:c}=n.data;a!==0&&this.checkPublishResultCode(a,c),await this.acceptAnswer(n.data.data),i.sdp&&this.updateSSRC(i.sdp)}catch(i){throw this._log.error(i),i}}async setBandwidth({bandwidth:e,type:t,videoType:i,sdp:s}){if(!os())return s?t===l.VIDEO?this.updateVideoBandwidthRestriction(s,e,i):this.updateAudioBandwidthRestriction(s,e):void 0;let o,n=this._peerConnection.getSenders();if(Be()){let a=0;t===l.VIDEO&&(i===l.SMALL?a=2:i===l.AUXILIARY?a=3:a=1),o=n[a]}else o=n.find(a=>a.track&&a.track.kind===t);if(o){let a=o.getParameters();(!a.encodings||a.encodings.length===0)&&(a.encodings=[{}]),a.encodings[0].maxBitrate=e*1e3;try{return await o.setParameters(a),this._log.info(`${i||""}${t} bandwidth ${e} kbps`),s}catch(c){if(this._log.info(`failed to set bandwidth by setting maxBitrate: ${c}`),s)return t===l.VIDEO?this.updateVideoBandwidthRestriction(s,e,i):this.updateAudioBandwidthRestriction(s,e)}}return s}updateVideoBandwidthRestriction(e,t,i){let s="AS";z&&(s="TIAS",t=t*1e3);let o=0,n=-1;return i===l.SMALL?o=1:i===l.AUXILIARY&&(o=2),e=e.replace(/m=video (.*)\r\nc=IN (.*)\r\n/g,a=>(n+=1,n===o?`${a}b=${s}:${t}\r
`:a)),e}updateAudioBandwidthRestriction(e,t){let i="AS";return z&&(i="TIAS",t=t*1e3),e=e.replace(/m=audio (.*)\r\nc=IN (.*)\r\n/,`m=audio $1\r
c=IN $2\r
b=${i}:${t}\r
`),e}removeBandwidthRestriction(e){return e.replace(/b=AS:.*\r\n/,"").replace(/b=TIAS:.*\r\n/,"")}removeVideoOrientation(e){return e.replace(/urn:3gpp:video-orientation/,"")}async connect(){try{await this.exchangeSDP(),await this.waitForPeerConnectionConnected()}catch(e){throw this.closePeerConnection(!0),this.uninstallEvents(),e}}async exchangeSDP(){try{await this.createOffer(),this._log.info("createOffer success, sending offer to remote server"),await this.doExchangeSDP()}catch(e){throw e}}async createOffer(){try{let e=await this._peerConnection.createOffer(kl);await this._peerConnection.setLocalDescription(e),e.sdp&&this.updateSSRC(e.sdp)}catch(e){throw e}}doExchangeSDP(){let e={command:H.PUBLISH,responseCommand:V.PUBLISH_RESULT,data:{type:this._peerConnection.localDescription.type,sdp:this.removeVideoOrientation(this._peerConnection.localDescription.sdp),screen:this.localMainVideoTrack instanceof ke||this.localAuxVideoTrack instanceof ke,state:this.publishState,constraintConfig:this._mediaSettings},enableLog:!1};return this._log.debug(`sending sdp offer: ${e.data.sdp}`),this._signalChannel.sendWaitForResponse(e).then(t=>{let{code:i,message:s,data:o}=t.data;return i===0?this.acceptAnswer(o):this.checkPublishResultCode(i,s)})}setSDPDirection(e,t,i="all"){let s=ne(e);return s.media.forEach(o=>{(i==="all"||o.type===i)&&(o.direction=t)}),He(s)}async acceptAnswer(e){try{let t;if(this._publishingLocalAudioTrack||this._publishingLocalVideoTrack||this.isMainStreamPublished){let o=this._publishingLocalVideoTrack?.profile.bitrate||this.localMainVideoTrack?.profile.bitrate,n=this._publishingLocalAudioTrack?.profile.bitrate||this.localMainAudioTrack?.profile.bitrate;if(o){let a=this._isPublishingAux?l.AUXILIARY:l.BIG;t=await this.setBandwidth({bandwidth:o,type:l.VIDEO,sdp:t,videoType:a})}n&&(t=await this.setBandwidth({bandwidth:n,type:l.AUDIO,sdp:t}))}if(t=this.removeVideoOrientation(e.sdp),this._publishingLocalVideoTrack?.small){let{smallStreamConfig:o}=this._room;t=await this.setBandwidth({bandwidth:this._publishingLocalVideoTrack.small.bitrate||o.bitrate,type:l.VIDEO,videoType:l.SMALL,sdp:t})}let s={type:e.type,sdp:t};await this._peerConnection.setRemoteDescription(s),this._log.debug(`accepted answer: ${t}`)}catch(t){throw this._log.error(`failed to accept remote answer ${t}`),t}}sendMutedFlag(e){if(e===this.localAuxAudioTrack||e===this.localAuxVideoTrack)return;let i={audio:!!this.localMainAudioTrack?.muted,bigVideo:!!this.localMainVideoTrack?.muted,auxVideo:!!this.localAuxVideoTrack?.muted};this._log.info(`send muted state: ${JSON.stringify(i)}`),this._signalChannel.send(H.UPDATE_MUTE_STAT,i)}getIsReconnecting(){return this._isReconnecting}async reconnect(){if(!(super.beforeReconnect()<0))try{await this._signalChannel.sendWaitForResponse({command:H.UNPUBLISH,responseCommand:V.UNPUBLISH_RESULT,enableLog:!1}),this.closePeerConnection(),this.initialize(),this.isMainStreamPublished&&await this.publish({localAudioTrack:this.localMainAudioTrack,localVideoTrack:this.localMainVideoTrack,isAuxiliary:!1}),this.isAuxStreamPublished&&await this.publish({localAudioTrack:this.localAuxAudioTrack,localVideoTrack:this.localAuxVideoTrack,isAuxiliary:!0}),this._log.warn("reconnect() uplink reconnect successfully"),this.stopReconnection()}catch{let i=we(this._reconnectionCount);this._log.warn(`reconnect() timeout, try again after ${i/1e3}s`),this._reconnectionTimer=setTimeout(()=>{this.clearReconnectionTimer(),this.reconnect()},i)}}handleConnectionStateChange(e){e.state==="CONNECTED"&&(this.localMainVideoTrack||this._publishingLocalVideoTrack&&!this._isPublishingAux)&&_.emit(m.SEND_FIRST_VIDEO_FRAME,{room:this._room})}updateSSRC(e){try{ne(e).media.forEach((i,s)=>{if(i.type===l.AUDIO){let o=i.ssrcs&&i.ssrcs[0];o&&(this.ssrc.audio=Number(o.id))}else{if(this._sdpSemantics===Xi&&i.ssrcGroups){i.ssrcGroups.forEach((n,a)=>{let c=Number(n.ssrcs.split(" ")[0]);a===0?this.ssrc.video=c:a===1&&(this.ssrc.small=c)});return}let o=i.ssrcs&&i.ssrcs[0];if(!o)return;switch(s){case 1:this.ssrc.video=Number(o.id);break;case 2:this.ssrc.small=Number(o.id);break;case 3:this.ssrc.auxiliary=Number(o.id);break;default:break}}})}catch{}}getVideoTrackId(e=l.VIDEO){if(this._peerConnection){let t=this._peerConnection.getSenders();if(e===l.AUXILIARY&&t[3]&&t[3].track)return t[3].track.id;if(e===l.VIDEO&&t[1]&&t[1].track)return t[1].track.id}if(this.localMainVideoTrack&&e===l.VIDEO){let t=this.localMainVideoTrack.mediaTrack;if(t)return t.id}if(this.localAuxVideoTrack&&e===l.AUXILIARY){let t=this.localAuxVideoTrack.mediaTrack;if(t)return t.id}return""}getSSRC(){return this.ssrc}checkPublishResultCode(e,t){if(e!==0)throw e===hi?(this._log.error(ue.NOT_SUPPORTED_H264ENCODE),new A({code:E.NOT_SUPPORTED_H264,message:v({key:b.NOT_SUPPORTED_H264ENCODE})})):new A({code:E.UNKNOWN,message:v({key:b.SIGNAL_RESPONSE_FAILED,data:{signalResponse:V.PUBLISH_RESULT,code:e,message:t}})})}sendSEI(e,t){this._sei?.push(e,t)}};y([F(r=>function(...e){return new Promise((t,i)=>{let s=o=>{this._emitter.off("closed",s),i(new A({code:E.API_CALL_ABORTED,message:v({key:b.CONNECTION_ABORTED,data:o})}))};this._emitter.on("closed",s),r.apply(this,e).then(t,i).finally(()=>{this._emitter.off("closed",s)})})})],bs.prototype,"publish",1),y([Uo(Le.prototype.afterConnect),Vo(Le.prototype.beforeConnect)],bs.prototype,"connect",1);var ln=bs;var Ns=class{constructor(e,t){this.room=e;this._log=t,this._prevReportTime=0,this._prevEncoderImplementation="",this._prevQualityLimitationReason="",this._prevAuxQualityLimitationReason=""}_log;_prevReportTime;_prevReport={};_prevEncoderImplementation;_prevQualityLimitationReason;_prevAuxQualityLimitationReason;_prevDecoderImplementationMap=new Map;totalBytesSent=0;totalBytesReceived=0;_spcStats=null;get statInterval(){return this._prevReportTime===0?2:(Date.now()-this._prevReportTime)/1e3}async getSenderStats(e){let t={audio:{bytesSent:0,packetsSent:0,audioLevel:0,totalAudioEnergy:0},video:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,fpsCapture:0},small:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,fpsCapture:0},auxiliary:{bytesSent:0,packetsSent:0,framesEncoded:0,frameWidth:0,frameHeight:0,framesSent:0,fpsCapture:0},rtt:0},i=e.getPeerConnection(),s=e.getSSRC();if(i)try{if((this._spcStats||await i.getStats()).forEach(n=>{if(n.type==="outbound-rtp")if((n.mediaType||n.kind)===l.VIDEO){let c,d;if(n.ssrc===s.video?(c=l.VIDEO,d=e.localMainVideoTrack):n.ssrc===s.small?c=l.SMALL:n.ssrc===s.auxiliary&&(d=e.localAuxVideoTrack,c=l.AUXILIARY),!c)return;t[c].bytesSent=n.bytesSent,t[c].packetsSent=n.packetsSent,t[c].framesEncoded=n.framesEncoded,f(n.keyFramesEncoded)||(t[c].keyFramesEncoded=n.keyFramesEncoded),f(n.nackCount)||(t[c].nackCount=n.nackCount),f(n.pliCount)||(t[c].pliCount=n.pliCount),f(n.retransmittedPacketsSent)||(t[c].retransmittedPacketsSent=n.retransmittedPacketsSent),f(n.totalEncodeTime)||(t[c].totalEncodeTime=n.totalEncodeTime),f(n.totalPacketSendDelay)||(t[c].totalPacketSendDelay=n.totalPacketSendDelay),n.ssrc===s.video?(!f(n.encoderImplementation)&&this._prevEncoderImplementation!==n.encoderImplementation&&(this._log.info(`encoderImplementation change to ${n.encoderImplementation}`),this._prevEncoderImplementation=n.encoderImplementation),!f(n.qualityLimitationReason)&&n.bytesSent!==0&&this._prevQualityLimitationReason!==n.qualityLimitationReason&&(this._log.info(`qualityLimitationReason change to ${n.qualityLimitationReason}`),this._prevQualityLimitationReason=n.qualityLimitationReason)):n.ssrc===s.auxiliary&&!f(n.qualityLimitationReason)&&n.bytesSent!==0&&this._prevAuxQualityLimitationReason!==n.qualityLimitationReason&&(this._log.info(`aux qualityLimitationReason change to ${n.qualityLimitationReason}`),this._prevAuxQualityLimitationReason=n.qualityLimitationReason)}else t.audio.bytesSent=n.bytesSent,t.audio.packetsSent=n.packetsSent;else n.type==="candidate-pair"?zt(n)&&K(n.currentRoundTripTime)&&(t.rtt=Math.floor(n.currentRoundTripTime*1e3),this.totalBytesSent=n.bytesSent):n.type==="media-source"&&(n.kind===l.AUDIO?(t.audio.audioLevel=n.audioLevel||0,t.audio.totalAudioEnergy=n.totalAudioEnergy||0):n.kind===l.VIDEO&&(n.trackIdentifier===e.getVideoTrackId(l.VIDEO)?t.video.fpsCapture=n.framesPerSecond:n.trackIdentifier===e.getVideoTrackId(l.AUXILIARY)?t.auxiliary.fpsCapture=n.framesPerSecond:t.small.fpsCapture=n.framesPerSecond));if(f(n.audioLevel)||(t.audio.audioLevel=n.audioLevel||0),!f(n.frameWidth)){let a=l.SMALL;n.trackIdentifier===e.getVideoTrackId(l.VIDEO)||n.ssrc===s.video?a=l.VIDEO:(n.trackIdentifier===e.getVideoTrackId(l.AUXILIARY)||n.ssrc===s.auxiliary)&&(a=l.AUXILIARY),t[a].frameWidth=n.frameWidth,t[a].frameHeight=n.frameHeight,t[a].framesSent=n.framesSent}}),e.localMainAudioTrack){let n=e.localMainAudioTrack.getInternalAudioLevel();t.audio.micAudioLevel=n,t.audio.audioLevel===0&&(t.audio.audioLevel=n)}this.totalBytesSent||(this.totalBytesSent+=t.audio.bytesSent+t.video.bytesSent+t.auxiliary.bytesSent)}catch(o){this._log.warn(`failed to getStats on sender connection ${o}`)}return t}async getReceiverStats(e){let t={tinyId:e.tinyId,userId:e.userId,rtt:0,hasAudio:!1,hasVideo:!1,hasAuxiliary:!1,isSmallSubscribed:!1,audio:{bytesReceived:0,packetsReceived:0,packetsLost:0,jitter:0,audioLevel:0,totalAudioEnergy:0,insertedSamplesForDeceleration:0,removedSamplesForAcceleration:0},video:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesReceived:0,framesDecoded:0,frameWidth:0,frameHeight:0,fpsDecoded:0},auxiliary:{bytesReceived:0,packetsReceived:0,packetsLost:0,framesReceived:0,framesDecoded:0,frameWidth:0,frameHeight:0,fpsDecoded:0}},i=e.getPeerConnection();if(i)try{let{ssrc:s}=e,{muteState:o,subscribeState:n}=e;(this._spcStats||await i.getStats()).forEach(c=>{if(c.type==="inbound-rtp")if((c.mediaType||c.kind)===l.AUDIO){if(c.ssrc!==s.audio||!o.hasAudio)return;t.audio.packetsReceived=c.packetsReceived,t.audio.bytesReceived=c.bytesReceived,t.audio.packetsLost=c.packetsLost,c.insertedSamplesForDeceleration&&(t.audio.insertedSamplesForDeceleration=c.insertedSamplesForDeceleration),c.removedSamplesForAcceleration&&(t.audio.removedSamplesForAcceleration=c.removedSamplesForAcceleration);let{remoteAudioTrack:u}=e;u.stat.packetsReceived=c.packetsReceived,u.stat.bytesReceived=c.bytesReceived,u.stat.packetsLost=c.packetsLost,c.jitterBufferDelay&&(u.stat.jitterBufferDelay=Math.floor(c.jitterBufferDelay/c.jitterBufferEmittedCount*1e3)),t.hasAudio=!0}else{if(z&&c.bytesReceived===0)return;let u;c.ssrc===s.video&&o.hasVideo&&(t.video.packetsReceived=c.packetsReceived,t.video.bytesReceived=c.bytesReceived,t.video.packetsLost=c.packetsLost,t.video.framesReceived=c.framesReceived,t.video.framesDecoded=c.framesDecoded,t.video.fpsDecoded=c.framesPerSecond,t.hasVideo=!0,u=e.remoteVideoTrack,o.hasSmall&&n.smallVideo&&(t.isSmallSubscribed=!0),c.decoderImplementation&&(!this._prevDecoderImplementationMap.has(t.userId)||this._prevDecoderImplementationMap.get(t.userId)!==c.decoderImplementation)&&(this._log.info(`${t.userId} decoderImplementation change to ${c.decoderImplementation}`),this._prevDecoderImplementationMap.set(t.userId,c.decoderImplementation))),c.ssrc===s.auxiliary&&o.hasAuxiliary&&(t.auxiliary.packetsReceived=c.packetsReceived,t.auxiliary.bytesReceived=c.bytesReceived,t.auxiliary.packetsLost=c.packetsLost,t.auxiliary.framesReceived=c.framesReceived,t.auxiliary.framesDecoded=c.framesDecoded,t.auxiliary.fpsDecoded=c.framesPerSecond,u=e.remoteAuxiliaryTrack,t.hasAuxiliary=!0),u&&(u.stat.packetsReceived=c.packetsReceived,u.stat.bytesReceived=c.bytesReceived,u.stat.packetsLost=c.packetsLost,u.stat.framesReceived=c.framesReceived,u.stat.framesDecoded=c.framesDecoded,c.jitterBufferDelay&&(u.stat.jitterBufferDelay=Math.floor(c.jitterBufferDelay/c.jitterBufferEmittedCount*1e3)))}else c.type==="candidate-pair"&&zt(c)&&K(c.currentRoundTripTime)&&(t.rtt=Math.floor(c.currentRoundTripTime*1e3),this.totalBytesReceived=c.bytesReceived);f(c.frameWidth)||((c.trackIdentifier===e.getMainStreamVideoTrackId()||c.ssrc===s.video)&&(t.video.frameWidth=c.frameWidth,t.video.frameHeight=c.frameHeight,e.remoteVideoTrack.stat.frameWidth=c.frameWidth,e.remoteVideoTrack.stat.frameHeight=c.frameHeight),(c.trackIdentifier===e.getAuxStreamVideoTrackId()||c.ssrc===s.auxiliary)&&(t.auxiliary.frameWidth=c.frameWidth,t.auxiliary.frameHeight=c.frameHeight,e.remoteAuxiliaryTrack.stat.frameWidth=c.frameWidth,e.remoteAuxiliaryTrack.stat.frameHeight=c.frameHeight)),f(c.audioLevel)||(t.audio.audioLevel=c.audioLevel||0,t.audio.totalAudioEnergy=c.totalAudioEnergy||0)}),t.audio.audioLevel===0&&(t.audio.audioLevel=e.remoteAudioTrack.getInternalAudioLevel()||0),this.totalBytesReceived||(this.totalBytesReceived+=t.audio.bytesReceived+t.video.bytesReceived+t.auxiliary.bytesReceived)}catch(s){this._log.warn(`failed to getStats on receiver connection ${s}`)}return t}async getStats(e,t){let i={},s=[];if(this.room.singlePC){let o=this.room.singlePC.getPeerConnection();if(!o)return{senderStats:i,receiverStats:s};let n=await o.getStats(),a=[],c=new Set(["inbound-rtp","outbound-rtp","track","candidate-pair","media-source"]);n.forEach(d=>c.has(d.type)&&a.push(d)),this._spcStats=a}e&&(i=await this.getSenderStats(e));for(let[o,n]of t){let a=await this.getReceiverStats(n);s.push(a)}return{senderStats:i,receiverStats:s}}getDifferenceValue(e,t){if(kt(e))return t;let i=t-e;return i<0?0:i}prepareReport({stats:e,report:t,freezeMap:i}){if(!kt(e.senderStats)){let u={uint32_audio_level:e.senderStats.audio.audioLevel*ze,uint32_audio_energy:e.senderStats.audio.totalAudioEnergy*1e6,uint32_audio_codec_bitrate:e.senderStats.audio.bytesSent};e.senderStats.audio.micAudioLevel&&(u.uint32_mic_audio_level=e.senderStats.audio.micAudioLevel*ze);let h=[];if(e.senderStats.video.bytesSent){let g={uint32_video_stream_type:2,uint32_video_codec_fps:e.senderStats.video.framesSent,uint32_video_capture_fps:e.senderStats.video.fpsCapture,uint32_video_width:e.senderStats.video.frameWidth,uint32_video_height:e.senderStats.video.frameHeight,uint32_video_codec_bitrate:e.senderStats.video.bytesSent,uint32_video_enc_fps:e.senderStats.video.framesEncoded,uint32_key_frame_count:e.senderStats.video.keyFramesEncoded,uint32_nack_count:e.senderStats.video.nackCount,uint32_pli_count:e.senderStats.video.pliCount,uint32_encode_cost:(e.senderStats.video.totalEncodeTime||0)*1e3,uint32_send_packet_cost:(e.senderStats.video.totalPacketSendDelay||0)*1e3,uint32_video_arq_packets:e.senderStats.video.retransmittedPacketsSent};h.push(g)}if(e.senderStats.small.bytesSent){let g={uint32_video_stream_type:3,uint32_video_codec_fps:e.senderStats.small.framesSent||0,uint32_video_capture_fps:e.senderStats.small.fpsCapture||0,uint32_video_width:e.senderStats.small.frameWidth||0,uint32_video_height:e.senderStats.small.frameHeight||0,uint32_video_codec_bitrate:e.senderStats.small.bytesSent,uint32_video_enc_fps:e.senderStats.small.framesEncoded||0,uint32_key_frame_count:e.senderStats.small.keyFramesEncoded,uint32_nack_count:e.senderStats.small.nackCount,uint32_pli_count:e.senderStats.small.pliCount,uint32_encode_cost:(e.senderStats.small.totalEncodeTime||0)*1e3,uint32_send_packet_cost:(e.senderStats.small.totalPacketSendDelay||0)*1e3,uint32_video_arq_packets:e.senderStats.small.retransmittedPacketsSent};h.push(g)}if(e.senderStats.auxiliary.bytesSent){let g={uint32_video_stream_type:7,uint32_video_codec_fps:e.senderStats.auxiliary.framesSent||0,uint32_video_capture_fps:e.senderStats.auxiliary.fpsCapture||0,uint32_video_width:e.senderStats.auxiliary.frameWidth||0,uint32_video_height:e.senderStats.auxiliary.frameHeight||0,uint32_video_codec_bitrate:e.senderStats.auxiliary.bytesSent,uint32_video_enc_fps:e.senderStats.auxiliary.framesEncoded||0,uint32_key_frame_count:e.senderStats.auxiliary.keyFramesEncoded,uint32_nack_count:e.senderStats.auxiliary.nackCount,uint32_pli_count:e.senderStats.auxiliary.pliCount,uint32_encode_cost:(e.senderStats.auxiliary.totalEncodeTime||0)*1e3,uint32_send_packet_cost:(e.senderStats.auxiliary.totalPacketSendDelay||0)*1e3,uint32_video_arq_packets:e.senderStats.auxiliary.retransmittedPacketsSent};h.push(g)}let p={uint32_bitrate:0,uint32_lost:0,uint32_rtt:e.senderStats.rtt};t.msg_up_stream_info={msg_audio_status:u,msg_video_status:h,msg_network_status:p}}let{statInterval:s}=this;t.msg_down_stream_info=[],e.receiverStats.forEach(u=>{let h={msg_user_info:{str_identifier:u.userId,uint64_tinyid:u.tinyId},msg_network_status:{uint32_rtt:u.rtt,uint32_bitrate:0,uint32_lost:0},msg_audio_status:{},msg_video_status:[]};if(u.hasAudio){let p={uint32_audio_codec_bitrate:u.audio.bytesReceived,uint32_audio_total_bitrate:u.audio.bytesReceived,uint32_audio_level:u.audio.audioLevel*1e8,uint32_audio_energy:u.audio.totalAudioEnergy*1e6,uint32_audio_receive:u.audio.packetsReceived,uint32_audio_origin_lost:u.audio.packetsLost};h.msg_audio_status=p}if(u.hasVideo){let p=i.get(`${u.userId}_${Aa}`),g=p?p.duration:0,C={uint32_video_stream_type:u.isSmallSubscribed?3:2,uint32_video_receive_fps:u.video.framesReceived,uint32_video_width:u.video.frameWidth,uint32_video_height:u.video.frameHeight,uint32_video_codec_bitrate:u.video.bytesReceived,uint32_video_receive:u.video.packetsReceived,uint32_video_origin_lost:u.video.packetsLost,uint32_video_block_time:g,uint32_video_dec_fps:u.video.framesDecoded};h.msg_video_status.push(C)}if(u.hasAuxiliary){let p=i.get(`${u.userId}_${Ca}`),g=p?p.duration:0,C={uint32_video_stream_type:7,uint32_video_receive_fps:u.auxiliary.framesReceived,uint32_video_width:u.auxiliary.frameWidth,uint32_video_height:u.auxiliary.frameHeight,uint32_video_codec_bitrate:u.auxiliary.bytesReceived,uint32_video_receive:u.auxiliary.packetsReceived+u.auxiliary.packetsLost,uint32_video_origin_lost:u.auxiliary.packetsLost,uint32_video_block_time:g,uint32_video_dec_fps:u.auxiliary.framesDecoded};h.msg_video_status.push(C)}t.msg_down_stream_info.push(h)});let o=this._prevReport;if(this._prevReport=JSON.parse(JSON.stringify(t)),t.msg_up_stream_info.msg_audio_status&&o.msg_up_stream_info.msg_audio_status){let u=o.msg_up_stream_info.msg_audio_status,h=t.msg_up_stream_info.msg_audio_status;if(u.uint32_audio_codec_bitrate===0)h.uint32_audio_codec_bitrate=0;else{let p=this.getDifferenceValue(u.uint32_audio_codec_bitrate,h.uint32_audio_codec_bitrate);h.uint32_audio_codec_bitrate=Math.round(p*8/s),t.msg_up_stream_info.msg_network_status.uint32_bitrate+=h.uint32_audio_codec_bitrate}}let n=o.msg_up_stream_info.msg_video_status;t.msg_up_stream_info.msg_video_status.forEach(u=>{let h=n.find(Ce=>Ce.uint32_video_stream_type===u.uint32_video_stream_type);if(!h||h.uint32_video_codec_bitrate===0){u.uint32_video_codec_bitrate=0,u.uint32_video_enc_fps=0,u.uint32_video_codec_fps=0;return}let p=0,g=0,C=0;h&&u.uint32_video_codec_bitrate>=h.uint32_video_codec_bitrate&&(p=h.uint32_video_codec_bitrate,g=h.uint32_video_enc_fps,C=h.uint32_video_codec_fps);let N=this.getDifferenceValue(p,u.uint32_video_codec_bitrate);u.uint32_video_codec_bitrate=Math.round(N*8/s),t.msg_up_stream_info.msg_network_status.uint32_bitrate+=u.uint32_video_codec_bitrate,u.uint32_video_enc_fps=Math.round(this.getDifferenceValue(g,u.uint32_video_enc_fps)/s),u.uint32_video_codec_fps=Math.round(this.getDifferenceValue(C,u.uint32_video_codec_fps)/s),Ct&&at()===115&&h.uint32_video_width===0&&h.uint32_video_height===0&&h.uint32_video_codec_fps===0&&(u.uint32_video_codec_fps=u.uint32_video_enc_fps),f(h.uint32_key_frame_count)||(u.uint32_key_frame_count=Math.round(this.getDifferenceValue(h.uint32_key_frame_count,u.uint32_key_frame_count))),f(h.uint32_nack_count)||(u.uint32_nack_count=Math.round(this.getDifferenceValue(h.uint32_nack_count,u.uint32_nack_count))),f(h.uint32_pli_count)||(u.uint32_pli_count=Math.round(this.getDifferenceValue(h.uint32_pli_count,u.uint32_pli_count))),f(h.uint32_video_arq_packets)||(u.uint32_video_arq_packets=Math.round(this.getDifferenceValue(h.uint32_video_arq_packets,u.uint32_video_arq_packets))),f(h.uint32_encode_cost)||(u.uint32_encode_cost=Math.round(this.getDifferenceValue(h.uint32_encode_cost,u.uint32_encode_cost)/s)),f(h.uint32_send_packet_cost)||(u.uint32_send_packet_cost=Math.round(this.getDifferenceValue(h.uint32_send_packet_cost,u.uint32_send_packet_cost)/s))});let c=t.msg_down_stream_info,d=o.msg_down_stream_info;return c.forEach(u=>{let h=d.find(p=>p.msg_user_info.uint64_tinyid===u.msg_user_info.uint64_tinyid);if(!!h){if(!kt(u.msg_audio_status)&&!kt(h.msg_audio_status)){let p=u.msg_audio_status,g=h.msg_audio_status;p.uint32_audio_origin_lost=this.getDifferenceValue(g.uint32_audio_origin_lost,p.uint32_audio_origin_lost),p.uint32_audio_receive=this.getDifferenceValue(g.uint32_audio_receive,p.uint32_audio_receive),p.uint32_audio_receive+=p.uint32_audio_origin_lost;let C=this.getDifferenceValue(g.uint32_audio_codec_bitrate,p.uint32_audio_codec_bitrate);p.uint32_audio_codec_bitrate=Math.round(C*8/s),p.uint32_audio_total_bitrate=Math.round(C*8/s)}else u.msg_audio_status={};if(u.msg_video_status&&h.msg_video_status){let p=h.msg_video_status;u.msg_video_status=u.msg_video_status.filter(C=>p.find(N=>N.uint32_video_stream_type===C.uint32_video_stream_type)),u.msg_video_status.forEach(C=>{let N=p.find(rh=>rh.uint32_video_stream_type===C.uint32_video_stream_type),Ce=N.uint32_video_receive,oi=N.uint32_video_origin_lost,Ms=N.uint32_video_codec_bitrate,Ls=N.uint32_video_receive_fps,eh=N.uint32_video_dec_fps;C.uint32_video_origin_lost=this.getDifferenceValue(oi,C.uint32_video_origin_lost),C.uint32_video_receive=this.getDifferenceValue(Ce,C.uint32_video_receive)+C.uint32_video_origin_lost;let th=this.getDifferenceValue(Ms,C.uint32_video_codec_bitrate);C.uint32_video_codec_bitrate=Math.round(th*8/s);let ih=this.getDifferenceValue(Ls,C.uint32_video_receive_fps);C.uint32_video_receive_fps=Math.round(ih/s),C.uint32_video_dec_fps=Math.round(this.getDifferenceValue(eh,C.uint32_video_dec_fps)/s)})}}}),t}async getStatsReport({uplinkConnection:e,downlinkConnections:t,freezeMap:i}){let s={msg_up_stream_info:{msg_audio_status:{uint32_audio_format:11,uint32_audio_sample_rate:0,uint32_audio_codec_bitrate:0,uint32_audio_receive:0,uint32_audio_origin_lost:0,uint32_audio_level:0,uint32_audio_energy:0},msg_video_status:[],msg_network_status:{uint32_bitrate:0,uint32_rtt:0,uint32_lost:0}},msg_down_stream_info:[{msg_user_info:{str_identifier:"",uint64_tinyid:0},msg_audio_status:{uint32_audio_format:11,uint32_audio_sample_rate:0,uint32_audio_codec_bitrate:0,uint32_audio_total_bitrate:0,uint32_audio_level:0,uint32_audio_energy:0,uint32_audio_receive:0,uint32_audio_origin_lost:0,uint32_audio_final_lost:0},msg_video_status:[{uint32_video_stream_type:0,uint32_video_receive_fps:0,uint32_video_width:0,uint32_video_height:0,uint32_video_codec_bitrate:0,uint32_video_receive:0,uint32_video_origin_lost:0,uint32_video_block_time:0,uint32_video_dec_fps:0}],msg_network_status:{uint32_bitrate:0,uint32_rtt:0,uint32_lost:0}}]},o=await this.getStats(e,t);return JSON.stringify(this._prevReport)==="{}"&&(this._prevReport=JSON.parse(JSON.stringify(s))),this.prepareReport({stats:o,report:s,freezeMap:i}),this._prevReportTime=Date.now(),s}reset(){this._prevReportTime=0,this._prevReport={},this._prevEncoderImplementation="",this._prevQualityLimitationReason="",this._prevDecoderImplementationMap=new Map}};var Pl=pe(ve());var od=class extends Pl.default{_room;_signalChannel;_log;_uplinkRTT=0;_uplinkLoss=0;_downlinkRTT=0;_downlinkLoss=0;_downlinkPrevStatMap=new Map;_downlinkLossAndRTTMap=new Map;_interval=-1;_uplinkNetworkQuality=0;_downlinkNetworkQuality=0;constructor({signalChannel:e,room:t}){super(),this._room=t,this._signalChannel=e,this._log=I.createLogger({id:"q",userId:this._room.userId,sdkAppId:this._room.sdkAppId}),this.initialize()}get uplinkNetworkQuality(){return this._uplinkNetworkQuality}set uplinkNetworkQuality(e){e!==this._uplinkNetworkQuality&&this._log.info(`uplink ${this.uplinkNetworkQuality} -> ${e}, rtt: ${this._uplinkRTT}, loss: ${this._uplinkLoss}`),this._uplinkNetworkQuality=e}get downlinkNetworkQuality(){return this._downlinkNetworkQuality}set downlinkNetworkQuality(e){if(e!==this._downlinkNetworkQuality){let{rtt:t,loss:i}=this.getAverageLossAndRTT([...this._downlinkLossAndRTTMap.values()]);this._log.info(`downlink ${this.downlinkNetworkQuality} -> ${e}, rtt: ${t}, loss: ${i}`)}this._downlinkNetworkQuality=e}initialize(){this._signalChannel.on(V.UPLINK_NETWORK_STATS,e=>{this.handleUplinkNetworkQuality(e)}),this._signalChannel.on(se.CONNECTION_STATE_CHANGED,this.handleSignalConnectionStateChange.bind(this)),this.start()}handleUplinkNetworkQuality(e){if(e.data.code!==0)return;let t=e.data.data;if(t.delay&&this.updateDelay(t.delay),!this._room.uplinkConnection){this.uplinkNetworkQuality=0,this._uplinkLoss=0,this._uplinkRTT=0;return}let i=this._room?.uplinkConnection?.getPeerConnection();if(i&&this.isPeerConnectionDisconnected(i)){this.uplinkNetworkQuality=6,this._uplinkLoss=0,this._uplinkRTT=0;return}let s=t.expectAudPkg+t.expectVidPkg,o=t.recvAudPkg+t.recvVidPkg,n=s-o;s===0&&o===0||(n<=0?this._uplinkLoss=0:this._uplinkLoss=Math.round(n/s*100),this._uplinkRTT=t.rtt,this.uplinkNetworkQuality=this.getNetworkQuality(this._uplinkLoss,this._uplinkRTT))}async handleDownlinkNetworkQuality(){if(this._room.remotePublishedUserMap.size===0){this.downlinkNetworkQuality=0;return}let e=[...this._room.remotePublishedUserMap.values()],t=e.filter(n=>n.getPeerConnection()?.connectionState===Q.CONNECTED);if(e.filter(n=>this.isPeerConnectionDisconnected(n.getPeerConnection())).length===e.length){this.downlinkNetworkQuality=6;return}for(let n=0;n<t.length;n++){let a=t[n].getPeerConnection();if(!a)return;let{rtt:c,totalPacketsLost:d,totalPacketsReceived:u}=await this.getStat(a);if(!this._downlinkPrevStatMap.has(a)){this._downlinkPrevStatMap.set(a,{totalPacketsLost:d,totalPacketsReceived:u});continue}let h=0,p=this._downlinkPrevStatMap.get(a),g=d-p.totalPacketsLost,C=u-p.totalPacketsReceived;g<=0||C<0?h=0:h=Math.round(g/(g+C)*100),this._downlinkPrevStatMap.set(a,{totalPacketsLost:d,totalPacketsReceived:u}),this._downlinkLossAndRTTMap.set(a,{rtt:c,loss:h,userId:t[n].getUserId(),audioDelay:t[n].remoteAudioTrack.stat.end2EndDelay,videoDelay:t[n].remoteVideoTrack.stat.end2EndDelay})}if([...this._downlinkPrevStatMap.keys()].forEach(n=>{this.isPeerConnectionDisconnected(n)&&(this._downlinkPrevStatMap.delete(n),this._downlinkLossAndRTTMap.delete(n))}),this._downlinkLossAndRTTMap.size===0)return;let{rtt:s,loss:o}=this.getAverageLossAndRTT([...this._downlinkLossAndRTTMap.values()]);this._downlinkRTT=s,this._downlinkLoss=o,this.downlinkNetworkQuality=this.getNetworkQuality(o,s)}async getStat(e){let t={rtt:0,totalPacketsLost:0,totalPacketsReceived:0};if(!e||!Ti())return t;let i=e.getReceivers();try{for(let s=0;s<i.length;s++)(await i[s].getStats()).forEach(a=>{a.type==="candidate-pair"&&K(a.currentRoundTripTime)&&(t.rtt=Math.round(a.currentRoundTripTime*1e3)),a.type==="inbound-rtp"&&(a.mediaType===l.AUDIO||a.mediaType===l.VIDEO)&&(t.totalPacketsLost+=a.packetsLost,t.totalPacketsReceived+=a.packetsReceived)});return t}catch{return t}}getAverageLossAndRTT(e){let t={rtt:0,loss:0};return Array.isArray(e)&&e.length>0&&(e.forEach(i=>{t.rtt+=i.rtt,t.loss+=i.loss}),Object.keys(t).forEach(i=>{t[i]=Math.round(t[i]/e.length)})),t}getNetworkQuality(e,t){return e>50||t>500?5:e>30||t>350?4:e>20||t>200?3:e>10||t>100?2:e>=0||t>=0?1:0}handleSignalConnectionStateChange(e){e.state===Me.DISCONNECTED?(this._uplinkRTT=0,this._uplinkLoss=0,this.uplinkNetworkQuality=6):e.state===Me.CONNECTED&&this.uplinkNetworkQuality===6&&(this.uplinkNetworkQuality=5)}handleUplinkConnectionStateChange({state:e}){e==="DISCONNECTED"?(this._uplinkLoss=0,this._uplinkRTT=0,this.uplinkNetworkQuality=6):e==="CONNECTED"&&this.uplinkNetworkQuality===6&&(this.uplinkNetworkQuality=5)}isPeerConnectionDisconnected(e){return!!(e&&(e.connectionState===Q.DISCONNECTED||e.connectionState===Q.FAILED||e.connectionState===Q.CLOSED))}setUplinkConnection(e){this._room.uplinkConnection=e,this._room.uplinkConnection?this._room.uplinkConnection.on("connection-state-changed",this.handleUplinkConnectionStateChange.bind(this)):(this.uplinkNetworkQuality=0,this._uplinkRTT=0,this._uplinkLoss=0)}start(){if(this._interval!==-1){this._log.info("network quality calculating is already started");return}this._log.debug("start network quality calculating"),this._interval=J.run(dt,()=>{this.handleDownlinkNetworkQuality();let e=[...this._downlinkLossAndRTTMap.values()];_.emit(m.NETWORK_QUALITY,{room:this._room,uplink:{rtt:this._uplinkRTT,loss:this._uplinkLoss},downlinks:e}),this.emit(od.EVENT_NETWORK_QUALITY,{uplinkNetworkQuality:this.uplinkNetworkQuality,downlinkNetworkQuality:this.downlinkNetworkQuality,uplinkRTT:this._uplinkRTT,uplinkLoss:this._uplinkLoss,downlinkRTT:this._downlinkRTT,downlinkLoss:this._downlinkLoss,downlinkInfo:e})},{delay:2e3})}stop(){this._log.debug("stopped"),this._interval!==-1&&(J.clearTask(this._interval),this._interval=-1),this._downlinkLossAndRTTMap.clear(),this._downlinkPrevStatMap.clear()}updateDelay(e){let{tinyIdToUserIdMap:t}=this._room;e.forEach(({srcTinyId:i,videoDelay:s,audioDelay:o})=>{let n=t.get(i);n&&this._room.remotePublishedUserMap.get(n)?.setDelay({videoDelay:s,audioDelay:o})})}},Di=od;ae(Di,"EVENT_NETWORK_QUALITY","0");var Bt=new WeakMap;function Er({settings:r={retries:5,timeout:2e3},onError:e,onRetrying:t,onRetryFailed:i}){return function(s,o,n){let a=ht({retryFunction:n.value,settings:r,onError({error:c,retry:d,reject:u,retryFuncArgs:h}){e&&e.call(this,c,()=>{Bt.get(s)?.has(o)?d():u(c)},u,h)},onRetrying:(c,d)=>{W(t)&&t(c,d),Bt.get(s)?.has(o)&&(Bt.get(s).get(o).stopRetry=d)},onRetryFailed:i});return n.value=function(...c){let d=Bt.get(s);return d?d.set(o,{args:c}):Bt.set(s,new Map([[o,{args:c}]])),a.apply(this,c).finally(()=>Bt.get(s)?.delete(o))},n}}function vs({fnName:r,callback:e,validateArgs:t=!0}){return function(i,s,o){let n=o.value;return o.value=function(...a){if(Bt.get(i)?.has(r)){let{stopRetry:c,args:d}=Bt.get(i).get(r),u=!0;if(t){for(let h of d)if(!a.find(p=>p===h)){u=!1;break}}u&&(e&&e.apply(this,a),c&&c(),Bt.get(i)?.delete(r))}return n.apply(this,a)},o}}function Nm({fn:r,context:e}){return function(...t){try{let i=r.apply(e,t);return Qr(i)?i.catch(s=>I.error(`${r.name}() error observed ${s}`)):i}catch(i){I.error(`${r.name}() error observed ${i}`)}}}var hn=class{constructor(e){this._signalInfo={tinyId:void 0,clientIp:"",signalIp:"",relayIp:"",relayInnerIp:"",relayPort:0};this._apiSuccessRateMap=new Map;this._eventMap=new Map;this._frameWorkType=e.frameWorkType||30,this._component=e.component||0,this.connectionType=e.connectionType||1,this._language=e.language||0,this._room=e.room,this._keyPrefix="key_point",this._log=I.createLogger({id:"kpm",userId:this._room.userId,sdkAppId:this._room.sdkAppId}),Object.getOwnPropertyNames(this.__proto__).forEach(t=>{t.startsWith("handle")&&W(this[t])&&(this[t]=Nm({fn:this[t],context:this}))}),this.initData(),this.installEvents(),this._intervalId=J.run(dt,this.setStorage.bind(this),{delay:2e4})}get _storageKey(){return`${this._keyPrefix}_${this._room.userId}`}initData(){this._firstPublishedUserList=[],this._networkQuality={totalUplinkRTT:0,totalUplinkLoss:0,count:0,totalDownlinkRTTAndLossMap:new Map},this._basicInfo={string_sdk_version:Se,uint32_os_type:15,string_device_name:"",string_http_user_agent:navigator.userAgent,string_os_version:"",uint32_avg_rtt:0,uint32_avg_up_loss:0,uint32_scene:this._room.scene==="live"?1:0,uint32_joining_duration:0,uint32_networkType:Gt[Co()],uint32_framework:this._frameWorkType,uint32_component:this._component,uint32_connection_type:this.connectionType,uint32_caller_coding_language:this._language,string_domain:location.hostname},this._pathJoinRoom={uint64_start_time:0,uint64_send_request_acc_ip_cmd_start_time:0,uint64_send_request_acc_ip_cmd_end_time:0,uint64_send_request_enter_room_cmd_start_time:0,uint64_send_request_enter_room_cmd_end_time:0,uint64_send_first_video_frame_time:0,uint64_recv_userlist_time:0,uint64_end_time:0,int32_send_request_acc_ip_cmd_ret:0,int32_send_request_enter_room_cmd_ret:0,int32_end_ret:0},this._pathLeaveRoom={uint64_start_time:0,uint64_send_request_exit_room_cmd_start_time:0,uint64_send_request_exit_room_cmd_end_time:0,uint64_end_time:0,int32_send_request_exit_room_cmd_ret:0,int32_end_ret:0},this._localStreamStat={totalVideoBitrate:0,totalVideoFPS:0,totalVideoHeight:0,totalVideoWidth:0,totalAudioLevel:0,videoCount:0,audioLevelCount:0,publishStartTime:0,statsToReport:{uint32_audio_capture_db:0,uint32_video_big_capture_fps:0,uint32_video_big_bitrate:0,uint32_video_big_resolution:0}},this._pathMainVideoMap=new Map,this._pathMainAudioMap=new Map,this._pathAuxiliaryMap=new Map,this._remoteStreamStatMap=new Map,this._apiSuccessRateMap.clear(),Gr().then(e=>{this._basicInfo.string_os_version=ma(),e&&(this._basicInfo.string_device_name=e.model)})}addEvent(e,t){return this._eventMap.set(e,t),_.on(e,t),this}installEvents(){this.handleUnload=this.handleUnload.bind(this),window.addEventListener("unload",this.handleUnload),this._room.once("banned",()=>this.handleLeaveSuccess({room:this._room,roomId:this._room.roomId})),this.addEvent(m.JOIN_START,this.handleJoinStart).addEvent(m.JOIN_SCHEDULE_SUCCESS,this.handleJoinScheduleSuccess).addEvent(m.JOIN_SIGNAL_CONNECTION_START,this.handleSignalConnectionStart).addEvent(m.JOIN_SIGNAL_CONNECTION_END,this.handleSignalConnectionEnd).addEvent(m.JOIN_SEND_CMD,this.handleJoinSendCMD).addEvent(m.JOIN_RECEIVED_CMD_RES,this.handleJoinReceivedCMDResponce).addEvent(m.JOIN_SUCCESS,this.handleJoinSuccess).addEvent(m.JOIN_FAILED,this.handleJoinFailed).addEvent(m.LEAVE_START,this.handleLeaveStart).addEvent(m.LEAVE_SUCCESS,this.handleLeaveSuccess).addEvent(m.LEAVE_SEND_CMD,this.handleLeaveSendCMD).addEvent(m.LOCAL_TRACK_CAPTURE_START,this.handleTrackCaptureStart).addEvent(m.LOCAL_TRACK_CAPTURE_SUCCESS,this.handleTrackCaptureSuccess).addEvent(m.LOCAL_TRACK_CAPTURE_FAILED,this.handleTrackCaptureFailed).addEvent(m.PUBLISH_START,this.handlePublishStart).addEvent(m.SEND_FIRST_VIDEO_FRAME,this.handleSendFirstVideoFrame).addEvent(m.SUBSCRIBE_START,this.handleSubscribeStart).addEvent(m.SUBSCRIBE_SUCCESS,this.handleSubscribed).addEvent(m.PLAY_TRACK_START,this.handlePlayStart).addEvent(m.VIDEO_LOADED_DATA,this.handleVideoLoadedData).addEvent(m.PLAYER_STATE_CHANGED,({track:e,state:t,type:i})=>{!jt(e)||!this.hitTest(e.room)||t==="PLAYING"&&(i===l.AUDIO?this.handleAudioPlaying(e):this.handleVideoPlaying(e))}).addEvent(m.NETWORK_QUALITY,this.handleNetworkQuality).addEvent(m.HEARTBEAT_REPORT,this.handleHeartbeatStats).addEvent(m.RECEIVED_PUBLISHED_USER_LIST,this.handleReceivedPublishUserList).addEvent(m.REMOTE_PUBLISH_STATE_CHANGED,({room:e,prevMuteState:t,muteState:i})=>{if(!this.hitTest(e))return;let s=t.hasAudio||t.hasVideo||t.hasSmall,o=t.hasAuxiliary,n=i.hasAudio||i.hasVideo||i.hasSmall,a=i.hasAuxiliary;!s&&n&&this.handleRemoteStreamAdded(i.userId,"main"),!o&&a&&this.handleRemoteStreamAdded(i.userId,"auxiliary")}).addEvent(m.API_SUCCESS_RATE,this.handleAPISuccessRate).addEvent(m.SINGLE_CONNECTION_STAT,({room:e,stat:t})=>{this.hitTest(e)&&(this._pathJoinRoom.int32_ice_cost=t.ice,this._pathJoinRoom.int32_dtls_cost=t.dtls,this._pathJoinRoom.int32_peer_connection_cost=t.peerConnection)})}uninstallEvents(){window.removeEventListener("unload",this.handleUnload),this._eventMap.forEach((e,t)=>_.off(t,e)),this._eventMap.clear()}destroy(){this.uninstallEvents(),J.clearTask(this._intervalId)}handleUnload(){this._room.isJoined&&this.handleLeaveSuccess({room:this._room,roomId:this._room.roomId})}handleJoinStart(e){this.hitTest(e.room)&&(this._pathJoinRoom.uint64_start_time===0&&(this._pathJoinRoom.uint64_start_time=Date.now(),this.checkStorage()),e.params&&(f(e.params.frameWorkType)||(this._frameWorkType=e.params.frameWorkType,this._basicInfo.uint32_framework=this._frameWorkType),f(e.params.component)||(this._component=e.params.component,this._basicInfo.uint32_component=this._component),f(e.params.language)||(this._language=e.params.language,this._basicInfo.uint32_caller_coding_language=this._language)))}handleJoinScheduleSuccess({room:e,detailCost:t}){if(this.hitTest(e)&&t){let{totalCost:i,local:s,dns:o,tcp:n,tls:a,request:c,response:d}=t;this._pathJoinRoom.int32_schedule_cost=i,this._pathJoinRoom.int32_schedule_local=s,this._pathJoinRoom.int32_schedule_dns=o,this._pathJoinRoom.int32_schedule_tcp=n,this._pathJoinRoom.int32_schedule_tls=a,this._pathJoinRoom.int32_schedule_request=c,this._pathJoinRoom.int32_schedule_response=d}}handleSignalConnectionStart({room:e}){this.hitTest(e)&&this._pathJoinRoom.uint64_send_request_acc_ip_cmd_start_time===0&&(this._pathJoinRoom.uint64_send_request_acc_ip_cmd_start_time=Date.now())}handleSignalConnectionEnd({room:e,error:t}){this.hitTest(e)&&this._pathJoinRoom.uint64_send_request_acc_ip_cmd_end_time===0&&(this._pathJoinRoom.uint64_send_request_acc_ip_cmd_end_time=Date.now(),t&&(this._pathJoinRoom.int32_send_request_acc_ip_cmd_ret=t instanceof A?Number(t.getExtraCode()||t.getCode()):E.UNKNOWN,this._pathJoinRoom.int32_end_ret=this._pathJoinRoom.int32_send_request_acc_ip_cmd_ret))}handleJoinSendCMD(e){this.hitTest(e.room)&&this._pathJoinRoom.uint64_send_request_enter_room_cmd_start_time===0&&(this._pathJoinRoom.uint64_send_request_enter_room_cmd_start_time=Date.now())}handleJoinReceivedCMDResponce(e){this.hitTest(e.room)&&this._pathJoinRoom.uint64_send_request_enter_room_cmd_end_time===0&&(this._pathJoinRoom.uint64_send_request_enter_room_cmd_end_time=Date.now(),this._pathJoinRoom.int32_send_request_enter_room_cmd_ret=e.code,e.code!==0&&(this._pathJoinRoom.int32_end_ret=this._pathJoinRoom.int32_send_request_enter_room_cmd_ret))}handleJoinSuccess(e){this.hitTest(e.room)&&this._pathJoinRoom.uint64_end_time===0&&(this._pathJoinRoom.uint64_end_time=Date.now(),this._pathJoinRoom.int32_end_ret=0,this._signalInfo=e.room.getSignalInfo())}handleJoinFailed({room:e,error:t}){this.hitTest(e)&&(this._pathJoinRoom.uint64_end_time=Date.now(),this._pathJoinRoom.int32_end_ret===0&&(this._pathJoinRoom.int32_end_ret=t.code||this._pathJoinRoom.int32_send_request_enter_room_cmd_ret||this._pathJoinRoom.int32_send_request_acc_ip_cmd_ret),setTimeout(()=>{this.report()}))}handleReceivedPublishUserList(e){this.hitTest(e.room)&&this._pathJoinRoom.uint64_recv_userlist_time===0&&(this._pathJoinRoom.uint64_recv_userlist_time=Date.now(),this._firstPublishedUserList=e.publishedUserList||[])}handleSendFirstVideoFrame({room:e}){!this.hitTest(e)||this._pathJoinRoom.uint64_send_first_video_frame_time===0&&this._pathJoinRoom.uint64_start_time!==0&&(this._pathJoinRoom.uint64_send_first_video_frame_time=Date.now())}handleLeaveStart(e){this.hitTest(e.room)&&(this._pathLeaveRoom.uint64_start_time=Date.now())}handleLeaveSuccess(e){this.hitTest(e.room)&&this._pathLeaveRoom.uint64_end_time===0&&(this._pathLeaveRoom.uint64_end_time=Date.now(),this._pathJoinRoom.uint64_end_time!==0?this._basicInfo.uint32_joining_duration=this._pathLeaveRoom.uint64_end_time-this._pathJoinRoom.uint64_end_time:this._log.warn("pathJoinRoom endTime is 0"),this.report())}handleLeaveSendCMD(e){this.hitTest(e.room)&&(this._pathLeaveRoom.uint64_send_request_exit_room_cmd_start_time=Date.now(),this._pathLeaveRoom.uint64_send_request_exit_room_cmd_end_time=Date.now())}handleRemoteStreamAdded(e,t){let i=`${e}_${t}`;if(!this._remoteStreamStatMap.has(i)){let s={userId:e,totalVideoFPS:0,totalVideoBitrate:0,totalAudioLevel:0,totalAudioBitrate:0,totalLoss:0,audioCount:0,audioLevelCount:0,videoCount:0,networkQualityCount:0,streamAddedTime:Date.now(),subscribeStartTime:0,subscribedTime:0,playStreamTime:0,statsToReport:{...vm,msg_user_info:new Ds({userId:e,tinyId:this._room.remotePublishedUserMap.get(e)?.tinyId,role:20})}};s.statsToReport.uint32_stream_type=t==="main"?2:7,this._remoteStreamStatMap.set(i,s)}}handleSubscribeStart({room:e,remotePublishedUser:t,streamType:i,subscribeState:s}){if(!this.hitTest(e))return;let{userId:o,tinyId:n,role:a}=t,c=new Ds({userId:o,tinyId:n,role:a==="anchor"?20:21}),d=Date.now(),u=`${o}_${i}`,h=this._remoteStreamStatMap.get(u);h&&h.subscribeStartTime===0&&(h.subscribeStartTime=d),i==="main"?(t.muteState.hasVideo&&(s.video||s.smallVideo)&&!this._pathMainVideoMap.has(u)&&this._pathMainVideoMap.set(u,{statsToReport:{msg_user_info:c,uint64_start_enter_time:this._pathJoinRoom.uint64_start_time,uint64_render_first_frame_time:0,uint64_combine_first_frame_time:0},userId:o,sendSubscribeCMDTime:d}),t.muteState.hasAudio&&s.audio&&!this._pathMainAudioMap.has(u)&&this._pathMainAudioMap.set(u,{statsToReport:{msg_user_info:c,uint64_start_enter_time:this._pathJoinRoom.uint64_start_time,uint64_play_first_frame_time:0},userId:o,sendSubscribeCMDTime:d})):t.muteState.hasAuxiliary&&s.auxiliary&&!this._pathAuxiliaryMap.has(u)&&this._pathAuxiliaryMap.set(u,{sendSubscribeCMDTime:d})}handleSubscribed({room:e,remotePublishedUser:t,streamType:i}){if(this.hitTest(e)){let s=`${t.userId}_${i}`,o=this._remoteStreamStatMap.get(s);o&&o.subscribedTime===0&&(o.subscribedTime=Date.now())}}handlePlayStart({track:e}){if(!jt(e)||!this.hitTest(e.room))return;let t=`${e.userId}_${e.streamType}`,i=this._remoteStreamStatMap.get(t);i?.playStreamTime===0&&(i.playStreamTime=Date.now())}handleVideoLoadedData({track:e}){if(!jt(e)||!this.hitTest(e.room))return;let t=`${e.userId}_${e.streamType}`,i=this._pathMainVideoMap.get(t);i&&i.statsToReport.uint64_combine_first_frame_time===0&&(i.statsToReport.uint64_combine_first_frame_time=Date.now())}handleVideoPlaying(e){let t=`${e.userId}_${e.streamType}`,i=Date.now(),s=this._pathMainVideoMap.get(t),o=this._remoteStreamStatMap.get(t);if(s&&(s.statsToReport.uint64_render_first_frame_time===0&&(s.statsToReport.uint64_render_first_frame_time=i),o)){let{statsToReport:a,playStreamTime:c,subscribedTime:d}=o;a.uint32_video_render_first===0&&c-d<=100&&(a.uint32_video_render_first=i-s.sendSubscribeCMDTime)}let n=this._pathAuxiliaryMap.get(t);if(n&&o){let{statsToReport:a,playStreamTime:c,subscribedTime:d}=o;a.uint32_video_render_first===0&&c-d<=100&&(a.uint32_video_render_first=i-n.sendSubscribeCMDTime)}}handleAudioPlaying(e){let t=`${e.userId}_${e.streamType}`,i=this._pathMainAudioMap.get(t);i&&i.statsToReport.uint64_play_first_frame_time===0&&(i.statsToReport.uint64_play_first_frame_time=Date.now())}handleNetworkQuality(e){this.hitTest(e.room)&&(this._networkQuality.totalUplinkLoss+=e.uplink.loss,this._networkQuality.totalUplinkRTT+=e.uplink.rtt,this._networkQuality.count++,e.downlinks.forEach(({rtt:t,loss:i,userId:s,videoDelay:o,audioDelay:n})=>{let a=this._networkQuality.totalDownlinkRTTAndLossMap.get(s);if(a)a.totalRTT+=t,a.totalLoss+=i,o&&(a.totalVideoDelay=(a.totalVideoDelay||0)+o,a.videoDelayCount=(a.videoDelayCount||0)+1),n&&(a.totalAudioDelay=(a.totalAudioDelay||0)+n,a.audioDelayCount=(a.audioDelayCount||0)+1),a.count++;else{let c,d,u,h;o&&(d=o,u=1),n&&(c=n,h=1),this._networkQuality.totalDownlinkRTTAndLossMap.set(s,{totalRTT:t,totalLoss:i,count:1,totalAudioDelay:c,totalVideoDelay:d,audioDelayCount:h,videoDelayCount:u})}}))}handleHeartbeatStats(e){if(this.hitTest(e.room)){let{msg_up_stream_info:t,msg_down_stream_info:i}=e.report;if(t.msg_video_status[0]){let{uint32_video_codec_bitrate:s,uint32_video_enc_fps:o,uint32_video_width:n,uint32_video_height:a}=t.msg_video_status[0];this._localStreamStat.totalVideoBitrate+=s,this._localStreamStat.totalVideoFPS+=o,this._localStreamStat.totalVideoWidth+=n,this._localStreamStat.totalVideoHeight+=a,this._localStreamStat.videoCount++}if(t.msg_audio_status){let{uint32_audio_level:s}=t.msg_audio_status;Math.floor(s/ze*100)>0&&(this._localStreamStat.totalAudioLevel+=s/ze,this._localStreamStat.audioLevelCount++)}i.forEach(s=>{let{msg_user_info:o,msg_audio_status:n,msg_video_status:a}=s,c=o.str_identifier,d=this._room.remotePublishedUserMap.get(c);if(a.forEach(u=>{let h=u.uint32_video_stream_type===2,p=u.uint32_video_stream_type===7,g=`${c}_${h?"main":"auxiliary"}`,C=this._remoteStreamStatMap.get(g);if(!!C&&(h&&d?.remoteVideoTrack.isSubscribed||p&&d?.remoteAuxiliaryTrack)){C.totalVideoFPS+=u.uint32_video_receive_fps,C.totalVideoBitrate+=u.uint32_video_codec_bitrate,C.videoCount++,C.statsToReport.uint32_video_width===0&&(C.statsToReport.uint32_video_width=u.uint32_video_width),C.statsToReport.uint32_video_height===0&&(C.statsToReport.uint32_video_height=u.uint32_video_height);let N=h?d.remoteVideoTrack:d.remoteAuxiliaryTrack;N.stat.jitterBufferDelay&&(C.videoJitterBufferDelay=N.stat.jitterBufferDelay),N.stat.framesReceived&&(C.statsToReport.uint32_video_consume_render_rate=Math.floor(N.stat.framesDecoded/N.stat.framesReceived*10**6))}}),n){let u=`${c}_${"main"}`,h=this._remoteStreamStatMap.get(u);this._remoteStreamStatMap.has(u)&&h&&d?.remoteAudioTrack.isSubscribed&&(h.totalAudioBitrate+=n.uint32_audio_codec_bitrate,h.audioCount++,d.remoteAudioTrack.stat.jitterBufferDelay&&(h.audioJitterBufferDelay=d.remoteAudioTrack.stat.jitterBufferDelay),Math.floor(n.uint32_audio_level/ze*100)>0&&(h.totalAudioLevel+=n.uint32_audio_level/ze,h.audioLevelCount++))}})}}handlePublishStart({room:e}){this.hitTest(e)&&this._localStreamStat.publishStartTime===0&&(this._localStreamStat.publishStartTime=Date.now())}handleTrackCaptureStart({track:e}){e.mediaType===1&&!this._pathJoinRoom.uint64_init_audio_start_time&&(this._pathJoinRoom.uint64_init_audio_start_time=Date.now()),e.mediaType===4&&!this._pathJoinRoom.uint64_init_camera_start_time&&(this._pathJoinRoom.uint64_init_camera_start_time=Date.now())}handleTrackCaptureSuccess({track:e}){e.mediaType===1&&!this._pathJoinRoom.uint64_init_audio_end_time&&(this._pathJoinRoom.int32_init_audio_ret=0,this._pathJoinRoom.uint64_init_audio_end_time=Date.now()),e.mediaType===4&&!this._pathJoinRoom.uint64_init_camera_end_time&&(this._pathJoinRoom.int32_init_camera_ret=0,this._pathJoinRoom.uint64_init_camera_end_time=Date.now())}handleTrackCaptureFailed({track:e,error:t}){let s={NotFoundError:1,NotAllowedError:2,NotReadableError:3,OverConstrainedError:4,AbortError:5,InvalidStateError:6,SecurityError:7,TypeError:8}[t.name]||(t instanceof A?t.getExtraCode()||t.getCode():E.UNKNOWN);e.mediaType===1&&!this._pathJoinRoom.uint64_init_audio_end_time&&(this._pathJoinRoom.int32_init_audio_ret=s,this._pathJoinRoom.uint64_init_audio_end_time=Date.now()),e.mediaType===4&&!this._pathJoinRoom.uint64_init_camera_end_time&&(this._pathJoinRoom.int32_init_camera_ret=s,this._pathJoinRoom.uint64_init_camera_end_time=Date.now())}handleAPISuccessRate({room:e,apiName:t,error:i,cost:s}){if(!this.hitTest(e))return;let o=Ml[t],n={uint32_function_request_type:o,uint32_avg_value:0,uint32_max_value:0,uint32_request_count:1,uint32_success_count:0,msg_error_code:[]};if(i){let c=i.extraCode||i.code||E.UNKNOWN;Y.logFailedEvent({eventType:t,code:c,userId:this._room.userId,error:i});let d={int32_error_code:c,error_count:1};n.msg_error_code.push(d)}else K(s)&&(Y.logSuccessEvent({eventType:t,userId:this._room.userId}),n.uint32_avg_value=s,n.uint32_max_value=s,n.uint32_success_count=1);let a=this._apiSuccessRateMap.get(o);a?(a.uint32_success_count+n.uint32_success_count>0&&(a.uint32_avg_value=(a.uint32_avg_value*a.uint32_success_count+n.uint32_avg_value)/(a.uint32_success_count+n.uint32_success_count)),a.uint32_max_value=Math.max(a.uint32_max_value,n.uint32_max_value),a.uint32_request_count+=1,a.uint32_success_count=a.uint32_success_count+n.uint32_success_count,n.msg_error_code.forEach(c=>{let d=a.msg_error_code.find(u=>u.int32_error_code===c.int32_error_code);d?d.error_count+=1:a.msg_error_code.push(c)})):this._apiSuccessRateMap.set(o,n)}hasVideoFlag(e){return this._firstPublishedUserList.findIndex(t=>t.userId===e&&t.flag&Yi)>=0}hasAudioFlag(e){return this._firstPublishedUserList.findIndex(t=>t.userId===e&&t.flag&zi)>=0}hasAuxFlag(e){return this._firstPublishedUserList.findIndex(t=>t.userId===e&&t.flag&Kr)>=0}hitTest(e){return e===this._room}async checkStorage(){try{let e=rr.getItem(this._storageKey);e&&(await this.upload(e),rr.deleteItem(this._storageKey))}catch(e){this._log.warn(e)}}setStorage(){this.prepareReport();let e=this.getReportData();e.msg_path_enter_room.uint64_start_time!==0&&rr.setItem(this._storageKey,e)}prepareReport(){if(this._networkQuality.count>0&&(this._basicInfo.uint32_avg_rtt=Math.floor(this._networkQuality.totalUplinkRTT/this._networkQuality.count),this._basicInfo.uint32_avg_up_loss=Math.floor(this._networkQuality.totalUplinkLoss/this._networkQuality.count)),this._localStreamStat.videoCount>0){this._localStreamStat.statsToReport.uint32_video_big_capture_fps=Math.floor(this._localStreamStat.totalVideoFPS/this._localStreamStat.videoCount),this._localStreamStat.statsToReport.uint32_video_big_bitrate=Math.floor(this._localStreamStat.totalVideoBitrate/this._localStreamStat.videoCount);let e=Math.floor(this._localStreamStat.totalVideoWidth/this._localStreamStat.videoCount),t=Math.floor(this._localStreamStat.totalVideoHeight/this._localStreamStat.videoCount);this._localStreamStat.statsToReport.uint32_video_big_resolution=e<<16|t}this._localStreamStat.audioLevelCount>0&&(this._localStreamStat.statsToReport.uint32_audio_capture_db=Math.floor(this._localStreamStat.totalAudioLevel/this._localStreamStat.audioLevelCount*100)),this._remoteStreamStatMap.forEach((e,t)=>{let{userId:i}=e,s=this._networkQuality.totalDownlinkRTTAndLossMap.get(i);if(s){let{totalLoss:u,count:h,audioDelayCount:p,videoDelayCount:g,totalAudioDelay:C,totalVideoDelay:N}=s;e.statsToReport.uint32_avg_down_loss=Math.floor(u/h),p&&C&&(e.statsToReport.uint32_audio_network_p2p_delay=Math.floor(C/p),e.audioJitterBufferDelay&&(e.statsToReport.uint32_p2p_delay=Math.floor(e.statsToReport.uint32_audio_network_p2p_delay+e.audioJitterBufferDelay))),g&&N&&(e.statsToReport.uint32_video_network_p2p_delay=Math.floor(N/g))}e.videoCount>0&&(e.statsToReport.uint32_video_avg_fps=Math.floor(e.totalVideoFPS/e.videoCount),e.statsToReport.uint32_video_avg_bitrate=Math.floor(e.totalVideoBitrate/e.videoCount)),e.audioCount>0&&(e.statsToReport.uint32_audio_recv_bitrate=e.statsToReport.uint32_audio_bitrate=Math.floor(e.totalAudioBitrate/e.audioCount)),e.audioLevelCount>0&&(e.statsToReport.uint32_audio_play_db=Math.floor(e.totalAudioLevel/e.audioLevelCount*100));let{callDurationCalculator:o}=this._room;o&&(e.statsToReport.uint32_audio_play_time=o.getDuration(t,l.AUDIO),e.statsToReport.uint32_video_play_time=o.getDuration(t,l.VIDEO)),e.statsToReport.uint32_video_render_first=Math.min(e.statsToReport.uint32_video_render_first,Oi);let{badCaseDetector:n}=this._room,{dataFreeze:a,count:c}=n.getDataFreezeDuration(t),{renderFreeze:d}=n.getRenderFreezeDuration(t);e.statsToReport.uint32_video_block_count=c,e.statsToReport.uint32_video_block_time=Math.min(a,e.statsToReport.uint32_video_play_time),e.statsToReport.uint32_video_external_block_time=Math.min(d,e.statsToReport.uint32_video_play_time),n.isBlackStream(t)&&e.statsToReport.uint32_video_avg_fps===0?e.statsToReport.uint32_video_black_screen_subjective=1:e.statsToReport.uint32_video_black_screen_subjective=0,(e.subscribeStartTime===0||e.subscribeStartTime-e.streamAddedTime>100||e.playStreamTime===0)&&(this._pathMainAudioMap.delete(t),this._pathMainVideoMap.delete(t),e.statsToReport.uint32_video_render_first=0)}),this._pathMainAudioMap.forEach((e,t)=>{if(!this.hasAudioFlag(e.userId)){this._pathMainAudioMap.delete(t);return}e.statsToReport.uint64_play_first_frame_time-e.statsToReport.uint64_start_enter_time>Oi&&(e.statsToReport.uint64_play_first_frame_time=e.statsToReport.uint64_start_enter_time+Oi)}),this._pathMainVideoMap.forEach((e,t)=>{if(!this.hasVideoFlag(e.userId)){this._pathMainVideoMap.delete(t);return}e.statsToReport.uint64_render_first_frame_time-e.statsToReport.uint64_start_enter_time>Oi&&(e.statsToReport.uint64_render_first_frame_time=e.statsToReport.uint64_start_enter_time+Oi)}),this._pathJoinRoom.uint64_end_time-this._pathJoinRoom.uint64_start_time>Oi&&(this._pathJoinRoom.uint64_end_time=this._pathJoinRoom.uint64_start_time+Oi)}getReportData(){return{uint32_sdk_app_id:Number(this._room.sdkAppId),msg_user_info:new Ds({userId:this._room.userId,tinyId:this._room.tinyId,role:this._room.role==="anchor"?20:21}),msg_basic_info:this._basicInfo,uint32_acc_ip:ts(this._signalInfo.relayIp),uint32_client_ip:ts(this._signalInfo.clientIp,!1),uint32_acc_port:this._signalInfo.relayPort||0,uint64_timestamp:Date.now(),uint32_seq:Math.floor(Math.random()*2147483648),msg_path_enter_room:this._pathJoinRoom,msg_path_exit_room:this._pathLeaveRoom,msg_path_recv_video:[...this._pathMainVideoMap.values()].map(t=>t.statsToReport),msg_quality_statistics:[...this._remoteStreamStatMap.values()].map(t=>t.statsToReport),str_room_name:String(this._room.roomId||0),msg_path_recv_audio:[...this._pathMainAudioMap.values()].map(t=>t.statsToReport),uint32_info_client_ip:ts(this._signalInfo.clientIp,!1),error_code:[],msg_local_statistics:this._localStreamStat.statsToReport,msg_function_request_stats:[...this._apiSuccessRateMap.values()]}}report(){try{this.prepareReport();let e=this.getReportData();this.upload(e),rr.deleteItem(this._storageKey),this.initData()}catch(e){this._log.warn(e)}}upload(e){if(Ke||e.msg_path_enter_room.uint64_start_time===0||[po,fa,Ta].findIndex(o=>o===location.host)>=0)return;let t=Number(this._room.sdkAppId),i=fi(t,li.KEY_POINT),s=!1;navigator.sendBeacon&&(s=navigator.sendBeacon(i,JSON.stringify(e))),s||Jt({url:i,body:JSON.stringify(e)})}setConnectionType(e){this.connectionType=e,this._basicInfo.uint32_connection_type=e}};y([Er({settings:{timeout:500,retries:3}})],hn.prototype,"upload",1);var Oi=5e3,vm={msg_user_info:null,uint32_video_avg_fps:0,uint32_video_width:0,uint32_video_height:0,uint32_video_avg_bitrate:0,uint32_video_block_time:0,uint32_video_play_time:0,uint32_audio_block_time:0,uint32_audio_play_time:0,uint32_audio_play_db:0,uint32_avg_down_loss:0,uint32_stream_type:0,uint32_video_render_first:0,uint32_video_block_count:0,uint32_audio_block_count:0,uint32_audio_bitrate:0,uint32_video_black_screen_subjective:0,uint32_audio_recv_bitrate:0,uint32_video_external_block_time:0,uint32_video_consume_render_rate:0},Ds=class{constructor(e){this.str_identifier=String(e.userId),this.str_tinyid=String(e.tinyId||0),this.uint32_role=e.role}},Ml=(R=>(R[R.enterRoom=50001]="enterRoom",R[R.exitRoom=50002]="exitRoom",R[R.switchRole=50003]="switchRole",R[R.destroy=50004]="destroy",R[R.startLocalAudio=50005]="startLocalAudio",R[R.updateLocalAudio=50006]="updateLocalAudio",R[R.stopLocalAudio=50007]="stopLocalAudio",R[R.startLocalVideo=50008]="startLocalVideo",R[R.updateLocalVideo=50009]="updateLocalVideo",R[R.stopLocalVideo=50010]="stopLocalVideo",R[R.startScreenShare=50011]="startScreenShare",R[R.updateScreenShare=50012]="updateScreenShare",R[R.stopScreenShare=50013]="stopScreenShare",R[R.enableAudioVolumeEvaluation=50014]="enableAudioVolumeEvaluation",R[R.startRemoteVideo=50015]="startRemoteVideo",R[R.updateRemoteVideo=50016]="updateRemoteVideo",R[R.stopRemoteVideo=50017]="stopRemoteVideo",R[R.muteRemoteAudio=50018]="muteRemoteAudio",R[R.setRemoteAudioVolume=50019]="setRemoteAudioVolume",R[R.startMixTranscode=50020]="startMixTranscode",R[R.stopMixTranscode=50021]="stopMixTranscode",R[R.startPublishCDNStream=50022]="startPublishCDNStream",R[R.stopPublishCDNStream=50023]="stopPublishCDNStream",R[R.PeerConnectionConnect=50024]="PeerConnectionConnect",R[R.PeerConnectionReconnect=50025]="PeerConnectionReconnect",R[R.WebsocketConnect=50026]="WebsocketConnect",R[R.WebsocketReconnect=50027]="WebsocketReconnect",R[R.schedule=50028]="schedule",R[R.SPCConnect=50029]="SPCConnect",R[R.SPCReconnect=50030]="SPCReconnect",R[R.videoCtxGl=50031]="videoCtxGl",R[R.videoCtxGlRecover=50032]="videoCtxGlRecover",R[R.videoCtx2d=50033]="videoCtx2d",R[R["CDNStreaming.startPlugin"]=50034]="CDNStreaming.startPlugin",R[R["CDNStreaming.updatePlugin"]=50035]="CDNStreaming.updatePlugin",R[R["CDNStreaming.stopPlugin"]=50036]="CDNStreaming.stopPlugin",R[R["AIDenoiser.startPlugin"]=50037]="AIDenoiser.startPlugin",R[R["AIDenoiser.updatePlugin"]=50038]="AIDenoiser.updatePlugin",R[R["AIDenoiser.stopPlugin"]=50039]="AIDenoiser.stopPlugin",R[R["AudioMixer.startPlugin"]=50040]="AudioMixer.startPlugin",R[R["AudioMixer.updatePlugin"]=50041]="AudioMixer.updatePlugin",R[R["AudioMixer.stopPlugin"]=50042]="AudioMixer.stopPlugin",R))(Ml||{}),Ll=hn;var nd=class{_startTime;_endTime;constructor(){this._startTime=0,this._endTime=0,this.start()}start(){this._startTime===0&&(this._startTime=U())}stop(){this._endTime===0&&(this._endTime=U())}getDuration(){return this._endTime===0?U()-this._startTime:this._endTime-this._startTime}get endTime(){return this._endTime}},gr=nd;var ad=class{_room=null;_durationMap;_eventMap=new Map;constructor(e){this._room=e.room,this._durationMap=new Map,this.installEvents()}installEvents(){this._eventMap.set(m.SUBSCRIBE_SUCCESS,this.handleSubscribed).set(m.UNSUBSCRIBE_SUCCESS,this.handleStreamStopped).set(m.REMOTE_PUBLISH_STATE_CHANGED,({room:e,prevMuteState:t,muteState:i})=>{let{userId:s}=i;if(!this.hitTest(e))return;t.hasAudio&&!i.hasAudio&&this.stopDurationItem(`${s}_${"main"}`,l.AUDIO),t.hasVideo&&!i.hasVideo&&this.stopDurationItem(`${s}_${"main"}`,l.VIDEO),t.hasAuxiliary&&!i.hasAuxiliary&&this.stopDurationItem(`${s}_${"auxiliary"}`,l.VIDEO);let o=this._room?.remotePublishedUserMap.get(s);!o||(!t.hasAudio&&i.hasAudio&&o.remoteAudioTrack.isSubscribed&&this.addDuractionItem(s,l.AUDIO,"main"),!t.hasVideo&&i.hasVideo&&o.remoteVideoTrack.isSubscribed&&this.addDuractionItem(s,l.VIDEO,"main"),!t.hasAuxiliary&&i.hasAuxiliary&&o.remoteAuxiliaryTrack.isSubscribed&&this.addDuractionItem(s,l.VIDEO,"auxiliary"))}),this._eventMap.forEach((e,t)=>_.on(t,e,this))}uninstallEvents(){this._eventMap.forEach((e,t)=>_.off(t,e,this)),this._eventMap.clear()}handleSubscribed({room:e,streamType:t,remotePublishedUser:i}){if(!this.hitTest(e))return;let{userId:s}=i,o=`${s}_${t}`;if(i.muteState.hasAudio&&t==="main")if(i.remoteAudioTrack.isSubscribed){let n=new gr,a=this._durationMap.get(o);a?this.isRecording(a.audio)||a.audio.push(n):this._durationMap.set(o,{userId:s,type:t,audio:[n],video:[]})}else this.stopDurationItem(o,l.AUDIO);if(i.muteState.hasVideo||i.muteState.hasAuxiliary)if(i.remoteVideoTrack.isSubscribed||i.remoteAuxiliaryTrack.isSubscribed){let n=new gr,a=this._durationMap.get(o);a?this.isRecording(a.video)||a.video.push(n):this._durationMap.set(o,{userId:s,type:t,audio:[],video:[n]})}else this.stopDurationItem(o,l.VIDEO)}handleStreamStopped({room:e,streamType:t,remotePublishedUser:i}){if(!this.hitTest(e))return;let{userId:s}=i,o=`${s}_${t}`;this.stopDurationItem(o,l.AUDIO),this.stopDurationItem(o,l.VIDEO)}isRecording(e){return e.findIndex(t=>t.endTime===0)>=0}addDuractionItem(e,t,i){let s=`${e}_${i}`,o=new gr,n=this._durationMap.get(s);n?this.isRecording(n[t])||n[t].push(o):this._durationMap.set(s,{userId:e,type:i,audio:t===l.AUDIO?[o]:[],video:t===l.AUDIO?[]:[o]})}stopDurationItem(e,t){if(this._durationMap.has(e)){let s=this._durationMap.get(e)[t].find(o=>o.endTime===0);s&&s.stop()}}hitTest(e){return this._room===e}getDuration(e,t){return this._durationMap.has(e)?this._durationMap.get(e)[t].reduce((s,o)=>s+o.getDuration(),0):0}getDurationMap(){return this._durationMap}reset(){this._durationMap.clear()}destroy(){this._room=null,this.uninstallEvents()}},xl=ad;var cd=class{_room;_renderFreezeMap=new Map;_isVideoPlayingEventFiredMap=new Map;_dataFreezeMap=new Map;_monitorFreezeData=new Map;_eventMap=new Map;constructor(e){this._room=e.room,this.installEvents()}installEvents(){this._eventMap.set(m.LEAVE_SUCCESS,({room:e})=>{this.hitTest(e)&&this.stop()}).set(m.PLAY_TRACK_START,this.onPlayTrackStart).set(m.UNSUBSCRIBE_SUCCESS,({room:e,streamType:t,remotePublishedUser:i})=>{if(!this.hitTest(e))return;let{userId:s}=i,o=`${s}_${t}`;this.stopDataFreeze({key:o,userId:s,type:t})}).set(m.REMOTE_PUBLISH_STATE_CHANGED,({room:e,prevMuteState:t,muteState:i})=>{if(!this.hitTest(e))return;let{userId:s}=i;if(t.hasVideo&&!i.hasVideo){let o="main",n=`${i.userId}_${o}`;this.stopDataFreeze({key:n,userId:s,type:o})}if(t.hasAuxiliary&&!i.hasAuxiliary){let o="auxiliary",n=`${i.userId}_${o}`;this.stopDataFreeze({key:n,userId:s,type:o})}}).set(m.PLAYER_STATE_CHANGED,({track:e,state:t,reason:i,type:s})=>{if(!(!jt(e)||!this.hitTest(e.room)||s!==l.VIDEO)){if(t==="PLAYING"){let o=`${e.userId}_${e.streamType}`;this._isVideoPlayingEventFiredMap.set(o,!0)}i===l.MUTE?this.onVideoTrackMuted(e):i===l.UNMUTE&&this.onVideoTrackUnmuted(e)}}).set(m.HEARTBEAT_REPORT,this.onHearBeatReport),this._eventMap.forEach((e,t)=>_.on(t,e,this))}uninstallEvents(){this._eventMap.forEach((e,t)=>_.off(t,e,this)),this._eventMap.clear()}stop(){this._renderFreezeMap.clear(),this._dataFreezeMap.clear(),this._isVideoPlayingEventFiredMap.clear()}onVideoTrackMuted(e){if(!e.isSubscribed)return;let{userId:t,streamType:i}=e,s=`${t}_${i}`,o=this._dataFreezeMap.get(s),n=new gr;o?o.durationItemList.push(n):this._dataFreezeMap.set(s,{userId:t,type:i,durationItemList:[n],isFreezing(){let a=this.durationItemList[this.durationItemList.length-1];return a&&a.endTime===0}})}onVideoTrackUnmuted(e){if(!e.isSubscribed)return;let{userId:t,streamType:i}=e,s=`${t}_${i}`;this.stopDataFreeze({key:s,userId:t,type:i})}onHearBeatReport({room:e,report:t}){!this.hitTest(e)||t.msg_down_stream_info.forEach(i=>{let s=this._room.remotePublishedUserMap.get(i.msg_user_info.str_identifier);if(!s)return;let{userId:o,muteState:n}=s;i.msg_video_status.forEach(a=>{a.uint32_video_stream_type===2&&n.hasVideo&&!n.videoMuted&&s.remoteVideoTrack.isSubscribed&&this.handleRenderFreeze({userId:o,fps:a.uint32_video_dec_fps,type:"main"}),a.uint32_video_stream_type===7&&n.hasAuxiliary&&s.remoteAuxiliaryTrack.isSubscribed&&this.handleRenderFreeze({userId:o,fps:a.uint32_video_dec_fps,type:"auxiliary"})})})}stopDataFreeze({key:e,userId:t,type:i}){let s=this._dataFreezeMap.get(e);if(!s||!s.isFreezing())return;let o=s.durationItemList[s.durationItemList.length-1];o.stop();let n=o.getDuration();n>So?this._monitorFreezeData.set(e,{userId:t,type:i,duration:n}):s.durationItemList.pop()}getTotalDuration(e){return e.reduce((t,i)=>{let s=i.getDuration();return t+Math.min(s,5e3)},0)}async handleRenderFreeze({userId:e,fps:t,type:i}){let s=`${e}_${i}`,o=this._renderFreezeMap.get(s);if(t<=2){let n=U();o&&!o.isFreeze&&(o.freezeTimeline.push({startTime:n,endTime:0}),o.isFreeze=!0),o||this._renderFreezeMap.set(s,{userId:e,type:i,isFreeze:!0,freezeTimeline:[{startTime:n,endTime:0}],renderFreezeTotal:0})}else if(o&&o.isFreeze){o.isFreeze=!1;let n=o.freezeTimeline.pop();if(n){n.endTime=U();let a=n.endTime-n.startTime;o.freezeTimeline.push(n),o.renderFreezeTotal+=Math.min(5e3,a)}}}onPlayTrackStart({track:e}){if(!jt(e)||!this.hitTest(e.room)||e.kind!==l.VIDEO||e.hasFlag)return;let t=`${e.userId}_${e.streamType}`;this._isVideoPlayingEventFiredMap.has(t)||this._isVideoPlayingEventFiredMap.set(t,!1)}getDataFreezeDuration(e){let t={dataFreeze:0,count:0},i=this._dataFreezeMap.get(e);if(i){if(i.isFreezing()){let s=i.durationItemList[i.durationItemList.length-1];s.stop(),s.getDuration()<So&&i.durationItemList.pop()}t.dataFreeze=this.getTotalDuration(i.durationItemList),t.count=i.durationItemList.length}return t}getRenderFreezeDuration(e){let t=this._renderFreezeMap.get(e),i=0,s=0;if(t)if(!t.isFreeze)i=t.renderFreezeTotal;else{let o=U(),n=t.freezeTimeline[t.freezeTimeline.length-1],a=o-n.startTime;i=t.renderFreezeTotal+Math.min(a,5e3),s=t.freezeTimeline.length}return{renderFreeze:i,count:s}}getMonitorFreeze(){return this._monitorFreezeData}isBlackStream(e){return this._isVideoPlayingEventFiredMap.has(e)?!this._isVideoPlayingEventFiredMap.get(e):!1}resetMonitor(){this._monitorFreezeData.clear()}hitTest(e){return e===this._room}destroy(){this.uninstallEvents()}},Vl=cd;var dd=null,ud=!0;document&&(document.head.innerHTML+=Object.values(pi).map(r=>`<link rel="dns-prefetch" href="https://${r}">`).join(`\r
`));function si(r){Z(r)&&r!==ud&&(ud=r,I.info(`setIsNeedToSchedule ${r}`))}_.on("28",()=>si(!0));_.on("63",()=>si(!0));_.on("84",()=>si(!0));_.on("201",r=>{r.state==="RECONNECTING"&&si(!0)});_.on("202",r=>{r.state==="RECONNECTING"&&si(!0)});function Dm(r,e,t){let i=performance.getEntriesByType("resource"),s=mn(r,l.MAIN),o=mn(r,l.BACKUP),n={totalCost:0,local:0,dns:0,tcp:0,tls:0,request:0,response:0};for(let a of i)if(a.startTime>=t&&(a.name===s||a.name===o)&&a.transferSize>0){let c=a.name===s?l.MAIN:l.BACKUP,d=Math.round(a.duration),u=Math.round(a.domainLookupStart-a.startTime),h=Math.round(a.domainLookupEnd-a.domainLookupStart),p=Math.round(a.requestStart-a.secureConnectionStart),g=Math.round(a.secureConnectionStart-a.connectStart),C=Math.round(a.responseStart-a.requestStart),N=Math.round(a.responseEnd-a.responseStart),Ce=[u,h,p,g,C,N];Y.uploadEvent({log:`stat-schedule-net:${d}(${Ce.join("->")}) ${c}`,userId:e}),n={...n,totalCost:d,local:u,dns:h,tcp:g,tls:p,request:C,response:N};break}return n}async function wl({userId:r,sdkAppId:e,useStringRoomId:t,roomId:i,userSig:s,version:o,frameWorkType:n}){if(!ud&&dd)return{isCached:!0,result:dd};let a={delta:0,count:[1,1],msg:[],detail:[]};try{let c=new FormData;c.append("userId",String(r)),c.append("sdkAppId",String(e)),c.append("isStrGroupId",String(t)),c.append("groupId",String(i)),c.append("sdkVersion",o),c.append("userSig",String(s)),c.append("frameWorkType",String(n));let d=U(),u=await km(c,a,e);u.config&&(u.config.loggerDomain&&ui(u.config.loggerDomain),Z(u.config.scheduleCache)&&si(!u.config.scheduleCache)),a.delta=U()-d;let h=Dm(Number(e),r,d);return dd=u,{isCached:!1,result:u,detailCost:h}}catch(c){let d=oe(c)?c[0]:c,u=K(d.code)?d.code:0,h=`schedule failed${d.message?`: ${d.message}`:""}`,p=new A({code:E.SCHEDULE_FAILED,extraCode:u,message:v({key:b.JOIN_ROOM_FAILED,data:{error:h,code:u}})});throw I.error(p),p}}var pn={main:"",backup:""};function ld(r){oe(r)?(pn.main=r[0],pn.backup=r[1]):pn.main=r}function mn(r,e=l.MAIN){let t;return Ao(r)?t=e===l.MAIN?pi.MAIN_OVERSEA:pi.BACKUP_OVERSEA:t=e===l.MAIN?pi.MAIN:pi.BACKUP,`https://${pn[e]||t}/api/v1/config`}function Om(r,e,t){return new Promise((i,s)=>{Jt({url:r,body:e,timeout:t.timeout}).then(o=>{o.data.code===0?i(o.data.data):s({code:o.data.code,message:o.data.msg})}).catch(s)})}var Ul=(r,e)=>ht({retryFunction:Om,settings:{retries:3,timeout:0},onError:e,onRetrying:r});function km(r,e,t){return new Promise((i,s)=>{let o=null;Pa([Ul(n=>e.count[0]=n+1,({error:n,retry:a})=>{e.msg[0]=n.message,o||a()})(mn(t,l.MAIN),r,{get timeout(){return er(2+e.count[0])*1e3}}),Ul(n=>e.count[1]=n+1,({error:n,retry:a})=>{e.msg[1]=n.message,o||a()})(mn(t,l.BACKUP),r,{get timeout(){return er(2+e.count[1])*1e3}})]).then(n=>{o=n,i(o)}).catch(s)})}var Bl=pe(ve(),1);var Pm=[-1,-1,1,-1,-1,1,1,1],Mm=[0,0,1,0,0,1,1,1],Ae=class extends k{constructor(t,i){super();this.context=t;this.context.on("disconnect",this.close,this),this.name=i.name,this.matchInputSize=i.matchInputSize!==!1,this.width=i.width||t.width,this.height=i.height||t.height;let s=t._gl;if(!s){if(t.ctx2d&&i.create2d){let o=document.createElement("canvas"),n=typeof o.transferControlToOffscreen=="function"?o.transferControlToOffscreen():o;n.width=this.width,n.height=this.height,this.ctx2d=n.getContext("2d"),this.image=n,this._canvas=n}return}try{this.texCoordBuffer=this.createBuffer(Mm),this.positionBuffer=this.createBuffer(Pm),i.createTexture!==!1&&(this.texture=s.createTexture(),this.useTexture(),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MIN_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_MAG_FILTER,s.LINEAR),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_S,s.CLAMP_TO_EDGE),s.texParameteri(s.TEXTURE_2D,s.TEXTURE_WRAP_T,s.CLAMP_TO_EDGE),s.pixelStorei(s.PACK_ALIGNMENT,1),s.pixelStorei(s.UNPACK_ALIGNMENT,1)),i.useFbo&&(this.fbo=s.createFramebuffer(),this.useBufferFrame(),this.useTexture(),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,this.width,this.height,0,s.RGBA,s.UNSIGNED_BYTE,null),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,this.texture,0)),i.useDefaultProgram?this.program=this.context.defaultProgam:(i.vertexShaderSource||i.fragmentShaderSource)&&(this.vertexShader=i.vertexShaderSource?t.createShader(s.VERTEX_SHADER,i.vertexShaderSource):t.defaultVShader,this.fragmentShader=i.fragmentShaderSource?t.createShader(s.FRAGMENT_SHADER,i.fragmentShaderSource):t.defaultFShader,this.program=t.createProgram(this.vertexShader,this.fragmentShader))}catch(o){this.context.destroy(`${this.name} init failed`,o)}}name;input;output;texture;image;ctx2d=null;fbo;width=0;height=0;x=0;y=0;program;vertexShader;fragmentShader;totalFrames=0;dropFrames=0;matchInputSize=!0;texCoordBuffer;positionBuffer;lastInfo={name:"",timestamp:0,totalFrames:0,x:0,y:0,width:0,height:0,fps:0};cost=0;_canvas=null;connect(t,...i){return t.addInput(this,...i),this.output=t,t}addInput(t,...i){this.input=t,this.matchInputSize&&t.width&&t.height&&this.resize(t.width,t.height)}requestFrame(t){let i=this.context._gl,s=Date.now();if(i&&this.render(t)||this.context.ctx2d&&this.render2d(t))this.totalFrames++;else return!1;return this.cost=Date.now()-s,!0}render2d(t){return this.input?.requestFrame(t)?this.draw2d(this.input.image,0,0,this.width,this.height):!1}disconnect(...t){this.output?.removeInput(this,...t),delete this.output}removeInput(t,...i){delete this.input}close(){this.context.off("disconnect",this.close,this),this.output?.removeInput(this),delete this.output,this.input?.disconnect();let t=this.context._gl;t&&(t.deleteBuffer(this.texCoordBuffer),t.deleteBuffer(this.positionBuffer),this.fbo&&t.deleteFramebuffer(this.fbo),this.texture&&t.deleteTexture(this.texture),this.vertexShader&&this.vertexShader!==this.context.defaultVShader&&t.deleteShader(this.vertexShader),this.fragmentShader&&this.fragmentShader!==this.context.defaultFShader&&t.deleteShader(this.fragmentShader),this.program&&this.program!==this.context.defaultProgam&&t.deleteProgram(this.program)),this._canvas&&(this._canvas.width=0,this._canvas.height=0,this.ctx2d=null),this.removeAllListeners()}useTexture(){this.useTextures(this.texture)}useInputTexture(){this.useTextures(this.input?.texture)}useTextures(...t){let i=this.context._gl;t.forEach((s,o)=>{s&&(i.activeTexture(i.TEXTURE0+o),i.bindTexture(i.TEXTURE_2D,s))})}useProgram(){this.context._gl.useProgram(this.program)}useBufferFrame(){let t=this.context._gl;t.bindFramebuffer(t.FRAMEBUFFER,this.fbo||null)}createBuffer(t){let i=this.context._gl,s=i.createBuffer();return i.bindBuffer(i.ARRAY_BUFFER,s),i.bufferData(i.ARRAY_BUFFER,new Float32Array(t),i.STATIC_DRAW),s}setTexBuffer(t){let i=this.context._gl;i.bindBuffer(i.ARRAY_BUFFER,this.texCoordBuffer),i.bufferData(i.ARRAY_BUFFER,new Float32Array(t),i.STATIC_DRAW)}setPosBuffer(t){let i=this.context._gl;i.bindBuffer(i.ARRAY_BUFFER,this.positionBuffer),i.bufferData(i.ARRAY_BUFFER,new Float32Array(t),i.STATIC_DRAW)}changeBufferData(t,i){let s=this.context._gl;s.bindBuffer(s.ARRAY_BUFFER,t),s.bufferData(s.ARRAY_BUFFER,new Float32Array(i),s.STATIC_DRAW)}setAttributes(...t){let i=this.context._gl;t.forEach((s,o)=>{i.enableVertexAttribArray(o),i.bindBuffer(i.ARRAY_BUFFER,s),i.vertexAttribPointer(o,2,i.FLOAT,!1,0,0)})}getVertexPoint(t,i){return[t/this.width*2-1,i/this.height*2-1]}layout2texCoords(t){return[...this.getVertexPoint(t.x,t.y),...this.getVertexPoint(t.x+t.width,t.y),...this.getVertexPoint(t.x,t.y+t.height),...this.getVertexPoint(t.x+t.width,t.y+t.height)]}resize(t,i){if(!(this.width===t&&this.height===i)){if(this.width=t,this.height=i,this._canvas&&(this._canvas.width=t,this._canvas.height=i),this.texture&&this.fbo){this.useTexture();let s=this.context._gl;s.texImage2D(s.TEXTURE_2D,0,s.RGBA,t,i,0,s.RGBA,s.UNSIGNED_BYTE,null)}this.output&&this.output.matchInputSize&&this.output.resize(t,i)}}draw(t,i){this.setAttributes(t||this.positionBuffer,i||this.texCoordBuffer);let s=this.context._gl;s.drawArrays(s.TRIANGLE_STRIP,0,4)}draw2d(t,i,s,o,n){return this.ctx2d&&t?(t instanceof ImageData?this.ctx2d.putImageData(t,i,s):this.ctx2d.drawImage(t,i,s,o,n),!0):!1}getInfo(){let{totalFrames:t,x:i,y:s,width:o,height:n,name:a,cost:c}=this,d=Date.now(),u=(t-this.lastInfo.totalFrames)/((d-this.lastInfo.timestamp)/1e3)>>0;return this.lastInfo={totalFrames:t,x:i,y:s,width:o,height:n,timestamp:d,fps:u,name:a,cost:c},{parent:this.input?.getInfo(),...this.lastInfo}}createTexture(t){let i=this.context._gl,s=i.createTexture();return this.useTextures(s),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,i.LINEAR),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,i.LINEAR),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,i.CLAMP_TO_EDGE),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,i.CLAMP_TO_EDGE),i.pixelStorei(i.PACK_ALIGNMENT,1),i.pixelStorei(i.UNPACK_ALIGNMENT,1),i.texImage2D(i.TEXTURE_2D,0,i.RGBA,i.RGBA,i.UNSIGNED_BYTE,t),s}};y([q(k.INIT,"connected")],Ae.prototype,"connect",1),y([q("connected",k.INIT,{ignoreError:!0})],Ae.prototype,"disconnect",1),y([q([],"closed")],Ae.prototype,"close",1);var Lm=[0,1,1,1,0,0,1,0],hd=class extends Ae{_intervalId=0;_sequence=0;start(e){this._intervalId=J.run("intervalInWorker",()=>{e!==this.context.frameRate&&(J.clearTask(this._intervalId),this.start(this.context.frameRate)),this.requestFrame(this._sequence++);let t=this.context._gl;if(t){let i=t.getError();i&&this.context.destroy(`${this.name} req ${this._sequence} render ${this.totalFrames} faild ${i}`)}},{fps:this.context.frameRate})}constructor(e,t){super(e,Object.assign({useDefaultProgram:!0,createTexture:!1,name:"destination"},t)),e._gl?this.setTexBuffer(Lm):e.ctx2d&&(this.ctx2d=e.ctx2d)}render(e){return this.input?.requestFrame(e)?(this.useProgram(),this.useBufferFrame(),this.useInputTexture(),this.draw(),!0):!1}addInput(e,...t){super.addInput(e,...t),this.start(this.context.frameRate)}removeInput(e){super.removeInput(e),J.clearTask(this._intervalId)}},Os=class extends hd{_videoTrack;constructor(e,t){super(e,t),[this._videoTrack]=e._canvas.captureStream(e.frameRate).getVideoTracks();let i=s=>{let o=()=>this._videoTrack.removeEventListener(s,n),n=()=>{o(),this.context.destroy(`video track ${s}`)};this.once("closed",o),this._videoTrack.addEventListener(s,n)};e._gl&&i("mute"),i("ended")}get videoTrack(){return this._videoTrack}close(){super.close(),this._videoTrack.stop()}resize(e,t){super.resize(e,t),this.context.setSize(e,t)}};var _n=class extends Os{constructor(t,i){super(t,{name:"smallDestination"});this.resolution=i}resize(t,i){let s,o=t*i,n=this.resolution.width*this.resolution.height;I.info(`big res: ${t}*${i} small res: ${this.resolution.width}*${this.resolution.height} `),o>n?s=o/n:(I.warn(`Small stream resolution is not smaller than big stream, which is invalid. big: ${t} * ${i} small: ${this.resolution.width} * ${this.resolution.height}`),s=o/(160*120)),super.resize(t/Math.sqrt(s),i/Math.sqrt(s))}};var ks=class extends Ae{constructor(t,i){super(t,{name:"imageSource"});this.image=i}_image;_totalFrames=0;_render(t,i){let{width:s,height:o}=this;if(this.image instanceof HTMLVideoElement){if(W(this.image.getVideoPlaybackQuality)&&!la){let a=this.image.getVideoPlaybackQuality().totalVideoFrames;if(this._totalFrames===a)return!1;this._totalFrames=a,this.dropFrames=this._totalFrames-this.totalFrames}({videoWidth:s,videoHeight:o}=this.image),this.image.width=s,this.image.height=o}else if(this.image instanceof HTMLImageElement||this.image instanceof ImageData||this.image instanceof ImageBitmap){if({width:s,height:o}=this.image,this.image!==this._image)this._image=this.image;else if(s===this.width&&o===this.height)return!1}else(this.image instanceof HTMLCanvasElement||this.image instanceof OffscreenCanvas)&&({width:s,height:o}=this.image,this._image=this.image);if(this.width===s&&this.height===o&&this.totalFrames){if(i){this.useTexture();let n=this.context._gl;n.texSubImage2D(n.TEXTURE_2D,0,0,0,n.RGBA,n.UNSIGNED_BYTE,this.image)}}else{if(i){this.useTexture();let n=this.context._gl;n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,this.image)}this.resize(s,o)}return!0}render(t){return this._render(t,!0)}render2d(t){return this._render(t,!1)}},fn=class extends ks{constructor(t,i){super(t,document.createElement("video"));this._videoTrack=i;this.name="videoTrackSource",this._mediaStream=new MediaStream([i]),this.image.srcObject=this._mediaStream,this.image.play()}_mediaStream;replaceTrack(t){this._videoTrack!==t&&(this._mediaStream?.removeTrack(this._videoTrack),this._videoTrack=t,this._mediaStream?.addTrack(this._videoTrack),this.image.play())}close(){super.close(),delete this._mediaStream,this.image.srcObject=null}};var xm=`
// \u9876\u70B9\u7740\u8272\u5668
attribute vec4 a_position;
attribute vec2 a_texCoord;
varying vec2 v_texCoord;

void main() {
  gl_Position = a_position;
  v_texCoord = a_texCoord;
}
`,Vm=`
// \u7247\u5143\u7740\u8272\u5668
precision mediump float;
varying vec2 v_texCoord;
uniform sampler2D u_texture;

void main() {
  gl_FragColor = texture2D(u_texture, v_texCoord);
} `,ki=class extends k{frameRate;_canvas;_gl;ctx2d;defaultProgam;defaultVShader;defaultFShader;log;hasAlpha=!1;glFaild=!1;name;constructor(e){super(),this.name=e.name,this.log=e.logger.createChild({id:`vc-${this.name}`}),this.frameRate=e.frameRate}create(e){if(this.glFaild=!1,this.hasAlpha=e.alpha,this._canvas=document.createElement("canvas"),e.use2d){this.ctx2d=this._canvas.getContext("2d",{alpha:e.alpha}),this.log.info("video context created use 2d");return}let t={alpha:e.alpha,antialias:!1,premultipliedAlpha:!1,preserveDrawingBuffer:!1,depth:!1,stencil:!1,failIfMajorPerformanceCaveat:!0,powerPreference:"low-power"},i=this._canvas.getContext("webgl2",t);if(i){this._gl=i;try{this.defaultVShader=this.createShader(i.VERTEX_SHADER,xm),this.defaultFShader=this.createShader(i.FRAGMENT_SHADER,Vm),this.defaultProgam=this.createProgram(this.defaultVShader,this.defaultFShader),this._canvas.addEventListener("webglcontextlost",()=>{this.destroy("webglcontextlost")}),this.log.info("video context created use webgl")}catch(s){return this.log.warn("video context create faild use webgl"),s}}else this.ctx2d=this._canvas.getContext("2d"),this.ctx2d&&this.log.info("video context created downgrade to 2d");!this.ctx2d&&!this._gl&&delete this._canvas}get available(){return!!this._canvas}createVideoTrackSource(e){return new fn(this,e)}createVideoTrackDestination(e){return new Os(this,e)}createVideoImageSource(e){return new ks(this,e)}set width(e){this._gl?.viewport(0,0,e,this.height),this._canvas&&(this._canvas.width=e)}get width(){return this._canvas?.width||0}set height(e){this._gl?.viewport(0,0,this.width,e),this._canvas&&(this._canvas.height=e)}get height(){return this._canvas?.height||0}setSize(e,t){this._gl?.viewport(0,0,e,t),this._canvas&&(this._canvas.width=e,this._canvas.height=t)}disconnect(){this.emit("disconnect")}destroy(e,t){return this.disconnect(),this.log.info(`video context destroy ${e?`: ${e}`:""}`),this._gl&&(this._gl.deleteShader(this.defaultVShader),this._gl.deleteShader(this.defaultFShader),this._gl.deleteProgram(this.defaultProgam),delete this._gl),delete this.ctx2d,this._canvas&&(this._canvas.remove(),this._canvas.width=0,this._canvas.height=0,delete this._canvas),[e,t]}createShader(e,t){let i=this._gl,s=i.createShader(e);return i.shaderSource(s,t),i.compileShader(s),s}createProgram(e,t){let i=this._gl,s=i.createProgram();return i.attachShader(s,e),i.attachShader(s,t),i.linkProgram(s),i.getProgramParameter(s,i.LINK_STATUS)||this.log.error(i.getProgramInfoLog(s)),s}};y([q(k.INIT,"created")],ki.prototype,"create",1),y([q("created",k.INIT,{ignoreError:!0,success(r){r[0]&&(this.glFaild=!0,this.emit("unavailable",...r)),this.removeAllListeners()}})],ki.prototype,"destroy",1);var Tn=class{constructor(e,t){this.createMediapipeSolutionsWasm=t;this.locateFile=i=>`${e}/${i}`}canvas;locateFile;_glName;_solutionWasm;ctx;bindTexture(){this.ctx?.activeTexture(this.ctx.TEXTURE0),this.bindTexture2d(this._glName)}async initialize(e,t="selfie_segmentation_landscape.tflite"){console.time("createMediapipeSolutionsWasm"),await this.createMediapipeSolutionsWasm(this),console.timeEnd("createMediapipeSolutionsWasm"),this.canvas=e,this.createContext(e,!0,!0,{}),this._glName=this.createTexture(),this._solutionWasm=new this.SolutionWasm;let i=new this.StringList;i.push_back("segmentation_mask");let s=this.PacketListener.implement({onResults:n=>this.onResults(n.get(0).glName)});this._solutionWasm.attachMultiListener(i,s),i.delete(),this._solutionWasm.loadGraph(await fetch(this.locateFile("selfie_segmentation.binarypb")).then(n=>n.arrayBuffer())),this._solutionWasm.overrideFile("third_party/mediapipe/modules/selfie_segmentation/selfie_segmentation_landscape.tflite",await fetch(this.locateFile(t)).then(n=>n.arrayBuffer()));let o=new this.GraphOptionChangeRequestList;o.push_back({calculatorIndex:1,calculatorName:"",calculatorType:"GlScalerCalculator",fieldName:"flip_horizontal",valueBoolean:!1,valueNumber:0,valueString:""}),o.push_back({calculatorIndex:0,calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorModelSelection",fieldName:"int_value",valueBoolean:!1,valueNumber:1,valueString:""}),o.push_back({calculatorIndex:0,calculatorType:"InferenceCalculator",calculatorName:"",fieldName:"use_cpu_inference",valueBoolean:!1,valueNumber:0,valueString:""}),this._solutionWasm.changeOptions(o)}send(e,t){let i=new this.PacketDataList;i.pushTexture2d({stream:"input_frames_gpu",timestamp:performance.now()*1e3,glName:this._glName,width:e,height:t}),this._solutionWasm.send(i),i.delete()}close(){this.deleteTexture?.(this._glName),this._solutionWasm.delete()}};var Um=`#version 300 es
// \u9876\u70B9\u7740\u8272\u5668
in vec2 a_position;
in vec2 a_texCoord;
out vec2 v_texCoord;

void main() {
  gl_Position = vec4(a_position.x, a_position.y, 0, 1);
  v_texCoord = a_texCoord;
}
`;function wm(r,e){let t=new Array(e).fill(0).map(()=>new Array(e).fill(0)),i=0;for(let s=0;s<e;s++)for(let o=0;o<e;o++){let n=Math.sqrt(s*s+o*o);if(n<=e){let a=1/(2*Math.PI*r**2)*Math.exp(-(n**2)/(2*r**2));t[s][o]=a,i+=a*(s===0||o===0?1:s*o===0?2:4)}}for(let s=0;s<e;s++)for(let o=0;o<e;o++)t[s][o]/=i;return t}var En=class extends Ae{_mediaStream;_input;_output;_intervalId=0;_sequence=0;ready;image=document.createElement("video");_totalFrames;_mirror;_blur;_blurRadius=3;_bg;_bgTexture;_waterMarkTexture;_waterMark;_selfModel=!1;useTflite=!0;selfieSegmentation;_greenScreen;_smallCanvas;_smallCtx;constructor(e,t){super(e,{name:"allin1",create2d:!0,useDefaultProgram:!1,useFbo:!1,createTexture:!0}),this._input=t.input,this._mirror=!!t.mirror,this._bg=t.bg instanceof HTMLImageElement?t.bg:void 0,this._blur=t.bg==="blur",this._greenScreen=t.bg==="green",this._waterMark=t.waterMark,this._selfModel=t.selfModel??!1,t.blurRadius&&(this._blurRadius=t.blurRadius),this._mediaStream=new MediaStream([t.input]),this.useTflite=!!t.useTflite,this.image.srcObject=this._mediaStream,this.image.play(),this.initProgram(),[this._output]=e._canvas.captureStream(e.frameRate).getVideoTracks();let i=()=>{};this._output.onmute=i,this._isSegmentationEnabled?this.useTflite&&(this.selfieSegmentation=new Tn(t.assetPath||"./selfie_segmentation",t.createMediapipeSolutionsWasm),this.selfieSegmentation.onResults=this.onPredict.bind(this),this.selfieSegmentation.initialize(e._canvas,this._selfModel?"google_small.tflite":"selfie_segmentation_landscape.tflite").then(()=>{if(this._smallCanvas){let o=this.context._gl;this.selfieSegmentation.bindTexture(),o.texImage2D(o.TEXTURE_2D,0,o.RGBA,o.RGBA,o.UNSIGNED_BYTE,this._smallCanvas)}this.ready=!0}).catch(()=>this.context.destroy("selfie_segmentation init faild"))):this.ready=!0;let s=o=>{let n=()=>this._output.removeEventListener(o,a),a=()=>{n(),this.context.destroy(`video track ${o}`)};this.once("closed",n),this._output.addEventListener(o,a)};e._gl&&s("mute"),s("ended"),this.start(e.frameRate)}get _isSegmentationEnabled(){return this._bg||this._blur||this._greenScreen}onPredict(e){let t=this.context._gl;this.useProgram(),this.context.setSize(this.width,this.height),this._smallCanvas?this.useTexture():this.selfieSegmentation.bindTexture(),t.activeTexture(t.TEXTURE1),this.selfieSegmentation.bindTexture2d(e),t.activeTexture(t.TEXTURE2),t.bindTexture(t.TEXTURE_2D,this._bgTexture||null),t.activeTexture(t.TEXTURE3),t.bindTexture(t.TEXTURE_2D,this._waterMarkTexture||null),this.useBufferFrame(),t.drawArrays(t.TRIANGLE_STRIP,0,4),this.totalFrames++}initProgram(){let e=this.context._gl;this.vertexShader=this.context.createShader(e.VERTEX_SHADER,Um),this.fragmentShader=this.context.createShader(e.FRAGMENT_SHADER,`#version 300 es
    precision highp float;
    
    uniform sampler2D frame;
    uniform sampler2D mask;
    uniform sampler2D bg;
    uniform sampler2D waterMark;
    in vec2 v_texCoord;
    out vec4 outColor;
    void main() {
      vec2 coord = ${this._mirror?"vec2(1.0 - v_texCoord.x, v_texCoord.y)":"v_texCoord"};
      coord.y = 1.0 - coord.y;
      vec4 src_color = texture(frame, coord);
      float probability = texture(mask, coord).a;
      vec2 onePixel = 1.0 / vec2(textureSize(frame, 0));
${this._blur?this.getGaussianBlur(this._blurRadius):this._bg?`
outColor = mix(texture(bg, coord),src_color,probability);
`:this._greenScreen?`
outColor = mix(vec4(0.0,1.0,0.0,1.0),src_color,probability);
`:"outColor = src_color;"}
${this.getWaterMarkShader()}
    }`),this.program=this.context.createProgram(this.vertexShader,this.fragmentShader),this.useProgram(),this.setAttributes(this.positionBuffer,this.texCoordBuffer),e.uniform1i(e.getUniformLocation(this.program,"mask"),1),this._bg&&(e.uniform1i(e.getUniformLocation(this.program,"bg"),2),this._bgTexture=this.createTexture(this._bg)),this._waterMark&&(e.uniform1i(e.getUniformLocation(this.program,"waterMark"),3),this._waterMarkTexture=this.createTexture(this._waterMark.image))}getGaussianBlur(e=3){if(!this._blur)return"";let t=7,i=wm(3,7),s="";for(let o=1-t;o<t;o++)for(let n=1-t;n<t;n++){let a=i[Math.abs(o)][Math.abs(n)];a>0&&(s+=`
        gaussianBlur += texture(frame, coord+ vec2(${o*e}.0 * onePixel.x, ${n*e}.0 * onePixel.y)) * ${a};`)}return`
    vec4 gaussianBlur = vec4(0.0);
    ${s}
    outColor = mix(gaussianBlur,src_color,probability);
    `}getWaterMarkShader(){if(!this._waterMark)return"";let{x:e,y:t,width:i,height:s}=this._waterMark;return`
    coord = vec2(v_texCoord.x, 1.0 - v_texCoord.y);
    vec2 coord1 = vec2(float(${e}) * onePixel.x, float(${t})*onePixel.y);
    vec2 coord2 = vec2(float(${e+i}) * onePixel.x, float(${t+s})*onePixel.y);
    if (coord.x > coord1.x && coord.x < coord2.x && coord.y > coord1.y && coord.y < coord2.y) {
      vec4 waterColor = texture(waterMark, vec2((coord.x - coord1.x)  / onePixel.x / float(${i}), (coord.y -coord1.y) / onePixel.y / float(${s})));
      outColor = mix(outColor,waterColor,  waterColor.a);
    }
    `}start(e){this._intervalId=J.run("intervalInWorker",()=>{e!==this.context.frameRate&&(J.clearTask(this._intervalId),this.start(this.context.frameRate)),this._sequence++,this.requestFrame(this._sequence)},{fps:this.context.frameRate})}replaceTrack(e){this._input!==e&&(this._mediaStream?.removeTrack(this._input),this._input=e,this._mediaStream?.addTrack(this._input),this.image.play())}get videoTrack(){return this._output}render(e){if(!this.ready)return!1;if(typeof this.image.getVideoPlaybackQuality=="function"){let n=this.image.getVideoPlaybackQuality().totalVideoFrames;if(this._totalFrames===n)return!1;this._totalFrames=n,this.dropFrames=this._totalFrames-this.totalFrames}let t=this.context._gl,{videoWidth:i,videoHeight:s}=this.image;if(this.image.width=i,this.image.height=s,this.useTflite&&this.selfieSegmentation?this.selfieSegmentation.bindTexture():this.useTexture(),this._smallCanvas&&(this._smallCtx?.drawImage(this.image,0,0,this._smallCanvas.width,this._smallCanvas.height),t.texSubImage2D(t.TEXTURE_2D,0,0,0,t.RGBA,t.UNSIGNED_BYTE,this._smallCanvas),this.useTexture()),this.width!==i||this.height!==s||this._totalFrames?(this.resize(i,s),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,this.image)):t.texSubImage2D(t.TEXTURE_2D,0,0,0,t.RGBA,t.UNSIGNED_BYTE,this.image),this._isSegmentationEnabled){if(this.useTflite&&this.selfieSegmentation)return this._smallCanvas?this.selfieSegmentation.send(this._smallCanvas.width,this._smallCanvas.height):this.selfieSegmentation.send(i,s),!1}else this.useProgram(),this.useTexture(),t.activeTexture(t.TEXTURE3),t.bindTexture(t.TEXTURE_2D,this._waterMarkTexture||null),t.drawArrays(t.TRIANGLE_STRIP,0,4);return!0}close(){super.close(),this._bgTexture&&this.context._gl.deleteTexture(this._bgTexture),this._waterMarkTexture&&this.context._gl.deleteTexture(this._waterMarkTexture),J.clearTask(this._intervalId),this._output.onmute=null,this._output.stop(),this._mediaStream?.removeTrack(this._input),delete this._mediaStream,this.ready=!1,this.selfieSegmentation?.close()}resize(e,t){super.resize(e,t),this.context.setSize(e,t)}};var Bm=[1,0,0,0,1,1,0,1],gn=class extends Ae{constructor(e){if(super(e,{useDefaultProgram:!0,useFbo:!0,create2d:!0,name:"mirror"}),e._gl)try{this.setTexBuffer(Bm)}catch(t){e.destroy("mirror set texCoords faild",t)}else this.ctx2d&&setTimeout(t=>{t.scale(-1,1),t.translate(-this.width,0)},0,this.ctx2d)}render(e){return this.input?.requestFrame(e)?(this.useProgram(),this.useBufferFrame(),this.useInputTexture(),this.draw(),!0):!1}};var pd=class{constructor(e,t){this.node=e;this.layout=t}positionBuffer;get x(){return this.layout.x||this.node.x}get y(){return this.layout.y||this.node.y}get width(){return this.layout.width||this.node.width}get height(){return this.layout.height||this.node.height}get right(){return this.x+this.width}get bottom(){return this.y+this.height}},Sn=class extends Ae{inputs=[];constructor(e){super(e,{useDefaultProgram:!0,useFbo:!0,name:"mix",create2d:!0})}addInput(e,t){if(this.inputs[t.zIndex])throw new Error("input already exists");let i=new pd(e,t);this.inputs[t.zIndex]=i}resize(e,t){let i=this.inputs.reduce((s,o)=>o?Object.assign(s,{width:Math.max(s.width,o.right),height:Math.max(s.height,o.bottom)}):s,{width:0,height:0});super.resize(i.width,i.height),this.context._gl&&this.inputs.forEach(s=>{if(s){let o=this.layout2texCoords(s);s.positionBuffer?this.changeBufferData(s.positionBuffer,o):s.positionBuffer=this.createBuffer(o)}})}connect(e,...t){return super.connect(e,...t),this.resize(0,0),e}removeInput(e){this.inputs[this.inputs.findIndex(t=>t?.node===e)]=void 0}render(e){let t=this.context._gl;if(t.clearColor(0,0,0,0),!this.inputs.reduce((s,o)=>(!o||!o.node.requestFrame(e))&&s,!0)&&t){this.useProgram(),t.enable(t.BLEND),t.blendFunc(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA),this.useBufferFrame();for(let s=0;s<this.inputs.length;s++){let o=this.inputs[s];o&&(o.node.useTexture(),this.draw(o.positionBuffer))}return!0}return!1}render2d(e){if(!this.inputs.reduce((i,s)=>(!s||!s.node.requestFrame(e))&&i,!0)&&this.ctx2d){this.ctx2d.clearRect(0,0,this.width,this.height);for(let i=0;i<this.inputs.length;i++){let s=this.inputs[i];s&&this.draw2d(s.node.image,s.x,s.y,s.width,s.height)}return!0}return!1}getInfo(){let{totalFrames:e,x:t,y:i,width:s,height:o,name:n}=this,a=Date.now(),c=(e-this.lastInfo.totalFrames)/((a-this.lastInfo.timestamp)/1e3)>>0;return this.lastInfo={totalFrames:e,x:t,y:i,width:s,height:o,timestamp:a,fps:c,name:n},{parent:this.inputs.filter(d=>d).map(d=>d.node.getInfo()),...this.lastInfo}}close(){super.close(),this.inputs.forEach(e=>{if(e&&(e.node?.disconnect(),e.positionBuffer&&this.context._gl))try{this.context._gl.deleteBuffer(e.positionBuffer)}catch{}})}};var In=class extends Bl.EventEmitter{videoContext;destination;smallVideoContext;smallDestination;smallTrackSource;smallImageSource;_isMirror=!1;cameraTrack;cameraNode;waterMarkNode;mirrorNode;mixNode;screenTrack;screenNode;selfModel=!1;blurRadius=3;createMediapipeSolutionsWasm;_isMixScreen=!1;_hasWaterMark=!1;_virualBackground;_bgAssetPath;_log;_checkId=0;_room;_use2d=!1;_autoSwitchRenderMode=!0;_waterMarkOption;constructor({room:e}){super(),this._room=e,this._log=I.createLogger({id:"vm",userId:e.userId,sdkAppId:e.sdkAppId}),this.videoContext=new ki({frameRate:15,logger:this._log,name:"m"}),this.videoContext.on("unavailable",(t,i)=>{this.emit("error",{reason:t,error:i}),this._log.warn("video context unavailable",t,i,`glFaild:${this.videoContext.glFaild}`),_.emit("262",{room:this._room,apiName:"videoCtxGl",error:i||new Error(t)}),this.update()}),this.smallVideoContext=new ki({frameRate:15,logger:this._log,name:"s"}),this.enablePrintDetail()}get _hasVirualBg(){return!!this._virualBackground}get renderMode(){return this._autoSwitchRenderMode?"auto":this._use2d?"2d":"webgl"}set renderMode(e){if(this._autoSwitchRenderMode=e==="auto",this._autoSwitchRenderMode)return;let t=e==="2d";this._use2d!==t&&(this._use2d=t,this.clear(),this.videoContext.destroy(),this.videoContext.create({alpha:this._hasWaterMark||this._hasVirualBg,use2d:this._use2d}),this.update())}enablePrintDetail(e=2e3){this._checkId=J.run(_i,()=>{this.destination&&this._log.debug(this.destination.getInfo())},{delay:e})}destroy(){this.videoContext.destroy(),this.smallVideoContext.destroy(),J.clearTask(this._checkId)}get needAlpha(){return this._hasWaterMark||this._hasVirualBg}get active(){return(Hr&&pa||this._isMixScreen||this._isMirror||this._hasWaterMark||this._hasVirualBg)&&this.checkOrCreateVideoContext()}sendCreateResult(e="videoCtxGl",t){_.emit("262",{room:this._room,apiName:e,error:t})}checkOrCreateVideoContext(){if(this.videoContext.available){let t=!this.videoContext.hasAlpha&&this.needAlpha,i=this._autoSwitchRenderMode&&this._use2d===this._hasVirualBg;if(t||i)this.clear(),this.videoContext.destroy();else return!0}else this.videoContext.glFaild&&(this.clear(),this._use2d=!0,this._autoSwitchRenderMode=!1);this._autoSwitchRenderMode&&(this._use2d=!this._hasVirualBg);let e=this.videoContext.create({alpha:this.needAlpha,use2d:this._use2d});return this.videoContext._gl?this.sendCreateResult():(this._use2d||(this.emit("error",{reason:"create",error:e}),this._use2d=!0,this.sendCreateResult("videoCtxGl",e)),this.sendCreateResult("videoCtx2d",this.videoContext.ctx2d?void 0:new Error("create 2d context failed"))),this.videoContext.available}get smallTrack(){return this.smallDestination?.videoTrack}get hasSmall(){return!!this.smallTrack}get initialTrack(){return this.cameraTrack?.mediaTrack}_setMainOutput(e){let t=this.cameraTrack,{small:i,settings:s}=t;return i?(this.smallVideoContext.available||(this.smallVideoContext.create({alpha:!1,use2d:!0}),this.smallDestination=new _n(this.smallVideoContext,i)),this.smallVideoContext.frameRate=i.frameRate,this.smallDestination.resolution=i,e?(this.smallTrackSource&&(this.smallTrackSource.close(),delete this.smallTrackSource),this.smallImageSource?this.smallImageSource.image=e:(this.smallImageSource=this.smallVideoContext.createVideoImageSource(e),this.smallImageSource.connect(this.smallDestination))):(this.smallImageSource&&(this.smallImageSource.close(),delete this.smallImageSource),this.smallTrackSource?this.smallTrackSource.replaceTrack(this.initialTrack):(this.smallTrackSource=this.smallVideoContext.createVideoTrackSource(this.initialTrack),this.smallTrackSource.width=s.width,this.smallTrackSource.height=s.height,this.smallTrackSource.connect(this.smallDestination)))):this.smallVideoContext.available&&(this.smallVideoContext.destroy(),delete this.smallDestination,delete this.smallTrackSource,delete this.smallImageSource),t.setOutputMediaStreamTrack(e?this.destination.videoTrack:this.initialTrack)}async update(){if(!(!this.cameraTrack||!this.cameraTrack.mediaTrack)){if(this.active){let{settings:e,profile:t}=this.cameraTrack;if(this._use2d)(!this.destination||this.destination===this.cameraNode)&&(this.destination=this.videoContext.createVideoTrackDestination({name:"mainDestination"})),this.cameraNode?this.cameraNode.replaceTrack(this.initialTrack):(this.cameraNode=this.videoContext.createVideoTrackSource(this.initialTrack),this.cameraNode.resize(e.width,e.height));else{if(this.cameraNode&&this.cameraNode.close(),console.warn("createMediapipeSolutionsWasm",this.createMediapipeSolutionsWasm),!this.createMediapipeSolutionsWasm)throw new Error("there is no createMediapipeSolutionsWasm()");this.cameraNode=new En(this.videoContext,{input:this.cameraTrack.mediaTrack,mirror:this._isMirror,bg:this._virualBackground,selfModel:this.selfModel,waterMark:this._waterMarkOption,useTflite:!0,blurRadius:this.blurRadius,assetPath:this._bgAssetPath,createMediapipeSolutionsWasm:this.createMediapipeSolutionsWasm}),this.destination=this.cameraNode}this.videoContext.frameRate=t.frameRate}else return this.cameraNode&&this.clear(),this._setMainOutput();if(this._use2d){let e=this.cameraNode;e.disconnect(),this._isMirror&&(this.mirrorNode||(this.mirrorNode=new gn(this.videoContext)),e=e.connect(this.mirrorNode),e.disconnect(),this._log.info("start mirror")),this.mixNode&&this.mixNode.close(),delete this.mixNode,(this._isMixScreen||this._hasWaterMark)&&(this.mixNode=new Sn(this.videoContext),e.connect(this.mixNode,{zIndex:1}),this._hasWaterMark&&!this.waterMarkNode&&this._waterMarkOption&&(this.waterMarkNode=this.videoContext.createVideoImageSource(this._waterMarkOption.image),this.waterMarkNode.resize(this._waterMarkOption.width,this._waterMarkOption.height),this.waterMarkNode.x=this._waterMarkOption.x,this.waterMarkNode.y=this._waterMarkOption.y),this.waterMarkNode?.connect(this.mixNode,{zIndex:2}),this.screenTrack&&!this.screenNode&&(this.screenNode=this.videoContext.createVideoTrackSource(this.screenTrack.mediaTrack)),this.screenNode?.connect(this.mixNode,{zIndex:0}),e=this.mixNode,this._log.info("start mix",`${this.mixNode.width}x${this.mixNode.height}`)),e.connect(this.destination)}return this._setMainOutput(this.videoContext._canvas)}}changeInput(e,t){if(this._log.info("change input"),e instanceof ke)return this.setScreenTrack(e);if(e instanceof fe)return this.setCameraTrack(e);if(e instanceof Et)return e.setOutputMediaStreamTrack(e.mediaTrack)}removeInput(e){e instanceof ke?(this.screenNode?.close(),delete this.screenNode,delete this.screenTrack,this.update()):e instanceof fe?(this.clear(),delete this.cameraTrack,this.smallImageSource&&(this.smallImageSource.close(),delete this.smallImageSource),this.smallTrackSource&&(this.smallTrackSource.close(),delete this.smallTrackSource)):e instanceof Et}async setCameraTrack(e){if(this.cameraTrack=e,this.cameraNode)this.cameraNode.replaceTrack(e.mediaTrack),this._setMainOutput(this.videoContext._canvas);else return this.update()}setScreenTrack(e){return this.screenTrack=e,this._isMixScreen&&(this.screenNode?this.screenNode.replaceTrack(e.mediaTrack):this.update()),e.setOutputMediaStreamTrack(e.mediaTrack)}loadImage(e){return new Promise((t,i)=>{let s=new Image;s.crossOrigin="anonymous",s.src=e,s.onload=()=>t(s),s.onerror=i})}async setWatermark(e){let t=await this.loadImage(e.imageUrl),{x:i=0,y:s=0,width:o=t.width,height:n=t.height}=e;this._hasWaterMark=!0,this._waterMarkOption={x:i,y:s,width:o,height:n,image:t},this._log.info("start watermark",JSON.stringify({x:i,y:s,width:o,height:n})),this.update()}stopWatermark(){!this._hasWaterMark||(this._hasWaterMark=!1,this._log.info("stop watermark"),this.waterMarkNode?.close(),delete this.waterMarkNode,delete this._waterMarkOption,this.update())}async setVirtualBackground(e){if(this._use2d&&!this._autoSwitchRenderMode)return Promise.reject(new Error("not support virtual background in 2d mode"));e?e.type==="image"?this._virualBackground=await this.loadImage(e.imageUrl):(this.blurRadius=e.blurRadius||3,this._virualBackground=e.type):this._virualBackground=void 0,this._log.info(`${this._virualBackground?"start":"stop"} visual background, ${e?.type||""}`),this.update()}get mixScreen(){return this._isMixScreen}set mixScreen(e){this._isMixScreen=e,this._isMixScreen||(this.screenNode?.close(),delete this.screenNode),this.update()}set mirror(e){this._isMirror!==e&&(this._isMirror=e,this._isMirror||(this.mirrorNode?.close(),delete this.mirrorNode),this.update())}get mirror(){return this._isMirror}clear(){this.videoContext.disconnect(),delete this.destination,delete this.cameraNode,delete this.mirrorNode,delete this.screenNode,delete this.waterMarkNode}};var $m=0;var Rn=class extends k{constructor(t){super("room");this.seq=++$m;this.role="anchor";this.localTracks=new Set;this.enableAutoPlayDialog=!0;this.autoReceiveAudio=!0;this.autoReceiveVideo=!0;this.scheduleResult={domains:null,iceServers:null,iceTransportPolicy:null,trtcAutoConf:null};this._isUsingCachedSchedule=!1;this._log=I.createLogger({id:`r${this.seq}`});this._joinedTimestamp=0;this._isDestroyed=!1;this.useStringRoomId=!!t.useStringRoomId,Z(t.autoReceiveAudio)&&(this.autoReceiveAudio=t.autoReceiveAudio),Z(t.autoReceiveVideo)&&(this.autoReceiveVideo=t.autoReceiveVideo),Z(t.enableAutoPlayDialog)&&(this.enableAutoPlayDialog=t.enableAutoPlayDialog),this._sdkType=t.sdkType,this.keyPointManager=new Ll({room:this,frameWorkType:t.frameWorkType,component:t.component,language:t.language}),this.callDurationCalculator=new xl({room:this}),this.badCaseDetector=new Vl({room:this}),this.audioManager=new Bo({room:this}),this.videoManager=new In({room:this})}get isMainStreamPublished(){for(let t of this.localTracks)if(t.mediaType&4)return!0;return!1}get isAuxStreamPublished(){for(let t of this.localTracks)if(t.mediaType&2)return!0;return!1}get hasAuxStream(){for(let t of this.remotePublishedUserMap.values())if(t.muteState.hasAuxiliary)return!0;return this.isAuxStreamPublished}getLogger(){return this._log}get isJoined(){return this.state==="joined"}get isLeft(){return this.state==="left"}async addTrack(t){return this.publish(t)}async removeTrack(t){return this.unpublish(t)}async replaceTrack(t){}setEncodedDataProcessingListener(t){throw new Error("Method not implemented.")}enableAIVoice(t){throw new Error("Method not implemented.")}setProxyServer(t){if(j(t))t.startsWith("wss://")?this.proxy_ws=t:t.startsWith("https://")&&(this.proxy_wt=t);else if(Ue(t)){let{websocketProxy:i,webtransportProxy:s,loggerProxy:o,scheduleProxy:n,unifiedProxy:a}=t;this.proxy_ws=i,this.proxy_wt=s,this.proxy_unified=a,a?(ld([a,a]),ui(`https://${a}`)):(o&&ui(o),n&&ld(n))}}async getRemoteAudioStats(){let t={};return this.remotePublishedUserMap.forEach(i=>{t[i.userId]=i.remoteAudioTrack.stat}),t}async getTransportStats(){let t={rtt:this.quality?.uplinkRTT||0,downlinksRTT:{}};if(this.quality)for(let i of this.quality.downlinkInfo)t.downlinksRTT[i.userId]=i.rtt;return t}async getRemoteVideoStats(t="main"){let i={};return this.remotePublishedUserMap.forEach(s=>{let o=t==="auxiliary"?s.remoteAuxiliaryTrack:s.remoteVideoTrack;i[s.userId]=o.stat}),i}checkDestroy(){if(this._isDestroyed)throw new A({code:E.INVALID_OPERATION,message:v({key:b.CLIENT_DESTROYED,data:{funName:"join"}})})}destroy(){if(this.isJoined)throw this._log.warn(ue.INVALID_DESTROY),new A({code:E.INVALID_OPERATION,message:v({key:b.INVALID_DESTROY})});this._log.info("destroy room"),this.audioManager.destroy(),this.videoManager.destroy(),this.keyPointManager.destroy(),this.callDurationCalculator.destroy(),this.badCaseDetector.destroy(),this._isDestroyed=!0,_.emit(m.ROOM_DESTROY,{room:this})}async schedule(t,i,s){let o=U();try{let{isCached:n,result:a,detailCost:c}=await wl({userId:this.userId,sdkAppId:this.sdkAppId,roomId:t,useStringRoomId:this.useStringRoomId,version:s,userSig:this.userSig,frameWorkType:i});this._isUsingCachedSchedule=n,this._log.info(`schedule cache:${+n} ${qe(a,{keysToExclude:["username","credential"]})}`),n&&_.once(m.JOIN_RECEIVED_CMD_RES,()=>this.sendAbilityStatus({scheduleCache:1})),this.scheduleResult={...this.scheduleResult,...a},K(a.config?.retryCount)&&ya(a.config.retryCount),j(a.config?.loggerDomain)&&ui(a.config.loggerDomain),_.emit(m.JOIN_SCHEDULE_SUCCESS,{room:this,schedule:this.scheduleResult,detailCost:c}),_.emit(m.API_SUCCESS_RATE,{room:this,apiName:"schedule",cost:U()-o})}catch(n){throw _.emit(m.API_SUCCESS_RATE,{room:this,apiName:"schedule",error:n}),n}}};var jl=pe(ve());var md=pe(id()),$l=r=>{let e=ne(r),t={audioSsrc:0,audioRtxSsrc:0,bigVideoSsrc:0,bigVideoRtxSsrc:0,smallVideoSsrc:0,smallVideoRtxSsrc:0,auxVideoSsrc:0,auxVideoRtxSsrc:0};return e.media.forEach((i,s)=>{if(i.ssrcs&&!f(i.ssrcs[0].id)){let o=Number(i.ssrcs[0].id),n=Number(i.ssrcs.filter(a=>a.attribute==="cname")[1]?.id);switch(s){case 0:t.audioSsrc=o;break;case 1:t.bigVideoSsrc=o,t.bigVideoRtxSsrc=n;break;case 2:t.smallVideoSsrc=o,t.smallVideoRtxSsrc=n;break;case 3:t.auxVideoSsrc=o,t.auxVideoRtxSsrc=n;break}}}),t},Fl=(r,e)=>{let t=ne(r),i={ice:{ufrag:"",password:""},dtls:{hash:"",fingerprint:"",setup:""},audio:{codecs:[],extensions:[]},video:{codecs:[],extensions:[]},useDataChannel:e};i.ice.ufrag=String(t.media[0].iceUfrag),i.ice.password=t.media[0].icePwd||"",t.fingerprint&&(i.dtls.hash=t.fingerprint.type,i.dtls.fingerprint=t.fingerprint.hash,i.dtls.setup=t.setup||""),t.media[0].fingerprint&&(i.dtls.hash=t.media[0].fingerprint.type,i.dtls.fingerprint=t.media[0].fingerprint.hash),i.dtls.setup=t.media[0].setup||"";let s=t.media[0],o=t.media[1];s.ext&&(i.audio.extensions=s.ext.map(a=>({id:a.value,uri:a.uri}))),o.ext&&(i.video.extensions=o.ext.map(a=>({id:a.value,uri:a.uri})));let n={codec:s.rtp[0].codec,fmtp:s.fmtp[0].config,payload:s.fmtp[0].payload,rate:s.rtp[0].rate,channel:s.rtp[0].encoding,rtcpFb:[],rtx:0};s.rtcpFb?.forEach(({payload:a,type:c,subtype:d})=>{if(a===n.payload){let u={id:c,params:[]};d&&u.params.push(d),n.rtcpFb.push(u)}}),i.audio.codecs.push(n);for(let a=0;a<o.rtp.length;a++){if(["rtx","red","ulpfec"].includes(o.rtp[a].codec))continue;let c=o.fmtp.filter(d=>d.payload===o.rtp[a].payload)[0];i.video.codecs.push({payload:o.rtp[a].payload,codec:o.rtp[a].codec,fmtp:c?c.config:"",rate:o.rtp[a].rate,rtx:o.rtp[a+1]?.codec==="rtx"?o.rtp[a+1].payload:0,rtcpFb:(o?.rtcpFb||[]).filter(d=>d.payload===o.rtp[a].payload).map(({type:d,subtype:u})=>({id:d,params:u?[u]:[]}))})}return i},Hl=({serverAbility:r,clientAbility:e,offerSDP:t,enableCustomMessage:i})=>{let s=ne(t),o={extmapAllowMixed:"extmap-allow-mixed",groups:s.groups,icelite:"ice-lite",media:[],msidSemantic:{semantic:"",token:"WMS"},name:"-",origin:{address:"127.0.0.1",username:"-",sessionId:String(Date.now()),sessionVersion:1,netType:"IN",ipVer:4},timing:{start:0,stop:0},version:0},n={candidates:r.candidates.map(a=>({component:1,foundation:"1",generation:0,ip:a.ip,port:a.port,priority:a.priority,transport:a.foundation,type:a.type})),connection:{version:4,ip:"0.0.0.0"},direction:l.TRANSCEIVER_DIRECTION_RECVONLY,ext:r.audio.extensions.map(a=>({value:a.id,uri:a.uri})),fingerprint:{type:r.dtls.hash,hash:r.dtls.fingerprint},fmtp:[{payload:r.audio.codecs[0].payload,config:r.audio.codecs[0].fmtp}],icePwd:r.ice.password,iceUfrag:r.ice.ufrag,mid:"0",payloads:String(r.audio.codecs[0].payload),port:s.media[0].port,protocol:s.media[0].protocol,type:l.AUDIO,setup:r.dtls.setup,rtcpFb:r.audio.codecs[0].rtcpfb.map(a=>({payload:r.audio.codecs[0].payload,type:a.id,subtype:a.params[0]})),rtcpMux:"rtcp-mux",rtcpRsize:"rtcp-rsize",rtp:[{payload:r.audio.codecs[0].payload,codec:r.audio.codecs[0].codec,rate:r.audio.codecs[0].rate,encoding:r.audio.codecs[0].channels}]};return o.media.push(n),[1,2,3].forEach(a=>{o.media.push(_d({mid:a,serverAbility:r,clientAbility:e,parsedOffer:s}))}),i&&o.media.push(s.media.find(a=>a.mid==="dc")),He(o)},_d=({mid:r,serverAbility:e,clientAbility:t,parsedOffer:i,useAllCodec:s=!1})=>{let o={candidates:e.candidates.map(n=>({component:1,foundation:"1",generation:0,ip:n.ip,port:n.port,priority:n.priority,transport:n.foundation,type:n.type})),connection:{version:4,ip:"0.0.0.0"},direction:l.TRANSCEIVER_DIRECTION_RECVONLY,ext:e.video.extensions.map(n=>({value:n.id,uri:n.uri})),fingerprint:{type:e.dtls.hash,hash:e.dtls.fingerprint},fmtp:[],icePwd:e.ice.password,iceUfrag:e.ice.ufrag,mid:String(r),payloads:"",port:i.media[0].port,protocol:i.media[0].protocol,type:l.VIDEO,setup:e.dtls.setup,rtcpFb:[],rtcpMux:"rtcp-mux",rtcpRsize:"rtcp-rsize",rtp:[]};if(e.video.codecs.length>0)if(s)for(let n=0;n<e.video.codecs.length;n++)An(o,e.video.codecs[n]);else{let n=e.video.codecs.findIndex(a=>a.codec.toLowerCase()===(e.useVp8?"vp8":"h264"));An(o,e.video.codecs[n])}else if(s)for(let n=0;n<t.video.codecs.length;n++)An(o,t.video.codecs[n]);else An(o,t.video.codecs[0]);return o.payloads=o.payloads.trim(),o},An=(r,e)=>{r.payloads=`${r.payloads} ${e.payload}`,r.fmtp.push({payload:e.payload,config:e.fmtp}),r.rtcpFb=[...r.rtcpFb||[],...(e.rtcpfb||e.rtcpFb).map(t=>({payload:e.payload,type:t.id,subtype:t.params[0]}))],r.rtp.push({payload:e.payload,codec:e.codec.toUpperCase(),rate:e.rate}),e.rtx&&(r.payloads=`${r.payloads} ${e.rtx}`,r.fmtp.push({payload:e.rtx,config:`apt=${e.payload}`}),r.rtp.push({payload:e.rtx,codec:"rtx",rate:e.rate}))};function Fm(r){let e=new Set(["urn:ietf:params:rtp-hdrext:sdes:mid","urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id","urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id"]);r.ext&&(r.ext=r.ext.filter(t=>!e.has(t.uri)))}function Hm(r){if(!r.rtcpFb)return;let e=[];r.rtcpFb.forEach((t,i)=>{e.push(t),r.rtcpFb&&r.rtcpFb[i+1]?.payload!==t.payload&&t.type!=="rrtr"&&e.push({payload:t.payload,type:"rrtr"})}),r.rtcpFb=e}function Gm(r){r.type===l.VIDEO&&r.fmtp&&r.fmtp.forEach(e=>{e.config.includes("apt")||(e.config+=";sps-pps-idr-in-keyframe=1")})}function Wm(r){r.type===l.AUDIO&&r.fmtp&&r.fmtp.forEach(e=>{e.config+=";sprop-stereo=1;stereo=1"})}var Gl=r=>{let e=md.default.parse(r);return e.media.forEach(t=>{(t.type===l.AUDIO||t.type===l.VIDEO)&&(Hm(t),Gm(t),Wm(t),Fm(t)),t.payloads?.includes("datachannel")&&e.groups&&t.mid&&(e.groups[0].mids=e.groups[0].mids.replace(t.mid,"dc"),t.mid="dc")}),md.default.write(e)};var Rt=(c=>(c.TRACK="track",c.DATA_CHANNEL_MESSAGE="data_channel_msg",c[c.CONNECTION_STATE_CHANGED="connection-state-changed"]="CONNECTION_STATE_CHANGED",c[c.FIREWALL_RESTRICTION="firewall-restriction"]="FIREWALL_RESTRICTION",c.RECONNECTED="spc-reconnected",c.RECONNECT_FAILED="spc-reconnect-failed",c.ERROR="error",c.SEI_MESSAGE="sei-message",c))(Rt||{}),Kl=0,Jl=!1,jm=new Set,Km=r=>Kl>2&&!Jl&&jm.size===0&&r,Jm=1,Sr=class extends jl.default{stat={iceStartTime:0,iceEndTime:0,dtlsStartTime:0,dtlsEndTime:0,peerConnectionStartTime:0,peerConnectionEndTime:0};currentState="DISCONNECTED";_room;_signalChannel;_peerConnection=null;_datachannel=null;_enableCustomMessage;_log;_downlinkMIDMap=new Map;_downlinkMIDUserIDMap=new Map;_reconnectionTimer=-1;reconnectionCount=0;_clientAbility=null;_serverAbility=null;addDownlinkQueue=new Set;removeDownlinkQueue=new Set;_parsedAnswer=null;_updateSDPPromise=null;_waitForPCConnectedPromise;_waitForPCConnectedPromiseReject=null;_isSDPLogged=!1;seiCodec=null;get isH264EncodeSupported(){let e=this._room.checkSystemResult.detail.isH264EncodeSupported;return this._serverAbility&&(e=e&&!!this._serverAbility.video.codecs.find(t=>t.codec.toLowerCase()==="h264")),e}get isVP8EncodeSupported(){let e=this._room.checkSystemResult.detail.isVp8EncodeSupported;return this._serverAbility&&(e=e&&this._serverAbility.useVp8),e}get isUsingH264(){return!!this._peerConnection?.remoteDescription?.sdp.includes("H264")}get uplinkSSRC(){return this._peerConnection&&this._peerConnection.localDescription?$l(this._peerConnection.localDescription.sdp):{audioSsrc:0,audioRtxSsrc:0,bigVideoSsrc:0,bigVideoRtxSsrc:0,smallVideoSsrc:0,smallVideoRtxSsrc:0,auxVideoSsrc:0,auxVideoRtxSsrc:0}}constructor({signalChannel:e,room:t,enableCustomMessage:i}){super(),this._room=t,this._enableCustomMessage=i,this._signalChannel=e,this._log=I.createLogger({id:`spc${Jm++}`,userId:this._room.userId,sdkAppId:this._room.sdkAppId}),this._room.enableSEI&&Ze&&(this.seiCodec=new fr(this,this._log),this.seiCodec.onSEIMessage=s=>{this.emit("sei-message",{userId:this._downlinkMIDUserIDMap.get(s.mid),data:s.data,seiPayloadType:s.seiPayloadType,streamType:s.streamType})})}get isReconnecting(){return this.currentState==="RECONNECTING"||this._reconnectionTimer>0||this.reconnectionCount>0}get dtlsTransport(){if(!this._peerConnection)return null;let e=this._peerConnection.getSenders();return e.length===0?null:e[0].transport}async initialize(){try{this._peerConnection=new RTCPeerConnection({encodedInsertableStreams:this._room.enableSEI&&Ze,offerExtmapAllowMixed:!0,iceServers:this._room.getIceServers(),iceTransportPolicy:this._room.getIceTransportPolicy(),sdpSemantics:this._room.sdpSemantics,bundlePolicy:"max-bundle",rtcpMuxPolicy:"require",tcpCandidatePolicy:"disable",IceTransportsType:"nohost"}),this._peerConnection.oniceconnectionstatechange=()=>{if(!this._peerConnection)return;let t=this._peerConnection.iceConnectionState;this._log.debug(`ice state: ${t}`),t==="checking"&&this.stat.iceStartTime===0?this.stat.iceStartTime=Date.now():t==="connected"&&this.stat.iceEndTime===0&&(this.stat.iceEndTime=Date.now())},this._peerConnection.onconnectionstatechange=this.onConnectionStateChange.bind(this),this._peerConnection.ontrack=t=>this.emit("track",t),this._enableCustomMessage&&(this._datachannel=this._peerConnection.createDataChannel(`${this._room.userId}dc`),this._datachannel.binaryType="arraybuffer",this._datachannel.onopen=()=>{this._log.info("datachannel open")},this._datachannel.onclose=()=>{this._log.warn("datachannel close")},this._datachannel.onmessage=t=>{let i=new Td(t.data);this.emit("data_channel_msg",{data:i})},this._datachannel.onerror=t=>{this._log.warn("datachannel error",t)}),this._peerConnection.addTransceiver(l.AUDIO,{direction:l.TRANSCEIVER_DIRECTION_SENDONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:l.TRANSCEIVER_DIRECTION_SENDONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:l.TRANSCEIVER_DIRECTION_SENDONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:l.TRANSCEIVER_DIRECTION_SENDONLY});let e=await this._peerConnection.createOffer();return await this.setOffer(e),this.dtlsTransport&&(this.dtlsTransport.onstatechange=()=>{let{dtlsTransport:t}=this;!t||(this._log.debug(`dtls state: ${t.state}`),t.state==="connecting"&&this.stat.dtlsStartTime===0?this.stat.dtlsStartTime=Date.now():t.state==="connected"&&this.stat.dtlsEndTime===0&&(this.stat.dtlsEndTime=Date.now()))}),this._clientAbility=Fl(e.sdp,this._enableCustomMessage),this._clientAbility}catch(e){throw this._log.error(`initialize failed ${e}`),e}}async connect(e,t=!1){try{if(this.currentState==="CONNECTED")return;let i=U(),s={type:"answer",sdp:Hl({serverAbility:e,clientAbility:this._clientAbility,offerSDP:this._peerConnection.localDescription.sdp,enableCustomMessage:this._enableCustomMessage})};this._serverAbility=e,await this.setAnswer(s),await this.waitForPeerConnectionConnected(),t||_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"SPCConnect",cost:Math.min(U()-i,30*1e3)}),this.seiCodec?.handleEncodedStreams()}catch(i){let s=i instanceof A&&i.code===E.API_CALL_ABORTED;throw s||this._log.error(`connect failed: ${i}`,e),this.reset(),!s&&!this.isReconnecting&&(_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"SPCConnect",error:i}),this.emitConnectionStateChangedEvent("DISCONNECTED"),this.startReconnection()),i}}async reconnect(){if(this._reconnectionTimer!==-1){this._log.warn("reconnect() is reconnecting, ignore current reconnection");return}if(!this._signalChannel.isConnected){this._log.warn("reconnect() wait signal channel is connected"),this._signalChannel.once(se.CONNECTED,this.reconnect,this);return}try{this.reconnectionCount++,this._log.warn(`reconnect() trying [${this.reconnectionCount}]`),this.reset();let e=await this.initialize(),t=await this._signalChannel.sendWaitForResponse({command:H.REBUILD_PEER_CONNECTION,responseCommand:V.REBUILD_PEER_CONNECTION_RES,data:{ability:e},enableLog:!1});await this.connect(t.data.data.ability,!0),this._log.warn("reconnect() successfully"),this.stopReconnection(),this.emit("spc-reconnected")}catch(e){if(!this.isReconnecting)return;if(e?.message.includes("timeout")){let t=we(this.reconnectionCount);this._log.warn(`reconnect() timeout, try again after ${t/1e3}s`),this._reconnectionTimer=window.setTimeout(()=>{this.clearReconnectionTimer(),this.reconnect()},t)}else this._log.error(`reconnect() failed ${e}`),_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"SPCReconnect",error:e}),this.reconnectionCount>=vt()&&this._log.warn(`SDK has tried reconnect for ${vt()} times, but all failed, please check your network`),this.stopReconnection(),this.emitConnectionStateChangedEvent("DISCONNECTED"),this.emit("error")}}getPeerConnection(){return this._peerConnection}async startReconnection(){this._log.warn("start reconnect"),this.emitConnectionStateChangedEvent("RECONNECTING");let e=U();await this.reconnect(),_.emit(m.API_SUCCESS_RATE,{room:this._room,apiName:"SPCReconnect",cost:Math.min(U()-e,30*1e3)})}stopReconnection(){this.isReconnecting&&(this._log.info("stop reconnect"),this.reconnectionCount=0,this.clearReconnectionTimer(),this._signalChannel.off(se.CONNECTED,this.reconnect,this))}checkPeerConnectionToReconnect(){!this.isReconnecting&&this._peerConnection?.connectionState===Q.CLOSED&&this.startReconnection()}clearReconnectionTimer(){this._reconnectionTimer!==-1&&(clearTimeout(this._reconnectionTimer),this._reconnectionTimer=-1)}onConnectionStateChange(e){let t=this._peerConnection.iceConnectionState,i=this.getDTLSTransportState();this._log.info(`connectionState: ${e.target.connectionState} ICE: ${t} DTLS: ${i}`),e.target.connectionState===Q.CONNECTING&&(this.stat.peerConnectionStartTime===0&&(this.stat.peerConnectionStartTime=Date.now()),this.emitConnectionStateChangedEvent("CONNECTING")),(e.target.connectionState===Q.FAILED||e.target.connectionState===Q.CLOSED)&&(this.emitConnectionStateChangedEvent("DISCONNECTED"),this.startReconnection()),(e.target.connectionState===Q.CONNECTED||e.target.connectionState===Q.COMPLETED)&&(this.stat.peerConnectionEndTime===0&&(this.stat.peerConnectionEndTime=Date.now()),_.emit(m.SINGLE_CONNECTION_STAT,{room:this._room,stat:{ice:this.stat.iceEndTime-this.stat.iceStartTime,dtls:this.stat.dtlsEndTime-this.stat.dtlsStartTime,peerConnection:this.stat.peerConnectionEndTime-this.stat.peerConnectionStartTime}}),this.logSelectedCandidate(),this.emitConnectionStateChangedEvent("CONNECTED"))}getDTLSTransportState(){if(!this._peerConnection)return Ye;let e=null;return!sr()||this._peerConnection.getSenders().length===0?Ye:(e=this._peerConnection.getSenders()[0].transport,!Ti()||this._peerConnection.getReceivers().length===0?Ye:e?e.state:Ye)}emitConnectionStateChangedEvent(e){e!==this.currentState&&(this.currentState==="RECONNECTING"&&e==="CONNECTING"||(this.emit(Rt.CONNECTION_STATE_CHANGED,{prevState:this.currentState,state:e}),this.currentState=e))}async logSelectedCandidate(){if(!this._peerConnection)return;let e=await this._peerConnection.getStats();for(let[t,i]of e)if(zt(i)){let s=e.get(i.localCandidateId),o=e.get(i.remoteCandidateId);s&&this._log.info(`local candidate: ${s.candidateType} ${s.protocol}:${s.ip||s.address}:${s.port} ${s.networkType||""} ${s.candidateType==="relay"?`relayProtocol:${s.relayProtocol}`:""}`),o&&this._log.info(`remote candidate: ${o.candidateType} ${o.protocol}:${o.ip||o.address}:${o.port}`);break}}waitForPeerConnectionConnected(){return this._waitForPCConnectedPromise?this._waitForPCConnectedPromise:(this._waitForPCConnectedPromise=new Promise((e,t)=>{if(this.currentState==="CONNECTED")return e();this._waitForPCConnectedPromiseReject=t;let i=a=>{a.state==="CONNECTED"&&(clearTimeout(n),o(),e())},s=({room:a})=>{a===this._room&&(clearTimeout(n),o(),t(new A({code:E.API_CALL_ABORTED,message:v({key:b.CONNECTION_ABORTED,data:"leave room"})})))},o=()=>{_.off(m.LEAVE_SUCCESS,s,this),this.off(Rt.CONNECTION_STATE_CHANGED,i,this)},n=setTimeout(()=>{o();let a=new A({code:E.API_CALL_TIMEOUT,message:"connection timeout"});Kl+=1,Km(this._signalChannel.isConnected)&&(this._log.warn("firewall restrition"),Jl=!0,this.emit(Rt.FIREWALL_RESTRICTION)),t(a)},zr);_.on(m.LEAVE_SUCCESS,s,this),this.on(Rt.CONNECTION_STATE_CHANGED,i,this)}),this._waitForPCConnectedPromise=this._waitForPCConnectedPromise.finally(()=>{this._waitForPCConnectedPromise=null,this._waitForPCConnectedPromiseReject=null}),this._waitForPCConnectedPromise)}waitForReconnected(){return this.isReconnecting?new Promise((e,t)=>{this.once("spc-reconnected",e),this.once("error",t)}):Promise.resolve()}async addDownlink(e){if(this._log.info(`addDownlink(${e.userId}) trying`),this.isReconnecting&&await this.waitForReconnected(),this.updateLocalAndRemoteSDPConfig(e),this.addDownlinkQueue.size===0)try{await this.updateSDP({isNeedToCreateOffer:!0}),this.seiCodec?.handleEncodedStreams(),this._log.info(`addDownlink(${e.userId}) done`)}catch{this._log.info(`addDownlink(${e.userId}) failed`),await this.startReconnection()}}updateLocalAndRemoteSDPConfig({ssrc:e,userId:t,tinyId:i}){if(!this._peerConnection)return;this._log.info(`updateLocalAndRemoteSDPConfig ${t} ${JSON.stringify(e)}`);let s=this._peerConnection.getTransceivers().filter(d=>d.direction==="inactive").slice(0,3).map(d=>(d.direction=l.TRANSCEIVER_DIRECTION_RECVONLY,Number(d.mid)));s.length===0&&(this._peerConnection.addTransceiver(l.AUDIO,{direction:l.TRANSCEIVER_DIRECTION_RECVONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:l.TRANSCEIVER_DIRECTION_RECVONLY}),this._peerConnection.addTransceiver(l.VIDEO,{direction:l.TRANSCEIVER_DIRECTION_RECVONLY})),this._parsedAnswer||(this._parsedAnswer=ne(this._peerConnection.remoteDescription.sdp));let o,n,a;if(s.length===3)o=this._parsedAnswer.media.find(d=>Number(d.mid)===Number(s[0])),n=this._parsedAnswer.media.find(d=>Number(d.mid)===Number(s[1])),a=this._parsedAnswer.media.find(d=>Number(d.mid)===Number(s[2]));else{o=JSON.parse(JSON.stringify(this._parsedAnswer.media[0]));let d=_d({mid:1,serverAbility:this._serverAbility,clientAbility:this._clientAbility,parsedOffer:ne(this._peerConnection.localDescription.sdp),useAllCodec:!0});n=JSON.parse(JSON.stringify(d)),a=JSON.parse(JSON.stringify(d)),o.mid=this._parsedAnswer.media.length,this._parsedAnswer.media.push(o),n.mid=this._parsedAnswer.media.length,this._parsedAnswer.media.push(n),a.mid=this._parsedAnswer.media.length,this._parsedAnswer.media.push(a)}o.direction=l.TRANSCEIVER_DIRECTION_SENDONLY,o.ssrcs=[{id:e.audio,attribute:"cname",value:`${i}`},{id:e.audio,attribute:"msid",value:`${i}-${l.MAIN} ${i}-audio`}],n.direction=l.TRANSCEIVER_DIRECTION_SENDONLY,n.ssrcs=[{id:e.video,attribute:"cname",value:`${i}`},{id:e.video,attribute:"msid",value:`${i}-${l.MAIN} ${i}-bigvideo`},{id:e.videoRtx,attribute:"cname",value:`${i}`},{id:e.videoRtx,attribute:"msid",value:`${i}-${l.MAIN} ${i}-bigvideo`}],n.ssrcGroups=[{semantics:"FID",ssrcs:`${e.video} ${e.videoRtx}`}],a.direction=l.TRANSCEIVER_DIRECTION_SENDONLY;let c=`${i}-aux`;a.ssrcs=[{id:e.auxiliary,attribute:"cname",value:c},{id:e.auxiliary,attribute:"msid",value:`${c} ${i}-aux${l.VIDEO}`},{id:e.auxiliaryRtx,attribute:"cname",value:`${c} ${i}-aux${l.VIDEO}`},{id:e.auxiliaryRtx,attribute:"msid",value:`${c} ${i}-aux${l.VIDEO}`}],a.ssrcGroups=[{semantics:"FID",ssrcs:`${e.auxiliary} ${e.auxiliaryRtx}`}],this._parsedAnswer.groups&&(this._parsedAnswer.groups[0].mids=this._parsedAnswer.media.map(d=>d.mid).join(" ")),this._downlinkMIDMap.set(t,[o.mid,n.mid,a.mid]),this._downlinkMIDUserIDMap.set(o.mid,t),this._downlinkMIDUserIDMap.set(n.mid,t),this._downlinkMIDUserIDMap.set(a.mid,t)}async removeDownlink(e,t){if(!this._downlinkMIDMap.has(t)||!this._peerConnection)return;this._log.info(`removeDownlink(${t}) trying`),this.isReconnecting&&await this.waitForReconnected();let i=this._downlinkMIDMap.get(t),s=!1;this._peerConnection.getTransceivers().forEach(o=>{i?.includes(Number(o.mid))&&(s=!0,o.direction="inactive")}),this._parsedAnswer||(this._parsedAnswer=ne(this._peerConnection.remoteDescription.sdp)),this._parsedAnswer.media.forEach(o=>{i?.includes(Number(o.mid))&&(s=!0,o.direction="inactive",o.ssrcs=[],o.ssrcGroups=[])}),this.removeDownlinkQueue.size===0&&s&&await this.updateSDP({isNeedToCreateOffer:!0,tinyIdRemoving:e}),this._downlinkMIDMap.delete(t),i?.forEach(o=>this._downlinkMIDUserIDMap.delete(o)),this._log.info(`removeDownlink(${t}) done`)}async setBandwidth(e){if(!this._peerConnection)return;let{audio:t,bigVideo:i,smallVideo:s,auxVideo:o}=e;try{if(os()){let n=this._peerConnection.getSenders().slice(0,4);for(let a=0;a<n.length;a++){let c=n[a],d;a===0&&t?d=t:a===1&&i?d=i:a===2&&s?d=s:a===3&&o&&(d=o),d&&await this.setSenderMaxBitrate(c,d)}}else await this.setBandwidthBySDP(e);this._log.info(`setBandwidth ${JSON.stringify(e)}`)}catch(n){this._log.error(`failed to set bandwidth${n}`)}}setSenderMaxBitrate(e,t){let i=e.getParameters();return(!i.encodings||i.encodings.length===0)&&(i.encodings=[{}]),t==="unlimited"?delete i.encodings[0].maxBitrate:i.encodings[0].maxBitrate=t*1e3,e.setParameters(i)}setBandwidthBySDP({audio:e,bigVideo:t,smallVideo:i,auxVideo:s}){if(!this._peerConnection||!this._peerConnection.localDescription)return;let o=ne(this._peerConnection.localDescription.sdp);this._parsedAnswer||(this._parsedAnswer=ne(this._peerConnection.remoteDescription.sdp));let n=z?"TIAS":"AS";e&&(o.media[0].bandwidth=[{type:n,limit:z?e*1e3:e}],this._parsedAnswer.media[0].bandwidth=[{type:n,limit:z?e*1e3:e}]),t&&(o.media[1].bandwidth=[{type:n,limit:z?t*1e3:t}],this._parsedAnswer.media[1].bandwidth=[{type:n,limit:z?t*1e3:t}]),i&&(o.media[2].bandwidth=[{type:n,limit:z?i*1e3:i}],this._parsedAnswer.media[2].bandwidth=[{type:n,limit:z?i*1e3:i}]),s&&(o.media[3].bandwidth=[{type:n,limit:z?s*1e3:s}],this._parsedAnswer.media[3].bandwidth=[{type:n,limit:z?s*1e3:s}]);let a={type:"offer",sdp:He(o)};return this.updateSDP({localDescription:a})}updateSDP({isNeedToCreateOffer:e=!1,localDescription:t,tinyIdRemoving:i}){if(!this._parsedAnswer)return;let s=He(this._parsedAnswer);return this._updateSDPPromise=new Promise(async(o,n)=>{try{e&&this._peerConnection&&(this._log.info("creating offer"),t=await this._peerConnection.createOffer()),t&&await this.setOffer(t),await this.setAnswer({type:"answer",sdp:s}),this._updateSDPPromise=null,o()}catch(a){this._log.error(a),!this._isSDPLogged&&this._peerConnection&&(this._log.warn(`transceivers: ${JSON.stringify(this._peerConnection.getTransceivers().map(({mid:c,currentDirection:d,direction:u,stopped:h})=>({mid:c,currentDirection:d,direction:u,stopped:h})))}`),this._log.warn(`parsedAnswer: ${JSON.stringify(this._parsedAnswer)}`),this._log.warn(`local sdp: ${t?.sdp}`||this._peerConnection.localDescription?.sdp),this._log.warn(`remote sdp: ${s}`||this._peerConnection.remoteDescription?.sdp),this._isSDPLogged=!0),this._updateSDPPromise=null,n(a)}}),this._updateSDPPromise}setOffer(e){return this._log.info("setting offer"),this._peerConnection.setLocalDescription({type:"offer",sdp:Gl(e.sdp)})}setAnswer(e){return this._log.info("setting answer"),this._room.enableHWEncoder&&e.sdp&&(e.sdp=e.sdp.replaceAll("42e01f","42001f")),this._peerConnection.setRemoteDescription(e)}sendDataChannelMessage(e){this._datachannel?.send(e)}reset(){this._peerConnection?.close(),this._waitForPCConnectedPromise=null,this._parsedAnswer=null}close(){this._log.info("close pc"),this.reset(),this.emitConnectionStateChangedEvent("DISCONNECTED"),this._downlinkMIDMap.clear(),this.stopReconnection(),this.removeAllListeners()}sendSEI(e,t){this.seiCodec?.push(e,t)}};y([Ri()],Sr.prototype,"updateSDP",1);var fd=class{tag;len;data;constructor(e){let t=new DataView(e);this.tag=t.getUint16(),this.len=t.getUint16(2),this.data=new Uint8Array(e).slice(4,2+2+this.len).buffer}},Td=class{tinyId;data;constructor(e){let t=new DataView(e),i=0,s=[];for(;i<t.byteLength;){let o=t.getUint16(i+2),n=new fd(new Uint8Array(e).slice(i,i+2+2+o).buffer);s.push(n),i+=2+2+o}s.forEach(o=>{o.tag===1?this.tinyId=new TextDecoder().decode(o.data):o.tag===2&&(this.data=o.data)})}},Wl=new Set;function Pi(){let r=Math.floor(Math.random()*4294967296);return Wl.has(r)?Pi():(Wl.add(r),r)}var Yl=pe(ve());var Mi=class extends Yl.default{userId;tinyId;_sdpSemantics;_isUplink;_room;_log;_signalChannel;_currentState="DISCONNECTED";_prevTime=-1;_enableSEI;_sei;get _peerConnection(){return this.singlePC?.getPeerConnection()||null}get singlePC(){return this._room.singlePC}constructor(e){super(),this.userId=e.userId,this.tinyId=e.tinyId,this._room=e.room,this._sdpSemantics=e.room.sdpSemantics,this._isUplink=e.isUplink,this._log=I.createLogger({id:"n",userId:this._room.userId,remoteUserId:this._isUplink?void 0:this.userId,sdkAppId:this._room.sdkAppId,isLocal:this._isUplink}),this._signalChannel=e.signalChannel,this._enableSEI=e.enableSEI,this._enableSEI&&Ze&&(this._sei=new fr(this,this._log,this._isUplink))}close(e){this._log.info("close connection"),this.emit("closed",e),this._sei&&(this._sei.destroy(),this._sei=null)}emitConnectionStateChangedEvent(e){return e===this._currentState?!1:(_.emit(m.PEER_CONNECTION_STATE_CHANGED,{room:this._room,prevState:this._currentState,state:e,remoteUserId:this._isUplink?void 0:this.userId}),this.emit("connection-state-changed",{prevState:this._currentState,state:e}),this._currentState=e,!0)}getPeerConnection(){return this._peerConnection}getRoom(){return this._room}getUserId(){return this.userId}getTinyId(){return this.tinyId}getCurrentState(){return this._currentState}get isH264(){return!!this._peerConnection?.remoteDescription?.sdp.includes("H264")}};var Ed=class extends Mi{localMainAudioTrack=null;localMainVideoTrack=null;localAuxAudioTrack=null;localAuxVideoTrack=null;_isPublishingAux=!1;_publishingLocalAudioTrack;_publishingLocalVideoTrack;_mediaSettings={videoCodec:"",videoWidth:0,videoHeight:0,videoBps:0,videoFps:0,audioCodec:"opus",audioFs:0,audioChannel:0,audioBps:0,smallVideoWidth:0,smallVideoHeight:0,smallVideoFps:0,smallVideoBps:0,auxVideoWidth:0,auxVideoHeight:0,auxVideoFps:0,auxVideoBps:0};_audioManager;constructor(e){super({...e,isUplink:!0}),this._audioManager=e.audioManager,this.initialize()}get ssrc(){if(!this.singlePC)return{audio:0,video:0,videoRtx:0,small:0,smallRtx:0,auxiliary:0,auxiliaryRtx:0};let{audioSsrc:e,bigVideoSsrc:t,bigVideoRtxSsrc:i,smallVideoSsrc:s,smallVideoRtxSsrc:o,auxVideoSsrc:n,auxVideoRtxSsrc:a}=this.singlePC.uplinkSSRC;return{audio:e||0,video:t||0,videoRtx:i||0,small:s||0,smallRtx:o||0,auxiliary:n||0,auxiliaryRtx:a||0}}get isMainStreamPublished(){return!!(this.localMainAudioTrack||this.localMainVideoTrack)}get isAuxStreamPublished(){return!!(this.localAuxVideoTrack||this.localAuxAudioTrack)}get publishState(){let e={audio:!1,bigVideo:!1,smallVideo:!1,auxVideo:!1};if(this._peerConnection){let t=this._peerConnection.getSenders();t&&(pt()?(e.audio=!!t[0]?.track,e.bigVideo=!!t[1]?.track,e.smallVideo=!!t[2]?.track,e.auxVideo=!!t[3]?.track):t.forEach(i=>{i.track&&(i.track.kind===l.AUDIO?e.audio=!0:(e.bigVideo=!0,this._room.videoManager.hasSmall&&(e.smallVideo=!0)))}))}return e}get muteState(){return{audio:!!this.localMainAudioTrack?.muted,bigVideo:!!this.localMainVideoTrack?.muted,auxVideo:!!this.localAuxVideoTrack?.muted}}initialize(){this.installEvents()}reset(){this.uninstallEvents()}close(e){super.close(e),this.reset(),this.emitConnectionStateChangedEvent("DISCONNECTED")}installEvents(){this.listeners("connection-state-changed").includes(this.handleConnectionStateChange)||this.on("connection-state-changed",this.handleConnectionStateChange,this),this.listeners("spc-reconnected").includes(this.onSinglePCReconnected)||this.singlePC?.on("spc-reconnected",this.onSinglePCReconnected,this)}uninstallEvents(){this.off("connection-state-changed",this.handleConnectionStateChange,this),this.singlePC?.off("spc-reconnected",this.onSinglePCReconnected,this)}emitConnectionStateChangedEvent(e,t){let i=this._currentState,s=super.emitConnectionStateChangedEvent(e);return s&&i!==e&&(t?t.emit("connection-state-changed",{prevState:i,state:e}):(this.localMainVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}),this.localAuxVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}),this._publishingLocalVideoTrack?.emit("connection-state-changed",{prevState:i,state:e}))),s}async publish({localAudioTrack:e,localVideoTrack:t,isAuxiliary:i}){if(!this.singlePC)return;if(await this.singlePC.waitForPeerConnectionConnected(),e&&(this._publishingLocalAudioTrack=e),t){if(!this.singlePC.isH264EncodeSupported&&!this.singlePC.isVP8EncodeSupported)throw new A({code:E.NOT_SUPPORTED_H264,message:v({key:b.NOT_SUPPORTED_H264ENCODE})});ge&&at()===115&&t.profile.width*t.profile.height<=640*360&&(this._log.warn("fallback video to 480p"),t.setProfile(Ve["480p_2"]),await t.applyProfile()),this._publishingLocalVideoTrack=t}this._isPublishingAux=i;let s;t&&!i&&t.small&&(s=this._room.videoManager.smallTrack),Be()&&await this.publishByTransceiver({localAudioTrack:e,localVideoTrack:t,smallTrack:s,isAuxiliary:i}),this.singlePC.seiCodec?.handleEncodedStreams(),this._publishingLocalAudioTrack=null,this._publishingLocalVideoTrack=null,this._isPublishingAux=!1,i?(t&&(this.localAuxVideoTrack=t),e&&(this.localAuxAudioTrack=e)):(t&&(this.localMainVideoTrack=t),e&&(this.localMainAudioTrack=e)),await this.singlePC.setBandwidth({audio:this.localMainAudioTrack?.profile.bitrate||this.localAuxAudioTrack?.profile.bitrate,bigVideo:this.localMainVideoTrack?.profile.bitrate,smallVideo:this.localMainVideoTrack?.small?.bitrate,auxVideo:this.localAuxVideoTrack?.profile.bitrate}),await this._signalChannel.sendWaitForResponse({command:H.SPC_PUBLISH,responseCommand:V.SPC_PUBLISH_RESULT,data:{...this.singlePC.uplinkSSRC,state:this.publishState,muteState:this.muteState}}),this.sendMediaSettings(),this.installTrackMuteEvents(e,t)}publishByTransceiver({localAudioTrack:e,localVideoTrack:t,smallTrack:i,isAuxiliary:s}){this._log.info("publish by transceiver");let o=t?.outMediaTrack,n=this._audioManager.mixedAudioTrack,a=this._peerConnection.getTransceivers(),c=[],d=[];if(n){let h=a[0].sender.replaceTrack(n);d.push(0),c.push(h)}if(o)if(s){let h=a[3].sender.replaceTrack(o);d.push(3),c.push(h)}else{let h=a[1].sender.replaceTrack(o);d.push(1),c.push(h)}if(i){let h=a[2].sender.replaceTrack(i);d.push(2),c.push(h)}let u=this.setTransceiverDirection($.SENDONLY,d);return c.push(u),Promise.all(c)}async enableSmall(e){if(!this.singlePC)return;let t=this._peerConnection.getTransceivers();e?this._room.videoManager.smallTrack&&(await t[2].sender.replaceTrack(this._room.videoManager.smallTrack),await this.setTransceiverDirection($.SENDONLY,[2])):(await t[2].sender.replaceTrack(null),await this.setTransceiverDirection($.INACTIVE,[2])),this.updateMediaSettings(),await this.doPublishChange()}installTrackMuteEvents(...e){e.forEach(t=>{t&&(t?.on("mute",this.sendMutedFlag,this),t?.on("unmute",this.sendMutedFlag,this))})}uninstallTrackMuteEvents(...e){e.forEach(t=>{t&&(t?.off("mute",this.sendMutedFlag,this),t?.off("unmute",this.sendMutedFlag,this))})}async unpublish({localAudioTrack:e,localVideoTrack:t}){let i=t&&t===this.localAuxVideoTrack,s=t?.outMediaTrack,o=this._peerConnection.getSenders(),n=[];e&&(this._audioManager.mixedAudioTrack||(await o[0].replaceTrack(null),n.push(0)),i?this.localAuxAudioTrack=null:this.localMainAudioTrack=null),s&&(i?(await o[3].replaceTrack(null),this.localAuxVideoTrack=null,this._mediaSettings={...this._mediaSettings,auxVideoBps:0,auxVideoFps:0,auxVideoWidth:0,auxVideoHeight:0},n.push(3)):(await o[1].replaceTrack(null),await o[2].replaceTrack(null),this.localMainVideoTrack=null,this._mediaSettings={...this._mediaSettings,videoWidth:0,videoHeight:0,videoBps:0,videoFps:0,audioFs:0,audioChannel:0,audioBps:0,smallVideoWidth:0,smallVideoHeight:0,smallVideoFps:0,smallVideoBps:0},n.push(1,2))),this.isMainStreamPublished||this.isAuxStreamPublished?(await this.setTransceiverDirection($.INACTIVE,n),await this.doPublishChange(!1)):await this.doUnpublish(),this.uninstallTrackMuteEvents(e,t),t?.emit("connection-state-changed",{prevState:this._currentState,state:"DISCONNECTED"})}async doPublishChange(e=!0){let t={state:this.publishState,constraintConfig:this._mediaSettings},i=await this._signalChannel.sendWaitForResponse({command:H.PUBLISH_STATE_CHANGE,data:t,responseCommand:V.PUBLISH_STATE_CHANGE_RESULT,enableLog:e});this.checkPublishResultCode(i.data.code,i.data.message)}doUnpublish(e=!1){return this._signalChannel.sendWaitForResponse({command:H.UNPUBLISH,commandDesc:"unpublish",responseCommand:V.UNPUBLISH_RESULT,enableLog:e}).catch(t=>{if(t.getCode()===E.API_CALL_TIMEOUT)return Promise.resolve();throw t})}updateMediaSettings(){let{detail:{isH264EncodeSupported:e,isVp8EncodeSupported:t}}=this._room.checkSystemResult;e?this._mediaSettings.videoCodec="H264":t&&(this._mediaSettings.videoCodec="VP8");let i=this._publishingLocalAudioTrack||this.localMainAudioTrack||this.localAuxAudioTrack,{localMainVideoTrack:s,localAuxVideoTrack:o}=this;if(this._publishingLocalVideoTrack&&(this._isPublishingAux?o=this._publishingLocalVideoTrack:s=this._publishingLocalVideoTrack),$e){if(i&&i.outMediaTrack){let n=i.outMediaTrack.getSettings();this._mediaSettings.audioChannel=n.channelCount||1,this._mediaSettings.audioBps=i.profile.bitrate*1e3,this._mediaSettings.audioFs=n.sampleRate||0}if(s&&s.outMediaTrack){let n=s.outMediaTrack.getSettings();this._mediaSettings.videoWidth=n.width||0,this._mediaSettings.videoHeight=n.height||0,this._mediaSettings.videoFps=n.frameRate||0,this._mediaSettings.videoBps=s.profile.bitrate*1e3,s.small&&(this._mediaSettings.smallVideoWidth=s.small.width,this._mediaSettings.smallVideoHeight=s.small.height,this._mediaSettings.smallVideoFps=s.small.frameRate,this._mediaSettings.smallVideoBps=s.small.bitrate*1e3)}if(o&&o.outMediaTrack){let n=o.outMediaTrack.getSettings();this._mediaSettings.auxVideoWidth=n.width||0,this._mediaSettings.auxVideoHeight=n.height||0,this._mediaSettings.auxVideoFps=n.frameRate||0,this._mediaSettings.auxVideoBps=o.profile.bitrate*1e3}}else i&&i.outMediaTrack&&(this._mediaSettings.audioChannel=i.profile.channelCount,this._mediaSettings.audioBps=i.profile.bitrate*1e3,this._mediaSettings.audioFs=i.profile.sampleRate),s&&s.outMediaTrack&&(this._mediaSettings.videoWidth=s.profile.width,this._mediaSettings.videoHeight=s.profile.height,this._mediaSettings.videoFps=s.profile.frameRate,this._mediaSettings.videoBps=s.profile.bitrate*1e3);this._log.info(`updateMediaSettings: ${JSON.stringify(this._mediaSettings)}`)}sendMediaSettings(){this.updateMediaSettings(),this._signalChannel.sendWaitForResponse({command:H.UPDATE_CONSTRAINT_CONFIG,data:this._mediaSettings,responseCommand:V.UPDATE_CONSTRAINT_CONFIG_RES}).then(e=>{e.data.code!==0&&this._log.warn(e.data.message)}).catch(()=>{})}async addTrack(e){if(!this._peerConnection)return;let t=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is adding ${e.kind} track to current published local ${t?l.AUXILIARY:l.MAIN} stream`),this._sei?.handleEncodedStreams(),pt()&&await this.addTrackByTransceiver(e,t)}async addTrackByTransceiver(e,t){if(!e.mediaTrack)return;let i=this._peerConnection.getTransceivers();if(e.kind===l.AUDIO)await i[0].sender.replaceTrack(this._audioManager.mixedAudioTrack);else{let s=t?3:1;await i[s].sender.replaceTrack(e.outMediaTrack),s===1&&this.localMainVideoTrack?.small&&await i[2].sender.replaceTrack(this._room.videoManager.smallTrack),i[s].direction===$.INACTIVE&&await this.setTransceiverDirection($.SENDONLY,[s])}this.updateMediaSettings(),await this.doPublishChange()}async removeTrack(e){if(!this._peerConnection)return;let t=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is removing ${e.kind} track from current published local ${t?l.AUXILIARY:l.MAIN} stream`),pt()&&await this.removeTrackByTransceiver(e,t)}async removeTrackByTransceiver(e,t){if(!e.mediaTrack)return;let i=this._peerConnection.getTransceivers();if(e.kind===l.AUDIO)this._audioManager.mixedAudioTrack||await i[0].sender.replaceTrack(null);else{let s=t?3:1;await i[s].sender.replaceTrack(null),s===1&&this._room.videoManager.hasSmall&&await i[2].sender.replaceTrack(null),await this.setTransceiverDirection($.INACTIVE,[s])}this.updateMediaSettings(),await this.doPublishChange()}async setTransceiverDirection(e,t){if(!z)return;let i=!1,s=!1;this._log.info(`setting transceiver ${t.join(",")} direction to ${e}`);let o=this._peerConnection.getTransceivers();if(t.forEach(c=>{o[c].direction!==e&&(o[c].direction=e,i=!0)}),i){this._log.info("updating offer");let c=await this._peerConnection.createOffer();await this._peerConnection.setLocalDescription(c)}let n=-1,a=this._peerConnection.remoteDescription.sdp.split(`\r
`).map(c=>{if(c.match(new RegExp(`a=(${$.INACTIVE}|${$.RECVONLY}|${$.SENDONLY})`))&&n++,t.includes(n)){if(e===$.INACTIVE&&c.includes(`a=${$.RECVONLY}`))return s=!0,`a=${e}`;if(e===$.SENDONLY&&c.includes(`a=${$.INACTIVE}`))return s=!0,`a=${$.RECVONLY}`}return c}).join(`\r
`);s&&(this._log.info("updating answer"),await this._peerConnection.setRemoteDescription({type:"answer",sdp:a}))}async replaceTrack(e){let t=this._peerConnection?.getSenders(),i=e.mediaTrack;if(!t||t.length===0||!i)return;let s=e===this.localAuxAudioTrack||e===this.localAuxVideoTrack;this._log.info(`is replacing ${i.kind} track on ${s?l.AUXILIARY:l.MAIN} stream`),i.kind===l.AUDIO&&t[0]&&await t[0].replaceTrack(this._audioManager.mixedAudioTrack),i.kind===l.VIDEO&&(!s&&t[1]&&await t[1].replaceTrack(e.outMediaTrack||i),s&&t[3]&&await t[3].replaceTrack(e.outMediaTrack))}async setBandwidth({bandwidth:e,type:t,videoType:i}){if(this.singlePC){let s={};t===l.AUDIO?s.audio=e:i==="big"?s.bigVideo=e:i==="small"?s.smallVideo=e:s.auxVideo=e,await this.singlePC.setBandwidth(s)}}sendMutedFlag(e){e===this.localAuxAudioTrack||e===this.localAuxVideoTrack||(this._log.info(`send muted state: ${JSON.stringify(this.muteState)}`),this._signalChannel.send(H.UPDATE_MUTE_STAT,this.muteState))}handleConnectionStateChange(e){e.state==="CONNECTED"&&(this.localMainVideoTrack||this._publishingLocalVideoTrack&&!this._isPublishingAux)&&_.emit(m.SEND_FIRST_VIDEO_FRAME,{room:this._room})}getVideoTrackId(e=l.VIDEO){if(this._peerConnection){let t=this._peerConnection.getSenders();if(e===l.AUXILIARY&&t[3]&&t[3].track)return t[3].track.id;if(e===l.VIDEO&&t[1]&&t[1].track)return t[1].track.id}if(this.localMainVideoTrack&&e===l.VIDEO){let t=this.localMainVideoTrack.mediaTrack;if(t)return t.id}if(this.localAuxVideoTrack&&e===l.AUXILIARY){let t=this.localAuxVideoTrack.mediaTrack;if(t)return t.id}return""}getSSRC(){return this.ssrc}checkPublishResultCode(e,t){if(e!==0)throw e===hi?(this._log.error(ue.NOT_SUPPORTED_H264ENCODE),new A({code:E.NOT_SUPPORTED_H264,message:v({key:b.NOT_SUPPORTED_H264ENCODE})})):new A({code:E.UNKNOWN,message:v({key:b.SIGNAL_RESPONSE_FAILED,data:{signalResponse:V.PUBLISH_RESULT,code:e,message:t}})})}sendSEI(e,t){this._sei?.push(e,t)}async onSinglePCReconnected(){this.isMainStreamPublished&&await this.publish({localAudioTrack:this.localMainAudioTrack,localVideoTrack:this.localMainVideoTrack,isAuxiliary:!1}),this.isAuxStreamPublished&&await this.publish({localAudioTrack:this.localAuxAudioTrack,localVideoTrack:this.localAuxVideoTrack,isAuxiliary:!0})}},gd=Ed;function zl(r){return Object.keys(r).filter(e=>r[e])}var Cn=class extends Mi{_flag=0;role="anchor";remoteAudioTrack;remoteVideoTrack;remoteAuxiliaryTrack;ssrc={audio:0,video:0,videoRtx:0,auxiliary:0,auxiliaryRtx:0};constructor(e){super({...e,isUplink:!1}),this.flag=e.flag,this.remoteAudioTrack=new Mt(this._room,this),this.remoteVideoTrack=new Et(this._room,this),this.remoteAuxiliaryTrack=new dr(this._room,this),this.initialize()}get subscribeState(){let e={audio:!1,video:!1,auxiliary:!1,smallVideo:!1};return this.remoteVideoTrack.isSubscribed&&(this.remoteVideoTrack.mediaType&8?e.smallVideo=!0:e.video=!0),this.remoteAudioTrack.isSubscribed&&(e.audio=!0),this.remoteAuxiliaryTrack.isSubscribed&&(e.auxiliary=!0),e}get muteState(){return Kt(this.flag,this.userId)}get flag(){return this._flag}set flag(e){e!==this._flag&&(this._flag=e,this.remoteAudioTrack?.onFlagChanged(),this.remoteVideoTrack?.onFlagChanged(),this.remoteAuxiliaryTrack?.onFlagChanged())}get hasMainStream(){return this.muteState.hasAudio||this.muteState.hasVideo||this.muteState.hasSmall}get hasAuxStream(){return this.muteState.hasAuxiliary}get isMainStreamSubscribed(){return(this.subscribeState.audio||this.subscribeState.video||this.subscribeState.smallVideo)&&(this.muteState.hasAudio||this.muteState.hasVideo||this.muteState.hasSmall)}get isAuxStreamSubscribed(){return this.subscribeState.auxiliary&&this.muteState.hasAuxiliary}get isSmallStreamSubscribed(){return this.subscribeState.smallVideo&&this.muteState.hasSmall}get isBigStreamSubscribed(){return this.subscribeState.video&&this.muteState.hasVideo}isStreamUnpublished(e){return e===l.MAIN?!this.muteState.hasAudio&&!this.muteState.hasVideo:!this.muteState.hasAuxiliary}initialize(){this.installEvents()}close(e){super.close(e),this.emitConnectionStateChangedEvent("DISCONNECTED"),this.remoteAudioTrack.close(),this.remoteVideoTrack.close(),this.remoteAuxiliaryTrack.close(),this.uninstallEvents(),this.removeDownlink()}installEvents(){!this.singlePC||(this.listeners("track").includes(this.onTrack)||this.singlePC.on("track",this.onTrack,this),this.listeners("spc-reconnected").includes(this.onSinglePCReconnected)||this.singlePC.on("spc-reconnected",this.onSinglePCReconnected,this))}uninstallEvents(){!this.singlePC||(this.singlePC.off("track",this.onTrack,this),this.singlePC.off("spc-reconnected",this.onSinglePCReconnected,this))}emitConnectionStateChangedEvent(e){let t=this._currentState,i=super.emitConnectionStateChangedEvent(e);return i&&t!==e&&(this.remoteVideoTrack?.emit("connection-state-changed",{prevState:t,state:e}),this.remoteAuxiliaryTrack?.emit("connection-state-changed",{prevState:t,state:e})),i}onTrack(e){let t=e.streams[0],{track:i}=e;if(t.id.split("-")[0]!==this.tinyId)return;let n=t.id.includes("aux")?"auxiliary":"main";this._log.debug(`ontrack ${n} ${i.kind}`);let a=l.AUDIO;i.kind===l.VIDEO&&(a=n===l.MAIN?l.VIDEO:l.AUXILIARY);let c=this.remoteAudioTrack;a===l.VIDEO?c=this.remoteVideoTrack:a===l.AUXILIARY&&(c=this.remoteAuxiliaryTrack),c.setInputMediaStreamTrack(i)}isSubscriptionStateNotChanged(e){return JSON.stringify(e)===JSON.stringify(this.subscribeState)}async subscribe(e,t){try{if(this.isSubscriptionStateNotChanged(e))return;if(this._log.info(`subscribe ${t} ${zl(e)}`),this.hasSSRC){let i="subscribe_change";Object.values(e).find(s=>s===!0)||(i="unsubscribe"),await this.sendSubscription(i,e)}else await this.doSubscribe(e)}catch(i){throw this._room.isJoined&&this.isStreamUnpublished(t)?(this._log.warn(`${i.message} ${JSON.stringify(this.muteState)}`),new A({code:E.REMOTE_STREAM_NOT_EXIST,message:`remote user ${this.userId} unpublished stream`})):i}}async unsubscribe({remoteTracks:e,streamType:t}){if(t==="main"&&!this.isMainStreamSubscribed||t==="auxiliary"&&!this.isAuxStreamSubscribed){this._log.info(`${t} stream already unsubscribed`);return}let i={...this.subscribeState};e.forEach(o=>{switch(o.mediaType){case 1:i.audio=!1;break;case 4:i.video=!1;break;case 8:i.smallVideo=!1;break;case 2:i.auxiliary=!1;break;default:break}});let s="subscribe_change";Object.values(i).find(o=>o===!0)||(s="unsubscribe"),this._log.info(`${s==="unsubscribe"?s:"subscribe"} ${t} [${zl(i)}]`),s==="unsubscribe"&&this.singlePC?.removeDownlinkQueue.add(this.tinyId),await this.sendSubscription(s,i),s==="unsubscribe"&&await this.removeDownlink()}sendSubscription(e,t=this.subscribeState){let i={srcTinyId:this.tinyId,srcUserId:this.userId},s=H.UNSUBSCRIBE,o=V.UNSUBSCRIBE_RESULT;return e==="subscribe_change"&&(i={audio:t.audio,bigVideo:t.video,auxVideo:t.auxiliary,smallVideo:t.smallVideo,srcTinyId:this.tinyId},s=H.SUBSCRIBE_CHANGE,o=V.SUBSCRIBE_CHANGE_RESULT),this._signalChannel.sendWaitForResponse({command:s,data:i,responseCommand:o,timeout:1e4}).then(({data:n})=>{if(n.code!==0){let a=new A({code:n.code,message:v({key:b.ERROR_MESSAGE,data:{type:e,message:n.message}})});throw this._log.error(a),a}})}getMainStreamVideoTrackId(){return this.remoteVideoTrack&&this.remoteVideoTrack.mediaTrack?this.remoteVideoTrack.mediaTrack.id:""}getAuxStreamVideoTrackId(){return this.remoteAuxiliaryTrack&&this.remoteAuxiliaryTrack.mediaTrack?this.remoteAuxiliaryTrack.mediaTrack.id:""}setDelay({audioDelay:e,videoDelay:t}){this.remoteAudioTrack.stat.end2EndDelay=e,this.remoteVideoTrack.stat.end2EndDelay=t}onSinglePCReconnected(){(this.ssrc.audio||this.ssrc.video||this.ssrc.auxiliary)&&this.doSubscribe(this.subscribeState,!1)}get hasSSRC(){return this.ssrc.audio&&this.ssrc.video&&this.ssrc.auxiliary}async doSubscribe(e=this.subscribeState,t=!0){if(!!this.singlePC){if(this.singlePC.addDownlinkQueue.add(this.tinyId),await this.singlePC.waitForPeerConnectionConnected(),t||!this.hasSSRC){let i={audioSsrc:Pi(),bigVideoSsrc:Pi(),bigVideoRtxSsrc:Pi(),auxVideoSsrc:Pi(),auxVideoRtxSsrc:Pi()},{audioSsrc:s,bigVideoSsrc:o,bigVideoRtxSsrc:n,auxVideoSsrc:a,auxVideoRtxSsrc:c}=i;this.ssrc={audio:s,video:o,videoRtx:n,auxiliary:a,auxiliaryRtx:c},this.singlePC.addDownlinkQueue.delete(this.tinyId),await this.singlePC.addDownlink({userId:this.userId,tinyId:this.tinyId,ssrc:this.ssrc});try{let d=await this._signalChannel.sendWaitForResponseWithRetry({command:H.SPC_SUBSCRIBE,responseCommand:V.SPC_SUBSCRIBE_RESULT,data:{srcUserId:this.userId,srcTinyId:this.tinyId,audio:e.audio,bigVideo:e.video,auxVideo:e.auxiliary,smallVideo:e.smallVideo,customData:!1,ssrc:i},retries:3,retryTimeout:0});if(d.data.code!==0)throw new A({code:d.data.code,message:d.data.message})}catch(d){throw await this.removeDownlink(),d}return}this.singlePC.addDownlinkQueue.delete(this.tinyId),await this.singlePC.addDownlink({userId:this.userId,tinyId:this.tinyId,ssrc:this.ssrc})}}async removeDownlink(){!this.singlePC||(this.ssrc={audio:0,video:0,videoRtx:0,auxiliary:0,auxiliaryRtx:0},this.singlePC.removeDownlinkQueue.delete(this.tinyId),await this.singlePC.removeDownlink(this.tinyId,this.userId))}};y([F(r=>function(...e){return new Promise((t,i)=>{let s=o=>{this.off("closed",s),i(new A({code:E.API_CALL_ABORTED,message:v({key:b.CONNECTION_ABORTED,data:o})}))};this.on("closed",s),r.apply(this,e).then(t,i).finally(()=>{this.off("closed",s)})})})],Cn.prototype,"subscribe",1);var Xl=Cn;function ql(){return F(r=>async function(...e){if(this.scene==="live"&&this.role!=="anchor"||(e=e.filter(i=>i.outMediaTrack&&i.state==="capture"),!e.length))return;_.emit("61",{room:this}),e.forEach(i=>{i.kind==="audio"&&this.audioManager.addAudioTrack(i)});let t=r.apply(this,e);return e.forEach(i=>{i.publish(this,t),i.kind==="audio"&&this.audioManager.localMixAudioTrack?.publish(this,t)}),t})}function Ql(){return F(r=>function(...e){e.forEach(i=>{i.kind==="audio"&&this.audioManager.removeAudioTrack(i)});let t=r.apply(this,e);return e.forEach(i=>i.unpublish()),t})}var{isString:Zl,isPlainObject:WP,isUndefined:Ir,getNetworkType:Ym,getTerminalType:zm,isEmpty:Ps}=be,At=class extends Rn{constructor(t){super(t);this.privateMapKey="";this._heartbeat=-1;this._lastHeartBeatTime=-1;this._joinTimeout=-1;this._firstPublishedList=null;this._joinReject=null;this._isRelayChanged=!1;this._signalChannel=null;this.uplinkConnection=null;this.singlePC=null;this.enableSPC=ss;this._changeBigSmallRecords=new Map;this._networkQuality=null;this._networkType=Ym();this._turnServers=[];this._syncUserListInterval=-1;this._smallStreamConfig={bitrate:100,frameRate:15,height:120,width:160};this.enableSEI=!1;this._enableAudioVolumeEvaluation=!1;this._audioVolumeIntervalId=0;this._enableMultiAuxStream=!1;this._pureAudioPushMode=!1;this.enableHWEncoder=!1;this._stats=new Ns(this,this._log),this.userManager=new Yo(this.userId,this._log),this._version=Se,this.sdpSemantics=Xi,Ir(t.sdpSemantics)?Xt.isUnifiedPlanDefault()&&(this.sdpSemantics=Wt):this.sdpSemantics=t.sdpSemantics,this._log.info(`sdpSemantics: ${this.sdpSemantics}, netType: ${this._networkType}`),t.iceTransportPolicy&&(this._iceTransportPolicy=t.iceTransportPolicy),this._enableMultiAuxStream=Ir(t.enableMultiAuxStream)?!1:t.enableMultiAuxStream,this.enableSEI=t.enableSEI,!Ir(t.enableSPC)&&ss&&(this.enableSPC=t.enableSPC),this.enableHWEncoder=t.enableHWEncoder||!1,this._initBusinessInfo(t)}get isMainStreamPublished(){return!!this.uplinkConnection?.isMainStreamPublished}get isMainAudioPublished(){return!!this.uplinkConnection?.localMainAudioTrack}get isAuxStreamPublished(){return!!this.uplinkConnection?.isAuxStreamPublished}get hasAuxStream(){return[...this.remotePublishedUserMap.values()].findIndex(t=>t.muteState.hasAuxiliary)>=0}get userMap(){return this.userManager.userMap}get remotePublishedUserMap(){return this.userManager.remotePublishedUserMap}get tinyIdToUserIdMap(){return new Map([...this.remotePublishedUserMap.values()].map(t=>[t.tinyId,t.userId]))}async join(t,i,s){return this.userManager.mySelfId=this.userId,this.userManager.on("1",o=>{this.emit("peer-join",o)}),this.userManager.on("2",o=>{this.closeDownLinkConnection(o,"remote user exitRoom"),this.emit("peer-leave",o)}),this.userManager.on("3",this.createDownlinkConnection,this),this.userManager.on("5",this.closeDownLinkConnection,this),this.userManager.on("6",({...o})=>{_.emit(m.REMOTE_PUBLISH_STATE_CHANGED,{room:this,...o}),this.emit("remote-publish-state-changed",{...o})}),this._joinOptions=t,new Promise(async(o,n)=>{this._joinReject=n;try{this.checkDestroy();let a=await this.initialize();await this.doJoin(t,a),o(),this._firstPublishedList&&this.onPublishedUserList({data:{userList:this._firstPublishedList}})}catch(a){n(a)}this._joinReject=null})}doJoin(t,i=!1){return new Promise(async(s,o)=>{t.privateMapKey&&(this.privateMapKey=t.privateMapKey),this._signalChannel.once(se.SETUP_FAILED,d=>{this.clearJoinTimeout(),_.emit(m.JOIN_SIGNAL_CONNECTION_END,{room:this,error:d}),o(d)}),Z(this.scheduleResult?.config?.singlePC)&&ss&&(this.enableSPC=this.scheduleResult.config.singlePC);let n;if(this.enableSPC&&!this.singlePC){this.singlePC=new Sr({signalChannel:this._signalChannel,room:this,enableCustomMessage:!1}),this.singlePC.on("sei-message",d=>this.emit("sei-message",d)),this.singlePC.once("error",()=>this.fallbackToMPC());try{n=await this.singlePC.initialize()}catch{this.fallbackToMPC()}}this.keyPointManager.setConnectionType(this.singlePC?1:2);let a={roomId:String(t.roomId||t.strRoomId),useStringRoomId:this.useStringRoomId,privateMapKey:this.privateMapKey,trtcRole:t.role,trtcScene:this.scene==="live"?2:1,sdpSemantics:this.sdpSemantics,version:this._version,ua:navigator&&navigator.userAgent||"",terminalType:zm(),netType:Gt[this._networkType],bussinessInfo:this._businessInfo,ability:n,sdkType:this._sdkType};i&&(a.userSig=this.userSig),this._log.debug(`join room signal data: ${JSON.stringify(a)}`);let c=5e3;this.scheduleResult.config?.enterRoomTimeout&&this.scheduleResult.config.enterRoomTimeout>=1&&(c=this.scheduleResult.config.enterRoomTimeout*1e3),this._joinTimeout=window.setTimeout(()=>{o(new A({code:E.JOIN_ROOM_FAILED,message:v({key:b.JOIN_ROOM_TIMEOUT})}))},c),_.emit(m.JOIN_SEND_CMD,{room:this}),this._signalChannel.send(this.singlePC?H.SPC_JOIN_ROOM:H.JOIN_ROOM,a),this._signalChannel.once(V.JOIN_ROOM_RESULT,d=>{this.clearJoinTimeout();let{code:u,message:h,data:p}=d.data;_.emit(m.JOIN_RECEIVED_CMD_RES,{room:this,code:u}),u===0?(this._log.info("Join room success, start heartbeat"),this.startHeartbeat(),this.syncUserList(),this.startSyncUserListInterval(),this._firstPublishedList=p.publishers,this.singlePC&&this.singlePC.connect(p.ability).catch(()=>{}),s()):(this._log.error(`Join room failed result: ${u} error: ${h}`),o(new A({code:E.JOIN_ROOM_FAILED,extraCode:u,message:v({key:b.JOIN_ROOM_FAILED,data:{error:h,code:u}})})))})})}async reJoin(){if(!this.isJoined){this._log.warn("reJoin abort");return}try{if(this._log.warn(`reJoin pending: ${this._joinOptions.roomId}`),this.singlePC&&(this.singlePC.close(),this.singlePC=null),this._signalChannel.close(),await this._signalChannel.connect(),await this.doJoin({...this._joinOptions,role:this.role==="anchor"?20:21,privateMapKey:this.privateMapKey}),this._log.warn("reJoin success"),Y.logSuccessEvent({userId:this.userId,eventType:ye.REJOIN}),this.singlePC){let t=i=>{i.state==="CONNECTED"&&(this.singlePC?.off(Rt.CONNECTION_STATE_CHANGED,t),this.uplinkConnection instanceof gd&&this.uplinkConnection.onSinglePCReconnected(),this.remotePublishedUserMap.forEach(s=>{s.installEvents(),s.doSubscribe()}))};this.singlePC.on(Rt.CONNECTION_STATE_CHANGED,t),this.checkConnectionsToReconnect(),this.uplinkConnection instanceof ln&&!this.uplinkConnection.getIsReconnecting()&&this.uplinkConnection.startReconnection()}}catch(t){this._log.warn(`reJoin fail ${t}`),this.reset(),Y.logFailedEvent({userId:this.userId,eventType:ye.REJOIN,error:t}),this.emit("error",new A({code:E.JOIN_ROOM_FAILED,message:v({key:b.REJOIN_ROOM_FAILED,data:{roomId:this._joinOptions.roomId}})}))}}async initialize(){let{mainUrl:t,backupUrl:i}=this.getSignalChannelUrl(),s=this._signalChannel||gl(this.userId),o=!!(s&&s.isConnected&&s.keepAlive);return this._log.info(`setup signal channel. reuse: ${+o}`),o?(s.url=t,s.backupUrl=i,s.room=this,this._signalChannel=s):this._signalChannel=new Rs({sdkAppId:this.sdkAppId,userId:this.userId,userSig:this.userSig,url:t,backupUrl:i,room:this}),this._networkQuality||(this._networkQuality=new Di({signalChannel:this._signalChannel,room:this}),this._networkQuality.on(Di.EVENT_NETWORK_QUALITY,n=>{this.emit("network-quality",n)})),_e(this,this._signalChannel).add(se.CONNECTION_STATE_CHANGED,n=>{_.emit(m.SIGNAL_CONNECTION_STATE_CHANGED,{room:this,...n}),this.emit("signal-connection-state-changed",n)}).add(se.RECONNECT_FAILED,n=>{this.reset(),this.emit("error",n)}).add(V.PEER_JOIN,n=>{let{srcTinyId:a,userId:c,role:d}=n.data.data;this.userManager.addUser({userId:c,tinyId:a,role:d})}).add(V.PEER_LEAVE,n=>{let{userId:a,reason:c=0}=n.data.data;this.userManager.deleteUser(a,c)}).add(V.UPDATE_REMOTE_MUTE_STAT,n=>{this._lastHeartBeatTime>0&&Date.now()-this._lastHeartBeatTime>=10*1e3&&this.doHeartbeat(),this.onPublishedUserList(n.data)}).add(V.CLIENT_BANNED,n=>{let a=n.data.data,{reason:c}=a;if(Y.uploadEvent({log:`stat-banned:${c}`,userId:this.userId}),c==="user_time_out"){this._log.warn(`${c} last heart beat time: ${this._lastHeartBeatTime} interval: ${Date.now()-this._lastHeartBeatTime}, visibility: ${document.visibilityState}`),this.reJoin();return}this._log[c==="kick"?"error":"info"](`user was banned because of [${c}]`),this.reset(),this.emit("banned",{reason:c})}),this._signalChannel.once(se.SETUP_SUCCESS,n=>{this.tinyId=n.signalInfo.tinyId,_.emit(m.JOIN_SIGNAL_CONNECTION_END,{room:this})}),_.emit(m.JOIN_SIGNAL_CONNECTION_START,{room:this}),await this._signalChannel.connect(this._isUsingCachedSchedule?10*1e3:void 0),o}setSignalChannel(t){this._signalChannel=t,t||ie(this)}async leave(){try{await this.doHeartbeat()}catch{}this._log.info("leave() => leaving room"),_.emit(m.LEAVE_SEND_CMD,{room:this}),this._signalChannel?.send(H.LEAVE_ROOM)}clearNetworkQuality(){this._networkQuality&&(this._networkQuality.stop(),this._networkQuality=null)}closeConnections(){this.remotePublishedUserMap.forEach(t=>{this.closeDownLinkConnection(t.userId,"you exitRoom")})}clearJoinTimeout(){clearTimeout(this._joinTimeout),this._joinTimeout=-1}startHeartbeat(){this._heartbeat===-1&&(this._heartbeat=J.run(dt,this.doHeartbeat.bind(this),{delay:2e3}))}stopHeartbeat(){this._heartbeat!==-1&&(this._log.info("stopHeartbeat"),J.clearTask(this._heartbeat),this._heartbeat=-1,this._lastHeartBeatTime=-1)}async doHeartbeat(){let t=this.badCaseDetector.getMonitorFreeze(),i=await this._stats.getStatsReport({uplinkConnection:this.uplinkConnection,downlinkConnections:this.remotePublishedUserMap,freezeMap:t});if(this.badCaseDetector.resetMonitor(),!this._signalChannel?.isConnected)return;let s=this._signalChannel.isConnected?Yu(this.userId):[],o={str_sdk_version:di,uint64_datetime:new Date().getTime(),msg_user_info:{str_identifier:this.userId,uint64_tinyid:this.tinyId},msg_device_info:{uint32_terminal_type:15,str_device_name:navigator.platform,str_os_version:"",uint32_net_type:Gt[this._networkType]},msg_event_msg:s,str_acc_ip:this.getSignalInfo().relayIp,str_client_ip:this.getSignalInfo().clientIp,...i};_.emit(m.HEARTBEAT_REPORT,{room:this,report:o}),this._signalChannel.send(H.ON_QUALITY_REPORT,o),this.emit("heartbeat-report",{...o,bytes_sent:this._stats.totalBytesSent+this._signalChannel.bytesSent,bytes_received:this._stats.totalBytesReceived+this._signalChannel.bytesReceived});let n=Date.now();this._lastHeartBeatTime>0&&n-this._lastHeartBeatTime>1e4&&this._log.warn(`heartbeat took ${n-this._lastHeartBeatTime}`),this._signalChannel.isConnected&&(this._lastHeartBeatTime=n),!this._isRelayChanged&&this.isRelayMaybeFailed()&&(this.reJoin(),this._isRelayChanged=!0)}onPublishedUserList(t){if(!this.isJoined)return;let i=t.data.userList.map(({userId:s,srcTinyId:o,flag:n})=>{let a=this.remotePublishedUserMap.get(s);return a&&this.checkSubscribeBigSmallVideo(a),{userId:s,tinyId:o,flag:n}});_.emit(m.RECEIVED_PUBLISHED_USER_LIST,{room:this,publishedUserList:i}),this.userManager.setRemotePublishedUserList(i)}closeUplink(t="you unpublished"){this.uplinkConnection&&(this.localTracks.size>0&&this.uplinkConnection.doUnpublish(),this.uplinkConnection.close(t),this.uplinkConnection=null),this.localTracks.forEach(i=>i.unpublish()),this.localTracks.clear()}createDownlinkConnection({userId:t,tinyId:i,flag:s}){let o=new(this.singlePC?Xl:sd)({userId:t,tinyId:i,room:this,signalChannel:this._signalChannel,enableSEI:this.enableSEI,flag:s});this.userManager.addRemotePublishedUser(o),this.installDownlinkEvents(o,t),this.emit("remote-published",o)}closeDownLinkConnection(t,i="remote user unpublished"){let s=this.remotePublishedUserMap.get(t);s&&(s.close(i),this.emit("remote-unpublished",s))}installDownlinkEvents(t,i){t.on("error",s=>{let o=s.getCode();o!==E.ICE_TRANSPORT_ERROR&&(o===E.DOWNLINK_RECONNECTION_FAILED&&this.closeDownLinkConnection(i),this.emit("error",s))}),t.on("connection-state-changed",s=>{this.emit("media-connection-state-changed",{...s,userId:t.userId})}),t.on("firewall-restriction",()=>{this.emit("firewall-restriction")})}startSyncUserListInterval(){this._syncUserListInterval===-1&&(this._syncUserListInterval=J.run(dt,this.syncUserList.bind(this)))}stopSyncUserListInterval(){J.clearTask(this._syncUserListInterval),this._syncUserListInterval=-1}syncUserList(){return this.getUserList().then(t=>{this.userManager.setUserList(t)}).catch(t=>{this._log.debug(`sync user list failed: ${t}`)})}getUserList(){return this._signalChannel?.isConnected?this._signalChannel.sendWaitForResponse({command:H.GET_USER_LIST,responseCommand:V.USER_LIST_RES,enableLog:!1,timeout:2e3}).then(({data:t})=>{let{code:i,message:s}=t;if(i===0)return(t.data&&t.data.userList||[]).map(({userId:n,srcTinyId:a,role:c})=>({userId:n,tinyId:a,role:c}));throw v({key:b.SIGNAL_RESPONSE_FAILED,data:{signalResponse:V.USER_LIST_RES,code:i,message:s}})}):Promise.reject("not connected")}getAllConnections(){let t=[...this.remotePublishedUserMap.values()];return this.uplinkConnection&&t.push(this.uplinkConnection),t}isRelayMaybeFailed(){if(this._signalChannel&&!this._signalChannel.isOnline||!Zc)return!1;if(this.singlePC)return this.singlePC.reconnectionCount>6;let t=this.getAllConnections();if(t.length===0)return!1;for(let i=0;i<t.length;i++)if(t[i].getReconnectionCount()<6)return!1;return!0}checkConnectionsToReconnect(){this.singlePC||this.getAllConnections().forEach(i=>{if(i instanceof Le&&!i.getIsReconnecting()){let s=i.getPeerConnection();s&&s.connectionState===Q.CLOSED&&(this._log.warn(`[${i.getUserId()}] pc is closed but not reconnect`),i.startReconnection())}})}async fallbackToMPC(){if(this._log.warn("fallback to multi pc"),Y.uploadEvent({log:"stat-fallback",userId:this.userId}),this.enableSPC=!1,this.singlePC?.close(),this.singlePC=null,await this.reJoin(),this.uplinkConnection){let t=this.uplinkConnection;this.uplinkConnection=new ln({userId:this.userId,tinyId:this.tinyId,room:this,signalChannel:this._signalChannel,enableSEI:this.enableSEI,audioManager:this.audioManager}),t.isMainStreamPublished&&await this.uplinkConnection.publish({localAudioTrack:t.localMainAudioTrack,localVideoTrack:t.localMainVideoTrack,isAuxiliary:!1}),t.isAuxStreamPublished&&await this.uplinkConnection.publish({localAudioTrack:t.localAuxAudioTrack,localVideoTrack:t.localAuxVideoTrack,isAuxiliary:!0})}this.remotePublishedUserMap.forEach(t=>{let i=new sd({userId:t.userId,tinyId:t.tinyId,room:this,signalChannel:this._signalChannel,enableSEI:this.enableSEI,flag:t.flag,remoteAudioTrack:t.remoteAudioTrack,remoteVideoTrack:t.remoteVideoTrack,remoteAuxiliaryTrack:t.remoteAuxiliaryTrack});this.installDownlinkEvents(i,t.userId),this.remotePublishedUserMap.set(t.userId,i),t.isMainStreamSubscribed&&i.subscribe(t.subscribeState,"main"),t.isAuxStreamSubscribed&&i.subscribe(t.subscribeState,"auxiliary")})}destroy(){this._isDestroyed||(this._signalChannel&&(this._log.info("destroying SignalChannel"),this._signalChannel.close(),this._signalChannel=null),super.destroy(),this._joinReject&&(this._joinReject(new A({code:E.INVALID_OPERATION,message:v({key:b.CLIENT_DESTROYED,data:{funName:"join"}})})),this.clearJoinTimeout(),this.reset()),this.removeAllListeners())}async switchRole(t){this.role!==t&&(t==="audience"&&this.uplinkConnection&&this.closeUplink("you switch role to audience"),await this.doSwitchRole(t))}doSwitchRole(t){let i={command:H.SWITCH_ROLE,data:{role:t==="anchor"?20:21,privateMapKey:this.privateMapKey},responseCommand:V.SWITCH_ROLE_RES,retries:1};return this._log.info(`switchRole signal data: ${JSON.stringify(i.data)}`),this._signalChannel.sendWaitForResponseWithRetry(i).then(s=>{let{code:o,message:n}=s.data;if(o!==0)throw new A({code:E.SWITCH_ROLE_FAILED,message:v({key:b.SWITCH_ROLE_FAILED,data:{message:n,code:o}})});this.role=t}).catch(s=>{throw s instanceof A&&s.getCode()===E.API_CALL_TIMEOUT&&(s=new A({code:E.SWITCH_ROLE_FAILED,message:v({key:b.SWITCH_ROLE_TIMEOUT})})),this._log.error(s),s})}async publish(...t){let i={},s={};t.forEach(c=>{c instanceof Ne&&(c instanceof Pt?s.audio=c:i.audio=c),c instanceof fe&&(c instanceof ke&&c.mediaType===2?s.video=c:i.video=c)});let o=Ps(i),n=Ps(s);(!o||!n)&&!this.uplinkConnection&&(this.singlePC?this.uplinkConnection=new gd({userId:this.userId,tinyId:this.tinyId,room:this,signalChannel:this._signalChannel,enableSEI:this.enableSEI,audioManager:this.audioManager}):this.uplinkConnection=new ln({userId:this.userId,tinyId:this.tinyId,room:this,signalChannel:this._signalChannel,enableSEI:this.enableSEI,audioManager:this.audioManager}),this.uplinkConnection.on("connection-state-changed",c=>{this.emit("media-connection-state-changed",{...c,userId:this.userId})}),this.uplinkConnection.on("firewall-restriction",()=>{this.emit("firewall-restriction")}),this.uplinkConnection.on("error",c=>{let d=c.getCode();d!==E.ICE_TRANSPORT_ERROR&&(d===E.UPLINK_RECONNECTION_FAILED&&this.closeUplink(),this.emit("error",c))}));let a=t.map(c=>c.kind).join(",");o||(this._log.info(`publish() => main ${a}`),await this.uplinkConnection.publish({localAudioTrack:i.audio,localVideoTrack:i.video,isAuxiliary:!1}),this._log.info("main is published")),n||(this._log.info(`publish() => aux ${a}`),await this.uplinkConnection.publish({localAudioTrack:s.audio,localVideoTrack:s.video,isAuxiliary:!0}),this._log.info("aux is published"))}async unpublish(...t){if(this.scene==="live"&&this.role!=="anchor"||!this.isMainStreamPublished&&!this.isAuxStreamPublished||!this.uplinkConnection)return;let i={},s={};t.forEach(o=>{!o.mediaTrack||(o instanceof Ne&&(o instanceof Pt?s.audio=o:i.audio=o),o instanceof fe&&(o instanceof ke&&o.mediaType===2?s.video=o:i.video=o))});try{let o=t.map(n=>n.kind).join(",");Ps(i)||(this._log.info(`unpublish() => main ${o}`),await this.uplinkConnection.unpublish({localAudioTrack:i.audio,localVideoTrack:i.video})),Ps(s)||(this._log.info(`unpublish() => aux ${o}`),await this.uplinkConnection.unpublish({localAudioTrack:s.audio,localVideoTrack:s.video}))}catch{}this.localTracks.size===0&&!this.audioManager.mixedAudioTrack&&this.closeUplink("you unpublished")}addTrack(t){if(!this.uplinkConnection||!t.mediaTrack)return Promise.resolve();let i=this.uplinkConnection.addTrack(t);return t.publish(this,i),i}removeTrack(t){return!this.uplinkConnection||!t.mediaTrack?Promise.resolve():this.uplinkConnection.removeTrack(t).then(i=>(t.unpublish(),i))}replaceTrack(t){return!this.uplinkConnection||!t.mediaTrack?Promise.resolve():this.uplinkConnection.replaceTrack(t).then(i=>{_.emit(m.LOCAL_TRACK_REPLACED,{track:t})})}async setBandWidth(t){!this.uplinkConnection||(await this.uplinkConnection.setBandwidth(t),await this.uplinkConnection.sendMediaSettings())}async enableSmall(t){if(!this.uplinkConnection||!this.uplinkConnection.localMainVideoTrack)return Promise.resolve();t&&this.uplinkConnection.localMainVideoTrack.small&&await this.setBandWidth({type:l.VIDEO,videoType:l.SMALL,bandwidth:this.uplinkConnection.localMainVideoTrack.small.bitrate}),await this.uplinkConnection.enableSmall(t)}async subscribe(...t){if(t=t.filter(n=>!n.isSubscribed),t.length===0)return;let{userId:i}=t[0],s=this.remotePublishedUserMap.get(i);if(!s)return;let o=t.find(n=>n.mediaType===2)?"auxiliary":"main";try{let n={...s.subscribeState};t.forEach(c=>{switch(c.mediaType){case 1:n.audio=!0;break;case 4:n.video=!0;break;case 2:n.auxiliary=!0;break}});let a=this._changeBigSmallRecords.get(i);a&&a.options.smallVideo&&s.muteState.hasSmall&&n.video&&(n.video=!1,n.smallVideo=!0),_.emit(m.SUBSCRIBE_START,{room:this,streamType:o,remotePublishedUser:s,subscribeState:n}),this._log.info(`subscribe() => ${i} ${o} [${un(n)}] prev: [${un(s.subscribeState)}]`),await s.subscribe(n,o);for(let c of t)c.mediaTrack||await c.waitHasMediaTrack();s.remoteVideoTrack.setMediaType(n.smallVideo?8:4),_.emit(m.SUBSCRIBE_SUCCESS,{room:this,streamType:o,remotePublishedUser:s})}catch(n){let a=n instanceof A?n.getCode():E.UNKNOWN,c=n;throw n instanceof A?a===E.REMOTE_STREAM_NOT_EXIST&&(c=new A({code:E.API_CALL_ABORTED,message:v({key:b.API_CALL_ABORTED,data:{message:n.message,userId:i,streamType:o}})}),this._log.warn(c)):(c=new A({code:a,message:v({key:b.SUBSCRIBE_FAILED,data:{message:n.message,userId:i,streamType:o}})}),this._log.error(c)),c}}async unsubscribe(...t){let{userId:i}=t[0],s=this.remotePublishedUserMap.get(i);if(!s)return;let o=t.find(n=>n.mediaType===2)?"auxiliary":"main";this._log.info(`unsubscribe() => ${i} ${o}`);try{await s.unsubscribe({remoteTracks:t,streamType:o})}catch(n){this._log.warn(`unsubscribe() => failed ${n}`)}t.forEach(n=>{n.unsubscribe(),n.mediaType===8&&n.setMediaType(4)}),_.emit(m.UNSUBSCRIBE_SUCCESS,{room:this,streamType:o,remotePublishedUser:s})}setEncodedDataProcessingListener(t){throw new Error("Method not implemented.")}enableAudioVolumeEvaluation(t=2e3,i){if(t<=0){this._enableAudioVolumeEvaluation=!1,J.clearTask(this._audioVolumeIntervalId);return}t=Math.floor(Math.max(t,100)),_.emit(m.AUDIO_LEVEL_INTERVAL,{interval:t}),this._audioVolumeIntervalId&&J.clearTask(this._audioVolumeIntervalId),this._enableAudioVolumeEvaluation=!0,this._audioVolumeIntervalId=J.run(qr,()=>{let s=[];this.remotePublishedUserMap?.forEach(o=>{if(o.muteState.hasAudio){let n=Math.floor(o.remoteAudioTrack.getAudioLevel()*100);s.push({userId:o.userId,volume:n})}}),this.emit("audio-volume",s)},{fps:1e3/t,backgroundTask:i})}async getLocalAudioStats(){let t={};if(t[this.userId]={bytesSent:0,packetsSent:0},this.uplinkConnection){let i=await this._stats.getSenderStats(this.uplinkConnection);t[this.userId]={bytesSent:i.audio.bytesSent,packetsSent:i.audio.packetsSent}}return t}async getLocalVideoStats(){let t={};if(t[this.userId]={bytesSent:0,packetsSent:0,framesEncoded:0,framesSent:0,frameWidth:0,frameHeight:0},this.uplinkConnection){let{video:{bytesSent:i,packetsSent:s,framesEncoded:o,framesSent:n,frameWidth:a,frameHeight:c}}=await this._stats.getSenderStats(this.uplinkConnection);t[this.userId]={bytesSent:i,packetsSent:s,framesEncoded:o,framesSent:n,frameWidth:a,frameHeight:c}}return t}async getTransportStats(){let t={rtt:0,downlinksRTT:{}};if(this.uplinkConnection){let i=await this._stats.getSenderStats(this.uplinkConnection);t.rtt=i.rtt}for(let[,i]of this.remotePublishedUserMap){let s=await this._stats.getReceiverStats(i);t.downlinksRTT[s.userId]=s.rtt}return t}async getRemoteVideoStats(t){let i={};for(let[s,o]of this.remotePublishedUserMap)t==="main"&&o.muteState.hasVideo&&(i[s]=o.remoteVideoTrack.stat),t==="auxiliary"&&o.muteState.hasAuxiliary&&(i[s]=o.remoteAuxiliaryTrack.stat);return i}async getRemoteAudioStats(){let t={};for(let[i,s]of this.remotePublishedUserMap)s.muteState.hasAudio&&(t[i]=s.remoteAudioTrack.stat);return t}setTurnServer(t,i){this._log.info(`set turn server: ${JSON.stringify(t)} ${i||""}`);let s=[];Array.isArray(t)?t.forEach(o=>s.push(be.getTurnServer(o))):be.isPlainObject(t)&&s.push(be.getTurnServer(t)),this._turnServers=s,i&&(this._iceTransportPolicy=i)}sendStartMixTranscode(t){return this._signalChannel.sendWaitForResponse({command:H.START_MIX_TRANSCODE,data:t,timeout:5e3,responseCommand:V.START_MIX_TRANSCODE_RES,commandDesc:"startMixTranscode"})}sendStopMixTranscode(t){return this._signalChannel.sendWaitForResponse({command:H.STOP_MIX_TRANSCODE,data:t,timeout:5e3,responseCommand:V.STOP_MIX_TRANSCODE_RES,commandDesc:"stopMixTranscode"})}sendStartPublishCDN(t,i=!0){return this._signalChannel.sendWaitForResponse({command:i?H.START_PUBLISH_TENCENT_CDN:H.START_PUBLISH_GIVEN_CDN,data:t,timeout:5e3,responseCommand:i?V.START_PUBLISH_TENCENT_CDN_RES:V.START_PUBLISH_GIVEN_CDN_RES,commandDesc:"startPublishCDN"})}sendStopPublishCDN(t,i=!0){return this._signalChannel.sendWaitForResponse({command:i?H.STOP_PUBLISH_TENCENT_CDN:H.STOP_PUBLISH_GIVEN_CDN,data:t,timeout:5e3,responseCommand:i?V.STOP_PUBLISH_TENCENT_CDN_RES:V.STOP_PUBLISH_GIVEN_CDN_RES,commandDesc:"stopPublishCDN"})}sendAbilityStatus(t){this._signalChannel?.sendWaitForResponse({command:H.ABILITY_STATUS_REPORT,data:t,timeout:5e3,responseCommand:V.ABILITY_STATUS_REPORT_RESULT,commandDesc:"ability status report"}).catch(i=>{})}getIceServers(){return this._turnServers.length===0&&this.scheduleResult.iceServers?this.scheduleResult.iceServers:this._turnServers}getIceTransportPolicy(){return this._iceTransportPolicy||this.scheduleResult.iceTransportPolicy||"all"}getLogger(){return this._log}enableAIVoice(){throw new Error("Method not implemented.")}getSignalChannelUrl(){let t={mainUrl:"",backupUrl:""},i=be.getEnv();return i?(t.mainUrl=`wss://${i}.rtc.qq.com`,t.backupUrl=t.mainUrl):this.proxy_ws?(t.mainUrl=this.proxy_ws,t.backupUrl=t.mainUrl):Array.isArray(this.scheduleResult.domains)&&this.scheduleResult.domains.length>0&&(t.mainUrl=`wss://${this.scheduleResult.domains[0]}`,t.backupUrl=t.mainUrl,this.scheduleResult.domains[1]&&(t.backupUrl=`wss://${this.scheduleResult.domains[1]}`)),t}getSignalInfo(){return this._signalChannel?.getSignalInfo()||{clientIp:"",relayIp:""}}reset(t=!1){this.stopSyncUserListInterval(),this.stopHeartbeat(),this.closeConnections(),this.clearNetworkQuality(),this.closeUplink("you exitRoom"),this._signalChannel&&(t&&this._signalChannel.keepAlive&&this._signalChannel.isConnected?this._signalChannel.stopKeepAliveIn(3600):this._signalChannel.close(),this.setSignalChannel(null)),this._stats.reset(),this.userManager.clear(),this.userManager.removeAllListeners(),this.singlePC&&(this.singlePC.close(),this.singlePC=null),this.scheduleResult={domains:null,iceServers:null,iceTransportPolicy:null,trtcAutoConf:null}}async checkSubscribeBigSmallVideo(t){let{subscribeState:i,userId:s,muteState:{hasSmall:o,hasVideo:n}}=t;if(!o&&!n||!i.video&&!i.smallVideo)return;let a=this._changeBigSmallRecords.get(s);if(!a||a.isSubscribing||a.reSubscribeCount<=0)return;let{options:c,reSubscribeCount:d}=a;if(c.video&&i.video||c.smallVideo&&i.smallVideo&&o)return;let u={audio:t.remoteAudioTrack.isSubscribed||t.remoteAudioTrack.isSubscribing,auxiliary:t.remoteAuxiliaryTrack.isSubscribed||t.remoteAuxiliaryTrack.isSubscribing,video:c.video,smallVideo:c.smallVideo};try{if(!o&&u.smallVideo&&(u.video=!0,u.smallVideo=!1),u.smallVideo===i.smallVideo&&u.video===i.video)return;a.isSubscribing=!0,a.reSubscribeCount=d-1,await t.subscribe(u,"main"),t.remoteVideoTrack.setMediaType(u.smallVideo?8:4),this._log.info(`change [${s}] to ${u.smallVideo?"small":"big"} video successfully. count ${qi-a.reSubscribeCount}.`),a.isSubscribing=!1,a.reSubscribeCount=qi}catch{this._log.info(`change [${s}] to ${u.smallVideo?"small":"big"} video failed. count ${qi-a.reSubscribeCount}.`),a.isSubscribing=!1,a.reSubscribeCount===0&&this._changeBigSmallRecords.delete(s)}}changeType(t,i){let o={options:{video:!t,smallVideo:t},isSubscribing:!1,reSubscribeCount:qi};this._changeBigSmallRecords.set(i.userId,o),this._log.info(`set [${i.userId}] video prefer type: ${t?"small":"big"}`)}get smallStreamConfig(){return this._smallStreamConfig}_initBusinessInfo(t){this._businessInfo=t.businessInfo;let i={};if(Zl(t.businessInfo)&&(i=JSON.parse(t.businessInfo)),!Ir(t.pureAudioPushMode)){if(!Number.isInteger(Number(t.pureAudioPushMode)))throw new A({code:E.INVALID_PARAMETER,message:v({key:b.INVALID_PURE_AUDIO})});this._pureAudioPushMode=t.pureAudioPushMode,i.Str_uc_params||(i.Str_uc_params={}),i.Str_uc_params.pure_audio_push_mod=this._pureAudioPushMode}if(!Ir(t.userDefineRecordId)){let s=/^[A-Za-z0-9_-]{1,64}$/gi;if(t.userDefineRecordId.match(s)===null)throw new A({code:E.INVALID_PARAMETER,message:v({key:b.INVALID_USER_DEFINE_RECORDID})});i.Str_uc_params||(i.Str_uc_params={}),i.Str_uc_params.userdefine_record_id=t.userDefineRecordId}if(!Ir(t.userDefinePushArgs))if(Zl(t.userDefinePushArgs)&&String(t.userDefinePushArgs)&&String(t.userDefinePushArgs).length<=256)i.Str_uc_params||(i.Str_uc_params={}),i.Str_uc_params.userdefine_push_args=t.userDefinePushArgs;else throw new A({code:E.INVALID_PARAMETER,message:v({key:b.INVALID_USER_DEFINE_PUSH_ARGS})});Ps(i)||(this._businessInfo=JSON.stringify(i))}sendSEI(t,i){this.singlePC&&this.singlePC.sendSEI(t,i)}};y([q(["left",k.INIT],"joined"),Er({settings:{retries:1,timeout:0},onError(r,e,t){this._isUsingCachedSchedule&&!this._isDestroyed?(this._log.warn("is using cached schedule, retry join"),si(!0),this.reset(),e()):this._signalChannel&&this._signalChannel.isConnected&&this._signalChannel.keepAlive?(this._log.warn("is using keepAlive ws, retry join"),this._signalChannel.close(),this.reset(),e()):(this.reset(),this._log.error(r),t(r))}}),ml()],At.prototype,"join",1),y([q("joined","left",{ignoreError:!0,success(){this.reset(!0)}}),_l(),Lu("leave room"),vs({fnName:"publish",validateArgs:!1}),vs({fnName:"unsubscribe",validateArgs:!1})],At.prototype,"leave",1),y([Ri(),ql(),Er({settings:{retries:vt,timeout:r=>we(r)},onError(r,e,t){r.message?.includes("timeout")?(this._log.warn("publish timeout"),e()):(this._log.error(`publish failed: ${r}`),t(r),_.emit(m.PUBLISH_FAILED,{room:this}))}})],At.prototype,"publish",1),y([vs({fnName:"publish",callback(...r){this.localTracks.size===0&&(this.uplinkConnection?.close("you unpublished"),this.uplinkConnection=null,r.forEach(e=>e.unpublish()))}}),Mu("api-call"),Ri(),Ql()],At.prototype,"unpublish",1),y([rc((...r)=>r[0].userId),fl(),Er({settings:{retries:vt,timeout:r=>we(r)},onError(r,e,t,i){r.message.includes("timeout")?(this._log.warn("subscribe timeout"),e()):(this._log.error(`subscribe failed: ${r}`),t(r),_.emit(m.SUBSCRIBE_FAILED,{room:this,remoteTracks:i}))}})],At.prototype,"subscribe",1),y([vs({fnName:"subscribe",callback(...r){this.singlePC||r.forEach(e=>{let t=this.remotePublishedUserMap.get(e.userId);t&&!t.isMainStreamSubscribed&&!t.isAuxStreamSubscribed&&t.close("you unsubscribed")})}}),rc((...r)=>r[0].userId)],At.prototype,"unsubscribe",1);Is.create=Is._create.bind(Is,At);var Xm=Is;return dh(qm);})().default;
