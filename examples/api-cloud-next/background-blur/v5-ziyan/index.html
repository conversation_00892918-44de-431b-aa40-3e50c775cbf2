<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>v5</title>
    <link href="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/css/fastbootstrap.min.css" rel="stylesheet" integrity="sha256-xLGBU65wCDv2/qEdq3ZYw2Qdiia/wxxeGepRyZmpQdY=" crossorigin="anonymous">
  </head>
  <body>
    <input type="radio" name="profile" value="480p15" disabled> 480p15
    <input type="radio" name="profile" value="480p30" disabled> 480p30
    <input type="radio" name="profile" value="720p15" disabled> 720p15
    <input type="radio" name="profile" value="720p30" disabled> 720p30
    <input type="radio" name="profile" value="1080p20" disabled> 1080p20
    <div id="log">  </div>
    <hr>
    <button id="video" class="btn btn-default"> 打开摄像头 </button>
    <button id="enter-room" class="btn btn-default"> 进房 </button>
    <button id="test" class="btn btn-default"> test </button>
    <div class="input-group flex-nowrap">
      <span class="input-group-text">选择设备</span>
      <select class="form-select" size="2" id="device-select">
      </select>
    </div>
    <!-- <button id="open-local-video-view" class="btn btn-default" disabled> 开启本地预览 </button>
    <button id="close-local-video-view" class="btn btn-default" disabled> 关闭本地预览 </button>
    <button id="exit" class="btn btn-default" disabled> 退房 </button> -->
    <hr>
    <div class="btn-group" role="group" aria-label="Basic example">
      <button id="startPlugin" class="btn btn-default"> startPlugin </button>
      <button id="updatePlugin" class="btn btn-default"> updatePlugin </button>
      <button id="stopPlugin" class="btn btn-default"> stopPlugin </button>
    </div>
    <div class="input-group flex-nowrap">
      <span class="input-group-text">虚拟背景样式</span>
      <select class="form-select" size="2" id="background-type">
        <option value="blur" selected> blur 模糊 </option>
        <option value="image">image 图片（需填入图片 url）</option>
      </select>
    </div>
    <div class="input-group flex-nowrap">
      <span class="input-group-text" id="addon-wrapping">图片 url</span>
      <input type="text" class="form-control" id="image-url" value="https://picsum.photos/seed/picsum/200/300" placeholder="https://..." />
    </div>
    <hr>
    其他测试：
    <input type="checkbox" name="water-mark" id="water-mark"> 水印
    <input type="checkbox" name="mirror" id="mirror">  编码翻转
    <input type="checkbox" name="webgl" checked id="webgl">  webgl
    <input type="checkbox" name="model" id="model">  自研模型
    <input type="checkbox" name="tflite" checked id="tflite">  tflite
    <input type="checkbox"  name="blur" checked id="blur-background"> 背景虚化
    <input type="checkbox" name="virtual" id="virtual-background">  图片背景
    <div id="local_stream" style="width: 666px"></div>
  </body>
</html>

<script src="https://cdn.jsdelivr.net/npm/fastbootstrap@1.1.2/dist/js/fastbootstrap.min.js" integrity="sha256-+c+/OCMmtlZadi89VaV1yUOkk1T4BD2pwBFpY3OcrqI=" crossorigin="anonymous"></script>
<!-- <script src="../selfie_segmentation/selfie_segmentation.js"></script> -->
<script type="module" src="./main.js"></script>
<script type="module" src="../common.js"></script>
<script src="./srtc.js"></script>
<!-- <script src="./video-effect.iife.js"></script> -->
<!-- <script src="../../dist/trtc.js"></script> -->
<!-- <script src="./../../../../plugins/cloud-video-effect/virtual-background/dist/virtual-background.iife.js"></script> -->
