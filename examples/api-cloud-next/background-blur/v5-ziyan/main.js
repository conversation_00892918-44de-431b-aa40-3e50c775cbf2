/* global TRTC VirtualBackground*/

import { sdkAppId, userId, roomId, userSig } from '../common.js';
// eslint-disable-next-line max-len
// import { VirtualBackground } from '../../../../packages/api-cloud-next/dist/npm-package/plugins/video-effect/virtual-background/virtual-background.esm.js';

document.getElementById('video').addEventListener('click', openCamera);
document.getElementById('enter-room').addEventListener('click', enter);
// document.getElementById('open-local-video-view').addEventListener('click', openLocalVideoView);
// document.getElementById('close-local-video-view').addEventListener('click', closeLocalVideoView);
// document.getElementById('exit').addEventListener('click', exit);
document.getElementById('test').addEventListener('click', test);

document.getElementById('startPlugin').addEventListener('click', startPlugin);
document.getElementById('updatePlugin').addEventListener('click', updatePlugin);
document.getElementById('stopPlugin').addEventListener('click', stopPlugin);
document.getElementById('device-select').addEventListener('change', changeCamera);

const backgroundTypeEle = document.getElementById('background-type');
const cameraListEle = document.getElementById('device-select');
const imageUrlEle = document.getElementById('image-url');

let trtc = TRTC.create();

TRTC.getCameraList().then(list => {
  console.warn(list);
  list.forEach(l => {
    const optionEle = document.createElement('option');
    optionEle.value = l.deviceId;
    optionEle.text = l.label;
    cameraListEle.appendChild(optionEle);
  });
});

async function test() {
}

let presetProfile = {};

function changeCamera() {
  trtc.updateLocalVideo({ option: { cameraId: cameraListEle.value } });
}

// function onError(e) {
//   const { extraCode } = e;
//   if (extraCode === 10000003 || extraCode === 10000006) {
//     // 降低分辨率帧率或者关闭插件
//     console.error('client', e, e.code);
//     console.error('client', JSON.stringify(e), e.code);
//   }
// }

async function onError(event) {
  const { code } = event;
  if (code === 10000003 || code === 10000006) {
    // 降低分辨率帧率或者关闭插件
    await trtc.stopPlugin('VirtualBackground');
  }
}

async function startPlugin() {
  console.warn(backgroundTypeEle.value, imageUrlEle.value);
  try {
    // await trtc.startPlugin('VirtualBackground', {
    //   sdkAppId,
    //   userId,
    //   userSig,
    //   type: backgroundTypeEle.value,
    //   src: imageUrlEle.value,
    //   onError,
    // });
    if (backgroundTypeEle.value === 'blur') {
      trtc._room.videoManager.setVirtualBackground();
      trtc._room.videoManager.setBlurBackground(true);
    } else {
      trtc._room.videoManager.setBlurBackground(false);
      await trtc._room.videoManager.setVirtualBackground(imageUrlEle.value);
    }
    console.warn('VirtualBackground success');
  } catch (e) {
    console.error('VirtualBackground failed', e);
  }
}

async function updatePlugin() {
  // trtc.updatePlugin('VirtualBackground', {
  //   type: backgroundTypeEle.value,
  //   src: imageUrlEle.value
  // });
  if (backgroundTypeEle.value === 'blur') {
    trtc._room.videoManager.setVirtualBackground();
    trtc._room.videoManager.setBlurBackground(true);
  } else {
    trtc._room.videoManager.setBlurBackground(false);
    await trtc._room.videoManager.setVirtualBackground(imageUrlEle.value);
  }
}

async function stopPlugin() {
  trtc._room.videoManager.setVirtualBackground();
  trtc._room.videoManager.setBlurBackground(false);
  trtc.stopPlugin('VirtualBackground');
}

async function enter() {
  await trtc.enterRoom({ roomId, sdkAppId, userId, userSig });
}

async function openCamera() {
  if (!trtc) trtc = TRTC.create();
  // await trtc.enterRoom({ roomId, sdkAppId, userId, userSig });
  trtc._room.videoManager.selfModel = document.getElementById('model').checked;
  trtc._room.videoManager.renderMode = document.getElementById('webgl').checked ? 'webgl' : '2d';
  trtc._room.videoManager.useTflite = document.getElementById('tflite').checked;
  trtc._room.videoManager.mirror = document.getElementById('mirror').checked;
  trtc._room.videoManager.setBlurBackground(document.getElementById('blur-background').checked);
  trtc._room.videoManager.setVirtualBackground(document.getElementById('virtual-background').checked && 'https://picsum.photos/seed/picsum/200/300');
  await trtc.startLocalVideo({ view: 'local_stream' });
  // await trtc.startLocalVideo({
  //   option: {
  //     profile: presetProfile,
  //     cameraId: cameraListEle.value
  //   },
  // });
  output();

  // openLocalVideoView();
}

function openLocalVideoView() {
  trtc.updateLocalVideo({ view: 'local_stream' });
}

function closeLocalVideoView() {
  trtc.updateLocalVideo({ view: null });
}

// async function enter() {
//   await trtc.exitRoom();
//   await trtc.stopLocalVideo();
//   await trtc.destroy();
//   await trtc.stopPlugin('VirtualBackground');
//   trtc = null;
// }

// async function exit() {
//   await trtc.exitRoom();
//   await trtc.stopLocalVideo();
//   await trtc.destroy();
//   await trtc.stopPlugin('VirtualBackground');
//   trtc = null;
// }

// 设置分辨率相关

const profiles = {
  '480p15': { width: 640, height: 480, frameRate: 15, bitrate: 1000 },
  '480p30': { width: 640, height: 480, frameRate: 30, bitrate: 1000 },
  '720p15': { width: 1280, height: 720, frameRate: 15, bitrate: 2000 },
  '720p30': { width: 1280, height: 720, frameRate: 30, bitrate: 2000 },
  '1080p20': { width: 1920, height: 1080, frameRate: 20, bitrate: 5000 }
};

const radioButtons = document.querySelectorAll('input[name="profile"]');
radioButtons.forEach(button => {
  button.addEventListener('change', () => {
    console.warn(button);
    const selectedProfile = button.value;
    const profile = profiles[selectedProfile];
    setVideoProfile(profile);
  });
  button.removeAttribute('disabled');
});

async function setVideoProfile(profile) {
  console.log(profile);
  presetProfile = profile;
  if (!trtc.getVideoTrack()) return;
  await trtc.updateLocalVideo({
    option: {
      profile,
    },
  });
  output();
}

async function output() {
  const videoTrack = trtc.getVideoTrack();
  if (!videoTrack) return;
  const settings = videoTrack.getSettings();
  const log = document.createElement('span');
  log.innerHTML = `当前分辨率：${settings.width}x${settings.height}, fps: ${settings.frameRate}`;
  document.getElementById('log').appendChild(log);
}

document.getElementById('water-mark').addEventListener('change', () => {
  if (document.getElementById('water-mark').checked) {
    trtc._room.videoManager.setWatermark({ x: 100, y: 10, imageUrl: './test.png' });
  } else {
    trtc._room.videoManager.stopWatermark();
  }
});

document.getElementById('mirror').addEventListener('change', () => {
  trtc._room.videoManager.mirror = document.getElementById('mirror').checked;
});

document.getElementById('webgl').addEventListener('change', () => {
  trtc._room.videoManager.renderMode = document.getElementById('webgl').checked ? 'webgl' : '2d';
});

document.getElementById('blur-background').addEventListener('change', () => {
  trtc._room.videoManager.setBlurBackground(document.getElementById('blur-background').checked);
});

document.getElementById('virtual-background').addEventListener('change', () => {
  if (document.getElementById('virtual-background').checked) {
    trtc._room.videoManager.setVirtualBackground('https://picsum.photos/seed/picsum/200/300');
  } else {
    trtc._room.videoManager.setVirtualBackground();
  }
});
