{"format": "graph-model", "generatedBy": "2.13.0", "convertedBy": "TensorFlow.js Converter v4.11.0", "signature": {"inputs": {"src": {"name": "src:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "3"}, {"size": "144"}, {"size": "256"}]}}}, "outputs": {"res": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "144"}, {"size": "256"}]}}}}, "modelTopology": {"node": [{"name": "ConstantFolding/PartitionedCall/split_59-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_187/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_60", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "unknown_131", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_188/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_190/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_61", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_133", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_191/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_13/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_80/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_23/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_23/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/split_54-folded-1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_170/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_55", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "unknown_120", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_171/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_173/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_56", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "unknown_122", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_174/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_11/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/add_71/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_22/Minimum/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/clip_by_value_22/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/split_49-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_153/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_50", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_109", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_154/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_156/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_51", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_111", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "PartitionedCall/transpose_157/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_9/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/add_62/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_21/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_21/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/transpose_142/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_45", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_99", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_143/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_41-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_133/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_42", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_93", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_134/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_136/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_43", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_95", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_137/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_7/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_53/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_20/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_20/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_190_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_181_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/split_40", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "96"}]}}}}}, {"name": "unknown_89", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_128/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_10", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_130/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_9", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_91", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_131/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_139/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_44", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_97", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "PartitionedCall/transpose_140/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "ConstantFolding/PartitionedCall/split_36-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_118/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_37", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_83", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "PartitionedCall/transpose_119/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_121/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_38", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_85", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_122/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_6/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_47/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_17/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_17/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_165_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_156_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/split_31-folded-1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_103/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_32", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_73", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "PartitionedCall/transpose_104/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_106/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_33", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "unknown_75", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_107/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_5/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_41/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_14/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_14/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_139_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_130_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/split_30", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_69", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_98/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_8", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_100/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_7", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "128"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_71", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_101/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_109/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_34", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "48"}]}}}}}, {"name": "unknown_77", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "PartitionedCall/transpose_110/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_26-folded-1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_88/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_27", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "48"}]}}}}}, {"name": "unknown_63", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "PartitionedCall/transpose_89/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_91/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_28", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "192"}]}}}}}, {"name": "unknown_65", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}}}, {"name": "PartitionedCall/transpose_92/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_4/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/add_35/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/clip_by_value_11/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_11/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_114_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_105_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/split_21-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_73/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_22", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "unknown_53", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_74/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_76/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_23", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_55", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_77/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_3/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/add_29/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/clip_by_value_8/Minimum/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/clip_by_value_8/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_88_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_79_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/split_20", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_49", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_68/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_6", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_70/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_5", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "128"}, {"size": "1"}]}}}}}, {"name": "unknown_51", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_71/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_79/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_24", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "unknown_57", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "PartitionedCall/transpose_80/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_16-folded-1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_58/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_17", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}}}, {"name": "unknown_43", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_59/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_61/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_18", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}}}, {"name": "unknown_45", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_62/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul_2/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_23/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_5/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_5/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_63_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_54_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/split_13", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "88"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_33", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}}}, {"name": "PartitionedCall/transpose_44/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_4", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_46/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_3", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "88"}, {"size": "1"}]}}}}}, {"name": "unknown_35", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_47/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_49/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_14", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "88"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_37", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_50/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_7-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_25/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_8", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "8"}]}}}}}, {"name": "unknown_21", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "8"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_26/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_28/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_9", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "8"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_23", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "PartitionedCall/transpose_29/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_1/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_11/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_2/Minimum/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_2/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/split_6", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "24"}]}}}}}, {"name": "unknown_17", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_20/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Const_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_22/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_1", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_19", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_23/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_31/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_10", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_25", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_32/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_2-folded-1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_10/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "8"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_11", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "8"}]}}}}}, {"name": "PartitionedCall/transpose_11/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_13/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_4", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "8"}, {"size": "16"}]}}}}}, {"name": "unknown_13", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_14/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/mul/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/add_5/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_1/Minimum/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "PartitionedCall/clip_by_value_1/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_11_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "1"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_3_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "1"}, {"size": "1"}]}}}}}, {"name": "PartitionedCall/Const", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_1/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}}}, {"name": "unknown_2", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_2/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_4/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_7", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_5/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_7/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_9", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_8/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_16/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_5", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "unknown_15", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_17/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_34/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_11", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "72"}]}}}}}, {"name": "unknown_27", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_35/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_37/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "72"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_29", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_38/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_40/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_12", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "24"}]}}}}}, {"name": "unknown_31", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_41/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_52/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_15", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}}}, {"name": "unknown_39", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_53/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_5", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_55/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_4", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "unknown_41", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_56/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_64/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_19", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}}}, {"name": "unknown_47", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_65/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_82/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_25", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "192"}]}}}}}, {"name": "unknown_59", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_83/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_7", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_85/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Reshape_6", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "192"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_61", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "192"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_86/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_94/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_29", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "192"}, {"size": "48"}]}}}}}, {"name": "unknown_67", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "48"}]}}}}}, {"name": "PartitionedCall/transpose_95/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_112/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_35", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "48"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_79", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_113/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_9", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_115/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_8", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_81", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "PartitionedCall/transpose_116/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "unknown_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_4", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_5", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_124/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_39", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}}}, {"name": "unknown_87", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_125/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "ConstantFolding/PartitionedCall/split_46-folded-1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_145/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_47", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "PartitionedCall/transpose_146/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_147/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Cast_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_148/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/SelectV2_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/zeros", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Const_13", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Shape", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SelectV2_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/Const_12", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/ExpandDims", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}, {"size": "1"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Cast_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_14", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/transpose_150/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_48", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_107", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_151/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_159/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_52", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "unknown_113", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "PartitionedCall/transpose_160/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Const_15", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_162/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_11", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}}}, {"name": "unknown_115", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "PartitionedCall/transpose_163/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_164/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Cast_5", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_165/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/SelectV2_5", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/zeros_1", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_18", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Shape_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/SelectV2_6", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/Const_17", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/ExpandDims_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}, {"size": "1"}]}}}}}, {"name": "PartitionedCall/Cast_7", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "PartitionedCall/Const_19", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/transpose_167/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_53", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "16"}]}}}}}, {"name": "unknown_118", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_168/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_176/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_57", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_124", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_177/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_20", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_179/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Reshape_13", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}}, {"name": "unknown_126", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_180/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_181/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Cast_9", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_182/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/SelectV2_9", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/zeros_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Const_23", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Shape_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/SelectV2_10", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_22", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/ExpandDims_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}, {"size": "1"}]}}}}}, {"name": "PartitionedCall/Cast_11", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_24", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/transpose_184/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_58", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "unknown_129", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_185/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_193/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_62", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_135", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "PartitionedCall/transpose_194/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_25", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_196/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_15", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}}, {"name": "unknown_137", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_197/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/transpose_198/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Cast_13", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_199/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/SelectV2_13", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "PartitionedCall/zeros_3", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Const_28", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}}}, {"name": "PartitionedCall/Shape_3", "op": "Const", "attr": {"dtype": {"type": "DT_INT64"}, "value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/SelectV2_14", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_27", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/ExpandDims_3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}, {"size": "1"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Cast_15", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "unknown_105", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_29", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT64", "tensorShape": {}}}, "dtype": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Const_30", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_201/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/split_63", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_140", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_202/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_31", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}}}, {"name": "PartitionedCall/transpose_204/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Reshape_17", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "4"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_142", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_205/perm", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/Const_32", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}, {"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_207/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Reshape_18", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "4"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "unknown_144", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "4"}]}}}}}, {"name": "PartitionedCall/split_64", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "4"}, {"size": "1"}]}}}}}, {"name": "unknown_146", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "PartitionedCall/transpose_211/perm", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "4"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "src", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "1"}, {"size": "3"}, {"size": "144"}, {"size": "256"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/SparseToDense", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims", "PartitionedCall/Cast_3", "PartitionedCall/SelectV2_1", "PartitionedCall/zeros"], "attr": {"validate_indices": {"b": true}, "Tindices": {"type": "DT_INT64"}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_1", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims", "PartitionedCall/Cast_3", "PartitionedCall/SelectV2_2", "PartitionedCall/Const_12"], "attr": {"T": {"type": "DT_INT64"}, "validate_indices": {"b": true}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_3", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_1", "PartitionedCall/Cast_7", "PartitionedCall/SelectV2_5", "PartitionedCall/zeros_1"], "attr": {"validate_indices": {"b": true}, "T": {"type": "DT_INT64"}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_4", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_1", "PartitionedCall/Cast_7", "PartitionedCall/SelectV2_6", "PartitionedCall/Const_17"], "attr": {"Tindices": {"type": "DT_INT64"}, "T": {"type": "DT_INT64"}, "validate_indices": {"b": true}}}, {"name": "PartitionedCall/SparseToDense_6", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_2", "PartitionedCall/Cast_11", "PartitionedCall/SelectV2_9", "PartitionedCall/zeros_2"], "attr": {"Tindices": {"type": "DT_INT64"}, "validate_indices": {"b": true}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_7", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_2", "PartitionedCall/Cast_11", "PartitionedCall/SelectV2_10", "PartitionedCall/Const_22"], "attr": {"Tindices": {"type": "DT_INT64"}, "T": {"type": "DT_INT64"}, "validate_indices": {"b": true}}}, {"name": "PartitionedCall/SparseToDense_9", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_3", "PartitionedCall/Cast_15", "PartitionedCall/SelectV2_13", "PartitionedCall/zeros_3"], "attr": {"Tindices": {"type": "DT_INT64"}, "T": {"type": "DT_INT64"}, "validate_indices": {"b": true}}}, {"name": "PartitionedCall/SparseToDense_10", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_3", "PartitionedCall/Cast_15", "PartitionedCall/SelectV2_14", "PartitionedCall/Const_27"], "attr": {"T": {"type": "DT_INT64"}, "validate_indices": {"b": true}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_2", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims", "PartitionedCall/Cast_3", "unknown_105", "PartitionedCall/Const_14"], "attr": {"Tindices": {"type": "DT_INT64"}, "T": {"type": "DT_INT64"}, "validate_indices": {"b": true}}}, {"name": "PartitionedCall/SparseToDense_5", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_1", "PartitionedCall/Cast_7", "unknown_105", "PartitionedCall/Const_19"], "attr": {"T": {"type": "DT_INT64"}, "validate_indices": {"b": true}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_8", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_2", "PartitionedCall/Cast_11", "unknown_105", "PartitionedCall/Const_24"], "attr": {"validate_indices": {"b": true}, "T": {"type": "DT_INT64"}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SparseToDense_11", "op": "SparseToDense", "input": ["PartitionedCall/ExpandDims_3", "PartitionedCall/Cast_15", "unknown_105", "PartitionedCall/Const_29"], "attr": {"T": {"type": "DT_INT64"}, "validate_indices": {"b": true}, "Tindices": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Sub_1", "op": "Sub", "input": ["src", "unknown"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Equal", "op": "Equal", "input": ["PartitionedCall/SparseToDense_1", "PartitionedCall/Const_13"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Equal_1", "op": "Equal", "input": ["PartitionedCall/SparseToDense_4", "PartitionedCall/Const_18"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Equal_2", "op": "Equal", "input": ["PartitionedCall/SparseToDense_7", "PartitionedCall/Const_23"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Equal_3", "op": "Equal", "input": ["PartitionedCall/SparseToDense_10", "PartitionedCall/Const_28"], "attr": {"incompatible_shape_error": {"b": true}, "T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_3", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Sub_1", "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_3_recip"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/SelectV2_3", "op": "SelectV2", "input": ["PartitionedCall/Equal", "PartitionedCall/Shape", "PartitionedCall/SparseToDense_1"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SelectV2_7", "op": "SelectV2", "input": ["PartitionedCall/Equal_1", "PartitionedCall/Shape_1", "PartitionedCall/SparseToDense_4"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SelectV2_11", "op": "SelectV2", "input": ["PartitionedCall/Equal_2", "PartitionedCall/Shape_2", "PartitionedCall/SparseToDense_7"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/SelectV2_15", "op": "SelectV2", "input": ["PartitionedCall/Equal_3", "PartitionedCall/Shape_3", "PartitionedCall/SparseToDense_10"], "attr": {"T": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/Pad", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Div_3", "PartitionedCall/Const"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_1", "op": "Transpose", "input": ["PartitionedCall/Pad", "PartitionedCall/transpose_1/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_1", "PartitionedCall/split", "unknown_2"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "filter_format": {"s": "SFdJTw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_2", "op": "Transpose", "input": ["PartitionedCall/Add", "PartitionedCall/transpose_2/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_11", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_11_recip", "PartitionedCall/transpose_2"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_6", "op": "AddV2", "input": ["PartitionedCall/transpose_2", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_6", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value", "op": "Maximum", "input": ["PartitionedCall/clip_by_value/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_12", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_11", "PartitionedCall/clip_by_value"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/transpose_4", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_12", "PartitionedCall/transpose_4/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_1", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_4", "PartitionedCall/split_1", "unknown_7"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "filter_format": {"s": "SFdJTw=="}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}}}, {"name": "PartitionedCall/transpose_5", "op": "Transpose", "input": ["PartitionedCall/Add_1", "PartitionedCall/transpose_5/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_14", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_1", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_14", "PartitionedCall/Const_1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_7", "op": "Transpose", "input": ["PartitionedCall/Pad_1", "PartitionedCall/transpose_7/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_7", "PartitionedCall/Reshape", "unknown_9"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/transpose_8", "op": "Transpose", "input": ["PartitionedCall/depthwise", "PartitionedCall/transpose_8/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_16", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_8"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Relu_16", "ConstantFolding/PartitionedCall/split_2-folded-1"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_10", "op": "Transpose", "input": ["PartitionedCall/Mean", "PartitionedCall/transpose_10/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_3", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_10", "PartitionedCall/split_3", "unknown_11"], "device": "/device:CPU:0", "attr": {"leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "num_host_args": {"i": "0"}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/transpose_11", "op": "Transpose", "input": ["PartitionedCall/Add_3", "PartitionedCall/transpose_11/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_19", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_11"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_13", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_19", "PartitionedCall/transpose_13/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_4", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_13", "PartitionedCall/split_4", "unknown_13"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_14", "op": "Transpose", "input": ["PartitionedCall/Add_4", "PartitionedCall/transpose_14/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_14", "PartitionedCall/mul/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_5", "op": "AddV2", "input": ["PartitionedCall/mul", "PartitionedCall/add_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_1/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_5", "PartitionedCall/clip_by_value_1/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_1", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_1/Minimum", "PartitionedCall/clip_by_value_1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_22", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_1", "PartitionedCall/onnx_tf_prefix_Relu_16"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_16", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_22", "PartitionedCall/transpose_16/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_6", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_16", "PartitionedCall/split_5", "unknown_15"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/Add_7", "op": "_FusedConv2D", "input": ["PartitionedCall/Add_6", "PartitionedCall/split_6", "unknown_17"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "leakyrelu_alpha": {"f": 0.2}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/transpose_17", "op": "Transpose", "input": ["PartitionedCall/Add_6", "PartitionedCall/transpose_17/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_20", "op": "Transpose", "input": ["PartitionedCall/Add_7", "PartitionedCall/transpose_20/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_25", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_20"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_2", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_25", "PartitionedCall/Const_2"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_22", "op": "Transpose", "input": ["PartitionedCall/Pad_2", "PartitionedCall/transpose_22/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_1", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_22", "PartitionedCall/Reshape_1", "unknown_19"], "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_23", "op": "Transpose", "input": ["PartitionedCall/depthwise_1", "PartitionedCall/transpose_23/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_27", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_23"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_1", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Relu_27", "ConstantFolding/PartitionedCall/split_7-folded-1"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_25", "op": "Transpose", "input": ["PartitionedCall/Mean_1", "PartitionedCall/transpose_25/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_9", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_25", "PartitionedCall/split_8", "unknown_21"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "use_cudnn_on_gpu": {"b": true}, "filter_format": {"s": "SFdJTw=="}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_host_args": {"i": "0"}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}}}, {"name": "PartitionedCall/transpose_26", "op": "Transpose", "input": ["PartitionedCall/Add_9", "PartitionedCall/transpose_26/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_30", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_26"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_28", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_30", "PartitionedCall/transpose_28/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_10", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_28", "PartitionedCall/split_9", "unknown_23"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}}}, {"name": "PartitionedCall/transpose_29", "op": "Transpose", "input": ["PartitionedCall/Add_10", "PartitionedCall/transpose_29/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_1", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_29", "PartitionedCall/mul_1/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_11", "op": "AddV2", "input": ["PartitionedCall/mul_1", "PartitionedCall/add_11/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_2/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_11", "PartitionedCall/clip_by_value_2/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_2", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_2/Minimum", "PartitionedCall/clip_by_value_2/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_33", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_2", "PartitionedCall/onnx_tf_prefix_Relu_27"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_31", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_33", "PartitionedCall/transpose_31/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_12", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_31", "PartitionedCall/split_10", "unknown_25"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_host_args": {"i": "0"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_32", "op": "Transpose", "input": ["PartitionedCall/Add_12", "PartitionedCall/transpose_32/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_35", "op": "AddV2", "input": ["PartitionedCall/transpose_32", "PartitionedCall/transpose_17"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_34", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_35", "PartitionedCall/transpose_34/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_13", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_34", "PartitionedCall/split_11", "unknown_27"], "device": "/device:CPU:0", "attr": {"TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_35", "op": "Transpose", "input": ["PartitionedCall/Add_13", "PartitionedCall/transpose_35/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_37", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_35"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_3", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_37", "PartitionedCall/Const_3"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_37", "op": "Transpose", "input": ["PartitionedCall/Pad_3", "PartitionedCall/transpose_37/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_2", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_37", "PartitionedCall/Reshape_2", "unknown_29"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/transpose_38", "op": "Transpose", "input": ["PartitionedCall/depthwise_2", "PartitionedCall/transpose_38/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_39", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_38"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_40", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_39", "PartitionedCall/transpose_40/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_15", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_40", "PartitionedCall/split_12", "unknown_31"], "device": "/device:CPU:0", "attr": {"leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "filter_format": {"s": "SFdJTw=="}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/Add_16", "op": "_FusedConv2D", "input": ["PartitionedCall/Add_15", "PartitionedCall/split_13", "unknown_33"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "num_host_args": {"i": "0"}}}, {"name": "PartitionedCall/transpose_41", "op": "Transpose", "input": ["PartitionedCall/Add_15", "PartitionedCall/transpose_41/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_44", "op": "Transpose", "input": ["PartitionedCall/Add_16", "PartitionedCall/transpose_44/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_42", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_44"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_4", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_42", "PartitionedCall/Const_4"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_46", "op": "Transpose", "input": ["PartitionedCall/Pad_4", "PartitionedCall/transpose_46/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_3", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_46", "PartitionedCall/Reshape_3", "unknown_35"], "attr": {"num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_47", "op": "Transpose", "input": ["PartitionedCall/depthwise_3", "PartitionedCall/transpose_47/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_44", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_47"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_49", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_44", "PartitionedCall/transpose_49/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_18", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_49", "PartitionedCall/split_14", "unknown_37"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/transpose_50", "op": "Transpose", "input": ["PartitionedCall/Add_18", "PartitionedCall/transpose_50/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_46", "op": "AddV2", "input": ["PartitionedCall/transpose_50", "PartitionedCall/transpose_41"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_52", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_46", "PartitionedCall/transpose_52/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_19", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_52", "PartitionedCall/split_15", "unknown_39"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_53", "op": "Transpose", "input": ["PartitionedCall/Add_19", "PartitionedCall/transpose_53/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_54", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_54_recip", "PartitionedCall/transpose_53"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_49", "op": "AddV2", "input": ["PartitionedCall/transpose_53", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_3/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_49", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_3", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_3/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_55", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_54", "PartitionedCall/clip_by_value_3"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_5", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_55", "PartitionedCall/Const_5"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_55", "op": "Transpose", "input": ["PartitionedCall/Pad_5", "PartitionedCall/transpose_55/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_4", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_55", "PartitionedCall/Reshape_4", "unknown_41"], "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_56", "op": "Transpose", "input": ["PartitionedCall/depthwise_4", "PartitionedCall/transpose_56/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_63", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_63_recip", "PartitionedCall/transpose_56"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_58", "op": "AddV2", "input": ["PartitionedCall/transpose_56", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_4/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_58", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_4", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_4/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_64", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_63", "PartitionedCall/clip_by_value_4"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/Mean_2", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_64", "ConstantFolding/PartitionedCall/split_16-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "PartitionedCall/transpose_58", "op": "Transpose", "input": ["PartitionedCall/Mean_2", "PartitionedCall/transpose_58/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_21", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_58", "PartitionedCall/split_17", "unknown_43"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/transpose_59", "op": "Transpose", "input": ["PartitionedCall/Add_21", "PartitionedCall/transpose_59/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_67", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_59"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_61", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_67", "PartitionedCall/transpose_61/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_22", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_61", "PartitionedCall/split_18", "unknown_45"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "num_host_args": {"i": "0"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}}}, {"name": "PartitionedCall/transpose_62", "op": "Transpose", "input": ["PartitionedCall/Add_22", "PartitionedCall/transpose_62/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_2", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_62", "PartitionedCall/mul_2/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_23", "op": "AddV2", "input": ["PartitionedCall/mul_2", "PartitionedCall/add_23/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_5/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_23", "PartitionedCall/clip_by_value_5/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_5", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_5/Minimum", "PartitionedCall/clip_by_value_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_70", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_5", "PartitionedCall/onnx_tf_prefix_Mul_64"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_64", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_70", "PartitionedCall/transpose_64/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_24", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_64", "PartitionedCall/split_19", "unknown_47"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "num_host_args": {"i": "0"}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/Add_25", "op": "_FusedConv2D", "input": ["PartitionedCall/Add_24", "PartitionedCall/split_20", "unknown_49"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "filter_format": {"s": "SFdJTw=="}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}}}, {"name": "PartitionedCall/transpose_65", "op": "Transpose", "input": ["PartitionedCall/Add_24", "PartitionedCall/transpose_65/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_68", "op": "Transpose", "input": ["PartitionedCall/Add_25", "PartitionedCall/transpose_68/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_79", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_79_recip", "PartitionedCall/transpose_68"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_74", "op": "AddV2", "input": ["PartitionedCall/transpose_68", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_6/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_74", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_6", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_6/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_80", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_79", "PartitionedCall/clip_by_value_6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/Pad_6", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_80", "PartitionedCall/Const_6"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_70", "op": "Transpose", "input": ["PartitionedCall/Pad_6", "PartitionedCall/transpose_70/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_5", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_70", "PartitionedCall/Reshape_5", "unknown_51"], "attr": {"T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/transpose_71", "op": "Transpose", "input": ["PartitionedCall/depthwise_5", "PartitionedCall/transpose_71/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_88", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_88_recip", "PartitionedCall/transpose_71"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_83", "op": "AddV2", "input": ["PartitionedCall/transpose_71", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_7/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_83", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_7", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_7/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_89", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_88", "PartitionedCall/clip_by_value_7"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_3", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_89", "ConstantFolding/PartitionedCall/split_21-folded-1"], "attr": {"keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_73", "op": "Transpose", "input": ["PartitionedCall/Mean_3", "PartitionedCall/transpose_73/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_27", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_73", "PartitionedCall/split_22", "unknown_53"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "filter_format": {"s": "SFdJTw=="}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "leakyrelu_alpha": {"f": 0.2}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/transpose_74", "op": "Transpose", "input": ["PartitionedCall/Add_27", "PartitionedCall/transpose_74/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_92", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_74"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_76", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_92", "PartitionedCall/transpose_76/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_28", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_76", "PartitionedCall/split_23", "unknown_55"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "num_host_args": {"i": "0"}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_77", "op": "Transpose", "input": ["PartitionedCall/Add_28", "PartitionedCall/transpose_77/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_3", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_77", "PartitionedCall/mul_3/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_29", "op": "AddV2", "input": ["PartitionedCall/mul_3", "PartitionedCall/add_29/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_8/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_29", "PartitionedCall/clip_by_value_8/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_8", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_8/Minimum", "PartitionedCall/clip_by_value_8/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_95", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_8", "PartitionedCall/onnx_tf_prefix_Mul_89"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_79", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_95", "PartitionedCall/transpose_79/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_30", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_79", "PartitionedCall/split_24", "unknown_57"], "device": "/device:CPU:0", "attr": {"filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "padding": {"s": "VkFMSUQ="}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}}}, {"name": "PartitionedCall/transpose_80", "op": "Transpose", "input": ["PartitionedCall/Add_30", "PartitionedCall/transpose_80/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_97", "op": "AddV2", "input": ["PartitionedCall/transpose_80", "PartitionedCall/transpose_65"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_82", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_97", "PartitionedCall/transpose_82/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_31", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_82", "PartitionedCall/split_25", "unknown_59"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "padding": {"s": "VkFMSUQ="}, "filter_format": {"s": "SFdJTw=="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_83", "op": "Transpose", "input": ["PartitionedCall/Add_31", "PartitionedCall/transpose_83/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_105", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_105_recip", "PartitionedCall/transpose_83"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_100", "op": "AddV2", "input": ["PartitionedCall/transpose_83", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_9/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_100", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_9", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_9/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_106", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_105", "PartitionedCall/clip_by_value_9"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_7", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_106", "PartitionedCall/Const_7"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_85", "op": "Transpose", "input": ["PartitionedCall/Pad_7", "PartitionedCall/transpose_85/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_6", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_85", "PartitionedCall/Reshape_6", "unknown_61"], "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_86", "op": "Transpose", "input": ["PartitionedCall/depthwise_6", "PartitionedCall/transpose_86/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_114", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_114_recip", "PartitionedCall/transpose_86"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_109", "op": "AddV2", "input": ["PartitionedCall/transpose_86", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_10/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_109", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_10", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_10/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_115", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_114", "PartitionedCall/clip_by_value_10"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/Mean_4", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_115", "ConstantFolding/PartitionedCall/split_26-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "PartitionedCall/transpose_88", "op": "Transpose", "input": ["PartitionedCall/Mean_4", "PartitionedCall/transpose_88/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_33", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_88", "PartitionedCall/split_27", "unknown_63"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "leakyrelu_alpha": {"f": 0.2}, "explicit_paddings": {"list": {}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}}}, {"name": "PartitionedCall/transpose_89", "op": "Transpose", "input": ["PartitionedCall/Add_33", "PartitionedCall/transpose_89/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_118", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_89"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_91", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_118", "PartitionedCall/transpose_91/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_34", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_91", "PartitionedCall/split_28", "unknown_65"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/transpose_92", "op": "Transpose", "input": ["PartitionedCall/Add_34", "PartitionedCall/transpose_92/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_4", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_92", "PartitionedCall/mul_4/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_35", "op": "AddV2", "input": ["PartitionedCall/mul_4", "PartitionedCall/add_35/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_11/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_35", "PartitionedCall/clip_by_value_11/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_11", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_11/Minimum", "PartitionedCall/clip_by_value_11/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_121", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_11", "PartitionedCall/onnx_tf_prefix_Mul_115"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_94", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_121", "PartitionedCall/transpose_94/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_36", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_94", "PartitionedCall/split_29", "unknown_67"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/Add_37", "op": "_FusedConv2D", "input": ["PartitionedCall/Add_36", "PartitionedCall/split_30", "unknown_69"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "epsilon": {"f": 0.0}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "filter_format": {"s": "SFdJTw=="}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_95", "op": "Transpose", "input": ["PartitionedCall/Add_36", "PartitionedCall/transpose_95/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_98", "op": "Transpose", "input": ["PartitionedCall/Add_37", "PartitionedCall/transpose_98/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_130", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_130_recip", "PartitionedCall/transpose_98"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_125", "op": "AddV2", "input": ["PartitionedCall/transpose_98", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_12/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_125", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_12", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_12/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_131", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_130", "PartitionedCall/clip_by_value_12"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_8", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_131", "PartitionedCall/Const_8"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_100", "op": "Transpose", "input": ["PartitionedCall/Pad_8", "PartitionedCall/transpose_100/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_7", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_100", "PartitionedCall/Reshape_7", "unknown_71"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/transpose_101", "op": "Transpose", "input": ["PartitionedCall/depthwise_7", "PartitionedCall/transpose_101/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_139", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_139_recip", "PartitionedCall/transpose_101"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_134", "op": "AddV2", "input": ["PartitionedCall/transpose_101", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_13/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_134", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_13", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_13/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_140", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_139", "PartitionedCall/clip_by_value_13"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/Mean_5", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_140", "ConstantFolding/PartitionedCall/split_31-folded-1"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "PartitionedCall/transpose_103", "op": "Transpose", "input": ["PartitionedCall/Mean_5", "PartitionedCall/transpose_103/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_39", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_103", "PartitionedCall/split_32", "unknown_73"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "filter_format": {"s": "SFdJTw=="}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/transpose_104", "op": "Transpose", "input": ["PartitionedCall/Add_39", "PartitionedCall/transpose_104/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_143", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_104"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_106", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_143", "PartitionedCall/transpose_106/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_40", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_106", "PartitionedCall/split_33", "unknown_75"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "leakyrelu_alpha": {"f": 0.2}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}}}, {"name": "PartitionedCall/transpose_107", "op": "Transpose", "input": ["PartitionedCall/Add_40", "PartitionedCall/transpose_107/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/mul_5", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_107", "PartitionedCall/mul_5/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_41", "op": "AddV2", "input": ["PartitionedCall/mul_5", "PartitionedCall/add_41/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_14/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_41", "PartitionedCall/clip_by_value_14/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_14", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_14/Minimum", "PartitionedCall/clip_by_value_14/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_146", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_14", "PartitionedCall/onnx_tf_prefix_Mul_140"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_109", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_146", "PartitionedCall/transpose_109/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_42", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_109", "PartitionedCall/split_34", "unknown_77"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}}}, {"name": "PartitionedCall/transpose_110", "op": "Transpose", "input": ["PartitionedCall/Add_42", "PartitionedCall/transpose_110/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_148", "op": "AddV2", "input": ["PartitionedCall/transpose_110", "PartitionedCall/transpose_95"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_112", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_148", "PartitionedCall/transpose_112/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_43", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_112", "PartitionedCall/split_35", "unknown_79"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "filter_format": {"s": "SFdJTw=="}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}}}, {"name": "PartitionedCall/transpose_113", "op": "Transpose", "input": ["PartitionedCall/Add_43", "PartitionedCall/transpose_113/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_156", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_156_recip", "PartitionedCall/transpose_113"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_151", "op": "AddV2", "input": ["PartitionedCall/transpose_113", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_15/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_151", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_15", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_15/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_157", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_156", "PartitionedCall/clip_by_value_15"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_9", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_157", "PartitionedCall/Const_9"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_115", "op": "Transpose", "input": ["PartitionedCall/Pad_9", "PartitionedCall/transpose_115/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_8", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_115", "PartitionedCall/Reshape_8", "unknown_81"], "attr": {"data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_116", "op": "Transpose", "input": ["PartitionedCall/depthwise_8", "PartitionedCall/transpose_116/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_165", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_165_recip", "PartitionedCall/transpose_116"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_160", "op": "AddV2", "input": ["PartitionedCall/transpose_116", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_16/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_160", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_16", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_16/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_166", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_165", "PartitionedCall/clip_by_value_16"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_6", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_166", "ConstantFolding/PartitionedCall/split_36-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "PartitionedCall/transpose_118", "op": "Transpose", "input": ["PartitionedCall/Mean_6", "PartitionedCall/transpose_118/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_45", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_118", "PartitionedCall/split_37", "unknown_83"], "device": "/device:CPU:0", "attr": {"TArgs": {"list": {"type": ["DT_FLOAT"]}}, "leakyrelu_alpha": {"f": 0.2}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_119", "op": "Transpose", "input": ["PartitionedCall/Add_45", "PartitionedCall/transpose_119/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_169", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_119"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_121", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_169", "PartitionedCall/transpose_121/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_46", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_121", "PartitionedCall/split_38", "unknown_85"], "device": "/device:CPU:0", "attr": {"filter_format": {"s": "SFdJTw=="}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_122", "op": "Transpose", "input": ["PartitionedCall/Add_46", "PartitionedCall/transpose_122/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_6", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_122", "PartitionedCall/mul_6/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_47", "op": "AddV2", "input": ["PartitionedCall/mul_6", "PartitionedCall/add_47/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_17/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_47", "PartitionedCall/clip_by_value_17/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_17", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_17/Minimum", "PartitionedCall/clip_by_value_17/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_172", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_17", "PartitionedCall/onnx_tf_prefix_Mul_166"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_124", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_172", "PartitionedCall/transpose_124/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_48", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_124", "PartitionedCall/split_39", "unknown_87"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "filter_format": {"s": "SFdJTw=="}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/Add_49", "op": "_FusedConv2D", "input": ["PartitionedCall/Add_48", "PartitionedCall/split_40", "unknown_89"], "device": "/device:CPU:0", "attr": {"leakyrelu_alpha": {"f": 0.2}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}}}, {"name": "PartitionedCall/transpose_125", "op": "Transpose", "input": ["PartitionedCall/Add_48", "PartitionedCall/transpose_125/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_128", "op": "Transpose", "input": ["PartitionedCall/Add_49", "PartitionedCall/transpose_128/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_181", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_181_recip", "PartitionedCall/transpose_128"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_176", "op": "AddV2", "input": ["PartitionedCall/transpose_128", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_18/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_176", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_18", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_18/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_182", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_181", "PartitionedCall/clip_by_value_18"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_10", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Mul_182", "PartitionedCall/Const_10"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_130", "op": "Transpose", "input": ["PartitionedCall/Pad_10", "PartitionedCall/transpose_130/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_9", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_130", "PartitionedCall/Reshape_9", "unknown_91"], "attr": {"explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/transpose_131", "op": "Transpose", "input": ["PartitionedCall/depthwise_9", "PartitionedCall/transpose_131/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Div_190", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_190_recip", "PartitionedCall/transpose_131"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_185", "op": "AddV2", "input": ["PartitionedCall/transpose_131", "unknown_3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_19/Minimum", "op": "Minimum", "input": ["PartitionedCall/onnx_tf_prefix_Add_185", "unknown_4"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_19", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_19/Minimum", "unknown_5"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_191", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Div_190", "PartitionedCall/clip_by_value_19"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_7", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Mul_191", "ConstantFolding/PartitionedCall/split_41-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "PartitionedCall/transpose_133", "op": "Transpose", "input": ["PartitionedCall/Mean_7", "PartitionedCall/transpose_133/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_51", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_133", "PartitionedCall/split_42", "unknown_93"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_134", "op": "Transpose", "input": ["PartitionedCall/Add_51", "PartitionedCall/transpose_134/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_194", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_134"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_136", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_194", "PartitionedCall/transpose_136/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_52", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_136", "PartitionedCall/split_43", "unknown_95"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "use_cudnn_on_gpu": {"b": true}, "filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}}}, {"name": "PartitionedCall/transpose_137", "op": "Transpose", "input": ["PartitionedCall/Add_52", "PartitionedCall/transpose_137/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/mul_7", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_137", "PartitionedCall/mul_7/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_53", "op": "AddV2", "input": ["PartitionedCall/mul_7", "PartitionedCall/add_53/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_20/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_53", "PartitionedCall/clip_by_value_20/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_20", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_20/Minimum", "PartitionedCall/clip_by_value_20/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_197", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_20", "PartitionedCall/onnx_tf_prefix_Mul_191"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_139", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_197", "PartitionedCall/transpose_139/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_54", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_139", "PartitionedCall/split_44", "unknown_97"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "filter_format": {"s": "SFdJTw=="}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "data_format": {"s": "TkhXQw=="}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_140", "op": "Transpose", "input": ["PartitionedCall/Add_54", "PartitionedCall/transpose_140/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_199", "op": "AddV2", "input": ["PartitionedCall/transpose_140", "PartitionedCall/transpose_125"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_142", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_199", "PartitionedCall/transpose_142/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Mean_8", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Add_199", "ConstantFolding/PartitionedCall/split_46-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_55", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_142", "PartitionedCall/split_45", "unknown_99"], "device": "/device:CPU:0", "attr": {"leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "filter_format": {"s": "SFdJTw=="}, "num_args": {"i": "1"}, "num_host_args": {"i": "0"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}}}, {"name": "PartitionedCall/transpose_145", "op": "Transpose", "input": ["PartitionedCall/Mean_8", "PartitionedCall/transpose_145/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_143", "op": "Transpose", "input": ["PartitionedCall/Add_55", "PartitionedCall/transpose_143/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/convolution_38", "op": "Conv2D", "input": ["PartitionedCall/transpose_145", "PartitionedCall/split_47"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_201", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_143"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_146", "op": "Transpose", "input": ["PartitionedCall/convolution_38", "PartitionedCall/transpose_146/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Sigmoid_204", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/transpose_146"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_205", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/onnx_tf_prefix_Relu_201", "PartitionedCall/onnx_tf_prefix_Sigmoid_204"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_147", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Mul_205", "PartitionedCall/transpose_147/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["PartitionedCall/transpose_147", "PartitionedCall/Cast_1"], "attr": {"half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}, "align_corners": {"b": false}}}, {"name": "PartitionedCall/transpose_148", "op": "Transpose", "input": ["PartitionedCall/resize/ResizeBilinear", "PartitionedCall/transpose_148/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/StridedSlice", "op": "StridedSlice", "input": ["PartitionedCall/transpose_148", "PartitionedCall/SparseToDense", "PartitionedCall/SelectV2_3", "PartitionedCall/SparseToDense_2"], "attr": {"T": {"type": "DT_FLOAT"}, "begin_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/transpose_150", "op": "Transpose", "input": ["PartitionedCall/StridedSlice", "PartitionedCall/transpose_150/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_59", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_150", "PartitionedCall/split_48", "unknown_107"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/transpose_151", "op": "Transpose", "input": ["PartitionedCall/Add_59", "PartitionedCall/transpose_151/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_221", "op": "AddV2", "input": ["PartitionedCall/transpose_151", "PartitionedCall/onnx_tf_prefix_Add_46"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_9", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Add_221", "ConstantFolding/PartitionedCall/split_49-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_153", "op": "Transpose", "input": ["PartitionedCall/Mean_9", "PartitionedCall/transpose_153/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_60", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_153", "PartitionedCall/split_50", "unknown_109"], "device": "/device:CPU:0", "attr": {"TArgs": {"list": {"type": ["DT_FLOAT"]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "num_host_args": {"i": "0"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "PartitionedCall/transpose_154", "op": "Transpose", "input": ["PartitionedCall/Add_60", "PartitionedCall/transpose_154/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_224", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_154"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_156", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_224", "PartitionedCall/transpose_156/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_61", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_156", "PartitionedCall/split_51", "unknown_111"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "leakyrelu_alpha": {"f": 0.2}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "num_host_args": {"i": "0"}}}, {"name": "PartitionedCall/transpose_157", "op": "Transpose", "input": ["PartitionedCall/Add_61", "PartitionedCall/transpose_157/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_9", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_157", "PartitionedCall/mul_9/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_62", "op": "AddV2", "input": ["PartitionedCall/mul_9", "PartitionedCall/add_62/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_21/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_62", "PartitionedCall/clip_by_value_21/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_21", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_21/Minimum", "PartitionedCall/clip_by_value_21/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_227", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_21", "PartitionedCall/onnx_tf_prefix_Add_46"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_228", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Mul_227", "PartitionedCall/transpose_151"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_159", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_228", "PartitionedCall/transpose_159/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_63", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_159", "PartitionedCall/split_52", "unknown_113"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_160", "op": "Transpose", "input": ["PartitionedCall/Add_63", "PartitionedCall/transpose_160/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_230", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_160"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_11", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_230", "PartitionedCall/Const_15"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_162", "op": "Transpose", "input": ["PartitionedCall/Pad_11", "PartitionedCall/transpose_162/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_10", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_162", "PartitionedCall/Reshape_11", "unknown_115"], "attr": {"num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_163", "op": "Transpose", "input": ["PartitionedCall/depthwise_10", "PartitionedCall/transpose_163/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_232", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_163"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_233", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Relu_230", "PartitionedCall/onnx_tf_prefix_Relu_232"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_164", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_233", "PartitionedCall/transpose_164/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/resize_1/ResizeBilinear", "op": "ResizeBilinear", "input": ["PartitionedCall/transpose_164", "PartitionedCall/Cast_5"], "attr": {"align_corners": {"b": false}, "half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_165", "op": "Transpose", "input": ["PartitionedCall/resize_1/ResizeBilinear", "PartitionedCall/transpose_165/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/StridedSlice_1", "op": "StridedSlice", "input": ["PartitionedCall/transpose_165", "PartitionedCall/SparseToDense_3", "PartitionedCall/SelectV2_7", "PartitionedCall/SparseToDense_5"], "attr": {"shrink_axis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "end_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "begin_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}}}, {"name": "PartitionedCall/transpose_167", "op": "Transpose", "input": ["PartitionedCall/StridedSlice_1", "PartitionedCall/transpose_167/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_68", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_167", "PartitionedCall/split_53", "unknown_118"], "device": "/device:CPU:0", "attr": {"filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "epsilon": {"f": 0.0}, "leakyrelu_alpha": {"f": 0.2}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/transpose_168", "op": "Transpose", "input": ["PartitionedCall/Add_68", "PartitionedCall/transpose_168/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_249", "op": "AddV2", "input": ["PartitionedCall/transpose_168", "PartitionedCall/onnx_tf_prefix_Add_35"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_10", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Add_249", "ConstantFolding/PartitionedCall/split_54-folded-1"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_170", "op": "Transpose", "input": ["PartitionedCall/Mean_10", "PartitionedCall/transpose_170/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_69", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_170", "PartitionedCall/split_55", "unknown_120"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "leakyrelu_alpha": {"f": 0.2}, "filter_format": {"s": "SFdJTw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}}}, {"name": "PartitionedCall/transpose_171", "op": "Transpose", "input": ["PartitionedCall/Add_69", "PartitionedCall/transpose_171/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_252", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_171"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_173", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_252", "PartitionedCall/transpose_173/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_70", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_173", "PartitionedCall/split_56", "unknown_122"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "filter_format": {"s": "SFdJTw=="}}}, {"name": "PartitionedCall/transpose_174", "op": "Transpose", "input": ["PartitionedCall/Add_70", "PartitionedCall/transpose_174/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/mul_11", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_174", "PartitionedCall/mul_11/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_71", "op": "AddV2", "input": ["PartitionedCall/mul_11", "PartitionedCall/add_71/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_22/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_71", "PartitionedCall/clip_by_value_22/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_22", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_22/Minimum", "PartitionedCall/clip_by_value_22/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_255", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_22", "PartitionedCall/onnx_tf_prefix_Add_35"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_256", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Mul_255", "PartitionedCall/transpose_168"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_176", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_256", "PartitionedCall/transpose_176/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_72", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_176", "PartitionedCall/split_57", "unknown_124"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}}}, {"name": "PartitionedCall/transpose_177", "op": "Transpose", "input": ["PartitionedCall/Add_72", "PartitionedCall/transpose_177/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_258", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_177"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_12", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_258", "PartitionedCall/Const_20"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_179", "op": "Transpose", "input": ["PartitionedCall/Pad_12", "PartitionedCall/transpose_179/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_11", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_179", "PartitionedCall/Reshape_13", "unknown_126"], "attr": {"padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_180", "op": "Transpose", "input": ["PartitionedCall/depthwise_11", "PartitionedCall/transpose_180/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_260", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_180"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_261", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Relu_258", "PartitionedCall/onnx_tf_prefix_Relu_260"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_181", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_261", "PartitionedCall/transpose_181/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/resize_2/ResizeBilinear", "op": "ResizeBilinear", "input": ["PartitionedCall/transpose_181", "PartitionedCall/Cast_9"], "attr": {"half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}, "align_corners": {"b": false}}}, {"name": "PartitionedCall/transpose_182", "op": "Transpose", "input": ["PartitionedCall/resize_2/ResizeBilinear", "PartitionedCall/transpose_182/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/StridedSlice_2", "op": "StridedSlice", "input": ["PartitionedCall/transpose_182", "PartitionedCall/SparseToDense_6", "PartitionedCall/SelectV2_11", "PartitionedCall/SparseToDense_8"], "attr": {"ellipsis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "Index": {"type": "DT_INT64"}, "new_axis_mask": {"i": "0"}, "begin_mask": {"i": "0"}}}, {"name": "PartitionedCall/transpose_184", "op": "Transpose", "input": ["PartitionedCall/StridedSlice_2", "PartitionedCall/transpose_184/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_77", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_184", "PartitionedCall/split_58", "unknown_129"], "device": "/device:CPU:0", "attr": {"filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "leakyrelu_alpha": {"f": 0.2}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "PartitionedCall/transpose_185", "op": "Transpose", "input": ["PartitionedCall/Add_77", "PartitionedCall/transpose_185/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_277", "op": "AddV2", "input": ["PartitionedCall/transpose_185", "PartitionedCall/onnx_tf_prefix_Mul_12"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Mean_11", "op": "Mean", "input": ["PartitionedCall/onnx_tf_prefix_Add_277", "ConstantFolding/PartitionedCall/split_59-folded-1"], "attr": {"Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_187", "op": "Transpose", "input": ["PartitionedCall/Mean_11", "PartitionedCall/transpose_187/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_78", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_187", "PartitionedCall/split_60", "unknown_131"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "leakyrelu_alpha": {"f": 0.2}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}}}, {"name": "PartitionedCall/transpose_188", "op": "Transpose", "input": ["PartitionedCall/Add_78", "PartitionedCall/transpose_188/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_280", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_188"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_190", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Relu_280", "PartitionedCall/transpose_190/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_79", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_190", "PartitionedCall/split_61", "unknown_133"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "num_host_args": {"i": "0"}, "leakyrelu_alpha": {"f": 0.2}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_191", "op": "Transpose", "input": ["PartitionedCall/Add_79", "PartitionedCall/transpose_191/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/mul_13", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_191", "PartitionedCall/mul_13/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/add_80", "op": "AddV2", "input": ["PartitionedCall/mul_13", "PartitionedCall/add_80/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_23/Minimum", "op": "Minimum", "input": ["PartitionedCall/add_80", "PartitionedCall/clip_by_value_23/Minimum/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/clip_by_value_23", "op": "Maximum", "input": ["PartitionedCall/clip_by_value_23/Minimum", "PartitionedCall/clip_by_value_23/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Mul_283", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/clip_by_value_23", "PartitionedCall/onnx_tf_prefix_Mul_12"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_284", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Mul_283", "PartitionedCall/transpose_185"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_193", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_284", "PartitionedCall/transpose_193/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/Add_81", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_193", "PartitionedCall/split_62", "unknown_135"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "filter_format": {"s": "SFdJTw=="}, "data_format": {"s": "TkhXQw=="}, "leakyrelu_alpha": {"f": 0.2}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "PartitionedCall/transpose_194", "op": "Transpose", "input": ["PartitionedCall/Add_81", "PartitionedCall/transpose_194/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_286", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_194"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_13", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_286", "PartitionedCall/Const_25"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_196", "op": "Transpose", "input": ["PartitionedCall/Pad_13", "PartitionedCall/transpose_196/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_12", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_196", "PartitionedCall/Reshape_15", "unknown_137"], "attr": {"padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_197", "op": "Transpose", "input": ["PartitionedCall/depthwise_12", "PartitionedCall/transpose_197/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_288", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_197"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Add_289", "op": "AddV2", "input": ["PartitionedCall/onnx_tf_prefix_Relu_286", "PartitionedCall/onnx_tf_prefix_Relu_288"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_198", "op": "Transpose", "input": ["PartitionedCall/onnx_tf_prefix_Add_289", "PartitionedCall/transpose_198/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/resize_3/ResizeBilinear", "op": "ResizeBilinear", "input": ["PartitionedCall/transpose_198", "PartitionedCall/Cast_13"], "attr": {"T": {"type": "DT_FLOAT"}, "half_pixel_centers": {"b": true}, "align_corners": {"b": false}}}, {"name": "PartitionedCall/transpose_199", "op": "Transpose", "input": ["PartitionedCall/resize_3/ResizeBilinear", "PartitionedCall/transpose_199/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/StridedSlice_3", "op": "StridedSlice", "input": ["PartitionedCall/transpose_199", "PartitionedCall/SparseToDense_9", "PartitionedCall/SelectV2_15", "PartitionedCall/SparseToDense_11"], "attr": {"T": {"type": "DT_FLOAT"}, "begin_mask": {"i": "0"}, "ellipsis_mask": {"i": "0"}, "new_axis_mask": {"i": "0"}, "Index": {"type": "DT_INT64"}, "end_mask": {"i": "0"}, "shrink_axis_mask": {"i": "0"}}}, {"name": "PartitionedCall/Pad_14", "op": "Pad", "input": ["PartitionedCall/StridedSlice_3", "PartitionedCall/Const_30"], "attr": {"T": {"type": "DT_FLOAT"}, "Tpaddings": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/transpose_201", "op": "Transpose", "input": ["PartitionedCall/Pad_14", "PartitionedCall/transpose_201/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Add_86", "op": "_FusedConv2D", "input": ["PartitionedCall/transpose_201", "PartitionedCall/split_63", "unknown_140"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "filter_format": {"s": "SFdJTw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/transpose_202", "op": "Transpose", "input": ["PartitionedCall/Add_86", "PartitionedCall/transpose_202/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_305", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_202"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_15", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_305", "PartitionedCall/Const_31"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_204", "op": "Transpose", "input": ["PartitionedCall/Pad_15", "PartitionedCall/transpose_204/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/depthwise_13", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_204", "PartitionedCall/Reshape_17", "unknown_142"], "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "PartitionedCall/transpose_205", "op": "Transpose", "input": ["PartitionedCall/depthwise_13", "PartitionedCall/transpose_205/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Relu_307", "op": "<PERSON><PERSON>", "input": ["PartitionedCall/transpose_205"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/Pad_16", "op": "Pad", "input": ["PartitionedCall/onnx_tf_prefix_Relu_307", "PartitionedCall/Const_32"], "attr": {"Tpaddings": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/transpose_207", "op": "Transpose", "input": ["PartitionedCall/Pad_16", "PartitionedCall/transpose_207/perm"], "attr": {"Tperm": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "PartitionedCall/depthwise_14", "op": "FusedDepthwiseConv2dNative", "input": ["PartitionedCall/transpose_207", "PartitionedCall/Reshape_18", "unknown_144"], "attr": {"num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "PartitionedCall/Add_89", "op": "_FusedConv2D", "input": ["PartitionedCall/depthwise_14", "PartitionedCall/split_64", "unknown_146"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "filter_format": {"s": "SFdJTw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "leakyrelu_alpha": {"f": 0.2}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "PartitionedCall/transpose_211", "op": "Transpose", "input": ["PartitionedCall/Add_89", "PartitionedCall/transpose_211/perm"], "attr": {"T": {"type": "DT_FLOAT"}, "Tperm": {"type": "DT_INT32"}}}, {"name": "PartitionedCall/onnx_tf_prefix_Sigmoid_310", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["PartitionedCall/transpose_211"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["PartitionedCall/onnx_tf_prefix_Sigmoid_310"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1482}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "ConstantFolding/PartitionedCall/split_59-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_187/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_60", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_131", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_188/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_190/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_61", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_133", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_191/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_13/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_80/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_23/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_23/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/split_54-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_170/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_55", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_120", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_171/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_173/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_56", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_122", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_174/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_11/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_71/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_22/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_22/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/split_49-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_153/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_50", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_109", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_154/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_156/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_51", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_111", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_157/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_9/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_62/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_21/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_21/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/transpose_142/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_45", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "unknown_99", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_143/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_41-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_133/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_42", "shape": [1, 1, 96, 24], "dtype": "float32"}, {"name": "unknown_93", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_134/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_136/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_43", "shape": [1, 1, 24, 96], "dtype": "float32"}, {"name": "unknown_95", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_137/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_7/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_53/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_20/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_20/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_190_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_181_recip", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/split_40", "shape": [1, 1, 32, 96], "dtype": "float32"}, {"name": "unknown_89", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_128/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_10", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_130/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_9", "shape": [5, 5, 96, 1], "dtype": "float32"}, {"name": "unknown_91", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_131/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_139/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_44", "shape": [1, 1, 96, 32], "dtype": "float32"}, {"name": "unknown_97", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_140/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_36-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_118/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_37", "shape": [1, 1, 96, 24], "dtype": "float32"}, {"name": "unknown_83", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_119/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_121/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_38", "shape": [1, 1, 24, 96], "dtype": "float32"}, {"name": "unknown_85", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_122/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_6/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_47/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_17/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_17/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_165_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_156_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/split_31-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_103/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_32", "shape": [1, 1, 128, 32], "dtype": "float32"}, {"name": "unknown_73", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_104/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_106/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_33", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "unknown_75", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_107/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_5/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_41/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_14/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_14/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_139_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_130_recip", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/split_30", "shape": [1, 1, 48, 128], "dtype": "float32"}, {"name": "unknown_69", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_98/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_8", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_100/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_7", "shape": [5, 5, 128, 1], "dtype": "float32"}, {"name": "unknown_71", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_101/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_109/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_34", "shape": [1, 1, 128, 48], "dtype": "float32"}, {"name": "unknown_77", "shape": [48], "dtype": "float32"}, {"name": "PartitionedCall/transpose_110/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_26-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_88/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_27", "shape": [1, 1, 192, 48], "dtype": "float32"}, {"name": "unknown_63", "shape": [48], "dtype": "float32"}, {"name": "PartitionedCall/transpose_89/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_91/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_28", "shape": [1, 1, 48, 192], "dtype": "float32"}, {"name": "unknown_65", "shape": [192], "dtype": "float32"}, {"name": "PartitionedCall/transpose_92/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_4/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_35/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_11/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_11/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_114_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_105_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/split_21-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_73/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_22", "shape": [1, 1, 128, 32], "dtype": "float32"}, {"name": "unknown_53", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_74/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_76/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_23", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "unknown_55", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_77/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_3/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_29/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_8/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_8/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_88_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_79_recip", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/split_20", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "unknown_49", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_68/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_6", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_70/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_5", "shape": [5, 5, 128, 1], "dtype": "float32"}, {"name": "unknown_51", "shape": [128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_71/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_79/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_24", "shape": [1, 1, 128, 32], "dtype": "float32"}, {"name": "unknown_57", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_80/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_16-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_58/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_17", "shape": [1, 1, 96, 24], "dtype": "float32"}, {"name": "unknown_43", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_59/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_61/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_18", "shape": [1, 1, 24, 96], "dtype": "float32"}, {"name": "unknown_45", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_62/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_2/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_23/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_5/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_5/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_63_recip", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_54_recip", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/split_13", "shape": [1, 1, 24, 88], "dtype": "float32"}, {"name": "unknown_33", "shape": [88], "dtype": "float32"}, {"name": "PartitionedCall/transpose_44/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_4", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_46/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_3", "shape": [3, 3, 88, 1], "dtype": "float32"}, {"name": "unknown_35", "shape": [88], "dtype": "float32"}, {"name": "PartitionedCall/transpose_47/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_49/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_14", "shape": [1, 1, 88, 24], "dtype": "float32"}, {"name": "unknown_37", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_50/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_7-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_25/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_8", "shape": [1, 1, 24, 8], "dtype": "float32"}, {"name": "unknown_21", "shape": [8], "dtype": "float32"}, {"name": "PartitionedCall/transpose_26/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_28/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_9", "shape": [1, 1, 8, 24], "dtype": "float32"}, {"name": "unknown_23", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_29/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul_1/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_11/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_2/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_2/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/split_6", "shape": [1, 1, 16, 24], "dtype": "float32"}, {"name": "unknown_17", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_20/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_2", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_22/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_1", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_19", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_23/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_31/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_10", "shape": [1, 1, 24, 16], "dtype": "float32"}, {"name": "unknown_25", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_32/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_2-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_10/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_3", "shape": [1, 1, 16, 8], "dtype": "float32"}, {"name": "unknown_11", "shape": [8], "dtype": "float32"}, {"name": "PartitionedCall/transpose_11/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_13/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_4", "shape": [1, 1, 8, 16], "dtype": "float32"}, {"name": "unknown_13", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_14/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/mul/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/add_5/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_1/Minimum/y", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/clip_by_value_1/y", "shape": [], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_11_recip", "shape": [], "dtype": "float32"}, {"name": "unknown", "shape": [3, 1, 1], "dtype": "float32"}, {"name": "ConstantFolding/PartitionedCall/onnx_tf_prefix_Div_3_recip", "shape": [3, 1, 1], "dtype": "float32"}, {"name": "PartitionedCall/Const", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_1/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split", "shape": [3, 3, 3, 16], "dtype": "float32"}, {"name": "unknown_2", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_2/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_4/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_1", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_7", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_5/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_1", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_7/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape", "shape": [3, 3, 16, 1], "dtype": "float32"}, {"name": "unknown_9", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_8/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_16/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_5", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_15", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_17/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_34/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_11", "shape": [1, 1, 16, 72], "dtype": "float32"}, {"name": "unknown_27", "shape": [72], "dtype": "float32"}, {"name": "PartitionedCall/transpose_35/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_3", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_37/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_2", "shape": [3, 3, 72, 1], "dtype": "float32"}, {"name": "unknown_29", "shape": [72], "dtype": "float32"}, {"name": "PartitionedCall/transpose_38/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_40/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_12", "shape": [1, 1, 72, 24], "dtype": "float32"}, {"name": "unknown_31", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_41/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_52/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_15", "shape": [1, 1, 24, 96], "dtype": "float32"}, {"name": "unknown_39", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_53/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_5", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_55/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_4", "shape": [5, 5, 96, 1], "dtype": "float32"}, {"name": "unknown_41", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_56/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_64/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_19", "shape": [1, 1, 96, 32], "dtype": "float32"}, {"name": "unknown_47", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_65/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_82/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_25", "shape": [1, 1, 32, 192], "dtype": "float32"}, {"name": "unknown_59", "shape": [192], "dtype": "float32"}, {"name": "PartitionedCall/transpose_83/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_7", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_85/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_6", "shape": [5, 5, 192, 1], "dtype": "float32"}, {"name": "unknown_61", "shape": [192], "dtype": "float32"}, {"name": "PartitionedCall/transpose_86/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_94/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_29", "shape": [1, 1, 192, 48], "dtype": "float32"}, {"name": "unknown_67", "shape": [48], "dtype": "float32"}, {"name": "PartitionedCall/transpose_95/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_112/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_35", "shape": [1, 1, 48, 96], "dtype": "float32"}, {"name": "unknown_79", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_113/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_9", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_115/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_8", "shape": [5, 5, 96, 1], "dtype": "float32"}, {"name": "unknown_81", "shape": [96], "dtype": "float32"}, {"name": "PartitionedCall/transpose_116/perm", "shape": [4], "dtype": "int32"}, {"name": "unknown_3", "shape": [], "dtype": "float32"}, {"name": "unknown_4", "shape": [], "dtype": "float32"}, {"name": "unknown_5", "shape": [], "dtype": "float32"}, {"name": "PartitionedCall/transpose_124/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_39", "shape": [1, 1, 96, 32], "dtype": "float32"}, {"name": "unknown_87", "shape": [32], "dtype": "float32"}, {"name": "PartitionedCall/transpose_125/perm", "shape": [4], "dtype": "int32"}, {"name": "ConstantFolding/PartitionedCall/split_46-folded-1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_145/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_47", "shape": [1, 1, 32, 128], "dtype": "float32"}, {"name": "PartitionedCall/transpose_146/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_147/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Cast_1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_148/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_1", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/zeros", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Const_13", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Shape", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_2", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/Const_12", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/ExpandDims", "shape": [2, 1], "dtype": "int32"}, {"name": "PartitionedCall/Cast_3", "shape": [1], "dtype": "int32"}, {"name": "PartitionedCall/Const_14", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/transpose_150/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_48", "shape": [1, 1, 128, 24], "dtype": "float32"}, {"name": "unknown_107", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_151/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_159/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_52", "shape": [1, 1, 24, 24], "dtype": "float32"}, {"name": "unknown_113", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_160/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_15", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_162/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_11", "shape": [3, 3, 24, 1], "dtype": "float32"}, {"name": "unknown_115", "shape": [24], "dtype": "float32"}, {"name": "PartitionedCall/transpose_163/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_164/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Cast_5", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_165/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_5", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/zeros_1", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Const_18", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Shape_1", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_6", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/Const_17", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/ExpandDims_1", "shape": [2, 1], "dtype": "int32"}, {"name": "PartitionedCall/Cast_7", "shape": [1], "dtype": "int32"}, {"name": "PartitionedCall/Const_19", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/transpose_167/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_53", "shape": [1, 1, 24, 16], "dtype": "float32"}, {"name": "unknown_118", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_168/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_176/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_57", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_124", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_177/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_20", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_179/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_13", "shape": [3, 3, 16, 1], "dtype": "float32"}, {"name": "unknown_126", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_180/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_181/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Cast_9", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_182/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_9", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/zeros_2", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Const_23", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Shape_2", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_10", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/Const_22", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/ExpandDims_2", "shape": [2, 1], "dtype": "int32"}, {"name": "PartitionedCall/Cast_11", "shape": [1], "dtype": "int32"}, {"name": "PartitionedCall/Const_24", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/transpose_184/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_58", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_129", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_185/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_193/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_62", "shape": [1, 1, 16, 16], "dtype": "float32"}, {"name": "unknown_135", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_194/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_25", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_196/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_15", "shape": [3, 3, 16, 1], "dtype": "float32"}, {"name": "unknown_137", "shape": [16], "dtype": "float32"}, {"name": "PartitionedCall/transpose_197/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/transpose_198/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Cast_13", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_199/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_13", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/zeros_3", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Const_28", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Shape_3", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/SelectV2_14", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/Const_27", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/ExpandDims_3", "shape": [2, 1], "dtype": "int32"}, {"name": "PartitionedCall/Cast_15", "shape": [1], "dtype": "int32"}, {"name": "unknown_105", "shape": [2], "dtype": "int32"}, {"name": "PartitionedCall/Const_29", "shape": [], "dtype": "int32"}, {"name": "PartitionedCall/Const_30", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_201/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/split_63", "shape": [3, 3, 16, 4], "dtype": "float32"}, {"name": "unknown_140", "shape": [4], "dtype": "float32"}, {"name": "PartitionedCall/transpose_202/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_31", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_204/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_17", "shape": [3, 3, 4, 1], "dtype": "float32"}, {"name": "unknown_142", "shape": [4], "dtype": "float32"}, {"name": "PartitionedCall/transpose_205/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Const_32", "shape": [4, 2], "dtype": "int32"}, {"name": "PartitionedCall/transpose_207/perm", "shape": [4], "dtype": "int32"}, {"name": "PartitionedCall/Reshape_18", "shape": [3, 3, 4, 1], "dtype": "float32"}, {"name": "unknown_144", "shape": [4], "dtype": "float32"}, {"name": "PartitionedCall/split_64", "shape": [1, 1, 4, 1], "dtype": "float32"}, {"name": "unknown_146", "shape": [1], "dtype": "float32"}, {"name": "PartitionedCall/transpose_211/perm", "shape": [4], "dtype": "int32"}]}]}