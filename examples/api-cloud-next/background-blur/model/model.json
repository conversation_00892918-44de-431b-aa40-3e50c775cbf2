{"format": "graph-model", "generatedBy": "2.7.0", "convertedBy": "TensorFlow.js Converter v3.7.0", "signature": {"inputs": {"input_1": {"name": "input_1:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "144"}, {"size": "256"}, {"size": "3"}]}}}, "outputs": {"tf.concat": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "144"}, {"size": "256"}, {"size": "2"}]}}}}, "modelTopology": {"node": [{"name": "unknown_208", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_2", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/1", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/2", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/3", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/conv2d_transpose/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "2"}, {"size": "2"}, {"size": "1"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_9/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_40/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_41/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_8/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_36/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_37/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_7/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_32/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_33/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_10/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_9/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_9/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_10/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_5/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_26/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_27/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_8/truediv_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_7/truediv_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_7/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_8/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_4/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_22/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_23/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_6/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_5/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_5/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_6/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_3/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_18/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_19/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_4/truediv_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_3/truediv_recip", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_3/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_4/add/y", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_2/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_14/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_15/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_2/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_1/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish/truediv_recip", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "8"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "8"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "8"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_3/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_1/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/h_swish_2/add/y", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_1/Mean/reduction_indices", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_10/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_11/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_6/Mean/reduction_indices", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d/mul", "op": "Const", "attr": {"dtype": {"type": "DT_INT32"}, "value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_31/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_1/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_35/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_2/mul", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_39/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/tf.concat/concat/axis", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "input_1", "op": "Placeholder", "attr": {"shape": {"shape": {"dim": [{"size": "-1"}, {"size": "144"}, {"size": "256"}, {"size": "3"}]}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "72"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "72"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "72"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "72"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "88"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "88"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "88"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "88"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "128"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "128"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "128"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "96"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "5"}, {"size": "5"}, {"size": "96"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "96"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "96"}, {"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "16"}, {"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "32"}, {"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "16"}, {"size": "1"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "1"}, {"size": "1"}, {"size": "24"}, {"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_weights", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "24"}, {"size": "1"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_bn_offset", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "24"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["input_1", "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/h_swish/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish/truediv_recip", "StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish/mul", "StatefulPartitionedCall/model/h_swish/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/re_lu/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/h_swish/truediv", "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu/Relu", "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_bn_offset"], "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/depthwise_conv2d/depthwise", "StatefulPartitionedCall/model/global_average_pooling2d/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/model/re_lu_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d/Mean", "StatefulPartitionedCall/model/conv2d_2/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/conv2d_3/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_2/Relu", "StatefulPartitionedCall/model/conv2d_3/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_3/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/activation/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_3/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/depthwise_conv2d/depthwise", "StatefulPartitionedCall/model/activation/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply/mul", "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/re_lu_3/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu_3/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "2", "2", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_6/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise", "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/re_lu_5/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/batch_normalization_6/FusedBatchNormV3", "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu_5/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_bn_offset"], "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_9/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise", "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/add/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_9/FusedBatchNormV3", "StatefulPartitionedCall/model/batch_normalization_6/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_10/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add/add", "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/h_swish_1/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_1/truediv_recip", "StatefulPartitionedCall/model/batch_normalization_10/FusedBatchNormV3"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_10/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish_1/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_1/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_1/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_1/mul", "StatefulPartitionedCall/model/h_swish_1/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/h_swish_1/truediv", "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_bn_offset"], "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/h_swish_2/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_2/truediv_recip", "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise", "StatefulPartitionedCall/model/h_swish_2/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_2/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_2/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_2/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_2/mul", "StatefulPartitionedCall/model/h_swish_2/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_1/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/h_swish_2/truediv", "StatefulPartitionedCall/model/global_average_pooling2d_1/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_7/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_1/Mean", "StatefulPartitionedCall/model/conv2d_10/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_10/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_11/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_7/Relu", "StatefulPartitionedCall/model/conv2d_11/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_11/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/activation_1/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_11/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_1/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_2/truediv", "StatefulPartitionedCall/model/activation_1/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_12/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply_1/mul", "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_13/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/batch_normalization_12/FusedBatchNormV3", "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/h_swish_3/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_3/truediv_recip", "StatefulPartitionedCall/model/batch_normalization_13/FusedBatchNormV3"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_3/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_13/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish_3/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_3/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_3/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_3/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_3/mul", "StatefulPartitionedCall/model/h_swish_3/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/h_swish_3/truediv", "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_bn_offset"], "attr": {"num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/h_swish_4/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_4/truediv_recip", "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_4/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise", "StatefulPartitionedCall/model/h_swish_4/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_4/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_4/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_4/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_4/mul", "StatefulPartitionedCall/model/h_swish_4/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_2/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/h_swish_4/truediv", "StatefulPartitionedCall/model/global_average_pooling2d_2/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/model/re_lu_8/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_2/Mean", "StatefulPartitionedCall/model/conv2d_14/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_14/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/conv2d_15/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_8/Relu", "StatefulPartitionedCall/model/conv2d_15/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_15/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/activation_2/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_15/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_2/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_4/truediv", "StatefulPartitionedCall/model/activation_2/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_15/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply_2/mul", "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_15/FusedBatchNormV3", "StatefulPartitionedCall/model/batch_normalization_12/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_16/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_1/add", "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/h_swish_5/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_5/truediv_recip", "StatefulPartitionedCall/model/batch_normalization_16/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_5/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_16/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish_5/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_5/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_5/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_5/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_5/mul", "StatefulPartitionedCall/model/h_swish_5/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/h_swish_5/truediv", "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_bn_offset"], "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/h_swish_6/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_6/truediv_recip", "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_6/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise", "StatefulPartitionedCall/model/h_swish_6/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_6/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_6/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_6/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_6/mul", "StatefulPartitionedCall/model/h_swish_6/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_3/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/h_swish_6/truediv", "StatefulPartitionedCall/model/global_average_pooling2d_3/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_9/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_3/Mean", "StatefulPartitionedCall/model/conv2d_18/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_18/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/conv2d_19/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_9/Relu", "StatefulPartitionedCall/model/conv2d_19/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_19/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/activation_3/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_19/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_3/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_6/truediv", "StatefulPartitionedCall/model/activation_3/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_18/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply_3/mul", "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/add_2/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_18/FusedBatchNormV3", "StatefulPartitionedCall/model/add_1/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_19/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_2/add", "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_7/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_7/truediv_recip", "StatefulPartitionedCall/model/batch_normalization_19/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_7/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_19/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish_7/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_7/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_7/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_7/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_7/mul", "StatefulPartitionedCall/model/h_swish_7/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/h_swish_7/truediv", "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_bn_offset"], "attr": {"num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "U0FNRQ=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/h_swish_8/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_8/truediv_recip", "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_8/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise", "StatefulPartitionedCall/model/h_swish_8/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_8/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_8/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_8/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_8/mul", "StatefulPartitionedCall/model/h_swish_8/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_4/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/h_swish_8/truediv", "StatefulPartitionedCall/model/global_average_pooling2d_4/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/re_lu_10/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_4/Mean", "StatefulPartitionedCall/model/conv2d_22/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_22/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/conv2d_23/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_10/Relu", "StatefulPartitionedCall/model/conv2d_23/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_23/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}}}, {"name": "StatefulPartitionedCall/model/activation_4/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_23/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_4/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_8/truediv", "StatefulPartitionedCall/model/activation_4/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_21/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply_4/mul", "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/add_3/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_21/FusedBatchNormV3", "StatefulPartitionedCall/model/add_2/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_22/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_3/add", "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/h_swish_9/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_9/truediv_recip", "StatefulPartitionedCall/model/batch_normalization_22/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_9/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_22/FusedBatchNormV3", "StatefulPartitionedCall/model/h_swish_9/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_9/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_9/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_9/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_9/mul", "StatefulPartitionedCall/model/h_swish_9/Relu6"], "attr": {"_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/h_swish_9/truediv", "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/h_swish_10/mul", "op": "<PERSON><PERSON>", "input": ["ConstantFolding/StatefulPartitionedCall/model/h_swish_10/truediv_recip", "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/h_swish_10/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise", "StatefulPartitionedCall/model/h_swish_10/add/y"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_10/Relu6", "op": "Relu6", "input": ["StatefulPartitionedCall/model/h_swish_10/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/h_swish_10/truediv", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_10/mul", "StatefulPartitionedCall/model/h_swish_10/Relu6"], "attr": {"T": {"type": "DT_FLOAT"}, "_grappler_ArithmeticOptimizer_MinimizeBroadcasts": {"b": true}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_5/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/h_swish_10/truediv", "StatefulPartitionedCall/model/global_average_pooling2d_5/Mean/reduction_indices"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}}}, {"name": "StatefulPartitionedCall/model/re_lu_11/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_5/Mean", "StatefulPartitionedCall/model/conv2d_26/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_26/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_27/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_11/Relu", "StatefulPartitionedCall/model/conv2d_27/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_27/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/activation_5/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_27/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_5/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish_10/truediv", "StatefulPartitionedCall/model/activation_5/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_24/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/multiply_5/mul", "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/add_4/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_24/FusedBatchNormV3", "StatefulPartitionedCall/model/add_3/add"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_6/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/add_4/add", "StatefulPartitionedCall/model/global_average_pooling2d_6/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_12/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_4/add", "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}}}, {"name": "StatefulPartitionedCall/model/batch_normalization_26/FusedBatchNormV3", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_6/Mean", "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/activation_6/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/batch_normalization_26/FusedBatchNormV3"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_6/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/re_lu_12/Relu", "StatefulPartitionedCall/model/activation_6/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/model/multiply_6/mul", "StatefulPartitionedCall/model/up_sampling2d/mul"], "attr": {"half_pixel_centers": {"b": true}, "T": {"type": "DT_FLOAT"}, "align_corners": {"b": false}}}, {"name": "StatefulPartitionedCall/model/conv2d_31/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/up_sampling2d/resize/ResizeBilinear", "StatefulPartitionedCall/model/conv2d_31/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_31/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "epsilon": {"f": 0.0}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}}}, {"name": "StatefulPartitionedCall/model/add_5/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/add/add", "StatefulPartitionedCall/model/conv2d_31/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_7/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/add_5/add", "StatefulPartitionedCall/model/global_average_pooling2d_7/Mean/reduction_indices"], "attr": {"T": {"type": "DT_FLOAT"}, "keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/re_lu_13/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_7/Mean", "StatefulPartitionedCall/model/conv2d_32/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_32/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"num_args": {"i": "1"}, "explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/conv2d_33/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_13/Relu", "StatefulPartitionedCall/model/conv2d_33/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_33/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/activation_7/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_33/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_7/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add/add", "StatefulPartitionedCall/model/activation_7/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_6/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/multiply_7/mul", "StatefulPartitionedCall/model/conv2d_31/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_14/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_6/add", "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "use_cudnn_on_gpu": {"b": true}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu_14/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_bn_offset"], "attr": {"padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_7/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/re_lu_14/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_1/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/model/add_7/add", "StatefulPartitionedCall/model/up_sampling2d_1/mul"], "attr": {"T": {"type": "DT_FLOAT"}, "align_corners": {"b": false}, "half_pixel_centers": {"b": true}}}, {"name": "StatefulPartitionedCall/model/conv2d_35/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/up_sampling2d_1/resize/ResizeBilinear", "StatefulPartitionedCall/model/conv2d_35/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_35/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "padding": {"s": "VkFMSUQ="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/add_8/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "StatefulPartitionedCall/model/conv2d_35/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_8/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/add_8/add", "StatefulPartitionedCall/model/global_average_pooling2d_8/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "T": {"type": "DT_FLOAT"}, "Tidx": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/model/re_lu_16/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_8/Mean", "StatefulPartitionedCall/model/conv2d_36/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_36/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "epsilon": {"f": 0.0}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/conv2d_37/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_16/Relu", "StatefulPartitionedCall/model/conv2d_37/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_37/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "epsilon": {"f": 0.0}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "VkFMSUQ="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "num_args": {"i": "1"}, "data_format": {"s": "TkhXQw=="}, "use_cudnn_on_gpu": {"b": true}}}, {"name": "StatefulPartitionedCall/model/activation_8/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_37/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_8/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/batch_normalization_3/FusedBatchNormV3", "StatefulPartitionedCall/model/activation_8/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_9/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/multiply_8/mul", "StatefulPartitionedCall/model/conv2d_35/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_17/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_9/add", "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"strides": {"list": {"i": ["1", "1", "1", "1"]}}, "num_args": {"i": "1"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "explicit_paddings": {"list": {}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu_17/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_bn_offset"], "attr": {"data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "T": {"type": "DT_FLOAT"}, "padding": {"s": "U0FNRQ=="}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_10/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/re_lu_17/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_2/resize/ResizeBilinear", "op": "ResizeBilinear", "input": ["StatefulPartitionedCall/model/add_10/add", "StatefulPartitionedCall/model/up_sampling2d_2/mul"], "attr": {"half_pixel_centers": {"b": true}, "align_corners": {"b": false}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_39/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/up_sampling2d_2/resize/ResizeBilinear", "StatefulPartitionedCall/model/conv2d_39/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_39/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"explicit_paddings": {"list": {}}, "T": {"type": "DT_FLOAT"}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "num_args": {"i": "1"}, "padding": {"s": "VkFMSUQ="}, "epsilon": {"f": 0.0}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}}}, {"name": "StatefulPartitionedCall/model/add_11/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/h_swish/truediv", "StatefulPartitionedCall/model/conv2d_39/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_9/Mean", "op": "Mean", "input": ["StatefulPartitionedCall/model/add_11/add", "StatefulPartitionedCall/model/global_average_pooling2d_9/Mean/reduction_indices"], "attr": {"keep_dims": {"b": true}, "Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_19/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/global_average_pooling2d_9/Mean", "StatefulPartitionedCall/model/conv2d_40/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_40/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "VkFMSUQ="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0.0}}}, {"name": "StatefulPartitionedCall/model/conv2d_41/BiasAdd", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/re_lu_19/Relu", "StatefulPartitionedCall/model/conv2d_41/Conv2D/ReadVariableOp", "StatefulPartitionedCall/model/conv2d_41/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"epsilon": {"f": 0.0}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "num_args": {"i": "1"}, "explicit_paddings": {"list": {}}}}, {"name": "StatefulPartitionedCall/model/activation_9/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_41/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/multiply_9/mul", "op": "<PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/h_swish/truediv", "StatefulPartitionedCall/model/activation_9/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/add_12/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/multiply_9/mul", "StatefulPartitionedCall/model/conv2d_39/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/re_lu_20/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/model/add_12/add", "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "padding": {"s": "VkFMSUQ="}, "use_cudnn_on_gpu": {"b": true}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "epsilon": {"f": 0.0}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise", "op": "FusedDepthwiseConv2dNative", "input": ["StatefulPartitionedCall/model/re_lu_20/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_weights", "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_bn_offset"], "attr": {"T": {"type": "DT_FLOAT"}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "explicit_paddings": {"list": {}}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/model/add_13/add", "op": "AddV2", "input": ["StatefulPartitionedCall/model/re_lu_20/Relu", "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/Shape", "op": "<PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/add_13/add"], "attr": {"out_type": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice", "op": "StridedSlice", "input": ["StatefulPartitionedCall/model/conv2d_transpose/Shape", "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack", "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_1", "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_2"], "attr": {"new_axis_mask": {"i": "0"}, "shrink_axis_mask": {"i": "1"}, "ellipsis_mask": {"i": "0"}, "end_mask": {"i": "0"}, "T": {"type": "DT_INT32"}, "Index": {"type": "DT_INT32"}, "begin_mask": {"i": "0"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack", "op": "Pack", "input": ["StatefulPartitionedCall/model/conv2d_transpose/strided_slice", "StatefulPartitionedCall/model/conv2d_transpose/stack/1", "StatefulPartitionedCall/model/conv2d_transpose/stack/2", "StatefulPartitionedCall/model/conv2d_transpose/stack/3"], "attr": {"T": {"type": "DT_INT32"}, "N": {"i": "4"}, "axis": {"i": "0"}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/conv2d_transpose", "op": "Conv2DBackpropInput", "input": ["StatefulPartitionedCall/model/conv2d_transpose/stack", "StatefulPartitionedCall/model/conv2d_transpose/conv2d_transpose/ReadVariableOp", "StatefulPartitionedCall/model/add_13/add"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "use_cudnn_on_gpu": {"b": true}, "explicit_paddings": {"list": {}}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/BiasAdd", "op": "BiasAdd", "input": ["StatefulPartitionedCall/model/conv2d_transpose/conv2d_transpose", "StatefulPartitionedCall/model/conv2d_transpose/BiasAdd/ReadVariableOp"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}}}, {"name": "StatefulPartitionedCall/model/segment_back/Sigmoid", "op": "<PERSON><PERSON><PERSON><PERSON>", "input": ["StatefulPartitionedCall/model/conv2d_transpose/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/tf.math.subtract/Sub", "op": "Sub", "input": ["unknown_208", "StatefulPartitionedCall/model/segment_back/Sigmoid"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/model/tf.concat/concat", "op": "ConcatV2", "input": ["StatefulPartitionedCall/model/tf.math.subtract/Sub", "StatefulPartitionedCall/model/segment_back/Sigmoid", "StatefulPartitionedCall/model/tf.concat/concat/axis"], "attr": {"Tidx": {"type": "DT_INT32"}, "T": {"type": "DT_FLOAT"}, "N": {"i": "2"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/model/tf.concat/concat"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {}}, "weightsManifest": [{"paths": ["group1-shard1of1.bin"], "weights": [{"name": "unknown_208", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_1", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/strided_slice/stack_2", "shape": [1], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/1", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/2", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/stack/3", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/conv2d_transpose/ReadVariableOp", "shape": [2, 2, 1, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_9/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_40/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_40/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_41/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_41/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_8/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_36/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_36/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_37/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_37/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_7/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_32/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_32/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_33/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_33/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_10/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_9/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_9/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_10/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_5/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_26/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_26/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_27/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_27/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_8/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_7/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_7/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_8/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_4/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_22/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_22/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_23/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_23/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_6/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_5/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_5/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_6/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_3/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_18/Conv2D/ReadVariableOp", "shape": [1, 1, 128, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_18/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_19/Conv2D/ReadVariableOp", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_19/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_4/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_3/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_3/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_4/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_2/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_14/Conv2D/ReadVariableOp", "shape": [1, 1, 128, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_14/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_15/Conv2D/ReadVariableOp", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_15/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_2/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish_1/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "ConstantFolding/StatefulPartitionedCall/model/h_swish/truediv_recip", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_2/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 8], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_2/BiasAdd/ReadVariableOp", "shape": [8], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_3/Conv2D/ReadVariableOp", "shape": [1, 1, 8, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_3/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_1/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/h_swish_2/add/y", "shape": [], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_1/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_10/Conv2D/ReadVariableOp", "shape": [1, 1, 96, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_10/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_11/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_11/BiasAdd/ReadVariableOp", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/global_average_pooling2d_6/Mean/reduction_indices", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/up_sampling2d/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_31/Conv2D/ReadVariableOp", "shape": [1, 1, 128, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_31/BiasAdd/ReadVariableOp", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_1/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_35/Conv2D/ReadVariableOp", "shape": [1, 1, 24, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_35/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/up_sampling2d_2/mul", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d_39/Conv2D/ReadVariableOp", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_39/BiasAdd/ReadVariableOp", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_transpose/BiasAdd/ReadVariableOp", "shape": [1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/tf.concat/concat/axis", "shape": [], "dtype": "int32"}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_weights", "shape": [3, 3, 3, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_weights", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_1/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_weights", "shape": [3, 3, 16, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d/depthwise_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_weights", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_4/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_weights", "shape": [1, 1, 16, 72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_5/Conv2D_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_weights", "shape": [3, 3, 72, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_1/depthwise_bn_offset", "shape": [72], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_weights", "shape": [1, 1, 72, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_6/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_weights", "shape": [1, 1, 24, 88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_7/Conv2D_bn_offset", "shape": [88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_weights", "shape": [3, 3, 88, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_2/depthwise_bn_offset", "shape": [88], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_weights", "shape": [1, 1, 88, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_8/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_weights", "shape": [1, 1, 24, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_9/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_weights", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_3/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_weights", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_weights", "shape": [1, 1, 96, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_38/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_12/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_weights", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_13/Conv2D_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_weights", "shape": [5, 5, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_4/depthwise_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_weights", "shape": [3, 3, 16, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_weights", "shape": [1, 1, 128, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_9/depthwise_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_16/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_weights", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_17/Conv2D_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_weights", "shape": [5, 5, 128, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_5/depthwise_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_weights", "shape": [1, 1, 128, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_20/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_weights", "shape": [1, 1, 32, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_21/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_weights", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_6/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_weights", "shape": [1, 1, 96, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_24/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_weights", "shape": [1, 1, 32, 96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_25/Conv2D_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_weights", "shape": [5, 5, 96, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_7/depthwise_bn_offset", "shape": [96], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_weights", "shape": [1, 1, 96, 32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_28/Conv2D_bn_offset", "shape": [32], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_weights", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_29/Conv2D_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_weights", "shape": [1, 1, 16, 16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_weights", "shape": [1, 1, 32, 128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_30/Conv2D_bn_offset", "shape": [128], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_42/Conv2D_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_weights", "shape": [3, 3, 16, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_weights", "shape": [1, 1, 24, 24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/conv2d_34/Conv2D_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_10/depthwise_bn_offset", "shape": [16], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_weights", "shape": [3, 3, 24, 1], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}, {"name": "StatefulPartitionedCall/model/depthwise_conv2d_8/depthwise_bn_offset", "shape": [24], "dtype": "float32", "quantization": {"dtype": "float16", "original_dtype": "float32"}}]}]}