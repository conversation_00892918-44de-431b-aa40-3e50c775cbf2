/* This used to work for the parent element of button divs */
/* But it does not work with newer browsers, the below doesn't hide the play button parent div */

*::-webkit-media-controls-panel {
  display: none !important;
  -webkit-appearance: none;
}

/* Old shadow dom for play button */

*::-webkit-media-controls-play-button {
  display: none !important;
  -webkit-appearance: none;
}

/* New shadow dom for play button */

/* This one works! */

*::-webkit-media-controls-start-playback-button {
  display: none !important;
  -webkit-appearance: none;
}

html,
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background: #fafafa;
}

.form-group {
  margin-bottom: 0;
}

.rtc-primary-bg {
  color: #fff;
  background-color: #1E88E5 !important;
  align-items: center;
}

.custom-row-container {
  display: flex;
  margin: 0 15px;
}

.navbar {
  display: flex;
  align-content: center;
  height: 54px;
}

h5 {
  margin: 0;
}

.card-body.rtc-expand-card {
  color: #fff !important;
  background-color: #9e9b9b !important;
}

.btn.rtc-expand-btn {
  width: 398px;
  color: #fff !important;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: #3a393d !important;
  display: flex;
  padding: 0 24px 0 24px;
  min-height: 48px;
  transition: min-height 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  text-decoration: none;
  margin: 0;
  margin-bottom: 10px;
  font-weight: bold;
  line-height: 48px;
}

.custom-container {
  margin: left;
  margin: 0 15px;
  box-sizing: border-box;
}

.card.custom-card {
  width: 398px;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

.card {
  width: 398px;
}

.radio-list {
  display: flex;
  margin: auto;
  margin-top: 10px;
}

.radio-list>.radio {
  margin: 0 10px;
  width: 50px;
}

#local_stream {
  position: relative;
}

#local_video_info {
  position: absolute;
}

.video-view,
#local_stream,
#local_video_info {
  width: 100%;
  /* height: 160px; */
}

.video-grid {
  display: flex;
  width: 100vw;
  height: 100vh;
}

#main {
  height: 100%;
  background: black;
}

#main,
#side {
  color: #fff;
  position: relative;
}

#side {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  overflow-y: overlay;
  background: gray;
}

#stat {
  position: fixed;
  font-size: 12px;
  /* right: 50px; */
  top: 24px;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  padding: 12px;
  max-width: 80vw;
}

#stat table {
  width: 300px;
}

#side div {
  width: 100%;
}

#form {
  max-width: 100vw;
  max-height: 80vh;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgb(255, 255, 255);
  overflow: scroll;
  padding: 24px 12px;
  z-index: 30;
}

#open-setting {
  z-index: 999;
  position: fixed;
  bottom: 50px;
  right: 50px;
}

#open-stat {
  z-index: 999;
  position: fixed;
  bottom: 100px;
  right: 50px;
}

.none {
  display: none !important;
}

.loading {
  width: 30px !important;
  height: 30px !important;
  border: 2px solid #000;
  border-top-color: transparent;
  border-radius: 100%;

  animation: circle infinite 0.75s linear;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

@keyframes circle {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}