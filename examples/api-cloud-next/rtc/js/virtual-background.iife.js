"use strict";var VirtualBackground=(()=>{var vn=Object.defineProperty,dc=Object.defineProperties,fc=Object.getOwnPropertyDescriptor,Ec=Object.getOwnPropertyDescriptors,Ic=Object.getOwnPropertyNames,si=Object.getOwnPropertySymbols;var ai=Object.prototype.hasOwnProperty,pc=Object.prototype.propertyIsEnumerable;var No=(X,R,n)=>R in X?vn(X,R,{enumerable:!0,configurable:!0,writable:!0,value:n}):X[R]=n,ci=(X,R)=>{for(var n in R||(R={}))ai.call(R,n)&&No(X,n,R[n]);if(si)for(var n of si(R))pc.call(R,n)&&No(X,n,R[n]);return X},gi=(X,R)=>dc(X,Ec(R));var Cc=(X=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(X,{get:(R,n)=>(typeof require!="undefined"?require:R)[n]}):X)(function(X){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+X+'" is not supported')});var hc=(X,R)=>{for(var n in R)vn(X,n,{get:R[n],enumerable:!0})},Bc=(X,R,n,re)=>{if(R&&typeof R=="object"||typeof R=="function")for(let se of Ic(R))!ai.call(X,se)&&se!==n&&vn(X,se,{get:()=>R[se],enumerable:!(re=fc(R,se))||re.enumerable});return X};var mc=X=>Bc(vn({},"__esModule",{value:!0}),X);var _e=(X,R,n)=>(No(X,typeof R!="symbol"?R+"":R,n),n);var bc={};hc(bc,{VirtualBackground:()=>Un,default:()=>Dc});async function li({sdkAppId:X,userId:R,userSig:n,core:re}){var De;let se=re.utils.getAbilityConfigUrl(X),ue=Math.round(new Date().getTime()/1e3),Ie=`${se}?sdkAppId=${X}&userId=${R}&userSig=${n}&timestamp=${ue}`;try{let ke=await(await fetch(Ie)).json();re.log.info(`virtual background send ${Ie}, response: ${JSON.stringify(ke)}`);let{data:Oe}=ke;return(De=Oe==null?void 0:Oe.trtcAutoConf)!=null&&De.web_ar?{auth:!0,timestamp:ue}:{auth:!1}}catch(ve){return re.log.error("beauty fetch error",ve),{auth:!1}}}var _c={sdkAppId:{required:!0,type:"number"},userId:{required:!0,type:"string"},userSig:{required:!0,type:"string"}};function ui(X){return{name:"VirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:gi(ci({},_c),{type:{required:!1,type:"string",values:["image","blur"]},src:{required:!1,type:"string"},onError:{required:!1}}),validate(R,n,re,se){var pe;let{RtcError:ue,ErrorCode:Ie,ErrorCodeDictionary:De}=X.errorModule;if(!R)return;let{type:ve,src:ke,onError:Oe}=R;if(ve==="image"&&!ke)throw new ue({code:Ie.INVALID_PARAMETER,extraCode:De.INVALID_PARAMETER_REQUIRED,fnName:re,messageParams:{key:"src"}});if(Oe&&!X.utils.isFunction(Oe))throw new ue({code:Ie.INVALID_PARAMETER,extraCode:De.INVALID_PARAMETER_TYPE,fnName:re,messageParams:{key:"onError",value:typeof Oe,rule:{type:"Function"}}});if(!((pe=X.room.videoManager.cameraTrack)!=null&&pe.mediaTrack))throw new ue({code:Ie.INVALID_OPERATION,extraCode:De.INVALID_OPERATION_NEED_VIDEO,fnName:re});if(X.utils.env.CHROME_MAJOR_VERSION<90)throw new ue({code:Ie.ENV_NOT_SUPPORTED,extraCode:De.NOT_SUPPORTED_CHROME_VERSION,fnName:re})}}}function di(X){return{name:"UpdateVirtualBackgroundOptions",type:"object",required:!0,allowEmpty:!1,properties:{type:{required:!0,type:"string",values:["image","blur"]},src:{required:!1,type:"string"}},validate(R,n,re,se){if(!R)return;let{RtcError:ue,ErrorCode:Ie,ErrorCodeDictionary:De}=X.errorModule,{type:ve,src:ke}=R;if(ve==="image"&&!ke)throw new ue({code:Ie.INVALID_PARAMETER,extraCode:De.INVALID_PARAMETER_REQUIRED,fnName:re,messageParams:{key:"src"}})}}}function fi(X){return{name:"StopVirtualBackgroundOptions",required:!1}}var yc=function(){let X=typeof document!="undefined"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename!="undefined"&&(X=X||__filename),function(R){R=R||{};let n=typeof R!="undefined"?R:{},re,se;n.ready=new Promise((e,t)=>{re=e,se=t}),Object.getOwnPropertyDescriptor(n.ready,"_main")||(Object.defineProperty(n.ready,"_main",{configurable:!0,get(){l("You are getting _main on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_main",{configurable:!0,set(){l("You are setting _main on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_stack_get_end")||(Object.defineProperty(n.ready,"_emscripten_stack_get_end",{configurable:!0,get(){l("You are getting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_stack_get_end",{configurable:!0,set(){l("You are setting _emscripten_stack_get_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_stack_get_free")||(Object.defineProperty(n.ready,"_emscripten_stack_get_free",{configurable:!0,get(){l("You are getting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_stack_get_free",{configurable:!0,set(){l("You are setting _emscripten_stack_get_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_stack_init")||(Object.defineProperty(n.ready,"_emscripten_stack_init",{configurable:!0,get(){l("You are getting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_stack_init",{configurable:!0,set(){l("You are setting _emscripten_stack_init on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_stackSave")||(Object.defineProperty(n.ready,"_stackSave",{configurable:!0,get(){l("You are getting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_stackSave",{configurable:!0,set(){l("You are setting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_stackRestore")||(Object.defineProperty(n.ready,"_stackRestore",{configurable:!0,get(){l("You are getting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_stackRestore",{configurable:!0,set(){l("You are setting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_stackAlloc")||(Object.defineProperty(n.ready,"_stackAlloc",{configurable:!0,get(){l("You are getting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_stackAlloc",{configurable:!0,set(){l("You are setting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___wasm_call_ctors")||(Object.defineProperty(n.ready,"___wasm_call_ctors",{configurable:!0,get(){l("You are getting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___wasm_call_ctors",{configurable:!0,set(){l("You are setting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_fflush")||(Object.defineProperty(n.ready,"_fflush",{configurable:!0,get(){l("You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_fflush",{configurable:!0,set(){l("You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___errno_location")||(Object.defineProperty(n.ready,"___errno_location",{configurable:!0,get(){l("You are getting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___errno_location",{configurable:!0,set(){l("You are setting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_malloc")||(Object.defineProperty(n.ready,"_malloc",{configurable:!0,get(){l("You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_malloc",{configurable:!0,set(){l("You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_free")||(Object.defineProperty(n.ready,"_free",{configurable:!0,get(){l("You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_free",{configurable:!0,set(){l("You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_setThrew")||(Object.defineProperty(n.ready,"_setThrew",{configurable:!0,get(){l("You are getting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_setThrew",{configurable:!0,set(){l("You are setting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___cxa_is_pointer_type")||(Object.defineProperty(n.ready,"___cxa_is_pointer_type",{configurable:!0,get(){l("You are getting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___cxa_is_pointer_type",{configurable:!0,set(){l("You are setting ___cxa_is_pointer_type on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___cxa_can_catch")||(Object.defineProperty(n.ready,"___cxa_can_catch",{configurable:!0,get(){l("You are getting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___cxa_can_catch",{configurable:!0,set(){l("You are setting ___cxa_can_catch on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_saveSetjmp")||(Object.defineProperty(n.ready,"_saveSetjmp",{configurable:!0,get(){l("You are getting _saveSetjmp on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_saveSetjmp",{configurable:!0,set(){l("You are setting _saveSetjmp on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_memalign")||(Object.defineProperty(n.ready,"_memalign",{configurable:!0,get(){l("You are getting _memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_memalign",{configurable:!0,set(){l("You are setting _memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_main_thread_process_queued_calls")||(Object.defineProperty(n.ready,"_emscripten_main_thread_process_queued_calls",{configurable:!0,get(){l("You are getting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_main_thread_process_queued_calls",{configurable:!0,set(){l("You are setting _emscripten_main_thread_process_queued_calls on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_webgl_make_context_current")||(Object.defineProperty(n.ready,"_emscripten_webgl_make_context_current",{configurable:!0,get(){l("You are getting _emscripten_webgl_make_context_current on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_webgl_make_context_current",{configurable:!0,set(){l("You are setting _emscripten_webgl_make_context_current on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_webgl_get_current_context")||(Object.defineProperty(n.ready,"_emscripten_webgl_get_current_context",{configurable:!0,get(){l("You are getting _emscripten_webgl_get_current_context on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_webgl_get_current_context",{configurable:!0,set(){l("You are setting _emscripten_webgl_get_current_context on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"__get_tzname")||(Object.defineProperty(n.ready,"__get_tzname",{configurable:!0,get(){l("You are getting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"__get_tzname",{configurable:!0,set(){l("You are setting __get_tzname on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"__get_daylight")||(Object.defineProperty(n.ready,"__get_daylight",{configurable:!0,get(){l("You are getting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"__get_daylight",{configurable:!0,set(){l("You are setting __get_daylight on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"__get_timezone")||(Object.defineProperty(n.ready,"__get_timezone",{configurable:!0,get(){l("You are getting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"__get_timezone",{configurable:!0,set(){l("You are setting __get_timezone on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_builtin_malloc")||(Object.defineProperty(n.ready,"_emscripten_builtin_malloc",{configurable:!0,get(){l("You are getting _emscripten_builtin_malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_builtin_malloc",{configurable:!0,set(){l("You are setting _emscripten_builtin_malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_builtin_free")||(Object.defineProperty(n.ready,"_emscripten_builtin_free",{configurable:!0,get(){l("You are getting _emscripten_builtin_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_builtin_free",{configurable:!0,set(){l("You are setting _emscripten_builtin_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"_emscripten_builtin_memalign")||(Object.defineProperty(n.ready,"_emscripten_builtin_memalign",{configurable:!0,get(){l("You are getting _emscripten_builtin_memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"_emscripten_builtin_memalign",{configurable:!0,set(){l("You are setting _emscripten_builtin_memalign on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___getTypeName")||(Object.defineProperty(n.ready,"___getTypeName",{configurable:!0,get(){l("You are getting ___getTypeName on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___getTypeName",{configurable:!0,set(){l("You are setting ___getTypeName on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"___embind_register_native_and_builtin_types")||(Object.defineProperty(n.ready,"___embind_register_native_and_builtin_types",{configurable:!0,get(){l("You are getting ___embind_register_native_and_builtin_types on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"___embind_register_native_and_builtin_types",{configurable:!0,set(){l("You are setting ___embind_register_native_and_builtin_types on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(n.ready,"onRuntimeInitialized")||(Object.defineProperty(n.ready,"onRuntimeInitialized",{configurable:!0,get(){l("You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(n.ready,"onRuntimeInitialized",{configurable:!0,set(){l("You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}));let ue={},Ie;for(Ie in n)n.hasOwnProperty(Ie)&&(ue[Ie]=n[Ie]);let De=[],ve="./this.program",ke=function(e,t){throw t},Oe=typeof window=="object",pe=typeof importScripts=="function",Bt=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Ln=!Oe&&!Bt&&!pe;if(n.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");let je="";function ar(e){return n.locateFile?n.locateFile(e,je):je+e}let cr,gr,St,$t,Uo,jn;if(Oe||pe){if(pe?je=self.location.href:typeof document!="undefined"&&document.currentScript&&(je=document.currentScript.src),X&&(je=X),je.indexOf("blob:")!==0?je=je.substr(0,je.lastIndexOf("/")+1):je="",!(typeof window=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");cr=function(e){let t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},pe&&(St=function(e){let t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),gr=function(e,t,r){let o=new XMLHttpRequest;o.open("GET",e,!0),o.responseType="arraybuffer",o.onload=function(){if(o.status==200||o.status==0&&o.response){t(o.response);return}r()},o.onerror=r,o.send(null)},$t=function(e){document.title=e}}else throw new Error("environment detection error");let ae=n.print||console.log.bind(console),J=n.printErr||console.warn.bind(console);for(Ie in ue)ue.hasOwnProperty(Ie)&&(n[Ie]=ue[Ie]);ue=null,n.arguments&&(De=n.arguments),Object.getOwnPropertyDescriptor(n,"arguments")||Object.defineProperty(n,"arguments",{configurable:!0,get(){l("Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),n.thisProgram&&(ve=n.thisProgram),Object.getOwnPropertyDescriptor(n,"thisProgram")||Object.defineProperty(n,"thisProgram",{configurable:!0,get(){l("Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),n.quit&&(ke=n.quit),Object.getOwnPropertyDescriptor(n,"quit")||Object.defineProperty(n,"quit",{configurable:!0,get(){l("Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),M(typeof n.memoryInitializerPrefixURL=="undefined","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),M(typeof n.pthreadMainPrefixURL=="undefined","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),M(typeof n.cdInitializerPrefixURL=="undefined","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),M(typeof n.filePackagePrefixURL=="undefined","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),M(typeof n.read=="undefined","Module.read option was removed (modify read_ in JS)"),M(typeof n.readAsync=="undefined","Module.readAsync option was removed (modify readAsync in JS)"),M(typeof n.readBinary=="undefined","Module.readBinary option was removed (modify readBinary in JS)"),M(typeof n.setWindowTitle=="undefined","Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),M(typeof n.TOTAL_MEMORY=="undefined","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Object.getOwnPropertyDescriptor(n,"read")||Object.defineProperty(n,"read",{configurable:!0,get(){l("Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(n,"readAsync")||Object.defineProperty(n,"readAsync",{configurable:!0,get(){l("Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(n,"readBinary")||Object.defineProperty(n,"readBinary",{configurable:!0,get(){l("Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(n,"setWindowTitle")||Object.defineProperty(n,"setWindowTitle",{configurable:!0,get(){l("Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),M(!Ln,"shell environment detected but not enabled at build time.  Add 'shell' to `-s ENVIRONMENT` to enable.");function Re(e){Re.shown||(Re.shown={}),Re.shown[e]||(Re.shown[e]=1,J(e))}function ct(e,t){if(typeof WebAssembly.Function=="function"){let C={i:"i32",j:"i64",f:"f32",d:"f64"},h={parameters:[],results:t[0]=="v"?[]:[C[t[0]]]};for(var r=1;r<t.length;++r)h.parameters.push(C[t[r]]);return new WebAssembly.Function(h,e)}let o=[1,0,1,96],i=t.slice(0,1),A=t.slice(1),s={i:127,j:126,f:125,d:124};o.push(A.length);for(var r=0;r<A.length;++r)o.push(s[A[r]]);i=="v"?o.push(0):o=o.concat([1,s[i]]),o[1]=o.length-2;let c=new Uint8Array([0,97,115,109,1,0,0,0].concat(o,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0])),d=new WebAssembly.Module(c);return new WebAssembly.Instance(d,{e:{f:e}}).exports.f}let be=[],Ne;function Vt(){if(be.length)return be.pop();try{F.grow(1)}catch(e){throw e instanceof RangeError?"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.":e}return F.length-1}function mt(e,t){if(!Ne){Ne=new WeakMap;for(let o=0;o<F.length;o++){let i=F.get(o);i&&Ne.set(i,o)}}if(Ne.has(e))return Ne.get(e);let r=Vt();try{F.set(r,e)}catch(o){if(!(o instanceof TypeError))throw o;M(typeof t!="undefined",`Missing signature argument to addFunction: ${e}`);let i=ct(e,t);F.set(r,i)}return Ne.set(e,r),r}let de=0,He=function(e){de=e},Ur=function(){return de},Ft;n.wasmBinary&&(Ft=n.wasmBinary),Object.getOwnPropertyDescriptor(n,"wasmBinary")||Object.defineProperty(n,"wasmBinary",{configurable:!0,get(){l("Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}});let xr=n.noExitRuntime||!0;Object.getOwnPropertyDescriptor(n,"noExitRuntime")||Object.defineProperty(n,"noExitRuntime",{configurable:!0,get(){l("Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),typeof WebAssembly!="object"&&l("no native wasm support detected");let _t,$e=!1,vt;function M(e,t){e||l(`Assertion failed: ${t}`)}function Hn(e){let t=n[`_${e}`];return M(t,`Cannot call unknown function ${e}, make sure it is exported`),t}function xo(e,t,r,o,i){let A={string(h){let y=0;if(h!=null&&h!==0){let T=(h.length<<2)+1;y=ni(T),Qt(h,y,T)}return y},array(h){let y=ni(h.length);return Yr(h,y),y}};function s(h){return t==="string"?Pe(h):t==="boolean"?!!h:h}let c=Hn(e),d=[],E=0;if(M(t!=="array",'Return type should not be "array".'),o)for(let h=0;h<o.length;h++){let y=A[r[h]];y?(E===0&&(E=L()),d[h]=y(o[h])):d[h]=o[h]}let I=c.apply(null,d);function C(h){return E!==0&&j(E),s(h)}return I=C(I),I}let Lo=1,Lr=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):void 0;function yt(e,t,r){let o=t+r,i=t;for(;e[i]&&!(i>=o);)++i;if(i-t>16&&e.subarray&&Lr)return Lr.decode(e.subarray(t,i));let A="";for(;t<i;){let s=e[t++];if(!(s&128)){A+=String.fromCharCode(s);continue}let c=e[t++]&63;if((s&224)==192){A+=String.fromCharCode((s&31)<<6|c);continue}let d=e[t++]&63;if((s&240)==224?s=(s&15)<<12|c<<6|d:((s&248)!=240&&Re(`Invalid UTF-8 leading byte 0x${s.toString(16)} encountered when deserializing a UTF-8 string in wasm memory to a JS string!`),s=(s&7)<<18|c<<12|d<<6|e[t++]&63),s<65536)A+=String.fromCharCode(s);else{let E=s-65536;A+=String.fromCharCode(55296|E>>10,56320|E&1023)}}return A}function Pe(e,t){return e?yt(ge,e,t):""}function Ge(e,t,r,o){if(!(o>0))return 0;let i=r,A=r+o-1;for(let s=0;s<e.length;++s){let c=e.charCodeAt(s);if(c>=55296&&c<=57343){let d=e.charCodeAt(++s);c=65536+((c&1023)<<10)|d&1023}if(c<=127){if(r>=A)break;t[r++]=c}else if(c<=2047){if(r+1>=A)break;t[r++]=192|c>>6,t[r++]=128|c&63}else if(c<=65535){if(r+2>=A)break;t[r++]=224|c>>12,t[r++]=128|c>>6&63,t[r++]=128|c&63}else{if(r+3>=A)break;c>=2097152&&Re(`Invalid Unicode code point 0x${c.toString(16)} encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x1FFFFF).`),t[r++]=240|c>>18,t[r++]=128|c>>12&63,t[r++]=128|c>>6&63,t[r++]=128|c&63}}return t[r]=0,r-i}function Qt(e,t,r){return M(typeof r=="number","stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),Ge(e,ge,t,r)}function Ye(e){let t=0;for(let r=0;r<e.length;++r){let o=e.charCodeAt(r);o>=55296&&o<=57343&&(o=65536+((o&1023)<<10)|e.charCodeAt(++r)&1023),o<=127?++t:o<=2047?t+=2:o<=65535?t+=3:t+=4}return t}let jr=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):void 0;function Gn(e,t){M(e%2==0,"Pointer passed to UTF16ToString must be aligned to two bytes!");let r=e,o=r>>1,i=o+t/2;for(;!(o>=i)&&kt[o];)++o;if(r=o<<1,r-e>32&&jr)return jr.decode(ge.subarray(e,r));let A="";for(let s=0;!(s>=t/2);++s){let c=qe[e+s*2>>1];if(c==0)break;A+=String.fromCharCode(c)}return A}function Kt(e,t,r){if(M(t%2==0,"Pointer passed to stringToUTF16 must be aligned to two bytes!"),M(typeof r=="number","stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),r===void 0&&(r=2147483647),r<2)return 0;r-=2;let o=t,i=r<e.length*2?r/2:e.length;for(let A=0;A<i;++A){let s=e.charCodeAt(A);qe[t>>1]=s,t+=2}return qe[t>>1]=0,t-o}function Hr(e){return e.length*2}function Gr(e,t){M(e%4==0,"Pointer passed to UTF32ToString must be aligned to four bytes!");let r=0,o="";for(;!(r>=t/4);){let i=m[e+r*4>>2];if(i==0)break;if(++r,i>=65536){let A=i-65536;o+=String.fromCharCode(55296|A>>10,56320|A&1023)}else o+=String.fromCharCode(i)}return o}function wt(e,t,r){if(M(t%4==0,"Pointer passed to stringToUTF32 must be aligned to four bytes!"),M(typeof r=="number","stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),r===void 0&&(r=2147483647),r<4)return 0;let o=t,i=o+r-4;for(let A=0;A<e.length;++A){let s=e.charCodeAt(A);if(s>=55296&&s<=57343){let c=e.charCodeAt(++A);s=65536+((s&1023)<<10)|c&1023}if(m[t>>2]=s,t+=4,t+4>i)break}return m[t>>2]=0,t-o}function Yn(e){let t=0;for(let r=0;r<e.length;++r){let o=e.charCodeAt(r);o>=55296&&o<=57343&&++r,t+=4}return t}function lr(e){let t=Ye(e)+1,r=Wt(t);return r&&Ge(e,ce,r,t),r}function Yr(e,t){M(e.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),ce.set(e,t)}function Xn(e,t,r){for(let o=0;o<e.length;++o)M(e.charCodeAt(o)===e.charCodeAt(o)&255),ce[t++>>0]=e.charCodeAt(o);r||(ce[t>>0]=0)}function Wn(e,t){return e%t>0&&(e+=t-e%t),e}let gt,ce,ge,qe,kt,m,ie,ye,Jt;function Qe(e){gt=e,n.HEAP8=ce=new Int8Array(e),n.HEAP16=qe=new Int16Array(e),n.HEAP32=m=new Int32Array(e),n.HEAPU8=ge=new Uint8Array(e),n.HEAPU16=kt=new Uint16Array(e),n.HEAPU32=ie=new Uint32Array(e),n.HEAPF32=ye=new Float32Array(e),n.HEAPF64=Jt=new Float64Array(e)}let et=5242880;n.TOTAL_STACK&&M(et===n.TOTAL_STACK,"the stack size can no longer be determined at runtime");let Ve=n.INITIAL_MEMORY||16777216;Object.getOwnPropertyDescriptor(n,"INITIAL_MEMORY")||Object.defineProperty(n,"INITIAL_MEMORY",{configurable:!0,get(){l("Module.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),M(Ve>=et,`INITIAL_MEMORY should be larger than TOTAL_STACK, was ${Ve}! (TOTAL_STACK=${et})`),M(typeof Int32Array!="undefined"&&typeof Float64Array!="undefined"&&Int32Array.prototype.subarray!==void 0&&Int32Array.prototype.set!==void 0,"JS engine does not provide full typed array support"),M(!n.wasmMemory,"Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally"),M(Ve==16777216,"Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically");let F;function lt(){let e=Fo();M((e&3)==0),ie[(e>>2)+1]=34821223,ie[(e>>2)+2]=2310721022,m[0]=1668509029}function W(){if($e)return;let e=Fo(),t=ie[(e>>2)+1],r=ie[(e>>2)+2];(t!=34821223||r!=2310721022)&&l(`Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x${r.toString(16)} ${t.toString(16)}`),m[0]!==1668509029&&l("Runtime error: The application has corrupted its heap memory area (address zero)!")}(function(){let e=new Int16Array(1),t=new Int8Array(e.buffer);if(e[0]=25459,t[0]!==115||t[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -s SUPPORT_BIG_ENDIAN=1 to bypass)"})();let ur=[],Nt=[],ut=[],Xr=[],Ue=!1,Wr=!1,$n=0;function dr(){return xr||$n>0}function fr(){if(n.preRun)for(typeof n.preRun=="function"&&(n.preRun=[n.preRun]);n.preRun.length;)Vr(n.preRun.shift());Cr(ur)}function Er(){W(),M(!Ue),Ue=!0,!n.noFSInit&&!a.init.initialized&&a.init(),a.ignorePermissions=!1,Ee.init(),Cr(Nt)}function $r(){W(),Wr=!0}function Vn(){if(W(),n.postRun)for(typeof n.postRun=="function"&&(n.postRun=[n.postRun]);n.postRun.length;)Jr(n.postRun.shift());Cr(Xr)}function Vr(e){ur.unshift(e)}function Kr(e){Nt.unshift(e)}function Jr(e){Xr.unshift(e)}M(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),M(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");let dt=0,ft=null,Ke=null,tt={};function Dt(e){let t=e;for(;;){if(!tt[e])return e;e=t+Math.random()}}function zt(e){dt++,n.monitorRunDependencies&&n.monitorRunDependencies(dt),e?(M(!tt[e]),tt[e]=1,ft===null&&typeof setInterval!="undefined"&&(ft=setInterval(()=>{if($e){clearInterval(ft),ft=null;return}let t=!1;for(let r in tt)t||(t=!0,J("still waiting on run dependencies:")),J(`dependency: ${r}`);t&&J("(end of list)")},1e4))):J("warning: run dependency added without ID")}function Ut(e){if(dt--,n.monitorRunDependencies&&n.monitorRunDependencies(dt),e?(M(tt[e]),delete tt[e]):J("warning: run dependency removed without ID"),dt==0&&(ft!==null&&(clearInterval(ft),ft=null),Ke)){let t=Ke;Ke=null,t()}}n.preloadedImages={},n.preloadedAudios={};function l(e){n.onAbort&&n.onAbort(e),e+="",J(e),$e=!0,vt=1,e=`abort(${e}) at ${no()}`;let r=new WebAssembly.RuntimeError(e);throw se(r),r}let Kn="data:application/octet-stream;base64,";function zr(e){return e.startsWith(Kn)}function bt(e){return e.startsWith("file://")}function V(e,t){return function(){let r=e,o=t;return t||(o=n.asm),M(Ue,`native function \`${r}\` called before runtime initialization`),M(!Wr,`native function \`${r}\` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)`),o[e]||M(o[e],`exported native function \`${r}\` not found`),o[e].apply(null,arguments)}}let fe;fe="selfie_segmentation_solution_simd_wasm_bin.wasm",zr(fe)||(fe=ar(fe));function Zr(e){try{if(e==fe&&Ft)return new Uint8Array(Ft);if(St)return St(e);throw"both async and sync fetching of the wasm failed"}catch(t){l(t)}}function Jn(){if(!Ft&&(Oe||pe)){if(typeof fetch=="function"&&!bt(fe))return fetch(fe,{credentials:"same-origin"}).then(e=>{if(!e.ok)throw`failed to load wasm binary file at '${fe}'`;return e.arrayBuffer()}).catch(()=>Zr(fe));if(gr)return new Promise((e,t)=>{gr(fe,r=>{e(new Uint8Array(r))},t)})}return Promise.resolve().then(()=>Zr(fe))}function zn(){let e={env:ri,wasi_snapshot_preview1:ri};function t(s,c){let{exports:d}=s;n.asm=d,_t=n.asm.memory,M(_t,"memory not found in wasm exports"),Qe(_t.buffer),F=n.asm.__indirect_function_table,M(F,"table not found in wasm exports"),Kr(n.asm.__wasm_call_ctors),Ut("wasm-instantiate")}zt("wasm-instantiate");let r=n;function o(s){M(n===r,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),r=null,t(s.instance)}function i(s){return Jn().then(c=>WebAssembly.instantiate(c,e)).then(s,c=>{J(`failed to asynchronously prepare wasm: ${c}`),bt(fe)&&J(`warning: Loading from a file URI (${fe}) is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing`),l(c)})}function A(){return!Ft&&typeof WebAssembly.instantiateStreaming=="function"&&!zr(fe)&&!bt(fe)&&typeof fetch=="function"?fetch(fe,{credentials:"same-origin"}).then(s=>WebAssembly.instantiateStreaming(s,e).then(o,d=>(J(`wasm streaming compile failed: ${d}`),J("falling back to ArrayBuffer instantiation"),i(o)))):i(o)}if(n.instantiateWasm)try{return n.instantiateWasm(e,t)}catch(s){return J(`Module.instantiateWasm callback failed with error: ${s}`),!1}return A().catch(se),{}}let Be,Je,ze={460656(){let e=!0;if(e){let t=Ro;if(typeof t!="function"){typeof console!="undefined"&&console.error('Expected Emscripten global function "findCanvasEventTarget" not found. WebGL context creation may fail.');return}Ro=function(r){if(r==0){if(n&&n.canvas)return n.canvas;if(n&&n.canvasCssSelector)return t(n.canvasCssSelector);typeof console!="undefined"&&console.warn("Module properties canvas and canvasCssSelector not found during WebGL context creation.")}return t(r)},e=!1}},461441(){return typeof wasmOffsetConverter!="undefined"}};function Zn(){return typeof wasmOffsetConverter!="undefined"}function Ir(e,t){if(_.mainLoop.timingMode=e,_.mainLoop.timingValue=t,!_.mainLoop.func)return J("emscripten_set_main_loop_timing: Cannot set timing mode for main loop since a main loop does not exist! Call emscripten_set_main_loop first to set one up."),1;if(_.mainLoop.running||(_.mainLoop.running=!0),e==0)_.mainLoop.scheduler=function(){let o=Math.max(0,_.mainLoop.tickStartTime+t-rt())|0;setTimeout(_.mainLoop.runner,o)},_.mainLoop.method="timeout";else if(e==1)_.mainLoop.scheduler=function(){_.requestAnimationFrame(_.mainLoop.runner)},_.mainLoop.method="rAF";else if(e==2){if(typeof setImmediate=="undefined"){let r=[],o="setimmediate",i=function(A){(A.data===o||A.data.target===o)&&(A.stopPropagation(),r.shift()())};addEventListener("message",i,!0),setImmediate=function(s){r.push(s),pe?(n.setImmediates===void 0&&(n.setImmediates=[]),n.setImmediates.push(s),postMessage({target:o})):postMessage(o,"*")}}_.mainLoop.scheduler=function(){setImmediate(_.mainLoop.runner)},_.mainLoop.method="immediate"}return 0}let rt;Bt?rt=function(){let e=process.hrtime();return e[0]*1e3+e[1]/1e6}:rt=function(){return performance.now()};function qn(){return!p.currentContext||!p.currentContext.GLctx?-3:p.currentContext.defaultFbo?(p.blitOffscreenFramebuffer(p.currentContext),0):p.currentContext.attributes.explicitSwapControl?0:-3}function pr(e){lc(e)}function eo(){if(!dr())try{pr(vt)}catch(e){if(e instanceof vo)return;throw e}}function to(e,t,r,o,i){M(!_.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),_.mainLoop.func=e,_.mainLoop.arg=o;let A=_.mainLoop.currentlyRunningMainloop;function s(){return A<_.mainLoop.currentlyRunningMainloop?(eo(),!1):!0}if(_.mainLoop.running=!1,_.mainLoop.runner=function(){if(!$e){if(_.mainLoop.queue.length>0){let d=Date.now(),E=_.mainLoop.queue.shift();if(E.func(E.arg),_.mainLoop.remainingBlockers){let I=_.mainLoop.remainingBlockers,C=I%1==0?I-1:Math.floor(I);E.counted?_.mainLoop.remainingBlockers=C:(C=C+.5,_.mainLoop.remainingBlockers=(8*I+C)/9)}if(ae(`main loop blocker "${E.name}" took ${Date.now()-d} ms`),_.mainLoop.updateStatus(),!s())return;setTimeout(_.mainLoop.runner,0);return}if(s()){if(_.mainLoop.currentFrameNumber=_.mainLoop.currentFrameNumber+1|0,_.mainLoop.timingMode==1&&_.mainLoop.timingValue>1&&_.mainLoop.currentFrameNumber%_.mainLoop.timingValue!=0){_.mainLoop.scheduler();return}_.mainLoop.timingMode==0&&(_.mainLoop.tickStartTime=rt()),p.newRenderingFrameStarted(),typeof p!="undefined"&&p.currentContext&&!p.currentContextIsProxied&&!p.currentContext.attributes.explicitSwapControl&&p.currentContext.GLctx.commit&&p.currentContext.GLctx.commit(),_.mainLoop.method==="timeout"&&n.ctx&&(Re("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),_.mainLoop.method=""),_.mainLoop.runIter(e),W(),s()&&(typeof SDL=="object"&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),_.mainLoop.scheduler())}}},i||(t&&t>0?Ir(0,1e3/t):Ir(1,1),_.mainLoop.scheduler()),r)throw"unwind"}function Tt(e,t){if($e){J("user callback triggered after application aborted.  Ignoring.");return}if(t){e();return}try{e()}catch(r){if(r instanceof vo)return;if(r!=="unwind")throw r&&typeof r=="object"&&r.stack&&J(`exception thrown: ${[r,r.stack]}`),r}}function qr(e,t){return setTimeout(()=>{Tt(e)},t)}var _={mainLoop:{running:!1,scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause(){_.mainLoop.scheduler=null,_.mainLoop.currentlyRunningMainloop++},resume(){_.mainLoop.currentlyRunningMainloop++;let{timingMode:e}=_.mainLoop,{timingValue:t}=_.mainLoop,{func:r}=_.mainLoop;_.mainLoop.func=null,to(r,0,!1,_.mainLoop.arg,!0),Ir(e,t),_.mainLoop.scheduler()},updateStatus(){if(n.setStatus){let e=n.statusMessage||"Please wait...",t=_.mainLoop.remainingBlockers,r=_.mainLoop.expectedBlockers;t?t<r?n.setStatus(`${e} (${r-t}/${r})`):n.setStatus(e):n.setStatus("")}},runIter(e){$e||n.preMainLoop&&n.preMainLoop()===!1||(Tt(e),n.postMainLoop&&n.postMainLoop())}},isFullscreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init(){if(n.preloadPlugins||(n.preloadPlugins=[]),_.initted)return;_.initted=!0;try{new Blob,_.hasBlobConstructor=!0}catch(i){_.hasBlobConstructor=!1,ae("warning: no blob constructor, cannot create blobs with mimetypes")}_.BlobBuilder=typeof MozBlobBuilder!="undefined"?MozBlobBuilder:typeof WebKitBlobBuilder!="undefined"?WebKitBlobBuilder:_.hasBlobConstructor?null:ae("warning: no BlobBuilder"),_.URLObject=typeof window!="undefined"?window.URL?window.URL:window.webkitURL:void 0,!n.noImageDecoding&&typeof _.URLObject=="undefined"&&(ae("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),n.noImageDecoding=!0);let e={};e.canHandle=function(A){return!n.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(A)},e.handle=function(A,s,c,d){let E=null;if(_.hasBlobConstructor)try{E=new Blob([A],{type:_.getMimetype(s)}),E.size!==A.length&&(E=new Blob([new Uint8Array(A).buffer],{type:_.getMimetype(s)}))}catch(h){Re(`Blob constructor present but fails: ${h}; falling back to blob builder`)}if(!E){let h=new _.BlobBuilder;h.append(new Uint8Array(A).buffer),E=h.getBlob()}let I=_.URLObject.createObjectURL(E);M(typeof I=="string","createObjectURL must return a url as a string");let C=new Image;C.onload=function(){M(C.complete,`Image ${s} could not be decoded`);let y=document.createElement("canvas");y.width=C.width,y.height=C.height,y.getContext("2d").drawImage(C,0,0),n.preloadedImages[s]=y,_.URLObject.revokeObjectURL(I),c&&c(A)},C.onerror=function(y){ae(`Image ${I} could not be decoded`),d&&d()},C.src=I},n.preloadPlugins.push(e);let t={};t.canHandle=function(A){return!n.noAudioDecoding&&A.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},t.handle=function(A,s,c,d){let E=!1;function I(y){E||(E=!0,n.preloadedAudios[s]=y,c&&c(A))}function C(){E||(E=!0,n.preloadedAudios[s]=new Audio,d&&d())}if(_.hasBlobConstructor){try{var h=new Blob([A],{type:_.getMimetype(s)})}catch(N){return C()}let y=_.URLObject.createObjectURL(h);M(typeof y=="string","createObjectURL must return a url as a string");let T=new Audio;T.addEventListener("canplaythrough",()=>{I(T)},!1),T.onerror=function(Y){if(E)return;ae(`warning: browser could not fully decode audio ${s}, trying slower base64 approach`);function q(D){let S="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ne="=",U="",me=0,Te=0;for(let Ze=0;Ze<D.length;Ze++)for(me=me<<8|D[Ze],Te+=8;Te>=6;){let uc=me>>Te-6&63;Te-=6,U+=S[uc]}return Te==2?(U+=S[(me&3)<<4],U+=ne+ne):Te==4&&(U+=S[(me&15)<<2],U+=ne),U}T.src=`data:audio/x-${s.substr(-3)};base64,${q(A)}`,I(T)},T.src=y,qr(()=>{I(T)},1e4)}else return C()},n.preloadPlugins.push(t);function r(){_.pointerLock=document.pointerLockElement===n.canvas||document.mozPointerLockElement===n.canvas||document.webkitPointerLockElement===n.canvas||document.msPointerLockElement===n.canvas}let{canvas:o}=n},createContext(e,t,r,o){if(t&&n.ctx&&e==n.canvas)return n.ctx;let i,A;if(t){let s={antialias:!1,alpha:!1,majorVersion:typeof WebGL2RenderingContext!="undefined"?2:1};if(o)for(let c in o)s[c]=o[c];typeof p!="undefined"&&(A=p.createContext(e,s),A&&(i=p.getContext(A).GLctx))}else i=e.getContext("2d");return i?(r&&(t||M(typeof w=="undefined","cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),n.ctx=i,t&&p.makeContextCurrent(A),n.useWebGL=t,_.moduleContextCreatedCallbacks.forEach(s=>{s()}),_.init()),i):null},destroyContext(e,t,r){},fullscreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullscreen(e,t){_.lockPointer=e,_.resizeCanvas=t,typeof _.lockPointer=="undefined"&&(_.lockPointer=!0),typeof _.resizeCanvas=="undefined"&&(_.resizeCanvas=!1);let{canvas:r}=n;function o(){_.isFullscreen=!1;let A=r.parentNode;(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===A?(r.exitFullscreen=_.exitFullscreen,_.lockPointer&&r.requestPointerLock(),_.isFullscreen=!0,_.resizeCanvas?_.setFullscreenCanvasSize():_.updateCanvasDimensions(r)):(A.parentNode.insertBefore(r,A),A.parentNode.removeChild(A),_.resizeCanvas?_.setWindowedCanvasSize():_.updateCanvasDimensions(r)),n.onFullScreen&&n.onFullScreen(_.isFullscreen),n.onFullscreen&&n.onFullscreen(_.isFullscreen)}_.fullscreenHandlersInstalled||(_.fullscreenHandlersInstalled=!0);let i=document.createElement("div");r.parentNode.insertBefore(i,r),i.appendChild(r),i.requestFullscreen=i.requestFullscreen||i.mozRequestFullScreen||i.msRequestFullscreen||(i.webkitRequestFullscreen?function(){i.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}:null)||(i.webkitRequestFullScreen?function(){i.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),i.requestFullscreen()},requestFullScreen(){l("Module.requestFullScreen has been replaced by Module.requestFullscreen (without a capital S)")},exitFullscreen(){return _.isFullscreen?((document.exitFullscreen||document.cancelFullScreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitCancelFullScreen||function(){}).apply(document,[]),!0):!1},nextRAF:0,fakeRequestAnimationFrame(e){let t=Date.now();if(_.nextRAF===0)_.nextRAF=t+1e3/60;else for(;t+2>=_.nextRAF;)_.nextRAF+=1e3/60;let r=Math.max(_.nextRAF-t,0);setTimeout(e,r)},requestAnimationFrame(e){if(typeof requestAnimationFrame=="function"){requestAnimationFrame(e);return}let t=_.fakeRequestAnimationFrame;t(e)},safeSetTimeout(e){return qr(e)},safeRequestAnimationFrame(e){return _.requestAnimationFrame(()=>{Tt(e)})},getMimetype(e){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[e.substr(e.lastIndexOf(".")+1)]},getUserMedia(e){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(e)},getMovementX(e){return e.movementX||e.mozMovementX||e.webkitMovementX||0},getMovementY(e){return e.movementY||e.mozMovementY||e.webkitMovementY||0},getMouseWheelDelta(e){let t=0;switch(e.type){case"DOMMouseScroll":t=e.detail/3;break;case"mousewheel":t=e.wheelDelta/120;break;case"wheel":switch(t=e.deltaY,e.deltaMode){case 0:t/=100;break;case 1:t/=3;break;case 2:t*=80;break;default:throw`unrecognized mouse wheel delta mode: ${e.deltaMode}`}break;default:throw`unrecognized mouse wheel event: ${e.type}`}return t},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent(e){if(_.pointerLock)e.type!="mousemove"&&"mozMovementX"in e?_.mouseMovementX=_.mouseMovementY=0:(_.mouseMovementX=_.getMovementX(e),_.mouseMovementY=_.getMovementY(e)),typeof SDL!="undefined"?(_.mouseX=SDL.mouseX+_.mouseMovementX,_.mouseY=SDL.mouseY+_.mouseMovementY):(_.mouseX+=_.mouseMovementX,_.mouseY+=_.mouseMovementY);else{let t=n.canvas.getBoundingClientRect(),r=n.canvas.width,o=n.canvas.height,i=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset,A=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;if(M(typeof i!="undefined"&&typeof A!="undefined","Unable to retrieve scroll position, mouse positions likely broken."),e.type==="touchstart"||e.type==="touchend"||e.type==="touchmove"){let{touch:d}=e;if(d===void 0)return;let E=d.pageX-(i+t.left),I=d.pageY-(A+t.top);E=E*(r/t.width),I=I*(o/t.height);let C={x:E,y:I};if(e.type==="touchstart")_.lastTouches[d.identifier]=C,_.touches[d.identifier]=C;else if(e.type==="touchend"||e.type==="touchmove"){let h=_.touches[d.identifier];h||(h=C),_.lastTouches[d.identifier]=h,_.touches[d.identifier]=C}return}let s=e.pageX-(i+t.left),c=e.pageY-(A+t.top);s=s*(r/t.width),c=c*(o/t.height),_.mouseMovementX=s-_.mouseX,_.mouseMovementY=c-_.mouseY,_.mouseX=s,_.mouseY=c}},resizeListeners:[],updateResizeListeners(){let{canvas:e}=n;_.resizeListeners.forEach(t=>{t(e.width,e.height)})},setCanvasSize(e,t,r){let{canvas:o}=n;_.updateCanvasDimensions(o,e,t),r||_.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize(){if(typeof SDL!="undefined"){let e=ie[SDL.screen>>2];e=e|8388608,m[SDL.screen>>2]=e}_.updateCanvasDimensions(n.canvas),_.updateResizeListeners()},setWindowedCanvasSize(){if(typeof SDL!="undefined"){let e=ie[SDL.screen>>2];e=e&-8388609,m[SDL.screen>>2]=e}_.updateCanvasDimensions(n.canvas),_.updateResizeListeners()},updateCanvasDimensions(e,t,r){t&&r?(e.widthNative=t,e.heightNative=r):(t=e.widthNative,r=e.heightNative);let o=t,i=r;if(n.forcedAspectRatio&&n.forcedAspectRatio>0&&(o/i<n.forcedAspectRatio?o=Math.round(i*n.forcedAspectRatio):i=Math.round(o/n.forcedAspectRatio)),(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===e.parentNode&&typeof screen!="undefined"){let A=Math.min(screen.width/o,screen.height/i);o=Math.round(o*A),i=Math.round(i*A)}_.resizeCanvas?(e.width!=o&&(e.width=o),e.height!=i&&(e.height=i),typeof e.style!="undefined"&&(e.style.removeProperty("width"),e.style.removeProperty("height"))):(e.width!=t&&(e.width=t),e.height!=r&&(e.height=r),typeof e.style!="undefined"&&(o!=t||i!=r?(e.style.setProperty("width",`${o}px`,"important"),e.style.setProperty("height",`${i}px`,"important")):(e.style.removeProperty("width"),e.style.removeProperty("height"))))}};function Cr(e){for(;e.length>0;){let t=e.shift();if(typeof t=="function"){t(n);continue}let{func:r}=t;typeof r=="number"?t.arg===void 0?F.get(r)():F.get(r)(t.arg):r(t.arg===void 0?null:t.arg)}}function Zt(e){return Re("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),e}function en(e){let t=/\b_Z[\w\d_]+/g;return e.replace(t,r=>{let o=Zt(r);return r===o?r:`${o} [${r}]`})}function ro(){let e=new Error;if(!e.stack){try{throw new Error}catch(t){e=t}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}function no(){let e=ro();return n.extraStackTrace&&(e+=`
${n.extraStackTrace()}`),en(e)}function oo(e){return Wt(e+16)+16}function nt(e,t){}function io(e,t){return void 0}function xt(e){this.excPtr=e,this.ptr=e-16,this.set_type=function(t){m[this.ptr+4>>2]=t},this.get_type=function(){return m[this.ptr+4>>2]},this.set_destructor=function(t){m[this.ptr+8>>2]=t},this.get_destructor=function(){return m[this.ptr+8>>2]},this.set_refcount=function(t){m[this.ptr>>2]=t},this.set_caught=function(t){t=t?1:0,ce[this.ptr+12>>0]=t},this.get_caught=function(){return ce[this.ptr+12>>0]!=0},this.set_rethrown=function(t){t=t?1:0,ce[this.ptr+13>>0]=t},this.get_rethrown=function(){return ce[this.ptr+13>>0]!=0},this.init=function(t,r){this.set_type(t),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){let t=m[this.ptr>>2];m[this.ptr>>2]=t+1},this.release_ref=function(){let t=m[this.ptr>>2];return m[this.ptr>>2]=t-1,M(t>0),t===1}}function Et(e){this.free=function(){at(this.ptr),this.ptr=0},this.set_base_ptr=function(t){m[this.ptr>>2]=t},this.get_base_ptr=function(){return m[this.ptr>>2]},this.set_adjusted_ptr=function(t){m[this.ptr+4>>2]=t},this.get_adjusted_ptr_addr=function(){return this.ptr+4},this.get_adjusted_ptr=function(){return m[this.ptr+4>>2]},this.get_exception_ptr=function(){if(ca(this.get_exception_info().get_type()))return m[this.get_base_ptr()>>2];let r=this.get_adjusted_ptr();return r!==0?r:this.get_base_ptr()},this.get_exception_info=function(){return new xt(this.get_base_ptr())},e===void 0?(this.ptr=Wt(8),this.set_adjusted_ptr(0)):this.ptr=e}let Ot=[];function Ao(e){e.add_ref()}let hr=0;function ot(e){let t=new Et(e),r=t.get_exception_info();return r.get_caught()||(r.set_caught(!0),hr--),r.set_rethrown(!1),Ot.push(t),Ao(r),t.get_exception_ptr()}let It=0;function Br(e){try{return at(new xt(e).ptr)}catch(t){J(`exception during cxa_free_exception: ${t}`)}}function tn(e){if(e.release_ref()&&!e.get_rethrown()){let t=e.get_destructor();t&&F.get(t)(e.excPtr),Br(e.excPtr)}}function Lt(){H(0),M(Ot.length>0);let e=Ot.pop();tn(e.get_exception_info()),e.free(),It=0}function so(e){let t=new Et(e),r=t.get_base_ptr();throw It||(It=r),t.free(),`${r} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`}function rn(){let e=It;if(!e)return He(0),0;let r=new xt(e).get_type(),o=new Et;if(o.set_base_ptr(e),o.set_adjusted_ptr(e),!r)return He(0),o.ptr|0;let i=Array.prototype.slice.call(arguments);for(let A=0;A<i.length;A++){let s=i[A];if(s===0||s===r)break;if(ii(s,r,o.get_adjusted_ptr_addr()))return He(s),o.ptr|0}return He(r),o.ptr|0}function mr(){let e=It;if(!e)return He(0),0;let r=new xt(e).get_type(),o=new Et;if(o.set_base_ptr(e),o.set_adjusted_ptr(e),!r)return He(0),o.ptr|0;let i=Array.prototype.slice.call(arguments);for(let A=0;A<i.length;A++){let s=i[A];if(s===0||s===r)break;if(ii(s,r,o.get_adjusted_ptr_addr()))return He(s),o.ptr|0}return He(r),o.ptr|0}function nn(){let e=Ot.pop();e||l("no exception to throw");let t=e.get_exception_info(),r=e.get_base_ptr();throw t.get_rethrown()?e.free():(Ot.push(e),t.set_rethrown(!0),t.set_caught(!1),hr++),It=r,`${r} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`}function ao(e,t){return void 0}function co(e,t,r){throw new xt(e).init(t,r),It=e,hr++,`${e} - Exception catching is disabled, this exception cannot be caught. Compile with -s NO_DISABLE_EXCEPTION_CATCHING or -s EXCEPTION_CATCHING_ALLOWED=[..] to catch.`}function _r(e){return m[oa()>>2]=e,e}var te={splitPath(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray(e,t){let r=0;for(let o=e.length-1;o>=0;o--){let i=e[o];i==="."?e.splice(o,1):i===".."?(e.splice(o,1),r++):r&&(e.splice(o,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize(e){let t=e.charAt(0)==="/",r=e.substr(-1)==="/";return e=te.normalizeArray(e.split("/").filter(o=>!!o),!t).join("/"),!e&&!t&&(e="."),e&&r&&(e+="/"),(t?"/":"")+e},dirname(e){let t=te.splitPath(e),r=t[0],o=t[1];return!r&&!o?".":(o&&(o=o.substr(0,o.length-1)),r+o)},basename(e){if(e==="/")return"/";e=te.normalize(e),e=e.replace(/\/$/,"");let t=e.lastIndexOf("/");return t===-1?e:e.substr(t+1)},extname(e){return te.splitPath(e)[3]},join(){let e=Array.prototype.slice.call(arguments,0);return te.normalize(e.join("/"))},join2(e,t){return te.normalize(`${e}/${t}`)}};function on(){if(typeof crypto=="object"&&typeof crypto.getRandomValues=="function"){let e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(Bt)try{let e=Cc("crypto");return function(){return e.randomBytes(1)[0]}}catch(e){}return function(){l("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: function(array) { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };")}}var Xe={resolve(){let e="",t=!1;for(let r=arguments.length-1;r>=-1&&!t;r--){let o=r>=0?arguments[r]:a.cwd();if(typeof o!="string")throw new TypeError("Arguments to path.resolve must be strings");if(!o)return"";e=`${o}/${e}`,t=o.charAt(0)==="/"}return e=te.normalizeArray(e.split("/").filter(r=>!!r),!t).join("/"),(t?"/":"")+e||"."},relative(e,t){e=Xe.resolve(e).substr(1),t=Xe.resolve(t).substr(1);function r(E){let I=0;for(;I<E.length&&E[I]==="";I++);let C=E.length-1;for(;C>=0&&E[C]==="";C--);return I>C?[]:E.slice(I,C-I+1)}let o=r(e.split("/")),i=r(t.split("/")),A=Math.min(o.length,i.length),s=A;for(var c=0;c<A;c++)if(o[c]!==i[c]){s=c;break}let d=[];for(var c=s;c<o.length;c++)d.push("..");return d=d.concat(i.slice(s)),d.join("/")}},Ee={ttys:[],init(){},shutdown(){},register(e,t){Ee.ttys[e]={input:[],output:[],ops:t},a.registerDevice(e,Ee.stream_ops)},stream_ops:{open(e){let t=Ee.ttys[e.node.rdev];if(!t)throw new a.ErrnoError(43);e.tty=t,e.seekable=!1},close(e){e.tty.ops.flush(e.tty)},flush(e){e.tty.ops.flush(e.tty)},read(e,t,r,o,i){if(!e.tty||!e.tty.ops.get_char)throw new a.ErrnoError(60);let A=0;for(let c=0;c<o;c++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(d){throw new a.ErrnoError(29)}if(s===void 0&&A===0)throw new a.ErrnoError(6);if(s==null)break;A++,t[r+c]=s}return A&&(e.node.timestamp=Date.now()),A},write(e,t,r,o,i){if(!e.tty||!e.tty.ops.put_char)throw new a.ErrnoError(60);try{for(var A=0;A<o;A++)e.tty.ops.put_char(e.tty,t[r+A])}catch(s){throw new a.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),A}},default_tty_ops:{get_char(e){if(!e.input.length){let t=null;if(Bt){let o=Buffer.alloc(256),i=0;try{i=Uo.readSync(process.stdin.fd,o,0,256,null)}catch(A){if(A.toString().includes("EOF"))i=0;else throw A}i>0?t=o.slice(0,i).toString("utf-8"):t=null}else typeof window!="undefined"&&typeof window.prompt=="function"?(t=window.prompt("Input: "),t!==null&&(t+=`
`)):typeof readline=="function"&&(t=readline(),t!==null&&(t+=`
`));if(!t)return null;e.input=Sn(t,!0)}return e.input.shift()},put_char(e,t){t===null||t===10?(ae(yt(e.output,0)),e.output=[]):t!=0&&e.output.push(t)},flush(e){e.output&&e.output.length>0&&(ae(yt(e.output,0)),e.output=[])}},default_tty1_ops:{put_char(e,t){t===null||t===10?(J(yt(e.output,0)),e.output=[]):t!=0&&e.output.push(t)},flush(e){e.output&&e.output.length>0&&(J(yt(e.output,0)),e.output=[])}}};function yr(e,t){ge.fill(0,e,e+t)}function go(e,t){return M(t,"alignment argument is required"),Math.ceil(e/t)*t}function An(e){e=go(e,65536);let t=ga(65536,e);return t?(yr(t,e),t):0}var x={ops_table:null,mount(e){return x.createNode(null,"/",16895,0)},createNode(e,t,r,o){if(a.isBlkdev(r)||a.isFIFO(r))throw new a.ErrnoError(63);x.ops_table||(x.ops_table={dir:{node:{getattr:x.node_ops.getattr,setattr:x.node_ops.setattr,lookup:x.node_ops.lookup,mknod:x.node_ops.mknod,rename:x.node_ops.rename,unlink:x.node_ops.unlink,rmdir:x.node_ops.rmdir,readdir:x.node_ops.readdir,symlink:x.node_ops.symlink},stream:{llseek:x.stream_ops.llseek}},file:{node:{getattr:x.node_ops.getattr,setattr:x.node_ops.setattr},stream:{llseek:x.stream_ops.llseek,read:x.stream_ops.read,write:x.stream_ops.write,allocate:x.stream_ops.allocate,mmap:x.stream_ops.mmap,msync:x.stream_ops.msync}},link:{node:{getattr:x.node_ops.getattr,setattr:x.node_ops.setattr,readlink:x.node_ops.readlink},stream:{}},chrdev:{node:{getattr:x.node_ops.getattr,setattr:x.node_ops.setattr},stream:a.chrdev_stream_ops}});let i=a.createNode(e,t,r,o);return a.isDir(i.mode)?(i.node_ops=x.ops_table.dir.node,i.stream_ops=x.ops_table.dir.stream,i.contents={}):a.isFile(i.mode)?(i.node_ops=x.ops_table.file.node,i.stream_ops=x.ops_table.file.stream,i.usedBytes=0,i.contents=null):a.isLink(i.mode)?(i.node_ops=x.ops_table.link.node,i.stream_ops=x.ops_table.link.stream):a.isChrdev(i.mode)&&(i.node_ops=x.ops_table.chrdev.node,i.stream_ops=x.ops_table.chrdev.stream),i.timestamp=Date.now(),e&&(e.contents[t]=i,e.timestamp=i.timestamp),i},getFileDataAsTypedArray(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage(e,t){let r=e.contents?e.contents.length:0;if(r>=t)return;let o=1024*1024;t=Math.max(t,r*(r<o?2:1.125)>>>0),r!=0&&(t=Math.max(t,256));let i=e.contents;e.contents=new Uint8Array(t),e.usedBytes>0&&e.contents.set(i.subarray(0,e.usedBytes),0)},resizeFileStorage(e,t){if(e.usedBytes!=t)if(t==0)e.contents=null,e.usedBytes=0;else{let r=e.contents;e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t}},node_ops:{getattr(e){let t={};return t.dev=a.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,a.isDir(e.mode)?t.size=4096:a.isFile(e.mode)?t.size=e.usedBytes:a.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr(e,t){t.mode!==void 0&&(e.mode=t.mode),t.timestamp!==void 0&&(e.timestamp=t.timestamp),t.size!==void 0&&x.resizeFileStorage(e,t.size)},lookup(e,t){throw a.genericErrors[44]},mknod(e,t,r,o){return x.createNode(e,t,r,o)},rename(e,t,r){if(a.isDir(e.mode)){let o;try{o=a.lookupNode(t,r)}catch(i){}if(o)for(let i in o.contents)throw new a.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir(e,t){let r=a.lookupNode(e,t);for(let o in r.contents)throw new a.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir(e){let t=[".",".."];for(let r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink(e,t,r){let o=x.createNode(e,t,41471,0);return o.link=r,o},readlink(e){if(!a.isLink(e.mode))throw new a.ErrnoError(28);return e.link}},stream_ops:{read(e,t,r,o,i){let{contents:A}=e.node;if(i>=e.node.usedBytes)return 0;let s=Math.min(e.node.usedBytes-i,o);if(M(s>=0),s>8&&A.subarray)t.set(A.subarray(i,i+s),r);else for(let c=0;c<s;c++)t[r+c]=A[i+c];return s},write(e,t,r,o,i,A){if(M(!(t instanceof ArrayBuffer)),t.buffer===ce.buffer&&(A=!1),!o)return 0;let{node:s}=e;if(s.timestamp=Date.now(),t.subarray&&(!s.contents||s.contents.subarray)){if(A)return M(i===0,"canOwn must imply no weird position inside the file"),s.contents=t.subarray(r,r+o),s.usedBytes=o,o;if(s.usedBytes===0&&i===0)return s.contents=t.slice(r,r+o),s.usedBytes=o,o;if(i+o<=s.usedBytes)return s.contents.set(t.subarray(r,r+o),i),o}if(x.expandFileStorage(s,i+o),s.contents.subarray&&t.subarray)s.contents.set(t.subarray(r,r+o),i);else for(let c=0;c<o;c++)s.contents[i+c]=t[r+c];return s.usedBytes=Math.max(s.usedBytes,i+o),o},llseek(e,t,r){let o=t;if(r===1?o+=e.position:r===2&&a.isFile(e.node.mode)&&(o+=e.node.usedBytes),o<0)throw new a.ErrnoError(28);return o},allocate(e,t,r){x.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap(e,t,r,o,i,A){if(t!==0)throw new a.ErrnoError(28);if(!a.isFile(e.node.mode))throw new a.ErrnoError(43);let s,c,{contents:d}=e.node;if(!(A&2)&&d.buffer===gt)c=!1,s=d.byteOffset;else{if((o>0||o+r<d.length)&&(d.subarray?d=d.subarray(o,o+r):d=Array.prototype.slice.call(d,o,o+r)),c=!0,s=An(r),!s)throw new a.ErrnoError(48);ce.set(d,s)}return{ptr:s,allocated:c}},msync(e,t,r,o,i){if(!a.isFile(e.node.mode))throw new a.ErrnoError(43);if(i&2)return 0;let A=x.stream_ops.write(e,t,0,o,r,!1);return 0}}};function lo(e,t,r,o){let i=o?"":Dt(`al ${e}`);gr(e,A=>{M(A,`Loading data file "${e}" failed (no arrayBuffer).`),t(new Uint8Array(A)),i&&Ut(i)},A=>{if(r)r();else throw`Loading data file "${e}" failed.`}),i&&zt(i)}let Qr={0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"},sn={EPERM:63,ENOENT:44,ESRCH:71,EINTR:27,EIO:29,ENXIO:60,E2BIG:1,ENOEXEC:45,EBADF:8,ECHILD:12,EAGAIN:6,EWOULDBLOCK:6,ENOMEM:48,EACCES:2,EFAULT:21,ENOTBLK:105,EBUSY:10,EEXIST:20,EXDEV:75,ENODEV:43,ENOTDIR:54,EISDIR:31,EINVAL:28,ENFILE:41,EMFILE:33,ENOTTY:59,ETXTBSY:74,EFBIG:22,ENOSPC:51,ESPIPE:70,EROFS:69,EMLINK:34,EPIPE:64,EDOM:18,ERANGE:68,ENOMSG:49,EIDRM:24,ECHRNG:106,EL2NSYNC:156,EL3HLT:107,EL3RST:108,ELNRNG:109,EUNATCH:110,ENOCSI:111,EL2HLT:112,EDEADLK:16,ENOLCK:46,EBADE:113,EBADR:114,EXFULL:115,ENOANO:104,EBADRQC:103,EBADSLT:102,EDEADLOCK:16,EBFONT:101,ENOSTR:100,ENODATA:116,ETIME:117,ENOSR:118,ENONET:119,ENOPKG:120,EREMOTE:121,ENOLINK:47,EADV:122,ESRMNT:123,ECOMM:124,EPROTO:65,EMULTIHOP:36,EDOTDOT:125,EBADMSG:9,ENOTUNIQ:126,EBADFD:127,EREMCHG:128,ELIBACC:129,ELIBBAD:130,ELIBSCN:131,ELIBMAX:132,ELIBEXEC:133,ENOSYS:52,ENOTEMPTY:55,ENAMETOOLONG:37,ELOOP:32,EOPNOTSUPP:138,EPFNOSUPPORT:139,ECONNRESET:15,ENOBUFS:42,EAFNOSUPPORT:5,EPROTOTYPE:67,ENOTSOCK:57,ENOPROTOOPT:50,ESHUTDOWN:140,ECONNREFUSED:14,EADDRINUSE:3,ECONNABORTED:13,ENETUNREACH:40,ENETDOWN:38,ETIMEDOUT:73,EHOSTDOWN:142,EHOSTUNREACH:23,EINPROGRESS:26,EALREADY:7,EDESTADDRREQ:17,EMSGSIZE:35,EPROTONOSUPPORT:66,ESOCKTNOSUPPORT:137,EADDRNOTAVAIL:4,ENETRESET:39,EISCONN:30,ENOTCONN:53,ETOOMANYREFS:141,EUSERS:136,EDQUOT:19,ESTALE:72,ENOTSUP:138,ENOMEDIUM:148,EILSEQ:25,EOVERFLOW:61,ECANCELED:11,ENOTRECOVERABLE:56,EOWNERDEAD:62,ESTRPIPE:135};var a={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(e,t){if(e=Xe.resolve(a.cwd(),e),t=t||{},!e)return{path:"",node:null};let r={follow_mount:!0,recurse_count:0};for(let s in r)t[s]===void 0&&(t[s]=r[s]);if(t.recurse_count>8)throw new a.ErrnoError(32);let o=te.normalizeArray(e.split("/").filter(s=>!!s),!1),i=a.root,A="/";for(let s=0;s<o.length;s++){let c=s===o.length-1;if(c&&t.parent)break;if(i=a.lookupNode(i,o[s]),A=te.join2(A,o[s]),a.isMountpoint(i)&&(!c||c&&t.follow_mount)&&(i=i.mounted.root),!c||t.follow){let d=0;for(;a.isLink(i.mode);){let E=a.readlink(A);if(A=Xe.resolve(te.dirname(A),E),i=a.lookupPath(A,{recurse_count:t.recurse_count}).node,d++>40)throw new a.ErrnoError(32)}}}return{path:A,node:i}},getPath(e){let t;for(;;){if(a.isRoot(e)){let r=e.mount.mountpoint;return t?r[r.length-1]!=="/"?`${r}/${t}`:r+t:r}t=t?`${e.name}/${t}`:e.name,e=e.parent}},hashName(e,t){let r=0;for(let o=0;o<t.length;o++)r=(r<<5)-r+t.charCodeAt(o)|0;return(e+r>>>0)%a.nameTable.length},hashAddNode(e){let t=a.hashName(e.parent.id,e.name);e.name_next=a.nameTable[t],a.nameTable[t]=e},hashRemoveNode(e){let t=a.hashName(e.parent.id,e.name);if(a.nameTable[t]===e)a.nameTable[t]=e.name_next;else{let r=a.nameTable[t];for(;r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}}},lookupNode(e,t){let r=a.mayLookup(e);if(r)throw new a.ErrnoError(r,e);let o=a.hashName(e.id,t);for(let i=a.nameTable[o];i;i=i.name_next){let A=i.name;if(i.parent.id===e.id&&A===t)return i}return a.lookup(e,t)},createNode(e,t,r,o){M(typeof e=="object");let i=new a.FSNode(e,t,r,o);return a.hashAddNode(i),i},destroyNode(e){a.hashRemoveNode(e)},isRoot(e){return e===e.parent},isMountpoint(e){return!!e.mounted},isFile(e){return(e&61440)===32768},isDir(e){return(e&61440)===16384},isLink(e){return(e&61440)===40960},isChrdev(e){return(e&61440)===8192},isBlkdev(e){return(e&61440)===24576},isFIFO(e){return(e&61440)===4096},isSocket(e){return(e&49152)===49152},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags(e){let t=a.flagModes[e];if(typeof t=="undefined")throw new Error(`Unknown file open mode: ${e}`);return t},flagsToPermissionString(e){let t=["r","w","rw"][e&3];return e&512&&(t+="w"),t},nodePermissions(e,t){return a.ignorePermissions?0:t.includes("r")&&!(e.mode&292)||t.includes("w")&&!(e.mode&146)||t.includes("x")&&!(e.mode&73)?2:0},mayLookup(e){let t=a.nodePermissions(e,"x");return t||(e.node_ops.lookup?0:2)},mayCreate(e,t){try{let r=a.lookupNode(e,t);return 20}catch(r){}return a.nodePermissions(e,"wx")},mayDelete(e,t,r){let o;try{o=a.lookupNode(e,t)}catch(A){return A.errno}let i=a.nodePermissions(e,"wx");if(i)return i;if(r){if(!a.isDir(o.mode))return 54;if(a.isRoot(o)||a.getPath(o)===a.cwd())return 10}else if(a.isDir(o.mode))return 31;return 0},mayOpen(e,t){return e?a.isLink(e.mode)?32:a.isDir(e.mode)&&(a.flagsToPermissionString(t)!=="r"||t&512)?31:a.nodePermissions(e,a.flagsToPermissionString(t)):44},MAX_OPEN_FDS:4096,nextfd(e,t){e=e||0,t=t||a.MAX_OPEN_FDS;for(let r=e;r<=t;r++)if(!a.streams[r])return r;throw new a.ErrnoError(33)},getStream(e){return a.streams[e]},createStream(e,t,r){a.FSStream||(a.FSStream=function(){},a.FSStream.prototype={object:{get(){return this.node},set(A){this.node=A}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}}});let o=new a.FSStream;for(let A in e)o[A]=e[A];e=o;let i=a.nextfd(t,r);return e.fd=i,a.streams[i]=e,e},closeStream(e){a.streams[e]=null},chrdev_stream_ops:{open(e){let t=a.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek(){throw new a.ErrnoError(70)}},major(e){return e>>8},minor(e){return e&255},makedev(e,t){return e<<8|t},registerDevice(e,t){a.devices[e]={stream_ops:t}},getDevice(e){return a.devices[e]},getMounts(e){let t=[],r=[e];for(;r.length;){let o=r.pop();t.push(o),r.push.apply(r,o.mounts)}return t},syncfs(e,t){typeof e=="function"&&(t=e,e=!1),a.syncFSRequests++,a.syncFSRequests>1&&J(`warning: ${a.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);let r=a.getMounts(a.root.mount),o=0;function i(s){return M(a.syncFSRequests>0),a.syncFSRequests--,t(s)}function A(s){if(s)return A.errored?void 0:(A.errored=!0,i(s));++o>=r.length&&i(null)}r.forEach(s=>{if(!s.type.syncfs)return A(null);s.type.syncfs(s,e,A)})},mount(e,t,r){if(typeof e=="string")throw e;let o=r==="/",i=!r,A;if(o&&a.root)throw new a.ErrnoError(10);if(!o&&!i){let d=a.lookupPath(r,{follow_mount:!1});if(r=d.path,A=d.node,a.isMountpoint(A))throw new a.ErrnoError(10);if(!a.isDir(A.mode))throw new a.ErrnoError(54)}let s={type:e,opts:t,mountpoint:r,mounts:[]},c=e.mount(s);return c.mount=s,s.root=c,o?a.root=c:A&&(A.mounted=s,A.mount&&A.mount.mounts.push(s)),c},unmount(e){let t=a.lookupPath(e,{follow_mount:!1});if(!a.isMountpoint(t.node))throw new a.ErrnoError(28);let{node:r}=t,o=r.mounted,i=a.getMounts(o);Object.keys(a.nameTable).forEach(s=>{let c=a.nameTable[s];for(;c;){let d=c.name_next;i.includes(c.mount)&&a.destroyNode(c),c=d}}),r.mounted=null;let A=r.mount.mounts.indexOf(o);M(A!==-1),r.mount.mounts.splice(A,1)},lookup(e,t){return e.node_ops.lookup(e,t)},mknod(e,t,r){let i=a.lookupPath(e,{parent:!0}).node,A=te.basename(e);if(!A||A==="."||A==="..")throw new a.ErrnoError(28);let s=a.mayCreate(i,A);if(s)throw new a.ErrnoError(s);if(!i.node_ops.mknod)throw new a.ErrnoError(63);return i.node_ops.mknod(i,A,t,r)},create(e,t){return t=t!==void 0?t:438,t&=4095,t|=32768,a.mknod(e,t,0)},mkdir(e,t){return t=t!==void 0?t:511,t&=1023,t|=16384,a.mknod(e,t,0)},mkdirTree(e,t){let r=e.split("/"),o="";for(let i=0;i<r.length;++i)if(r[i]){o+=`/${r[i]}`;try{a.mkdir(o,t)}catch(A){if(A.errno!=20)throw A}}},mkdev(e,t,r){return typeof r=="undefined"&&(r=t,t=438),t|=8192,a.mknod(e,t,r)},symlink(e,t){if(!Xe.resolve(e))throw new a.ErrnoError(44);let o=a.lookupPath(t,{parent:!0}).node;if(!o)throw new a.ErrnoError(44);let i=te.basename(t),A=a.mayCreate(o,i);if(A)throw new a.ErrnoError(A);if(!o.node_ops.symlink)throw new a.ErrnoError(63);return o.node_ops.symlink(o,i,e)},rename(e,t){let r=te.dirname(e),o=te.dirname(t),i=te.basename(e),A=te.basename(t),s,c,d;if(s=a.lookupPath(e,{parent:!0}),c=s.node,s=a.lookupPath(t,{parent:!0}),d=s.node,!c||!d)throw new a.ErrnoError(44);if(c.mount!==d.mount)throw new a.ErrnoError(75);let E=a.lookupNode(c,i),I=Xe.relative(e,o);if(I.charAt(0)!==".")throw new a.ErrnoError(28);if(I=Xe.relative(t,r),I.charAt(0)!==".")throw new a.ErrnoError(55);let C;try{C=a.lookupNode(d,A)}catch(T){}if(E===C)return;let h=a.isDir(E.mode),y=a.mayDelete(c,i,h);if(y)throw new a.ErrnoError(y);if(y=C?a.mayDelete(d,A,h):a.mayCreate(d,A),y)throw new a.ErrnoError(y);if(!c.node_ops.rename)throw new a.ErrnoError(63);if(a.isMountpoint(E)||C&&a.isMountpoint(C))throw new a.ErrnoError(10);if(d!==c&&(y=a.nodePermissions(c,"w"),y))throw new a.ErrnoError(y);try{a.trackingDelegate.willMovePath&&a.trackingDelegate.willMovePath(e,t)}catch(T){J(`FS.trackingDelegate['willMovePath']('${e}', '${t}') threw an exception: ${T.message}`)}a.hashRemoveNode(E);try{c.node_ops.rename(E,d,A)}catch(T){throw T}finally{a.hashAddNode(E)}try{a.trackingDelegate.onMovePath&&a.trackingDelegate.onMovePath(e,t)}catch(T){J(`FS.trackingDelegate['onMovePath']('${e}', '${t}') threw an exception: ${T.message}`)}},rmdir(e){let r=a.lookupPath(e,{parent:!0}).node,o=te.basename(e),i=a.lookupNode(r,o),A=a.mayDelete(r,o,!0);if(A)throw new a.ErrnoError(A);if(!r.node_ops.rmdir)throw new a.ErrnoError(63);if(a.isMountpoint(i))throw new a.ErrnoError(10);try{a.trackingDelegate.willDeletePath&&a.trackingDelegate.willDeletePath(e)}catch(s){J(`FS.trackingDelegate['willDeletePath']('${e}') threw an exception: ${s.message}`)}r.node_ops.rmdir(r,o),a.destroyNode(i);try{a.trackingDelegate.onDeletePath&&a.trackingDelegate.onDeletePath(e)}catch(s){J(`FS.trackingDelegate['onDeletePath']('${e}') threw an exception: ${s.message}`)}},readdir(e){let t=a.lookupPath(e,{follow:!0}),{node:r}=t;if(!r.node_ops.readdir)throw new a.ErrnoError(54);return r.node_ops.readdir(r)},unlink(e){let r=a.lookupPath(e,{parent:!0}).node,o=te.basename(e),i=a.lookupNode(r,o),A=a.mayDelete(r,o,!1);if(A)throw new a.ErrnoError(A);if(!r.node_ops.unlink)throw new a.ErrnoError(63);if(a.isMountpoint(i))throw new a.ErrnoError(10);try{a.trackingDelegate.willDeletePath&&a.trackingDelegate.willDeletePath(e)}catch(s){J(`FS.trackingDelegate['willDeletePath']('${e}') threw an exception: ${s.message}`)}r.node_ops.unlink(r,o),a.destroyNode(i);try{a.trackingDelegate.onDeletePath&&a.trackingDelegate.onDeletePath(e)}catch(s){J(`FS.trackingDelegate['onDeletePath']('${e}') threw an exception: ${s.message}`)}},readlink(e){let r=a.lookupPath(e).node;if(!r)throw new a.ErrnoError(44);if(!r.node_ops.readlink)throw new a.ErrnoError(28);return Xe.resolve(a.getPath(r.parent),r.node_ops.readlink(r))},stat(e,t){let r=a.lookupPath(e,{follow:!t}),{node:o}=r;if(!o)throw new a.ErrnoError(44);if(!o.node_ops.getattr)throw new a.ErrnoError(63);return o.node_ops.getattr(o)},lstat(e){return a.stat(e,!0)},chmod(e,t,r){let o;if(typeof e=="string"?o=a.lookupPath(e,{follow:!r}).node:o=e,!o.node_ops.setattr)throw new a.ErrnoError(63);o.node_ops.setattr(o,{mode:t&4095|o.mode&-4096,timestamp:Date.now()})},lchmod(e,t){a.chmod(e,t,!0)},fchmod(e,t){let r=a.getStream(e);if(!r)throw new a.ErrnoError(8);a.chmod(r.node,t)},chown(e,t,r,o){let i;if(typeof e=="string"?i=a.lookupPath(e,{follow:!o}).node:i=e,!i.node_ops.setattr)throw new a.ErrnoError(63);i.node_ops.setattr(i,{timestamp:Date.now()})},lchown(e,t,r){a.chown(e,t,r,!0)},fchown(e,t,r){let o=a.getStream(e);if(!o)throw new a.ErrnoError(8);a.chown(o.node,t,r)},truncate(e,t){if(t<0)throw new a.ErrnoError(28);let r;if(typeof e=="string"?r=a.lookupPath(e,{follow:!0}).node:r=e,!r.node_ops.setattr)throw new a.ErrnoError(63);if(a.isDir(r.mode))throw new a.ErrnoError(31);if(!a.isFile(r.mode))throw new a.ErrnoError(28);let o=a.nodePermissions(r,"w");if(o)throw new a.ErrnoError(o);r.node_ops.setattr(r,{size:t,timestamp:Date.now()})},ftruncate(e,t){let r=a.getStream(e);if(!r)throw new a.ErrnoError(8);if(!(r.flags&2097155))throw new a.ErrnoError(28);a.truncate(r.node,t)},utime(e,t,r){let o=a.lookupPath(e,{follow:!0}),{node:i}=o;i.node_ops.setattr(i,{timestamp:Math.max(t,r)})},open(e,t,r,o,i){if(e==="")throw new a.ErrnoError(44);t=typeof t=="string"?a.modeStringToFlags(t):t,r=typeof r=="undefined"?438:r,t&64?r=r&4095|32768:r=0;let A;if(typeof e=="object")A=e;else{e=te.normalize(e);try{A=a.lookupPath(e,{follow:!(t&131072)}).node}catch(d){}}let s=!1;if(t&64)if(A){if(t&128)throw new a.ErrnoError(20)}else A=a.mknod(e,r,0),s=!0;if(!A)throw new a.ErrnoError(44);if(a.isChrdev(A.mode)&&(t&=-513),t&65536&&!a.isDir(A.mode))throw new a.ErrnoError(54);if(!s){let d=a.mayOpen(A,t);if(d)throw new a.ErrnoError(d)}t&512&&a.truncate(A,0),t&=-131713;let c=a.createStream({node:A,path:a.getPath(A),flags:t,seekable:!0,position:0,stream_ops:A.stream_ops,ungotten:[],error:!1},o,i);c.stream_ops.open&&c.stream_ops.open(c),n.logReadFiles&&!(t&1)&&(a.readFiles||(a.readFiles={}),e in a.readFiles||(a.readFiles[e]=1,J(`FS.trackingDelegate error on read file: ${e}`)));try{if(a.trackingDelegate.onOpenFile){let d=0;(t&2097155)!==1&&(d|=a.tracking.openFlags.READ),t&2097155&&(d|=a.tracking.openFlags.WRITE),a.trackingDelegate.onOpenFile(e,d)}}catch(d){J(`FS.trackingDelegate['onOpenFile']('${e}', flags) threw an exception: ${d.message}`)}return c},close(e){if(a.isClosed(e))throw new a.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(t){throw t}finally{a.closeStream(e.fd)}e.fd=null},isClosed(e){return e.fd===null},llseek(e,t,r){if(a.isClosed(e))throw new a.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new a.ErrnoError(70);if(r!=0&&r!=1&&r!=2)throw new a.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read(e,t,r,o,i){if(o<0||i<0)throw new a.ErrnoError(28);if(a.isClosed(e))throw new a.ErrnoError(8);if((e.flags&2097155)===1)throw new a.ErrnoError(8);if(a.isDir(e.node.mode))throw new a.ErrnoError(31);if(!e.stream_ops.read)throw new a.ErrnoError(28);let A=typeof i!="undefined";if(!A)i=e.position;else if(!e.seekable)throw new a.ErrnoError(70);let s=e.stream_ops.read(e,t,r,o,i);return A||(e.position+=s),s},write(e,t,r,o,i,A){if(o<0||i<0)throw new a.ErrnoError(28);if(a.isClosed(e))throw new a.ErrnoError(8);if(!(e.flags&2097155))throw new a.ErrnoError(8);if(a.isDir(e.node.mode))throw new a.ErrnoError(31);if(!e.stream_ops.write)throw new a.ErrnoError(28);e.seekable&&e.flags&1024&&a.llseek(e,0,2);let s=typeof i!="undefined";if(!s)i=e.position;else if(!e.seekable)throw new a.ErrnoError(70);let c=e.stream_ops.write(e,t,r,o,i,A);s||(e.position+=c);try{e.path&&a.trackingDelegate.onWriteToFile&&a.trackingDelegate.onWriteToFile(e.path)}catch(d){J(`FS.trackingDelegate['onWriteToFile']('${e.path}') threw an exception: ${d.message}`)}return c},allocate(e,t,r){if(a.isClosed(e))throw new a.ErrnoError(8);if(t<0||r<=0)throw new a.ErrnoError(28);if(!(e.flags&2097155))throw new a.ErrnoError(8);if(!a.isFile(e.node.mode)&&!a.isDir(e.node.mode))throw new a.ErrnoError(43);if(!e.stream_ops.allocate)throw new a.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap(e,t,r,o,i,A){if(i&2&&!(A&2)&&(e.flags&2097155)!==2)throw new a.ErrnoError(2);if((e.flags&2097155)===1)throw new a.ErrnoError(2);if(!e.stream_ops.mmap)throw new a.ErrnoError(43);return e.stream_ops.mmap(e,t,r,o,i,A)},msync(e,t,r,o,i){return!e||!e.stream_ops.msync?0:e.stream_ops.msync(e,t,r,o,i)},munmap(e){return 0},ioctl(e,t,r){if(!e.stream_ops.ioctl)throw new a.ErrnoError(59);return e.stream_ops.ioctl(e,t,r)},readFile(e,t){if(t=t||{},t.flags=t.flags||0,t.encoding=t.encoding||"binary",t.encoding!=="utf8"&&t.encoding!=="binary")throw new Error(`Invalid encoding type "${t.encoding}"`);let r,o=a.open(e,t.flags),A=a.stat(e).size,s=new Uint8Array(A);return a.read(o,s,0,A,0),t.encoding==="utf8"?r=yt(s,0):t.encoding==="binary"&&(r=s),a.close(o),r},writeFile(e,t,r){r=r||{},r.flags=r.flags||577;let o=a.open(e,r.flags,r.mode);if(typeof t=="string"){let i=new Uint8Array(Ye(t)+1),A=Ge(t,i,0,i.length);a.write(o,i,0,A,void 0,r.canOwn)}else if(ArrayBuffer.isView(t))a.write(o,t,0,t.byteLength,void 0,r.canOwn);else throw new Error("Unsupported data type");a.close(o)},cwd(){return a.currentPath},chdir(e){let t=a.lookupPath(e,{follow:!0});if(t.node===null)throw new a.ErrnoError(44);if(!a.isDir(t.node.mode))throw new a.ErrnoError(54);let r=a.nodePermissions(t.node,"x");if(r)throw new a.ErrnoError(r);a.currentPath=t.path},createDefaultDirectories(){a.mkdir("/tmp"),a.mkdir("/home"),a.mkdir("/home/<USER>")},createDefaultDevices(){a.mkdir("/dev"),a.registerDevice(a.makedev(1,3),{read(){return 0},write(t,r,o,i,A){return i}}),a.mkdev("/dev/null",a.makedev(1,3)),Ee.register(a.makedev(5,0),Ee.default_tty_ops),Ee.register(a.makedev(6,0),Ee.default_tty1_ops),a.mkdev("/dev/tty",a.makedev(5,0)),a.mkdev("/dev/tty1",a.makedev(6,0));let e=on();a.createDevice("/dev","random",e),a.createDevice("/dev","urandom",e),a.mkdir("/dev/shm"),a.mkdir("/dev/shm/tmp")},createSpecialDirectories(){a.mkdir("/proc");let e=a.mkdir("/proc/self");a.mkdir("/proc/self/fd"),a.mount({mount(){let t=a.createNode(e,"fd",16895,73);return t.node_ops={lookup(r,o){let i=+o,A=a.getStream(i);if(!A)throw new a.ErrnoError(8);let s={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink(){return A.path}}};return s.parent=s,s}},t}},{},"/proc/self/fd")},createStandardStreams(){n.stdin?a.createDevice("/dev","stdin",n.stdin):a.symlink("/dev/tty","/dev/stdin"),n.stdout?a.createDevice("/dev","stdout",null,n.stdout):a.symlink("/dev/tty","/dev/stdout"),n.stderr?a.createDevice("/dev","stderr",null,n.stderr):a.symlink("/dev/tty1","/dev/stderr");let e=a.open("/dev/stdin",0),t=a.open("/dev/stdout",1),r=a.open("/dev/stderr",1);M(e.fd===0,`invalid handle for stdin (${e.fd})`),M(t.fd===1,`invalid handle for stdout (${t.fd})`),M(r.fd===2,`invalid handle for stderr (${r.fd})`)},ensureErrnoError(){a.ErrnoError||(a.ErrnoError=function(t,r){this.node=r,this.setErrno=function(o){this.errno=o;for(let i in sn)if(sn[i]===o){this.code=i;break}},this.setErrno(t),this.message=Qr[t],this.stack&&(Object.defineProperty(this,"stack",{value:new Error().stack,writable:!0}),this.stack=en(this.stack))},a.ErrnoError.prototype=new Error,a.ErrnoError.prototype.constructor=a.ErrnoError,[44].forEach(e=>{a.genericErrors[e]=new a.ErrnoError(e),a.genericErrors[e].stack="<generic error, no stack>"}))},staticInit(){a.ensureErrnoError(),a.nameTable=new Array(4096),a.mount(x,{},"/"),a.createDefaultDirectories(),a.createDefaultDevices(),a.createSpecialDirectories(),a.filesystems={MEMFS:x}},init(e,t,r){M(!a.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),a.init.initialized=!0,a.ensureErrnoError(),n.stdin=e||n.stdin,n.stdout=t||n.stdout,n.stderr=r||n.stderr,a.createStandardStreams()},quit(){a.init.initialized=!1;let e=n._fflush;e&&e(0);for(let t=0;t<a.streams.length;t++){let r=a.streams[t];r&&a.close(r)}},getMode(e,t){let r=0;return e&&(r|=365),t&&(r|=146),r},findObject(e,t){let r=a.analyzePath(e,t);return r.exists?r.object:null},analyzePath(e,t){try{var r=a.lookupPath(e,{follow:!t});e=r.path}catch(i){}let o={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var r=a.lookupPath(e,{parent:!0});o.parentExists=!0,o.parentPath=r.path,o.parentObject=r.node,o.name=te.basename(e),r=a.lookupPath(e,{follow:!t}),o.exists=!0,o.path=r.path,o.object=r.node,o.name=r.node.name,o.isRoot=r.path==="/"}catch(i){o.error=i.errno}return o},createPath(e,t,r,o){e=typeof e=="string"?e:a.getPath(e);let i=t.split("/").reverse();for(;i.length;){let s=i.pop();if(s){var A=te.join2(e,s);try{a.mkdir(A)}catch(c){}e=A}}return A},createFile(e,t,r,o,i){let A=te.join2(typeof e=="string"?e:a.getPath(e),t),s=a.getMode(o,i);return a.create(A,s)},createDataFile(e,t,r,o,i,A){let s=t?te.join2(typeof e=="string"?e:a.getPath(e),t):e,c=a.getMode(o,i),d=a.create(s,c);if(r){if(typeof r=="string"){let I=new Array(r.length);for(let C=0,h=r.length;C<h;++C)I[C]=r.charCodeAt(C);r=I}a.chmod(d,c|146);let E=a.open(d,577);a.write(E,r,0,r.length,0,A),a.close(E),a.chmod(d,c)}return d},createDevice(e,t,r,o){let i=te.join2(typeof e=="string"?e:a.getPath(e),t),A=a.getMode(!!r,!!o);a.createDevice.major||(a.createDevice.major=64);let s=a.makedev(a.createDevice.major++,0);return a.registerDevice(s,{open(c){c.seekable=!1},close(c){o&&o.buffer&&o.buffer.length&&o(10)},read(c,d,E,I,C){let h=0;for(let T=0;T<I;T++){var y;try{y=r()}catch(N){throw new a.ErrnoError(29)}if(y===void 0&&h===0)throw new a.ErrnoError(6);if(y==null)break;h++,d[E+T]=y}return h&&(c.node.timestamp=Date.now()),h},write(c,d,E,I,C){for(var h=0;h<I;h++)try{o(d[E+h])}catch(y){throw new a.ErrnoError(29)}return I&&(c.node.timestamp=Date.now()),h}}),a.mkdev(i,A,s)},forceLoadFile(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if(typeof XMLHttpRequest!="undefined")throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(cr)try{e.contents=Sn(cr(e.url),!0),e.usedBytes=e.contents.length}catch(t){throw new a.ErrnoError(29)}else throw new Error("Cannot load without read() or XMLHttpRequest.")},createLazyFile(e,t,r,o,i){function A(){this.lengthKnown=!1,this.chunks=[]}if(A.prototype.get=function(C){if(C>this.length-1||C<0)return;let h=C%this.chunkSize,y=C/this.chunkSize|0;return this.getter(y)[h]},A.prototype.setDataGetter=function(C){this.getter=C},A.prototype.cacheLength=function(){let C=new XMLHttpRequest;if(C.open("HEAD",r,!1),C.send(null),!(C.status>=200&&C.status<300||C.status===304))throw new Error(`Couldn't load ${r}. Status: ${C.status}`);let h=Number(C.getResponseHeader("Content-length")),y,T=(y=C.getResponseHeader("Accept-Ranges"))&&y==="bytes",N=(y=C.getResponseHeader("Content-Encoding"))&&y==="gzip",Y=1024*1024;T||(Y=h);let q=function(S,ne){if(S>ne)throw new Error(`invalid range (${S}, ${ne}) or no bytes requested!`);if(ne>h-1)throw new Error(`only ${h} bytes available! programmer error!`);let U=new XMLHttpRequest;if(U.open("GET",r,!1),h!==Y&&U.setRequestHeader("Range",`bytes=${S}-${ne}`),typeof Uint8Array!="undefined"&&(U.responseType="arraybuffer"),U.overrideMimeType&&U.overrideMimeType("text/plain; charset=x-user-defined"),U.send(null),!(U.status>=200&&U.status<300||U.status===304))throw new Error(`Couldn't load ${r}. Status: ${U.status}`);return U.response!==void 0?new Uint8Array(U.response||[]):Sn(U.responseText||"",!0)},D=this;D.setDataGetter(S=>{let ne=S*Y,U=(S+1)*Y-1;if(U=Math.min(U,h-1),typeof D.chunks[S]=="undefined"&&(D.chunks[S]=q(ne,U)),typeof D.chunks[S]=="undefined")throw new Error("doXHR failed!");return D.chunks[S]}),(N||!h)&&(Y=h=1,h=this.getter(0).length,Y=h,ae("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=h,this._chunkSize=Y,this.lengthKnown=!0},typeof XMLHttpRequest!="undefined"){if(!pe)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";let I=new A;Object.defineProperties(I,{length:{get(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:I}}else var s={isDevice:!1,url:r};let c=a.createFile(e,t,s,o,i);s.contents?c.contents=s.contents:s.url&&(c.contents=null,c.url=s.url),Object.defineProperties(c,{usedBytes:{get(){return this.contents.length}}});let d={};return Object.keys(c.stream_ops).forEach(I=>{let C=c.stream_ops[I];d[I]=function(){return a.forceLoadFile(c),C.apply(null,arguments)}}),d.read=function(C,h,y,T,N){a.forceLoadFile(c);let{contents:Y}=C.node;if(N>=Y.length)return 0;let q=Math.min(Y.length-N,T);if(M(q>=0),Y.slice)for(var D=0;D<q;D++)h[y+D]=Y[N+D];else for(var D=0;D<q;D++)h[y+D]=Y.get(N+D);return q},c.stream_ops=d,c},createPreloadedFile(e,t,r,o,i,A,s,c,d,E){_.init();let I=t?Xe.resolve(te.join2(e,t)):e,C=Dt(`cp ${I}`);function h(y){function T(Y){E&&E(),c||a.createDataFile(e,t,Y,o,i,d),A&&A(),Ut(C)}let N=!1;n.preloadPlugins.forEach(Y=>{N||Y.canHandle(I)&&(Y.handle(y,I,T,()=>{s&&s(),Ut(C)}),N=!0)}),N||T(y)}zt(C),typeof r=="string"?lo(r,y=>{h(y)},s):h(r)},indexedDB(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME(){return`EM_FS_${window.location.pathname}`},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB(e,t,r){t=t||function(){},r=r||function(){};let o=a.indexedDB();try{var i=o.open(a.DB_NAME(),a.DB_VERSION)}catch(A){return r(A)}i.onupgradeneeded=function(){ae("creating db"),i.result.createObjectStore(a.DB_STORE_NAME)},i.onsuccess=function(){let c=i.result.transaction([a.DB_STORE_NAME],"readwrite"),d=c.objectStore(a.DB_STORE_NAME),E=0,I=0,C=e.length;function h(){I==0?t():r()}e.forEach(y=>{let T=d.put(a.analyzePath(y).object.contents,y);T.onsuccess=function(){E++,E+I==C&&h()},T.onerror=function(){I++,E+I==C&&h()}}),c.onerror=r},i.onerror=r},loadFilesFromDB(e,t,r){t=t||function(){},r=r||function(){};let o=a.indexedDB();try{var i=o.open(a.DB_NAME(),a.DB_VERSION)}catch(A){return r(A)}i.onupgradeneeded=r,i.onsuccess=function(){let s=i.result;try{var c=s.transaction([a.DB_STORE_NAME],"readonly")}catch(y){r(y);return}let d=c.objectStore(a.DB_STORE_NAME),E=0,I=0,C=e.length;function h(){I==0?t():r()}e.forEach(y=>{let T=d.get(y);T.onsuccess=function(){a.analyzePath(y).exists&&a.unlink(y),a.createDataFile(te.dirname(y),te.basename(y),T.result,!0,!0,!0),E++,E+I==C&&h()},T.onerror=function(){I++,E+I==C&&h()}}),c.onerror=r},i.onerror=r},absolutePath(){l("FS.absolutePath has been removed; use PATH_FS.resolve instead")},createFolder(){l("FS.createFolder has been removed; use FS.mkdir instead")},createLink(){l("FS.createLink has been removed; use FS.symlink instead")},joinPath(){l("FS.joinPath has been removed; use PATH.join instead")},mmapAlloc(){l("FS.mmapAlloc has been replaced by the top level function mmapAlloc")},standardizePath(){l("FS.standardizePath has been removed; use PATH.normalize instead")}},Ae={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt(e,t,r){if(t[0]==="/")return t;let o;if(e===-100)o=a.cwd();else{let i=a.getStream(e);if(!i)throw new a.ErrnoError(8);o=i.path}if(t.length==0){if(!r)throw new a.ErrnoError(44);return o}return te.join2(o,t)},doStat(e,t,r){try{var o=e(t)}catch(i){if(i&&i.node&&te.normalize(t)!==te.normalize(a.getPath(i.node)))return-54;throw i}return m[r>>2]=o.dev,m[r+4>>2]=0,m[r+8>>2]=o.ino,m[r+12>>2]=o.mode,m[r+16>>2]=o.nlink,m[r+20>>2]=o.uid,m[r+24>>2]=o.gid,m[r+28>>2]=o.rdev,m[r+32>>2]=0,Je=[o.size>>>0,(Be=o.size,+Math.abs(Be)>=1?Be>0?(Math.min(+Math.floor(Be/4294967296),4294967295)|0)>>>0:~~+Math.ceil((Be-+(~~Be>>>0))/4294967296)>>>0:0)],m[r+40>>2]=Je[0],m[r+44>>2]=Je[1],m[r+48>>2]=4096,m[r+52>>2]=o.blocks,m[r+56>>2]=o.atime.getTime()/1e3|0,m[r+60>>2]=0,m[r+64>>2]=o.mtime.getTime()/1e3|0,m[r+68>>2]=0,m[r+72>>2]=o.ctime.getTime()/1e3|0,m[r+76>>2]=0,Je=[o.ino>>>0,(Be=o.ino,+Math.abs(Be)>=1?Be>0?(Math.min(+Math.floor(Be/4294967296),4294967295)|0)>>>0:~~+Math.ceil((Be-+(~~Be>>>0))/4294967296)>>>0:0)],m[r+80>>2]=Je[0],m[r+84>>2]=Je[1],0},doMsync(e,t,r,o,i){let A=ge.slice(e,e+r);a.msync(t,A,i,r,o)},doMkdir(e,t){return e=te.normalize(e),e[e.length-1]==="/"&&(e=e.substr(0,e.length-1)),a.mkdir(e,t,0),0},doMknod(e,t,r){switch(t&61440){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return a.mknod(e,t,r),0},doReadlink(e,t,r){if(r<=0)return-28;let o=a.readlink(e),i=Math.min(r,Ye(o)),A=ce[t+i];return Qt(o,t,r+1),ce[t+i]=A,i},doAccess(e,t){if(t&-8)return-28;let r;if(r=a.lookupPath(e,{follow:!0}).node,!r)return-44;let i="";return t&4&&(i+="r"),t&2&&(i+="w"),t&1&&(i+="x"),i&&a.nodePermissions(r,i)?-2:0},doDup(e,t,r){let o=a.getStream(r);return o&&a.close(o),a.open(e,t,0,r,r).fd},doReadv(e,t,r,o){let i=0;for(let A=0;A<r;A++){let s=m[t+A*8>>2],c=m[t+(A*8+4)>>2],d=a.read(e,ce,s,c,o);if(d<0)return-1;if(i+=d,d<c)break}return i},doWritev(e,t,r,o){let i=0;for(let A=0;A<r;A++){let s=m[t+A*8>>2],c=m[t+(A*8+4)>>2],d=a.write(e,ce,s,c,o);if(d<0)return-1;i+=d}return i},varargs:void 0,get(){return M(Ae.varargs!=null),Ae.varargs+=4,m[Ae.varargs-4>>2]},getStr(e){return Pe(e)},getStreamFromFD(e){let t=a.getStream(e);if(!t)throw new a.ErrnoError(8);return t},get64(e,t){return e>=0?M(t===0):M(t===-1),e}};function uo(e,t,r){Ae.varargs=r;try{let i=Ae.getStreamFromFD(e);switch(t){case 0:{var o=Ae.get();if(o<0)return-28;let A;return A=a.open(i.path,i.flags,0,o),A.fd}case 1:case 2:return 0;case 3:return i.flags;case 4:{var o=Ae.get();return i.flags|=o,0}case 12:{var o=Ae.get();let s=0;return qe[o+s>>1]=2,0}case 13:case 14:return 0;case 16:case 8:return-28;case 9:return _r(28),-1;default:return-28}}catch(i){return(typeof a=="undefined"||!(i instanceof a.ErrnoError))&&l(i),-i.errno}}function fo(e,t,r){Ae.varargs=r;try{let i=Ae.getStreamFromFD(e);switch(t){case 21509:case 21505:return i.tty?0:-59;case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:return i.tty?0:-59;case 21519:{if(!i.tty)return-59;var o=Ae.get();return m[o>>2]=0,0}case 21520:return i.tty?-28:-59;case 21531:{var o=Ae.get();return a.ioctl(i,t,o)}case 21523:return i.tty?0:-59;case 21524:return i.tty?0:-59;default:l(`bad ioctl syscall ${t}`)}}catch(i){return(typeof a=="undefined"||!(i instanceof a.ErrnoError))&&l(i),-i.errno}}function Eo(e,t,r,o,i,A){A<<=12;let s,c=!1;if(o&16&&e%65536!==0)return-28;if(o&32){if(s=An(t),!s)return-48;c=!0}else{let d=a.getStream(i);if(!d)return-8;let E=a.mmap(d,e,t,A,r,o);s=E.ptr,c=E.allocated}return Ae.mappings[s]={malloc:s,len:t,allocated:c,fd:i,prot:r,flags:o,offset:A},s}function Io(e,t,r,o,i,A){try{return Eo(e,t,r,o,i,A)}catch(s){return(typeof a=="undefined"||!(s instanceof a.ErrnoError))&&l(s),-s.errno}}function po(e,t,r){Ae.varargs=r;try{let o=Ae.getStr(e),i=r?Ae.get():0;return a.open(o,t,i).fd}catch(o){return(typeof a=="undefined"||!(o instanceof a.ErrnoError))&&l(o),-o.errno}}function an(e,t){try{return e=Ae.getStr(e),Ae.doStat(a.stat,e,t)}catch(r){return(typeof a=="undefined"||!(r instanceof a.ErrnoError))&&l(r),-r.errno}}let Co=48,ho=57;function qt(e){if(e===void 0)return"_unknown";e=e.replace(/[^a-zA-Z0-9_]/g,"$");let t=e.charCodeAt(0);return t>=Co&&t<=ho?`_${e}`:e}function jt(e,t){return e=qt(e),new Function("body",`return function ${e}() {
    "use strict";    return body.apply(this, arguments);
};
`)(t)}let wr=[],Me=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Bo(){let e=0;for(let t=5;t<Me.length;++t)Me[t]!==void 0&&++e;return e}function mo(){for(let e=5;e<Me.length;++e)if(Me[e]!==void 0)return Me[e];return null}function _o(){n.count_emval_handles=Bo,n.get_first_emval=mo}function er(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{let t=wr.length?wr.pop():Me.length;return Me[t]={refcount:1,value:e},t}}}function tr(e,t){let r=jt(t,function(o){this.name=t,this.message=o;let{stack:i}=new Error(o);i!==void 0&&(this.stack=`${this.toString()}
${i.replace(/^Error(:[^\n]*)?\n/,"")}`)});return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},r}let cn;function Dr(){let e=new Array(256);for(let t=0;t<256;++t)e[t]=String.fromCharCode(t);gn=e}var gn=void 0;function Ce(e){let t="",r=e;for(;ge[r];)t+=gn[ge[r++]];return t}function yo(){return Object.keys(We).length}function ln(){let e=[];for(let t in We)We.hasOwnProperty(t)&&e.push(We[t]);return e}let it=[];function br(){for(;it.length;){let e=it.pop();e.$$.deleteScheduled=!1,e.delete()}}let Ht;function Qo(e){Ht=e,it.length&&Ht&&Ht(br)}function wo(){n.getInheritedInstanceCount=yo,n.getLiveInheritedInstances=ln,n.flushPendingDeletes=br,n.setDelayFunction=Qo}var We={};let Rt;function $(e){throw new Rt(e)}function Tr(e,t){for(t===void 0&&$("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}function Do(e,t,r){t=Tr(e,t),We.hasOwnProperty(t)?$(`Tried to register registered instance: ${t}`):We[t]=r}function un(e){return e||$(`Cannot use deleted val. handle = ${e}`),Me[e].value}let pt={};function dn(e){let t=ia(e),r=Ce(t);return at(t),r}function Or(e,t){let r=pt[e];return r===void 0&&$(`${t} has unknown type ${dn(e)}`),r}function bo(e,t){t=Tr(e,t),We.hasOwnProperty(t)?delete We[t]:$(`Tried to unregister unregistered instance: ${t}`)}function Rr(e){}let Pr=!1;function Mr(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function Sr(e){e.count.value-=1,e.count.value===0&&Mr(e)}function Pt(e){return typeof FinalizationGroup=="undefined"?(Pt=function(t){return t},e):(Pr=new FinalizationGroup(t=>{for(let r=t.next();!r.done;r=t.next()){let o=r.value;o.ptr?Sr(o):console.warn(`object already deleted: ${o.ptr}`)}}),Pt=function(t){return Pr.register(t,t.$$,t.$$),t},Rr=function(t){Pr.unregister(t.$$)},Pt(e))}function To(e,t,r){e=Ce(e),t=Or(t,"wrapper"),r=un(r);let o=[].slice,{registeredClass:i}=t,A=i.instancePrototype,{baseClass:s}=i,c=s.instancePrototype,d=i.baseClass.constructor,E=jt(e,function(){i.baseClass.pureVirtualFunctions.forEach(I=>{if(this[I]===c[I])throw new cn(`Pure virtual function ${I} must be implemented in JavaScript`)}),Object.defineProperty(this,"__parent",{value:A}),this.__construct.apply(this,o.call(arguments))});A.__construct=function(){this===A&&$("Pass correct 'this' to __construct");let C=d.implement.apply(void 0,[this].concat(o.call(arguments)));Rr(C);let{$$:h}=C;C.notifyOnDestruction(),h.preservePointerOnDelete=!0,Object.defineProperties(this,{$$:{value:h}}),Pt(this),Do(i,h.ptr,this)},A.__destruct=function(){this===A&&$("Pass correct 'this' to __destruct"),Rr(this),bo(i,this.$$.ptr)},E.prototype=Object.create(A);for(let I in r)E.prototype[I]=r[I];return er(E)}let Se={};function rr(e){for(;e.length;){let t=e.pop();e.pop()(t)}}function nr(e){return this.fromWireType(ie[e>>2])}let Mt={},or={},Gt;function Yt(e){throw new Gt(e)}function g(e,t,r){e.forEach(c=>{or[c]=t});function o(c){let d=r(c);d.length!==e.length&&Yt("Mismatched type converter count");for(let E=0;E<e.length;++E)Q(e[E],d[E])}let i=new Array(t.length),A=[],s=0;t.forEach((c,d)=>{pt.hasOwnProperty(c)?i[d]=pt[c]:(A.push(c),Mt.hasOwnProperty(c)||(Mt[c]=[]),Mt[c].push(()=>{i[d]=pt[c],++s,s===A.length&&o(i)}))}),A.length===0&&o(i)}function u(e){let t=Se[e];delete Se[e];let{rawConstructor:r}=t,{rawDestructor:o}=t,i=t.fields,A=i.map(s=>s.getterReturnType).concat(i.map(s=>s.setterArgumentType));g([e],A,s=>{let c={};return i.forEach((d,E)=>{let{fieldName:I}=d,C=s[E],{getter:h}=d,{getterContext:y}=d,T=s[E+i.length],{setter:N}=d,{setterContext:Y}=d;c[I]={read(q){return C.fromWireType(h(y,q))},write(q,D){let S=[];N(Y,q,T.toWireType(S,D)),rr(S)}}}),[{name:t.name,fromWireType(d){let E={};for(let I in c)E[I]=c[I].read(d);return o(d),E},toWireType(d,E){for(var I in c)if(!(I in E))throw new TypeError(`Missing field:  "${I}"`);let C=r();for(I in c)c[I].write(C,E[I]);return d!==null&&d.push(o,C),C},argPackAdvance:8,readValueFromPointer:nr,destructorFunction:o}]})}function f(e,t,r,o,i){}function B(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}function Q(e,t,r){if(r=r||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");let{name:o}=t;if(e||$(`type "${o}" must have a positive integer typeid pointer`),pt.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;$(`Cannot register type '${o}' twice`)}if(pt[e]=t,delete or[e],Mt.hasOwnProperty(e)){let i=Mt[e];delete Mt[e],i.forEach(A=>{A()})}}function O(e,t,r,o,i){let A=B(r);t=Ce(t),Q(e,{name:t,fromWireType(s){return!!s},toWireType(s,c){return c?o:i},argPackAdvance:8,readValueFromPointer(s){let c;if(r===1)c=ce;else if(r===2)c=qe;else if(r===4)c=m;else throw new TypeError(`Unknown boolean type size: ${t}`);return this.fromWireType(c[s>>A])},destructorFunction:null})}function P(e){if(!(this instanceof oe)||!(e instanceof oe))return!1;let t=this.$$.ptrType.registeredClass,r=this.$$.ptr,o=e.$$.ptrType.registeredClass,i=e.$$.ptr;for(;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;o.baseClass;)i=o.upcast(i),o=o.baseClass;return t===o&&r===i}function b(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function v(e){function t(r){return r.$$.ptrType.registeredClass.name}$(`${t(e)} instance already deleted`)}function k(){if(this.$$.ptr||v(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;let e=Pt(Object.create(Object.getPrototypeOf(this),{$$:{value:b(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function G(){this.$$.ptr||v(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&$("Object already scheduled for deletion"),Rr(this),Sr(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function K(){return!this.$$.ptr}function ee(){return this.$$.ptr||v(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&$("Object already scheduled for deletion"),it.push(this),it.length===1&&Ht&&Ht(br),this.$$.deleteScheduled=!0,this}function z(){oe.prototype.isAliasOf=P,oe.prototype.clone=k,oe.prototype.delete=G,oe.prototype.isDeleted=K,oe.prototype.deleteLater=ee}function oe(){}let we={};function le(e,t,r){if(e[t].overloadTable===void 0){let o=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||$(`Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].overloadTable})!`),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[o.argCount]=o}}function xe(e,t,r){n.hasOwnProperty(e)?((r===void 0||n[e].overloadTable!==void 0&&n[e].overloadTable[r]!==void 0)&&$(`Cannot register public name '${e}' twice`),le(n,e,e),n.hasOwnProperty(r)&&$(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`),n[e].overloadTable[r]=t):(n[e]=t,r!==void 0&&(n[e].numArguments=r))}function Ct(e,t,r,o,i,A,s,c){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=o,this.baseClass=i,this.getActualType=A,this.upcast=s,this.downcast=c,this.pureVirtualFunctions=[]}function he(e,t,r){for(;t!==r;)t.upcast||$(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.upcast(e),t=t.baseClass;return e}function At(e,t){if(t===null)return this.isReference&&$(`null is not a valid ${this.name}`),0;t.$$||$(`Cannot pass "${Ar(t)}" as a ${this.name}`),t.$$.ptr||$(`Cannot pass deleted object as a pointer of type ${this.name}`);let r=t.$$.ptrType.registeredClass;return he(t.$$.ptr,r,this.registeredClass)}function ir(e,t){let r;if(t===null)return this.isReference&&$(`null is not a valid ${this.name}`),this.isSmartPointer?(r=this.rawConstructor(),e!==null&&e.push(this.rawDestructor,r),r):0;t.$$||$(`Cannot pass "${Ar(t)}" as a ${this.name}`),t.$$.ptr||$(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&t.$$.ptrType.isConst&&$(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);let o=t.$$.ptrType.registeredClass;if(r=he(t.$$.ptr,o,this.registeredClass),this.isSmartPointer)switch(t.$$.smartPtr===void 0&&$("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:$(`Cannot convert argument of type ${t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{let i=t.clone();r=this.rawShare(r,er(()=>{i.delete()})),e!==null&&e.push(this.rawDestructor,r)}break;default:$("Unsupporting sharing policy")}return r}function fn(e,t){if(t===null)return this.isReference&&$(`null is not a valid ${this.name}`),0;t.$$||$(`Cannot pass "${Ar(t)}" as a ${this.name}`),t.$$.ptr||$(`Cannot pass deleted object as a pointer of type ${this.name}`),t.$$.ptrType.isConst&&$(`Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`);let r=t.$$.ptrType.registeredClass;return he(t.$$.ptr,r,this.registeredClass)}function En(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function pi(e){this.rawDestructor&&this.rawDestructor(e)}function Ci(e){e!==null&&e.delete()}function jo(e,t,r){if(t===r)return e;if(r.baseClass===void 0)return null;let o=jo(e,t,r.baseClass);return o===null?null:r.downcast(o)}function hi(e,t){return t=Tr(e,t),We[t]}function In(e,t){(!t.ptrType||!t.ptr)&&Yt("makeClassHandle requires ptr and ptrType");let r=!!t.smartPtrType,o=!!t.smartPtr;return r!==o&&Yt("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Pt(Object.create(e,{$$:{value:t}}))}function Bi(e){let t=this.getPointee(e);if(!t)return this.destructor(e),null;let r=hi(this.registeredClass,t);if(r!==void 0){if(r.$$.count.value===0)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();let d=r.clone();return this.destructor(e),d}function o(){return this.isSmartPointer?In(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):In(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}let i=this.registeredClass.getActualType(t),A=we[i];if(!A)return o.call(this);let s;this.isConst?s=A.constPointerType:s=A.pointerType;let c=jo(t,this.registeredClass,s.registeredClass);return c===null?o.call(this):this.isSmartPointer?In(s.registeredClass.instancePrototype,{ptrType:s,ptr:c,smartPtrType:this,smartPtr:e}):In(s.registeredClass.instancePrototype,{ptrType:s,ptr:c})}function mi(){ht.prototype.getPointee=En,ht.prototype.destructor=pi,ht.prototype.argPackAdvance=8,ht.prototype.readValueFromPointer=nr,ht.prototype.deleteObject=Ci,ht.prototype.fromWireType=Bi}function ht(e,t,r,o,i,A,s,c,d,E,I){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=o,this.isSmartPointer=i,this.pointeeType=A,this.sharingPolicy=s,this.rawGetPointee=c,this.rawConstructor=d,this.rawShare=E,this.rawDestructor=I,!i&&t.baseClass===void 0?o?(this.toWireType=At,this.destructorFunction=null):(this.toWireType=fn,this.destructorFunction=null):this.toWireType=ir}function Ho(e,t,r){n.hasOwnProperty(e)||Yt("Replacing nonexistant public symbol"),n[e].overloadTable!==void 0&&r!==void 0?n[e].overloadTable[r]=t:(n[e]=t,n[e].argCount=r)}function _i(e,t,r){M(`dynCall_${e}`in n,`bad function pointer type - no table for sig '${e}'`),r&&r.length?M(r.length===e.substring(1).replace(/j/g,"--").length):M(e.length==1);let o=n[`dynCall_${e}`];return r&&r.length?o.apply(null,[t].concat(r)):o.call(null,t)}function yi(e,t,r){return e.includes("j")?_i(e,t,r):(M(F.get(t),`missing table entry in dynCall: ${t}`),F.get(t).apply(null,r))}function Qi(e,t){M(e.includes("j"),"getDynCaller should only be called with i64 sigs");let r=[];return function(){r.length=arguments.length;for(let o=0;o<arguments.length;o++)r[o]=arguments[o];return yi(e,t,r)}}function Fe(e,t){e=Ce(e);function r(){return e.includes("j")?Qi(e,t):F.get(t)}let o=r();return typeof o!="function"&&$(`unknown function pointer with signature ${e}: ${t}`),o}let Go;function Xt(e,t){let r=[],o={};function i(A){if(!o[A]&&!pt[A]){if(or[A]){or[A].forEach(i);return}r.push(A),o[A]=!0}}throw t.forEach(i),new Go(`${e}: ${r.map(dn).join([", "])}`)}function wi(e,t,r,o,i,A,s,c,d,E,I,C,h){I=Ce(I),A=Fe(i,A),c&&(c=Fe(s,c)),E&&(E=Fe(d,E)),h=Fe(C,h);let y=qt(I);xe(y,()=>{Xt(`Cannot construct ${I} due to unbound types`,[o])}),g([e,t,r],o?[o]:[],T=>{T=T[0];let N,Y;o?(N=T.registeredClass,Y=N.instancePrototype):Y=oe.prototype;let q=jt(y,function(){if(Object.getPrototypeOf(this)!==D)throw new Rt(`Use 'new' to construct ${I}`);if(S.constructor_body===void 0)throw new Rt(`${I} has no accessible constructor`);let Te=S.constructor_body[arguments.length];if(Te===void 0)throw new Rt(`Tried to invoke ctor of ${I} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(S.constructor_body).toString()}) parameters instead!`);return Te.apply(this,arguments)});var D=Object.create(Y,{constructor:{value:q}});q.prototype=D;var S=new Ct(I,q,D,h,N,A,c,E);let ne=new ht(I,S,!0,!1,!1),U=new ht(`${I}*`,S,!1,!1,!1),me=new ht(`${I} const*`,S,!1,!0,!1);return we[e]={pointerType:U,constPointerType:me},Ho(y,q),[ne,U,me]})}function Yo(e,t){if(!(e instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof e} which is not a function`);let r=jt(e.name||"unknownFunctionName",()=>{});r.prototype=e.prototype;let o=new r,i=e.apply(o,t);return i instanceof Object?i:o}function pn(e,t,r,o,i){let A=t.length;A<2&&$("argTypes array size mismatch! Must at least get return value and 'this' types!");let s=t[1]!==null&&r!==null,c=!1;for(var d=1;d<t.length;++d)if(t[d]!==null&&t[d].destructorFunction===void 0){c=!0;break}let E=t[0].name!=="void",I="",C="";for(var d=0;d<A-2;++d)I+=`${d!==0?", ":""}arg${d}`,C+=`${d!==0?", ":""}arg${d}Wired`;let h=`return function ${qt(e)}(${I}) {
if (arguments.length !== ${A-2}) {
throwBindingError('function ${e} called with ' + arguments.length + ' arguments, expected ${A-2} args!');
}
`;c&&(h+=`var destructors = [];
`);let y=c?"destructors":"null",T=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],N=[$,o,i,rr,t[0],t[1]];s&&(h+=`var thisWired = classParam.toWireType(${y}, this);
`);for(var d=0;d<A-2;++d)h+=`var arg${d}Wired = argType${d}.toWireType(${y}, arg${d}); // ${t[d+2].name}
`,T.push(`argType${d}`),N.push(t[d+2]);if(s&&(C=`thisWired${C.length>0?", ":""}${C}`),h+=`${E?"var rv = ":""}invoker(fn${C.length>0?", ":""}${C});
`,c)h+=`runDestructors(destructors);
`;else for(var d=s?1:2;d<t.length;++d){let D=d===1?"thisWired":`arg${d-2}Wired`;t[d].destructorFunction!==null&&(h+=`${D}_dtor(${D}); // ${t[d].name}
`,T.push(`${D}_dtor`),N.push(t[d].destructorFunction))}return E&&(h+=`var ret = retType.fromWireType(rv);
return ret;
`),h+=`}
`,T.push(h),Yo(Function,T).apply(null,N)}function Cn(e,t){let r=[];for(let o=0;o<e;o++)r.push(m[(t>>2)+o]);return r}function Di(e,t,r,o,i,A,s){let c=Cn(r,o);t=Ce(t),A=Fe(i,A),g([],[e],d=>{d=d[0];let E=`${d.name}.${t}`;function I(){Xt(`Cannot call ${E} due to unbound types`,c)}t.startsWith("@@")&&(t=Symbol[t.substring(2)]);let C=d.registeredClass.constructor;return C[t]===void 0?(I.argCount=r-1,C[t]=I):(le(C,t,E),C[t].overloadTable[r-1]=I),g([],c,h=>{let y=[h[0],null].concat(h.slice(1)),T=pn(E,y,null,A,s);return C[t].overloadTable===void 0?(T.argCount=r-1,C[t]=T):C[t].overloadTable[r-1]=T,[]}),[]})}function bi(e,t,r,o,i,A){M(t>0);let s=Cn(t,r);i=Fe(o,i),g([],[e],c=>{c=c[0];let d=`constructor ${c.name}`;if(c.registeredClass.constructor_body===void 0&&(c.registeredClass.constructor_body=[]),c.registeredClass.constructor_body[t-1]!==void 0)throw new Rt(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${c.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return c.registeredClass.constructor_body[t-1]=function(){Xt(`Cannot construct ${c.name} due to unbound types`,s)},g([],s,E=>(E.splice(1,0,null),c.registeredClass.constructor_body[t-1]=pn(d,E,null,i,A),[])),[]})}function Ti(e,t,r,o,i,A,s,c){let d=Cn(r,o);t=Ce(t),A=Fe(i,A),g([],[e],E=>{E=E[0];let I=`${E.name}.${t}`;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),c&&E.registeredClass.pureVirtualFunctions.push(t);function C(){Xt(`Cannot call ${I} due to unbound types`,d)}let h=E.registeredClass.instancePrototype,y=h[t];return y===void 0||y.overloadTable===void 0&&y.className!==E.name&&y.argCount===r-2?(C.argCount=r-2,C.className=E.name,h[t]=C):(le(h,t,I),h[t].overloadTable[r-2]=C),g([],d,T=>{let N=pn(I,T,E,A,s);return h[t].overloadTable===void 0?(N.argCount=r-2,h[t]=N):h[t].overloadTable[r-2]=N,[]}),[]})}function Xo(e,t,r){return e instanceof Object||$(`${r} with invalid "this": ${e}`),e instanceof t.registeredClass.constructor||$(`${r} incompatible with "this" of type ${e.constructor.name}`),e.$$.ptr||$(`cannot call emscripten binding method ${r} on deleted object`),he(e.$$.ptr,e.$$.ptrType.registeredClass,t.registeredClass)}function Oi(e,t,r,o,i,A,s,c,d,E){t=Ce(t),i=Fe(o,i),g([],[e],I=>{I=I[0];let C=`${I.name}.${t}`,h={get(){Xt(`Cannot access ${C} due to unbound types`,[r,s])},enumerable:!0,configurable:!0};return d?h.set=function(){Xt(`Cannot access ${C} due to unbound types`,[r,s])}:h.set=function(y){$(`${C} is a read-only property`)},Object.defineProperty(I.registeredClass.instancePrototype,t,h),g([],d?[r,s]:[r],y=>{let T=y[0],N={get(){let Y=Xo(this,I,`${C} getter`);return T.fromWireType(i(A,Y))},enumerable:!0};if(d){d=Fe(c,d);let Y=y[1];N.set=function(q){let D=Xo(this,I,`${C} setter`),S=[];d(E,D,Y.toWireType(S,q)),rr(S)}}return Object.defineProperty(I.registeredClass.instancePrototype,t,N),[]}),[]})}function Wo(e){e>4&&--Me[e].refcount===0&&(Me[e]=void 0,wr.push(e))}function Ri(e,t){t=Ce(t),Q(e,{name:t,fromWireType(r){let o=Me[r].value;return Wo(r),o},toWireType(r,o){return er(o)},argPackAdvance:8,readValueFromPointer:nr,destructorFunction:null})}function Ar(e){if(e===null)return"null";let t=typeof e;return t==="object"||t==="array"||t==="function"?e.toString():`${e}`}function Pi(e,t){switch(t){case 2:return function(r){return this.fromWireType(ye[r>>2])};case 3:return function(r){return this.fromWireType(Jt[r>>3])};default:throw new TypeError(`Unknown float type: ${e}`)}}function Mi(e,t,r){let o=B(r);t=Ce(t),Q(e,{name:t,fromWireType(i){return i},toWireType(i,A){if(typeof A!="number"&&typeof A!="boolean")throw new TypeError(`Cannot convert "${Ar(A)}" to ${this.name}`);return A},argPackAdvance:8,readValueFromPointer:Pi(t,o),destructorFunction:null})}function Si(e,t,r,o,i,A){let s=Cn(t,r);e=Ce(e),i=Fe(o,i),xe(e,()=>{Xt(`Cannot call ${e} due to unbound types`,s)},t-1),g([],s,c=>{let d=[c[0],null].concat(c.slice(1));return Ho(e,pn(e,d,null,i,A),t-1),[]})}function Fi(e,t,r){switch(t){case 0:return r?function(i){return ce[i]}:function(i){return ge[i]};case 1:return r?function(i){return qe[i>>1]}:function(i){return kt[i>>1]};case 2:return r?function(i){return m[i>>2]}:function(i){return ie[i>>2]};default:throw new TypeError(`Unknown integer type: ${e}`)}}function vi(e,t,r,o,i){t=Ce(t),i===-1&&(i=4294967295);let A=B(r),s=function(d){return d};if(o===0){let d=32-8*r;s=function(E){return E<<d>>>d}}let c=t.includes("unsigned");Q(e,{name:t,fromWireType:s,toWireType(d,E){if(typeof E!="number"&&typeof E!="boolean")throw new TypeError(`Cannot convert "${Ar(E)}" to ${this.name}`);if(E<o||E>i)throw new TypeError(`Passing a number "${Ar(E)}" from JS side to C/C++ side to an argument of type "${t}", which is outside the valid range [${o}, ${i}]!`);return c?E>>>0:E|0},argPackAdvance:8,readValueFromPointer:Fi(t,A,o!==0),destructorFunction:null})}function ki(e,t,r){let i=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function A(s){s=s>>2;let c=ie,d=c[s],E=c[s+1];return new i(gt,E,d)}r=Ce(r),Q(e,{name:r,fromWireType:A,argPackAdvance:8,readValueFromPointer:A},{ignoreDuplicateRegistrations:!0})}function Ni(e,t){t=Ce(t);let r=t==="std::string";Q(e,{name:t,fromWireType(o){let i=ie[o>>2],A;if(r){let c=o+4;for(var s=0;s<=i;++s){let d=o+4+s;if(s==i||ge[d]==0){let E=d-c,I=Pe(c,E);A===void 0?A=I:(A+=String.fromCharCode(0),A+=I),c=d+1}}}else{let c=new Array(i);for(var s=0;s<i;++s)c[s]=String.fromCharCode(ge[o+4+s]);A=c.join("")}return at(o),A},toWireType(o,i){i instanceof ArrayBuffer&&(i=new Uint8Array(i));let A,s=typeof i=="string";s||i instanceof Uint8Array||i instanceof Uint8ClampedArray||i instanceof Int8Array||$("Cannot pass non-string to std::string"),r&&s?A=function(){return Ye(i)}:A=function(){return i.length};let c=A(),d=Wt(4+c+1);if(ie[d>>2]=c,r&&s)Qt(i,d+4,c+1);else if(s)for(var E=0;E<c;++E){let I=i.charCodeAt(E);I>255&&(at(d),$("String has UTF-16 code units that do not fit in 8 bits")),ge[d+4+E]=I}else for(var E=0;E<c;++E)ge[d+4+E]=i[E];return o!==null&&o.push(at,d),d},argPackAdvance:8,readValueFromPointer:nr,destructorFunction(o){at(o)}})}function Ui(e,t,r){r=Ce(r);let o,i,A,s,c;t===2?(o=Gn,i=Kt,s=Hr,A=function(){return kt},c=1):t===4&&(o=Gr,i=wt,s=Yn,A=function(){return ie},c=2),Q(e,{name:r,fromWireType(d){let E=ie[d>>2],I=A(),C,h=d+4;for(let y=0;y<=E;++y){let T=d+4+y*t;if(y==E||I[T>>c]==0){let N=T-h,Y=o(h,N);C===void 0?C=Y:(C+=String.fromCharCode(0),C+=Y),h=T+t}}return at(d),C},toWireType(d,E){typeof E!="string"&&$(`Cannot pass non-string to C++ string type ${r}`);let I=s(E),C=Wt(4+I+t);return ie[C>>2]=I>>c,i(E,C+4,I+t),d!==null&&d.push(at,C),C},argPackAdvance:8,readValueFromPointer:nr,destructorFunction(d){at(d)}})}function xi(e,t,r,o,i,A){Se[e]={name:Ce(t),rawConstructor:Fe(r,o),rawDestructor:Fe(i,A),fields:[]}}function Li(e,t,r,o,i,A,s,c,d,E){Se[e].fields.push({fieldName:Ce(t),getterReturnType:r,getter:Fe(o,i),getterContext:A,setterArgumentType:s,setter:Fe(c,d),setterContext:E})}function ji(e,t){t=Ce(t),Q(e,{isVoid:!0,name:t,argPackAdvance:0,fromWireType(){},toWireType(r,o){}})}let Hi={};function Gi(e){let t=Hi[e];return t===void 0?Ce(e):t}let Oo=[];function Yi(e,t,r,o){e=Oo[e],t=un(t),r=Gi(r),e(t,r,null,o)}function Xi(e){let t=Oo.length;return Oo.push(e),t}function Wi(e,t){let r=new Array(e);for(let o=0;o<e;++o)r[o]=Or(m[(t>>2)+o],`parameter ${o}`);return r}function $i(e,t){let r=Wi(e,t),o=r[0],i=`${o.name}_$${r.slice(1).map(y=>y.name).join("_")}$`,A=["retType"],s=[o],c="";for(var d=0;d<e-1;++d)c+=`${d!==0?", ":""}arg${d}`,A.push(`argType${d}`),s.push(r[1+d]);let I=`return function ${qt(`methodCaller_${i}`)}(handle, name, destructors, args) {
`,C=0;for(var d=0;d<e-1;++d)I+=`    var arg${d} = argType${d}.readValueFromPointer(args${C?`+${C}`:""});
`,C+=r[d+1].argPackAdvance;I+=`    var rv = handle[name](${c});
`;for(var d=0;d<e-1;++d)r[d+1].deleteObject&&(I+=`    argType${d}.deleteObject(arg${d});
`);o.isVoid||(I+=`    return retType.toWireType(destructors, rv);
`),I+=`};
`,A.push(I);let h=Yo(Function,A).apply(null,s);return Xi(h)}function Vi(e){e>4&&(Me[e].refcount+=1)}function Ki(e,t){e=Or(e,"_emval_take_value");let r=e.readValueFromPointer(t);return er(r)}function Ji(){l()}let zi=!0;function Zi(e,t){let r;if(e===0)r=Date.now();else if((e===1||e===4)&&zi)r=rt();else return _r(28),-1;return m[t>>2]=r/1e3|0,m[t+4>>2]=r%1e3*1e3*1e3|0,0}function qi(e,t){l("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")}function eA(e,t){l("To use dlopen, you need to use Emscripten's linking support, see https://github.com/emscripten-core/emscripten/wiki/Linking")}let hn=[];function tA(e,t){M(Array.isArray(hn)),M(t%16==0),hn.length=0;let r;for(t>>=2;r=ge[e++];){M(r===100||r===102||r===105);let o=r<105;o&&t&1&&t++,hn.push(o?Jt[t++>>1]:m[t]),++t}return hn}function rA(e,t,r){let o=tA(t,r);return ze.hasOwnProperty(e)||l(`No EM_ASM constant found at address ${e}`),ze[e].apply(null,o)}function nA(){return 2147483648}function oA(e,t,r){ge.copyWithin(e,t,t+r)}function iA(e){l("Cannot use emscripten_pc_get_function without -s USE_OFFSET_CONVERTER")}function AA(e){try{return _t.grow(e-gt.byteLength+65535>>>16),Qe(_t.buffer),1}catch(t){J(`emscripten_realloc_buffer: Attempted to grow heap from ${gt.byteLength} bytes to ${e} bytes, but got error: ${t}`)}}function sA(e){let t=ge.length;e=e>>>0,M(e>t);let r=2147483648;if(e>r)return J(`Cannot enlarge memory, asked to go up to ${e} bytes, but the limit is ${r} bytes!`),!1;for(let i=1;i<=4;i*=2){let A=t*(1+.2/i);A=Math.min(A,e+100663296);var o=Math.min(r,Wn(Math.max(e,A),65536));if(AA(o))return!0}return J(`Failed to grow the heap from ${t} bytes to ${o} bytes, not enough memory!`),!1}function Bn(e){l("Cannot use emscripten_generate_pc (needed by __builtin_return_address) without -s USE_OFFSET_CONVERTER")}let sr={};function $o(e){e.forEach(t=>{let r=Bn(t);r&&(sr[r]=t)})}function aA(){let e=new Error().stack.split(`
`);return e[0]=="Error"&&e.shift(),$o(e),sr.last_addr=Bn(e[2]),sr.last_stack=e,sr.last_addr}function cA(e,t,r){let o;sr.last_addr==e?o=sr.last_stack:(o=new Error().stack.split(`
`),o[0]=="Error"&&o.shift(),$o(o));let i=2;for(;o[i]&&Bn(o[i])!=e;)++i;for(var A=0;A<r&&o[A+i];++A)m[t+A*4>>2]=Bn(o[A+i]);return A}function gA(e){let t=rt();for(;rt()-t<e;);}function lA(e){let t=e.getExtension("ANGLE_instanced_arrays");if(t)return e.vertexAttribDivisor=function(r,o){t.vertexAttribDivisorANGLE(r,o)},e.drawArraysInstanced=function(r,o,i,A){t.drawArraysInstancedANGLE(r,o,i,A)},e.drawElementsInstanced=function(r,o,i,A,s){t.drawElementsInstancedANGLE(r,o,i,A,s)},1}function uA(e){let t=e.getExtension("OES_vertex_array_object");if(t)return e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(r){t.deleteVertexArrayOES(r)},e.bindVertexArray=function(r){t.bindVertexArrayOES(r)},e.isVertexArray=function(r){return t.isVertexArrayOES(r)},1}function dA(e){let t=e.getExtension("WEBGL_draw_buffers");if(t)return e.drawBuffers=function(r,o){t.drawBuffersWEBGL(r,o)},1}function fA(e){return!!(e.dibvbi=e.getExtension("WEBGL_draw_instanced_base_vertex_base_instance"))}function EA(e){return!!(e.mdibvbi=e.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance"))}function IA(e){return!!(e.multiDrawWebgl=e.getExtension("WEBGL_multi_draw"))}var p={counter:1,buffers:[],mappedBuffers:{},programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:[],offscreenCanvases:{},queries:[],samplers:[],transformFeedbacks:[],syncs:[],byteSizeByTypeRoot:5120,byteSizeByType:[1,1,2,2,4,4,4,2,3,4,8],stringCache:{},stringiCache:{},unpackAlignment:4,recordError:function(t){p.lastError||(p.lastError=t)},getNewId(e){let t=p.counter++;for(let r=e.length;r<t;r++)e[r]=null;return t},MAX_TEMP_BUFFER_SIZE:2097152,numTempVertexBuffersPerSize:64,log2ceilLookup(e){return 32-Math.clz32(e===0?0:e-1)},generateTempBuffers(e,t){let r=p.log2ceilLookup(p.MAX_TEMP_BUFFER_SIZE);t.tempVertexBufferCounters1=[],t.tempVertexBufferCounters2=[],t.tempVertexBufferCounters1.length=t.tempVertexBufferCounters2.length=r+1,t.tempVertexBuffers1=[],t.tempVertexBuffers2=[],t.tempVertexBuffers1.length=t.tempVertexBuffers2.length=r+1,t.tempIndexBuffers=[],t.tempIndexBuffers.length=r+1;for(var o=0;o<=r;++o){t.tempIndexBuffers[o]=null,t.tempVertexBufferCounters1[o]=t.tempVertexBufferCounters2[o]=0;let i=p.numTempVertexBuffersPerSize;t.tempVertexBuffers1[o]=[],t.tempVertexBuffers2[o]=[];let A=t.tempVertexBuffers1[o],s=t.tempVertexBuffers2[o];A.length=s.length=i;for(let c=0;c<i;++c)A[c]=s[c]=null}if(e){t.tempQuadIndexBuffer=w.createBuffer(),t.GLctx.bindBuffer(34963,t.tempQuadIndexBuffer);let i=p.MAX_TEMP_BUFFER_SIZE>>1,A=new Uint16Array(i);var o=0;let c=0;for(;A[o++]=c,!(o>=i||(A[o++]=c+1,o>=i)||(A[o++]=c+2,o>=i)||(A[o++]=c,o>=i)||(A[o++]=c+2,o>=i)||(A[o++]=c+3,o>=i));)c+=4;t.GLctx.bufferData(34963,A,35044),t.GLctx.bindBuffer(34963,null)}},getTempVertexBuffer:function(t){let r=p.log2ceilLookup(t),o=p.currentContext.tempVertexBuffers1[r],i=p.currentContext.tempVertexBufferCounters1[r];p.currentContext.tempVertexBufferCounters1[r]=p.currentContext.tempVertexBufferCounters1[r]+1&p.numTempVertexBuffersPerSize-1;let A=o[i];if(A)return A;let s=w.getParameter(34964);return o[i]=w.createBuffer(),w.bindBuffer(34962,o[i]),w.bufferData(34962,1<<r,35048),w.bindBuffer(34962,s),o[i]},getTempIndexBuffer:function(t){let r=p.log2ceilLookup(t),o=p.currentContext.tempIndexBuffers[r];if(o)return o;let i=w.getParameter(34965);return p.currentContext.tempIndexBuffers[r]=w.createBuffer(),w.bindBuffer(34963,p.currentContext.tempIndexBuffers[r]),w.bufferData(34963,1<<r,35048),w.bindBuffer(34963,i),p.currentContext.tempIndexBuffers[r]},newRenderingFrameStarted:function(){if(!p.currentContext)return;let t=p.currentContext.tempVertexBuffers1;p.currentContext.tempVertexBuffers1=p.currentContext.tempVertexBuffers2,p.currentContext.tempVertexBuffers2=t,t=p.currentContext.tempVertexBufferCounters1,p.currentContext.tempVertexBufferCounters1=p.currentContext.tempVertexBufferCounters2,p.currentContext.tempVertexBufferCounters2=t;let r=p.log2ceilLookup(p.MAX_TEMP_BUFFER_SIZE);for(let o=0;o<=r;++o)p.currentContext.tempVertexBufferCounters1[o]=0},getSource(e,t,r,o){let i="";for(let A=0;A<t;++A){let s=o?m[o+A*4>>2]:-1;i+=Pe(m[r+A*4>>2],s<0?void 0:s)}return i},calcBufLength:function(t,r,o,i){if(o>0)return i*o;let A=p.byteSizeByType[r-p.byteSizeByTypeRoot];return t*A*i},usedTempBuffers:[],preDrawHandleClientVertexAttribBindings:function(t){p.resetBufferBinding=!1;for(let r=0;r<p.currentContext.maxVertexAttribs;++r){let o=p.currentContext.clientBuffers[r];if(!o.clientside||!o.enabled)continue;p.resetBufferBinding=!0;let i=p.calcBufLength(o.size,o.type,o.stride,t),A=p.getTempVertexBuffer(i);w.bindBuffer(34962,A),w.bufferSubData(34962,0,ge.subarray(o.ptr,o.ptr+i)),o.vertexAttribPointerAdaptor.call(w,r,o.size,o.type,o.normalized,o.stride,0)}},postDrawHandleClientVertexAttribBindings:function(){p.resetBufferBinding&&w.bindBuffer(34962,p.buffers[w.currentArrayBufferBinding])},createContext(e,t){t.renderViaOffscreenBackBuffer&&(t.preserveDrawingBuffer=!0),e.getContextSafariWebGL2Fixed||(e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=function(i,A){let s=e.getContextSafariWebGL2Fixed(i,A);return i=="webgl"==s instanceof WebGLRenderingContext?s:null});let r=t.majorVersion>1?e.getContext("webgl2",t):e.getContext("webgl",t);return r?p.registerContext(r,t):0},enableOffscreenFramebufferAttributes(e){e.renderViaOffscreenBackBuffer=!0,e.preserveDrawingBuffer=!0},createOffscreenFramebuffer(e){let t=e.GLctx,r=t.createFramebuffer();if(t.bindFramebuffer(36160,r),e.defaultFbo=r,e.defaultFboForbidBlitFramebuffer=!1,t.getContextAttributes().antialias)e.defaultFboForbidBlitFramebuffer=!0;else{let C=navigator.userAgent.toLowerCase().match(/firefox\/(\d\d)/);if(C!=null){let h=C[1];e.defaultFboForbidBlitFramebuffer=h<67}}e.defaultColorTarget=t.createTexture(),e.defaultDepthTarget=t.createRenderbuffer(),p.resizeOffscreenFramebuffer(e),t.bindTexture(3553,e.defaultColorTarget),t.texParameteri(3553,10241,9728),t.texParameteri(3553,10240,9728),t.texParameteri(3553,10242,33071),t.texParameteri(3553,10243,33071),t.texImage2D(3553,0,6408,t.canvas.width,t.canvas.height,0,6408,5121,null),t.framebufferTexture2D(36160,36064,3553,e.defaultColorTarget,0),t.bindTexture(3553,null);let o=t.createRenderbuffer();t.bindRenderbuffer(36161,e.defaultDepthTarget),t.renderbufferStorage(36161,33189,t.canvas.width,t.canvas.height),t.framebufferRenderbuffer(36160,36096,36161,e.defaultDepthTarget),t.bindRenderbuffer(36161,null);let i=[-1,-1,-1,1,1,-1,1,1],A=t.createBuffer();t.bindBuffer(34962,A),t.bufferData(34962,new Float32Array(i),35044),t.bindBuffer(34962,null),e.blitVB=A;let s="attribute vec2 pos;varying lowp vec2 tex;void main() { tex = pos * 0.5 + vec2(0.5,0.5); gl_Position = vec4(pos, 0.0, 1.0); }",c=t.createShader(35633);t.shaderSource(c,s),t.compileShader(c);let d="varying lowp vec2 tex;uniform sampler2D sampler;void main() { gl_FragColor = texture2D(sampler, tex); }",E=t.createShader(35632);t.shaderSource(E,d),t.compileShader(E);let I=t.createProgram();t.attachShader(I,c),t.attachShader(I,E),t.linkProgram(I),e.blitProgram=I,e.blitPosLoc=t.getAttribLocation(I,"pos"),t.useProgram(I),t.uniform1i(t.getUniformLocation(I,"sampler"),0),t.useProgram(null),e.defaultVao=void 0,t.createVertexArray&&(e.defaultVao=t.createVertexArray(),t.bindVertexArray(e.defaultVao),t.enableVertexAttribArray(e.blitPosLoc),t.bindVertexArray(null))},resizeOffscreenFramebuffer(e){let t=e.GLctx;if(e.defaultColorTarget){let r=t.getParameter(32873);t.bindTexture(3553,e.defaultColorTarget),t.texImage2D(3553,0,6408,t.drawingBufferWidth,t.drawingBufferHeight,0,6408,5121,null),t.bindTexture(3553,r)}if(e.defaultDepthTarget){let r=t.getParameter(36007);t.bindRenderbuffer(36161,e.defaultDepthTarget),t.renderbufferStorage(36161,33189,t.drawingBufferWidth,t.drawingBufferHeight),t.bindRenderbuffer(36161,r)}},blitOffscreenFramebuffer(e){let t=e.GLctx,r=t.getParameter(3089);r&&t.disable(3089);let o=t.getParameter(36006);if(t.blitFramebuffer&&!e.defaultFboForbidBlitFramebuffer)t.bindFramebuffer(36008,e.defaultFbo),t.bindFramebuffer(36009,null),t.blitFramebuffer(0,0,t.canvas.width,t.canvas.height,0,0,t.canvas.width,t.canvas.height,16384,9728);else{let N=function(){t.vertexAttribPointer(e.blitPosLoc,2,5126,!1,0,0),t.drawArrays(5,0,4)};var s=N;t.bindFramebuffer(36160,null);let c=t.getParameter(35725);t.useProgram(e.blitProgram);let d=t.getParameter(34964);t.bindBuffer(34962,e.blitVB);let E=t.getParameter(34016);t.activeTexture(33984);let I=t.getParameter(32873);t.bindTexture(3553,e.defaultColorTarget);let C=t.getParameter(3042);C&&t.disable(3042);let h=t.getParameter(2884);h&&t.disable(2884);let y=t.getParameter(2929);y&&t.disable(2929);let T=t.getParameter(2960);if(T&&t.disable(2960),e.defaultVao){let Y=t.getParameter(34229);t.bindVertexArray(e.defaultVao),N(),t.bindVertexArray(Y)}else{let Y={buffer:t.getVertexAttrib(e.blitPosLoc,34975),size:t.getVertexAttrib(e.blitPosLoc,34339),stride:t.getVertexAttrib(e.blitPosLoc,34340),type:t.getVertexAttrib(e.blitPosLoc,34341),normalized:t.getVertexAttrib(e.blitPosLoc,34922),pointer:t.getVertexAttribOffset(e.blitPosLoc,34373)},q=t.getParameter(34921),D=[];for(var i=0;i<q;++i){var A=t.getVertexAttrib(i,34338);let S=i==e.blitPosLoc;A&&!S&&t.disableVertexAttribArray(i),!A&&S&&t.enableVertexAttribArray(i),D[i]=A}N();for(var i=0;i<q;++i){var A=D[i];let U=i==e.blitPosLoc;A&&!U&&t.enableVertexAttribArray(i),!A&&U&&t.disableVertexAttribArray(i)}t.bindBuffer(34962,Y.buffer),t.vertexAttribPointer(e.blitPosLoc,Y.size,Y.type,Y.normalized,Y.stride,Y.offset)}T&&t.enable(2960),y&&t.enable(2929),h&&t.enable(2884),C&&t.enable(3042),t.bindTexture(3553,I),t.activeTexture(E),t.bindBuffer(34962,d),t.useProgram(c)}t.bindFramebuffer(36160,o),r&&t.enable(3089)},registerContext(e,t){let r=p.getNewId(p.contexts),o={handle:r,attributes:t,version:t.majorVersion,GLctx:e};e.canvas&&(e.canvas.GLctxObject=o),p.contexts[r]=o,(typeof t.enableExtensionsByDefault=="undefined"||t.enableExtensionsByDefault)&&p.initExtensions(o),o.maxVertexAttribs=o.GLctx.getParameter(34921),o.clientBuffers=[];for(let i=0;i<o.maxVertexAttribs;i++)o.clientBuffers[i]={enabled:!1,clientside:!1,size:0,type:0,normalized:0,stride:0,ptr:0,vertexAttribPointerAdaptor:null};return p.generateTempBuffers(!1,o),t.renderViaOffscreenBackBuffer&&p.createOffscreenFramebuffer(o),r},makeContextCurrent(e){return p.currentContext=p.contexts[e],n.ctx=w=p.currentContext&&p.currentContext.GLctx,!(e&&!w)},getContext(e){return p.contexts[e]},deleteContext(e){p.currentContext===p.contexts[e]&&(p.currentContext=null),typeof Z=="object"&&Z.removeAllHandlersOnTarget(p.contexts[e].GLctx.canvas),p.contexts[e]&&p.contexts[e].GLctx.canvas&&(p.contexts[e].GLctx.canvas.GLctxObject=void 0),p.contexts[e]=null},initExtensions(e){if(e||(e=p.currentContext),e.initExtensionsDone)return;e.initExtensionsDone=!0;let{GLctx:t}=e;lA(t),uA(t),dA(t),fA(t),EA(t),e.version>=2&&(t.disjointTimerQueryExt=t.getExtension("EXT_disjoint_timer_query_webgl2")),(e.version<2||!t.disjointTimerQueryExt)&&(t.disjointTimerQueryExt=t.getExtension("EXT_disjoint_timer_query")),IA(t),(t.getSupportedExtensions()||[]).forEach(o=>{!o.includes("lose_context")&&!o.includes("debug")&&t.getExtension(o)})}},Z={inEventHandler:0,removeAllEventListeners(){for(let e=Z.eventHandlers.length-1;e>=0;--e)Z._removeHandler(e);Z.eventHandlers=[],Z.deferredCalls=[]},registerRemoveEventListeners(){Z.removeEventListenersRegistered||(ut.push(Z.removeAllEventListeners),Z.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall(e,t,r){function o(i,A){if(i.length!=A.length)return!1;for(let s in i)if(i[s]!=A[s])return!1;return!0}for(let i in Z.deferredCalls){let A=Z.deferredCalls[i];if(A.targetFunction==e&&o(A.argsList,r))return}Z.deferredCalls.push({targetFunction:e,precedence:t,argsList:r}),Z.deferredCalls.sort((i,A)=>i.precedence<A.precedence)},removeDeferredCalls(e){for(let t=0;t<Z.deferredCalls.length;++t)Z.deferredCalls[t].targetFunction==e&&(Z.deferredCalls.splice(t,1),--t)},canPerformEventHandlerRequests(){return Z.inEventHandler&&Z.currentEventHandler.allowsDeferredCalls},runDeferredCalls(){if(Z.canPerformEventHandlerRequests())for(let e=0;e<Z.deferredCalls.length;++e){let t=Z.deferredCalls[e];Z.deferredCalls.splice(e,1),--e,t.targetFunction.apply(null,t.argsList)}},eventHandlers:[],removeAllHandlersOnTarget(e,t){for(let r=0;r<Z.eventHandlers.length;++r)Z.eventHandlers[r].target==e&&(!t||t==Z.eventHandlers[r].eventTypeString)&&Z._removeHandler(r--)},_removeHandler(e){let t=Z.eventHandlers[e];t.target.removeEventListener(t.eventTypeString,t.eventListenerFunc,t.useCapture),Z.eventHandlers.splice(e,1)},registerOrRemoveHandler(e){let t=function(o){++Z.inEventHandler,Z.currentEventHandler=e,Z.runDeferredCalls(),e.handlerFunc(o),Z.runDeferredCalls(),--Z.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=t,e.target.addEventListener(e.eventTypeString,t,e.useCapture),Z.eventHandlers.push(e),Z.registerRemoveEventListeners();else for(let r=0;r<Z.eventHandlers.length;++r)Z.eventHandlers[r].target==e.target&&Z.eventHandlers[r].eventTypeString==e.eventTypeString&&Z._removeHandler(r--)},getNodeNameForTarget(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};let Vo=["default","low-power","high-performance"],pA=[0,typeof document!="undefined"?document:0,typeof window!="undefined"?window:0];function CA(e){Re("Rules for selecting event targets in HTML5 API are changing: instead of using document.getElementById() that only can refer to elements by their DOM ID, new event target selection mechanism uses the more flexible function document.querySelector() that can look up element names, classes, and complex CSS selectors. Build with -s DISABLE_DEPRECATED_FIND_EVENT_TARGET_BEHAVIOR=1 to change to the new lookup rules. See https://github.com/emscripten-core/emscripten/pull/7977 for more details.");try{return!e||(typeof e=="number"&&(e=pA[e]||Pe(e)),e==="#window")?window:e==="#document"?document:e==="#screen"?screen:e==="#canvas"?n.canvas:typeof e=="string"?document.getElementById(e):e}catch(t){return null}}function Ro(e){return typeof e=="number"&&(e=Pe(e)),!e||e==="#canvas"?typeof p!="undefined"&&p.offscreenCanvases.canvas?p.offscreenCanvases.canvas:n.canvas:typeof p!="undefined"&&p.offscreenCanvases[e]?p.offscreenCanvases[e]:CA(e)}function hA(e,t){M(t);let r=t>>2,o=m[r+6],i={alpha:!!m[r+0],depth:!!m[r+1],stencil:!!m[r+2],antialias:!!m[r+3],premultipliedAlpha:!!m[r+4],preserveDrawingBuffer:!!m[r+5],powerPreference:Vo[o],failIfMajorPerformanceCaveat:!!m[r+7],majorVersion:m[r+8],minorVersion:m[r+9],enableExtensionsByDefault:m[r+10],explicitSwapControl:m[r+11],proxyContextToMainThread:m[r+12],renderViaOffscreenBackBuffer:m[r+13]},A=Ro(e);if(!A)return 0;if(A.offscreenCanvas&&(A=A.offscreenCanvas),i.explicitSwapControl&&(A.transferControlToOffscreen||typeof OffscreenCanvas!="undefined"&&A instanceof OffscreenCanvas||i.renderViaOffscreenBackBuffer||(i.renderViaOffscreenBackBuffer=!0),A.transferControlToOffscreen)){if(!A.controlTransferredOffscreen)p.offscreenCanvases[A.id]={canvas:A.transferControlToOffscreen(),canvasSharedPtr:Wt(12),id:A.id},A.controlTransferredOffscreen=!0;else if(!p.offscreenCanvases[A.id])return 0;A=p.offscreenCanvases[A.id]}return p.createContext(A,i)}function BA(e,t){return hA(e,t)}function mA(){return p.currentContext?p.currentContext.handle:0}function Ko(){return mA()}n._emscripten_webgl_get_current_context=Ko;function Jo(e){return p.makeContextCurrent(e)?0:-5}n._emscripten_webgl_make_context_current=Jo;function _A(e){p.currentContext==e&&(p.currentContext=0),p.deleteContext(e)}function yA(e,t){if(!t)return-5;if(e=p.contexts[e],!e)return-3;let r=e.GLctx;if(!r)return-3;r=r.getContextAttributes(),m[t>>2]=r.alpha,m[t+4>>2]=r.depth,m[t+8>>2]=r.stencil,m[t+12>>2]=r.antialias,m[t+16>>2]=r.premultipliedAlpha,m[t+20>>2]=r.preserveDrawingBuffer;let o=r.powerPreference&&Vo.indexOf(r.powerPreference);return m[t+24>>2]=o,m[t+28>>2]=r.failIfMajorPerformanceCaveat,m[t+32>>2]=e.version,m[t+36>>2]=0,m[t+40>>2]=e.attributes.enableExtensionsByDefault,0}function QA(e){M(e);let t=e>>2;for(let r=0;r<14;++r)m[t+r]=0;m[t+0]=m[t+1]=m[t+3]=m[t+4]=m[t+8]=m[t+10]=1}let Po={};function wA(){return ve||"./this.program"}function Fr(){if(!Fr.strings){let r={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:`${(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")}.UTF-8`,_:wA()};for(var e in Po)Po[e]===void 0?delete r[e]:r[e]=Po[e];let o=[];for(var e in r)o.push(`${e}=${r[e]}`);Fr.strings=o}return Fr.strings}function DA(e,t){let r=0;return Fr().forEach((o,i)=>{let A=t+r;m[e+i*4>>2]=A,Xn(o,A),r+=o.length+1}),0}function bA(e,t){let r=Fr();m[e>>2]=r.length;let o=0;return r.forEach(i=>{o+=i.length+1}),m[t>>2]=o,0}function TA(e){try{let t=Ae.getStreamFromFD(e);return a.close(t),0}catch(t){return(typeof a=="undefined"||!(t instanceof a.ErrnoError))&&l(t),t.errno}}function OA(e,t,r,o){try{let i=Ae.getStreamFromFD(e),A=Ae.doReadv(i,t,r);return m[o>>2]=A,0}catch(i){return(typeof a=="undefined"||!(i instanceof a.ErrnoError))&&l(i),i.errno}}function RA(e,t,r,o,i){try{let A=Ae.getStreamFromFD(e),c=r*4294967296+(t>>>0),d=9007199254740992;return c<=-d||c>=d?-61:(a.llseek(A,c,o),Je=[A.position>>>0,(Be=A.position,+Math.abs(Be)>=1?Be>0?(Math.min(+Math.floor(Be/4294967296),4294967295)|0)>>>0:~~+Math.ceil((Be-+(~~Be>>>0))/4294967296)>>>0:0)],m[i>>2]=Je[0],m[i+4>>2]=Je[1],A.getdents&&c===0&&o===0&&(A.getdents=null),0)}catch(A){return(typeof a=="undefined"||!(A instanceof a.ErrnoError))&&l(A),A.errno}}function PA(e,t,r,o){try{let i=Ae.getStreamFromFD(e),A=Ae.doWritev(i,t,r);return m[o>>2]=A,0}catch(i){return(typeof a=="undefined"||!(i instanceof a.ErrnoError))&&l(i),i.errno}}function MA(){return Ur()}function mn(e,t){mn.randomDevice||(mn.randomDevice=on());for(let r=0;r<t;r++)ce[e+r>>0]=mn.randomDevice();return 0}function SA(e){w.activeTexture(e)}function FA(e,t){w.attachShader(p.programs[e],p.shaders[t])}function vA(e,t,r){w.bindAttribLocation(p.programs[e],t,Pe(r))}function kA(e,t){e==34962?w.currentArrayBufferBinding=t:e==34963&&(w.currentElementArrayBufferBinding=t),e==35051?w.currentPixelPackBufferBinding=t:e==35052&&(w.currentPixelUnpackBufferBinding=t),w.bindBuffer(e,p.buffers[t])}function NA(e,t,r){w.bindBufferBase(e,t,p.buffers[r])}function UA(e,t){w.bindFramebuffer(e,t?p.framebuffers[t]:p.currentContext.defaultFbo)}function xA(e,t){w.bindTexture(e,p.textures[t])}function LA(e){w.bindVertexArray(p.vaos[e]);let t=w.getParameter(34965);w.currentElementArrayBufferBinding=t?t.name|0:0}function jA(e,t,r,o){p.currentContext.version>=2?r?w.bufferData(e,ge,o,r,t):w.bufferData(e,t,o):w.bufferData(e,r?ge.subarray(r,r+t):t,o)}function HA(e,t){return M(t===(t|0)),(e>>>0)+t*4294967296}function GA(e,t,r,o){return w.clientWaitSync(p.syncs[e],t,HA(r,o))}function YA(e){w.compileShader(p.shaders[e])}function XA(){let e=p.getNewId(p.programs),t=w.createProgram();return t.name=e,t.maxUniformLength=t.maxAttributeLength=t.maxUniformBlockNameLength=0,t.uniformIdCounter=1,p.programs[e]=t,e}function WA(e){let t=p.getNewId(p.shaders);return p.shaders[t]=w.createShader(e),t}function $A(e,t){for(let r=0;r<e;r++){let o=m[t+r*4>>2],i=p.buffers[o];i&&(w.deleteBuffer(i),i.name=0,p.buffers[o]=null,o==w.currentArrayBufferBinding&&(w.currentArrayBufferBinding=0),o==w.currentElementArrayBufferBinding&&(w.currentElementArrayBufferBinding=0),o==w.currentPixelPackBufferBinding&&(w.currentPixelPackBufferBinding=0),o==w.currentPixelUnpackBufferBinding&&(w.currentPixelUnpackBufferBinding=0))}}function VA(e,t){for(let r=0;r<e;++r){let o=m[t+r*4>>2],i=p.framebuffers[o];i&&(w.deleteFramebuffer(i),i.name=0,p.framebuffers[o]=null)}}function KA(e){if(!e)return;let t=p.programs[e];if(!t){p.recordError(1281);return}w.deleteProgram(t),t.name=0,p.programs[e]=null}function JA(e){if(!e)return;let t=p.shaders[e];if(!t){p.recordError(1281);return}w.deleteShader(t),p.shaders[e]=null}function zA(e){if(!e)return;let t=p.syncs[e];if(!t){p.recordError(1281);return}w.deleteSync(t),t.name=0,p.syncs[e]=null}function ZA(e,t){for(let r=0;r<e;r++){let o=m[t+r*4>>2],i=p.textures[o];i&&(w.deleteTexture(i),i.name=0,p.textures[o]=null)}}function qA(e,t){for(let r=0;r<e;r++){let o=m[t+r*4>>2];w.deleteVertexArray(p.vaos[o]),p.vaos[o]=null}}function es(e){w.disable(e)}function ts(e){let t=p.currentContext.clientBuffers[e];t.enabled=!1,w.disableVertexAttribArray(e)}function rs(e,t,r){p.preDrawHandleClientVertexAttribBindings(t+r),w.drawArrays(e,t,r),p.postDrawHandleClientVertexAttribBindings()}let zo=[];function ns(e,t){let r=zo[e];for(let o=0;o<e;o++)r[o]=m[t+o*4>>2];w.drawBuffers(r)}function os(e){let t=p.currentContext.clientBuffers[e];t.enabled=!0,w.enableVertexAttribArray(e)}function is(e,t){let r=w.fenceSync(e,t);if(r){let o=p.getNewId(p.syncs);return r.name=o,p.syncs[o]=r,o}return 0}function As(){w.finish()}function ss(){w.flush()}function as(e,t,r,o,i){w.framebufferTexture2D(e,t,r,p.textures[o],i)}function cs(e,t,r,o,i){w.framebufferTextureLayer(e,t,p.textures[r],o,i)}function _n(e,t,r,o){for(let i=0;i<e;i++){let A=w[r](),s=A&&p.getNewId(o);A?(A.name=s,o[s]=A):p.recordError(1282),m[t+i*4>>2]=s}}function gs(e,t){_n(e,t,"createBuffer",p.buffers)}function ls(e,t){_n(e,t,"createFramebuffer",p.framebuffers)}function us(e,t){_n(e,t,"createTexture",p.textures)}function ds(e,t){_n(e,t,"createVertexArray",p.vaos)}function fs(e,t){return w.getAttribLocation(p.programs[e],Pe(t))}function Es(){let e=w.getError()||p.lastError;return p.lastError=0,e}function Is(e){return ie[e>>2]+m[e+4>>2]*4294967296}function ps(e){return ie[e>>2]+ie[e+4>>2]*4294967296}function Cs(e,t){ie[e>>2]=t,ie[e+4>>2]=(t-ie[e>>2])/4294967296;let r=t>=0?ps(e):Is(e);r!=t&&Re(`writeI53ToI64() out of range: serialized JS Number ${t} to Wasm heap as bytes lo=0x${ie[e>>2].toString(16)}, hi=0x${ie[e+4>>2].toString(16)}, which deserializes back to ${r} instead!`)}function hs(e,t,r){if(!t){p.recordError(1281);return}let o;switch(e){case 36346:o=1;break;case 36344:r!=0&&r!=1&&p.recordError(1280);return;case 34814:case 36345:o=0;break;case 34466:var i=w.getParameter(34467);o=i?i.length:0;break;case 33309:if(p.currentContext.version<2){p.recordError(1282);return}var A=w.getSupportedExtensions()||[];o=2*A.length;break;case 33307:case 33308:if(p.currentContext.version<2){p.recordError(1280);return}o=e==33307?3:0;break}if(o===void 0){let s=w.getParameter(e);switch(typeof s){case"number":o=s;break;case"boolean":o=s?1:0;break;case"string":p.recordError(1280);return;case"object":if(s===null)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:{o=0;break}default:{p.recordError(1280);return}}else if(s instanceof Float32Array||s instanceof Uint32Array||s instanceof Int32Array||s instanceof Array){for(let c=0;c<s.length;++c)switch(r){case 0:m[t+c*4>>2]=s[c];break;case 2:ye[t+c*4>>2]=s[c];break;case 4:ce[t+c>>0]=s[c]?1:0;break}return}else try{o=s.name|0}catch(c){p.recordError(1280),J(`GL_INVALID_ENUM in glGet${r}v: Unknown object returned from WebGL getParameter(${e})! (error: ${c})`);return}break;default:p.recordError(1280),J(`GL_INVALID_ENUM in glGet${r}v: Native code calling glGet${r}v(${e}) and it returns ${s} of type ${typeof s}!`);return}}switch(r){case 1:Cs(t,o);break;case 0:m[t>>2]=o;break;case 2:ye[t>>2]=o;break;case 4:ce[t>>0]=o?1:0;break}}function Bs(e,t){hs(e,t,0)}function yn(e){let t=Ye(e)+1,r=Wt(t);return Qt(e,r,t),r}function ms(e){let t=p.stringCache[e];if(!t){switch(e){case 7939:var r=w.getSupportedExtensions()||[];r=r.concat(r.map(d=>`GL_${d}`)),t=yn(r.join(" "));break;case 7936:case 7937:case 37445:case 37446:var o=w.getParameter(e);o||p.recordError(1280),t=o&&yn(o);break;case 7938:var i=w.getParameter(7938);p.currentContext.version>=2?i=`OpenGL ES 3.0 (${i})`:i=`OpenGL ES 2.0 (${i})`,t=yn(i);break;case 35724:var A=w.getParameter(35724),s=/^WebGL GLSL ES ([0-9]\.[0-9][0-9]?)(?:$| .*)/,c=A.match(s);c!==null&&(c[1].length==3&&(c[1]=`${c[1]}0`),A=`OpenGL ES GLSL ES ${c[1]} (${A})`),t=yn(A);break;default:p.recordError(1280)}p.stringCache[e]=t}return t}function _s(e,t){return w.getUniformBlockIndex(p.programs[e],Pe(t))}function ys(e){return parseInt(e)}function Zo(e){return e.slice(-1)=="]"&&e.lastIndexOf("[")}function Qs(e){let{uniformLocsById:t}=e,{uniformSizeAndIdsByName:r}=e,o,i;if(!t)for(e.uniformLocsById=t={},e.uniformArrayNamesById={},o=0;o<w.getProgramParameter(e,35718);++o){let A=w.getActiveUniform(e,o),s=A.name,c=A.size,d=Zo(s),E=d>0?s.slice(0,d):s,I=e.uniformIdCounter;for(e.uniformIdCounter+=c,r[E]=[c,I],i=0;i<c;++i)t[I]=i,e.uniformArrayNamesById[I++]=E}}function ws(e,t){if(t=Pe(t),e=p.programs[e]){Qs(e);let{uniformLocsById:r}=e,o=0,i=t,A=Zo(t);A>0&&(o=ys(t.slice(A+1))>>>0,i=t.slice(0,A));let s=e.uniformSizeAndIdsByName[i];if(s&&o<s[0]&&(o+=s[1],r[o]=r[o]||w.getUniformLocation(e,t)))return o}else p.recordError(1281);return-1}function Ds(e){e=p.programs[e],w.linkProgram(e),e.uniformLocsById=0,e.uniformSizeAndIdsByName={}}function bs(e,t){e==3317&&(p.unpackAlignment=t),w.pixelStorei(e,t)}function Ts(e,t,r,o){function i(c,d){return c+d-1&-d}let A=e*r,s=i(A,o);return t*s}function Os(e){return{5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[e-6402]||1}function vr(e){return e-=5120,e==0?ce:e==1?ge:e==2?qe:e==4?m:e==6?ye:e==5||e==28922||e==28520||e==30779||e==30782?ie:kt}function kr(e){return 31-Math.clz32(e.BYTES_PER_ELEMENT)}function Mo(e,t,r,o,i,A){let s=vr(e),c=kr(s),d=1<<c,E=Os(t)*d,I=Ts(r,o,E,p.unpackAlignment);return s.subarray(i>>c,i+I>>c)}function Rs(e,t,r,o,i,A,s){if(p.currentContext.version>=2){if(w.currentPixelPackBufferBinding)w.readPixels(e,t,r,o,i,A,s);else{let d=vr(A);w.readPixels(e,t,r,o,i,A,d,s>>kr(d))}return}let c=Mo(A,i,r,o,s,i);if(!c){p.recordError(1280);return}w.readPixels(e,t,r,o,i,A,c)}function Ps(e,t,r,o){let i=p.getSource(e,t,r,o);w.shaderSource(p.shaders[e],i)}function Ms(e,t,r,o,i,A,s,c,d){if(p.currentContext.version>=2){if(w.currentPixelUnpackBufferBinding)w.texImage2D(e,t,r,o,i,A,s,c,d);else if(d){let E=vr(c);w.texImage2D(e,t,r,o,i,A,s,c,E,d>>kr(E))}else w.texImage2D(e,t,r,o,i,A,s,c,null);return}w.texImage2D(e,t,r,o,i,A,s,c,d?Mo(c,s,o,i,d,r):null)}function Ss(e,t,r){let o=ye[r>>2];w.texParameterf(e,t,o)}function Fs(e,t,r){w.texParameteri(e,t,r)}function vs(e,t,r,o,i){w.texStorage2D(e,t,r,o,i)}function ks(e,t,r,o,i,A){w.texStorage3D(e,t,r,o,i,A)}function Ns(e,t,r,o,i,A,s,c,d){if(p.currentContext.version>=2){if(w.currentPixelUnpackBufferBinding)w.texSubImage2D(e,t,r,o,i,A,s,c,d);else if(d){let I=vr(c);w.texSubImage2D(e,t,r,o,i,A,s,c,I,d>>kr(I))}else w.texSubImage2D(e,t,r,o,i,A,s,c,null);return}let E=null;d&&(E=Mo(c,s,i,A,d,0)),w.texSubImage2D(e,t,r,o,i,A,s,c,E)}function Us(e,t,r,o,i,A,s,c,d,E,I){if(w.currentPixelUnpackBufferBinding)w.texSubImage3D(e,t,r,o,i,A,s,c,d,E,I);else if(I){let C=vr(E);w.texSubImage3D(e,t,r,o,i,A,s,c,d,E,C,I>>kr(C))}else w.texSubImage3D(e,t,r,o,i,A,s,c,d,E,null)}function st(e){let t=w.currentProgram;if(t){let r=t.uniformLocsById[e];return typeof r=="number"&&(t.uniformLocsById[e]=r=w.getUniformLocation(t,t.uniformArrayNamesById[e]+(r>0?`[${r}]`:""))),r}p.recordError(1282)}function xs(e,t){w.uniform1f(st(e),t)}function Ls(e,t){w.uniform1i(st(e),t)}let Qn=[];function js(e,t,r){if(p.currentContext.version>=2){w.uniform2fv(st(e),ye,r>>2,t*2);return}if(t<=144){var o=Qn[2*t-1];for(let i=0;i<2*t;i+=2)o[i]=ye[r+4*i>>2],o[i+1]=ye[r+(4*i+4)>>2]}else var o=ye.subarray(r>>2,r+t*8>>2);w.uniform2fv(st(e),o)}function Hs(e,t,r){if(p.currentContext.version>=2){w.uniform4fv(st(e),ye,r>>2,t*4);return}if(t<=72){var o=Qn[4*t-1];let i=ye;r>>=2;for(let A=0;A<4*t;A+=4){let s=r+A;o[A]=i[s],o[A+1]=i[s+1],o[A+2]=i[s+2],o[A+3]=i[s+3]}}else var o=ye.subarray(r>>2,r+t*16>>2);w.uniform4fv(st(e),o)}let qo=[];function Gs(e,t,r){if(p.currentContext.version>=2){w.uniform4iv(st(e),m,r>>2,t*4);return}if(t<=72){var o=qo[4*t-1];for(let i=0;i<4*t;i+=4)o[i]=m[r+4*i>>2],o[i+1]=m[r+(4*i+4)>>2],o[i+2]=m[r+(4*i+8)>>2],o[i+3]=m[r+(4*i+12)>>2]}else var o=m.subarray(r>>2,r+t*16>>2);w.uniform4iv(st(e),o)}function Ys(e,t,r){e=p.programs[e],w.uniformBlockBinding(e,t,r)}function Xs(e,t,r,o){if(p.currentContext.version>=2){w.uniformMatrix4fv(st(e),!!r,ye,o>>2,t*16);return}if(t<=18){var i=Qn[16*t-1];let A=ye;o>>=2;for(let s=0;s<16*t;s+=16){let c=o+s;i[s]=A[c],i[s+1]=A[c+1],i[s+2]=A[c+2],i[s+3]=A[c+3],i[s+4]=A[c+4],i[s+5]=A[c+5],i[s+6]=A[c+6],i[s+7]=A[c+7],i[s+8]=A[c+8],i[s+9]=A[c+9],i[s+10]=A[c+10],i[s+11]=A[c+11],i[s+12]=A[c+12],i[s+13]=A[c+13],i[s+14]=A[c+14],i[s+15]=A[c+15]}}else var i=ye.subarray(o>>2,o+t*64>>2);w.uniformMatrix4fv(st(e),!!r,i)}function Ws(e){e=p.programs[e],w.useProgram(e),w.currentProgram=e}function $s(e,t,r,o,i,A){let s=p.currentContext.clientBuffers[e];if(!w.currentArrayBufferBinding){s.size=t,s.type=r,s.normalized=o,s.stride=i,s.ptr=A,s.clientside=!0,s.vertexAttribPointerAdaptor=function(c,d,E,I,C,h){this.vertexAttribPointer(c,d,E,I,C,h)};return}s.clientside=!1,w.vertexAttribPointer(e,t,r,!!o,i,A)}function Vs(e,t,r,o){w.viewport(e,t,r,o)}function wn(e,t){let r=new Date(m[e>>2]*1e3);m[t>>2]=r.getUTCSeconds(),m[t+4>>2]=r.getUTCMinutes(),m[t+8>>2]=r.getUTCHours(),m[t+12>>2]=r.getUTCDate(),m[t+16>>2]=r.getUTCMonth(),m[t+20>>2]=r.getUTCFullYear()-1900,m[t+24>>2]=r.getUTCDay(),m[t+36>>2]=0,m[t+32>>2]=0;let o=Date.UTC(r.getUTCFullYear(),0,1,0,0,0,0),i=(r.getTime()-o)/(1e3*60*60*24)|0;return m[t+28>>2]=i,wn.GMTString||(wn.GMTString=lr("GMT")),m[t+40>>2]=wn.GMTString,t}function Ks(e){return e}function Dn(){if(Dn.called)return;Dn.called=!0;let e=new Date().getFullYear(),t=new Date(e,0,1),r=new Date(e,6,1),o=t.getTimezoneOffset(),i=r.getTimezoneOffset(),A=Math.max(o,i);m[sa()>>2]=A*60,m[Aa()>>2]=+(o!=i);function s(C){let h=C.toTimeString().match(/\(([A-Za-z ]+)\)$/);return h?h[1]:"GMT"}let c=s(t),d=s(r),E=lr(c),I=lr(d);i<o?(m[Nr()>>2]=E,m[Nr()+4>>2]=I):(m[Nr()>>2]=I,m[Nr()+4>>2]=E)}function Js(e,t){Dn();let r=new Date(m[e>>2]*1e3);m[t>>2]=r.getSeconds(),m[t+4>>2]=r.getMinutes(),m[t+8>>2]=r.getHours(),m[t+12>>2]=r.getDate(),m[t+16>>2]=r.getMonth(),m[t+20>>2]=r.getFullYear()-1900,m[t+24>>2]=r.getDay();let o=new Date(r.getFullYear(),0,1),i=(r.getTime()-o.getTime())/(1e3*60*60*24)|0;m[t+28>>2]=i,m[t+36>>2]=-(r.getTimezoneOffset()*60);let A=new Date(r.getFullYear(),6,1).getTimezoneOffset(),s=o.getTimezoneOffset(),c=(A!=s&&r.getTimezoneOffset()==Math.min(s,A))|0;m[t+32>>2]=c;let d=m[Nr()+(c?4:0)>>2];return m[t+40>>2]=d,t}function zs(e){Dn();let t=new Date(m[e+20>>2]+1900,m[e+16>>2],m[e+12>>2],m[e+8>>2],m[e+4>>2],m[e>>2],0),r=m[e+32>>2],o=t.getTimezoneOffset(),i=new Date(t.getFullYear(),0,1),A=new Date(t.getFullYear(),6,1).getTimezoneOffset(),s=i.getTimezoneOffset(),c=Math.min(s,A);if(r<0)m[e+32>>2]=+(A!=s&&c==o);else if(r>0!=(c==o)){let E=Math.max(s,A),I=r>0?c:E;t.setTime(t.getTime()+(I-o)*6e4)}m[e+24>>2]=t.getDay();let d=(t.getTime()-i.getTime())/(1e3*60*60*24)|0;return m[e+28>>2]=d,m[e>>2]=t.getSeconds(),m[e+4>>2]=t.getMinutes(),m[e+8>>2]=t.getHours(),m[e+12>>2]=t.getDate(),m[e+16>>2]=t.getMonth(),t.getTime()/1e3|0}function Zs(e){Ai(e)}function qs(e){He(e)}function bn(e){return e%4===0&&(e%100!==0||e%400===0)}function So(e,t){let r=0;for(let o=0;o<=t;r+=e[o++]);return r}let Tn=[31,29,31,30,31,30,31,31,30,31,30,31],On=[31,28,31,30,31,30,31,31,30,31,30,31];function Rn(e,t){let r=new Date(e.getTime());for(;t>0;){let o=bn(r.getFullYear()),i=r.getMonth(),A=(o?Tn:On)[i];if(t>A-r.getDate())t-=A-r.getDate()+1,r.setDate(1),i<11?r.setMonth(i+1):(r.setMonth(0),r.setFullYear(r.getFullYear()+1));else return r.setDate(r.getDate()+t),r}return r}function ei(e,t,r,o){let i=m[o+40>>2],A={tm_sec:m[o>>2],tm_min:m[o+4>>2],tm_hour:m[o+8>>2],tm_mday:m[o+12>>2],tm_mon:m[o+16>>2],tm_year:m[o+20>>2],tm_wday:m[o+24>>2],tm_yday:m[o+28>>2],tm_isdst:m[o+32>>2],tm_gmtoff:m[o+36>>2],tm_zone:i?Pe(i):""},s=Pe(r),c={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var d in c)s=s.replace(new RegExp(d,"g"),c[d]);let E=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],I=["January","February","March","April","May","June","July","August","September","October","November","December"];function C(D,S,ne){let U=typeof D=="number"?D.toString():D||"";for(;U.length<S;)U=ne[0]+U;return U}function h(D,S){return C(D,S,"0")}function y(D,S){function ne(me){return me<0?-1:me>0?1:0}let U;return(U=ne(D.getFullYear()-S.getFullYear()))===0&&(U=ne(D.getMonth()-S.getMonth()))===0&&(U=ne(D.getDate()-S.getDate())),U}function T(D){switch(D.getDay()){case 0:return new Date(D.getFullYear()-1,11,29);case 1:return D;case 2:return new Date(D.getFullYear(),0,3);case 3:return new Date(D.getFullYear(),0,2);case 4:return new Date(D.getFullYear(),0,1);case 5:return new Date(D.getFullYear()-1,11,31);case 6:return new Date(D.getFullYear()-1,11,30)}}function N(D){let S=Rn(new Date(D.tm_year+1900,0,1),D.tm_yday),ne=new Date(S.getFullYear(),0,4),U=new Date(S.getFullYear()+1,0,4),me=T(ne),Te=T(U);return y(me,S)<=0?y(Te,S)<=0?S.getFullYear()+1:S.getFullYear():S.getFullYear()-1}let Y={"%a"(D){return E[D.tm_wday].substring(0,3)},"%A"(D){return E[D.tm_wday]},"%b"(D){return I[D.tm_mon].substring(0,3)},"%B"(D){return I[D.tm_mon]},"%C"(D){let S=D.tm_year+1900;return h(S/100|0,2)},"%d"(D){return h(D.tm_mday,2)},"%e"(D){return C(D.tm_mday,2," ")},"%g"(D){return N(D).toString().substring(2)},"%G"(D){return N(D)},"%H"(D){return h(D.tm_hour,2)},"%I"(D){let S=D.tm_hour;return S==0?S=12:S>12&&(S-=12),h(S,2)},"%j"(D){return h(D.tm_mday+So(bn(D.tm_year+1900)?Tn:On,D.tm_mon-1),3)},"%m"(D){return h(D.tm_mon+1,2)},"%M"(D){return h(D.tm_min,2)},"%n"(){return`
`},"%p"(D){return D.tm_hour>=0&&D.tm_hour<12?"AM":"PM"},"%S"(D){return h(D.tm_sec,2)},"%t"(){return"	"},"%u"(D){return D.tm_wday||7},"%U"(D){let S=new Date(D.tm_year+1900,0,1),ne=S.getDay()===0?S:Rn(S,7-S.getDay()),U=new Date(D.tm_year+1900,D.tm_mon,D.tm_mday);if(y(ne,U)<0){let me=So(bn(U.getFullYear())?Tn:On,U.getMonth()-1)-31,Ze=31-ne.getDate()+me+U.getDate();return h(Math.ceil(Ze/7),2)}return y(ne,S)===0?"01":"00"},"%V"(D){let S=new Date(D.tm_year+1900,0,4),ne=new Date(D.tm_year+1901,0,4),U=T(S),me=T(ne),Te=Rn(new Date(D.tm_year+1900,0,1),D.tm_yday);if(y(Te,U)<0)return"53";if(y(me,Te)<=0)return"01";let Ze;return U.getFullYear()<D.tm_year+1900?Ze=D.tm_yday+32-U.getDate():Ze=D.tm_yday+1-U.getDate(),h(Math.ceil(Ze/7),2)},"%w"(D){return D.tm_wday},"%W"(D){let S=new Date(D.tm_year,0,1),ne=S.getDay()===1?S:Rn(S,S.getDay()===0?1:7-S.getDay()+1),U=new Date(D.tm_year+1900,D.tm_mon,D.tm_mday);if(y(ne,U)<0){let me=So(bn(U.getFullYear())?Tn:On,U.getMonth()-1)-31,Ze=31-ne.getDate()+me+U.getDate();return h(Math.ceil(Ze/7),2)}return y(ne,S)===0?"01":"00"},"%y"(D){return(D.tm_year+1900).toString().substring(2)},"%Y"(D){return D.tm_year+1900},"%z"(D){let S=D.tm_gmtoff,ne=S>=0;return S=Math.abs(S)/60,S=S/60*100+S%60,(ne?"+":"-")+`0000${S}`.slice(-4)},"%Z"(D){return D.tm_zone},"%%"(){return"%"}};for(var d in Y)s.includes(d)&&(s=s.replace(new RegExp(d,"g"),Y[d](A)));let q=Sn(s,!1);return q.length>t?0:(Yr(q,e),q.length-1)}function ea(e,t,r,o){return ei(e,t,r,o)}n.requestFullscreen=function(t,r){_.requestFullscreen(t,r)},n.requestFullScreen=function(){_.requestFullScreen()},n.requestAnimationFrame=function(t){_.requestAnimationFrame(t)},n.setCanvasSize=function(t,r,o){_.setCanvasSize(t,r,o)},n.pauseMainLoop=function(){_.mainLoop.pause()},n.resumeMainLoop=function(){_.mainLoop.resume()},n.getUserMedia=function(){_.getUserMedia()},n.createContext=function(t,r,o,i){return _.createContext(t,r,o,i)};let ti=function(e,t,r,o){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=a.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=o},Pn=365,Mn=146;Object.defineProperties(ti.prototype,{read:{get(){return(this.mode&Pn)===Pn},set(e){e?this.mode|=Pn:this.mode&=~Pn}},write:{get(){return(this.mode&Mn)===Mn},set(e){e?this.mode|=Mn:this.mode&=~Mn}},isFolder:{get(){return a.isDir(this.mode)}},isDevice:{get(){return a.isChrdev(this.mode)}}}),a.FSNode=ti,a.staticInit(),n.FS_createPath=a.createPath,n.FS_createDataFile=a.createDataFile,n.FS_createPreloadedFile=a.createPreloadedFile,n.FS_createLazyFile=a.createLazyFile,n.FS_createDevice=a.createDevice,n.FS_unlink=a.unlink,_o(),cn=n.PureVirtualError=tr(Error,"PureVirtualError"),Dr(),wo(),Rt=n.BindingError=tr(Error,"BindingError"),Gt=n.InternalError=tr(Error,"InternalError"),z(),mi(),Go=n.UnboundTypeError=tr(Error,"UnboundTypeError");let w;for(var Le=0;Le<32;++Le)zo.push(new Array(Le));let ta=new Float32Array(288);for(var Le=0;Le<288;++Le)Qn[Le]=ta.subarray(0,Le+1);let ra=new Int32Array(288);for(var Le=0;Le<288;++Le)qo[Le]=ra.subarray(0,Le+1);let Tc=!0;function Sn(e,t,r){let o=r>0?r:Ye(e)+1,i=new Array(o),A=Ge(e,i,0,i.length);return t&&(i.length=A),i}let na=typeof atob=="function"?atob:function(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="",o,i,A,s,c,d,E,I=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");do s=t.indexOf(e.charAt(I++)),c=t.indexOf(e.charAt(I++)),d=t.indexOf(e.charAt(I++)),E=t.indexOf(e.charAt(I++)),o=s<<2|c>>4,i=(c&15)<<4|d>>2,A=(d&3)<<6|E,r=r+String.fromCharCode(o),d!==64&&(r=r+String.fromCharCode(i)),E!==64&&(r=r+String.fromCharCode(A));while(I<e.length);return r};function Oc(e){if(typeof Bt=="boolean"&&Bt){let t=Buffer.from(e,"base64");return new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}try{let t=na(e),r=new Uint8Array(t.length);for(let o=0;o<t.length;++o)r[o]=t.charCodeAt(o);return r}catch(t){throw new Error("Converting base64 string to bytes failed.")}}var ri={HaveOffsetConverter:Zn,__cxa_allocate_exception:oo,__cxa_atexit:io,__cxa_begin_catch:ot,__cxa_end_catch:Lt,__cxa_find_matching_catch_2:rn,__cxa_find_matching_catch_3:mr,__cxa_free_exception:Br,__cxa_rethrow:nn,__cxa_thread_atexit:ao,__cxa_throw:co,__resumeException:so,__sys_fcntl64:uo,__sys_ioctl:fo,__sys_mmap2:Io,__sys_open:po,__sys_stat64:an,_embind_create_inheriting_constructor:To,_embind_finalize_value_object:u,_embind_register_bigint:f,_embind_register_bool:O,_embind_register_class:wi,_embind_register_class_class_function:Di,_embind_register_class_constructor:bi,_embind_register_class_function:Ti,_embind_register_class_property:Oi,_embind_register_emval:Ri,_embind_register_float:Mi,_embind_register_function:Si,_embind_register_integer:vi,_embind_register_memory_view:ki,_embind_register_std_string:Ni,_embind_register_std_wstring:Ui,_embind_register_value_object:xi,_embind_register_value_object_field:Li,_embind_register_void:ji,_emval_call_void_method:Yi,_emval_decref:Wo,_emval_get_method_caller:$i,_emval_incref:Vi,_emval_take_value:Ki,abort:Ji,clock_gettime:Zi,dlopen:qi,dlsym:eA,emscripten_asm_const_int:rA,emscripten_get_heap_max:nA,emscripten_memcpy_big:oA,emscripten_pc_get_function:iA,emscripten_resize_heap:sA,emscripten_stack_snapshot:aA,emscripten_stack_unwind_buffer:cA,emscripten_thread_sleep:gA,emscripten_webgl_create_context:BA,emscripten_webgl_destroy_context:_A,emscripten_webgl_get_context_attributes:yA,emscripten_webgl_get_current_context:Ko,emscripten_webgl_init_context_attributes:QA,emscripten_webgl_make_context_current:Jo,environ_get:DA,environ_sizes_get:bA,exit:pr,fd_close:TA,fd_read:OA,fd_seek:RA,fd_write:PA,getTempRet0:MA,getentropy:mn,glActiveTexture:SA,glAttachShader:FA,glBindAttribLocation:vA,glBindBuffer:kA,glBindBufferBase:NA,glBindFramebuffer:UA,glBindTexture:xA,glBindVertexArray:LA,glBufferData:jA,glClientWaitSync:GA,glCompileShader:YA,glCreateProgram:XA,glCreateShader:WA,glDeleteBuffers:$A,glDeleteFramebuffers:VA,glDeleteProgram:KA,glDeleteShader:JA,glDeleteSync:zA,glDeleteTextures:ZA,glDeleteVertexArrays:qA,glDisable:es,glDisableVertexAttribArray:ts,glDrawArrays:rs,glDrawBuffers:ns,glEnableVertexAttribArray:os,glFenceSync:is,glFinish:As,glFlush:ss,glFramebufferTexture2D:as,glFramebufferTextureLayer:cs,glGenBuffers:gs,glGenFramebuffers:ls,glGenTextures:us,glGenVertexArrays:ds,glGetAttribLocation:fs,glGetError:Es,glGetIntegerv:Bs,glGetString:ms,glGetUniformBlockIndex:_s,glGetUniformLocation:ws,glLinkProgram:Ds,glPixelStorei:bs,glReadPixels:Rs,glShaderSource:Ps,glTexImage2D:Ms,glTexParameterfv:Ss,glTexParameteri:Fs,glTexStorage2D:vs,glTexStorage3D:ks,glTexSubImage2D:Ns,glTexSubImage3D:Us,glUniform1f:xs,glUniform1i:Ls,glUniform2fv:js,glUniform4fv:Hs,glUniform4iv:Gs,glUniformBlockBinding:Ys,glUniformMatrix4fv:Xs,glUseProgram:Ws,glVertexAttribPointer:$s,glViewport:Vs,gmtime_r:wn,invoke_diii:Ma,invoke_i:Na,invoke_ii:ha,invoke_iii:da,invoke_iiii:Sa,invoke_iiiii:Da,invoke_iiiiii:Oa,invoke_iiiiiii:Ea,invoke_iiiiiiiddi:oc,invoke_iiiiiiii:Pa,invoke_iiiiiiiii:ja,invoke_v:Ia,invoke_vdiii:ac,invoke_vi:ua,invoke_vididdii:qa,invoke_vidii:tc,invoke_vii:Ca,invoke_viid:la,invoke_viidi:sc,invoke_viididii:La,invoke_viii:Ba,invoke_viiid:ec,invoke_viiidd:Ta,invoke_viiiddi:ka,invoke_viiidi:Ua,invoke_viiii:wa,invoke_viiiid:ic,invoke_viiiidi:xa,invoke_viiiidid:Ac,invoke_viiiidiidiiiiiii:Za,invoke_viiiifiifiiiiiii:za,invoke_viiiii:fa,invoke_viiiiid:Ya,invoke_viiiiif:Va,invoke_viiiiii:ma,invoke_viiiiiid:Xa,invoke_viiiiiif:Ka,invoke_viiiiiii:_a,invoke_viiiiiiiddi:nc,invoke_viiiiiiidiiii:$a,invoke_viiiiiiifiiii:Ja,invoke_viiiiiiii:ya,invoke_viiiiiiiidd:Ga,invoke_viiiiiiiidf:Ha,invoke_viiiiiiiii:Qa,invoke_viiiiiiiiidd:Fa,invoke_viiiiiiiiiddi:va,invoke_viiiiiiiiii:pa,invoke_viiiiiiiiiiddi:rc,invoke_viiiiiiiiiii:ba,invoke_viiiiiiiiiiii:Wa,invoke_viiiiiiiiiiiii:Ra,llvm_eh_typeid_for:Ks,localtime_r:Js,mktime:zs,proc_exit:Zs,setTempRet0:qs,strftime:ei,strftime_l:ea};let Rc=zn(),Pc=n.___wasm_call_ctors=V("__wasm_call_ctors");var at=n._free=V("free"),Wt=n._malloc=V("malloc"),oa=n.___errno_location=V("__errno_location");let Mc=n._fflush=V("fflush");var ia=n.___getTypeName=V("__getTypeName");let Sc=n.___embind_register_native_and_builtin_types=V("__embind_register_native_and_builtin_types"),Fc=n._emscripten_main_thread_process_queued_calls=V("emscripten_main_thread_process_queued_calls");var Fo=n._emscripten_stack_get_end=function(){return(Fo=n._emscripten_stack_get_end=n.asm.emscripten_stack_get_end).apply(null,arguments)},Nr=n.__get_tzname=V("_get_tzname"),Aa=n.__get_daylight=V("_get_daylight"),sa=n.__get_timezone=V("_get_timezone"),L=n.stackSave=V("stackSave"),j=n.stackRestore=V("stackRestore"),ni=n.stackAlloc=V("stackAlloc"),oi=n._emscripten_stack_init=function(){return(oi=n._emscripten_stack_init=n.asm.emscripten_stack_init).apply(null,arguments)},aa=n._emscripten_stack_get_free=function(){return(aa=n._emscripten_stack_get_free=n.asm.emscripten_stack_get_free).apply(null,arguments)};let vc=n._saveSetjmp=V("saveSetjmp");var H=n._setThrew=V("setThrew"),ii=n.___cxa_can_catch=V("__cxa_can_catch"),ca=n.___cxa_is_pointer_type=V("__cxa_is_pointer_type"),ga=n._memalign=V("memalign");let kc=n._emscripten_builtin_malloc=V("emscripten_builtin_malloc"),Nc=n._emscripten_builtin_free=V("emscripten_builtin_free"),Uc=n._emscripten_builtin_memalign=V("emscripten_builtin_memalign"),xc=n.dynCall_jii=V("dynCall_jii"),Lc=n.dynCall_viji=V("dynCall_viji"),jc=n.dynCall_jjj=V("dynCall_jjj"),Hc=n.dynCall_iiiijj=V("dynCall_iiiijj"),Gc=n.dynCall_viijj=V("dynCall_viijj"),Yc=n.dynCall_viiijjjj=V("dynCall_viiijjjj"),Xc=n.dynCall_ji=V("dynCall_ji"),Wc=n.dynCall_vij=V("dynCall_vij"),$c=n.dynCall_viijii=V("dynCall_viijii"),Vc=n.dynCall_vj=V("dynCall_vj"),Kc=n.dynCall_viij=V("dynCall_viij"),Jc=n.dynCall_viiiiij=V("dynCall_viiiiij"),zc=n.dynCall_iijjiiii=V("dynCall_iijjiiii"),Zc=n.dynCall_jiji=V("dynCall_jiji"),qc=n.dynCall_iiiiij=V("dynCall_iiiiij"),eg=n.dynCall_iiiiijj=V("dynCall_iiiiijj"),tg=n.dynCall_iiiiiijj=V("dynCall_iiiiiijj");function la(e,t,r,o){let i=L();try{F.get(e)(t,r,o)}catch(A){if(j(i),A!==A+0&&A!=="longjmp")throw A;H(1,0)}}function ua(e,t){let r=L();try{F.get(e)(t)}catch(o){if(j(r),o!==o+0&&o!=="longjmp")throw o;H(1,0)}}function da(e,t,r){let o=L();try{return F.get(e)(t,r)}catch(i){if(j(o),i!==i+0&&i!=="longjmp")throw i;H(1,0)}}function fa(e,t,r,o,i,A){let s=L();try{F.get(e)(t,r,o,i,A)}catch(c){if(j(s),c!==c+0&&c!=="longjmp")throw c;H(1,0)}}function Ea(e,t,r,o,i,A,s){let c=L();try{return F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function Ia(e){let t=L();try{F.get(e)()}catch(r){if(j(t),r!==r+0&&r!=="longjmp")throw r;H(1,0)}}function pa(e,t,r,o,i,A,s,c,d,E,I){let C=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I)}catch(h){if(j(C),h!==h+0&&h!=="longjmp")throw h;H(1,0)}}function Ca(e,t,r){let o=L();try{F.get(e)(t,r)}catch(i){if(j(o),i!==i+0&&i!=="longjmp")throw i;H(1,0)}}function ha(e,t){let r=L();try{return F.get(e)(t)}catch(o){if(j(r),o!==o+0&&o!=="longjmp")throw o;H(1,0)}}function Ba(e,t,r,o){let i=L();try{F.get(e)(t,r,o)}catch(A){if(j(i),A!==A+0&&A!=="longjmp")throw A;H(1,0)}}function ma(e,t,r,o,i,A,s){let c=L();try{F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function _a(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function ya(e,t,r,o,i,A,s,c,d){let E=L();try{F.get(e)(t,r,o,i,A,s,c,d)}catch(I){if(j(E),I!==I+0&&I!=="longjmp")throw I;H(1,0)}}function Qa(e,t,r,o,i,A,s,c,d,E){let I=L();try{F.get(e)(t,r,o,i,A,s,c,d,E)}catch(C){if(j(I),C!==C+0&&C!=="longjmp")throw C;H(1,0)}}function wa(e,t,r,o,i){let A=L();try{F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}function Da(e,t,r,o,i){let A=L();try{return F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}function ba(e,t,r,o,i,A,s,c,d,E,I,C){let h=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C)}catch(y){if(j(h),y!==y+0&&y!=="longjmp")throw y;H(1,0)}}function Ta(e,t,r,o,i,A){let s=L();try{F.get(e)(t,r,o,i,A)}catch(c){if(j(s),c!==c+0&&c!=="longjmp")throw c;H(1,0)}}function Oa(e,t,r,o,i,A){let s=L();try{return F.get(e)(t,r,o,i,A)}catch(c){if(j(s),c!==c+0&&c!=="longjmp")throw c;H(1,0)}}function Ra(e,t,r,o,i,A,s,c,d,E,I,C,h,y){let T=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h,y)}catch(N){if(j(T),N!==N+0&&N!=="longjmp")throw N;H(1,0)}}function Pa(e,t,r,o,i,A,s,c){let d=L();try{return F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function Ma(e,t,r,o){let i=L();try{return F.get(e)(t,r,o)}catch(A){if(j(i),A!==A+0&&A!=="longjmp")throw A;H(1,0)}}function Sa(e,t,r,o){let i=L();try{return F.get(e)(t,r,o)}catch(A){if(j(i),A!==A+0&&A!=="longjmp")throw A;H(1,0)}}function Fa(e,t,r,o,i,A,s,c,d,E,I,C){let h=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C)}catch(y){if(j(h),y!==y+0&&y!=="longjmp")throw y;H(1,0)}}function va(e,t,r,o,i,A,s,c,d,E,I,C,h){let y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h)}catch(T){if(j(y),T!==T+0&&T!=="longjmp")throw T;H(1,0)}}function ka(e,t,r,o,i,A,s){let c=L();try{F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function Na(e){let t=L();try{return F.get(e)()}catch(r){if(j(t),r!==r+0&&r!=="longjmp")throw r;H(1,0)}}function Ua(e,t,r,o,i,A){let s=L();try{F.get(e)(t,r,o,i,A)}catch(c){if(j(s),c!==c+0&&c!=="longjmp")throw c;H(1,0)}}function xa(e,t,r,o,i,A,s){let c=L();try{F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function La(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function ja(e,t,r,o,i,A,s,c,d){let E=L();try{return F.get(e)(t,r,o,i,A,s,c,d)}catch(I){if(j(E),I!==I+0&&I!=="longjmp")throw I;H(1,0)}}function Ha(e,t,r,o,i,A,s,c,d,E,I){let C=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I)}catch(h){if(j(C),h!==h+0&&h!=="longjmp")throw h;H(1,0)}}function Ga(e,t,r,o,i,A,s,c,d,E,I){let C=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I)}catch(h){if(j(C),h!==h+0&&h!=="longjmp")throw h;H(1,0)}}function Ya(e,t,r,o,i,A,s){let c=L();try{F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function Xa(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function Wa(e,t,r,o,i,A,s,c,d,E,I,C,h){let y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h)}catch(T){if(j(y),T!==T+0&&T!=="longjmp")throw T;H(1,0)}}function $a(e,t,r,o,i,A,s,c,d,E,I,C,h){let y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h)}catch(T){if(j(y),T!==T+0&&T!=="longjmp")throw T;H(1,0)}}function Va(e,t,r,o,i,A,s){let c=L();try{F.get(e)(t,r,o,i,A,s)}catch(d){if(j(c),d!==d+0&&d!=="longjmp")throw d;H(1,0)}}function Ka(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function Ja(e,t,r,o,i,A,s,c,d,E,I,C,h){let y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h)}catch(T){if(j(y),T!==T+0&&T!=="longjmp")throw T;H(1,0)}}function za(e,t,r,o,i,A,s,c,d,E,I,C,h,y,T,N){let Y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h,y,T,N)}catch(q){if(j(Y),q!==q+0&&q!=="longjmp")throw q;H(1,0)}}function Za(e,t,r,o,i,A,s,c,d,E,I,C,h,y,T,N){let Y=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h,y,T,N)}catch(q){if(j(Y),q!==q+0&&q!=="longjmp")throw q;H(1,0)}}function qa(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function ec(e,t,r,o,i){let A=L();try{F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}function tc(e,t,r,o,i){let A=L();try{F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}function rc(e,t,r,o,i,A,s,c,d,E,I,C,h,y){let T=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I,C,h,y)}catch(N){if(j(T),N!==N+0&&N!=="longjmp")throw N;H(1,0)}}function nc(e,t,r,o,i,A,s,c,d,E,I){let C=L();try{F.get(e)(t,r,o,i,A,s,c,d,E,I)}catch(h){if(j(C),h!==h+0&&h!=="longjmp")throw h;H(1,0)}}function oc(e,t,r,o,i,A,s,c,d,E){let I=L();try{return F.get(e)(t,r,o,i,A,s,c,d,E)}catch(C){if(j(I),C!==C+0&&C!=="longjmp")throw C;H(1,0)}}function ic(e,t,r,o,i,A){let s=L();try{F.get(e)(t,r,o,i,A)}catch(c){if(j(s),c!==c+0&&c!=="longjmp")throw c;H(1,0)}}function Ac(e,t,r,o,i,A,s,c){let d=L();try{F.get(e)(t,r,o,i,A,s,c)}catch(E){if(j(d),E!==E+0&&E!=="longjmp")throw E;H(1,0)}}function sc(e,t,r,o,i){let A=L();try{F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}function ac(e,t,r,o,i){let A=L();try{F.get(e)(t,r,o,i)}catch(s){if(j(A),s!==s+0&&s!=="longjmp")throw s;H(1,0)}}Object.getOwnPropertyDescriptor(n,"intArrayFromString")||(n.intArrayFromString=function(){l("'intArrayFromString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"intArrayToString")||(n.intArrayToString=function(){l("'intArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ccall")||(n.ccall=function(){l("'ccall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"cwrap")||(n.cwrap=function(){l("'cwrap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setValue")||(n.setValue=function(){l("'setValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getValue")||(n.getValue=function(){l("'getValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"allocate")||(n.allocate=function(){l("'allocate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UTF8ArrayToString")||(n.UTF8ArrayToString=function(){l("'UTF8ArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UTF8ToString")||(n.UTF8ToString=function(){l("'UTF8ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToUTF8Array")||(n.stringToUTF8Array=function(){l("'stringToUTF8Array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToUTF8")||(n.stringToUTF8=function(){l("'stringToUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"lengthBytesUTF8")||(n.lengthBytesUTF8=function(){l("'lengthBytesUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stackTrace")||(n.stackTrace=function(){l("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addOnPreRun")||(n.addOnPreRun=function(){l("'addOnPreRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addOnInit")||(n.addOnInit=function(){l("'addOnInit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addOnPreMain")||(n.addOnPreMain=function(){l("'addOnPreMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addOnExit")||(n.addOnExit=function(){l("'addOnExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addOnPostRun")||(n.addOnPostRun=function(){l("'addOnPostRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeStringToMemory")||(n.writeStringToMemory=function(){l("'writeStringToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeArrayToMemory")||(n.writeArrayToMemory=function(){l("'writeArrayToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeAsciiToMemory")||(n.writeAsciiToMemory=function(){l("'writeAsciiToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),n.addRunDependency=zt,n.removeRunDependency=Ut,Object.getOwnPropertyDescriptor(n,"FS_createFolder")||(n.FS_createFolder=function(){l("'FS_createFolder' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),n.FS_createPath=a.createPath,n.FS_createDataFile=a.createDataFile,n.FS_createPreloadedFile=a.createPreloadedFile,n.FS_createLazyFile=a.createLazyFile,Object.getOwnPropertyDescriptor(n,"FS_createLink")||(n.FS_createLink=function(){l("'FS_createLink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),n.FS_createDevice=a.createDevice,n.FS_unlink=a.unlink,Object.getOwnPropertyDescriptor(n,"getLEB")||(n.getLEB=function(){l("'getLEB' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getFunctionTables")||(n.getFunctionTables=function(){l("'getFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"alignFunctionTables")||(n.alignFunctionTables=function(){l("'alignFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerFunctions")||(n.registerFunctions=function(){l("'registerFunctions' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"addFunction")||(n.addFunction=function(){l("'addFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"removeFunction")||(n.removeFunction=function(){l("'removeFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getFuncWrapper")||(n.getFuncWrapper=function(){l("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"prettyPrint")||(n.prettyPrint=function(){l("'prettyPrint' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"dynCall")||(n.dynCall=function(){l("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getCompilerSetting")||(n.getCompilerSetting=function(){l("'getCompilerSetting' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"print")||(n.print=function(){l("'print' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"printErr")||(n.printErr=function(){l("'printErr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getTempRet0")||(n.getTempRet0=function(){l("'getTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setTempRet0")||(n.setTempRet0=function(){l("'setTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"callMain")||(n.callMain=function(){l("'callMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"abort")||(n.abort=function(){l("'abort' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"keepRuntimeAlive")||(n.keepRuntimeAlive=function(){l("'keepRuntimeAlive' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"zeroMemory")||(n.zeroMemory=function(){l("'zeroMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToNewUTF8")||(n.stringToNewUTF8=function(){l("'stringToNewUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setFileTime")||(n.setFileTime=function(){l("'setFileTime' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscripten_realloc_buffer")||(n.emscripten_realloc_buffer=function(){l("'emscripten_realloc_buffer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ENV")||(n.ENV=function(){l("'ENV' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ERRNO_CODES")||(n.ERRNO_CODES=function(){l("'ERRNO_CODES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ERRNO_MESSAGES")||(n.ERRNO_MESSAGES=function(){l("'ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setErrNo")||(n.setErrNo=function(){l("'setErrNo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"inetPton4")||(n.inetPton4=function(){l("'inetPton4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"inetNtop4")||(n.inetNtop4=function(){l("'inetNtop4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"inetPton6")||(n.inetPton6=function(){l("'inetPton6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"inetNtop6")||(n.inetNtop6=function(){l("'inetNtop6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readSockaddr")||(n.readSockaddr=function(){l("'readSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeSockaddr")||(n.writeSockaddr=function(){l("'writeSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"DNS")||(n.DNS=function(){l("'DNS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getHostByName")||(n.getHostByName=function(){l("'getHostByName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"GAI_ERRNO_MESSAGES")||(n.GAI_ERRNO_MESSAGES=function(){l("'GAI_ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"Protocols")||(n.Protocols=function(){l("'Protocols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"Sockets")||(n.Sockets=function(){l("'Sockets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getRandomDevice")||(n.getRandomDevice=function(){l("'getRandomDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"traverseStack")||(n.traverseStack=function(){l("'traverseStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UNWIND_CACHE")||(n.UNWIND_CACHE=function(){l("'UNWIND_CACHE' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"withBuiltinMalloc")||(n.withBuiltinMalloc=function(){l("'withBuiltinMalloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readAsmConstArgsArray")||(n.readAsmConstArgsArray=function(){l("'readAsmConstArgsArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readAsmConstArgs")||(n.readAsmConstArgs=function(){l("'readAsmConstArgs' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"mainThreadEM_ASM")||(n.mainThreadEM_ASM=function(){l("'mainThreadEM_ASM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"jstoi_q")||(n.jstoi_q=function(){l("'jstoi_q' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"jstoi_s")||(n.jstoi_s=function(){l("'jstoi_s' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getExecutableName")||(n.getExecutableName=function(){l("'getExecutableName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"listenOnce")||(n.listenOnce=function(){l("'listenOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"autoResumeAudioContext")||(n.autoResumeAudioContext=function(){l("'autoResumeAudioContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"dynCallLegacy")||(n.dynCallLegacy=function(){l("'dynCallLegacy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getDynCaller")||(n.getDynCaller=function(){l("'getDynCaller' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"dynCall")||(n.dynCall=function(){l("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"callRuntimeCallbacks")||(n.callRuntimeCallbacks=function(){l("'callRuntimeCallbacks' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"runtimeKeepalivePush")||(n.runtimeKeepalivePush=function(){l("'runtimeKeepalivePush' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"runtimeKeepalivePop")||(n.runtimeKeepalivePop=function(){l("'runtimeKeepalivePop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"callUserCallback")||(n.callUserCallback=function(){l("'callUserCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"maybeExit")||(n.maybeExit=function(){l("'maybeExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"safeSetTimeout")||(n.safeSetTimeout=function(){l("'safeSetTimeout' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"asmjsMangle")||(n.asmjsMangle=function(){l("'asmjsMangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"asyncLoad")||(n.asyncLoad=function(){l("'asyncLoad' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"alignMemory")||(n.alignMemory=function(){l("'alignMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"mmapAlloc")||(n.mmapAlloc=function(){l("'mmapAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"reallyNegative")||(n.reallyNegative=function(){l("'reallyNegative' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"unSign")||(n.unSign=function(){l("'unSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"reSign")||(n.reSign=function(){l("'reSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"formatString")||(n.formatString=function(){l("'formatString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"PATH")||(n.PATH=function(){l("'PATH' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"PATH_FS")||(n.PATH_FS=function(){l("'PATH_FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SYSCALLS")||(n.SYSCALLS=function(){l("'SYSCALLS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"syscallMmap2")||(n.syscallMmap2=function(){l("'syscallMmap2' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"syscallMunmap")||(n.syscallMunmap=function(){l("'syscallMunmap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getSocketFromFD")||(n.getSocketFromFD=function(){l("'getSocketFromFD' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getSocketAddress")||(n.getSocketAddress=function(){l("'getSocketAddress' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"JSEvents")||(n.JSEvents=function(){l("'JSEvents' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerKeyEventCallback")||(n.registerKeyEventCallback=function(){l("'registerKeyEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"specialHTMLTargets")||(n.specialHTMLTargets=function(){l("'specialHTMLTargets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"findEventTarget")||(n.findEventTarget=function(){l("'findEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"findCanvasEventTarget")||(n.findCanvasEventTarget=function(){l("'findCanvasEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getBoundingClientRect")||(n.getBoundingClientRect=function(){l("'getBoundingClientRect' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillMouseEventData")||(n.fillMouseEventData=function(){l("'fillMouseEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerMouseEventCallback")||(n.registerMouseEventCallback=function(){l("'registerMouseEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerWheelEventCallback")||(n.registerWheelEventCallback=function(){l("'registerWheelEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerUiEventCallback")||(n.registerUiEventCallback=function(){l("'registerUiEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerFocusEventCallback")||(n.registerFocusEventCallback=function(){l("'registerFocusEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillDeviceOrientationEventData")||(n.fillDeviceOrientationEventData=function(){l("'fillDeviceOrientationEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerDeviceOrientationEventCallback")||(n.registerDeviceOrientationEventCallback=function(){l("'registerDeviceOrientationEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillDeviceMotionEventData")||(n.fillDeviceMotionEventData=function(){l("'fillDeviceMotionEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerDeviceMotionEventCallback")||(n.registerDeviceMotionEventCallback=function(){l("'registerDeviceMotionEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"screenOrientation")||(n.screenOrientation=function(){l("'screenOrientation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillOrientationChangeEventData")||(n.fillOrientationChangeEventData=function(){l("'fillOrientationChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerOrientationChangeEventCallback")||(n.registerOrientationChangeEventCallback=function(){l("'registerOrientationChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillFullscreenChangeEventData")||(n.fillFullscreenChangeEventData=function(){l("'fillFullscreenChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerFullscreenChangeEventCallback")||(n.registerFullscreenChangeEventCallback=function(){l("'registerFullscreenChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerRestoreOldStyle")||(n.registerRestoreOldStyle=function(){l("'registerRestoreOldStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"hideEverythingExceptGivenElement")||(n.hideEverythingExceptGivenElement=function(){l("'hideEverythingExceptGivenElement' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"restoreHiddenElements")||(n.restoreHiddenElements=function(){l("'restoreHiddenElements' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setLetterbox")||(n.setLetterbox=function(){l("'setLetterbox' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"currentFullscreenStrategy")||(n.currentFullscreenStrategy=function(){l("'currentFullscreenStrategy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"restoreOldWindowedStyle")||(n.restoreOldWindowedStyle=function(){l("'restoreOldWindowedStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"softFullscreenResizeWebGLRenderTarget")||(n.softFullscreenResizeWebGLRenderTarget=function(){l("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"doRequestFullscreen")||(n.doRequestFullscreen=function(){l("'doRequestFullscreen' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillPointerlockChangeEventData")||(n.fillPointerlockChangeEventData=function(){l("'fillPointerlockChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerPointerlockChangeEventCallback")||(n.registerPointerlockChangeEventCallback=function(){l("'registerPointerlockChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerPointerlockErrorEventCallback")||(n.registerPointerlockErrorEventCallback=function(){l("'registerPointerlockErrorEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"requestPointerLock")||(n.requestPointerLock=function(){l("'requestPointerLock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillVisibilityChangeEventData")||(n.fillVisibilityChangeEventData=function(){l("'fillVisibilityChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerVisibilityChangeEventCallback")||(n.registerVisibilityChangeEventCallback=function(){l("'registerVisibilityChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerTouchEventCallback")||(n.registerTouchEventCallback=function(){l("'registerTouchEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillGamepadEventData")||(n.fillGamepadEventData=function(){l("'fillGamepadEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerGamepadEventCallback")||(n.registerGamepadEventCallback=function(){l("'registerGamepadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerBeforeUnloadEventCallback")||(n.registerBeforeUnloadEventCallback=function(){l("'registerBeforeUnloadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"fillBatteryEventData")||(n.fillBatteryEventData=function(){l("'fillBatteryEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"battery")||(n.battery=function(){l("'battery' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerBatteryEventCallback")||(n.registerBatteryEventCallback=function(){l("'registerBatteryEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setCanvasElementSize")||(n.setCanvasElementSize=function(){l("'setCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getCanvasElementSize")||(n.getCanvasElementSize=function(){l("'getCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"polyfillSetImmediate")||(n.polyfillSetImmediate=function(){l("'polyfillSetImmediate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"demangle")||(n.demangle=function(){l("'demangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"demangleAll")||(n.demangleAll=function(){l("'demangleAll' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"jsStackTrace")||(n.jsStackTrace=function(){l("'jsStackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stackTrace")||(n.stackTrace=function(){l("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getEnvStrings")||(n.getEnvStrings=function(){l("'getEnvStrings' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"checkWasiClock")||(n.checkWasiClock=function(){l("'checkWasiClock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeI53ToI64")||(n.writeI53ToI64=function(){l("'writeI53ToI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeI53ToI64Clamped")||(n.writeI53ToI64Clamped=function(){l("'writeI53ToI64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeI53ToI64Signaling")||(n.writeI53ToI64Signaling=function(){l("'writeI53ToI64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeI53ToU64Clamped")||(n.writeI53ToU64Clamped=function(){l("'writeI53ToU64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeI53ToU64Signaling")||(n.writeI53ToU64Signaling=function(){l("'writeI53ToU64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readI53FromI64")||(n.readI53FromI64=function(){l("'readI53FromI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readI53FromU64")||(n.readI53FromU64=function(){l("'readI53FromU64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"convertI32PairToI53")||(n.convertI32PairToI53=function(){l("'convertI32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"convertU32PairToI53")||(n.convertU32PairToI53=function(){l("'convertU32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"uncaughtExceptionCount")||(n.uncaughtExceptionCount=function(){l("'uncaughtExceptionCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"exceptionLast")||(n.exceptionLast=function(){l("'exceptionLast' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"exceptionCaught")||(n.exceptionCaught=function(){l("'exceptionCaught' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ExceptionInfo")||(n.ExceptionInfo=function(){l("'ExceptionInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"CatchInfo")||(n.CatchInfo=function(){l("'CatchInfo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"exception_addRef")||(n.exception_addRef=function(){l("'exception_addRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"exception_decRef")||(n.exception_decRef=function(){l("'exception_decRef' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"Browser")||(n.Browser=function(){l("'Browser' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"funcWrappers")||(n.funcWrappers=function(){l("'funcWrappers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getFuncWrapper")||(n.getFuncWrapper=function(){l("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setMainLoop")||(n.setMainLoop=function(){l("'setMainLoop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"wget")||(n.wget=function(){l("'wget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"FS")||(n.FS=function(){l("'FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"MEMFS")||(n.MEMFS=function(){l("'MEMFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"TTY")||(n.TTY=function(){l("'TTY' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"PIPEFS")||(n.PIPEFS=function(){l("'PIPEFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SOCKFS")||(n.SOCKFS=function(){l("'SOCKFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"_setNetworkCallback")||(n._setNetworkCallback=function(){l("'_setNetworkCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"tempFixedLengthArray")||(n.tempFixedLengthArray=function(){l("'tempFixedLengthArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"miniTempWebGLFloatBuffers")||(n.miniTempWebGLFloatBuffers=function(){l("'miniTempWebGLFloatBuffers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"heapObjectForWebGLType")||(n.heapObjectForWebGLType=function(){l("'heapObjectForWebGLType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"heapAccessShiftForWebGLHeap")||(n.heapAccessShiftForWebGLHeap=function(){l("'heapAccessShiftForWebGLHeap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),n.GL=p,Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGet")||(n.emscriptenWebGLGet=function(){l("'emscriptenWebGLGet' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"computeUnpackAlignedImageSize")||(n.computeUnpackAlignedImageSize=function(){l("'computeUnpackAlignedImageSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGetTexPixelData")||(n.emscriptenWebGLGetTexPixelData=function(){l("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGetUniform")||(n.emscriptenWebGLGetUniform=function(){l("'emscriptenWebGLGetUniform' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"webglGetUniformLocation")||(n.webglGetUniformLocation=function(){l("'webglGetUniformLocation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"webglPrepareUniformLocationsBeforeFirstUse")||(n.webglPrepareUniformLocationsBeforeFirstUse=function(){l("'webglPrepareUniformLocationsBeforeFirstUse' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"webglGetLeftBracePos")||(n.webglGetLeftBracePos=function(){l("'webglGetLeftBracePos' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGetVertexAttrib")||(n.emscriptenWebGLGetVertexAttrib=function(){l("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGetBufferBinding")||(n.emscriptenWebGLGetBufferBinding=function(){l("'emscriptenWebGLGetBufferBinding' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLValidateMapBufferTarget")||(n.emscriptenWebGLValidateMapBufferTarget=function(){l("'emscriptenWebGLValidateMapBufferTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"writeGLArray")||(n.writeGLArray=function(){l("'writeGLArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"AL")||(n.AL=function(){l("'AL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SDL_unicode")||(n.SDL_unicode=function(){l("'SDL_unicode' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SDL_ttfContext")||(n.SDL_ttfContext=function(){l("'SDL_ttfContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SDL_audio")||(n.SDL_audio=function(){l("'SDL_audio' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SDL")||(n.SDL=function(){l("'SDL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"SDL_gfx")||(n.SDL_gfx=function(){l("'SDL_gfx' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"GLUT")||(n.GLUT=function(){l("'GLUT' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"EGL")||(n.EGL=function(){l("'EGL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"GLFW_Window")||(n.GLFW_Window=function(){l("'GLFW_Window' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"GLFW")||(n.GLFW=function(){l("'GLFW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"GLEW")||(n.GLEW=function(){l("'GLEW' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"IDBStore")||(n.IDBStore=function(){l("'IDBStore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"runAndAbortIfError")||(n.runAndAbortIfError=function(){l("'runAndAbortIfError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_handle_array")||(n.emval_handle_array=function(){l("'emval_handle_array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_free_list")||(n.emval_free_list=function(){l("'emval_free_list' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_symbols")||(n.emval_symbols=function(){l("'emval_symbols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"init_emval")||(n.init_emval=function(){l("'init_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"count_emval_handles")||(n.count_emval_handles=function(){l("'count_emval_handles' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"get_first_emval")||(n.get_first_emval=function(){l("'get_first_emval' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getStringOrSymbol")||(n.getStringOrSymbol=function(){l("'getStringOrSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"requireHandle")||(n.requireHandle=function(){l("'requireHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_newers")||(n.emval_newers=function(){l("'emval_newers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"craftEmvalAllocator")||(n.craftEmvalAllocator=function(){l("'craftEmvalAllocator' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_get_global")||(n.emval_get_global=function(){l("'emval_get_global' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emval_methodCallers")||(n.emval_methodCallers=function(){l("'emval_methodCallers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"InternalError")||(n.InternalError=function(){l("'InternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"BindingError")||(n.BindingError=function(){l("'BindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UnboundTypeError")||(n.UnboundTypeError=function(){l("'UnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"PureVirtualError")||(n.PureVirtualError=function(){l("'PureVirtualError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"init_embind")||(n.init_embind=function(){l("'init_embind' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"throwInternalError")||(n.throwInternalError=function(){l("'throwInternalError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"throwBindingError")||(n.throwBindingError=function(){l("'throwBindingError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"throwUnboundTypeError")||(n.throwUnboundTypeError=function(){l("'throwUnboundTypeError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ensureOverloadTable")||(n.ensureOverloadTable=function(){l("'ensureOverloadTable' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"exposePublicSymbol")||(n.exposePublicSymbol=function(){l("'exposePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"replacePublicSymbol")||(n.replacePublicSymbol=function(){l("'replacePublicSymbol' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"extendError")||(n.extendError=function(){l("'extendError' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"createNamedFunction")||(n.createNamedFunction=function(){l("'createNamedFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registeredInstances")||(n.registeredInstances=function(){l("'registeredInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getBasestPointer")||(n.getBasestPointer=function(){l("'getBasestPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerInheritedInstance")||(n.registerInheritedInstance=function(){l("'registerInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"unregisterInheritedInstance")||(n.unregisterInheritedInstance=function(){l("'unregisterInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getInheritedInstance")||(n.getInheritedInstance=function(){l("'getInheritedInstance' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getInheritedInstanceCount")||(n.getInheritedInstanceCount=function(){l("'getInheritedInstanceCount' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getLiveInheritedInstances")||(n.getLiveInheritedInstances=function(){l("'getLiveInheritedInstances' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registeredTypes")||(n.registeredTypes=function(){l("'registeredTypes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"awaitingDependencies")||(n.awaitingDependencies=function(){l("'awaitingDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"typeDependencies")||(n.typeDependencies=function(){l("'typeDependencies' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registeredPointers")||(n.registeredPointers=function(){l("'registeredPointers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"registerType")||(n.registerType=function(){l("'registerType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"whenDependentTypesAreResolved")||(n.whenDependentTypesAreResolved=function(){l("'whenDependentTypesAreResolved' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"embind_charCodes")||(n.embind_charCodes=function(){l("'embind_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"embind_init_charCodes")||(n.embind_init_charCodes=function(){l("'embind_init_charCodes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"readLatin1String")||(n.readLatin1String=function(){l("'readLatin1String' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getTypeName")||(n.getTypeName=function(){l("'getTypeName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"heap32VectorToArray")||(n.heap32VectorToArray=function(){l("'heap32VectorToArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"requireRegisteredType")||(n.requireRegisteredType=function(){l("'requireRegisteredType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"getShiftFromSize")||(n.getShiftFromSize=function(){l("'getShiftFromSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"integerReadValueFromPointer")||(n.integerReadValueFromPointer=function(){l("'integerReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"enumReadValueFromPointer")||(n.enumReadValueFromPointer=function(){l("'enumReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"floatReadValueFromPointer")||(n.floatReadValueFromPointer=function(){l("'floatReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"simpleReadValueFromPointer")||(n.simpleReadValueFromPointer=function(){l("'simpleReadValueFromPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"runDestructors")||(n.runDestructors=function(){l("'runDestructors' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"new_")||(n.new_=function(){l("'new_' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"craftInvokerFunction")||(n.craftInvokerFunction=function(){l("'craftInvokerFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"embind__requireFunction")||(n.embind__requireFunction=function(){l("'embind__requireFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"tupleRegistrations")||(n.tupleRegistrations=function(){l("'tupleRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"structRegistrations")||(n.structRegistrations=function(){l("'structRegistrations' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"genericPointerToWireType")||(n.genericPointerToWireType=function(){l("'genericPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"constNoSmartPtrRawPointerToWireType")||(n.constNoSmartPtrRawPointerToWireType=function(){l("'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"nonConstNoSmartPtrRawPointerToWireType")||(n.nonConstNoSmartPtrRawPointerToWireType=function(){l("'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"init_RegisteredPointer")||(n.init_RegisteredPointer=function(){l("'init_RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredPointer")||(n.RegisteredPointer=function(){l("'RegisteredPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredPointer_getPointee")||(n.RegisteredPointer_getPointee=function(){l("'RegisteredPointer_getPointee' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredPointer_destructor")||(n.RegisteredPointer_destructor=function(){l("'RegisteredPointer_destructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredPointer_deleteObject")||(n.RegisteredPointer_deleteObject=function(){l("'RegisteredPointer_deleteObject' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredPointer_fromWireType")||(n.RegisteredPointer_fromWireType=function(){l("'RegisteredPointer_fromWireType' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"runDestructor")||(n.runDestructor=function(){l("'runDestructor' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"releaseClassHandle")||(n.releaseClassHandle=function(){l("'releaseClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"finalizationGroup")||(n.finalizationGroup=function(){l("'finalizationGroup' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"detachFinalizer_deps")||(n.detachFinalizer_deps=function(){l("'detachFinalizer_deps' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"detachFinalizer")||(n.detachFinalizer=function(){l("'detachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"attachFinalizer")||(n.attachFinalizer=function(){l("'attachFinalizer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"makeClassHandle")||(n.makeClassHandle=function(){l("'makeClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"init_ClassHandle")||(n.init_ClassHandle=function(){l("'init_ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle")||(n.ClassHandle=function(){l("'ClassHandle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle_isAliasOf")||(n.ClassHandle_isAliasOf=function(){l("'ClassHandle_isAliasOf' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"throwInstanceAlreadyDeleted")||(n.throwInstanceAlreadyDeleted=function(){l("'throwInstanceAlreadyDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle_clone")||(n.ClassHandle_clone=function(){l("'ClassHandle_clone' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle_delete")||(n.ClassHandle_delete=function(){l("'ClassHandle_delete' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"deletionQueue")||(n.deletionQueue=function(){l("'deletionQueue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle_isDeleted")||(n.ClassHandle_isDeleted=function(){l("'ClassHandle_isDeleted' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"ClassHandle_deleteLater")||(n.ClassHandle_deleteLater=function(){l("'ClassHandle_deleteLater' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"flushPendingDeletes")||(n.flushPendingDeletes=function(){l("'flushPendingDeletes' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"delayFunction")||(n.delayFunction=function(){l("'delayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"setDelayFunction")||(n.setDelayFunction=function(){l("'setDelayFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"RegisteredClass")||(n.RegisteredClass=function(){l("'RegisteredClass' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"shallowCopyInternalPointer")||(n.shallowCopyInternalPointer=function(){l("'shallowCopyInternalPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"downcastPointer")||(n.downcastPointer=function(){l("'downcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"upcastPointer")||(n.upcastPointer=function(){l("'upcastPointer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"validateThis")||(n.validateThis=function(){l("'validateThis' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"char_0")||(n.char_0=function(){l("'char_0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"char_9")||(n.char_9=function(){l("'char_9' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"makeLegalFunctionName")||(n.makeLegalFunctionName=function(){l("'makeLegalFunctionName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"emscriptenWebGLGetIndexed")||(n.emscriptenWebGLGetIndexed=function(){l("'emscriptenWebGLGetIndexed' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"warnOnce")||(n.warnOnce=function(){l("'warnOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stackSave")||(n.stackSave=function(){l("'stackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stackRestore")||(n.stackRestore=function(){l("'stackRestore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stackAlloc")||(n.stackAlloc=function(){l("'stackAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"AsciiToString")||(n.AsciiToString=function(){l("'AsciiToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToAscii")||(n.stringToAscii=function(){l("'stringToAscii' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UTF16ToString")||(n.UTF16ToString=function(){l("'UTF16ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToUTF16")||(n.stringToUTF16=function(){l("'stringToUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"lengthBytesUTF16")||(n.lengthBytesUTF16=function(){l("'lengthBytesUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"UTF32ToString")||(n.UTF32ToString=function(){l("'UTF32ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"stringToUTF32")||(n.stringToUTF32=function(){l("'stringToUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"lengthBytesUTF32")||(n.lengthBytesUTF32=function(){l("'lengthBytesUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"allocateUTF8")||(n.allocateUTF8=function(){l("'allocateUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(n,"allocateUTF8OnStack")||(n.allocateUTF8OnStack=function(){l("'allocateUTF8OnStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),n.writeStackCookie=lt,n.checkStackCookie=W,Object.getOwnPropertyDescriptor(n,"ALLOC_NORMAL")||Object.defineProperty(n,"ALLOC_NORMAL",{configurable:!0,get(){l("'ALLOC_NORMAL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Object.getOwnPropertyDescriptor(n,"ALLOC_STACK")||Object.defineProperty(n,"ALLOC_STACK",{configurable:!0,get(){l("'ALLOC_STACK' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}});let Fn;function vo(e){this.name="ExitStatus",this.message=`Program terminated with exit(${e})`,this.status=e}Ke=function e(){Fn||ko(),Fn||(Ke=e)};function cc(){oi(),lt()}function ko(e){if(e=e||De,dt>0||(cc(),fr(),dt>0))return;function t(){Fn||(Fn=!0,n.calledRun=!0,!$e&&(Er(),re(n),n.onRuntimeInitialized&&n.onRuntimeInitialized(),M(!n._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),Vn()))}n.setStatus?(n.setStatus("Running..."),setTimeout(()=>{setTimeout(()=>{n.setStatus("")},1),t()},1)):t(),W()}n.run=ko;function gc(){let e=ae,t=J,r=!1;ae=J=function(o){r=!0};try{let o=n._fflush;o&&o(0),["stdout","stderr"].forEach(i=>{let A=a.analyzePath(`/dev/${i}`);if(!A)return;let s=A.object,{rdev:c}=s,d=Ee.ttys[c];d&&d.output&&d.output.length&&(r=!0)})}catch(o){}ae=e,J=t,r&&Re("stdio streams had content in them that was not flushed. you should set EXIT_RUNTIME to 1 (see the FAQ), or make sure to emit a newline when you printf etc.")}function lc(e,t){if(vt=e,gc(),dr()){if(!t){let r=`program exited (with status: ${e}), but EXIT_RUNTIME is not set, so halting execution but not exiting the runtime or preventing further async execution (build with EXIT_RUNTIME=1, if you want a true shutdown)`;se(r),J(r)}}else $r();Ai(e)}function Ai(e){vt=e,dr()||(n.onExit&&n.onExit(e),$e=!0),ke(e,new vo(e))}if(n.preInit)for(typeof n.preInit=="function"&&(n.preInit=[n.preInit]);n.preInit.length>0;)n.preInit.pop()();return ko(),R.ready}}(),Ei=yc;var wc={},Qc=(()=>{var X=wc.url;return function(R={}){var n=R,re,se;n.ready=new Promise((g,u)=>{re=g,se=u});var ue=Object.assign({},n),Ie=[],De="./this.program",ve=(g,u)=>{throw u},ke=!0,Oe=!1,pe="";function Bt(g){return n.locateFile?n.locateFile(g,pe):pe+g}var Ln,je,ar,cr;(ke||Oe)&&(Oe?pe=self.location.href:typeof document!="undefined"&&document.currentScript&&(pe=document.currentScript.src),X&&(pe=X),pe.indexOf("blob:")!==0?pe=pe.substr(0,pe.replace(/[?#].*/,"").lastIndexOf("/")+1):pe="",Ln=g=>{var u=new XMLHttpRequest;return u.open("GET",g,!1),u.send(null),u.responseText},Oe&&(ar=g=>{var u=new XMLHttpRequest;return u.open("GET",g,!1),u.responseType="arraybuffer",u.send(null),new Uint8Array(u.response)}),je=(g,u,f)=>{var B=new XMLHttpRequest;B.open("GET",g,!0),B.responseType="arraybuffer",B.onload=()=>{if(B.status==200||B.status==0&&B.response){u(B.response);return}f()},B.onerror=f,B.send(null)},cr=g=>document.title=g);var gr=n.print||console.log.bind(console),St=n.printErr||console.error.bind(console);Object.assign(n,ue),ue=null,n.arguments&&(Ie=n.arguments),n.thisProgram&&(De=n.thisProgram),n.quit&&(ve=n.quit);var $t;n.wasmBinary&&($t=n.wasmBinary);var Uo=n.noExitRuntime||!0;typeof WebAssembly!="object"&&Kt("no native wasm support detected");var jn,ae,J=!1,Re,ct,be,Ne,Vt,mt,de,He,Ur;function Ft(){var g=jn.buffer;n.HEAP8=ct=new Int8Array(g),n.HEAP16=Ne=new Int16Array(g),n.HEAP32=mt=new Int32Array(g),n.HEAPU8=be=new Uint8Array(g),n.HEAPU16=Vt=new Uint16Array(g),n.HEAPU32=de=new Uint32Array(g),n.HEAPF32=He=new Float32Array(g),n.HEAPF64=Ur=new Float64Array(g)}var xr,_t=[],$e=[],vt=[],M=!1;function Hn(){if(n.preRun)for(typeof n.preRun=="function"&&(n.preRun=[n.preRun]);n.preRun.length;)Lr(n.preRun.shift());gt(_t)}function xo(){M=!0,gt($e)}function Lo(){if(n.postRun)for(typeof n.postRun=="function"&&(n.postRun=[n.postRun]);n.postRun.length;)Pe(n.postRun.shift());gt(vt)}function Lr(g){_t.unshift(g)}function yt(g){$e.unshift(g)}function Pe(g){vt.unshift(g)}var Ge=0,Qt=null,Ye=null;function jr(g){Ge++,n.monitorRunDependencies&&n.monitorRunDependencies(Ge)}function Gn(g){if(Ge--,n.monitorRunDependencies&&n.monitorRunDependencies(Ge),Ge==0&&(Qt!==null&&(clearInterval(Qt),Qt=null),Ye)){var u=Ye;Ye=null,u()}}function Kt(g){n.onAbort&&n.onAbort(g),g="Aborted("+g+")",St(g),J=!0,Re=1,g+=". Build with -sASSERTIONS for more info.";var u=new WebAssembly.RuntimeError(g);throw se(u),u}var Hr="data:application/octet-stream;base64,";function Gr(g){return g.startsWith(Hr)}var wt;wt="data:application/octet-stream;base64,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",Gr(wt)||(wt=Bt(wt));function Yn(g){if(g==wt&&$t)return new Uint8Array($t);var u=or(g);if(u)return u;if(ar)return ar(g);throw"both async and sync fetching of the wasm failed"}function lr(g){return Promise.resolve().then(()=>Yn(g))}function Yr(g,u,f){return lr(g).then(B=>WebAssembly.instantiate(B,u)).then(B=>B).then(f,B=>{St("failed to asynchronously prepare wasm: "+B),Kt(B)})}function Xn(g,u,f,B){return Yr(u,f,B)}function Wn(){var g={a:bo};function u(B,Q){var O=B.exports;return ae=O,jn=ae.z,Ft(),xr=ae.C,yt(ae.A),Gn("wasm-instantiate"),O}jr("wasm-instantiate");function f(B){u(B.instance)}if(n.instantiateWasm)try{return n.instantiateWasm(g,u)}catch(B){St("Module.instantiateWasm callback failed with error: "+B),se(B)}return Xn($t,wt,g,f).catch(se),{}}var gt=g=>{for(;g.length>0;)g.shift()(n)};function ce(g){this.excPtr=g,this.ptr=g-24,this.set_type=function(u){de[this.ptr+4>>2]=u},this.get_type=function(){return de[this.ptr+4>>2]},this.set_destructor=function(u){de[this.ptr+8>>2]=u},this.get_destructor=function(){return de[this.ptr+8>>2]},this.set_caught=function(u){u=u?1:0,ct[this.ptr+12>>0]=u},this.get_caught=function(){return ct[this.ptr+12>>0]!=0},this.set_rethrown=function(u){u=u?1:0,ct[this.ptr+13>>0]=u},this.get_rethrown=function(){return ct[this.ptr+13>>0]!=0},this.init=function(u,f){this.set_adjusted_ptr(0),this.set_type(u),this.set_destructor(f)},this.set_adjusted_ptr=function(u){de[this.ptr+16>>2]=u},this.get_adjusted_ptr=function(){return de[this.ptr+16>>2]},this.get_exception_ptr=function(){var u=rr(this.get_type());if(u)return de[this.excPtr>>2];var f=this.get_adjusted_ptr();return f!==0?f:this.excPtr}}var ge=0,qe=0;function kt(g,u,f){var B=new ce(g);throw B.init(u,f),ge=g,qe++,ge}function m(g,u,f,B,Q){}function ie(g){switch(g){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${g}`)}}function ye(){for(var g=new Array(256),u=0;u<256;++u)g[u]=String.fromCharCode(u);Jt=g}var Jt=void 0;function Qe(g){for(var u="",f=g;be[f];)u+=Jt[be[f++]];return u}var et={},Ve={},F={},lt=void 0;function W(g){throw new lt(g)}var ur=void 0;function Nt(g){throw new ur(g)}function ut(g,u,f){g.forEach(function(b){F[b]=u});function B(b){var v=f(b);v.length!==g.length&&Nt("Mismatched type converter count");for(var k=0;k<g.length;++k)Ue(g[k],v[k])}var Q=new Array(u.length),O=[],P=0;u.forEach((b,v)=>{Ve.hasOwnProperty(b)?Q[v]=Ve[b]:(O.push(b),et.hasOwnProperty(b)||(et[b]=[]),et[b].push(()=>{Q[v]=Ve[b],++P,P===O.length&&B(Q)}))}),O.length===0&&B(Q)}function Xr(g,u,f={}){var B=u.name;if(g||W(`type "${B}" must have a positive integer typeid pointer`),Ve.hasOwnProperty(g)){if(f.ignoreDuplicateRegistrations)return;W(`Cannot register type '${B}' twice`)}if(Ve[g]=u,delete F[g],et.hasOwnProperty(g)){var Q=et[g];delete et[g],Q.forEach(O=>O())}}function Ue(g,u,f={}){if(!("argPackAdvance"in u))throw new TypeError("registerType registeredInstance requires argPackAdvance");return Xr(g,u,f)}function Wr(g,u,f,B,Q){var O=ie(f);u=Qe(u),Ue(g,{name:u,fromWireType:function(P){return!!P},toWireType:function(P,b){return b?B:Q},argPackAdvance:8,readValueFromPointer:function(P){var b;if(f===1)b=ct;else if(f===2)b=Ne;else if(f===4)b=mt;else throw new TypeError("Unknown boolean type size: "+u);return this.fromWireType(b[P>>O])},destructorFunction:null})}function $n(g){if(!(this instanceof ze)||!(g instanceof ze))return!1;for(var u=this.$$.ptrType.registeredClass,f=this.$$.ptr,B=g.$$.ptrType.registeredClass,Q=g.$$.ptr;u.baseClass;)f=u.upcast(f),u=u.baseClass;for(;B.baseClass;)Q=B.upcast(Q),B=B.baseClass;return u===B&&f===Q}function dr(g){return{count:g.count,deleteScheduled:g.deleteScheduled,preservePointerOnDelete:g.preservePointerOnDelete,ptr:g.ptr,ptrType:g.ptrType,smartPtr:g.smartPtr,smartPtrType:g.smartPtrType}}function fr(g){function u(f){return f.$$.ptrType.registeredClass.name}W(u(g)+" instance already deleted")}var Er=!1;function $r(g){}function Vn(g){g.smartPtr?g.smartPtrType.rawDestructor(g.smartPtr):g.ptrType.registeredClass.rawDestructor(g.ptr)}function Vr(g){g.count.value-=1;var u=g.count.value===0;u&&Vn(g)}function Kr(g,u,f){if(u===f)return g;if(f.baseClass===void 0)return null;var B=Kr(g,u,f.baseClass);return B===null?null:f.downcast(B)}var Jr={};function dt(){return Object.keys(l).length}function ft(){var g=[];for(var u in l)l.hasOwnProperty(u)&&g.push(l[u]);return g}var Ke=[];function tt(){for(;Ke.length;){var g=Ke.pop();g.$$.deleteScheduled=!1,g.delete()}}var Dt=void 0;function zt(g){Dt=g,Ke.length&&Dt&&Dt(tt)}function Ut(){n.getInheritedInstanceCount=dt,n.getLiveInheritedInstances=ft,n.flushPendingDeletes=tt,n.setDelayFunction=zt}var l={};function Kn(g,u){for(u===void 0&&W("ptr should not be undefined");g.baseClass;)u=g.upcast(u),g=g.baseClass;return u}function zr(g,u){return u=Kn(g,u),l[u]}function bt(g,u){(!u.ptrType||!u.ptr)&&Nt("makeClassHandle requires ptr and ptrType");var f=!!u.smartPtrType,B=!!u.smartPtr;return f!==B&&Nt("Both smartPtrType and smartPtr must be specified"),u.count={value:1},fe(Object.create(g,{$$:{value:u}}))}function V(g){var u=this.getPointee(g);if(!u)return this.destructor(g),null;var f=zr(this.registeredClass,u);if(f!==void 0){if(f.$$.count.value===0)return f.$$.ptr=u,f.$$.smartPtr=g,f.clone();var B=f.clone();return this.destructor(g),B}function Q(){return this.isSmartPointer?bt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:u,smartPtrType:this,smartPtr:g}):bt(this.registeredClass.instancePrototype,{ptrType:this,ptr:g})}var O=this.registeredClass.getActualType(u),P=Jr[O];if(!P)return Q.call(this);var b;this.isConst?b=P.constPointerType:b=P.pointerType;var v=Kr(u,this.registeredClass,b.registeredClass);return v===null?Q.call(this):this.isSmartPointer?bt(b.registeredClass.instancePrototype,{ptrType:b,ptr:v,smartPtrType:this,smartPtr:g}):bt(b.registeredClass.instancePrototype,{ptrType:b,ptr:v})}var fe=function(g){return typeof FinalizationRegistry=="undefined"?(fe=u=>u,g):(Er=new FinalizationRegistry(u=>{Vr(u.$$)}),fe=u=>{var f=u.$$,B=!!f.smartPtr;if(B){var Q={$$:f};Er.register(u,Q,u)}return u},$r=u=>Er.unregister(u),fe(g))};function Zr(){if(this.$$.ptr||fr(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var g=fe(Object.create(Object.getPrototypeOf(this),{$$:{value:dr(this.$$)}}));return g.$$.count.value+=1,g.$$.deleteScheduled=!1,g}function Jn(){this.$$.ptr||fr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&W("Object already scheduled for deletion"),$r(this),Vr(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function zn(){return!this.$$.ptr}function Be(){return this.$$.ptr||fr(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&W("Object already scheduled for deletion"),Ke.push(this),Ke.length===1&&Dt&&Dt(tt),this.$$.deleteScheduled=!0,this}function Je(){ze.prototype.isAliasOf=$n,ze.prototype.clone=Zr,ze.prototype.delete=Jn,ze.prototype.isDeleted=zn,ze.prototype.deleteLater=Be}function ze(){}var Zn=48,Ir=57;function rt(g){if(g===void 0)return"_unknown";g=g.replace(/[^a-zA-Z0-9_]/g,"$");var u=g.charCodeAt(0);return u>=Zn&&u<=Ir?`_${g}`:g}function qn(g,u){return g=rt(g),{[g]:function(){return u.apply(this,arguments)}}[g]}function pr(g,u,f){if(g[u].overloadTable===void 0){var B=g[u];g[u]=function(){return g[u].overloadTable.hasOwnProperty(arguments.length)||W(`Function '${f}' called with an invalid number of arguments (${arguments.length}) - expects one of (${g[u].overloadTable})!`),g[u].overloadTable[arguments.length].apply(this,arguments)},g[u].overloadTable=[],g[u].overloadTable[B.argCount]=B}}function eo(g,u,f){n.hasOwnProperty(g)?((f===void 0||n[g].overloadTable!==void 0&&n[g].overloadTable[f]!==void 0)&&W(`Cannot register public name '${g}' twice`),pr(n,g,g),n.hasOwnProperty(f)&&W(`Cannot register multiple overloads of a function with the same number of arguments (${f})!`),n[g].overloadTable[f]=u):(n[g]=u,f!==void 0&&(n[g].numArguments=f))}function to(g,u,f,B,Q,O,P,b){this.name=g,this.constructor=u,this.instancePrototype=f,this.rawDestructor=B,this.baseClass=Q,this.getActualType=O,this.upcast=P,this.downcast=b,this.pureVirtualFunctions=[]}function Tt(g,u,f){for(;u!==f;)u.upcast||W(`Expected null or instance of ${f.name}, got an instance of ${u.name}`),g=u.upcast(g),u=u.baseClass;return g}function qr(g,u){if(u===null)return this.isReference&&W(`null is not a valid ${this.name}`),0;u.$$||W(`Cannot pass "${Qr(u)}" as a ${this.name}`),u.$$.ptr||W(`Cannot pass deleted object as a pointer of type ${this.name}`);var f=u.$$.ptrType.registeredClass,B=Tt(u.$$.ptr,f,this.registeredClass);return B}function _(g,u){var f;if(u===null)return this.isReference&&W(`null is not a valid ${this.name}`),this.isSmartPointer?(f=this.rawConstructor(),g!==null&&g.push(this.rawDestructor,f),f):0;u.$$||W(`Cannot pass "${Qr(u)}" as a ${this.name}`),u.$$.ptr||W(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.isConst&&u.$$.ptrType.isConst&&W(`Cannot convert argument of type ${u.$$.smartPtrType?u.$$.smartPtrType.name:u.$$.ptrType.name} to parameter type ${this.name}`);var B=u.$$.ptrType.registeredClass;if(f=Tt(u.$$.ptr,B,this.registeredClass),this.isSmartPointer)switch(u.$$.smartPtr===void 0&&W("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:u.$$.smartPtrType===this?f=u.$$.smartPtr:W(`Cannot convert argument of type ${u.$$.smartPtrType?u.$$.smartPtrType.name:u.$$.ptrType.name} to parameter type ${this.name}`);break;case 1:f=u.$$.smartPtr;break;case 2:if(u.$$.smartPtrType===this)f=u.$$.smartPtr;else{var Q=u.clone();f=this.rawShare(f,x.toHandle(function(){Q.delete()})),g!==null&&g.push(this.rawDestructor,f)}break;default:W("Unsupporting sharing policy")}return f}function Cr(g,u){if(u===null)return this.isReference&&W(`null is not a valid ${this.name}`),0;u.$$||W(`Cannot pass "${Qr(u)}" as a ${this.name}`),u.$$.ptr||W(`Cannot pass deleted object as a pointer of type ${this.name}`),u.$$.ptrType.isConst&&W(`Cannot convert argument of type ${u.$$.ptrType.name} to parameter type ${this.name}`);var f=u.$$.ptrType.registeredClass,B=Tt(u.$$.ptr,f,this.registeredClass);return B}function Zt(g){return this.fromWireType(mt[g>>2])}function en(g){return this.rawGetPointee&&(g=this.rawGetPointee(g)),g}function ro(g){this.rawDestructor&&this.rawDestructor(g)}function no(g){g!==null&&g.delete()}function oo(){nt.prototype.getPointee=en,nt.prototype.destructor=ro,nt.prototype.argPackAdvance=8,nt.prototype.readValueFromPointer=Zt,nt.prototype.deleteObject=no,nt.prototype.fromWireType=V}function nt(g,u,f,B,Q,O,P,b,v,k,G){this.name=g,this.registeredClass=u,this.isReference=f,this.isConst=B,this.isSmartPointer=Q,this.pointeeType=O,this.sharingPolicy=P,this.rawGetPointee=b,this.rawConstructor=v,this.rawShare=k,this.rawDestructor=G,!Q&&u.baseClass===void 0?B?(this.toWireType=qr,this.destructorFunction=null):(this.toWireType=Cr,this.destructorFunction=null):this.toWireType=_}function io(g,u,f){n.hasOwnProperty(g)||Nt("Replacing nonexistant public symbol"),n[g].overloadTable!==void 0&&f!==void 0?n[g].overloadTable[f]=u:(n[g]=u,n[g].argCount=f)}var xt=(g,u,f)=>{var B=n["dynCall_"+g];return f&&f.length?B.apply(null,[u].concat(f)):B.call(null,u)},Et=[],Ot=g=>{var u=Et[g];return u||(g>=Et.length&&(Et.length=g+1),Et[g]=u=xr.get(g)),u},Ao=(g,u,f)=>{if(g.includes("j"))return xt(g,u,f);var B=Ot(u).apply(null,f);return B},hr=(g,u)=>{var f=[];return function(){return f.length=0,Object.assign(f,arguments),Ao(g,u,f)}};function ot(g,u){g=Qe(g);function f(){return g.includes("j")?hr(g,u):Ot(u)}var B=f();return typeof B!="function"&&W(`unknown function pointer with signature ${g}: ${u}`),B}function It(g,u){var f=qn(u,function(B){this.name=u,this.message=B;var Q=new Error(B).stack;Q!==void 0&&(this.stack=this.toString()+`
`+Q.replace(/^Error(:[^\n]*)?\n/,""))});return f.prototype=Object.create(g.prototype),f.prototype.constructor=f,f.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},f}var Br=void 0;function tn(g){var u=Sr(g),f=Qe(u);return Se(u),f}function Lt(g,u){var f=[],B={};function Q(O){if(!B[O]&&!Ve[O]){if(F[O]){F[O].forEach(Q);return}f.push(O),B[O]=!0}}throw u.forEach(Q),new Br(`${g}: `+f.map(tn).join([", "]))}function so(g,u,f,B,Q,O,P,b,v,k,G,K,ee){G=Qe(G),O=ot(Q,O),b&&(b=ot(P,b)),k&&(k=ot(v,k)),ee=ot(K,ee);var z=rt(G);eo(z,function(){Lt(`Cannot construct ${G} due to unbound types`,[B])}),ut([g,u,f],B?[B]:[],function(oe){oe=oe[0];var we,le;B?(we=oe.registeredClass,le=we.instancePrototype):le=ze.prototype;var xe=qn(z,function(){if(Object.getPrototypeOf(this)!==Ct)throw new lt("Use 'new' to construct "+G);if(he.constructor_body===void 0)throw new lt(G+" has no accessible constructor");var En=he.constructor_body[arguments.length];if(En===void 0)throw new lt(`Tried to invoke ctor of ${G} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(he.constructor_body).toString()}) parameters instead!`);return En.apply(this,arguments)}),Ct=Object.create(le,{constructor:{value:xe}});xe.prototype=Ct;var he=new to(G,xe,Ct,ee,we,O,b,k);he.baseClass&&(he.baseClass.__derivedClasses===void 0&&(he.baseClass.__derivedClasses=[]),he.baseClass.__derivedClasses.push(he));var At=new nt(G,he,!0,!1,!1),ir=new nt(G+"*",he,!1,!1,!1),fn=new nt(G+" const*",he,!1,!0,!1);return Jr[g]={pointerType:ir,constPointerType:fn},io(z,xe),[At,ir,fn]})}function rn(g,u){for(var f=[],B=0;B<g;B++)f.push(de[u+B*4>>2]);return f}function mr(g){for(;g.length;){var u=g.pop(),f=g.pop();f(u)}}function nn(g,u,f,B,Q,O){var P=u.length;P<2&&W("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var b=u[1]!==null&&f!==null,v=!1,k=1;k<u.length;++k)if(u[k]!==null&&u[k].destructorFunction===void 0){v=!0;break}var G=u[0].name!=="void",K=P-2,ee=new Array(K),z=[],oe=[];return function(){arguments.length!==K&&W(`function ${g} called with ${arguments.length} arguments, expected ${K} args!`),oe.length=0;var we;z.length=b?2:1,z[0]=Q,b&&(we=u[1].toWireType(oe,this),z[1]=we);for(var le=0;le<K;++le)ee[le]=u[le+2].toWireType(oe,arguments[le]),z.push(ee[le]);var xe=B.apply(null,z);function Ct(he){if(v)mr(oe);else for(var At=b?1:2;At<u.length;At++){var ir=At===1?we:ee[At-2];u[At].destructorFunction!==null&&u[At].destructorFunction(ir)}if(G)return u[0].fromWireType(he)}return Ct(xe)}}function ao(g,u,f,B,Q,O){var P=rn(u,f);Q=ot(B,Q),ut([],[g],function(b){b=b[0];var v=`constructor ${b.name}`;if(b.registeredClass.constructor_body===void 0&&(b.registeredClass.constructor_body=[]),b.registeredClass.constructor_body[u-1]!==void 0)throw new lt(`Cannot register multiple constructors with identical number of parameters (${u-1}) for class '${b.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return b.registeredClass.constructor_body[u-1]=()=>{Lt(`Cannot construct ${b.name} due to unbound types`,P)},ut([],P,function(k){return k.splice(1,0,null),b.registeredClass.constructor_body[u-1]=nn(v,k,null,Q,O),[]}),[]})}function co(g,u,f,B,Q,O,P,b,v){var k=rn(f,B);u=Qe(u),O=ot(Q,O),ut([],[g],function(G){G=G[0];var K=`${G.name}.${u}`;u.startsWith("@@")&&(u=Symbol[u.substring(2)]),b&&G.registeredClass.pureVirtualFunctions.push(u);function ee(){Lt(`Cannot call ${K} due to unbound types`,k)}var z=G.registeredClass.instancePrototype,oe=z[u];return oe===void 0||oe.overloadTable===void 0&&oe.className!==G.name&&oe.argCount===f-2?(ee.argCount=f-2,ee.className=G.name,z[u]=ee):(pr(z,u,K),z[u].overloadTable[f-2]=ee),ut([],k,function(we){var le=nn(K,we,G,O,P,v);return z[u].overloadTable===void 0?(le.argCount=f-2,z[u]=le):z[u].overloadTable[f-2]=le,[]}),[]})}function _r(g,u,f){return g instanceof Object||W(`${f} with invalid "this": ${g}`),g instanceof u.registeredClass.constructor||W(`${f} incompatible with "this" of type ${g.constructor.name}`),g.$$.ptr||W(`cannot call emscripten binding method ${f} on deleted object`),Tt(g.$$.ptr,g.$$.ptrType.registeredClass,u.registeredClass)}function te(g,u,f,B,Q,O,P,b,v,k){u=Qe(u),Q=ot(B,Q),ut([],[g],function(G){G=G[0];var K=`${G.name}.${u}`,ee={get(){Lt(`Cannot access ${K} due to unbound types`,[f,P])},enumerable:!0,configurable:!0};return v?ee.set=()=>{Lt(`Cannot access ${K} due to unbound types`,[f,P])}:ee.set=z=>{W(K+" is a read-only property")},Object.defineProperty(G.registeredClass.instancePrototype,u,ee),ut([],v?[f,P]:[f],function(z){var oe=z[0],we={get(){var xe=_r(this,G,K+" getter");return oe.fromWireType(Q(O,xe))},enumerable:!0};if(v){v=ot(b,v);var le=z[1];we.set=function(xe){var Ct=_r(this,G,K+" setter"),he=[];v(k,Ct,le.toWireType(he,xe)),mr(he)}}return Object.defineProperty(G.registeredClass.instancePrototype,u,we),[]}),[]})}function on(){Object.assign(Xe.prototype,{get(g){return this.allocated[g]},has(g){return this.allocated[g]!==void 0},allocate(g){var u=this.freelist.pop()||this.allocated.length;return this.allocated[u]=g,u},free(g){this.allocated[g]=void 0,this.freelist.push(g)}})}function Xe(){this.allocated=[void 0],this.freelist=[]}var Ee=new Xe;function yr(g){g>=Ee.reserved&&--Ee.get(g).refcount===0&&Ee.free(g)}function go(){for(var g=0,u=Ee.reserved;u<Ee.allocated.length;++u)Ee.allocated[u]!==void 0&&++g;return g}function An(){Ee.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),Ee.reserved=Ee.allocated.length,n.count_emval_handles=go}var x={toValue:g=>(g||W("Cannot use deleted val. handle = "+g),Ee.get(g).value),toHandle:g=>{switch(g){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return Ee.allocate({refcount:1,value:g})}}};function lo(g,u){u=Qe(u),Ue(g,{name:u,fromWireType:function(f){var B=x.toValue(f);return yr(f),B},toWireType:function(f,B){return x.toHandle(B)},argPackAdvance:8,readValueFromPointer:Zt,destructorFunction:null})}function Qr(g){if(g===null)return"null";var u=typeof g;return u==="object"||u==="array"||u==="function"?g.toString():""+g}function sn(g,u){switch(u){case 2:return function(f){return this.fromWireType(He[f>>2])};case 3:return function(f){return this.fromWireType(Ur[f>>3])};default:throw new TypeError("Unknown float type: "+g)}}function a(g,u,f){var B=ie(f);u=Qe(u),Ue(g,{name:u,fromWireType:function(Q){return Q},toWireType:function(Q,O){return O},argPackAdvance:8,readValueFromPointer:sn(u,B),destructorFunction:null})}function Ae(g,u,f){switch(u){case 0:return f?function(Q){return ct[Q]}:function(Q){return be[Q]};case 1:return f?function(Q){return Ne[Q>>1]}:function(Q){return Vt[Q>>1]};case 2:return f?function(Q){return mt[Q>>2]}:function(Q){return de[Q>>2]};default:throw new TypeError("Unknown integer type: "+g)}}function uo(g,u,f,B,Q){u=Qe(u),Q===-1&&(Q=4294967295);var O=ie(f),P=K=>K;if(B===0){var b=32-8*f;P=K=>K<<b>>>b}var v=u.includes("unsigned"),k=(K,ee)=>{},G;v?G=function(K,ee){return k(ee,this.name),ee>>>0}:G=function(K,ee){return k(ee,this.name),ee},Ue(g,{name:u,fromWireType:P,toWireType:G,argPackAdvance:8,readValueFromPointer:Ae(u,O,B!==0),destructorFunction:null})}function fo(g,u,f){var B=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],Q=B[u];function O(P){P=P>>2;var b=de,v=b[P],k=b[P+1];return new Q(b.buffer,k,v)}f=Qe(f),Ue(g,{name:f,fromWireType:O,argPackAdvance:8,readValueFromPointer:O},{ignoreDuplicateRegistrations:!0})}var Eo=(g,u,f,B)=>{if(!(B>0))return 0;for(var Q=f,O=f+B-1,P=0;P<g.length;++P){var b=g.charCodeAt(P);if(b>=55296&&b<=57343){var v=g.charCodeAt(++P);b=65536+((b&1023)<<10)|v&1023}if(b<=127){if(f>=O)break;u[f++]=b}else if(b<=2047){if(f+1>=O)break;u[f++]=192|b>>6,u[f++]=128|b&63}else if(b<=65535){if(f+2>=O)break;u[f++]=224|b>>12,u[f++]=128|b>>6&63,u[f++]=128|b&63}else{if(f+3>=O)break;u[f++]=240|b>>18,u[f++]=128|b>>12&63,u[f++]=128|b>>6&63,u[f++]=128|b&63}}return u[f]=0,f-Q},Io=(g,u,f)=>Eo(g,be,u,f),po=g=>{for(var u=0,f=0;f<g.length;++f){var B=g.charCodeAt(f);B<=127?u++:B<=2047?u+=2:B>=55296&&B<=57343?(u+=4,++f):u+=3}return u},an=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):void 0,Co=(g,u,f)=>{for(var B=u+f,Q=u;g[Q]&&!(Q>=B);)++Q;if(Q-u>16&&g.buffer&&an)return an.decode(g.subarray(u,Q));for(var O="";u<Q;){var P=g[u++];if(!(P&128)){O+=String.fromCharCode(P);continue}var b=g[u++]&63;if((P&224)==192){O+=String.fromCharCode((P&31)<<6|b);continue}var v=g[u++]&63;if((P&240)==224?P=(P&15)<<12|b<<6|v:P=(P&7)<<18|b<<12|v<<6|g[u++]&63,P<65536)O+=String.fromCharCode(P);else{var k=P-65536;O+=String.fromCharCode(55296|k>>10,56320|k&1023)}}return O},ho=(g,u)=>g?Co(be,g,u):"";function qt(g,u){u=Qe(u);var f=u==="std::string";Ue(g,{name:u,fromWireType:function(B){var Q=de[B>>2],O=B+4,P;if(f)for(var b=O,v=0;v<=Q;++v){var k=O+v;if(v==Q||be[k]==0){var G=k-b,K=ho(b,G);P===void 0?P=K:(P+=String.fromCharCode(0),P+=K),b=k+1}}else{for(var ee=new Array(Q),v=0;v<Q;++v)ee[v]=String.fromCharCode(be[O+v]);P=ee.join("")}return Se(B),P},toWireType:function(B,Q){Q instanceof ArrayBuffer&&(Q=new Uint8Array(Q));var O,P=typeof Q=="string";P||Q instanceof Uint8Array||Q instanceof Uint8ClampedArray||Q instanceof Int8Array||W("Cannot pass non-string to std::string"),f&&P?O=po(Q):O=Q.length;var b=Mr(4+O+1),v=b+4;if(de[b>>2]=O,f&&P)Io(Q,v,O+1);else if(P)for(var k=0;k<O;++k){var G=Q.charCodeAt(k);G>255&&(Se(v),W("String has UTF-16 code units that do not fit in 8 bits")),be[v+k]=G}else for(var k=0;k<O;++k)be[v+k]=Q[k];return B!==null&&B.push(Se,b),b},argPackAdvance:8,readValueFromPointer:Zt,destructorFunction:function(B){Se(B)}})}var jt=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):void 0,wr=(g,u)=>{for(var f=g,B=f>>1,Q=B+u/2;!(B>=Q)&&Vt[B];)++B;if(f=B<<1,f-g>32&&jt)return jt.decode(be.subarray(g,f));for(var O="",P=0;!(P>=u/2);++P){var b=Ne[g+P*2>>1];if(b==0)break;O+=String.fromCharCode(b)}return O},Me=(g,u,f)=>{if(f===void 0&&(f=2147483647),f<2)return 0;f-=2;for(var B=u,Q=f<g.length*2?f/2:g.length,O=0;O<Q;++O){var P=g.charCodeAt(O);Ne[u>>1]=P,u+=2}return Ne[u>>1]=0,u-B},Bo=g=>g.length*2,mo=(g,u)=>{for(var f=0,B="";!(f>=u/4);){var Q=mt[g+f*4>>2];if(Q==0)break;if(++f,Q>=65536){var O=Q-65536;B+=String.fromCharCode(55296|O>>10,56320|O&1023)}else B+=String.fromCharCode(Q)}return B},_o=(g,u,f)=>{if(f===void 0&&(f=2147483647),f<4)return 0;for(var B=u,Q=B+f-4,O=0;O<g.length;++O){var P=g.charCodeAt(O);if(P>=55296&&P<=57343){var b=g.charCodeAt(++O);P=65536+((P&1023)<<10)|b&1023}if(mt[u>>2]=P,u+=4,u+4>Q)break}return mt[u>>2]=0,u-B},er=g=>{for(var u=0,f=0;f<g.length;++f){var B=g.charCodeAt(f);B>=55296&&B<=57343&&++f,u+=4}return u},tr=function(g,u,f){f=Qe(f);var B,Q,O,P,b;u===2?(B=wr,Q=Me,P=Bo,O=()=>Vt,b=1):u===4&&(B=mo,Q=_o,P=er,O=()=>de,b=2),Ue(g,{name:f,fromWireType:function(v){for(var k=de[v>>2],G=O(),K,ee=v+4,z=0;z<=k;++z){var oe=v+4+z*u;if(z==k||G[oe>>b]==0){var we=oe-ee,le=B(ee,we);K===void 0?K=le:(K+=String.fromCharCode(0),K+=le),ee=oe+u}}return Se(v),K},toWireType:function(v,k){typeof k!="string"&&W(`Cannot pass non-string to C++ string type ${f}`);var G=P(k),K=Mr(4+G+u);return de[K>>2]=G>>b,Q(k,K+4,G+u),v!==null&&v.push(Se,K),K},argPackAdvance:8,readValueFromPointer:Zt,destructorFunction:function(v){Se(v)}})};function cn(g,u){u=Qe(u),Ue(g,{isVoid:!0,name:u,argPackAdvance:0,fromWireType:function(){},toWireType:function(f,B){}})}function Dr(g,u){var f=Ve[g];return f===void 0&&W(u+" has unknown type "+tn(g)),f}function gn(g,u,f){g=x.toValue(g),u=Dr(u,"emval::as");var B=[],Q=x.toHandle(B);return de[f>>2]=Q,u.toWireType(B,g)}function Ce(g){var u=[];return de[g>>2]=x.toHandle(u),u}var yo={};function ln(g){var u=yo[g];return u===void 0?Qe(g):u}var it=[];function br(g,u,f,B,Q){return g=it[g],u=x.toValue(u),f=ln(f),g(u,f,Ce(B),Q)}function Ht(g,u,f,B){g=it[g],u=x.toValue(u),f=ln(f),g(u,f,null,B)}function Qo(g){var u=it.length;return it.push(g),u}function wo(g,u){for(var f=new Array(g),B=0;B<g;++B)f[B]=Dr(de[u+B*4>>2],"parameter "+B);return f}var We=[];function Rt(g,u){var f=wo(g,u),B=f[0],Q=B.name+"_$"+f.slice(1).map(function(v){return v.name}).join("_")+"$",O=We[Q];if(O!==void 0)return O;var P=new Array(g-1),b=(v,k,G,K)=>{for(var ee=0,z=0;z<g-1;++z)P[z]=f[z+1].readValueFromPointer(K+ee),ee+=f[z+1].argPackAdvance;for(var oe=v[k].apply(v,P),z=0;z<g-1;++z)f[z+1].deleteObject&&f[z+1].deleteObject(P[z]);if(!B.isVoid)return B.toWireType(G,oe)};return O=Qo(b),We[Q]=O,O}function $(g){g>4&&(Ee.get(g).refcount+=1)}function Tr(g){var u=x.toValue(g);mr(u),yr(g)}function Do(g,u){g=Dr(g,"_emval_take_value");var f=g.readValueFromPointer(u);return x.toHandle(f)}var un=()=>{Kt("")},pt=(g,u,f)=>be.copyWithin(g,u,u+f),dn=g=>{Kt("OOM")},Or=g=>{var u=be.length;g>>>=0,dn(g)};ye(),lt=n.BindingError=class extends Error{constructor(u){super(u),this.name="BindingError"}},ur=n.InternalError=class extends Error{constructor(u){super(u),this.name="InternalError"}},Je(),Ut(),oo(),Br=n.UnboundTypeError=It(Error,"UnboundTypeError"),on(),An();var bo={w:kt,q:m,u:Wr,y:so,x:ao,i:co,k:te,t:lo,o:a,e:uo,a:fo,n:qt,l:tr,v:cn,j:gn,p:br,f:Ht,c:yr,d:Rt,b:$,g:Tr,h:Do,m:un,s:pt,r:Or},Rr=Wn(),Pr=()=>(Pr=ae.A)(),Mr=g=>(Mr=ae.B)(g),Sr=g=>(Sr=ae.D)(g),Pt=n.__embind_initialize_bindings=()=>(Pt=n.__embind_initialize_bindings=ae.E)(),To=()=>(To=ae.__errno_location)(),Se=g=>(Se=ae.F)(g),rr=g=>(rr=ae.G)(g),nr=n._vertexShaderSource=8688;function Mt(g){try{for(var u=atob(g),f=new Uint8Array(u.length),B=0;B<u.length;++B)f[B]=u.charCodeAt(B);return f}catch(Q){throw new Error("Converting base64 string to bytes failed.")}}function or(g){if(Gr(g))return Mt(g.slice(Hr.length))}var Gt;Ye=function g(){Gt||Yt(),Gt||(Ye=g)};function Yt(){if(Ge>0||(Hn(),Ge>0))return;function g(){Gt||(Gt=!0,n.calledRun=!0,!J&&(xo(),re(n),n.onRuntimeInitialized&&n.onRuntimeInitialized(),Lo()))}n.setStatus?(n.setStatus("Running..."),setTimeout(function(){setTimeout(function(){n.setStatus("")},1),g()},1)):g()}if(n.preInit)for(typeof n.preInit=="function"&&(n.preInit=[n.preInit]);n.preInit.length>0;)n.preInit.pop()();return Yt(),R.ready}})(),Ii=Qc;var kn=class{constructor(R){_e(this,"canvas");_e(this,"locateFile");_e(this,"_glName");_e(this,"_solutionWasm");_e(this,"ctx");_e(this,"pl",{onResults:R=>this.onResults(R.get(0).glName)});this.locateFile=n=>`${R}/${n}`}bindTexture(){var R;(R=this.ctx)==null||R.activeTexture(this.ctx.TEXTURE0),this.bindTexture2d(this._glName)}async initialize(){this._solutionWasm=new this.SolutionWasm;let R=new this.StringList;R.push_back("segmentation_mask");let n=this.PacketListener.implement(this.pl);return this._solutionWasm.attachMultiListener(R,n),R.delete(),this._solutionWasm.loadGraph(await this.fetchFile("selfie_segmentation.binarypb")),this}setCanvas(R){this.canvas=R,this.createContext(R,!0,!0,{}),this._glName=this.createTexture()}async fetchFile(R){try{let n=await fetch(this.locateFile(R));if(!n.ok)throw new Error(`fetch status: ${n.status}`);return n.arrayBuffer()}catch(n){throw new Error(`Failed to fetch file: ${R} ${n}`)}}async changeModel(R="selfie_segmentation_landscape.tflite"){R=typeof R=="string"?await this.fetchFile(R):R,this._solutionWasm.overrideFile("third_party/mediapipe/modules/selfie_segmentation/selfie_segmentation_landscape.tflite",R);let n=new this.GraphOptionChangeRequestList;n.push_back({calculatorIndex:1,calculatorName:"",calculatorType:"GlScalerCalculator",fieldName:"flip_horizontal",valueBoolean:!1,valueNumber:0,valueString:""}),n.push_back({calculatorIndex:0,calculatorType:"ConstantSidePacketCalculator",calculatorName:"ConstantSidePacketCalculatorModelSelection",fieldName:"int_value",valueBoolean:!1,valueNumber:1,valueString:""}),n.push_back({calculatorIndex:0,calculatorType:"InferenceCalculator",calculatorName:"",fieldName:"use_cpu_inference",valueBoolean:!1,valueNumber:0,valueString:""}),this._solutionWasm.changeOptions(n)}send(R,n){let re=new this.PacketDataList;re.pushTexture2d({stream:"input_frames_gpu",timestamp:performance.now()*1e3,glName:this._glName,width:R,height:n}),this._solutionWasm.send(re),re.delete()}close(){var R,n,re;(R=this.canvas)==null||delete R.getContext,delete this.readyPromiseResolve,delete this.readyPromiseReject,this.pl.onResults=null,delete this.onResults,(n=this.deleteTexture)==null||n.call(this,this._glName),(re=this._solutionWasm)==null||re.delete();for(let se of Object.keys(this))delete this[se]}};var Nn=0,xn=class xn{constructor(R){this.core=R;_e(this,"seq");_e(this,"_core");_e(this,"log");_e(this,"preLoadPromise");_e(this,"initError",null);_e(this,"startResolve");_e(this,"startReject");_e(this,"mediaPipeSolutions");_e(this,"assetsPath");Nn=Nn+1,this.seq=Nn,this._core=R,this.log=R.log.createChild({id:`${this.getAlias()}${Nn}`}),this.log.info("created"),R.assetsPath&&(this.preLoadPromise=this.preload(R.assetsPath))}async preload(R){try{this._core.room.videoManager.Wasm=await Ii(),this.assetsPath=`${R}/selfie_segmentation/`;let n=new kn(this.assetsPath);this.mediaPipeSolutions=await Ei(n),await this.mediaPipeSolutions.initialize(),await this._core.room.videoManager.initVirtualBackground(this.mediaPipeSolutions)}catch(n){let{RtcError:re,ErrorCode:se}=this._core.errorModule,ue=new re({code:se.INVALID_OPERATION,message:`VirtualBackground preload error, please check your assetsPath. detail: ${n}`});throw this.initError=ue,ue}}getName(){return xn.Name}getAlias(){return"vb"}getValidateRule(R){switch(R){case"start":return ui(this._core);case"update":return di(this._core);case"stop":return fi(this._core)}}getGroup(){return"vb"}async start(R){let{type:n="blur",src:re}=R;if(this.preLoadPromise||(this.preLoadPromise=this.preload(this._core.assetsPath)),await this.preLoadPromise,this.initError)throw this.initError;let{auth:se,sign:ue,timestamp:Ie}=await li({sdkAppId:R.sdkAppId,userId:R.userId,userSig:R.userSig,core:this._core});if(!se){let{RtcError:De,ErrorCode:ve,ErrorCodeDictionary:ke}=this._core.errorModule;throw new De({code:ve.SERVER_ERROR,extraCode:ke.NEED_TO_BUY,messageParams:{value:"Virtual Background",url:"https://cloud.tencent.com/document/product/647/85386"}})}return this.core.room.videoManager.setVirtualBackground({type:n,imageUrl:re})}async update(R){let{type:n,src:re}=R;return this.core.room.videoManager.setVirtualBackground({type:n,imageUrl:re})}async stop(){return this.core.room.videoManager.setVirtualBackground()}};_e(xn,"Name","VirtualBackground");var Un=xn;var Dc=Un;return mc(bc);})().default;
