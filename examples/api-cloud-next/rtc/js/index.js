/* eslint-disable no-undef */
/* global $ TRTC Toastify */
/* eslint-disable require-jsdoc */
let trtcWeakRef;
let weakRefIntervalId = -1;
const fetchUrl = 'https://service.trtc.qcloud.com/release/UserSigService';
const defaultSdkAppId = Number(new URLSearchParams(location.search).get('sdkAppId') || **********); // unified sdkAppId for Demos on all platforms.
// const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
// 与 native 互通的 appid
// const defaultSdkAppId = **********; // unified sdkAppId for Demos on all platforms.
const accountType = 14418;

window.onunhandledrejection = (event) => {
  ToastError(event.reason);
};

const urlParams = new URLSearchParams(location.search);
const autoReceiveAudio = urlParams.get('autoReceiveAudio');
if (autoReceiveAudio) {
  document.getElementById('auto-receive-audio').selectedIndex = +autoReceiveAudio;
}

const autoReceiveVideo = urlParams.get('autoReceiveVideo');
if (autoReceiveVideo) {
  document.getElementById('auto-receive-video').selectedIndex = +autoReceiveVideo;
}
const spc = urlParams.get('spc');
if (spc) {
  $('#enable-spc')[0].checked = (Boolean(+spc));
}

const small = urlParams.get('small');
if (small) {
  $('#small')[0].checked = true;
}

const enableAutoPlayDialog = urlParams.get('enableAutoPlayDialog') ? Boolean(Number(urlParams.get('enableAutoPlayDialog'))) : true;
let recordPCM;

setTimeout(() => {
  const checkboxElement = document.getElementById('add_playout_delay').parentElement;
  checkboxElement.innerHTML = `${checkboxElement.innerHTML}
    <input type="text" name="playoutDelayMin" id="playoutDelayMin" value="50" style="width: 50px;">
    <input type="text" name="playoutDelayMax" id="playoutDelayMax" value="100" style="width: 50px;">`;
  const checkboxElementJitterBufferDelay = document.getElementById('jitterBufferDelay').parentElement;
  checkboxElementJitterBufferDelay.innerHTML = `${checkboxElementJitterBufferDelay.innerHTML}
    <input type="text" name="jitterBufferDelayMain" placeholder="main" id="jitterBufferDelayMain" value="500" style="width: 50px;">
    <input type="text" name="jitterBufferDelayAux" placeholder="aux" id="jitterBufferDelayAux" value="0" style="width: 50px;">`;
}, 1000);

// preset before starting RTC
class Presetting {
  init() {
    // populate userId/roomId/privMap
    $('#userId').val(`u${parseInt(Math.random() * 100000000)}`);
    $('#userId').trigger('change'); // 触发 bootstrap 的 is-filled
    $('#roomId').val(new Date().getMinutes() * 10 + 66666);
    $('#roomId').trigger('change');
    const roomId = this.query('roomId');
    const userId = this.query('userId');
    if (roomId) {
      $('#roomId').val(roomId);
    }
    if (userId) {
      $('#userId').val(userId);
    }
    const audioProfile = this.query('audioProfile');
    if (audioProfile) {
      $('#audio-profile').val(audioProfile);
    }
    const AIDenoiser = this.query('AIDenoiser') || 'false';
    if (AIDenoiser === 'true') {
      $('#ai-denoiser').attr('checked', true);
    } else {
      $('#ai-denoiser').attr('checked', false);
    }
    const enableSEI = this.query('enableSEI');
    if (enableSEI === 'true') {
      $('#enable-sei').attr('checked', true);
    } else {
      $('#enable-sei').attr('checked', false);
    }

    if (this.query('forceTurn')) {
      document.getElementById('force-turn').checked = true;
    }

    $('#switch-room-roomId').val(Number($('#roomId').val()) + 1)
      .trigger('change');
  }

  query(name) {
    const match = window.location.search.match(new RegExp(`(\\?|&)${name}=([^&]*)(&|$)`));
    return !match ? '' : decodeURIComponent(match[2]);
  }

  login(share, callback) {
    let userId = $('#userId')
      .val();
    if (share) {
      userId = `share_${parseInt(Math.random() * 100000000)}`;
    }
    const sdkAppId = this.query('sdkAppId') || defaultSdkAppId;
    const roomId = $('#roomId')
      .val();
    if (genTestUserSig && location.search.includes('secretKey')) {
      const { userSig } = genTestUserSig(userId);
      return callback({
        sdkAppId,
        userId,
        userSig,
        roomId,
        privateMapKey: '',
      });
    }
    $.ajax({
      type: 'POST',
      url: fetchUrl,
      dataType: 'json',
      data: JSON.stringify({
        pwd: '********',
        appid: parseInt(sdkAppId),
        roomnum: parseInt(roomId),
        privMap: 255,
        identifier: userId,
        accounttype: accountType,
      }),
      success(json) {
        if (json && json.errorCode === 0) {
          const { userSig } = json.data;
          const privateMapKey = json.data.privMapEncrypt;
          callback({
            sdkAppId,
            userId,
            userSig,
            roomId,
            privateMapKey,
          });
        } else {
          console.error(`got invalid json:${json}`);
        }
      },
      error(err) {
        console.error('failed to retreive userSig');
      },
    });
  }
}

async function getUserSigFromServer(sdkAppId, roomId, userId) {
  const response = await $.ajax({
    type: 'POST',
    url: fetchUrl,
    dataType: 'json',
    data: JSON.stringify({
      pwd: '********',
      appid: parseInt(sdkAppId),
      roomnum: parseInt(roomId),
      privMap: 255,
      identifier: userId,
      accounttype: 14418,
    })
  });
  if (!response.data) return undefined;
  return response.data.userSig;
}

const extraLocalViews = [];

TRTC.setLogLevel(1);
let selectedCameraId = '';
let selectedMicrophoneId = '';
let selectedSpeakerId = '';
updateDeviceList();


// 比较版本号大小的函数
function compareVersions(v1, v2) {
  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);
  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  return 0;
}

class RtcClient {
  constructor(options) {
    this.sdkAppId_ = Number(options.sdkAppId);
    this.userId_ = options.userId;
    this.userSig_ = options.userSig;
    this.roomId_ = options.roomId;
    this.strRoomId_ = options.strRoomId_ || new URLSearchParams(location.search).get('strRoomId');
    $('#strRoomId').val(this.strRoomId_);
    if (this.strRoomId_) {
      $('#strRoomId').trigger('change');
    }
    this.privateMapKey_ = options.privateMapKey;

    this.isJoined_ = false;
    this.isPublished_ = false;
    this.initTRTC();
  }

  updateParams(options) {
    console.log('updateParams', options);
    if (typeof options.userId !== 'undefined' && typeof options.userSig !== 'undefined') {
      this.userId_ = options.userId;
      this.userSig_ = options.userSig;
    }
    if (typeof options.roomId !== 'undefined') {
      this.roomId_ = options.roomId;
    }
    if (typeof options.strRoomId !== 'undefined') {
      this.strRoomId_ = options.strRoomId;
    }
  }

  initTRTC() {
    if (this.trtc) return;

    const pluginsList = [];
    if (pluginMap.VirtualBackground && pluginMap.VirtualBackground.loaded) pluginsList.push(VirtualBackground);
    if (pluginMap.Watermark && pluginMap.Watermark.loaded) pluginsList.push(Watermark);
    if (pluginMap.Beauty && pluginMap.Beauty.loaded) pluginsList.push(Beauty);
    if (pluginMap.BasicBeauty && pluginMap.BasicBeauty.loaded) pluginsList.push(BasicBeauty);
    if (pluginMap.DeviceDetector && pluginMap.DeviceDetector.loaded) pluginsList.push(DeviceDetector);
    // if (pluginMap.Debug?.loaded) pluginsList.push(Debug);
    if (typeof CustomEncryption !== 'undefined' && pluginMap.CustomEncryption && pluginMap.CustomEncryption.loaded) {
      pluginsList.push(CustomEncryption);
    }
    if (pluginMap.CrossRoom && pluginMap.CrossRoom.loaded) pluginsList.push(CrossRoom);
    // eslint-disable-next-line max-len
    if (pluginMap.TRTCVideoDecoder && pluginMap.TRTCVideoDecoder.loaded) pluginsList.push(window.TRTCVideoDecoder || VideoDecoderPlugin);
    if (pluginMap.VoiceChanger && pluginMap.VoiceChanger.loaded) pluginsList.push(VoiceChanger);
    if (pluginMap.SmallStreamAutoSwitcher && pluginMap.SmallStreamAutoSwitcher.loaded) pluginsList.push(SmallStreamAutoSwitcher);
    if (pluginMap.VideoMixer && pluginMap.VideoMixer.loaded) pluginsList.push(VideoMixer);

    console.log('pluginsList', pluginsList);

    // 更新插件状态到界面
    if (document.getElementById('plugins-status')) {
      // 直接从pluginMap获取所有插件信息
      const statusHTML = Object.keys(pluginMap).map((pluginName) => {
        const isLoaded = pluginMap[pluginName].loaded;
        return isLoaded ? `<span>${pluginName}</span>` : `<span style="color: red;">${pluginName}</span>`;
      })
        .join(', ');

      document.getElementById('plugins-status').innerHTML = `${statusHTML}`;
    }

    // 获取当前版本信息
    const version = new URLSearchParams(location.search).get('version')
      || $('#version').val();

    // 根据版本确定静态资源路径
    function getAssetsPath(version) {
      if (version.includes('dev')) return '/static/';
      if (version === 'latest') return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/assets';
      if (version === 'build') return '../dist/npm-package/assets';
      // 如果version小于5.10.0，则将version设为5.10.0
      version = compareVersions(version, '5.10.0') < 0 ? '5.10.0-beta.1' : version;
      return `https://unpkg.com/trtc-sdk-v5@${version}/assets`;
    }

    // 根据版本获取SDK根路径
    function getRootPathForSDK(version) {
      if (version.includes('dev')) return '/';
      if (version === 'latest') return 'https://web.sdk.cloudcachetci.com/trtc/webrtc/v5/dist/';
      if (version === 'build') return '../dist/npm-package/';
      return `https://unpkg.com/trtc-sdk-v5@${version}/`;
    }

    const assetsPath = getAssetsPath(version);

    // 将资源路径信息更新到界面
    if (document.getElementById('sdk-path-info')) {
      // 使用window上挂载的getRootPathForSDK函数
      const sdkPath = window.getRootPathForSDK ? window.getRootPathForSDK(version) : '未知';
      document.getElementById('sdk-path-info').textContent = `SDK和插件: ${sdkPath}, 资源: ${assetsPath}`;
    }

    // 更新简短状态到顶部状态栏
    if (document.getElementById('sdk-status')) {
      // 只设置文本内容为版本号，不添加状态图标
      document.getElementById('sdk-status').textContent = `${version}`;

      // 根据加载状态添加样式类，图标会通过CSS伪元素添加
      if (pluginMap.TRTC && pluginMap.TRTC.loaded) {
        document.getElementById('sdk-status').className = 'success';
      } else {
        document.getElementById('sdk-status').className = 'fail';
      }
    }

    // 创建TRTC实例，传入配置和已加载的插件
    this.trtc = TRTC.create({
      iceTransportPolicy: document.getElementById('force-turn').checked ? 'relay' : 'all',
      enableSPC: $('#enable-spc')[0].checked,
      enableSEI: $('#enable-sei')[0].checked,
      plugins: pluginsList, // 注入成功加载的插件
      assetsPath, // 设置静态资源路径（美颜、虚拟背景等资源）
    });

    this.trtc.startPlugin('SmallStreamAutoSwitcher', {
      maxAutoSwitchToSmallCount: 2
    });

    if (version.includes('dev') || version === 'latest') {
      const selectedOption = $('#version option:selected');
      if (selectedOption) {
        selectedOption.text(`${version === 'latest' ? 'npm-latest' : version} (${this.trtc._room._version})`);
      }
    }

    clearInterval(weakRefIntervalId);
    if (typeof WeakRef !== 'undefined') {
      trtcWeakRef = new WeakRef(this.trtc);
    }

    this.trtc.enableAudioVolumeEvaluation(200);
    this.trtc.on('sei-message', (event) => {
      console.log(`received sei message from ${event.userId} ${event.streamType} ${(new TextDecoder()).decode(event.data)}`);

      const toastSEI = !new URLSearchParams(location.search).get('disableSEIToast');
      if (toastSEI) {
        Toast(`received sei message from ${event.userId} ${event.streamType} ${(new TextDecoder()).decode(event.data)}`);
      }
    });
    this.trtc.on('video-decode-downgrade-state-changed', (event) => {
      console.log(`video-decode-downgrade-state-changed ${event.userId} ${event.streamType} ${event.prevState} -> ${event.state}, reason: ${event.reason}`);
      Toast(`video-decode-downgrade-state-changed ${event.userId} ${event.prevState} -> ${event.state}, reason: ${event.reason}`);
      const span = document.getElementById(`${event.userId}_${event.streamType || 'main'}_downgrade-state`);
      console.warn('video-decode-downgrade-state-changed span', span);
      if (span) {
        let text = `${event.type}(${event.renderer})`;
        if (event.state === 'STARTING') {
          text += '...';
        } else if (event.state === 'FAILED') {
          text = '';
        }
        span.textContent = text;
      }
    });
    this.trtc.on('first-video-frame', (event) => {
      console.warn('first-video-frame', event.width, event.height, event.streamType, event.userId);
    });
    this.remoteUserIdSet = new Set();
    this.remoteUserIdSet.add('*');
    this.remotePublishedMainVideoUserIdSet = new Set();
    this.remotePublishedSubVideoUserIdSet = new Set();
    this.remotePublishedAudioUserIdSet = new Set();
    this.startedRemoteVideoUserIdSet = new Set();
    this.handleEvents();
  }

  join() {
    if (!this.trtc) {
      this.initTRTC();
    }
    const roomId = this.strRoomId_ ? { strRoomId: this.strRoomId_ } : { roomId: Number(this.roomId_) };
    if ($('#webcodecsDecode')[0].checked) {
      this.trtc.startPlugin('VideoDecoder', {
        type: 'webCodecs',
        config: {
          codec: 'avc'
        }
      });
    } else if ($('#ffmpegDecode')[0].checked) {
      this.trtc.startPlugin('VideoDecoder', {
        type: 'wasm',
        outputFormat: $('#yuvMode')[0].checked ? 'yuv' : 'videoFrame',
        config: {
          codec: 'avc'
        }
      });
    }
    this.trtc.enterRoom(Object.assign({
      sdkAppId: this.sdkAppId_,
      userId: urlParams.get('userId') || this.userId_,
      userSig: urlParams.get('userSig') || this.userSig_,
      privateMapKey: urlParams.get('privateMapKey') || this.privateMapKey_,
      latencyLevel: getLatencyLevel(),
      proxy: getProxyServer(),
      // proxy: 'wss://gz-signaling.rtc.qq.com',
      // proxy: {
      //   websocketProxy: 'wss://gz-signaling.rtc.qq.com',
      //   scheduleProxy: 'schedule.cloud-rtc.com'
      // },
      role: urlParams.get('role') || getRole(),
      scene: urlParams.get('scene') || getScene(),
      autoReceiveAudio: isAutoReceiveAudio(),
      autoReceiveVideo: isAutoReceiveVideo(),
      enableAutoPlayDialog,
      preferHW: $('#HWEncoder')[0].checked,
      latencyLevel: Number(new URLSearchParams(location.search).get('latencyLevel')) || void 0,
      useVp8: $('#vp8')[0].checked,
      playoutDelay: $('#add_playout_delay')[0].checked ? { min: +$('#playoutDelayMin')[0].value, max: +$('#playoutDelayMax')[0].value } : void 0,
      jitterBufferDelay: $('#jitterBufferDelay')[0].checked ? { main: +$('#jitterBufferDelayMain')[0].value, aux: +$('#jitterBufferDelayAux')[0].value } : void 0
    }, roomId)).then(() => {
      Toast('enterRoom success');
      // eslint-disable-next-line max-len
      const { uint64_start_time, uint64_end_time, int32_schedule_cost, uint64_send_request_acc_ip_cmd_end_time, uint64_send_request_acc_ip_cmd_start_time, uint64_send_request_enter_room_cmd_end_time, uint64_send_request_enter_room_cmd_start_time } = this.trtc._room.keyPointManager._pathJoinRoom;
      // eslint-disable-next-line camelcase
      console.warn(`进房耗时: ${uint64_end_time - uint64_start_time}ms(${int32_schedule_cost} ${uint64_send_request_acc_ip_cmd_end_time - uint64_send_request_acc_ip_cmd_start_time} ${uint64_send_request_enter_room_cmd_end_time - uint64_send_request_enter_room_cmd_start_time})`);
      if (this.userId_ === 'test1') {
        const encoder = new TextEncoder();
        rtc.trtc.sendCustomMessage({
          cmdId: +document.getElementById('cmdId').value,
          data: encoder.encode('test msg 1').buffer
        });
        rtc.trtc.sendCustomMessage({
          cmdId: +document.getElementById('cmdId').value,
          data: encoder.encode('test msg 2').buffer
        });
      }
    })
      .catch((error) => {
        console.warn(error);
        ToastError(error);
      });
  }

  leave() {
    // this.trtc.stopPlugin('TRTCVideoDecoder');
    this.trtc.exitRoom().then(() => {
      Toast('exitRoom success');
    });;
  }

  destroy() {
    this.trtc.destroy();
    Toast('destroy success');
    this.trtc = null;
    // 测试垃圾回收
    if (trtcWeakRef) {
      weakRefIntervalId = setInterval(() => {
        if (trtcWeakRef.deref()) {
          console.error('trtc is not gc. Please go to Chrome devtool -> Memory Tab -> click "Collect rubbish" button.');
        } else {
          console.log('trtc is successfully garbage collected!');
          clearInterval(weakRefIntervalId);
        }
      }, 1000);
    }
  }

  // 自定义采集
  // async startLocalVideo() {
  //   const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
  //   this.trtc.startLocalVideo({
  // view: 'local_stream', option: { videoSource: stream.getTracks()[0], mirror: false, fillMode: 'contain' } });
  // }

  startLocalVideo(cameraName, capture = true) {
    // const div = addLocalStreamDiv();
    const div = addView('local_stream', TRTC.TYPE.STREAM_TYPE_MAIN, true);
    const width = +new URLSearchParams(location.search).get('width');
    const height = +new URLSearchParams(location.search).get('height');
    const frameRate = +new URLSearchParams(location.search).get('frameRate');
    const bitrate = +new URLSearchParams(location.search).get('bitrate');
    const mainContainer = document.querySelector('#main');
    const mainView = mainContainer.childNodes.length === 0 ? mainContainer : 'local_stream';
    const views = extraLocalViews.concat(mainView);
    const convertedViews = views.map((item) => {
      if (typeof item === 'string') {
        return document.getElementById(item);
      }
      return item;
    });
    console.warn(convertedViews);
    this.trtc.startLocalVideo({
      view: div,
      capture,
      option: {
        cameraId: getCameraId(cameraName),
        profile: width ? { width, height, frameRate: frameRate || 15, bitrate: bitrate || 500 } : getVideoProfile(),
        mirror: false,
        small: $('#small')[0].checked ? true : undefined,
        smallMode: new URLSearchParams(location.search).get('smallMode') || void 0,
        fillMode: 'contain',
        avoidCropping: $('#avoidCropping')[0].checked,
        rotation: getRotationAngle(),
      },
    }).catch((error) => {
      if (error.extraCode === 5302) {
        ToastError(error);
        if (error.handler) {
          setTimeout(error.handler, 1000);
        }
      }
      throw error;
    });;
  }

  switchCamera() {
    this.trtc.updateLocalVideo({
      option: { cameraId: getCameraId() },
    });
  }

  switchMicrophone() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId() },
    });
  }

  updateVideoProfile() {
    this.trtc.updateLocalVideo({
      option: { profile: getVideoProfile() },
    });
  }

  updateAudioProfile() {
    this.trtc.updateLocalAudio({
      option: { microphoneId: getMicrophoneId(), profile: getAudioProfile() },
    });
  }
  publishSmall(small) {
    this.trtc.updateLocalVideo({ option: { small } }).catch(() => { });
  }
  subscribeSmall(userId) {
    if (userId === '*') {
      this.remotePublishedMainVideoUserIdSet.forEach((remoteUserId) => {
        this.trtc.updateRemoteVideo({
          userId: remoteUserId,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: true }
        });
      });
    } else {
      this.trtc.updateRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: true } });
    }
  }

  subscribeBig(userId) {
    if (userId === '*') {
      this.remotePublishedMainVideoUserIdSet.forEach((remoteUserId) => {
        this.trtc.updateRemoteVideo({
          userId: remoteUserId,
          streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: false }
        });
      });
    } else {
      this.trtc.updateRemoteVideo({ userId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN, option: { small: false } });
    }
  }

  stopLocalVideo() {
    removeLocalStreamDiv();
    this.trtc.stopLocalVideo();
  }
  stopPreviewLocalVideo() {
    this.trtc.updateLocalVideo({ view: null, publish: true });
  }
  switchRole(role) {
    this.trtc.switchRole(role, { latencyLevel: getLatencyLevel() });
  }
  updateRemoteUserOptionTag() {
    document.querySelector('#remote-userId').innerHTML = null;
    this.remoteUserIdSet.forEach((remoteUserId) => {
      $('<option/>', {
        value: remoteUserId,
        text: remoteUserId,
      })
        .appendTo('#remote-userId');
    });
  }
  handleEvents() {
    if (!enableAutoPlayDialog) {
      this.trtc.on(TRTC.EVENT.AUTOPLAY_FAILED, (event) => {
        console.warn('autoplay failed', event);
        const userContainer = document.querySelector(`#${event.userId}_main`);
        console.warn('userContainer', `#${event.userId}`, userContainer);
        if (userContainer) {
          const volumeLimitElement = document.createElement('span');
          volumeLimitElement.innerHTML = '音频播放受限，点击此处恢复';
          volumeLimitElement.style.cssText = 'color: red; cursor: pointer; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.8); padding: 10px 20px; border-radius: 8px;';
          volumeLimitElement.onclick = async () => {
            await event.resume();
            volumeLimitElement.remove();
          };
          userContainer.appendChild(volumeLimitElement);
        }
      });
    }
    this.trtc.on(TRTC.EVENT.TRACK, (event) => {
      console.warn('ontrack ', event);
    });
    this.trtc.on(TRTC.EVENT.CUSTOM_MESSAGE, (event) => {
      console.warn('on custom message ', event);
      Toast(`receive custom msg from ${event.userId} cmdId: ${event.cmdId} seq: ${event.seq} data: ${new TextDecoder().decode(event.data)}`);
    });
    this.trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, ({ userId }) => {
      this.remoteUserIdSet.add(userId);
      this.updateRemoteUserOptionTag();
    });
    this.trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, ({ userId }) => {
      this.remoteUserIdSet.delete(userId);
      this.updateRemoteUserOptionTag();
    });
    this.trtc.on(TRTC.EVENT.PUBLISH_STATE_CHANGED, (event) => {
      let text = `${event.mediaType} publish ${event.state}`;
      if (event.state === 'stopped') {
        text += ` reason: ${event.reason}`;
        if (event.reason === 'error') {
          text += ` error: ${event.error.code} ${event.error.extraCode} ${event.error.message}`;
          return ToastError(text);
        }
      }
      if (event.state === 'started') {
        setInterval(() => {
          if (!this.trtc || !this.trtc._room || !this.trtc._room._stats) return;
          const encoder = this.trtc._room._stats._prevEncoderImplementation;
          const HWSet = new Set(['ExternalEncoder', 'VideoToolbox']);
          const SWSet = new Set(['OpenH264', 'libvpx']);
          if (encoder) {
            document.querySelector('#encoderImplementation').innerHTML = `编码器：${encoder}`;
            if (HWSet.has(encoder)) {
              document.querySelector('#encoderImplementation').innerHTML += '(硬编)';
            } else if (SWSet.has(encoder)) {
              document.querySelector('#encoderImplementation').innerHTML += '(软编)';
            }
          }
        }, 2000);
      }
      Toast(text);
    });
    this.trtc.on(TRTC.EVENT.KICKED_OUT, console.error);
    this.trtc.on('screen-share-stopped', () => console.warn('screen share stopped'));
    this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, ({ userId }) => {
      console.warn(`remote-audio-available ${userId}`);
      this.remotePublishedAudioUserIdSet.add(userId);
      addView(userId, 'main');
      if (!isAutoReceiveAudio()) {
        // this.trtc.muteRemoteAudio(userId, false);
      }
    });
    this.trtc.on(TRTC.EVENT.DEVICE_CHANGED, (event) => {
      console.warn(event);
      if (event.action === 'active') {
        if (event.type === 'camera') {
          selectedCameraId = event.device.deviceId;
        } else if (event.type === 'microphone') {
          selectedMicrophoneId = event.device.deviceId;
        } else if (event.type === 'speaker') {
          selectedSpeakerId = event.device.deviceId;
        }
      }
      updateDeviceList();
      Toast(`${event.type} ${event.action} ${event.device.label}`);

      if (event.action === 'add') {
        if (event.type === 'camera') {
          if (this.trtc.getVideoTrack()) {
            this.trtc.updateLocalVideo({ option: { cameraId: event.device.deviceId } });
          }
        } else if (event.type === 'microphone') {
          if (this.trtc.getAudioTrack()) {
            this.trtc.updateLocalAudio({ option: { microphoneId: event.device.deviceId } });
          }
        }
      }
    });
    this.trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
      console.warn(`remote-audio-unavailable ${event.userId}`);
      this.remotePublishedAudioUserIdSet.delete(event.userId);
      // this.trtc.muteRemoteAudio(event.userId, true);
    });
    this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, ({ userId, streamType }) => {
      console.warn(`remote-video-available ${userId} ${streamType}`);
      try {
        const div = addView(userId, streamType);
        // observer.observe(div);

        if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
          this.remotePublishedMainVideoUserIdSet.add(userId);
        } else {
          this.remotePublishedSubVideoUserIdSet.add(userId);
        }
        if (isAutoReceiveVideo()) {
          this.startRemoteVideo(div, userId, streamType);
        }
      } catch (error) {
        // debugger;
        console.error(error);
      }
    });
    this.trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
      console.warn(`remote-video-unavailable ${JSON.stringify(event)}`);
      if (event.streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
        this.remotePublishedMainVideoUserIdSet.delete(event.userId);
      } else {
        this.remotePublishedSubVideoUserIdSet.delete(event.userId);
      }
      removeView(event.userId, event.streamType);
    });
    this.trtc.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
      event.result.forEach(({ userId, volume }) => {
        const container = userId === '' ? document.getElementById('local_stream_main') : document.getElementById(`${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
        if (container) {
          let volumeSpan = container.querySelector('#volume-span');
          if (!volumeSpan) {
            volumeSpan = document.createElement('span');
            volumeSpan.style.position = 'absolute';
            volumeSpan.style.top = '24px';
            volumeSpan.style.left = '0';
            volumeSpan.style.background = 'black';
            volumeSpan.style.color = '#fff';
            volumeSpan.style.zIndex = 999;
            container.appendChild(volumeSpan);
          }
          volumeSpan.id = 'volume-span';
          volumeSpan.innerText = `音量：${volume}`;
        }
      });
    });
    // this.trtc.on('network-quality', console.debug);
    this.trtc.on('audio-play-state-changed', console.warn);
    this.trtc.on('video-play-state-changed', (event) => {
      console.warn(event);
      if (event.userId !== '') {
        const id = `${event.userId}_${event.streamType}`;
        const view = document.getElementById(id);
        const loading = view.querySelector('.loading');
        loading.style.display = event.state === 'PLAYING' ? 'none' : 'block';
      }
    });
    this.trtc.on(TRTC.EVENT.ERROR, (error) => {
      console.error('rtc error:', error);
      ToastError(error);
    });
    this.trtc.on('connection-state-changed', console.warn);
    this.trtc.on('autoplay-failed', () => {
      // const button = document.createElement('button');
      // button.innerText = '自动播放失败，点击恢复播放';
      // button.onclick = () => document.body.removeChild(button);
      // document.body.appendChild(button);
    });
    let prevBytesSent = 0;
    let prevBytesReceived = 0;
    this.trtc.on(TRTC.EVENT.STATISTICS, (stat) => {
      const byteRateSent = (stat.bytesSent - prevBytesSent) / 2;
      const byteRateReceived = (stat.bytesReceived - prevBytesReceived) / 2;
      prevBytesSent = stat.bytesSent;
      prevBytesReceived = stat.bytesReceived;
      const statElement = document.querySelector('#debug-stat');
      let html = `rtt: ${stat.rtt} ms upLoss: ${stat.upLoss} downLoss: ${stat.downLoss} <br>
        ↑ ${byteRateSent / 1000} KB/s ${8 * byteRateSent / 1000} Kb/s <br>
        ↓ ${byteRateReceived / 1000} KB/s ${8 * byteRateReceived / 1000} Kb/s <br><br>
      `;
      if (stat.localStatistics.audio.bitrate) {
        html += `<li>local audio: ${stat.localStatistics.audio.bitrate} Kb/s audioLevel: ${stat.localStatistics.audio.audioLevel}</li>`;
      }
      if (stat.localStatistics.video.length > 0) {
        html += `${stat.localStatistics.video.map(item => `<li>local ${item.videoType} video: ${item.bitrate} Kb/s ${item.width}*${item.height} ${item.frameRate}fps</li>`).join('')}<br>`;
      }
      if (stat.remoteStatistics.length > 0) {
        stat.remoteStatistics.forEach((remoteStat) => {
          let tempHtml = `remote ${remoteStat.userId}:`;
          if (remoteStat.audio.bitrate > 0) {
            tempHtml += `<br><li>audio: ${remoteStat.audio.bitrate} Kb/s audioLevel: ${remoteStat.audio.audioLevel} point2pointDelay:${remoteStat.audio.point2pointDelay}ms jitterBufferDelay:${remoteStat.audio.jitterBufferDelay}ms </li>`;
          }
          if (remoteStat.video.length > 0) {
            remoteStat.video.forEach((videoItem) => {
              tempHtml += `<li> ${videoItem.codec} ${videoItem.videoType} video: ${videoItem.bitrate} Kb/s ${videoItem.width}*${videoItem.height} ${videoItem.frameRate}fps point2pointDelay:${videoItem.point2pointDelay}ms jitterBufferDelay:${videoItem.jitterBufferDelay}ms </li>`;
            });
          }
          html += tempHtml;
        });
      }
      statElement.innerHTML = html;
    });
  }
  startLocalAudio() {
    // addLocalStreamDiv();
    this.trtc.startLocalAudio({
      publish: true,
      option: {
        microphoneId: getMicrophoneId(),
        captureVolume: Number($('#volume-local').val()),
        earMonitorVolume: Number($('#volume-ear').val()),
        echoCancellation: document.querySelector('#aec').checked,
        noiseSuppression: document.querySelector('#ans').checked,
        autoGainControl: document.querySelector('#agc').checked,
        profile: getAudioProfile(),
      },
    }).then(async () => {
      if (document.querySelector('#ai-denoiser').checked) {
        await this.trtc.startPlugin('AIDenoiser', {
          sdkAppId: this.sdkAppId_,
          userId: new URLSearchParams(location.search).get('userId') || this.userId_,
          userSig: new URLSearchParams(location.search).get('userSig') || this.userSig_,
        });
      }
      if (document.querySelector('#audio-processor').checked) {
        await this.trtc.startPlugin('AudioProcessor', {
          sdkAppId: this.sdkAppId_,
          userId: new URLSearchParams(location.search).get('userId') || this.userId_,
          userSig: new URLSearchParams(location.search).get('userSig') || this.userSig_,
        });
      }
      if (document.querySelector('#bgm').checked) {
        this.trtc.startPlugin('AudioMixer', {
          id: 'bgm',
          url: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/assets/music/bgm.mp3',
          loop: true,
        });
      }
    })
      .catch((error) => {
        ToastError(error);
        if (error.handler) {
          setTimeout(error.handler, 1000);
        }
      });
  }

  stopLocalAudio() {
    this.trtc.stopLocalAudio();
  }

  startRemoteVideo(view, userId, streamType) {
    if ($('#sub-num').val() !== '' && this.startedRemoteVideoUserIdSet.size >= +$('#sub-num').val()) {
      return;
    }
    this.startedRemoteVideoUserIdSet.add(`${userId}_${streamType}`);
    return this.trtc.startRemoteVideo({
      view,
      userId,
      streamType,
      option: {
        fillMode: 'contain',
        small: $('#auto_sub_small')[0].checked,
        receiveWhenViewVisible: true,
        poster: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/rtc/trtc-loading.png'
      }
    })
      .catch((error) => {
        ToastError(error);
        this.startedRemoteVideoUserIdSet.delete(`${userId}_${streamType}`);
      });
  }

  stopRemoteVideo({ userId, streamType }) {
    return this.trtc.stopRemoteVideo({ userId, streamType }).then(() => {
      this.startedRemoteVideoUserIdSet.delete(`${userId}_${streamType}`);
    });
  }

  updateRemoveVideoOnMain(userId, streamType) {
    this.trtc.updateRemoteVideo({ view: document.querySelector('#main'), userId, streamType, option: { small: false, fillMode: 'contain', receiveWhenViewVisible: true } });
  }
  updateRemoveVideoOnSide(sideNode, userId, streamType) {
    this.trtc.updateRemoteVideo({ view: sideNode, userId, streamType, option: { small: true, fillMode: 'cover', receiveWhenViewVisible: true } });
  }

  async startPublishMediaFile(url) {
    const video = document.createElement('video');
    video.src = url;
    video.muted = true;
    video.autoplay = true;
    video.loop = true;
    video.crossOrigin = 'anonymous';
    await video.play();
    const mediaStream = video.captureStream();
    const videoTrack = mediaStream.getVideoTracks()[0];
    const audioTrack = mediaStream.getAudioTracks()[0];
    const div = addView('local_stream', TRTC.TYPE.STREAM_TYPE_MAIN, true);
    this.trtc.startLocalVideo({
      view: div,
      option: {
        videoTrack,
        mirror: false,
        fillMode: 'contain',
      }
    });
    this.trtc.startLocalAudio({
      option: {
        audioTrack,
      },
    });
  }
}

function getUserIdAndStreamTypeFromId(id) {
  return {
    userId: id.split('_').slice(0, -1)
      .join('_')
      .substr(1),
    streamType: id.split('_').pop()
  };
}

// // 只拉可视区的视频，不可视区不拉取。
// const observer = new IntersectionObserver((entries, observer) => {
//   entries.forEach((entry) => {
//     const { userId, streamType } = getUserIdAndStreamTypeFromId(entry.target.id);
//     if (entry.isIntersecting) {
//       if (isAutoReceiveVideo()) {
//         rtc.startRemoteVideo(entry.target, userId, streamType);
//       }
//     } else {
//       // 远端流正在在主窗口显示，不停止拉流
//       if (!entry.target.querySelector('video')) return;
//       rtc.stopRemoteVideo({ userId, streamType });
//     }
//   });
// }, { root: document.querySelector('#side'), });

const isMainScreen = {};

async function playOnMain(userId, streamType) {
  const main = document.querySelector('#main');
  if (main.querySelector('video') || main.querySelector('canvas')) {
    Toast('main already has video');
    return;
  }
  if (userId === 'local_stream') {
    rtc.trtc.updateLocalVideo({ view: main });
  } else if (userId === 'local_screen') {
    rtc.trtc.updateScreenShare({ view: main });
  } else {
    rtc.updateRemoveVideoOnMain(userId, streamType);
  }
}

function addView(userId, streamType, isLocal = false) {
  const id = `${userId}_${streamType}`;
  if (!document.getElementById(id)) {
    const div = document.createElement('div');
    div.id = id;
    div.className = 'flex-center';
    div.style.position = 'relative';
    div.style.minHeight = '100px';
    div.appendChild(createToolHeader(userId, streamType, isLocal));
    if (isLocal) {
      document.querySelector('#side').insertBefore(div, document.querySelector('#side').firstChild);
    } else {
      document.querySelector('#side').appendChild(div);
      const loading = document.createElement('div');
      loading.className = 'loading';

      div.appendChild(loading);
    }
    return div;
  }
  return document.getElementById(id);
}
function createAudioDowngradeTest(userId) {
  const userIdSpan = document.createElement('button');
  userIdSpan.innerText = '音频降级';
  userIdSpan.style.userSelect = 'none';
  userIdSpan.style.cursor = 'pointer';
  userIdSpan.onclick = (event) => {
    event.stopPropagation();
    rtc.trtc._room.remotePublishedUserMap.get(userId).remoteAudioTrack.emit('decode-failed');
  };
  return userIdSpan;
}

function createVideoDowngradeMock(userId, streamType) {
  const userIdSpan = document.createElement('button');
  userIdSpan.innerText = '视频降级';
  userIdSpan.style.userSelect = 'none';
  userIdSpan.style.cursor = 'pointer';
  userIdSpan.onclick = (event) => {
    event.stopPropagation();
    const track = streamType === 'main' ? rtc.trtc._room.remotePublishedUserMap.get(userId).remoteVideoTrack : rtc.trtc._room.remotePublishedUserMap.get(userId).remoteAuxiliaryTrack;
    console.warn('updatePlugin downgrade streamType', streamType);
    rtc.trtc.updatePlugin('TRTCVideoDecoder', { type: 'mock', track }).catch(() => {
      track.emit('decode-failed');
    });
  };
  return userIdSpan;
}

const isMobile = () => {
  const userAgent = (window.navigator && window.navigator.userAgent) || '';
  const isIOS = /iPad/i.test(userAgent) || /iPhone/i.test(userAgent);
  const isAndroid = /Android/i.test(userAgent);
  console.warn('isIOS', isIOS, 'isAndroid', isAndroid);
  return isIOS || isAndroid;
};

function createToolHeader(userId, streamType, isLocal = false) {
  console.warn('createToolHeader', userId, streamType, isLocal);
  const div = document.createElement('div');
  div.style.position = 'absolute';
  div.style.top = 0;
  div.style.left = 0;
  div.style.zIndex = 20;
  div.style.display = 'flex';
  div.style.flexDirection = 'row';
  div.style.alignItems = 'center';
  const name = `${userId}_${streamType}`;
  const mainScreenButton = document.createElement('button');
  if (isMainScreen[name] === undefined) {
    isMainScreen[name] = false;
  }
  mainScreenButton.innerText = isMainScreen[name] ? '退出大屏' : '全屏';
  mainScreenButton.onclick = () => {
    const theMainScreenUserId = Object.keys(isMainScreen).find(key => isMainScreen[key]);
    if (theMainScreenUserId && theMainScreenUserId !== name) {
      Toast('已经有其他人全屏了');
      return;
    }
    if (isMainScreen[name]) {
      if (userId === 'local_stream') {
        rtc.trtc.updateLocalVideo({ view: document.querySelector('#local_stream_main') });
      } else if (userId === 'local_screen') {
        rtc.trtc.updateScreenShare({ view: document.querySelector('#local_screen_sub') });
      } else {
        rtc.updateRemoveVideoOnSide(document.getElementById(name), userId, streamType);
      }
    } else {
      playOnMain(userId, streamType);
    }
    console.warn('!isMainScreen[name]', !isMainScreen[name]);
    isMainScreen[name] = !isMainScreen[name];
    console.warn('set isMainScreen[name]', name, isMainScreen[name]);
    mainScreenButton.innerText = isMainScreen[name] ? '退出大屏' : '全屏';
  };
  div.appendChild(createUserIdSpan(isLocal ? '我' : name));
  if (!isMobile()) div.appendChild(mainScreenButton);
  div.appendChild(createVideoDowngradeMock(userId, streamType));
  div.appendChild(createAudioDowngradeTest(userId));
  div.appendChild(createSmallVideoToggleButton(userId, streamType, isLocal));
  const span = document.createElement('span');
  span.id = `${userId}_${streamType}_downgrade-state`;
  span.textContent = 'hardware';
  span.style.background = 'black';
  div.appendChild(span);
  return div;
}

const smallVideoMap = {};
function createSmallVideoToggleButton(userId, streamType, isLocal) {
  smallVideoMap[userId] = smallVideoMap[userId] || false;
  const button = document.createElement('button');
  button.innerText = '小流切换';
  button.onclick = async () => {
    if (isLocal) {
      await rtc.trtc.updateLocalVideo({ option: { small: !smallVideoMap[userId] } });
    } else {
      rtc.trtc.updateRemoteVideo({ userId, streamType, option: { small: !smallVideoMap[userId] } });
    }
    smallVideoMap[userId] = !smallVideoMap[userId];
  };
  return button;
}

function createUserIdSpan(userId) {
  const userIdSpan = document.createElement('span');
  userIdSpan.innerText = userId;
  userIdSpan.style.background = 'black';
  userIdSpan.style.color = '#fff';
  userIdSpan.onclick = event => event.stopPropagation();
  return userIdSpan;
}

function addLocalStreamDiv() {
  if (document.querySelector('#local_stream')) return;
  const div = document.createElement('div');
  div.id = 'local_stream';
  div.style.position = 'relative';
  div.appendChild(createUserIdSpan('Me'));
  const main = document.querySelector('#main');
  const side = document.querySelector('#side');
  if (side.children.length > 0) {
    side.insertBefore(div, side.children[0]);
  } else {
    side.appendChild(div);
  }
  return div;
}

function removeLocalStreamDiv() {
  document.querySelector('#local_stream_main').remove();
}

function removeView(userId, streamType) {
  const id = `${userId}_${streamType}`;
  document.getElementById(id) && document.getElementById(id).remove();
}

let rtc = null;
const presetting = new Presetting();

// initialize sdkAppId/userId/userSig stuffs
(function login() {
  presetting.init();
  presetting.login(false, (options) => {
    rtc = new RtcClient(options);
    const params = new URLSearchParams(location.search);
    if (params.get('autoJoin') === '1') {
      rtc.join();
    };
    if (params.get('autoCamera')) {
      rtc.startLocalVideo(params.get('autoCamera'));
    }
    // 确保 DOM 已经加载完成后再执行关闭设置页面的操作
    setTimeout(() => {
      if (params.get('closeSetting') === '1') {
        const closeSettingBtn = document.querySelector('#close-setting');
        if (closeSettingBtn) {
          closeSettingBtn.click();
        }
      }
    }, 100);
  });
}());


$('#start-switch-room').on('click', async () => {
  if (!rtc.trtc.room.isJoined) {
    ToastError('请先加入房间');
    return;
  }

  const startTime = performance.now();
  const handleVideoPlayStateChanged = (event) => {
    if (event.state === 'PLAYING') {
      const endTime = performance.now();
      console.log('[switch-room] click button -> PLAYING time: ', endTime - startTime);
      rtc.trtc.off('video-play-state-changed', handleVideoPlayStateChanged);
    }
  };
  rtc.trtc.on('video-play-state-changed', handleVideoPlayStateChanged);

  const targetRoomId = Number($('#switch-room-roomId').val());
  const targetStrRoomId = String($('#switch-room-strRoomId').val());
  await rtc.trtc.switchRoom({
    roomId: targetRoomId,
    strRoomId: targetStrRoomId,
    userSig: $('#switch-room-userSig').val() || rtc.userSig_,
    privateMapKey: $('#switch-room-privateMapKey').val() || rtc.privateMapKey_
  });
  if (targetRoomId) $('#roomId').val(targetRoomId);
  if (targetStrRoomId) $('#strRoomId').val(targetStrRoomId);
});

$('#start-switch-room-fallback').on('click', async () => {
  if (!rtc.trtc.room.isJoined) {
    ToastError('请先加入房间');
    return;
  }

  const startTime = performance.now();
  const handleVideoPlayStateChanged = (event) => {
    if (event.state === 'PLAYING') {
      const endTime = performance.now();
      console.log('[switch-room (exit-and-enter)] click button -> PLAYING time: ', endTime - startTime);
      rtc.trtc.off('video-play-state-changed', handleVideoPlayStateChanged);
    }
  };
  rtc.trtc.on('video-play-state-changed', handleVideoPlayStateChanged);

  const targetRoomId = Number($('#switch-room-roomId').val());
  const targetStrRoomId = String($('#switch-room-strRoomId').val());
  await rtc.trtc.exitRoom();
  await rtc.trtc.enterRoom({
    sdkAppId: rtc.sdkAppId_,
    userId: urlParams.get('userId') || rtc.userId_,
    userSig: urlParams.get('userSig') || rtc.userSig_,
    roomId: targetRoomId || undefined,
    strRoomId: targetStrRoomId || undefined,
    privateMapKey: this.privateMapKey_,
    latencyLevel: getLatencyLevel(),
    proxy: getProxyServer(),
    role: urlParams.get('role') || getRole(),
    scene: urlParams.get('scene') || getScene(),
    autoReceiveAudio: isAutoReceiveAudio(),
    autoReceiveVideo: isAutoReceiveVideo(),
    enableAutoPlayDialog,
    enableSEI: $('#enable-sei')[0].checked,
    preferHW: $('#HWEncoder')[0].checked,
    latencyLevel: Number(new URLSearchParams(location.search).get('latencyLevel')) || void 0,
    useVp8: $('#vp8')[0].checked
  });
  if (targetRoomId) $('#roomId').val(targetRoomId);
  if (targetStrRoomId) $('#strRoomId').val(targetStrRoomId);
});

$('#publish-mp4').on('click', async () => {
  rtc.startPublishMediaFile($('#mp4-url').val());
});
$('#change-speaker').on('click', async () => {
  TRTC.setCurrentSpeaker(TRTC.TYPE.SPEAKER);
});

$('#change-headset').on('click', async () => {
  TRTC.setCurrentSpeaker(TRTC.TYPE.HEADSET);
});

$('#switch-bad-cam').on('click', () => {
  rtc.trtc.updateLocalVideo({ option: { cameraId: 'bad' } });
});

$('#userId')
  .on('input', (e) => {
    e.preventDefault();
    console.log('userId changed');
    presetting.login(false, (options) => {
      delete options.roomId;
      rtc.updateParams(options);
    });
  });
$('#roomId')
  .on('input', (e) => {
    e.preventDefault();
    console.log(`roomId changed ${e.target.value}`);
    const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#roomId')
      .val(validateVal);
    rtc.updateParams({ roomId: validateVal });
  });
$('#strRoomId')
  .on('input', (e) => {
    e.preventDefault();
    console.log(`strRoomId changed ${e.target.value}`);
    // const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#strRoomId')
      .val(e.target.value);
    rtc.updateParams({ strRoomId: e.target.value });
  });
$('#sub-num')
  .on('input', (e) => {
    e.preventDefault();
    const validateVal = e.target.value.replace(/[^\d]/g, '');
    $('#sub-num')
      .val(validateVal);
  });
$('#small')
  .on('change', () => {
    const v = $('#small')[0].checked;
    console.log('pub small', v);
    rtc.publishSmall(v);
  });
$('#auto_sub_small')
  .on('change', () => {
    const v = $('#auto_sub_small')[0].checked;
    console.log('sub small', v);
    if (v) rtc.subscribeSmall(getRemoteUserId());
    else rtc.subscribeBig(getRemoteUserId());
  });
$('#audio-processor')
  .on('change', () => {
    const { checked } = $('#audio-processor')[0];
    if (checked) {
      rtc.trtc.startPlugin('AudioProcessor', {
        sdkAppId: rtc.sdkAppId_,
        userId: new URLSearchParams(location.search).get('userId') || rtc.userId_,
        userSig: new URLSearchParams(location.search).get('userSig') || rtc.userSig_,
      });
    } else {
      rtc.trtc.stopPlugin('AudioProcessor');
    }
  });
$('#ai-denoiser')
  .on('change', () => {
    const { checked } = $('#ai-denoiser')[0];
    if (checked && rtc.trtc.getAudioTrack()) {
      rtc.trtc.startPlugin('AIDenoiser', {
        sdkAppId: rtc.sdkAppId_,
        userId: new URLSearchParams(location.search).get('userId') || rtc.userId_,
        userSig: new URLSearchParams(location.search).get('userSig') || rtc.userSig_,
      });
    } else {
      rtc.trtc.stopPlugin('AIDenoiser');
    }
  });
document.querySelector('#bgm').onchange = () => {
  if (document.querySelector('#bgm').checked && rtc.trtc.getAudioTrack()) {
    rtc.trtc.startPlugin('AudioMixer', {
      id: 'bgm',
      url: 'https://web.sdk.qcloud.com/trtc/webrtc/v5/test/latest/assets/music/bgm.mp3',
      loop: true
    });
  } else {
    rtc.trtc.stopPlugin('AudioMixer', { id: 'bgm' });
  }
};
$('#bgm')
  .on('change', () => {

  });

$('#add-local-view')
  .on('click', (e) => {
    e.preventDefault();
    console.log('add-local-view');
    const d = addView(rtc.userId_, extraLocalViews.length + 1);
    extraLocalViews.push(d.id);
  });

$('#join')
  .on('click', (e) => {
    e.preventDefault();
    console.log('join');
    rtc.join();
  });

$('#start-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalVideo();
  });

$('#enable-spc').on('change', () => {
  if (rtc.trtc) {
    rtc.trtc._room.enableSPC = $('#enable-spc')[0].checked;
  }
});
$('#enable-sei').on('change', () => {
  const url = new URL(window.location.href);
  url.searchParams.set('enableSEI', $('#enable-sei')[0].checked);
  if (confirm('需要刷新页面才能生效，是否继续？')) {
    window.location.href = url.toString();
  } else {
    $('#enable-sei')[0].checked = !$('#enable-sei')[0].checked;
  }
});
$('#update-water-mark').on('click', () => {
  const imageUrls = [
    './mute.png',
    './tv2.png',
  ];
  const imageUrl = imageUrls[Math.floor(Math.random() * imageUrls.length)];
  const width = 100 + Math.floor((Math.random() - 0.5) * 100);
  rtc.trtc.updatePlugin('Watermark', {
    x: 100 + Math.floor(Math.random() * 100),
    y: 100 + Math.floor(Math.random() * 100),
    size: {
      width,
      height: width,
    },
    imageUrl
  });
});

$('#water-mark').on('change', () => {
  if ($('#water-mark')[0].checked) {
    // if (intervalId > 0 || !rtc.trtc.getVideoTrack()) {
    //   return;
    // }
    // startWaterMark({ x: 100, y: 10, imageUrl: './test.png' });
    rtc.trtc.startPlugin('Watermark', {
      x: 0,
      y: 0,
      imageUrl: './tv2.png'
    });
  } else {
    rtc.trtc.stopPlugin('Watermark');
  }
});

$('#install-vb').on('click', async () => {
  try {
    rtc.trtc.use(VirtualBackground);
    // rtc.trtc.use({ plugin: VirtualBackground });
    // rtc.trtc.use({ plugin: VirtualBackground, assetsPath: './js/assets/' });
    rtc.trtc.use(Watermark);
  } catch (e) {
    alert(e);
  }
});

let isStartedVirtualBackground = false;

$('input:radio[name="set-rotation-angle"]').on('change', async function () {
  const angle = +$(this).val();
  console.log(angle);
  await rtc.trtc.updateLocalVideo({
    option: {
      rotation: angle,
    },
  });
});

$('input:radio[name="background"]').on('change', async function () {
  const v = $(this).val();
  async function setVirtualBackground(config) {
    try {
      if (!isStartedVirtualBackground) {
        await rtc.trtc.startPlugin('VirtualBackground', Object.assign(config, {
          sdkAppId: rtc.sdkAppId_,
          userId: rtc.userId_,
          userSig: rtc.userSig_,
        }));
        isStartedVirtualBackground = true;
      } else {
        await rtc.trtc.updatePlugin('VirtualBackground', config);
      }
    } catch (e) {
      document.getElementById('normal-background').checked = true;
      alert(e);
    }
  }
  const enableFaceCentering = document.getElementById('face-centering').checked;
  switch (v) {
    case 'blur':
      await setVirtualBackground({
        type: 'blur',
        blurLevel: parseInt(document.getElementById('vb-level-select').value, 10),
        enableFaceCentering
      });
      break;
    case 'normal':
      await rtc.trtc.stopPlugin('VirtualBackground');
      isStartedVirtualBackground = false;
      break;
    default:
      await setVirtualBackground({
        type: v,
        src: './vb.jpg',
        enableFaceCentering
      });
  }
});

$('#vb-level-select').on('change', function () {
  const v = parseInt($(this).val(), 10);
  console.log(v);
  if (document.getElementById('blur-background').checked) {
    rtc.trtc.updatePlugin('VirtualBackground', {
      type: 'blur',
      blurLevel: parseInt(document.getElementById('vb-level-select').value, 10),
    });
  }
});

$('#face-centering').on('change', function () {
  const selectedBackground = $('input[name="background"]:checked').val();
  const enableFaceCentering = $(this).prop('checked');
  if (selectedBackground === 'image') {
    rtc.trtc.updatePlugin('VirtualBackground', {
      enableFaceCentering,
      type: 'image',
      src: './vb.jpg'
    });
  } else if (selectedBackground === 'blur') {
    rtc.trtc.updatePlugin('VirtualBackground', {
      enableFaceCentering,
      type: 'blur',
      blurLevel: parseInt(document.getElementById('vb-level-select').value, 10),
    });
  }
});

$('#webcodecsDecode').on('change', function () {
  if (rtc.trtc && rtc.trtc.room.isJoined && $(this)[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'webCodecs',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('#ffmpegDecode').on('change', function () {
  if (rtc.trtc && rtc.trtc.room.isJoined && $(this)[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'wasm',
      renderer: $('#yuvMode')[0].checked ? 'webgl' : 'videoFrame',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('#yuvMode').on('change', () => {
  if (rtc.trtc && rtc.trtc.room.isJoined && $('#ffmpegDecode')[0].checked) {
    rtc.trtc.updatePlugin('VideoDecoder', {
      type: 'wasm',
      renderer: $('#yuvMode')[0].checked ? 'webgl' : 'videoFrame',
      config: {
        codec: 'avc'
      }
    });
  }
});

$('input:radio[name="mirror"]').on('change', function () {
  const v = $(this).val();
  let mirror = v;
  if (mirror === 'true') mirror = true;
  if (mirror === 'false') mirror = false;
  rtc.trtc.updateLocalVideo({ option: { mirror } });
});

function getBeautyParams() {
  const beauty = document.getElementById('beauty-beauty').value;
  const brightness = document.getElementById('beauty-brightness').value;
  const ruddy = document.getElementById('beauty-ruddy').value;
  return {
    beauty: Number(beauty),
    brightness: Number(brightness),
    ruddy: Number(ruddy),
  };
}

$('#start-beauty').on('click', () => {
  rtc.trtc.startPlugin('BasicBeauty', getBeautyParams());
});

$('#update-beauty').on('click', () => {
  rtc.trtc.updatePlugin('BasicBeauty', getBeautyParams());
});

$('#stop-beauty').on('click', () => {
  rtc.trtc.stopPlugin('BasicBeauty');
});

$('#mirror').on('change', () => {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.mirror = ($('#mirror')[0].checked);
  }
});

$('#stop-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopLocalVideo();
  });
$('#stop-preview-local-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopPreviewLocalVideo();
  });
$('#start-local-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.startLocalAudio();
  });
$('#switch-microphone')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchMicrophone();
  });
$('#stop-local-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.stopLocalAudio();
  });
$('#start-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    const div = addView('local_screen', TRTC.TYPE.STREAM_TYPE_SUB, true);
    rtc.trtc.startScreenShare({
      view: div,
      option: {
        systemAudio: $('#system-audio')[0].checked,
        // qosPreference: screenQos(),
        profile: { width: 1920, height: 1080, frameRate: 15, bitrate: 6000 },
        // captureElement: document.getElementById('form'),
        // preferDisplaySurface: 'tab'
      },
    }).catch((error) => {
      ToastError(error);
      if (error.handler) {
        setTimeout(error.handler, 1000);
      }
      throw error;
    });
  });
$('#stop-screen-share')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.stopScreenShare();
  });
$('#start-remote-video')
  .on('click', (e) => {
    e.preventDefault();
    const remoteUserId = getRemoteUserId();
    if (remoteUserId === '*') {
      rtc.remotePublishedMainVideoUserIdSet.forEach((userId) => {
        const id = `${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_MAIN);
        rtc.startRemoteVideo(id, userId, TRTC.TYPE.STREAM_TYPE_MAIN);
      });
      rtc.remotePublishedSubVideoUserIdSet.forEach((userId) => {
        const id = `${userId}_${TRTC.TYPE.STREAM_TYPE_SUB}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_SUB);
        rtc.startRemoteVideo(id, userId, TRTC.TYPE.STREAM_TYPE_SUB);
      });
    } else {
      if (rtc.remotePublishedMainVideoUserIdSet.has(remoteUserId)) {
        const id = `${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`;
        addView(remoteUserId, TRTC.TYPE.STREAM_TYPE_MAIN);
        rtc.startRemoteVideo(id, remoteUserId, TRTC.TYPE.STREAM_TYPE_MAIN);
      }
      if (rtc.remotePublishedSubVideoUserIdSet.has(remoteUserId)) {
        const id = `${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_SUB}`;
        addView(userId, TRTC.TYPE.STREAM_TYPE_SUB);
        rtc.startRemoteVideo(id, remoteUserId, TRTC.TYPE.STREAM_TYPE_SUB);
      }
    }
  });
$('#stop-remote-video')
  .on('click', async (e) => {
    e.preventDefault();
    const remoteUserId = getRemoteUserId();
    if (remoteUserId === '*') {
      rtc.stopRemoteVideo({ userId: remoteUserId });
      rtc.remotePublishedMainVideoUserIdSet.forEach((userId) => {
        removeView(`${userId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
      });
      rtc.remotePublishedSubVideoUserIdSet.forEach((userId) => {
        removeView(`${userId}_${TRTC.TYPE.STREAM_TYPE_SUB}`);
      });
    } else {
      await rtc.stopRemoteVideo({ userId: remoteUserId, streamType: TRTC.TYPE.STREAM_TYPE_MAIN });
      await rtc.stopRemoteVideo({ userId: remoteUserId, streamType: TRTC.TYPE.STREAM_TYPE_SUB });
      removeView(`${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_MAIN}`);
      removeView(`${remoteUserId}_${TRTC.TYPE.STREAM_TYPE_SUB}`);
    }
  });
$('#mute-remote-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.muteRemoteAudio(getRemoteUserId(), true);
  });
$('#unmute-remote-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.muteRemoteAudio(getRemoteUserId(), false);
  });
$('#switch-camera')
  .on('click', (e) => {
    e.preventDefault();
    rtc.switchCamera();
  });
$('#update-video-profile')
  .on('click', (e) => {
    e.preventDefault();
    rtc.updateVideoProfile();
  });
$('#update-audio-profile')
  .on('click', (e) => {
    e.preventDefault();
    rtc.updateAudioProfile();
  });
$('#recordPCM')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.dumpAudio(10).then((download) => {
      $('#downloadPCM').removeClass('disabled');
      $('#downloadPCM')
        .one('click', (e) => {
          e.preventDefault();
          if (download) {
            download();
            $('#downloadPCM').addClass('disabled');
          }
        });
    });
  });
$('#gain0').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 0 }
  });
});
$('#gain50').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 50 }
  });
});
$('#gain100').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 100 }
  });
});
$('#gain300').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 300 }
  });
});
$('#gain500').on('click', (e) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: 500 }
  });
});

$('#beauty-ar').on('click', (e) => {
  rtc.trtc.startPlugin('Beauty', {
    sdkAppId: rtc.sdkAppId_,
    userId: rtc.userId_,
    userSig: rtc.userSig_,

    whiten: 0.5, // Whitening 0-1
    dermabrasion: 0.5, // Dermabrasion 0-1
    lift: 0.5, // Face Slimming 0-1
    shave: 0.5, // Jaw Shaving 0-1
    eye: 0.5, // Enlarged Eyes 0-1
    chin: 0.5, // Chin Adjustment 0-1

    effect: [{
      id: '7A62218064EF7959', // the effect id
      intensity: 0.7, // specify the strength of the effect
    },
    {
      id: 'D7C6418064B90F4C', // the effect id
      intensity: 0.7, // specify the strength of the effect
    }]
  });
});
function getMixCamera() {
  const camera = [];
  if ($('#mixCamera1')[0].checked) camera.push({
    id: 'camera1',
    profile: { width: 1280, height: 720, frameRate: 30 },
    layout: {
      x: parseFloat($('#cameraX').val()),
      y: parseFloat($('#cameraY').val()),
      width: parseFloat($('#cameraWidth').val()),
      height: parseFloat($('#cameraHeight').val()),
      zIndex: parseInt($('#cameraZIndex').val(), 10),
      mirror: $('#mix-camera-mirror')[0].checked,
      rotation: parseInt($('#camera-mix-rotation').val(), 10),
      fillMode: $('#camera-mix-fillMode').val()
    }
  });
  return camera;
}
function getMixScreen() {
  const screen = [];
  if ($('#mixScreen1')[0].checked) screen.push({
    id: 'screen1',
    profile: { width: 1920, height: 1080, frameRate: 15 },
    layout: {
      x: parseFloat($('#screenX').val()),
      y: parseFloat($('#screenY').val()),
      width: parseFloat($('#screenWidth').val()),
      height: parseFloat($('#screenHeight').val()),
      zIndex: parseInt($('#screenZIndex').val(), 10),
      mirror: $('#mix-screen-mirror')[0].checked,
      rotation: parseInt($('#screen-mix-rotation').val(), 10),
      fillMode: $('#screen-mix-fillMode').val()
    }
  });
  return screen;
}
function getMixText() {
  const text = [];
  if ($('#mixText1')[0].checked) text.push({
    id: 'text1',
    content: '众🀄️AQY',
    font: 'bold 60px SimHei',
    color: 'red',
    layout: {
      x: 200,
      y: 300,
      width: 600,
      height: 150,
      zIndex: 6,
      // mirror: true
      rotation: 90
    },
  });
  if ($('#mixText2')[0].checked) text.push({
    id: 'text2',
    content: 'MultiLine\nEMOJI🥳',
    font: 'bold 60px Georgia',
    color: 'rgb(255 0 153 / 80%)',
    layout: {
      x: 0,
      y: 500,
      width: 300,
      height: 150,
      zIndex: 7,
      rotation: 90
    },
  },);
  return text;
}
function getMixImage() {
  const image = [];
  if ($('#mixImage1')[0].checked) image.push({
    id: 'img1',
    url: './tv2.png',
    layout: {
      x: 0,
      y: 500,
      width: 800,
      height: 400,
      zIndex: 4,
      fillMode: 'fill',
      rotation: 90
      // mirror: true
    }
  });
  if ($('#mixImage2')[0].checked) image.push({
    id: 'img2',
    url: './mute.png',
    layout: {
      x: 500,
      y: 100,
      width: 500,
      height: 400,
      zIndex: 5
    }
  });
  return image;
}
let mixVideoTrack;
function getMixVideo() {
  const video = [];
  if ($('#mixVideo1')[0].checked) video.push({
    id: 'video1',
    // url: 'https://webrtc-1252463788.cos-website.ap-guangzhou.myqcloud.com/test/jialelin/rtc/video_1920_1080_24fps.mp4',
    url: './video_1920_1080_24fps.mp4',
    layout: {
      x: 500,
      y: 200,
      width: 1000,
      height: 500,
      zIndex: 10,
      rotation: 90
    }
  });
  if ($('#mixVideo2')[0].checked) video.push({
    id: 'video2',
    // url: 'https://webrtc-1252463788.cos-website.ap-guangzhou.myqcloud.com/test/jialelin/rtc/video_1280_720_30fps.mp4',
    url: './video_1280_720_30fps.mp4',
    layout: {
      x: 500,
      y: 300,
      width: 1000,
      height: 720,
      zIndex: 11,
      fillMode: 'fill',
      mirror: true
    }
  });
  return video;
}
async function getAnotherCameraTrack() {
  const cameraList = await TRTC.getCameraList();
  console.log(cameraList);
  const constraints = isMobile() ? { video: { facingMode: 'environment' } } : { video: { deviceId: { exact: cameraList[1].deviceId } } };
  const stream = await navigator.mediaDevices.getUserMedia(constraints);
  const track = stream.getVideoTracks()[0];
  return track;
}
$('#start-video-mixer').on('click', async (e) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  // 渐变起点在 x=500、y=0 终点在 x=1500、y=0
  const gradient = ctx.createLinearGradient(500, 0, 1500, 0);

  // 添加三个色标
  gradient.addColorStop(0, 'green');
  gradient.addColorStop(0.5, 'cyan');
  gradient.addColorStop(1, 'yellow');
  const div = addView('local_mix_stream', TRTC.TYPE.STREAM_TYPE_MAIN, true);
  const { track, result } = await rtc.trtc.startPlugin('VideoMixer', {
    view: div,
    canvasInfo: {
      canvasColor: gradient,
      width: parseFloat($('#mixCanvasWidth').val()),
      height: parseFloat($('#mixCanvasHeight').val()),
    },
    camera: getMixCamera(),
    screen: getMixScreen(),
    text: getMixText(),
    image: getMixImage(),
    video: getMixVideo(),
    onScreenShareStop: (id) => {
      console.log(`screen share stop, id: ${id}`);
    }
  });
  mixVideoTrack = track;
  console.log('mix parse result:', result);
  canvas.remove();
});
$('#update-video-mixer').on('click', async (e) => {
  let mixResult;
  ({ result: mixResult } = await rtc.trtc.updatePlugin('VideoMixer', {
    canvasInfo: {
      width: parseFloat($('#mixCanvasWidth').val()),
      height: parseFloat($('#mixCanvasHeight').val()),
    },
    camera: getMixCamera(),
    screen: getMixScreen(),
    text: getMixText(),
    image: getMixImage(),
    video: getMixVideo()
  }));
  console.log('mix parse result:', mixResult);
  return;
  // 下面用于自定义调试
  // const cameraList = await TRTC.getCameraList();
  // const videoTrack = await getAnotherCameraTrack();
  // await videoTrack.applyConstraints({
  //   width: 1280,
  //   height: 720,
  //   frameRate: 30
  // });
  ({ result: mixResult }  = await rtc.trtc.updatePlugin('VideoMixer', {
    // canvasInfo: {
    //   width: parseFloat($('#mixCanvasWidth').val()),
    //   height: parseFloat($('#mixCanvasHeight').val()),
    //   frameRate: 10
    // },
    camera: [
      {
        id: 'camera1',
        // cameraId: cameraList[1].deviceId,
        // videoTrack,
        // profile: {
        //   width: 1280,
        //   height: 720,
        //   frameRate: 30,
        // },
        layout: {
          x: 100,
          y: 100,
          width: 400,
          height: 400,
          zIndex: 1,
          // rotation: 0,
          fillMode: 'contain'
        }
      },
      // {
      //   id: 'camera2',
      //   cameraId: cameraList[1].deviceId,
      //   // videoTrack,
      //   profile: {
      //     width: 1920,
      //     height: 1080,
      //     frameRate: 30,
      //     birate: 2000
      //   },
      //   layout: {
      //     x: 500,
      //     y: 100,
      //     width: 960,
      //     height: 480,
      //     zIndex: 0,
      //   }
      // }
    ],
    screen: [
      {
        id: 'screen1',
        profile: { width: 1000, height: 500, frameRate: 15 },
        layout: {
          x: 400,
          y: 100,
          width: 1000,
          height: 500,
          zIndex: 0,
          mirror: true
        }
      },
      {
        id: 'screen2',
        profile: { width: 1000, height: 500, frameRate: 20 },
        preferDisplaySurface: 'tab',
        layout: {
          x: 500,
          y: 500,
          width: 1000,
          height: 500,
          zIndex: 2
        }
      }
    ],
    text: [
      {
        id: 'text1',
        content: '变化文字',
        font: 'bold 120px Georgia',
        color: 'blue',
        layout: {
          x: 200,
          y: 300,
          width: 400,
          height: 150,
          zIndex: 6,
          // mirror: true,
          rotation: 0
        },
      }
    ],
    image: [{
      id: 'img1',
      url: './tv2.png',
      layout: {
        x: 0,
        y: 500,
        width: 800,
        height: 400,
        zIndex: 4,
        mirror: true
      }
    }],
    video: [{
      id: 'video1',
      url: './video_1280_720_30fps.mp4',
      layout: {
        x: 500,
        y: 200,
        width: 1000,
        height: 500,
        zIndex: -1,
        rotation: 0
      }
    }]
  }));
  console.log('mix parse result:', mixResult);
});
$('#publish-mix').on('click', (e) => {
  rtc.trtc.startLocalVideo({
    option: {
      videoTrack: mixVideoTrack,
      profile: {
        width: 1920,
        height: 1080,
        bitrate: 2000
      }
    }
  });
});
$('#update-publish-mix').on('click', (e) => {
  rtc.trtc.updateLocalVideo({
    option: {
      videoTrack: mixVideoTrack,
      profile: {
        width: 1280,
        height: 540,
        bitrate: 1000
      }
    }
  });
});
$('#stop-publish-mix').on('click', (e) => {
  rtc.trtc.stopLocalVideo();
});
let vmIntervalId;
$('#simulate-drag').on('change', (e) => {
  if ($('#simulate-drag')[0].checked) {
    let x = 0; let y = 0;
    const cameraW = parseFloat($('#cameraWidth').val());
    const cameraH =  parseFloat($('#cameraHeight').val());
    const cameraZ = parseInt($('#cameraZIndex').val(), 10);
    const screenW = parseFloat($('#screenWidth').val());
    const screenH = parseFloat($('#screenHeight').val());
    const screenZ = parseInt($('#screenZIndex').val(), 10);
    vmIntervalId = setInterval(() => {
      x += 8;
      y += 6;
      if (x > 1920 || y > 1080) {
        x = 0; y = 0;
      }
      const mixParams = { camera: [], screen: [], text: [], image: [], video: [] };
      if ($('#mixCamera1')[0].checked) mixParams.camera.push({
        id: 'camera1',
        layout: {
          x,
          y,
          width: cameraW,
          height: cameraH,
          zIndex: cameraZ,
        }
      });
      if ($('#mixScreen1')[0].checked) mixParams.screen.push({
        id: 'screen1',
        layout: {
          x: 0.5 * x,
          y: 0.5 * y,
          width: screenW,
          height: screenH,
          zIndex: screenZ
        }
      });
      if ($('#mixText1')[0].checked) mixParams.text.push({
        id: 'text1',
        content: '众🀄️AQY',
        layout: {
          x: x * 2,
          y: y * 2,
          width: 600,
          height: 300,
          zIndex: 6
        },
      });
      if ($('#mixImage1')[0].checked) mixParams.image.push({
        id: 'img1',
        url: './tv2.png',
        layout: {
          x: 0,
          y,
          width: 300,
          height: 500,
          zIndex: 4
        }
      });
      if ($('#mixVideo1')[0].checked) mixParams.video.push({
        id: 'video1',
        url: './video_1920_1080_24fps.mp4',
        layout: {
          x,
          y: 200,
          width: 1000,
          height: 500,
          zIndex: 10
        },
      });
      rtc.trtc.updatePlugin('VideoMixer', mixParams);
    }, 50);
  } else {
    clearInterval(vmIntervalId);
  }
});
$('#stop-video-mixer').on('click', (e) => {
  rtc.trtc.stopPlugin('VideoMixer');
  clearInterval(vmIntervalId);
  mixVideoTrack = null;
});
$('#device-detector').html('开启检测组件');
$('#device-detector-network').html('开启检测组件(网络)');
$('#device-detector').on('click', async (e) => {
  const result = await rtc.trtc.startPlugin('DeviceDetector', {
    allowSkip: false,
    language: 'auto',
    cameraDetect: {
      mirror: true
    }
  });
  console.log(result);
});
$('#device-detector-network').on('click', async (e) => {
  const sdkAppId = defaultSdkAppId;
  const userId = 'user_uplink_test';
  const userSig = await getUserSigFromServer(sdkAppId, 8080, userId);
  console.log('这是userSig', userSig);

  const downlinkUserId = 'user_downlink_test';
  const downlinkUserSig = await getUserSigFromServer(sdkAppId, 8080, downlinkUserId);
  const result = await rtc.trtc.startPlugin('DeviceDetector', { allowSkip: false, networkDetect: { sdkAppId, userId, userSig, downlinkUserId, downlinkUserSig } });
  console.log(result);
});
$('#debug-start').on('click', (e) => {
  rtc.trtc.startPlugin('Debug');
});
$('#debug-url').on('click', (e) => {
  const url = new URL(location.href);
  if (!url.searchParams.has('trtcDebug')) {
    url.searchParams.set('trtcDebug', '1');
  }
  window.open(url, '_blank');
});
$('#debug-stop').on('click', (e) => {
  rtc.trtc.stopPlugin('Debug');
});
$('#switch-role-anchor').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-anchor');
  rtc.switchRole('anchor');
});

$('#switch-role-audience').on('click', (e) => {
  e.preventDefault();
  console.log('switch-role-audience');
  rtc.switchRole('audience');
});

$('#volume').on('input', (event) => {
  rtc.remotePublishedAudioUserIdSet.forEach((userId) => {
    rtc.trtc.setRemoteAudioVolume(userId, Number(event.target.value));
  });
});

$('#volume-local').on('input', (event) => {
  rtc.trtc.updateLocalAudio({
    option: { captureVolume: Number(event.target.value) }
  });
});

$('#volume-ear').on('input', (event) => {
  rtc.trtc.updateLocalAudio({
    option: { earMonitorVolume: Number(event.target.value) }
  });
});

$('#leave')
  .on('click', (e) => {
    e.preventDefault();
    console.log('leave');
    rtc.leave();
  });
$('#destroy')
  .on('click', (e) => {
    e.preventDefault();
    rtc.destroy();
  });

$('#mute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: true });
  });

$('#mute-video-image')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: './mute.png' });
  });

$('#unmute-video')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalVideo({ mute: false });
  });
$('#mute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: true });
  });
$('#unmute-audio')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.updateLocalAudio({ mute: false });
  });
$('#send-sei')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#sei').value);
    rtc.trtc.sendSEIMessage(buffer.buffer);
  });
$('#send-sei-aux')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#sei').value);
    rtc.trtc.sendSEIMessage(buffer.buffer, { toSubStream: true });
  });
$('#send-custom-msg')
  .on('click', (e) => {
    e.preventDefault();
    const encoder = new TextEncoder();
    const buffer = encoder.encode(document.querySelector('#custom-msg').value);
    rtc.trtc.sendCustomMessage({
      cmdId: +document.getElementById('cmdId').value,
      data: buffer.buffer
    });
  });
$('#open-setting')
  .on('click', (e) => {
    e.preventDefault();
    const form = document.querySelector('#form');
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-setting');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭设置' : '打开设置';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#close-setting')
  .on('click', (e) => {
    e.preventDefault();
    const form = document.querySelector('#form');
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-setting');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭设置' : '打开设置';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#open-stat')
  .on('click', (e) => {
    e.preventDefault();
    const stat = document.querySelector('#debug-stat');
    stat.style.display = stat.style.display === 'none' ? 'block' : 'none';
    const button = document.querySelector('#open-stat');
    const IS_OPEN = button.innerText.includes('打开');
    button.innerText = IS_OPEN ? '关闭 Stat' : '打开 Stat';
    if (IS_OPEN) {
      button.className = button.className.replace('primary', 'danger');
    } else {
      button.className = button.className.replace('danger', 'primary');
    }
  });
$('#speaker')
  .on('change', (e) => {
    e.preventDefault();
    TRTC.setCurrentSpeaker(getSpeakerId());
  });

$('#settings')
  .on('click', (e) => {
    e.preventDefault();
    $('#settings')
      .toggleClass('btn-raised');
    $('#setting-collapse')
      .collapse();
  });

$('#video-frame')
  .on('click', (e) => {
    e.preventDefault();
    const frameURL = rtc.trtc.getVideoSnapshot({ userId: $('#video-frame-userId').val(), streamType: $('#video-frame-streamType').val() });
    if (frameURL) {
      const img = document.createElement('img');
      img.width = '640';
      img.height = '480';
      img.src = frameURL;
      document.body.appendChild(img);
    }
  });

$('#start-audio-frame')
  .on('click', (e) => {
    e.preventDefault();
    const sampleRate = +$('input[name="audio-frame-sample-rate"]:checked').val();
    const channelCount = +$('input[name="audio-frame-channel-count"]:checked').val();
    rtc.trtc.callExperimentalAPI('enableAudioFrameEvent', { userId: '', enable: true, sampleRate, channelCount }); // local
    rtc.trtc.callExperimentalAPI('enableAudioFrameEvent', { userId: '*', enable: true, sampleRate, channelCount }); // remote
    rtc.trtc.on('audio-frame', (event) => {
      console.log(event);
    });
  });

$('#stop-audio-frame')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.callExperimentalAPI('enableAudioFrameEvent', { userId: '', enable: false }); // local
    rtc.trtc.callExperimentalAPI('enableAudioFrameEvent', { userId: '*', enable: false }); // remote
    rtc.trtc.off('audio-frame');
  });

$('#start-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const roomId = +$('#remote-roomId').val();
    const strRoomId = $('#remote-strRoomId').val();
    const userId = $('#cross-room-remote-userId').val() || undefined;
    rtc.trtc.startPlugin('CrossRoom', {
      roomId,
      strRoomId,
      userId,
    });
  });
$('#update-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const roomId = +$('#remote-roomId').val();
    const strRoomId = $('#remote-strRoomId').val();
    const userId = $('#cross-room-remote-userId').val() || undefined;
    rtc.trtc.updatePlugin('CrossRoom', {
      updateList: [{
        roomId,
        strRoomId,
        userId,
        muteAudio: $('#cross-room-muteAudio')[0].checked,
        muteVideo: $('#cross-room-muteVideo')[0].checked,
        muteSubStream: $('#cross-room-muteSubStream')[0].checked,
      }]
    });
  });
$('#stop-cross-room')
  .on('click', (e) => {
    e.preventDefault();
    const userId = $('#cross-room-remote-userId').val() || undefined;

    let option;
    if (!userId) {
      option = {
        roomId: +$('#remote-roomId').val(),
        strRoomId: $('#remote-strRoomId').val(),
      };
    }

    rtc.trtc.stopPlugin('CrossRoom', option);
  });
$('#algorithm-select').on('change', () => {
  if (!$('#algorithm-select option:selected').val()) {
    $('#secret-key').attr('disabled', true);
    $('#salt').attr('disabled', true);
  } else {
    $('#secret-key').attr('disabled', false);
    $('#salt').attr('disabled', false);
  }
});
$('#start-custom-encryption')
  .on('click', (e) => {
    e.preventDefault();
    const customCryptors = {
      encryptor: xorUint8Array,
      decryptor: xorUint8Array
    };
    const algorithm = $('#algorithm-select option:selected').val();
    const secretKey = new TextEncoder().encode($('#secret-key').val());
    // if (algorithm === 'aes128gcm') {
    //   secretKey = padOrTruncateString($('#secret-key').val(), 128);
    // } else if (algorithm === 'aes256gcm') {
    //   secretKey = padOrTruncateString($('#secret-key').val(), 256);
    // }
    const salt = new TextEncoder().encode($('#salt').val()) || undefined;
    if (algorithm) {
      rtc.trtc.startPlugin('CustomEncryption', {
        mode: 'webrtc',
        video: true,
        builtinOptions: {
          algorithm,
          secretKey,
          salt
        },
      });
    } else {
      rtc.trtc.startPlugin('CustomEncryption', {
        mode: 'webrtc',
        video: true,
        customCryptors
      });
    }
  });

$('#stop-custom-encryption')
  .on('click', (e) => {
    e.preventDefault();
    rtc.trtc.stopPlugin('CustomEncryption');
  });

$('#start-voice-changer').on('click', (e) => {
  e.preventDefault();
  const voiceChangerType = document.querySelector('#voice-changer').value;
  rtc.trtc.startPlugin('VoiceChanger', {
    voiceType: Number(voiceChangerType),
    sdkAppId: rtc.sdkAppId_,
    userId: rtc.userId_,
    userSig: rtc.userSig_
  });
});
$('#update-voice-changer').on('click', (e) => {
  e.preventDefault();
  const voiceChangerType = document.querySelector('#voice-changer').value;
  rtc.trtc.updatePlugin('VoiceChanger', { voiceType: Number(voiceChangerType) });
});

$('#stop-voice-changer').on('click', (e) => {
  e.preventDefault();
  rtc.trtc.stopPlugin('VoiceChanger');
});

$('#microphoneId')
  .on('change', (e) => {
    e.preventDefault();
    rtc.switchMicrophone();
  });
$('#cameraId')
  .on('change', (e) => {
    e.preventDefault();
    rtc.switchCamera();
  });
TRTC.isSupported().then((result) => {
  const notSupported = Object.keys(result.detail).filter(key => !result.detail[key]);
  if (notSupported.length > 0) {
    const p = document.createElement('p');
    p.style.width = '300px';
    p.style.wordWrap = 'break-word';
    p.style.background = 'gray';

    p.innerText = `不支持如下能力：\n${notSupported.join('\n')}`;
    document.querySelector('#form').appendChild(p);
  }
});

async function updateDeviceList() {
  const microphoneList = await TRTC.getMicrophoneList();
  const cameraList = await TRTC.getCameraList();
  const speakerList = await TRTC.getSpeakerList();

  document.querySelector('#microphoneId').innerHTML = `
    ${microphoneList.filter(device => !device.label.includes('OPPO'))
    .map(device => `<option ${selectedMicrophoneId === device.deviceId ? 'selected' : ''} value="${device.deviceId}">${device.label}</option>`)}
  `;
  document.querySelector('#speaker').innerHTML = `
    ${speakerList
    .map(device => `<option value="${device.deviceId}" ${selectedSpeakerId === device.deviceId ? 'selected' : ''}>${device.label}</option>`)}
  `;
  document.querySelector('#cameraId').innerHTML = `
    ${cameraList.filter(device => !device.label.includes('xxxxxxxxx'))
    .map(device => `<option value="${device.deviceId}" ${selectedCameraId === device.deviceId ? 'selected' : ''}>${device.label}</option>`)}
  `;
}

function getCameraId(cameraName) {
  const selector = document.getElementById('cameraId');
  let cameraId = selector[selector.selectedIndex] ? selector[selector.selectedIndex].value : undefined;
  console.warn('getCameraId() cameraName', cameraName);
  if (cameraName) {
    const options = Array.from(selector.options);
    console.warn('getCameraId() cameraName', cameraName, 'options', options);
    const targetOption = options.find(option => option.text.toLowerCase().includes(cameraName.toLowerCase()));
    console.warn('targetOption', targetOption);
    if (targetOption) {
      console.warn(`auto find cameraId includes ${cameraName}: ${targetOption.value}`);
      cameraId = targetOption.value;
    }
  }
  console.log(`selected cameraId: ${cameraId}`);
  return cameraId;
}

function getRemoteUserId() {
  const selector = document.getElementById('remote-userId');
  const result = selector[selector.selectedIndex].value;
  return result;
}

function getMicrophoneId() {
  const selector = document.getElementById('microphoneId');
  const microphoneId = selector[selector.selectedIndex] ? selector[selector.selectedIndex].value : undefined;
  console.log(`selected microphoneId: ${microphoneId}`);
  return microphoneId;
}
function getSpeakerId() {
  const selector = document.getElementById('speaker');
  if (!selector[selector.selectedIndex]) {
    return '';
  }
  const speakerId = selector[selector.selectedIndex].value;
  console.log(`selected speakerId: ${speakerId}`);
  return speakerId;
}

function getRotationAngle() {
  const selector = document.getElementById('rotation-angle');
  const result = selector[selector.selectedIndex].value;
  return Number(result) || undefined;
}

function getVideoProfile() {
  const selector = document.getElementById('video-profile');
  const profile = selector[selector.selectedIndex].value;
  console.log(`selected video profile: ${profile}`);
  if (profile === '480p30fps600kbps') {
    console.log('selected video profile: 480p30fps600kbps');
    return { width: 864, height: 480, frameRate: 30, bitrate: 600 };
  }
  if (profile === '480p30fps1500kbps') {
    console.log('selected video profile: 480p30fps1500kbps');
    return { width: 864, height: 480, frameRate: 30, bitrate: 1500 };
  }
  if (profile === '960p30fps1000kbps') {
    console.log('selected video profile: 960p30fps1000kbps');
    return { width: 1728, height: 960, frameRate: 30, bitrate: 1000 };
  }
  if (profile === '960p30fps2000kbps') {
    console.log('selected video profile: 960p30fps2000kbps');
    return { width: 1728, height: 960, frameRate: 30, bitrate: 2000 };
  }
  if (profile === '720p30') {
    console.log('selected video profile: 720p30');
    return { width: 1280, height: 720, frameRate: 30, bitrate: 1200 };
  }
  if (profile === '1080p60fps') {
    console.log('selected video profile: 1080p60fps');
    return { width: 1920, height: 1080, frameRate: 60, bitrate: 6000 };
  }
  return profile;
}
function getAudioProfile() {
  const selector = document.getElementById('audio-profile');
  const profile = selector[selector.selectedIndex].value;
  console.log(`selected audio profile: ${profile}`);
  return profile;
}
function getProxyServer() {
  const selector = document.getElementById('proxy-server');
  const _proxy = selector[selector.selectedIndex].value;
  console.log(`selected proxy: ${_proxy}`);
  const proxy = {
    unifiedProxy: getUnifiedServer(),
  };
  if (document.getElementById('turn-server').value) {
    proxy.turnServer = {
      url: document.getElementById('turn-server').value,
      username: document.getElementById('turn-username').value,
      credential: document.getElementById('turn-credential').value,
      credentialType: 'password'
    };
    if (document.getElementById('force-turn').checked) {
      proxy.iceTransportPolicy = 'relay';
    }
  }
  if (_proxy.startsWith('ws')) {
    proxy.websocketProxy = _proxy;
  } else {
    proxy.webtransportProxy = _proxy;
  }
  return proxy;
}

function getUnifiedServer() {
  const input = $('#unified-proxy-input').val();
  const selector = document.getElementById('unified-proxy');
  const proxy = input || selector[selector.selectedIndex].value;
  console.log(`selected unified-proxy: ${proxy}`);
  return proxy;
}

function getRole() {
  const selector = document.getElementById('role');
  const role = selector[selector.selectedIndex].value;
  console.log(`selected role: ${role}`);
  return role;
}

function getLatencyLevel() {
  const selector = document.getElementById('latencyLevel');
  const latencyLevel = selector[selector.selectedIndex].value;
  console.log(`selected latencyLevel: ${latencyLevel}`);
  return Number(latencyLevel) || undefined;
}

function getScene() {
  const selector = document.getElementById('scene');
  const scene = selector[selector.selectedIndex].value;
  console.log(`selected scene: ${scene}`);
  return scene;
}

function isAutoReceiveAudio() {
  const selector = document.getElementById('auto-receive-audio');
  const result = selector[selector.selectedIndex].value;
  return Number(result) === 1;
}
function isAutoReceiveVideo() {
  const selector = document.getElementById('auto-receive-video');
  const result = selector[selector.selectedIndex].value;
  return Number(result) === 1;
}

function screenQos() {
  const selector = document.getElementById('screen-qos');
  const result = selector[selector.selectedIndex].value;
  return result;
}

function Toast(text) {
  Toastify({
    text,
    position: 'left',
    offset: {
      y: 40
    }
  }).showToast();
}

function ToastError(error = {}) {
  if (error && error.code === 5998) return;
  console.error(error);
  Toastify({ text: typeof error === 'string' ? error : `${error.functionName} failed, code: ${error.code} extraCode: ${error.extraCode} message: ${error.message}`, style: { background: 'red' } }).showToast();
}

// let sourceVideoTrack = null;
// let intervalId = -1;
// let video = null;

// 用于加载水印图片
// function loadImage(imageUrl) {
//   return new Promise((resolve) => {
//     const image = new Image();
//     // 开启跨域访问，避免出现加载非同源的图片资源时，开启水印生成的 videoTrack 是黑屏的问题。
//     image.crossOrigin = 'anonymous';
//     image.src = imageUrl;
//     image.onload = () => resolve(image);
//   });
// }

async function startWaterMark({ x, y, width, height, imageUrl }) {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.setWatermark({ x, y, width, height, imageUrl });
  }
  // if (intervalId > 0) return;
  // // 2. 创建 video 标签播放视频流
  // const video = document.createElement('video');
  // sourceVideoTrack = rtc.trtc.getVideoTrack();
  // const mediaStream = new MediaStream();
  // mediaStream.addTrack(sourceVideoTrack);
  // video.srcObject = mediaStream;
  // await video.play();

  // // 3. 加载水印图片
  // const image = await loadImage(imageUrl);

  // // 4. 创建 canvas 标签，并使用 setInterval 将视频和水印绘制到 canvas 画布中
  // const canvas = document.createElement('canvas');
  // const ctx = canvas.getContext('2d');
  // const settings = sourceVideoTrack.getSettings();
  // canvas.width = settings.width;
  // canvas.height = settings.height;

  // intervalId = setInterval(() => {
  //   // 将视频绘制到画布中
  //   ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
  //   // 将水印图片绘制到画布中，可以控制水印的位置和大小
  //   ctx.drawImage(image, x, y, width || image.width, height || image.height);
  // }, Math.floor(1000 / settings.frameRate)); // 根据帧率计算每次绘制的时间间隔

  // // 5. 使用 canvas.captureStream 从画布中采集视频流，使用 updateLocalVideo 替换视频流
  // const canvasStream = canvas.captureStream();
  // await rtc.trtc.updateLocalVideo({ option: { videoTrack: canvasStream.getVideoTracks()[0] } });
}

// 关闭水印
async function stopWaterMark() {
  if (rtc.trtc) {
    rtc.trtc._room.videoManager.stopWatermark();
  }
  // if (intervalId > 0) {
  //   clearInterval(intervalId);
  //   intervalId = -1;
  //   await rtc.trtc.updateLocalVideo({ option: { videoTrack: sourceVideoTrack } });
  //   if (video) {
  //     video.srcObject = null;
  //     video = null;
  //   }
  // }
}

function xorUint8Array(frame) {
  const result = new Uint8Array(frame.length);
  for (let i = 0; i < frame.length; i++) {
    result[i] = frame[i] ^ 5;
  }
  return result;
}

function padOrTruncateString(inputString, bitNum) {
  const utf8Encoder = new TextEncoder();
  const bitLength = inputString.length * 8;
  if (bitLength > bitNum) {
    return utf8Encoder.encode(inputString.slice(0, Math.ceil(bitNum / 8)));
  }
  const paddingLength = Math.ceil(bitNum / 8) - inputString.length;
  const padding = '0'.repeat(paddingLength);
  return utf8Encoder.encode(padding + inputString);
}

// test change
