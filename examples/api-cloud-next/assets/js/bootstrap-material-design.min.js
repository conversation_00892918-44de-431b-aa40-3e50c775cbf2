(function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["jquery","popper.js"],t):(e=e||self,t(e.j<PERSON><PERSON><PERSON>,e<PERSON><PERSON><PERSON>))})(this,function(e,t){'use strict';var f=Math.max;function n(e){return{}.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase()}function o(){return{bindType:u,delegateType:u,handle(t){return e(t.target).is(this)?t.handleObj.handler.apply(this,arguments):void 0}}}function a(t){let n=!1;return e(this).one(_.TRANSITION_END,()=>{n=!0}),setTimeout(()=>{n||_.triggerTransitionEnd(this)},t),this}function r(){e.fn.emulateTransitionEnd=a,e.event.special[_.TRANSITION_END]=o()}function i(e,t){const n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===se.indexOf(n)||!!(e.nodeValue.match(fe)||e.nodeValue.match(ue));const o=t.filter(e=>e instanceof RegExp);for(let a=0,r=o.length;a<r;a++)if(n.match(o[a]))return!0;return!1}function l(e,t,n){if(0===e.length)return e;if(n&&"function"==typeof n)return n(e);const o=new window.DOMParser,a=o.parseFromString(e,"text/html"),r=Object.keys(t),l=[].slice.call(a.body.querySelectorAll("*"));for(let o=0,a=l.length;o<a;o++){const e=l[o],n=e.nodeName.toLowerCase();if(-1===r.indexOf(e.nodeName.toLowerCase())){e.parentNode.removeChild(e);continue}const a=[].slice.call(e.attributes),d=[].concat(t["*"]||[],t[n]||[]);a.forEach(t=>{i(t,d)||e.removeAttribute(t.nodeName)})}return a.body.innerHTML}function d(e,t){for(var n,o=0;o<t.length;o++)n=t[o],n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}function s(e,t,n){return t&&d(e.prototype,t),n&&d(e,n),e}function c(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}e=e&&e.hasOwnProperty("default")?e["default"]:e,t=t&&t.hasOwnProperty("default")?t["default"]:t;const u="transitionend",_={TRANSITION_END:"bsTransitionEnd",getUID(e){do e+=~~(Math.random()*1e6);while(document.getElementById(e));return e},getSelectorFromElement(e){let t=e.getAttribute("data-target");if(!t||"#"===t){const n=e.getAttribute("href");t=n&&"#"!==n?n.trim():""}try{return document.querySelector(t)?t:null}catch(e){return null}},getTransitionDurationFromElement(t){if(!t)return 0;let n=e(t).css("transition-duration"),o=e(t).css("transition-delay");const a=parseFloat(n),r=parseFloat(o);return a||r?(n=n.split(",")[0],o=o.split(",")[0],(parseFloat(n)+parseFloat(o))*1e3):0},reflow(e){return e.offsetHeight},triggerTransitionEnd(t){e(t).trigger(u)},supportsTransitionEnd(){return!0},isElement(e){return(e[0]||e).nodeType},typeCheckConfig(e,t,o){for(const a in o)if(Object.prototype.hasOwnProperty.call(o,a)){const r=o[a],i=t[a],l=i&&_.isElement(i)?"element":n(i);if(!new RegExp(r).test(l))throw new Error(`${e.toUpperCase()}: `+`Option "${a}" provided type "${l}" `+`but expected type "${r}".`)}},findShadowRoot(e){if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?_.findShadowRoot(e.parentNode):null}};r();const m="bs.alert",p=`.${m}`,g=e.fn.alert,E={CLOSE:`close${p}`,CLOSED:`closed${p}`,CLICK_DATA_API:`click${p}${".data-api"}`},h={ALERT:"alert",FADE:"fade",SHOW:"show"};class A{constructor(e){this._element=e}static get VERSION(){return"4.3.1"}close(e){let t=this._element;e&&(t=this._getRootElement(e));const n=this._triggerCloseEvent(t);n.isDefaultPrevented()||this._removeElement(t)}dispose(){e.removeData(this._element,m),this._element=null}_getRootElement(t){const n=_.getSelectorFromElement(t);let o=!1;return n&&(o=document.querySelector(n)),o||(o=e(t).closest(`.${h.ALERT}`)[0]),o}_triggerCloseEvent(t){const n=e.Event(E.CLOSE);return e(t).trigger(n),n}_removeElement(t){if(e(t).removeClass(h.SHOW),!e(t).hasClass(h.FADE))return void this._destroyElement(t);const n=_.getTransitionDurationFromElement(t);e(t).one(_.TRANSITION_END,e=>this._destroyElement(t,e)).emulateTransitionEnd(n)}_destroyElement(t){e(t).detach().trigger(E.CLOSED).remove()}static _jQueryInterface(t){return this.each(function(){const n=e(this);let o=n.data(m);o||(o=new A(this),n.data(m,o)),"close"===t&&o[t](this)})}static _handleDismiss(e){return function(t){t&&t.preventDefault(),e.close(this)}}}e(document).on(E.CLICK_DATA_API,{DISMISS:"[data-dismiss=\"alert\"]"}.DISMISS,A._handleDismiss(new A)),e.fn.alert=A._jQueryInterface,e.fn.alert.Constructor=A,e.fn.alert.noConflict=()=>(e.fn["alert"]=g,A._jQueryInterface);const C="bs.button",T=`.${C}`,I=".data-api",b=e.fn.button,O={ACTIVE:"active",BUTTON:"btn",FOCUS:"focus"},S={DATA_TOGGLE_CARROT:"[data-toggle^=\"button\"]",DATA_TOGGLE:"[data-toggle=\"buttons\"]",INPUT:"input:not([type=\"hidden\"])",ACTIVE:".active",BUTTON:".btn"},N={CLICK_DATA_API:`click${T}${I}`,FOCUS_BLUR_DATA_API:`focus${T}${I} `+`blur${T}${I}`};class D{constructor(e){this._element=e}static get VERSION(){return"4.3.1"}toggle(){let t=!0,n=!0;const o=e(this._element).closest(S.DATA_TOGGLE)[0];if(o){const a=this._element.querySelector(S.INPUT);if(a){if("radio"===a.type)if(a.checked&&this._element.classList.contains(O.ACTIVE))t=!1;else{const t=o.querySelector(S.ACTIVE);t&&e(t).removeClass(O.ACTIVE)}if(t){if(a.hasAttribute("disabled")||o.hasAttribute("disabled")||a.classList.contains("disabled")||o.classList.contains("disabled"))return;a.checked=!this._element.classList.contains(O.ACTIVE),e(a).trigger("change")}a.focus(),n=!1}}n&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(O.ACTIVE)),t&&e(this._element).toggleClass(O.ACTIVE)}dispose(){e.removeData(this._element,C),this._element=null}static _jQueryInterface(t){return this.each(function(){let n=e(this).data(C);n||(n=new D(this),e(this).data(C,n)),"toggle"===t&&n[t]()})}}e(document).on(N.CLICK_DATA_API,S.DATA_TOGGLE_CARROT,t=>{t.preventDefault();let n=t.target;e(n).hasClass(O.BUTTON)||(n=e(n).closest(S.BUTTON)),D._jQueryInterface.call(e(n),"toggle")}).on(N.FOCUS_BLUR_DATA_API,S.DATA_TOGGLE_CARROT,t=>{const n=e(t.target).closest(S.BUTTON)[0];e(n).toggleClass(O.FOCUS,/^focus(in)?$/.test(t.type))}),e.fn.button=D._jQueryInterface,e.fn.button.Constructor=D,e.fn.button.noConflict=()=>(e.fn["button"]=b,D._jQueryInterface);const y="carousel",v="bs.carousel",R=`.${v}`,L=".data-api",M=e.fn.carousel,U={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},P={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},F={NEXT:"next",PREV:"prev",LEFT:"left",RIGHT:"right"},w={SLIDE:`slide${R}`,SLID:`slid${R}`,KEYDOWN:`keydown${R}`,MOUSEENTER:`mouseenter${R}`,MOUSELEAVE:`mouseleave${R}`,TOUCHSTART:`touchstart${R}`,TOUCHMOVE:`touchmove${R}`,TOUCHEND:`touchend${R}`,POINTERDOWN:`pointerdown${R}`,POINTERUP:`pointerup${R}`,DRAG_START:`dragstart${R}`,LOAD_DATA_API:`load${R}${L}`,CLICK_DATA_API:`click${R}${L}`},H={CAROUSEL:"carousel",ACTIVE:"active",SLIDE:"slide",RIGHT:"carousel-item-right",LEFT:"carousel-item-left",NEXT:"carousel-item-next",PREV:"carousel-item-prev",ITEM:"carousel-item",POINTER_EVENT:"pointer-event"},k={ACTIVE:".active",ACTIVE_ITEM:".active.carousel-item",ITEM:".carousel-item",ITEM_IMG:".carousel-item img",NEXT_PREV:".carousel-item-next, .carousel-item-prev",INDICATORS:".carousel-indicators",DATA_SLIDE:"[data-slide], [data-slide-to]",DATA_RIDE:"[data-ride=\"carousel\"]"},G={TOUCH:"touch",PEN:"pen"};class Q{constructor(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(k.INDICATORS),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=!!(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}static get VERSION(){return"4.3.1"}static get Default(){return U}next(){this._isSliding||this._slide(F.NEXT)}nextWhenVisible(){!document.hidden&&e(this._element).is(":visible")&&"hidden"!==e(this._element).css("visibility")&&this.next()}prev(){this._isSliding||this._slide(F.PREV)}pause(e){e||(this._isPaused=!0),this._element.querySelector(k.NEXT_PREV)&&(_.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null}cycle(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))}to(t){this._activeElement=this._element.querySelector(k.ACTIVE_ITEM);const n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||0>t)){if(this._isSliding)return void e(this._element).one(w.SLID,()=>this.to(t));if(n===t)return this.pause(),void this.cycle();const o=t>n?F.NEXT:F.PREV;this._slide(o,this._items[t])}}dispose(){e(this._element).off(R),e.removeData(this._element,v),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null}_getConfig(e){return e={...U,...e},_.typeCheckConfig(y,e,P),e}_handleSwipe(){const e=Math.abs(this.touchDeltaX);if(!(e<=40)){const t=e/this.touchDeltaX;0<t&&this.prev(),0>t&&this.next()}}_addEventListeners(){this._config.keyboard&&e(this._element).on(w.KEYDOWN,e=>this._keydown(e)),"hover"===this._config.pause&&e(this._element).on(w.MOUSEENTER,e=>this.pause(e)).on(w.MOUSELEAVE,e=>this.cycle(e)),this._config.touch&&this._addTouchEventListeners()}_addTouchEventListeners(){if(!this._touchSupported)return;const t=e=>{this._pointerEvent&&G[e.originalEvent.pointerType.toUpperCase()]?this.touchStartX=e.originalEvent.clientX:!this._pointerEvent&&(this.touchStartX=e.originalEvent.touches[0].clientX)},n=e=>{this.touchDeltaX=e.originalEvent.touches&&1<e.originalEvent.touches.length?0:e.originalEvent.touches[0].clientX-this.touchStartX},o=e=>{this._pointerEvent&&G[e.originalEvent.pointerType.toUpperCase()]&&(this.touchDeltaX=e.originalEvent.clientX-this.touchStartX),this._handleSwipe(),"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(e=>this.cycle(e),500+this._config.interval))};e(this._element.querySelectorAll(k.ITEM_IMG)).on(w.DRAG_START,t=>t.preventDefault()),this._pointerEvent?(e(this._element).on(w.POINTERDOWN,e=>t(e)),e(this._element).on(w.POINTERUP,e=>o(e)),this._element.classList.add(H.POINTER_EVENT)):(e(this._element).on(w.TOUCHSTART,e=>t(e)),e(this._element).on(w.TOUCHMOVE,e=>n(e)),e(this._element).on(w.TOUCHEND,e=>o(e)))}_keydown(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next();break;default:}}_getItemIndex(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(k.ITEM)):[],this._items.indexOf(e)}_getItemByDirection(e,t){const n=e===F.NEXT,o=e===F.PREV,a=this._getItemIndex(t),r=this._items.length-1;if((o&&0===a||n&&a===r)&&!this._config.wrap)return t;const i=e===F.PREV?-1:1,l=(a+i)%this._items.length;return-1===l?this._items[this._items.length-1]:this._items[l]}_triggerSlideEvent(t,n){const o=this._getItemIndex(t),a=this._getItemIndex(this._element.querySelector(k.ACTIVE_ITEM)),r=e.Event(w.SLIDE,{relatedTarget:t,direction:n,from:a,to:o});return e(this._element).trigger(r),r}_setActiveIndicatorElement(t){if(this._indicatorsElement){const n=[].slice.call(this._indicatorsElement.querySelectorAll(k.ACTIVE));e(n).removeClass(H.ACTIVE);const o=this._indicatorsElement.children[this._getItemIndex(t)];o&&e(o).addClass(H.ACTIVE)}}_slide(t,n){const o=this._element.querySelector(k.ACTIVE_ITEM),a=this._getItemIndex(o),r=n||o&&this._getItemByDirection(t,o),i=this._getItemIndex(r),l=!!this._interval;let d,s,c;if(t===F.NEXT?(d=H.LEFT,s=H.NEXT,c=F.LEFT):(d=H.RIGHT,s=H.PREV,c=F.RIGHT),r&&e(r).hasClass(H.ACTIVE))return void(this._isSliding=!1);const f=this._triggerSlideEvent(r,c);if(f.isDefaultPrevented())return;if(!o||!r)return;this._isSliding=!0,l&&this.pause(),this._setActiveIndicatorElement(r);const u=e.Event(w.SLID,{relatedTarget:r,direction:c,from:a,to:i});if(e(this._element).hasClass(H.SLIDE)){e(r).addClass(s),_.reflow(r),e(o).addClass(d),e(r).addClass(d);const t=parseInt(r.getAttribute("data-interval"),10);t?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=t):this._config.interval=this._config.defaultInterval||this._config.interval;const n=_.getTransitionDurationFromElement(o);e(o).one(_.TRANSITION_END,()=>{e(r).removeClass(`${d} ${s}`).addClass(H.ACTIVE),e(o).removeClass(`${H.ACTIVE} ${s} ${d}`),this._isSliding=!1,setTimeout(()=>e(this._element).trigger(u),0)}).emulateTransitionEnd(n)}else e(o).removeClass(H.ACTIVE),e(r).addClass(H.ACTIVE),this._isSliding=!1,e(this._element).trigger(u);l&&this.cycle()}static _jQueryInterface(t){return this.each(function(){let n=e(this).data(v),o={...U,...e(this).data()};"object"==typeof t&&(o={...o,...t});const a="string"==typeof t?t:o.slide;if(n||(n=new Q(this,o),e(this).data(v,n)),"number"==typeof t)n.to(t);else if("string"==typeof a){if("undefined"==typeof n[a])throw new TypeError(`No method named "${a}"`);n[a]()}else o.interval&&o.ride&&(n.pause(),n.cycle())})}static _dataApiClickHandler(t){const n=_.getSelectorFromElement(this);if(!n)return;const o=e(n)[0];if(!o||!e(o).hasClass(H.CAROUSEL))return;const a={...e(o).data(),...e(this).data()},r=this.getAttribute("data-slide-to");r&&(a.interval=!1),Q._jQueryInterface.call(e(o),a),r&&e(o).data(v).to(r),t.preventDefault()}}e(document).on(w.CLICK_DATA_API,k.DATA_SLIDE,Q._dataApiClickHandler),e(window).on(w.LOAD_DATA_API,()=>{const t=[].slice.call(document.querySelectorAll(k.DATA_RIDE));for(let n=0,o=t.length;n<o;n++){const o=e(t[n]);Q._jQueryInterface.call(o,o.data())}}),e.fn.carousel=Q._jQueryInterface,e.fn.carousel.Constructor=Q,e.fn.carousel.noConflict=()=>(e.fn[y]=M,Q._jQueryInterface);const W="collapse",j="bs.collapse",x=`.${j}`,V=e.fn.collapse,Y={toggle:!0,parent:""},B={toggle:"boolean",parent:"(string|element)"},J={SHOW:`show${x}`,SHOWN:`shown${x}`,HIDE:`hide${x}`,HIDDEN:`hidden${x}`,CLICK_DATA_API:`click${x}${".data-api"}`},q={SHOW:"show",COLLAPSE:"collapse",COLLAPSING:"collapsing",COLLAPSED:"collapsed"},K={WIDTH:"width",HEIGHT:"height"},X={ACTIVES:".show, .collapsing",DATA_TOGGLE:"[data-toggle=\"collapse\"]"};class z{constructor(e,t){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(t),this._triggerArray=[].slice.call(document.querySelectorAll(`[data-toggle="collapse"][href="#${e.id}"],`+`[data-toggle="collapse"][data-target="#${e.id}"]`));const n=[].slice.call(document.querySelectorAll(X.DATA_TOGGLE));for(let o=0,a=n.length;o<a;o++){const t=n[o],a=_.getSelectorFromElement(t),r=[].slice.call(document.querySelectorAll(a)).filter(t=>t===e);null!==a&&0<r.length&&(this._selector=a,this._triggerArray.push(t))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}static get VERSION(){return"4.3.1"}static get Default(){return Y}toggle(){e(this._element).hasClass(q.SHOW)?this.hide():this.show()}show(){if(this._isTransitioning||e(this._element).hasClass(q.SHOW))return;let t,n;if(this._parent&&(t=[].slice.call(this._parent.querySelectorAll(X.ACTIVES)).filter(e=>"string"==typeof this._config.parent?e.getAttribute("data-parent")===this._config.parent:e.classList.contains(q.COLLAPSE)),0===t.length&&(t=null)),t&&(n=e(t).not(this._selector).data(j),n&&n._isTransitioning))return;const o=e.Event(J.SHOW);if(e(this._element).trigger(o),o.isDefaultPrevented())return;t&&(z._jQueryInterface.call(e(t).not(this._selector),"hide"),!n&&e(t).data(j,null));const a=this._getDimension();e(this._element).removeClass(q.COLLAPSE).addClass(q.COLLAPSING),this._element.style[a]=0,this._triggerArray.length&&e(this._triggerArray).removeClass(q.COLLAPSED).attr("aria-expanded",!0),this.setTransitioning(!0);const r=()=>{e(this._element).removeClass(q.COLLAPSING).addClass(q.COLLAPSE).addClass(q.SHOW),this._element.style[a]="",this.setTransitioning(!1),e(this._element).trigger(J.SHOWN)},i=a[0].toUpperCase()+a.slice(1),l=_.getTransitionDurationFromElement(this._element);e(this._element).one(_.TRANSITION_END,r).emulateTransitionEnd(l),this._element.style[a]=`${this._element[`scroll${i}`]}px`}hide(){if(!this._isTransitioning&&e(this._element).hasClass(q.SHOW)){const t=e.Event(J.HIDE);if(e(this._element).trigger(t),!t.isDefaultPrevented()){const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,_.reflow(this._element),e(this._element).addClass(q.COLLAPSING).removeClass(q.COLLAPSE).removeClass(q.SHOW);const n=this._triggerArray.length;if(0<n)for(let t=0;t<n;t++){const n=this._triggerArray[t],o=_.getSelectorFromElement(n);if(null!==o){const t=e([].slice.call(document.querySelectorAll(o)));t.hasClass(q.SHOW)||e(n).addClass(q.COLLAPSED).attr("aria-expanded",!1)}}this.setTransitioning(!0);const o=()=>{this.setTransitioning(!1),e(this._element).removeClass(q.COLLAPSING).addClass(q.COLLAPSE).trigger(J.HIDDEN)};this._element.style[t]="";const a=_.getTransitionDurationFromElement(this._element);e(this._element).one(_.TRANSITION_END,o).emulateTransitionEnd(a)}}}setTransitioning(e){this._isTransitioning=e}dispose(){e.removeData(this._element,j),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null}_getConfig(e){return e={...Y,...e},e.toggle=!!e.toggle,_.typeCheckConfig(W,e,B),e}_getDimension(){const t=e(this._element).hasClass(K.WIDTH);return t?K.WIDTH:K.HEIGHT}_getParent(){let t;_.isElement(this._config.parent)?(t=this._config.parent,"undefined"!=typeof this._config.parent.jquery&&(t=this._config.parent[0])):t=document.querySelector(this._config.parent);const n=`[data-toggle="collapse"][data-parent="${this._config.parent}"]`,o=[].slice.call(t.querySelectorAll(n));return e(o).each((e,t)=>{this._addAriaAndCollapsedClass(z._getTargetFromElement(t),[t])}),t}_addAriaAndCollapsedClass(t,n){const o=e(t).hasClass(q.SHOW);n.length&&e(n).toggleClass(q.COLLAPSED,!o).attr("aria-expanded",o)}static _getTargetFromElement(e){const t=_.getSelectorFromElement(e);return t?document.querySelector(t):null}static _jQueryInterface(t){return this.each(function(){const n=e(this);let o=n.data(j);const a={...Y,...n.data(),...("object"==typeof t&&t?t:{})};if(!o&&a.toggle&&/show|hide/.test(t)&&(a.toggle=!1),o||(o=new z(this,a),n.data(j,o)),"string"==typeof t){if("undefined"==typeof o[t])throw new TypeError(`No method named "${t}"`);o[t]()}})}}e(document).on(J.CLICK_DATA_API,X.DATA_TOGGLE,function(t){"A"===t.currentTarget.tagName&&t.preventDefault();const n=e(this),o=_.getSelectorFromElement(this),a=[].slice.call(document.querySelectorAll(o));e(a).each(function(){const t=e(this),o=t.data(j),a=o?"toggle":n.data();z._jQueryInterface.call(t,a)})}),e.fn.collapse=z._jQueryInterface,e.fn.collapse.Constructor=z,e.fn.collapse.noConflict=()=>(e.fn[W]=V,z._jQueryInterface);const Z="modal",ee="bs.modal",te=`.${ee}`,ne=e.fn.modal,oe={backdrop:!0,keyboard:!0,focus:!0,show:!0},ae={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},re={HIDE:`hide${te}`,HIDDEN:`hidden${te}`,SHOW:`show${te}`,SHOWN:`shown${te}`,FOCUSIN:`focusin${te}`,RESIZE:`resize${te}`,CLICK_DISMISS:`click.dismiss${te}`,KEYDOWN_DISMISS:`keydown.dismiss${te}`,MOUSEUP_DISMISS:`mouseup.dismiss${te}`,MOUSEDOWN_DISMISS:`mousedown.dismiss${te}`,CLICK_DATA_API:`click${te}${".data-api"}`},ie={SCROLLABLE:"modal-dialog-scrollable",SCROLLBAR_MEASURER:"modal-scrollbar-measure",BACKDROP:"modal-backdrop",OPEN:"modal-open",FADE:"fade",SHOW:"show"},le={DIALOG:".modal-dialog",MODAL_BODY:".modal-body",DATA_TOGGLE:"[data-toggle=\"modal\"]",DATA_DISMISS:"[data-dismiss=\"modal\"]",FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top"};class de{constructor(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(le.DIALOG),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}static get VERSION(){return"4.3.1"}static get Default(){return oe}toggle(e){return this._isShown?this.hide():this.show(e)}show(t){if(!(this._isShown||this._isTransitioning)){e(this._element).hasClass(ie.FADE)&&(this._isTransitioning=!0);const n=e.Event(re.SHOW,{relatedTarget:t});e(this._element).trigger(n),this._isShown||n.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),e(this._element).on(re.CLICK_DISMISS,le.DATA_DISMISS,e=>this.hide(e)),e(this._dialog).on(re.MOUSEDOWN_DISMISS,()=>{e(this._element).one(re.MOUSEUP_DISMISS,t=>{e(t.target).is(this._element)&&(this._ignoreBackdropClick=!0)})}),this._showBackdrop(()=>this._showElement(t)))}}hide(t){if(t&&t.preventDefault(),this._isShown&&!this._isTransitioning){const t=e.Event(re.HIDE);if(e(this._element).trigger(t),this._isShown&&!t.isDefaultPrevented()){this._isShown=!1;const t=e(this._element).hasClass(ie.FADE);if(t&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),e(document).off(re.FOCUSIN),e(this._element).removeClass(ie.SHOW),e(this._element).off(re.CLICK_DISMISS),e(this._dialog).off(re.MOUSEDOWN_DISMISS),t){const t=_.getTransitionDurationFromElement(this._element);e(this._element).one(_.TRANSITION_END,e=>this._hideModal(e)).emulateTransitionEnd(t)}else this._hideModal()}}}dispose(){[window,this._element,this._dialog].forEach(t=>e(t).off(te)),e(document).off(re.FOCUSIN),e.removeData(this._element,ee),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null}handleUpdate(){this._adjustDialog()}_getConfig(e){return e={...oe,...e},_.typeCheckConfig(Z,e,ae),e}_showElement(t){const n=e(this._element).hasClass(ie.FADE);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),e(this._dialog).hasClass(ie.SCROLLABLE)?this._dialog.querySelector(le.MODAL_BODY).scrollTop=0:this._element.scrollTop=0,n&&_.reflow(this._element),e(this._element).addClass(ie.SHOW),this._config.focus&&this._enforceFocus();const o=e.Event(re.SHOWN,{relatedTarget:t}),a=()=>{this._config.focus&&this._element.focus(),this._isTransitioning=!1,e(this._element).trigger(o)};if(n){const t=_.getTransitionDurationFromElement(this._dialog);e(this._dialog).one(_.TRANSITION_END,a).emulateTransitionEnd(t)}else a()}_enforceFocus(){e(document).off(re.FOCUSIN).on(re.FOCUSIN,t=>{document!==t.target&&this._element!==t.target&&0===e(this._element).has(t.target).length&&this._element.focus()})}_setEscapeEvent(){this._isShown&&this._config.keyboard?e(this._element).on(re.KEYDOWN_DISMISS,e=>{e.which===27&&(e.preventDefault(),this.hide())}):!this._isShown&&e(this._element).off(re.KEYDOWN_DISMISS)}_setResizeEvent(){this._isShown?e(window).on(re.RESIZE,e=>this.handleUpdate(e)):e(window).off(re.RESIZE)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._isTransitioning=!1,this._showBackdrop(()=>{e(document.body).removeClass(ie.OPEN),this._resetAdjustments(),this._resetScrollbar(),e(this._element).trigger(re.HIDDEN)})}_removeBackdrop(){this._backdrop&&(e(this._backdrop).remove(),this._backdrop=null)}_showBackdrop(t){const n=e(this._element).hasClass(ie.FADE)?ie.FADE:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=ie.BACKDROP,n&&this._backdrop.classList.add(n),e(this._backdrop).appendTo(document.body),e(this._element).on(re.CLICK_DISMISS,e=>this._ignoreBackdropClick?void(this._ignoreBackdropClick=!1):void(e.target!==e.currentTarget||("static"===this._config.backdrop?this._element.focus():this.hide()))),n&&_.reflow(this._backdrop),e(this._backdrop).addClass(ie.SHOW),!t)return;if(!n)return void t();const o=_.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(_.TRANSITION_END,t).emulateTransitionEnd(o)}else if(!this._isShown&&this._backdrop){e(this._backdrop).removeClass(ie.SHOW);const n=()=>{this._removeBackdrop(),t&&t()};if(e(this._element).hasClass(ie.FADE)){const t=_.getTransitionDurationFromElement(this._backdrop);e(this._backdrop).one(_.TRANSITION_END,n).emulateTransitionEnd(t)}else n()}else t&&t()}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=`${this._scrollbarWidth}px`),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=`${this._scrollbarWidth}px`)}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}_checkScrollbar(){const e=document.body.getBoundingClientRect();this._isBodyOverflowing=e.left+e.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()}_setScrollbar(){if(this._isBodyOverflowing){const t=[].slice.call(document.querySelectorAll(le.FIXED_CONTENT)),n=[].slice.call(document.querySelectorAll(le.STICKY_CONTENT));e(t).each((t,n)=>{const o=n.style.paddingRight,a=e(n).css("padding-right");e(n).data("padding-right",o).css("padding-right",`${parseFloat(a)+this._scrollbarWidth}px`)}),e(n).each((t,n)=>{const o=n.style.marginRight,a=e(n).css("margin-right");e(n).data("margin-right",o).css("margin-right",`${parseFloat(a)-this._scrollbarWidth}px`)});const o=document.body.style.paddingRight,a=e(document.body).css("padding-right");e(document.body).data("padding-right",o).css("padding-right",`${parseFloat(a)+this._scrollbarWidth}px`)}e(document.body).addClass(ie.OPEN)}_resetScrollbar(){const t=[].slice.call(document.querySelectorAll(le.FIXED_CONTENT));e(t).each((t,n)=>{const o=e(n).data("padding-right");e(n).removeData("padding-right"),n.style.paddingRight=o?o:""});const n=[].slice.call(document.querySelectorAll(`${le.STICKY_CONTENT}`));e(n).each((t,n)=>{const o=e(n).data("margin-right");"undefined"!=typeof o&&e(n).css("margin-right",o).removeData("margin-right")});const o=e(document.body).data("padding-right");e(document.body).removeData("padding-right"),document.body.style.paddingRight=o?o:""}_getScrollbarWidth(){const e=document.createElement("div");e.className=ie.SCROLLBAR_MEASURER,document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t}static _jQueryInterface(t,n){return this.each(function(){let o=e(this).data(ee);const a={...oe,...e(this).data(),...("object"==typeof t&&t?t:{})};if(o||(o=new de(this,a),e(this).data(ee,o)),"string"==typeof t){if("undefined"==typeof o[t])throw new TypeError(`No method named "${t}"`);o[t](n)}else a.show&&o.show(n)})}}e(document).on(re.CLICK_DATA_API,le.DATA_TOGGLE,function(t){let n;const o=_.getSelectorFromElement(this);o&&(n=document.querySelector(o));const a=e(n).data(ee)?"toggle":{...e(n).data(),...e(this).data()};("A"===this.tagName||"AREA"===this.tagName)&&t.preventDefault();const r=e(n).one(re.SHOW,t=>{t.isDefaultPrevented()||r.one(re.HIDDEN,()=>{e(this).is(":visible")&&this.focus()})});de._jQueryInterface.call(e(n),a,this)}),e.fn.modal=de._jQueryInterface,e.fn.modal.Constructor=de,e.fn.modal.noConflict=()=>(e.fn[Z]=ne,de._jQueryInterface);const se=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],ce=/^aria-[\w-]*$/i,fe=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,ue=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i,_e="tooltip",me="bs.tooltip",pe=`.${me}`,ge=e.fn[_e],Ee="bs-tooltip",he=new RegExp(`(^|\\s)${Ee}\\S+`,"g"),Ae=["sanitize","whiteList","sanitizeFn"],Ce={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object"},Te={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Ie={animation:!0,template:"<div class=\"tooltip\" role=\"tooltip\"><div class=\"arrow\"></div><div class=\"tooltip-inner\"></div></div>",trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:{"*":["class","dir","id","lang","role",ce],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]}},be={SHOW:"show",OUT:"out"},Oe={HIDE:`hide${pe}`,HIDDEN:`hidden${pe}`,SHOW:`show${pe}`,SHOWN:`shown${pe}`,INSERTED:`inserted${pe}`,CLICK:`click${pe}`,FOCUSIN:`focusin${pe}`,FOCUSOUT:`focusout${pe}`,MOUSEENTER:`mouseenter${pe}`,MOUSELEAVE:`mouseleave${pe}`},Se={FADE:"fade",SHOW:"show"},Ne={TOOLTIP:".tooltip",TOOLTIP_INNER:".tooltip-inner",ARROW:".arrow"},De={HOVER:"hover",FOCUS:"focus",CLICK:"click",MANUAL:"manual"};class ye{constructor(e,n){if("undefined"==typeof t)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(n),this.tip=null,this._setListeners()}static get VERSION(){return"4.3.1"}static get Default(){return Ie}static get NAME(){return _e}static get DATA_KEY(){return me}static get Event(){return Oe}static get EVENT_KEY(){return pe}static get DefaultType(){return Ce}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(t){if(this._isEnabled)if(t){const n=this.constructor.DATA_KEY;let o=e(t.currentTarget).data(n);o||(o=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(n,o)),o._activeTrigger.click=!o._activeTrigger.click,o._isWithActiveTrigger()?o._enter(null,o):o._leave(null,o)}else{if(e(this.getTipElement()).hasClass(Se.SHOW))return void this._leave(null,this);this._enter(null,this)}}dispose(){clearTimeout(this._timeout),e.removeData(this.element,this.constructor.DATA_KEY),e(this.element).off(this.constructor.EVENT_KEY),e(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&e(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,null!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null}show(){if("none"===e(this.element).css("display"))throw new Error("Please use show on visible elements");const n=e.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){e(this.element).trigger(n);const o=_.findShadowRoot(this.element),a=e.contains(null===o?this.element.ownerDocument.documentElement:o,this.element);if(n.isDefaultPrevented()||!a)return;const r=this.getTipElement(),i=_.getUID(this.constructor.NAME);r.setAttribute("id",i),this.element.setAttribute("aria-describedby",i),this.setContent(),this.config.animation&&e(r).addClass(Se.FADE);const l="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,d=this._getAttachment(l);this.addAttachmentClass(d);const s=this._getContainer();e(r).data(this.constructor.DATA_KEY,this),e.contains(this.element.ownerDocument.documentElement,this.tip)||e(r).appendTo(s),e(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new t(this.element,r,{placement:d,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:Ne.ARROW},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:e=>{e.originalPlacement!==e.placement&&this._handlePopperPlacementChange(e)},onUpdate:e=>this._handlePopperPlacementChange(e)}),e(r).addClass(Se.SHOW),"ontouchstart"in document.documentElement&&e(document.body).children().on("mouseover",null,e.noop);const c=()=>{this.config.animation&&this._fixTransition();const t=this._hoverState;this._hoverState=null,e(this.element).trigger(this.constructor.Event.SHOWN),t===be.OUT&&this._leave(null,this)};if(e(this.tip).hasClass(Se.FADE)){const t=_.getTransitionDurationFromElement(this.tip);e(this.tip).one(_.TRANSITION_END,c).emulateTransitionEnd(t)}else c()}}hide(t){const n=this.getTipElement(),o=e.Event(this.constructor.Event.HIDE),a=()=>{this._hoverState!==be.SHOW&&n.parentNode&&n.parentNode.removeChild(n),this._cleanTipClass(),this.element.removeAttribute("aria-describedby"),e(this.element).trigger(this.constructor.Event.HIDDEN),null!==this._popper&&this._popper.destroy(),t&&t()};if(e(this.element).trigger(o),!o.isDefaultPrevented()){if(e(n).removeClass(Se.SHOW),"ontouchstart"in document.documentElement&&e(document.body).children().off("mouseover",null,e.noop),this._activeTrigger[De.CLICK]=!1,this._activeTrigger[De.FOCUS]=!1,this._activeTrigger[De.HOVER]=!1,e(this.tip).hasClass(Se.FADE)){const t=_.getTransitionDurationFromElement(n);e(n).one(_.TRANSITION_END,a).emulateTransitionEnd(t)}else a();this._hoverState=""}}update(){null!==this._popper&&this._popper.scheduleUpdate()}isWithContent(){return!!this.getTitle()}addAttachmentClass(t){e(this.getTipElement()).addClass(`${Ee}-${t}`)}getTipElement(){return this.tip=this.tip||e(this.config.template)[0],this.tip}setContent(){const t=this.getTipElement();this.setElementContent(e(t.querySelectorAll(Ne.TOOLTIP_INNER)),this.getTitle()),e(t).removeClass(`${Se.FADE} ${Se.SHOW}`)}setElementContent(t,n){return"object"==typeof n&&(n.nodeType||n.jquery)?void(this.config.html?!e(n).parent().is(t)&&t.empty().append(n):t.text(e(n).text())):void(this.config.html?(this.config.sanitize&&(n=l(n,this.config.whiteList,this.config.sanitizeFn)),t.html(n)):t.text(n))}getTitle(){let e=this.element.getAttribute("data-original-title");return e||(e="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),e}_getOffset(){const e={};return"function"==typeof this.config.offset?e.fn=e=>(e.offsets={...e.offsets,...(this.config.offset(e.offsets,this.element)||{})},e):e.offset=this.config.offset,e}_getContainer(){return!1===this.config.container?document.body:_.isElement(this.config.container)?e(this.config.container):e(document).find(this.config.container)}_getAttachment(e){return Te[e.toUpperCase()]}_setListeners(){const t=this.config.trigger.split(" ");t.forEach(t=>{if("click"===t)e(this.element).on(this.constructor.Event.CLICK,this.config.selector,e=>this.toggle(e));else if(t!==De.MANUAL){const n=t===De.HOVER?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,o=t===De.HOVER?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT;e(this.element).on(n,this.config.selector,e=>this._enter(e)).on(o,this.config.selector,e=>this._leave(e))}}),e(this.element).closest(".modal").on("hide.bs.modal",()=>{this.element&&this.hide()}),this.config.selector?this.config={...this.config,trigger:"manual",selector:""}:this._fixTitle()}_fixTitle(){const e=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!=e)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))}_enter(t,n){const o=this.constructor.DATA_KEY;return(n=n||e(t.currentTarget).data(o),n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(o,n)),t&&(n._activeTrigger["focusin"===t.type?De.FOCUS:De.HOVER]=!0),e(n.getTipElement()).hasClass(Se.SHOW)||n._hoverState===be.SHOW)?void(n._hoverState=be.SHOW):(clearTimeout(n._timeout),n._hoverState=be.SHOW,n.config.delay&&n.config.delay.show?void(n._timeout=setTimeout(()=>{n._hoverState===be.SHOW&&n.show()},n.config.delay.show)):void n.show())}_leave(t,n){const o=this.constructor.DATA_KEY;if(n=n||e(t.currentTarget).data(o),n||(n=new this.constructor(t.currentTarget,this._getDelegateConfig()),e(t.currentTarget).data(o,n)),t&&(n._activeTrigger["focusout"===t.type?De.FOCUS:De.HOVER]=!1),!n._isWithActiveTrigger())return clearTimeout(n._timeout),n._hoverState=be.OUT,n.config.delay&&n.config.delay.hide?void(n._timeout=setTimeout(()=>{n._hoverState===be.OUT&&n.hide()},n.config.delay.hide)):void n.hide()}_isWithActiveTrigger(){for(const e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1}_getConfig(t){const n=e(this.element).data();return Object.keys(n).forEach(e=>{-1!==Ae.indexOf(e)&&delete n[e]}),t={...this.constructor.Default,...n,...("object"==typeof t&&t?t:{})},"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),_.typeCheckConfig(_e,t,this.constructor.DefaultType),t.sanitize&&(t.template=l(t.template,t.whiteList,t.sanitizeFn)),t}_getDelegateConfig(){const e={};if(this.config)for(const t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e}_cleanTipClass(){const t=e(this.getTipElement()),n=t.attr("class").match(he);null!==n&&n.length&&t.removeClass(n.join(""))}_handlePopperPlacementChange(e){const t=e.instance;this.tip=t.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))}_fixTransition(){const t=this.getTipElement(),n=this.config.animation;null!==t.getAttribute("x-placement")||(e(t).removeClass(Se.FADE),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)}static _jQueryInterface(t){return this.each(function(){let n=e(this).data(me);if((n||!/dispose|hide/.test(t))&&(n||(n=new ye(this,"object"==typeof t&&t),e(this).data(me,n)),"string"==typeof t)){if("undefined"==typeof n[t])throw new TypeError(`No method named "${t}"`);n[t]()}})}}e.fn[_e]=ye._jQueryInterface,e.fn[_e].Constructor=ye,e.fn[_e].noConflict=()=>(e.fn[_e]=ge,ye._jQueryInterface);const ve="popover",Re="bs.popover",Le=`.${Re}`,Me=e.fn.popover,Ue="bs-popover",Pe=new RegExp(`(^|\\s)${Ue}\\S+`,"g"),Fe={...ye.Default,placement:"right",trigger:"click",content:"",template:"<div class=\"popover\" role=\"tooltip\"><div class=\"arrow\"></div><h3 class=\"popover-header\"></h3><div class=\"popover-body\"></div></div>"},we={...ye.DefaultType,content:"(string|element|function)"},$e={FADE:"fade",SHOW:"show"},He={TITLE:".popover-header",CONTENT:".popover-body"},ke={HIDE:`hide${Le}`,HIDDEN:`hidden${Le}`,SHOW:`show${Le}`,SHOWN:`shown${Le}`,INSERTED:`inserted${Le}`,CLICK:`click${Le}`,FOCUSIN:`focusin${Le}`,FOCUSOUT:`focusout${Le}`,MOUSEENTER:`mouseenter${Le}`,MOUSELEAVE:`mouseleave${Le}`};class Ge extends ye{static get VERSION(){return"4.3.1"}static get Default(){return Fe}static get NAME(){return ve}static get DATA_KEY(){return Re}static get Event(){return ke}static get EVENT_KEY(){return Le}static get DefaultType(){return we}isWithContent(){return this.getTitle()||this._getContent()}addAttachmentClass(t){e(this.getTipElement()).addClass(`${Ue}-${t}`)}getTipElement(){return this.tip=this.tip||e(this.config.template)[0],this.tip}setContent(){const t=e(this.getTipElement());this.setElementContent(t.find(He.TITLE),this.getTitle());let n=this._getContent();"function"==typeof n&&(n=n.call(this.element)),this.setElementContent(t.find(He.CONTENT),n),t.removeClass(`${$e.FADE} ${$e.SHOW}`)}_getContent(){return this.element.getAttribute("data-content")||this.config.content}_cleanTipClass(){const t=e(this.getTipElement()),n=t.attr("class").match(Pe);null!==n&&0<n.length&&t.removeClass(n.join(""))}static _jQueryInterface(t){return this.each(function(){let n=e(this).data(Re);const o="object"==typeof t?t:null;if((n||!/dispose|hide/.test(t))&&(n||(n=new Ge(this,o),e(this).data(Re,n)),"string"==typeof t)){if("undefined"==typeof n[t])throw new TypeError(`No method named "${t}"`);n[t]()}})}}e.fn.popover=Ge._jQueryInterface,e.fn.popover.Constructor=Ge,e.fn.popover.noConflict=()=>(e.fn[ve]=Me,Ge._jQueryInterface);const Qe="scrollspy",We="bs.scrollspy",je=`.${We}`,xe=e.fn.scrollspy,Ve={offset:10,method:"auto",target:""},Ye={offset:"number",method:"string",target:"(string|element)"},Be={ACTIVATE:`activate${je}`,SCROLL:`scroll${je}`,LOAD_DATA_API:`load${je}${".data-api"}`},Je={DROPDOWN_ITEM:"dropdown-item",DROPDOWN_MENU:"dropdown-menu",ACTIVE:"active"},qe={DATA_SPY:"[data-spy=\"scroll\"]",ACTIVE:".active",NAV_LIST_GROUP:".nav, .list-group",NAV_LINKS:".nav-link",NAV_ITEMS:".nav-item",LIST_ITEMS:".list-group-item",DROPDOWN:".dropdown",DROPDOWN_ITEMS:".dropdown-item",DROPDOWN_TOGGLE:".dropdown-toggle"},Ke={OFFSET:"offset",POSITION:"position"};class Xe{constructor(t,n){this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(n),this._selector=`${this._config.target} ${qe.NAV_LINKS},`+`${this._config.target} ${qe.LIST_ITEMS},`+`${this._config.target} ${qe.DROPDOWN_ITEMS}`,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,e(this._scrollElement).on(Be.SCROLL,e=>this._process(e)),this.refresh(),this._process()}static get VERSION(){return"4.3.1"}static get Default(){return Ve}refresh(){const t=this._scrollElement===this._scrollElement.window?Ke.OFFSET:Ke.POSITION,n="auto"===this._config.method?t:this._config.method,o=n===Ke.POSITION?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight();const a=[].slice.call(document.querySelectorAll(this._selector));a.map(t=>{let a;const r=_.getSelectorFromElement(t);if(r&&(a=document.querySelector(r)),a){const t=a.getBoundingClientRect();if(t.width||t.height)return[e(a)[n]().top+o,r]}return null}).filter(e=>e).sort((e,t)=>e[0]-t[0]).forEach(e=>{this._offsets.push(e[0]),this._targets.push(e[1])})}dispose(){e.removeData(this._element,We),e(this._scrollElement).off(je),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null}_getConfig(t){if(t={...Ve,...("object"==typeof t&&t?t:{})},"string"!=typeof t.target){let n=e(t.target).attr("id");n||(n=_.getUID(Qe),e(t.target).attr("id",n)),t.target=`#${n}`}return _.typeCheckConfig(Qe,t,Ye),t}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||f(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){const e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),e>=n){const e=this._targets[this._targets.length-1];return void(this._activeTarget!==e&&this._activate(e))}if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();const o=this._offsets.length;for(let t=o;t--;){const n=this._activeTarget!==this._targets[t]&&e>=this._offsets[t]&&("undefined"==typeof this._offsets[t+1]||e<this._offsets[t+1]);n&&this._activate(this._targets[t])}}_activate(t){this._activeTarget=t,this._clear();const n=this._selector.split(",").map(e=>`${e}[data-target="${t}"],${e}[href="${t}"]`),o=e([].slice.call(document.querySelectorAll(n.join(","))));o.hasClass(Je.DROPDOWN_ITEM)?(o.closest(qe.DROPDOWN).find(qe.DROPDOWN_TOGGLE).addClass(Je.ACTIVE),o.addClass(Je.ACTIVE)):(o.addClass(Je.ACTIVE),o.parents(qe.NAV_LIST_GROUP).prev(`${qe.NAV_LINKS}, ${qe.LIST_ITEMS}`).addClass(Je.ACTIVE),o.parents(qe.NAV_LIST_GROUP).prev(qe.NAV_ITEMS).children(qe.NAV_LINKS).addClass(Je.ACTIVE)),e(this._scrollElement).trigger(Be.ACTIVATE,{relatedTarget:t})}_clear(){[].slice.call(document.querySelectorAll(this._selector)).filter(e=>e.classList.contains(Je.ACTIVE)).forEach(e=>e.classList.remove(Je.ACTIVE))}static _jQueryInterface(t){return this.each(function(){let n=e(this).data(We);if(n||(n=new Xe(this,"object"==typeof t&&t),e(this).data(We,n)),"string"==typeof t){if("undefined"==typeof n[t])throw new TypeError(`No method named "${t}"`);n[t]()}})}}e(window).on(Be.LOAD_DATA_API,()=>{const t=[].slice.call(document.querySelectorAll(qe.DATA_SPY)),n=t.length;for(let o=n;o--;){const n=e(t[o]);Xe._jQueryInterface.call(n,n.data())}}),e.fn.scrollspy=Xe._jQueryInterface,e.fn.scrollspy.Constructor=Xe,e.fn.scrollspy.noConflict=()=>(e.fn[Qe]=xe,Xe._jQueryInterface);const ze="bs.tab",Ze=`.${ze}`,et=e.fn.tab,tt={HIDE:`hide${Ze}`,HIDDEN:`hidden${Ze}`,SHOW:`show${Ze}`,SHOWN:`shown${Ze}`,CLICK_DATA_API:`click${Ze}${".data-api"}`},nt={DROPDOWN_MENU:"dropdown-menu",ACTIVE:"active",DISABLED:"disabled",FADE:"fade",SHOW:"show"},ot={DROPDOWN:".dropdown",NAV_LIST_GROUP:".nav, .list-group",ACTIVE:".active",ACTIVE_UL:"> li > .active",DATA_TOGGLE:"[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]",DROPDOWN_TOGGLE:".dropdown-toggle",DROPDOWN_ACTIVE_CHILD:"> .dropdown-menu .active"};class at{constructor(e){this._element=e}static get VERSION(){return"4.3.1"}show(){if(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&e(this._element).hasClass(nt.ACTIVE)||e(this._element).hasClass(nt.DISABLED))return;let t,n;const o=e(this._element).closest(ot.NAV_LIST_GROUP)[0],a=_.getSelectorFromElement(this._element);if(o){const t="UL"===o.nodeName||"OL"===o.nodeName?ot.ACTIVE_UL:ot.ACTIVE;n=e.makeArray(e(o).find(t)),n=n[n.length-1]}const r=e.Event(tt.HIDE,{relatedTarget:this._element}),i=e.Event(tt.SHOW,{relatedTarget:n});if(n&&e(n).trigger(r),e(this._element).trigger(i),!(i.isDefaultPrevented()||r.isDefaultPrevented())){a&&(t=document.querySelector(a)),this._activate(this._element,o);const r=()=>{const t=e.Event(tt.HIDDEN,{relatedTarget:this._element}),o=e.Event(tt.SHOWN,{relatedTarget:n});e(n).trigger(t),e(this._element).trigger(o)};t?this._activate(t,t.parentNode,r):r()}}dispose(){e.removeData(this._element,ze),this._element=null}_activate(t,n,o){const a=n&&("UL"===n.nodeName||"OL"===n.nodeName)?e(n).find(ot.ACTIVE_UL):e(n).children(ot.ACTIVE),r=a[0],i=o&&r&&e(r).hasClass(nt.FADE),l=()=>this._transitionComplete(t,r,o);if(r&&i){const t=_.getTransitionDurationFromElement(r);e(r).removeClass(nt.SHOW).one(_.TRANSITION_END,l).emulateTransitionEnd(t)}else l()}_transitionComplete(t,n,o){if(n){e(n).removeClass(nt.ACTIVE);const t=e(n.parentNode).find(ot.DROPDOWN_ACTIVE_CHILD)[0];t&&e(t).removeClass(nt.ACTIVE),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!1)}if(e(t).addClass(nt.ACTIVE),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),_.reflow(t),t.classList.contains(nt.FADE)&&t.classList.add(nt.SHOW),t.parentNode&&e(t.parentNode).hasClass(nt.DROPDOWN_MENU)){const n=e(t).closest(ot.DROPDOWN)[0];if(n){const t=[].slice.call(n.querySelectorAll(ot.DROPDOWN_TOGGLE));e(t).addClass(nt.ACTIVE)}t.setAttribute("aria-expanded",!0)}o&&o()}static _jQueryInterface(t){return this.each(function(){const n=e(this);let o=n.data(ze);if(o||(o=new at(this),n.data(ze,o)),"string"==typeof t){if("undefined"==typeof o[t])throw new TypeError(`No method named "${t}"`);o[t]()}})}}e(document).on(tt.CLICK_DATA_API,ot.DATA_TOGGLE,function(t){t.preventDefault(),at._jQueryInterface.call(e(this),"show")}),e.fn.tab=at._jQueryInterface,e.fn.tab.Constructor=at,e.fn.tab.noConflict=()=>(e.fn["tab"]=et,at._jQueryInterface);var rt=function(){function e(){if(window.QUnit)return!1;var e=document.createElement("bmd");for(var t in o)if(void 0!==e.style[t])return o[t];return!1}var t=!1,n="",o={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};return function(){for(var a in t=e(),o)n+=" "+o[a]}(),{transitionEndSupported:function(){return t},transitionEndSelector:function(){return n},isChar:function(e){return!("undefined"!=typeof e.which)||!!("number"==typeof e.which&&0<e.which)&&!e.ctrlKey&&!e.metaKey&&!e.altKey&&8!==e.which&&9!==e.which&&13!==e.which&&16!==e.which&&17!==e.which&&20!==e.which&&27!==e.which},assert:function(e,t,n){if(t)throw void 0===!e&&e.css("border","1px solid red"),console.error(n,e),n},describe:function(e){return void 0===e?"undefined":0===e.length?"(no matching elements)":e[0].outerHTML.split(">")[0]+">"}}}(jQuery),it=function(e){var t={BMD_FORM_GROUP:"bmd-form-group",IS_FILLED:"is-filled",IS_FOCUSED:"is-focused"},n={BMD_FORM_GROUP:"."+t.BMD_FORM_GROUP},o={},a=function(){function a(t,n,a){for(var r in void 0===a&&(a={}),this.$element=t,this.config=e.extend(!0,{},o,n),a)this[r]=a[r]}var r=a.prototype;return r.dispose=function(e){this.$element.data(e,null),this.$element=null,this.config=null},r.addFormGroupFocus=function(){this.$element.prop("disabled")||this.$bmdFormGroup.addClass(t.IS_FOCUSED)},r.removeFormGroupFocus=function(){this.$bmdFormGroup.removeClass(t.IS_FOCUSED)},r.removeIsFilled=function(){this.$bmdFormGroup.removeClass(t.IS_FILLED)},r.addIsFilled=function(){this.$bmdFormGroup.addClass(t.IS_FILLED)},r.findMdbFormGroup=function(t){void 0===t&&(t=!0);var o=this.$element.closest(n.BMD_FORM_GROUP);return 0===o.length&&t&&e.error("Failed to find "+n.BMD_FORM_GROUP+" for "+rt.describe(this.$element)),o},a}();return a}(jQuery),lt=function(e){var t={FORM_GROUP:"form-group",BMD_FORM_GROUP:"bmd-form-group",BMD_LABEL:"bmd-label",BMD_LABEL_STATIC:"bmd-label-static",BMD_LABEL_PLACEHOLDER:"bmd-label-placeholder",BMD_LABEL_FLOATING:"bmd-label-floating",HAS_DANGER:"has-danger",IS_FILLED:"is-filled",IS_FOCUSED:"is-focused",INPUT_GROUP:"input-group"},n={FORM_GROUP:"."+t.FORM_GROUP,BMD_FORM_GROUP:"."+t.BMD_FORM_GROUP,BMD_LABEL_WILDCARD:"label[class^='"+t.BMD_LABEL+"'], label[class*=' "+t.BMD_LABEL+"']"},o={validate:!1,formGroup:{required:!1},bmdFormGroup:{template:"<span class='"+t.BMD_FORM_GROUP+"'></span>",create:!0,required:!0},label:{required:!1,selectors:[".form-control-label","> label"],className:t.BMD_LABEL_STATIC},requiredClasses:[],invalidComponentMatches:[],convertInputSizeVariations:!0},a={"form-control-lg":"bmd-form-group-lg","form-control-sm":"bmd-form-group-sm"},r=function(r){function i(t,n,a){var i;return void 0===a&&(a={}),i=r.call(this,t,e.extend(!0,{},o,n),a)||this,i._rejectInvalidComponentMatches(),i.rejectWithoutRequiredStructure(),i._rejectWithoutRequiredClasses(),i.$formGroup=i.findFormGroup(i.config.formGroup.required),i.$bmdFormGroup=i.resolveMdbFormGroup(),i.$bmdLabel=i.resolveMdbLabel(),i.resolveMdbFormGroupSizing(),i.addFocusListener(),i.addChangeListener(),""!=i.$element.val()&&i.addIsFilled(),i}c(i,r);var l=i.prototype;return l.dispose=function(e){r.prototype.dispose.call(this,e),this.$bmdFormGroup=null,this.$formGroup=null},l.rejectWithoutRequiredStructure=function(){},l.addFocusListener=function(){var e=this;this.$element.on("focus",function(){e.addFormGroupFocus()}).on("blur",function(){e.removeFormGroupFocus()})},l.addChangeListener=function(){var e=this;this.$element.on("keydown paste",function(t){rt.isChar(t)&&e.addIsFilled()}).on("keyup change",function(){if(e.isEmpty()?e.removeIsFilled():e.addIsFilled(),e.config.validate){var t="undefined"==typeof e.$element[0].checkValidity||e.$element[0].checkValidity();t?e.removeHasDanger():e.addHasDanger()}})},l.addHasDanger=function(){this.$bmdFormGroup.addClass(t.HAS_DANGER)},l.removeHasDanger=function(){this.$bmdFormGroup.removeClass(t.HAS_DANGER)},l.isEmpty=function(){return null===this.$element.val()||void 0===this.$element.val()||""===this.$element.val()},l.resolveMdbFormGroup=function(){var e=this.findMdbFormGroup(!1);return(void 0===e||0===e.length)&&(this.config.bmdFormGroup.create&&(void 0===this.$formGroup||0===this.$formGroup.length)?this.outerElement().parent().hasClass(t.INPUT_GROUP)?this.outerElement().parent().wrap(this.config.bmdFormGroup.template):this.outerElement().wrap(this.config.bmdFormGroup.template):this.$formGroup.addClass(t.BMD_FORM_GROUP),e=this.findMdbFormGroup(this.config.bmdFormGroup.required)),e},l.outerElement=function(){return this.$element},l.resolveMdbLabel=function(){var e=this.$bmdFormGroup.find(n.BMD_LABEL_WILDCARD);if(void 0===e||0===e.length)if(e=this.findMdbLabel(this.config.label.required),void 0===e||0===e.length);else e.addClass(this.config.label.className);return e},l.findMdbLabel=function(t){void 0===t&&(t=!0);for(var o=null,a=this.config.label.selectors,r=Array.isArray(a),i=0,a=r?a:a[Symbol.iterator]();;){var l;if(r){if(i>=a.length)break;l=a[i++]}else{if(i=a.next(),i.done)break;l=i.value}var d=l;if(o=e.isFunction(d)?d(this):this.$bmdFormGroup.find(d),void 0!==o&&0<o.length)break}return 0===o.length&&t&&e.error("Failed to find "+n.BMD_LABEL_WILDCARD+" within form-group for "+rt.describe(this.$element)),o},l.findFormGroup=function(t){void 0===t&&(t=!0);var o=this.$element.closest(n.FORM_GROUP);return 0===o.length&&t&&e.error("Failed to find "+n.FORM_GROUP+" for "+rt.describe(this.$element)),o},l.resolveMdbFormGroupSizing=function(){if(this.config.convertInputSizeVariations)for(var e in a)this.$element.hasClass(e)&&this.$bmdFormGroup.addClass(a[e])},l._rejectInvalidComponentMatches=function(){for(var e=this.config.invalidComponentMatches,t=Array.isArray(e),n=0,e=t?e:e[Symbol.iterator]();;){var o;if(t){if(n>=e.length)break;o=e[n++]}else{if(n=e.next(),n.done)break;o=n.value}var a=o;a.rejectMatch(this.constructor.name,this.$element)}},l._rejectWithoutRequiredClasses=function(){for(var e=this.config.requiredClasses,t=Array.isArray(e),n=0,e=t?e:e[Symbol.iterator]();;){var o;if(t){if(n>=e.length)break;o=e[n++]}else{if(n=e.next(),n.done)break;o=n.value}var a=o;if(-1!==a.indexOf("||"))for(var r=a.split("||"),i=r,l=Array.isArray(i),d=0,i=l?i:i[Symbol.iterator]();;){var s;if(l){if(d>=i.length)break;s=i[d++]}else{if(d=i.next(),d.done)break;s=d.value}var c=s;if(this.$element.hasClass(c))break}else if(this.$element.hasClass(a));}},i}(it);return r}(jQuery),dt=function(e){var t={label:{required:!1}},n={LABEL:"label"},o=function(o){function a(n,a,r){var i;return i=o.call(this,n,e.extend(!0,{},t,a),r)||this,i.decorateMarkup(),i}c(a,o);var r=a.prototype;return r.decorateMarkup=function(){var t=e(this.config.template);this.$element.after(t),!1!==this.config.ripples&&t.bmdRipples()},r.outerElement=function(){return this.$element.parent().closest("."+this.outerClass)},r.rejectWithoutRequiredStructure=function(){rt.assert(this.$element,"label"===!this.$element.parent().prop("tagName"),this.constructor.name+"'s "+rt.describe(this.$element)+" parent element should be <label>."),rt.assert(this.$element,!this.outerElement().hasClass(this.outerClass),this.constructor.name+"'s "+rt.describe(this.$element)+" outer element should have class "+this.outerClass+".")},r.addFocusListener=function(){var e=this;this.$element.closest(n.LABEL).hover(function(){e.addFormGroupFocus()},function(){e.removeFormGroupFocus()})},r.addChangeListener=function(){var e=this;this.$element.change(function(){e.$element.blur()})},a}(lt);return o}(jQuery),st=function(e){var t="bmd.checkbox",n="bmd"+("c".toUpperCase()+"checkbox".slice(1)),o=e.fn[n],a={template:"<span class='checkbox-decorator'><span class='check'></span></span>"},r=function(n){function o(t,o,r){return void 0===r&&(r={inputType:"checkbox",outerClass:"checkbox"}),n.call(this,t,e.extend(!0,a,o),r)||this}c(o,n);var r=o.prototype;return r.dispose=function(e){void 0===e&&(e=t),n.prototype.dispose.call(this,e)},o.matches=function(e){return"checkbox"===e.attr("type")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for type='checkbox'.")},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(dt);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),ct=function(e){var t="bmd.checkboxInline",n="bmd"+("c".toUpperCase()+"checkboxInline".slice(1)),o=e.fn[n],a={bmdFormGroup:{create:!1,required:!1}},r=function(n){function o(t,o,r){return void 0===r&&(r={inputType:"checkbox",outerClass:"checkbox-inline"}),n.call(this,t,e.extend(!0,{},a,o),r)||this}c(o,n);var r=o.prototype;return r.dispose=function(){n.prototype.dispose.call(this,t)},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(st);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),ft=function(e){var t="bmd.collapseInline",n="bmd"+("c".toUpperCase()+"collapseInline".slice(1)),o=e.fn[n],a={ANY_INPUT:"input, select, textarea"},r={IN:"in",COLLAPSE:"collapse",COLLAPSING:"collapsing",COLLAPSED:"collapsed",WIDTH:"width"},i={},l=function(n){function o(t,o){var l;l=n.call(this,t,e.extend(!0,{},i,o))||this,l.$bmdFormGroup=l.findMdbFormGroup(!0);var d=t.data("target");l.$collapse=e(d),rt.assert(t,0===l.$collapse.length,"Cannot find collapse target for "+rt.describe(t)),rt.assert(l.$collapse,!l.$collapse.hasClass(r.COLLAPSE),rt.describe(l.$collapse)+" is expected to have the '"+r.COLLAPSE+"' class.  It is being targeted by "+rt.describe(t));var s=l.$bmdFormGroup.find(a.ANY_INPUT);return 0<s.length&&(l.$input=s.first()),l.$collapse.hasClass(r.WIDTH)||l.$collapse.addClass(r.WIDTH),l.$input&&(l.$collapse.on("shown.bs.collapse",function(){l.$input.focus()}),l.$input.blur(function(){l.$collapse.collapse("hide")})),l}c(o,n);var l=o.prototype;return l.dispose=function(){n.prototype.dispose.call(this,t),this.$bmdFormGroup=null,this.$collapse=null,this.$input=null},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(it);return e.fn[n]=l._jQueryInterface,e.fn[n].Constructor=l,e.fn[n].noConflict=function(){return e.fn[n]=o,l._jQueryInterface},l}(jQuery),ut=function(e){var t="bmd.file",n="bmd"+("f".toUpperCase()+"file".slice(1)),o=e.fn[n],a={},r={FILE:"file",IS_FILE:"is-file"},i={FILENAMES:"input.form-control[readonly]"},l=function(n){function o(t,o){var i;return i=n.call(this,t,e.extend(!0,a,o))||this,i.$bmdFormGroup.addClass(r.IS_FILE),i}c(o,n);var l=o.prototype;return l.dispose=function(){n.prototype.dispose.call(this,t)},o.matches=function(e){return"file"===e.attr("type")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for type='file'.")},l.outerElement=function(){return this.$element.parent().closest("."+r.FILE)},l.rejectWithoutRequiredStructure=function(){rt.assert(this.$element,"label"===!this.outerElement().prop("tagName"),this.constructor.name+"'s "+rt.describe(this.$element)+" parent element "+rt.describe(this.outerElement())+" should be <label>."),rt.assert(this.$element,!this.outerElement().hasClass(r.FILE),this.constructor.name+"'s "+rt.describe(this.$element)+" parent element "+rt.describe(this.outerElement())+" should have class ."+r.FILE+".")},l.addFocusListener=function(){var e=this;this.$bmdFormGroup.on("focus",function(){e.addFormGroupFocus()}).on("blur",function(){e.removeFormGroupFocus()})},l.addChangeListener=function(){var t=this;this.$element.on("change",function(){var n="";e.each(t.$element.files,function(e,t){n+=t.name+"  , "}),n=n.substring(0,n.length-2),n?t.addIsFilled():t.removeIsFilled(),t.$bmdFormGroup.find(i.FILENAMES).val(n)})},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(lt);return e.fn[n]=l._jQueryInterface,e.fn[n].Constructor=l,e.fn[n].noConflict=function(){return e.fn[n]=o,l._jQueryInterface},l}(jQuery),_t=function(e){var t="bmd.radio",n="bmd"+("r".toUpperCase()+"radio".slice(1)),o=e.fn[n],a={template:"<span class='bmd-radio'></span>"},r=function(n){function o(t,o,r){return void 0===r&&(r={inputType:"radio",outerClass:"radio"}),n.call(this,t,e.extend(!0,a,o),r)||this}c(o,n);var r=o.prototype;return r.dispose=function(e){void 0===e&&(e=t),n.prototype.dispose.call(this,e)},o.matches=function(e){return"radio"===e.attr("type")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for type='radio'.")},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(dt);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),mt=function(e){var t="bmd.radioInline",n="bmd"+("r".toUpperCase()+"radioInline".slice(1)),o=e.fn[n],a={bmdFormGroup:{create:!1,required:!1}},r=function(n){function o(t,o,r){return void 0===r&&(r={inputType:"radio",outerClass:"radio-inline"}),n.call(this,t,e.extend(!0,{},a,o),r)||this}c(o,n);var r=o.prototype;return r.dispose=function(){n.prototype.dispose.call(this,t)},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(_t);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),pt=function(e){var t={requiredClasses:["form-control"]},n=function(n){function o(o,a){var r;return r=n.call(this,o,e.extend(!0,t,a))||this,r.isEmpty()&&r.removeIsFilled(),r}return c(o,n),o}(lt);return n}(jQuery),gt=function(e){var t="bmd.select",n="bmd"+("s".toUpperCase()+"select".slice(1)),o=e.fn[n],a={requiredClasses:["form-control||custom-select"]},r=function(n){function o(t,o){var r;return r=n.call(this,t,e.extend(!0,a,o))||this,r.addIsFilled(),r}c(o,n);var r=o.prototype;return r.dispose=function(){n.prototype.dispose.call(this,t)},o.matches=function(e){return"select"===e.prop("tagName")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for <select>.")},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(pt);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),Et=function(e){var t="bmd.switch",n="bmd"+("s".toUpperCase()+"switch".slice(1)),o=e.fn[n],a={template:"<span class='bmd-switch-track'></span>"},r=function(n){function o(t,o,r){return void 0===r&&(r={inputType:"checkbox",outerClass:"switch"}),n.call(this,t,e.extend(!0,{},a,o),r)||this}c(o,n);var r=o.prototype;return r.dispose=function(){n.prototype.dispose.call(this,t)},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(st);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),ht=function(e){var t="bmd.text",n="bmd"+("t".toUpperCase()+"text".slice(1)),o=e.fn[n],a={},r=function(n){function o(t,o){return n.call(this,t,e.extend(!0,a,o))||this}c(o,n);var r=o.prototype;return r.dispose=function(e){void 0===e&&(e=t),n.prototype.dispose.call(this,e)},o.matches=function(e){return"text"===e.attr("type")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for type='text'.")},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(pt);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),At=function(e){var t="bmd.textarea",n="bmd"+("t".toUpperCase()+"textarea".slice(1)),o=e.fn[n],a={},r=function(n){function o(t,o){return n.call(this,t,e.extend(!0,a,o))||this}c(o,n);var r=o.prototype;return r.dispose=function(){n.prototype.dispose.call(this,t)},o.matches=function(e){return"textarea"===e.prop("tagName")},o.rejectMatch=function(e,t){rt.assert(this.$element,this.matches(t),e+" component element "+rt.describe(t)+" is invalid for <textarea>.")},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(pt);return e.fn[n]=r._jQueryInterface,e.fn[n].Constructor=r,e.fn[n].noConflict=function(){return e.fn[n]=o,r._jQueryInterface},r}(jQuery),Ct=function(e){if("undefined"==typeof Popper)throw new Error("Bootstrap dropdown require Popper.js (https://popper.js.org)");var t="bs.dropdown",n="."+t,o=".data-api",a=e.fn.dropdown,r=27,i=32,l=9,d=38,c=40,f=new RegExp(d+"|"+c+"|"+r),u={HIDE:"hide"+n,HIDDEN:"hidden"+n,SHOW:"show"+n,SHOWN:"shown"+n,CLICK:"click"+n,CLICK_DATA_API:"click"+n+o,KEYDOWN_DATA_API:"keydown"+n+o,KEYUP_DATA_API:"keyup"+n+o,TRANSITION_END:"transitionend webkitTransitionEnd oTransitionEnd animationend webkitAnimationEnd oAnimationEnd"},m={DISABLED:"disabled",SHOW:"show",SHOWING:"showing",HIDING:"hiding",DROPUP:"dropup",MENURIGHT:"dropdown-menu-right",MENULEFT:"dropdown-menu-left"},p={DATA_TOGGLE:"[data-toggle=\"dropdown\"]",FORM_CHILD:".dropdown form",MENU:".dropdown-menu",NAVBAR_NAV:".navbar-nav",VISIBLE_ITEMS:".dropdown-menu .dropdown-item:not(.disabled)"},g={TOP:"top-start",TOPEND:"top-end",BOTTOM:"bottom-start",BOTTOMEND:"bottom-end"},E={placement:g.BOTTOM,offset:0,flip:!0},h={placement:"string",offset:"(number|string)",flip:"boolean"},A=function(){function o(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var a=o.prototype;return a.toggle=function(){var t=this;if(!(this._element.disabled||e(this._element).hasClass(m.DISABLED))){var n=o._getParentFromElement(this._element),a=e(this._menu).hasClass(m.SHOW);if(o._clearMenus(),!a){var r={relatedTarget:this._element},i=e.Event(u.SHOW,r);if(e(n).trigger(i),!i.isDefaultPrevented()){var l=this._element;e(n).hasClass(m.DROPUP)&&(e(this._menu).hasClass(m.MENULEFT)||e(this._menu).hasClass(m.MENURIGHT))&&(l=n),this._popper=new Popper(l,this._menu,this._getPopperConfig()),"ontouchstart"in document.documentElement&&!e(n).closest(p.NAVBAR_NAV).length&&e("body").children().on("mouseover",null,e.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),e(this._menu).one(u.TRANSITION_END,function(){e(n).trigger(e.Event(u.SHOWN,r)),e(t._menu).removeClass(m.SHOWING)}),e(this._menu).addClass(m.SHOW+" "+m.SHOWING),e(n).addClass(m.SHOW)}}}},a.dispose=function(){e.removeData(this._element,t),e(this._element).off(n),this._element=null,this._menu=null,null!==this._popper&&this._popper.destroy(),this._popper=null},a.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},a._addEventListeners=function(){var t=this;e(this._element).on(u.CLICK,function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},a._getConfig=function(t){var n=e(this._element).data();return void 0!==n.placement&&(n.placement=g[n.placement.toUpperCase()]),t=e.extend({},this.constructor.Default,e(this._element).data(),t),_.typeCheckConfig("dropdown",t,this.constructor.DefaultType),t},a._getMenuElement=function(){if(!this._menu){var t=o._getParentFromElement(this._element);this._menu=e(t).find(p.MENU)[0]}return this._menu},a._getPlacement=function(){var t=e(this._element).parent(),n=this._config.placement;return t.hasClass(m.DROPUP)||this._config.placement===g.TOP?(n=g.TOP,e(this._menu).hasClass(m.MENURIGHT)&&(n=g.TOPEND)):e(this._menu).hasClass(m.MENURIGHT)&&(n=g.BOTTOMEND),n},a._detectNavbar=function(){return 0<e(this._element).closest(".navbar").length},a._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:{offset:this._config.offset},flip:{enabled:this._config.flip}}};return this._inNavbar&&(e.modifiers.applyStyle={enabled:!this._inNavbar}),e},o._jQueryInterface=function(n){return this.each(function(){var a=e(this).data(t),r="object"==typeof n?n:null;if(a||(a=new o(this,r),e(this).data(t,a)),"string"==typeof n){if(void 0===a[n])throw new Error("No method named \""+n+"\"");a[n]()}})},o._clearMenus=function(n){if(!(n&&(n.which===3||"keyup"===n.type&&n.which!==l)))for(var a,r=e.makeArray(e(p.DATA_TOGGLE)),d=function(a){var i=o._getParentFromElement(r[a]),d=e(r[a]).data(t),s={relatedTarget:r[a]};if(!d)return"continue";var c=d._menu;if(!e(i).hasClass(m.SHOW))return"continue";if(n&&("click"===n.type&&/input|textarea/i.test(n.target.tagName)||"keyup"===n.type&&n.which===l)&&e.contains(i,n.target))return"continue";var f=e.Event(u.HIDE,s);return e(i).trigger(f),f.isDefaultPrevented()?"continue":void(("ontouchstart"in document.documentElement)&&e("body").children().off("mouseover",null,e.noop),r[a].setAttribute("aria-expanded","false"),e(c).addClass(m.HIDING).removeClass(m.SHOW),e(i).removeClass(m.SHOW),e(c).one(u.TRANSITION_END,function(){e(i).trigger(e.Event(u.HIDDEN,s)),e(c).removeClass(m.HIDING)}))},s=0;s<r.length;s++)a=d(s),"continue"===a},o._getParentFromElement=function(t){var n,o=_.getSelectorFromElement(t);return o&&(n=e(o)[0]),n||t.parentNode},o._dataApiKeydownHandler=function(t){if(!(!f.test(t.which)||/button/i.test(t.target.tagName)&&t.which===i||/input|textarea/i.test(t.target.tagName))&&(t.preventDefault(),t.stopPropagation(),!(this.disabled||e(this).hasClass(m.DISABLED)))){var n=o._getParentFromElement(this),a=e(n).hasClass(m.SHOW);if(!a&&(t.which!==r||t.which!==i)||a&&(t.which===r||t.which===i)){if(t.which===r){var l=e(n).find(p.DATA_TOGGLE)[0];e(l).trigger("focus")}return void e(this).trigger("click")}var s=e(n).find(p.VISIBLE_ITEMS).get();if(s.length){var u=s.indexOf(t.target);t.which===d&&0<u&&u--,t.which===c&&u<s.length-1&&u++,0>u&&(u=0),s[u].focus()}}},s(o,null,[{key:"VERSION",get:function(){return"4.1.0"}},{key:"Default",get:function(){return E}},{key:"DefaultType",get:function(){return h}}]),o}();return e(document).on(u.KEYDOWN_DATA_API,p.DATA_TOGGLE,A._dataApiKeydownHandler).on(u.KEYDOWN_DATA_API,p.MENU,A._dataApiKeydownHandler).on(u.CLICK_DATA_API+" "+u.KEYUP_DATA_API,A._clearMenus).on(u.CLICK_DATA_API,p.DATA_TOGGLE,function(t){t.preventDefault(),t.stopPropagation(),A._jQueryInterface.call(e(this),"toggle")}).on(u.CLICK_DATA_API,p.FORM_CHILD,function(t){t.stopPropagation()}),e.fn.dropdown=A._jQueryInterface,e.fn.dropdown.Constructor=A,e.fn.dropdown.noConflict=function(){return e.fn.dropdown=a,A._jQueryInterface},A}(jQuery),Tt=function(e){var t={CANVAS:"bmd-layout-canvas",CONTAINER:"bmd-layout-container",BACKDROP:"bmd-layout-backdrop"},n={CANVAS:"."+t.CANVAS,CONTAINER:"."+t.CONTAINER,BACKDROP:"."+t.BACKDROP},o={canvas:{create:!0,required:!0,template:"<div class=\""+t.CANVAS+"\"></div>"},backdrop:{create:!0,required:!0,template:"<div class=\""+t.BACKDROP+"\"></div>"}},a=function(t){function a(n,a,r){var i;return void 0===r&&(r={}),i=t.call(this,n,e.extend(!0,{},o,a),r)||this,i.$container=i.findContainer(!0),i.$backdrop=i.resolveBackdrop(),i.resolveCanvas(),i}c(a,t);var r=a.prototype;return r.dispose=function(e){t.prototype.dispose.call(this,e),this.$container=null,this.$backdrop=null},r.resolveCanvas=function(){var e=this.findCanvas(!1);return(void 0===e||0===e.length)&&(this.config.canvas.create&&this.$container.wrap(this.config.canvas.template),e=this.findCanvas(this.config.canvas.required)),e},r.findCanvas=function(t,o){void 0===t&&(t=!0),void 0===o&&(o=this.$container);var a=o.closest(n.CANVAS);return 0===a.length&&t&&e.error("Failed to find "+n.CANVAS+" for "+rt.describe(o)),a},r.resolveBackdrop=function(){var e=this.findBackdrop(!1);return(void 0===e||0===e.length)&&(this.config.backdrop.create&&this.$container.append(this.config.backdrop.template),e=this.findBackdrop(this.config.backdrop.required)),e},r.findBackdrop=function(t,o){void 0===t&&(t=!0),void 0===o&&(o=this.$container);var a=o.find("> "+n.BACKDROP);return 0===a.length&&t&&e.error("Failed to find "+n.BACKDROP+" for "+rt.describe(o)),a},r.findContainer=function(t,o){void 0===t&&(t=!0),void 0===o&&(o=this.$element);var a=o.closest(n.CONTAINER);return 0===a.length&&t&&e.error("Failed to find "+n.CONTAINER+" for "+rt.describe(o)),a},a}(it);return a}(jQuery),It=function(e){var t="bmd.drawer",n="bmd"+("d".toUpperCase()+"drawer".slice(1)),o=e.fn[n],a={ESCAPE:27},r={IN:"in",DRAWER_IN:"bmd-drawer-in",DRAWER_OUT:"bmd-drawer-out",DRAWER:"bmd-layout-drawer",CONTAINER:"bmd-layout-container"},i={focusSelector:"a, button, input"},l=function(n){function o(t,o){var r;return r=n.call(this,t,e.extend(!0,{},i,o))||this,r.$toggles=e("[data-toggle=\"drawer\"][href=\"#"+r.$element[0].id+"\"], [data-toggle=\"drawer\"][data-target=\"#"+r.$element[0].id+"\"]"),r._addAria(),r.$backdrop.keydown(function(e){e.which===a.ESCAPE&&r.hide()}).click(function(){r.hide()}),r.$element.keydown(function(e){e.which===a.ESCAPE&&r.hide()}),r.$toggles.click(function(){r.toggle()}),r}c(o,n);var l=o.prototype;return l.dispose=function(){n.prototype.dispose.call(this,t),this.$toggles=null},l.toggle=function(){this._isOpen()?this.hide():this.show()},l.show=function(){if(!(this._isForcedClosed()||this._isOpen())){this.$toggles.attr("aria-expanded",!0),this.$element.attr("aria-expanded",!0),this.$element.attr("aria-hidden",!1);var e=this.$element.find(this.config.focusSelector);0<e.length&&e.first().focus(),this.$container.addClass(r.DRAWER_IN),this.$backdrop.addClass(r.IN)}},l.hide=function(){this._isOpen()&&(this.$toggles.attr("aria-expanded",!1),this.$element.attr("aria-expanded",!1),this.$element.attr("aria-hidden",!0),this.$container.removeClass(r.DRAWER_IN),this.$backdrop.removeClass(r.IN))},l._isOpen=function(){return this.$container.hasClass(r.DRAWER_IN)},l._isForcedClosed=function(){return this.$container.hasClass(r.DRAWER_OUT)},l._addAria=function(){var e=this._isOpen();this.$element.attr("aria-expanded",e),this.$element.attr("aria-hidden",e),this.$toggles.length&&this.$toggles.attr("aria-expanded",e)},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(Tt);return e.fn[n]=l._jQueryInterface,e.fn[n].Constructor=l,e.fn[n].noConflict=function(){return e.fn[n]=o,l._jQueryInterface},l}(jQuery),bt=function(e){var t="bmd.ripples",n="bmd"+("r".toUpperCase()+"ripples".slice(1)),o=e.fn[n],a={CONTAINER:"ripple-container",DECORATOR:"ripple-decorator"},r={CONTAINER:"."+a.CONTAINER,DECORATOR:"."+a.DECORATOR},i={container:{template:"<div class='"+a.CONTAINER+"'></div>"},decorator:{template:"<div class='"+a.DECORATOR+"'></div>"},trigger:{start:"mousedown touchstart",end:"mouseup mouseleave touchend"},touchUserAgentRegex:/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i,duration:500},l=function(){function n(t,n){var o=this;this.$element=t,this.config=e.extend(!0,{},i,n),this.$element.on(this.config.trigger.start,function(e){o._onStartRipple(e)})}var o=n.prototype;return o.dispose=function(){this.$element.data(t,null),this.$element=null,this.$container=null,this.$decorator=null,this.config=null},o._onStartRipple=function(e){var t=this;if(!(this._isTouch()&&"mousedown"===e.type)){this._findOrCreateContainer();var n=this._getRelY(e),o=this._getRelX(e);(n||o)&&(this.$decorator.css({left:o,top:n,"background-color":this._getRipplesColor()}),this._forceStyleApplication(),this.rippleOn(),setTimeout(function(){t.rippleEnd()},this.config.duration),this.$element.on(this.config.trigger.end,function(){t.$decorator&&(t.$decorator.data("mousedown","off"),"off"===t.$decorator.data("animating")&&t.rippleOut())}))}},o._findOrCreateContainer=function(){(!this.$container||0<!this.$container.length)&&(this.$element.append(this.config.container.template),this.$container=this.$element.find(r.CONTAINER)),this.$container.append(this.config.decorator.template),this.$decorator=this.$container.find(r.DECORATOR)},o._forceStyleApplication=function(){return window.getComputedStyle(this.$decorator[0]).opacity},o._getRelX=function(e){var t=this.$container.offset(),n=null;return this._isTouch()?(e=e.originalEvent,n=1===e.touches.length&&e.touches[0].pageX-t.left):n=e.pageX-t.left,n},o._getRelY=function(e){var t=this.$container.offset(),n=null;return this._isTouch()?(e=e.originalEvent,n=1===e.touches.length&&e.touches[0].pageY-t.top):n=e.pageY-t.top,n},o._getRipplesColor=function(){var e=this.$element.data("ripple-color")?this.$element.data("ripple-color"):window.getComputedStyle(this.$element[0]).color;return e},o._isTouch=function(){return this.config.touchUserAgentRegex.test(navigator.userAgent)},o.rippleEnd=function(){this.$decorator&&(this.$decorator.data("animating","off"),"off"===this.$decorator.data("mousedown")&&this.rippleOut(this.$decorator))},o.rippleOut=function(){var e=this;this.$decorator.off(),rt.transitionEndSupported()?this.$decorator.addClass("ripple-out"):this.$decorator.animate({opacity:0},100,function(){e.$decorator.trigger("transitionend")}),this.$decorator.on(rt.transitionEndSelector(),function(){e.$decorator&&(e.$decorator.remove(),e.$decorator=null)})},o.rippleOn=function(){var e=this,t=this._getNewSize();rt.transitionEndSupported()?this.$decorator.css({"-ms-transform":"scale("+t+")","-moz-transform":"scale("+t+")","-webkit-transform":"scale("+t+")",transform:"scale("+t+")"}).addClass("ripple-on").data("animating","on").data("mousedown","on"):this.$decorator.animate({width:2*f(this.$element.outerWidth(),this.$element.outerHeight()),height:2*f(this.$element.outerWidth(),this.$element.outerHeight()),"margin-left":-1*f(this.$element.outerWidth(),this.$element.outerHeight()),"margin-top":-1*f(this.$element.outerWidth(),this.$element.outerHeight()),opacity:.2},this.config.duration,function(){e.$decorator.trigger("transitionend")})},o._getNewSize=function(){return 2.5*(f(this.$element.outerWidth(),this.$element.outerHeight())/this.$decorator.outerWidth())},n._jQueryInterface=function(o){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new n(a,o),a.data(t,r))})},n}();return e.fn[n]=l._jQueryInterface,e.fn[n].Constructor=l,e.fn[n].noConflict=function(){return e.fn[n]=o,l._jQueryInterface},l}(jQuery),Ot=function(e){var t="bmd.autofill",n="bmd"+("a".toUpperCase()+"autofill".slice(1)),o=e.fn[n],a="bmd.last_value",r={},i=function(n){function o(t,o){var a;return a=n.call(this,t,e.extend(!0,{},r,o))||this,a._watchLoading(),a._attachEventHandlers(),a}c(o,n);var i=o.prototype;return i.dispose=function(){n.prototype.dispose.call(this,t)},i._watchLoading=function(){var e=this;setTimeout(function(){clearInterval(e._onLoading)},1e4)},i._onLoading=function(){setInterval(function(){e("input[type!=checkbox]").each(function(t,n){var o=e(n),r=o.data(a);void 0===r&&(r=o.attr("value")),void 0===r&&(r="");var i=o.val();i!==r&&o.trigger("change"),o.data(a,i)})},100)},i._attachEventHandlers=function(){var t=null;e(document).on("focus","input",function(n){var o=e(n.currentTarget).closest("form").find("input").not("[type=file], [type=date]");t=setInterval(function(){o.each(function(t,n){var o=e(n),r=o.data(a);void 0===r&&(r=o.attr("value")),void 0===r&&(r="");var i=o.val();i!==r&&o.trigger("change"),o.data(a,i)})},100)}).on("blur",".form-group input",function(){clearInterval(t)})},o._jQueryInterface=function(n){return this.each(function(){var a=e(this),r=a.data(t);r||(r=new o(a,n),a.data(t,r))})},o}(it);return e.fn[n]=i._jQueryInterface,e.fn[n].Constructor=i,e.fn[n].noConflict=function(){return e.fn[n]=o,i._jQueryInterface},i}(jQuery);Popper.Defaults.modifiers.computeStyle.gpuAcceleration=!1;(function(t){var e="bmd.bootstrapMaterialDesign",n="bootstrapMaterialDesign",o=t.fn[n],a={global:{validate:!1,label:{className:"bmd-label-static"}},autofill:{selector:"body"},checkbox:{selector:".checkbox > label > input[type=checkbox]"},checkboxInline:{selector:"label.checkbox-inline > input[type=checkbox]"},collapseInline:{selector:".bmd-collapse-inline [data-toggle=\"collapse\"]"},drawer:{selector:".bmd-layout-drawer"},file:{selector:"input[type=file]"},radio:{selector:".radio > label > input[type=radio]"},radioInline:{selector:"label.radio-inline > input[type=radio]"},ripples:{selector:[".btn:not(.btn-link):not(.ripple-none)",".card-image:not(.ripple-none)",".navbar a:not(.ripple-none)",".dropdown-menu a:not(.ripple-none)",".nav-tabs a:not(.ripple-none)",".pagination li:not(.active):not(.disabled) a:not(.ripple-none)",".ripple"]},select:{selector:["select"]},switch:{selector:".switch > label > input[type=checkbox]"},text:{selector:["input:not([type=hidden]):not([type=checkbox]):not([type=radio]):not([type=file]):not([type=button]):not([type=submit]):not([type=reset])"]},textarea:{selector:["textarea"]},arrive:!0,instantiation:["ripples","checkbox","checkboxInline","collapseInline","drawer","radio","radioInline","switch","text","textarea","select","autofill"]},r=function(){function n(e,n){var o=this;this.$element=e,this.config=t.extend(!0,{},a,n);for(var r=t(document),i=function(){if(d){if(s>=l.length)return"break";c=l[s++]}else{if(s=l.next(),s.done)return"break";c=s.value}var e=c,n=o.config[e];if(n){var a=o._resolveSelector(n);n=t.extend(!0,{},o.config.global,n);var i=""+(e.charAt(0).toUpperCase()+e.slice(1)),f="bmd"+i;try{t(a)[f](n),document.arrive&&o.config.arrive&&r.arrive(a,function(){t(this)[f](n)})}catch(o){var u="Failed to instantiate component: $('"+a+"')["+f+"]("+n+")";throw console.error(u,o,"\nSelected elements: ",t(a)),o}}},l=this.config.instantiation,d=Array.isArray(l),s=0,l=d?l:l[Symbol.iterator]();;){var c,f=i();if("break"===f)break}}var o=n.prototype;return o.dispose=function(){this.$element.data(e,null),this.$element=null,this.config=null},o._resolveSelector=function(e){var t=e.selector;return Array.isArray(t)&&(t=t.join(", ")),t},n._jQueryInterface=function(o){return this.each(function(){var a=t(this),r=a.data(e);r||(r=new n(a,o),a.data(e,r))})},n}();return t.fn[n]=r._jQueryInterface,t.fn[n].Constructor=r,t.fn[n].noConflict=function(){return t.fn[n]=o,r._jQueryInterface},r})(jQuery)});