html, body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background: #fafafa; }
  
.rtc-primary-bg {
  color: #fff;
  background-color: #1E88E5 !important;
  align-items: center; }

.custom-row-container {
  display: flex;
  margin: 0 15px; }

.navbar {
  display: flex;
  align-content: center;
  height: 54px; }

h5 {
  margin: 0; }

.custom-container {
  margin: left;
  margin: 0 15px;
  margin-top: 88px;
  box-sizing: border-box; }

.card.custom-card {
  width: 398px;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px; }

.card {
  width: 398px; }

.radio-list {
  display: flex;
  margin: auto;
  margin-top: 10px; }

.radio-list > .radio {
  margin: 0 10px;
  width: 50px; }
