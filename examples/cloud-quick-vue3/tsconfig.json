{
	"compilerOptions": {
		"target": "esnext",
		"module": "esnext",
		"strict": true,
		"jsx": "preserve",
		"moduleResolution": "node",
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"forceConsistentCasingInFileNames": true,
		"strictPropertyInitialization": false,
		"strictNullChecks": false,
		"useDefineForClassFields": true,
		"sourceMap": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"baseUrl": "/",
		"types": [
			"vite/client"
			// if using vite
		],
		"paths": {
			"@/*": [
				"src/*"
			]
		},
		"lib": [
			"esnext",
			"dom"
		]
	},
	"include": [
		"src/**/*.ts",
		"src/**/*.tsx",
		"src/**/*.vue",
		"tests/**/*.ts",
		"tests/**/*.tsx"
	],
	"references": [
		{
			"path": "./tsconfig.node.json"
		}
	]
}
