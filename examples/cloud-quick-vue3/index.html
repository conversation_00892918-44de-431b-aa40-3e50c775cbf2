<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='UTF-8' />
  <link rel="icon" href="https://web.sdk.qcloud.com/trtc/webrtc/assets/trtcio48.avif">
  <meta name='viewport' content='width=device-width, initial-scale=1.0' />
  <title>Quick Demo Vue3 | Tencent RTC</title>

  <!-- Open Graph Meta Tags -->
  <meta property="og:url" content="https://web.sdk.qcloud.com/trtc/webrtc/v5/test/qer/og/cloud-quick-vue3/index.html">
  <meta property="og:type" content="website">
  <meta property="og:title" content="Quick Demo Vue3 | Tencent RTC">
  <meta property="og:description" content="This demo uses Vue3 to quickly test essential Tencent RTC features.">
  <meta property="og:image" content="https://web.sdk.qcloud.com/trtc/webrtc/assets/og-image-vue3.png">

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta property="twitter:domain" content="web.sdk.qcloud.com">
  <meta property="twitter:url" content="https://web.sdk.qcloud.com/trtc/webrtc/v5/test/qer/og/cloud-quick-vue3/index.html">
  <meta name="twitter:title" content="Quick Demo Vue3 | Tencent RTC">
  <meta name="twitter:description" content="This demo uses Vue3 to quickly test essential Tencent RTC features.">
  <meta name="twitter:image" content="https://web.sdk.qcloud.com/trtc/webrtc/assets/og-image-vue3.png">
</head>
<body>
<div id='app'></div>
<script type='module' src='./src/main.ts'></script>
<script src='/lib-generate-test-usersig.min.js'></script>
</body>
</html>
