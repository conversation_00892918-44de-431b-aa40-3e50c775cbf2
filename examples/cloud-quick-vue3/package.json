{"name": "cloud-quick-vue3", "version": "0.1.0", "private": true, "scripts": {"start": "npm install && npm run dev", "dev": "vite", "build": "vite build", "build-tsc": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"aegis-web-sdk": "^1.35.25", "clipboard": "^2.0.10", "core-js": "^3.8.3", "element-plus": "^2.1.10", "pinia": "^2.0.13", "trtc-sdk-v5": "latest", "vue": "^3.2.25", "vue-i18n": "9", "vue-router": "^4.0.3", "vue3-clipboard": "^1.0.0", "vite": "^2.9.9", "@vitejs/plugin-vue": "^2.3.3"}, "devDependencies": {"eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/eslint-plugin": "^5.40.1", "@typescript-eslint/parser": "^5.40.1", "@intlify/vite-plugin-vue-i18n": "^3.4.0", "@types/node": "^17.0.33", "@vue/eslint-config-airbnb": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.21.0", "eslint-config-tencent": "^1.0.4", "eslint-plugin-import": "^2.25.3", "eslint-plugin-vue": "^8.0.3", "eslint-plugin-vuejs-accessibility": "^1.1.0", "stylus": "^0.55.0", "stylus-loader": "^6.1.0", "typescript": "~5.0.4", "unplugin-auto-import": "^0.7.1", "unplugin-element-plus": "^0.4.0", "unplugin-vue-components": "^0.19.3", "vue-tsc": "^0.34.7"}}